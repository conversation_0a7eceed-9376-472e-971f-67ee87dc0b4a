<template>
  <div class="body">
    <div class="header">
      <div class="head-l">
        <div class="font-a">{{ content }}</div>
        <div class="font-pack">
          <div class="font-b">
            <div class="fons">
              {{ sectionTime }}
              {{ sectionSeason }}
            </div>
            <div>
              <div v-if="status == 1" class="type not_started">未开始</div>
              <div v-if="status == 2" class="type conduct">进行中</div>
              <div v-if="status == 3" class="type end">已结束</div>
            </div>
          </div>
          <div class="font-c">
            <div class="font-c_title">涵盖知识点</div>
            <div class="fons">
              <div class="dipack">
                <div
                  v-for="(item, index) in knowledges"
                  :key="index"
                  class="but"
                >
                  {{ item.knowledgeName }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="number">
        <div class="number-div">
          <div class="number-content">{{ groupNumber }}<span>组</span></div>
          <div class="number-title">小组数量</div>
        </div>
        <div class="number-div">
          <div class="number-content">{{ studentNumber }}<span>人</span></div>
          <div class="number-title">学生数量</div>
        </div>
        <div class="number-div">
          <div class="number-content">
            {{ completedGroupNumber }}<span>组</span>
          </div>
          <div class="number-title">已完成</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { searchAffairTeachingTaskAPI } from '@/api/teacher/index.js'

export default {
  props: {
    // 高度配置，默认为自适应外层高度，传null则不控制高度，也可传数字控制
    // 列表数据
    datas: {
      type: Object
    }
  },
  data() {
    return {
      content: '',
      completedGroupNumber: '',
      studentNumber: '',
      groupNumber: '',
      knowledges: '',
      sectionSeason: '',
      sectionTime: '',
      options: [],
      changeData: null,
      value: 0
    }
  },
  computed: {
    // 1 未开始 2 进行中 3 已结束
    status: function() {
      const [s, e] = this.sectionSeason.split('-')
      const start = this.sectionTime + ' ' + s
      const end = this.sectionTime + ' ' + e
      var currentTiem = new Date().getTime()
      var startTiem = new Date(start).getTime()
      var endTiem = new Date(end).getTime()
      if (currentTiem < startTiem) {
        return 1
      }
      if (currentTiem > startTiem && currentTiem < endTiem) {
        return 2
      }
      if (currentTiem > endTiem) {
        return 3
      }
      return null
    }
  },
  watch: {
    '$route': function(to, from) {
      this.getBase()
    }
  },
  created() {
    this.getBase()
    this.$bus.$on('headTeacherEventClassChange', newData => {
      this.changeClassMethod(newData)
    })
  },
  methods: {
    getBase() {
      this.options = JSON.parse(localStorage.getItem(`manageAffairs_resultList`) || '[]')
      if (![undefined, null, ''].includes(this.options[0].className)) {
        this.changeData = this.options[0]
      }
      this.changeClassMethod()
    },
    changeClassMethod(changeData) {
      if (changeData) { this.changeData = changeData }
      const resultList = this.changeData instanceof Array ? [...this.changeData] : [this.changeData]
      const params = {
        classCode: this.datas.classCode,
        schedulingCode: this.datas.schedulingCode,
        curriculumCode: this.datas.curriculumCode,
        resultList: resultList }
      searchAffairTeachingTaskAPI(params).then(res => {
        this.content = res.data.content
        this.completedGroupNumber = res.data.completedGroupNumber
        this.studentNumber = res.data.studentNumber
        this.groupNumber = res.data.groupNumber
        this.knowledges = res.data.knowledges
        this.sectionSeason = res.data.sectionSeason
        this.sectionTime = res.data.sectionTime
      }).catch(() => {
        this.tableLoading = false
      })
    }
  }
}
</script>
<style lang="less" scoped>
  .header {
    background: #ffffff;
    padding-bottom: 10px;
    border-radius: 4px;
    display: flex;
    .head-l {
      font-family: HarmonyOS Sans SC-Medium, HarmonyOS Sans SC;
      width: 750px;
      .font-a {
        font-size: 20px;
        font-weight: 500;
        color: #1d2129;
      }
      .font-pack {
        .font-b {
          margin: 20px 0;
          display: flex;
          align-items: center;
          .fons {
            font-size: 14px;
            font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
            font-weight: 400;
            color: #4e5969;
          }
          .type {
            padding: 5px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
            font-weight: 400;
            color: #ffffff;
            margin-left: 20px;
          }
          .not_started {
            background: #ff7d00;
          }
          .conduct {
            background: #00b42a;
          }
          .end {
            background: #869094;
          }
        }
        ._c_section_left {
          margin-left: -10px;
          display: flex;
          align-items: center;
          font-weight: 300;
          ._c_section_left_1 {
            width: 70px;
            font-size: 14px;
            font-family: SourceHanSansCN-Regular, SourceHanSansCN;
            font-weight: 400;
            color: #999999;
          }
        }
        .font-c {
          font-size: 14px;
          font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
          font-weight: 400;
          color: #4e5969;
          .font-c_title {
            margin-bottom: 20px;
          }
          .fons {
            width: 100%;
            font-size: 14px;
            font-family: Microsoft YaHei;
            font-weight: 400;
            color: #999999;
            display: flex;
            justify-content: flex-start;

            .dipack {
              width: 100%;
              word-wrap: break-word;
              word-break: break-all;
              overflow: hidden;
              display: flex;
              align-items: center;
              justify-content: flex-start;
              flex-wrap: wrap;
              .but {
                padding: 5px 8px;
                background: #f2f3f5;
                border-radius: 4px;
                font-size: 12px;
                font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
                font-weight: 400;
                color: #4e5969;
                margin: 0 20px 12px 0;
              }
            }
          }
        }
      }
    }
    .head-r {
      width: 20%;
      height: 186px;
      display: flex;
      align-items: center;
      justify-content: space-evenly;

      .font-l {
        width: 120px;
        height: 120px;
        display: flex;
        align-items: center;
        flex-direction: column;
        justify-content: space-evenly;
        .tit {
          font-size: 14px;
          font-family: Microsoft YaHei;
          font-weight: 400;
          color: #999999;
        }
        .font-main {
          span {
            font-size: 26px;
            font-family: Arial;
            font-weight: 400;
            color: #288fef;
          }
        }
      }
      .font-r {
        width: 120px;
        height: 120px;
        display: flex;
        align-items: center;
        flex-direction: column;
        justify-content: space-evenly;
        .tit {
          font-size: 14px;
          font-family: Microsoft YaHei;
          font-weight: 400;
          color: #999999;
        }
        .font-main {
          span {
            font-size: 26px;
            font-family: Arial;
            font-weight: 400;
            color: #288fef;
          }
        }
      }
    }
    .number {
        display: flex;
        align-items: center;
        .number-div {
          margin: 0 10px;
          padding: 12px 16px;
          height: 88px;
          width: 88px;
          display: flex;
          background: #F7F8FA;
          align-items: center;
          flex-direction: column;
          border-radius: 4px;
          .number-title {
            font-size: 14px;
            font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
            font-weight: 400;
            color: #4E5969;
          }
          .number-content {
            font-size: 24px;
            font-family: HarmonyOS Sans SC-Medium, HarmonyOS Sans SC;
            font-weight: 500;
            color: var(--color-600);
            margin-bottom: 12px;
            span {
              color: #4E5969;
              font-size: 14px;
            }
          }
        }
      }
  }
</style>
