<template>
  <div class="cr-menu-header">
    <div class="cr-menu-header-left">
      <!-- 全部菜单 -->
      <el-popover
        v-model="allMenuVisible"
        :visible-arrow="false"
        popper-class="sitemap-popper"
        placement="bottom-start"
        width="750"
        trigger="click">
        <div class="sitemap">
          <div v-for="(item,index) in config" :key="index" class="menu-level1-container">
            <div class="menu-level1-title">
              <span>{{ item.title }}</span>
              <span class="menu-level1-title-line"/>
            </div>
            <div v-if="item.subs && !item.special" class="menu-level1-content">
              <div v-for="(subs, subsIndex) in item.subs" :key="subsIndex" class="menu-level2-item">
                <div class="menu-level2-item-label">
                  <span v-if="getManageRouters(subs.path).length === 0" class="can-click" @click="go(subs)">{{ subs.title }}</span>
                  <span v-else>{{ subs.title }}</span>
                </div>
                <div v-if="getManageRouters(subs.path).children" class="menu-level2-item-list">
                  <div v-for="(meta, metaIndex) in getManageRouters(subs.path).children" v-show="!meta.hidden" :key="metaIndex" class="menu-level2-item-list-item">
                    <span @click="go(subs, meta)">{{ meta.meta.title }}</span>
                  </div>
                </div>
                <div v-else>
                  <div class="menu-level2-item-list-item">
                    <span @click="go({path: '/manage' + subs.path})">{{ subs.title }}</span>
                  </div>
                </div>
              </div>
            </div>
            <div v-else-if="item.special" class="menu-level1-content">
              <div v-for="(subs, subsIndex) in item.subs" :key="subsIndex" class="menu-level2-item">
                <div class="menu-level2-item-label">
                  <span v-if="subs">{{ subs.title }}</span>
                </div>
                <div v-if="subs.children" class="menu-level2-item-list">
                  <div v-for="(meta, metaIndex) in subs.children" :key="metaIndex" class="menu-level2-item-list-item">
                    <span @click="go(meta)">{{ meta.title }}</span>
                  </div>
                </div>
              </div>
            </div>
            <div v-else class="menu-level1-content">
              <div class="menu-level2-item">
                <div class="menu-level2-item-label">
                  <span v-if="getManageRouters(item.path).length === 0" class="can-click" @click="go(item)">{{ item.title }}</span>
                  <span v-else>{{ item.title }}</span>
                </div>
                <div class="menu-level2-item-list">
                  <div v-for="(meta, metaIndex) in getManageRouters(item.path).children" v-show="!meta.hidden" :key="metaIndex" class="menu-level2-item-list-item">
                    <span @click="go(item, meta)">{{ meta.meta.title }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <img slot="reference" src="../../assets/images/menu.svg" alt="menu">
      </el-popover>
      <!-- 分割线 -->
      <el-divider direction="vertical" />
      <!-- 一级菜单 -->
      <el-menu
        :default-active="activeMenu"
        class="cr-first-menu"
        mode="horizontal"
        background-color="var(--neutral-0)"
        text-color="var(--neutral-700)"
        active-text-color="var(--color-600)"
        @select="handleSelect">
        <component
          v-for="menu in config"
          v-show="menu.path !== '/penetrant'"
          :key="menu.key"
          :index="menu.key"
          :is="menu.subs && menu.subs.length && !menu.special ? 'el-submenu' : 'el-menu-item'"
          :popper-class="menu.subs && menu.subs.length && !menu.special ? 'cr-first-sub-menu' : ''"
        >
          <template slot="title">{{ menu.title }}</template>
          <template v-if="menu.subs && menu.subs.length && !menu.special">
            <el-menu-item v-for="sub in menu.subs" :key="sub.key" :index="sub.key" :disabled="prohibit">
              <img :src="iconImg(sub.icon)" alt="">{{ sub.title }}
            </el-menu-item>
          </template>
        </component>
      </el-menu>
    </div>
    <div class="cr-menu-header-right">
      <!-- <el-tooltip class="header-icon-item" effect="dark" content="监控大屏" placement="bottom">
        <i class="el-icon-data-analysis" />
      </el-tooltip> -->
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
export default {
  props: {
    activeMenu: {
      type: String,
      default: () => {
        return ''
      }
    },
    config: {
      type: Array,
      default: () => {
        return []
      }
    },
    webRouters: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      allMenuVisible: false
    }
  },
  computed: {
    ...mapGetters(['manage', 'manageRouters', 'prohibit'])
  },
  methods: {
    getManageRouters(path) {
      const router = this.webRouters && this.webRouters.length ? this.webRouters : this.manageRouters
      return router.find(item => item.path === path) || []
    },
    iconImg(icon) {
      const themeKey = window.ADMIN_CONFIG ? (window.ADMIN_CONFIG.THEME || 'green') : (window.WEB_CONFIG.THEME || 'green')
      return require('../../assets/' + themeKey + '/' + icon + '.png')
    },
    handleSelect(key, keyPath) {
      if (this.prohibit) {
        return
      }
      this.$emit('changeMenu', key, keyPath)
    },
    go(subs, meta) {
      if (this.prohibit) {
        return
      }
      this.$emit('pageJump', subs, meta)
      this.allMenuVisible = false
    }
  }
}
</script>

<style lang="scss" scoped>
.cr-menu-header {
  position: relative;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  height: 48px;
  background-color: var(--neutral-0);
  border-bottom: 1px solid var(--neutral-300);
  overflow: hidden;
  .cr-menu-header-left {
    display: flex;
    align-items: center;
    flex-grow: 1;
    /deep/ .el-popover__reference-wrapper {
      margin-left: 16px;
      width: 32px;
      height: 48px;
      display: flex;
      justify-content: center;
      align-items: center;
      img {
        width: 16px;
        cursor: pointer;
      }
    }
    /deep/ .el-divider {
      background-color: var(--neutral-300);
    }
    .cr-first-menu {
      width: 100%;
      height: 48px;
      /deep/ .el-submenu .el-submenu__title, /deep/ .el-menu-item {
        height: 47px;
        line-height: 47px;
        margin: 0 12px;
        padding: 0;
        font-weight: 900;
        font-size: 14px;
        i {
          display: none;
        }
        &:hover{
          background-color: transparent !important;
          border-bottom: 2px solid #2362FB !important;
          color: var(--color-600) !important;
        }
      }
    }
  }
  .cr-menu-header-right {
    display: flex;
    align-items: center;
    margin-right: 16px;
    .header-icon-item {
      font-size: 16px;
      padding: 6px 8px;
      height: 32px;
      cursor: pointer;
      &:hover {
        color: var(--color-600);
        background-color: var(--neutral-200);
      }
    }
  }
}
</style>
<style lang="scss">
.sitemap-popper {
  left: 0 !important;
  top: 95px !important;
  margin: 0 !important;
  padding: 0 !important;
  color: rgba(0, 0, 0, 0.85);
  font-size: 14px;
  z-index: 1050;
  display: block;
  .sitemap {
    background: var(--neutral-0);
    box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.12);
    overflow: auto;
    height: 750px;
    padding: 24px 0;
    .menu-level1-container {
      padding: 0 24px;
      .menu-level1-title {
        margin-bottom: 20px;
        font-size: 12px;
        color: var(--neutral-500);
        position: relative;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .menu-level1-title-line {
          flex-grow: 1;
          border-bottom: 1px solid var(--neutral-300);
        }
      }
      .menu-level1-content {
        // display: flex;
        // flex-flow: row wrap;
        column-count: 4;
        padding: 0 20px;
        .menu-level2-item {
          // display: block;
          // flex: 0 0 25%;
          padding-bottom: 35px;
          break-inside: avoid;
          .menu-level2-item-label {
            color: var(--neutral-800);
            font-weight: bold;
            .can-click {
              cursor: pointer;
              &:hover {
                text-decoration: underline;
              }
            }
          }
          .menu-level2-item-list-item {
            color: var(--neutral-600);
            margin-top: 8px;
            span {
              cursor: pointer;
            }
          }
          .menu-level2-item-list-item:hover {
            span {
              text-decoration: underline;
            }
          }
        }
      }
    }
  }
}
.cr-first-sub-menu {
  .el-menu {
    padding: 8px 0;
    min-width: 160px;
    max-height: calc(100vh - 100px);
    .el-menu-item {
      height: 40px;
      line-height: 40px;
      padding: 0 20px;
      margin: 0;
      font-size: 14px;
      font-weight: 500;
      &.is-active{
        color: var(--neutral-700) !important;
        background-color: var(--neutral-100) !important;
        font-weight: 600;
      }
      &:hover{
        background-color: var(--neutral-100) !important;
        opacity: 0.8;
      }
      img {
        width: 16px;
        margin-right: 10px;
      }
    }
  }
}
</style>
