<template>
  <div class="resource-table">
    <!-- 操作区 -->
    <div class="operation-wrap">
      <div class="operation-left">
        <slot name="action" />
        <el-button type="primary" icon="el-icon-refresh" @click="refresh" />
        <el-checkbox v-model="isAll" style="margin-left: 35px;">仅查看自己创建</el-checkbox>
      </div>
      <div class="operation-right">
        <el-badge :value="searchBtnShowNum">
          <el-button icon="el-icon-search" @click="openSearch">搜索</el-button>
        </el-badge>
        <!-- 自定义表格列 -->
        <t-table-config
          v-if="!customColData.length"
          :data="columnsObj"
          :active-key-arr="columnsViewArr"
          @on-change-col="onChangeCol"
        />
      </div>
    </div>
    <!-- 搜索区 -->
    <t-search-box
      v-show="searchView"
      :search-key-list="searchKeyListView"
      default-placeholder="默认搜索名称"
      @search="searchMultiple"
    />
    <!-- 列表 -->
    <t-table-view
      ref="tableView"
      :height="height"
      :single="single"
      :loading="tableLoading"
      :data="tableData"
      :total="tableTotal"
      :page-size="pageSize"
      :current="pageCurrent"
      :select-item="selectItem"
      current-key="id"
      @on-select="onSelect"
      @on-current="onCurrent"
      @on-change="changePage"
      @on-sort-change="onSortChange"
      @on-page-size-change="onPageSizeChange"
    >
      <el-table-column
        v-for="item in columnsViewArr"
        :key="item"
        :min-width="colMinWidth"
        :label="columnsObj[item].title"
        :fixed="columnsObj[item].master ? 'left' : false"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span v-if="item == 'name'">
            <a
              v-if="link"
              :href="getHref(scope.row, '查看')"
              @click.prevent="handleDetailClick(scope.row, '查看')"
            >
              {{ scope.row.name }}
            </a>
            <span v-else>-</span>
          </span>
          <span v-else-if="item == 'contentType'">
            <span>{{ contentTypeList[Number(scope.row.contentType)-1] || '-' }}</span>
          </span>
          <span v-else-if="item == 'contentLevel'">
            <span>{{ difficultyList[scope.row.contentLevel-1] || '-' }}</span>
          </span>
          <span v-else>{{ scope.row[item] || '-' }}</span>
        </template>
      </el-table-column>
    </t-table-view>
  </div>
</template>

<script>
import module from '../config.js'
import tSearchBox from '@/packages/search-box/index.vue'
import tTableView from '@/packages/table-view/index.vue'
import tTableConfig from '@/packages/table-config/table-col-config.vue'
import tableTdMultiCol from '@/packages/table-config/table-td-multi-col.vue'
import mixinsPageTable from '@/packages/mixins/page_table'
import { pjtContentQuery } from '@/api/teacher/index.js'
import { EventBus } from '../eventBus.js'

export default {
  components: {
    tSearchBox,
    tTableView,
    tTableConfig,
    tableTdMultiCol
  },
  mixins: [mixinsPageTable],
  data() {
    return {
      moduleName: module.name,
      // 搜索配置项
      searchKeyList: [
        { key: 'name', label: '名称', master: true },
        { key: 'userName', label: '创建人' }
      ],
      // 所有可配置显示列 master：不可隐藏 title:列名称
      columnsObj: {
        'name': {
          title: '名称',
          master: true
        },
        'contentType': {
          title: '类型'
        },
        'contentCategoryName': {
          title: '分类'
        },
        'contentLevel': {
          title: '难度'
        },
        'contentPeriod': {
          title: '课时'
        },
        'userName': {
          title: '创建人'
        },
        'createdAt': {
          title: '时间'
        }
      },
      // 当前显示列key表 默认，如果localStorage有数据将被覆盖
      columnsViewArr: [
        'name',
        'contentType',
        'contentCategoryName',
        'contentLevel',
        'contentPeriod',
        'userName',
        'createdAt'
      ],
      tableData: [],
      contentTypeList: ['理论', '仿真'],
      difficultyList: ['初级', '中级', '高级'],
      isAll: false
    }
  },
  watch: {
    isAll() {
      this.getList()
    }
  },
  methods: {
    getList: function(type, showLoading = true) {
      if (showLoading) {
        this.tableLoading = true
      }
      const params = this.getPostData('page', 'limit')
      params.flag = this.isAll === true ? 1 : ''
      params.id = ''
      pjtContentQuery(params).then(res => {
        this.tableData = res.data.records
        if (type === 'editConfig') {
          EventBus.$emit('getCourseData', this.tableData)
        }
        this.tableTotal = res.data.total
        this.tableLoading = false
        this.handleSelection()
      }).catch(() => {
        this.tableLoading = false
      })
    },
    // 查看
    handleDetailClick(row, type) {
      const { name, contentType } = row
      this.$router.push({
        name: 'contentDetail',
        query: Object.assign({ curriculumName: '课程内容', teachingName: name, type: '查看', curriculumType: contentType == 1 ? '理论' : '仿真', oneLevelTitle: '课程内容', oneLevelName: 'trainingContent' }, row)
      })
      this.$refs['tableView'].clearSelection()
      this.setHighlightRow(row)
      this.$refs['tableView'].toggleRowSelection(row)
    },
    getHref(row, type) {
      const { name, contentType } = row
      const route = this.$router.resolve({
        name: 'contentDetail',
        query: Object.assign({ curriculumName: '课程内容', teachingName: name, type: '查看', curriculumType: contentType == 1 ? '理论' : '仿真', oneLevelTitle: '课程内容', oneLevelName: 'trainingContent' }, row)
      })
      return route.href
    }
  }
}
</script>
