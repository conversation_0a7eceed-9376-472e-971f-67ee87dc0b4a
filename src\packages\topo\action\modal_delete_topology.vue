<template>
  <div v-loading="loading" class="dialog-wrap">
    <!-- <el-alert type="warning">
      <div slot="title">此操作在删除拓扑的同时会删除拓扑中当前所有已创建的虚拟设备实例，以及会释放拓扑中已应用的物理设备的端口。此操作无法撤销，请谨慎操作！</div>
    </el-alert>
    <el-form :model="formData" label-width="110px" @submit.native.prevent>
      <el-form-item label="拓扑名称">{{ formData.name }}</el-form-item>
      <el-form-item label="物理设备数量">{{ formData.pnfNum }}</el-form-item>
      <el-form-item label="虚拟设备实例数量">{{ formData.vnfNum }}</el-form-item>
    </el-form> -->
    <div slot="title">
      <p>删除之后无法恢复，请确认是否删除拓扑模板：{{ formData.name }}?</p>
    </div>
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button :disabled="availableArr.length === 0" type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>
<script>
import module from '../config.js'
import { getItem, deleteTopology } from '../api/orchestration'
import modalMixins from '../../mixins/modal_form'
export default {
  mixins: [modalMixins],
  props: {
    // 传入数据
    data: {
      type: Array
    }
  },
  data() {
    return {
      moduleName: module.name,
      loading: false,
      formData: {
        name: '',
        pnfNum: 0,
        vnfNum: 0
      }
    }
  },
  computed: {
    // 筛选返回可操作数据
    'availableArr': function() {
      const _data = []
      this.data.forEach((item) => {
        if (this.handleAvailable(item)) {
          _data.push(item)
        }
      })
      return _data
    }
  },
  created() {
    this.getItem()
  },
  methods: {
    'getItem': function() {
      this.loading = true
      this.formData.name = this.data[0].name
      getItem(this.data[0].id)
        .then(res => {
          if (res.data.data.nodes.length) {
            res.data.data.nodes.forEach(item => {
              if (item.resource_type === 'pnf') {
                this.formData.pnfNum++
              } else if (item.resource_type === 'vnf') {
                this.formData.vnfNum++
              }
            })
          }
          this.loading = false
        })
        .catch((error) => {
          console.log(error)
        })
    },
    // 可操作数据判断逻辑
    handleAvailable(item) {
      return true
    },
    // 点击取消
    close() {
      this.$emit('close')
    },
    // 点击确认 ！规范：若产品需要api处理完成后关闭modal，可将api处理放在此处，若要求先关闭modal异步执行api，则使用call事件
    'confirm': function(modal) {
      this.loading = true
      this.$bus.$emit(
        'SINGLE_TASK_API',
        {
          taskName: '删除拓扑',
          resource: this.availableArr[0],
          apiObj: deleteTopology,
          data: {
            id: this.availableArr[0].id
          },
          sucsessCallback: (res) => {
            this.$bus.$emit(this.moduleName + '_module', 'reload')
          },
          errorCallback: () => {
            this.$bus.$emit(this.moduleName + '_module', 'reload')
          }
        }
      )
      this.$emit('close')
    }
  }
}
</script>
