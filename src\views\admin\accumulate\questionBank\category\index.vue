<template>
  <div class="course-classification">
    <transverse-list
      v-if="categoryType != 4"
      :data="questionCategoryList"
      :allow-add="false"
      :allow-edit="false"
      :allow-deletion="false"
      :module-name="categoryName + '_' + moduleName + '_questionLibrary'"
      :cache-pattern="true"
      title="题库"
      id-name="id"
      style="border:none;"
      @node-click="switchCategory($event, 'questionDepotId')"
    />
    <transverse-list
      :data="categoryList"
      :module-name="categoryName + '_' + moduleName + '_class'"
      :cache-pattern="true"
      id-name="id"
      @node-click="switchCategory($event, 'categoryId')"
      @add="clickDrop('modalAdd')"
      @edit="clickDrop('modalEdit')"
      @delete="clickDrop('modalDelete', $event)"
    />
    <!-- 中部弹窗 start-->
    <el-dialog
      :title="titleMapping[modalName]"
      :visible.sync="modalShow"
      :width="modalWidth"
      @close="modalClose"
    >
      <transition name="el-fade-in-linear">
        <component
          :is="modalName"
          :name="modalName"
          :category-list="categoryList"
          :category-type="categoryType"
          :data="selectItem"
          @close="modalClose"
        />
      </transition>
    </el-dialog>
    <!-- 中部弹窗 end-->
  </div>
</template>
<script>
import moduleConf from './config'
import transverseList from '@/packages/transverse-list/index.vue'
import modalAdd from './modal-add'
import modalEdit from './modal-edit'
import modalDelete from './modal-delete'
import { getCategory } from '@/api/accumulate/category'
import { getQuestionCategory } from '@/api/accumulate/questionLibrary'
export default {
  components: {
    transverseList,
    modalAdd,
    modalEdit,
    modalDelete
  },
  props: {
    categoryType: {
      type: Number,
      default: 1
    },
    categoryName: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      // 弹窗title映射
      titleMapping: {
        modalAdd: '新增',
        modalEdit: '编辑',
        modalDelete: '删除'
      },
      moduleName: moduleConf.name,
      selectItem: [], // 删除时点击的分类
      categoryList: [], // 分类列表
      questionCategoryList: [], // 题库列表
      categoryQuery: {
        questionDepotId: null,
        categoryId: null
      },
      modalWidth: '520px',
      modalShow: false,
      modalName: null
    }
  },
  watch: {
    // 监听模态框动态组件，如置空则关闭模态框
    'modalName': function(val) {
      this.modalShow = (!!val)
    }
  },
  created() {
    this.getList()
  },
  mounted() {
    // 事件监听
    this.$bus.$on(this.moduleName + '_module', (type, data) => {
      switch (type) {
        case 'reload':
          this.getList()
          break
      }
    })
  },
  beforeDestroy() {
    this.$bus.$off(this.moduleName + '_module')
  },
  methods: {
    'modalClose': function() {
      this.modalName = null
      this.modalWidth = '520px'
    },
    'clickDrop': function(name, data) {
      this.modalName = name
      if (data) {
        this.selectItem = [data]
      }
    },
    'getList': function() {
      getCategory({ pageType: 0, categoryType: this.categoryType }).then(res => {
        this.categoryList = res.data.records
      })
      getQuestionCategory().then(res => {
        this.questionCategoryList = (res.data || []).map(item => ({
          ...item,
          categoryName: item.questionDepotName
        }))
      })
    },
    switchCategory(data, key) {
      this.categoryQuery[key] = data.id
      this.$emit('switchCategory', this.categoryQuery)
    }
  }
}
</script>
