<template>
  <div v-loading="loading" class="dialog-wrap">
    <el-form v-loading="loading" ref="form" :model="formData" :rules="rules" label-width="90px">
      <el-form-item label="试卷克隆">
        <span>{{ data[0].examName }}</span>
      </el-form-item>
      <el-form-item label="试卷名称" prop="examName">
        <el-input v-model.trim="formData.examName" />
      </el-form-item>
    </el-form>
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>

<script>
import moduleConf from '../config.js'
import validate from '@/packages/validate'
import { cloneExam } from '@/api/accumulate/exam.js'

export default {
  components: {
  },
  props: {
    data: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      moduleName: moduleConf.name,
      loading: false,
      validate: validate,
      formData: {
        examName: '' // 新试卷名称
      },
      rules: {
        examName: [validate.required(), validate.name_64_char]
      }
    }
  },
  methods: {
    close() {
      this.$emit('close')
    },
    confirm: function() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.loading = true
          const postData = JSON.parse(JSON.stringify(this.formData))
          postData['id'] = this.data[0].id
          cloneExam(postData).then(res => {
            this.$message.success('克隆试卷成功')
            this.$bus.$emit(this.moduleName + '_module', 'reload')
            this.close()
          }).catch(() => {
            this.loading = false
          })
        }
      })
    }
  }
}
</script>
