<template>
  <div v-loading="loading" class="dialog-wrap" >
    <el-form
      ref="form"
      :model="formData"
      :rules="rules"
      label-width="80px"
      @submit.native.prevent
    >
      <el-form-item label="名称" prop="name">
        <el-input v-model.trim="formData.name"/>
      </el-form-item>
      <el-form-item label="描述" prop="description">
        <el-input v-model.trim="formData.description" type="textarea"/>
      </el-form-item>
    </el-form>
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>

<script>
import module from '../config.js'
import { createTopology } from '../api/orchestration'
import modalMixins from '../../mixins/modal_form'
import { mapGetters } from 'vuex'
import validate from '../../validate'
export default {
  mixins: [modalMixins],
  data() {
    return {
      moduleName: module.name,
      loading: false,
      formData: {
        name: '',
        description: ''
      },
      rules: {
        name: [validate.required(), validate.base_name],
        description: [validate.description]
      }
    }
  },
  computed: {
    ...mapGetters([
      'userInfo'
    ])
  },
  mounted() {
    console.log(this.userInfo)
  },
  methods: {
    // modal点击确定
    confirm() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.loading = true
          const postData = this.formData
          postData.type = 'template'
          postData.user_id = this.userInfo.userId
          this.$bus.$emit('SINGLE_TASK_API', {
            taskName: '创建拓扑',
            resource: postData,
            apiObj: createTopology,
            data: { data: postData },
            sucsessCallback: (res) => {
              this.$bus.$emit(this.moduleName + '_module', 'reload')
            },
            errorCallback: () => {}
          })
          this.close()
        }
      })
    },
    close() {
      this.$emit('close')
    }
  }
}
</script>
