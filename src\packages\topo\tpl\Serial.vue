<template>
  <div class="content-wrap">
    <el-dialog
      :visible.sync="modalMain"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
      fullscreen
      footer-hide
      size="small"
      class="serial-warp"
    >
      <serial-console :url="url" />
    </el-dialog>
  </div>
</template>
<script>
import serialConsole from './serial_console/index.vue'
export default {
  components: {
    serialConsole
  },
  data() {
    return {
      modalMain: true, // 全屏弹窗
      url: null
    }
  },
  created() {
    const url = this.$route.query.url
    this.url = url.indexOf('http') === -1 ? 'wss://' + url : url.replace('http', 'ws')
  }
}
</script>
<style lang="less" scoped>
.serial-warp {
  /deep/ .el-dialog__header {
    display: none;
  }
  /deep/ .el-dialog__body {
    padding: 8px;
    overflow: hidden;
  }
}
</style>
