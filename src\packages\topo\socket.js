import Vue from 'vue'
import VueSocketio from 'vue-socket.io'
import Socketio from 'socket.io-client'
import { mapGetters } from 'vuex'
import store from '@/store'
import { getWsToken } from '../topo/api/orchestration'

export default {
  computed: {
    ...mapGetters(['userInfo'])
  },
  watch: {
    'userInfo': function(userInfo) {
      // 检测项目负责人、检测人员和检测厂商不走没有权限的socket
      if (userInfo.roleId == '181251' || userInfo.roleId == '181253' || userInfo.roleId == '181254') {
        return
      }
      getWsToken({ 'user_id': this.userInfo.userId })
        .then(res => {
          localStorage.setItem('nfvo_socket_token', res.data.data)
          const ws = {
            'path': window.NFVO_CONFIG.socket_path || '/ws',
            'token': res.data.data,
            'url': ''
          }
          Vue.use(new VueSocketio({
            connection: Socketio.connect(
              window.NFVO_CONFIG.socket_url + ws.url,
              {
                path: ws.path,
                query: {
                  token: ws.token,
                  lang: sessionStorage.getItem('django_language')
                }
              }
            ),
            vuex: {
              store,
              actionPrefix: 'SOCKET_',
              mutationPrefix: 'SOCKET_'
            }
          }))
        })
        .catch((error) => {
          console.log(error)
        })
    }
  }
}
