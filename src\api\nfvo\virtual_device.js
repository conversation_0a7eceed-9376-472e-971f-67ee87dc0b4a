import request from '@/packages/request'
const _thisApi = window.NFVO_CONFIG.nfvo_api
const headers = { 'Content-Type': 'application/json', 'x-access-module': 'ADMIN' }

export function getListApi(params) {
  return request({
    url: _thisApi + '/networkelement',
    method: 'get',
    params,
    headers
  })
}

export function getItem(id) {
  return request({
    url: _thisApi + '/networkelement/' + id,
    method: 'get',
    headers
  })
}

export function getVTList(params) {
  return request({
    url: _thisApi + '/networkelement/templates',
    method: 'get',
    params,
    headers
  })
}

export function addDevice(data) {
  return request({
    url: _thisApi + '/networkelement',
    method: 'post',
    data,
    headers
  })
}

export function editDevice(id, data) {
  return request({
    url: _thisApi + '/networkelement/' + id,
    method: 'put',
    data,
    headers
  })
}

export function deleteDevice(id) {
  return request({
    url: _thisApi + '/networkelement/' + id,
    method: 'DELETE',
    headers
  })
}

export function getExample(params) {
  return request({
    url: _thisApi + '/topology/nodes',
    method: 'get',
    params,
    headers
  })
}

export function getExampleItem(id) {
  return request({
    url: _thisApi + '/topology/nodes/' + id,
    method: 'get',
    headers
  })
}

export function editExample(id, data) {
  return request({
    url: _thisApi + '/topology/nodes/' + id,
    method: 'PUT',
    data,
    headers
  })
}

export function powerOn(id) {
  return request({
    url: _thisApi + '/topology/nodes/' + id + '/start',
    method: 'post',
    headers
  })
}

export function powerOff(id) {
  return request({
    url: _thisApi + '/topology/nodes/' + id + '/stop',
    method: 'post',
    headers
  })
}

export function reboot(id) {
  return request({
    url: _thisApi + '/topology/nodes/' + id + '/reboot',
    method: 'post',
    headers
  })
}

export function getIcon(params) {
  return request({
    url: _thisApi + '/icon',
    method: 'get',
    params,
    headers
  })
}
