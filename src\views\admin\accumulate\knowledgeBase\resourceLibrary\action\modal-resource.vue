<template>
  <div v-loading="loading" class="dialog-wrap">
    <el-form ref="form" :model="form" :rules="rules" class="form-wrap" label-position="left" label-width="100px">
      <el-form-item label="来源" prop="source">
        <el-select v-model="form.source" placeholder="请选择">
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="资源类型" prop="type">
        <el-select v-model="form.type" placeholder="请选择">
          <el-option
            v-for="item in options1"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="资源名称" prop="file">
        <el-tag
          v-if="form.file"
          :disable-transitions="true"
          closable
          @click="drawerName = 'selectedFile'"
          @close="form.file = null">
          {{ form.file.realname }}
        </el-tag>
        <el-button v-else type="ghost" @click="drawerName = 'selectedFile'">选择资源</el-button>
      </el-form-item>
      <el-form-item label="知识标签" prop="value">
        <el-select v-model="form.value" placeholder="请选择">
          <el-option
            v-for="item in options2"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="分类" prop="value1">
        <el-select v-model="form.value1" placeholder="请选择">
          <el-option
            v-for="item in options3"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <!-- 侧拉弹窗 start -->
    <el-drawer
      :title="titleMapping[drawerName]"
      :visible.sync="drawerShow"
      :size="drawerWidth"
      append-to-body
      @close="drawerClose"
    >
      <transition name="el-fade-in-linear">
        <component
          :is="drawerName"
          :name="drawerName"
          :data="data"
          @close="drawerClose"
          @call="drawerConfirmCall"
        />
      </transition>
    </el-drawer>
    <!-- 侧拉弹窗 end -->
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>
<script>
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
import validate from '@/packages/validate'
import selectedFile from './select-teacher.vue'
// import { getTimeItems } from '@/views/admin/hrm/TeachingAffairs/utils.js'
// import { insertTeacherScheduling } from '@/api/teachingAffairs/index.js'

export default {
  name: 'Resource',
  components: {
    selectedFile
  },
  mixins: [mixinsActionMenu],
  props: {
    // 传入数据
    data: {
      type: Array,
      default: () => {
        return []
      }
    },
    name: {
      type: String
    }
  },
  data() {
    return {
      drawerAction: ['selectedFile'], // 需要侧拉打开的操作
      titleMapping: {
        'selectedFile': '选择文件'
      },
      loading: false,
      submitLoading: false,
      validate: validate,
      form: {
        source: '',
        type: '',
        file: '',
        value: '',
        value1: ''
      },
      rules: {
        source: [validate.required(['blur', 'change'])],
        type: [validate.required(['blur', 'change'])],
        file: [validate.required(['blur', 'change'])],
        value: [validate.required(['blur', 'change'])],
        value1: [validate.required(['blur', 'change'])]
      },
      options: [],
      options1: [],
      options2: [],
      options3: []
    }
  },
  computed: {
  },
  watch: {
  },
  mounted() {
  },
  methods: {
    close: function() {
      this.$emit('close')
    },
    confirm: function() {
      this.$refs['form'].validate((valid, object) => {
        if (valid) {
          // this.submitLoading = true
        }
      })
    },
    drawerConfirmCall: function(type, data) {
      if (type === 'close') {
        this.drawerClose()
      } else if (type === 'confirm_teacher') {
        this.form.file = data[0]
        this.drawerClose()
        this.$nextTick(() => {
          this.$refs['form'].clearValidate('file')
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.pointer {
  cursor: pointer;
}
.ellipsis {
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
/deep/ .el-button--ghost {
    border: 1px dashed #c8cacd !important;
}
/deep/ .el-table__cell {
  padding: 0;
}
/deep/ .el-tooltip__popper {
    max-width: 300px;
}
/deep/ .el-form .tableData .el-form-item:not(.is-error) {
  margin-bottom: 0;
}
</style>
