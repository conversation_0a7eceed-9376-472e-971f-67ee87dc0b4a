export default {
  data() {
    return {
      createStatus: ['shutoff', 'running'],
      deleteStatus: ['shutoff', 'running', 'paused', 'suspended']
    }
  },
  computed: {
    canCreate: function() {
      return !this.resourceData.task && this.createStatus.indexOf(this.resourceData.status.toLowerCase()) > -1
    },
    canDelete: function() {
      return !this.resourceData.task && this.deleteStatus.indexOf(this.resourceData.status.toLowerCase()) > -1
    }
  },
  methods: {
    'noActive': function(status) {
      const statusList = ['deleting', 'creating', 'error_deleting', 'updating', 'unmanaging', 'deleted']
      return statusList.includes(status)
    }
  }
}
