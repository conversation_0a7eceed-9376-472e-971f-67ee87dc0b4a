<template>
  <el-row :gutter="20">
    <el-col :span="12">
      <detail-card title="基本信息">
        <el-form slot="content" label-position="left" label-width="130px">
          <el-form-item label="用户：">
            {{ data.user || '-' }}
          </el-form-item>
          <el-form-item label="设备名称：">
            {{ data.asset || '-' }}
          </el-form-item>
          <el-form-item label="账号：">
            {{ data.account || '-' }}
          </el-form-item>
          <el-form-item label="协议：">
            {{ data.protocol || '-' }}
          </el-form-item>
          <el-form-item label="登录来源：">
            {{ data.loginFrom || '-' }}
          </el-form-item>
          <el-form-item label="开始日期：">
            {{ data.dateStart || '-' }}
          </el-form-item>
          <el-form-item label="结束日期：">
            {{ data.dateEnd || '-' }}
          </el-form-item>
        </el-form>
      </detail-card>
    </el-col>
    <el-col :span="12">
      <detail-card title="命令">
        <template slot="content">
          <t-table-view
            ref="tableView"
            :height="height"
            :single="single"
            :loading="tableLoading"
            :data="tableData"
            :total="tableTotal"
            :page-size="pageSize"
            :current="pageCurrent"
            :select-item="selectItem"
            :multiple-page="false"
            type="list"
            current-key="id"
            @on-select="onSelect"
            @on-current="onCurrent"
            @on-change="changePage"
            @on-sort-change="onSortChange"
            @on-page-size-change="onPageSizeChange"
          >
            <el-table-column
              v-for="item in columnsViewArr"
              :key="item"
              :min-width="colMinWidth"
              :label="columnsObj[item].title"
              :fixed="columnsObj[item].master ? 'left' : false"
              show-overflow-tooltip
            >
              <template slot-scope="scope">
                <span>{{ scope.row[item] || "-" }}</span>
              </template>
            </el-table-column>
          </t-table-view>
        </template>
      </detail-card>
    </el-col>
  </el-row>
</template>

<script>
import detailCard from '@/packages/detail-view/detail-card.vue'
import mixinsPageTable from '@/packages/mixins/page_table'
import tTableView from '@/packages/table-view/index.vue'
export default {
  name: 'DetailOverview',
  components: {
    detailCard,
    tTableView
  },
  mixins: [mixinsPageTable],
  props: {
    id: {
      type: [Number, String]
    },
    data: {
      type: Object
    }
  },
  data() {
    return {
      columnsObj: {
        'id': {
          title: 'ID', master: true
        },
        'output': {
          title: '命令'
        },
        'timestamp': {
          title: '日期'
        }
      },
      columnsViewArr: [
        'id',
        'output',
        'timestamp'
      ]
    }
  },
  methods: {
    getList: function(showLoading = true) {
      this.tableData = [
        { id: 1, output: 'rpm -i cdm Enter', timestamp: '2025-03-19 18:31:51' },
        { id: 2, output: 'ShiftTdology.com123 Enter', timestamp: '2025-03-21 18:31:51' },
        { id: 3, output: 'su root Enter', timestamp: '2025-04-19 18:31:51' }
      ]
      this.tableTotal = this.tableData.length
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .detail-card-body {
  max-height: 65vh;
  overflow-y: auto;
}
</style>
