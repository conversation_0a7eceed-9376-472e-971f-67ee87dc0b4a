export default {
  props: {
    // 高度配置，默认为自适应外层高度，传null则不控制高度，也可传数字控制
    height: {
      type: [String, Number],
      default: 'auto'
    },
    // 是否存在分页
    multiplePage: {
      type: Boolean,
      default: true
    },
    // 默认选中数据，根据defaultSelectedKey识别，每次初始化列表数据后与该数据中匹配会默认选中添加到selectItem中
    // 多用于资源选择时显示已选择的项目
    defaultSelectedArr: {
      type: Array,
      default: () => {
        return []
      }
    },
    // 默认不可勾选的数据，根据defaultSelectedKey识别，每次初始化列表数据后与该数据中匹配会将对应数据置灰不可勾选
    // 多用于侧拉资源选择时置灰斗某些不可选择的项目
    notAllowedArr: {
      type: Array,
      default: () => {
        return []
      }
    },
    // 默认选中识别key 默认为id
    defaultSelectedKey: {
      type: String,
      default: 'id'
    },
    // API请求附带过滤参数
    filterData: {
      type: Object
    },
    // 列表内容显示链接
    link: {
      type: Boolean,
      default: true
    },
    // 自定义显示列（定义后将忽略组件默认与用户localStorage记录值）
    customColData: {
      type: Array,
      default: () => {
        return []
      }
    },
    // 自定义隐藏搜索key（使之不可见）
    customHideSerachKey: {
      type: Array,
      default: () => {
        return []
      }
    },
    // 单选模式
    single: {
      type: Boolean,
      default: false
    },
    // 手动初始化数据getList,应用于列表请求前增加筛选条件，比如：云主机目录
    ManualInit: {
      type: Boolean,
      default: false
    },
    // 单选模式下是否禁止选择某些项
    disableOpt: {
      type: Boolean,
      default: false
    },
    // 自定义显示列写入Key
    customLocalKey: {
      type: String,
      default: null
    },
    cachePattern: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // 列最小宽度
      colMinWidth: 160,
      // 当前页数
      pageCurrent: 1,
      // 每页条数
      pageSize: parseInt(localStorage.getItem('page-size')) || 10,
      // 数据表loading可见状态
      tableLoading: false,
      // 数据表总量
      tableTotal: 0,
      // 数据表数据对象
      tableData: [],
      // 数据表数据请求被点击时间戳
      tableClickTime: 0,
      // post请求扩展字段: 检索等
      postDataExtend: {},
      // 搜索数据
      searchData: {},
      // 排序数据对象
      sorting: {},
      // 搜索显示开关
      searchView: false,
      // 已选数据
      selectItem: [],
      // 备份的table数据
      tableDatas: null,
      // 接收组件传入的参数 有其他需求可以直接在 引用的地方对 selectedKey 进行赋值修改
      selectedKey: this.defaultSelectedKey,
      // 特殊的情况时列表主键与禁用的主键不是同一字段 多传入某个参数进行禁用
      notAllowedKey: '',
      // 接收组件传入的参数 有其他需求可以直接在 引用的地方对 convertNotAllowedArr 进行赋值修改
      convertNotAllowedArr: this.notAllowedArr,
      cache: this.cachePattern,
      // 支持范围选择的类型
      supportRangeTime: ['datetimerange', 'daterange', 'monthrange']
    }
  },
  watch: {
  // 监听instance的websocket，只用于来更新画布上node的状态
    'cache': function(nval, oval) {
      if (nval) {
        if (this.$store.state.cache[this.moduleName]) {
          const obj = {}
          for (const key in this.$store.state.cache[this.moduleName]) {
            obj[key] = this.$store.state.cache[this.moduleName][key]
          }
          this.$bus.$emit('cache-search', obj)
        }
      }
    }
  },
  provide() {
    return {
      tableVm: this
    }
  },
  computed: {
    // 可用检索项目 (根据列表勾选显示项目过滤) 4.1.1版本修改为不过滤
    searchKeyListView: function() {
      const out = []
      this.searchKeyList.forEach((item) => {
        if (!this.customHideSerachKey.includes(item['key'])) {
          out.push(item)
        }
      })
      return out
    },
    // 请求数据附带参数
    postData: {
      get: function() {
        const searchData = JSON.parse(JSON.stringify(this.searchData))
        // range选择情况日期时间处理
        this.supportRangeTime.forEach((key) => {
          if (searchData[key]) {
            const arr = searchData[key].split(',')
            searchData.startTime = arr[0]
            searchData.endTime = arr[1]
            delete searchData[key]
          }
        })
        // 兼容目前诸多项目传递 time_range 作为时间选择的情况
        if (searchData.time_range) {
          const arr = searchData.time_range.split(',')
          searchData.startTime = arr[0]
          searchData.endTime = arr[1]
          delete searchData.time_range
        }
        // 多选处理(将'1,2'转换为[1, 2])
        const selectData = this.searchKeyList.filter(item => item.type === 'select')
        for (const key in searchData) {
          if (Object.hasOwnProperty.call(searchData, key)) {
            if (selectData.find(val => val.key === key)) {
              searchData[key] = this.searchData[key].split(',')
            }
          }
        }
        // 返回参数对象 顺序依次为：分页/检索/排序/模块附带参数
        return Object.assign(
          {
            'page': this.pageCurrent,
            'limit': this.pageSize
          },
          this.filterData,
          searchData
        )
      }
    },
    // 本地存储表列配置key
    localColKey() {
      return (this.isAdmin ? 'col_config_admin_' : 'col_config_') + (this.customLocalKey || this.moduleName)
    },
    'searchBtnShowNum': function() { // 搜索项的数量
      if (this.searchView) return null
      return Object.keys(this.searchData).length || null
    }
  },
  methods: {
    'getNfvoPostData': function() {
      const newData = {}
      for (const key in this.postData) {
        if (Object.prototype.toString.call(this.postData[key]) === '[object Array]') {
          newData[key] = this.postData[key].join(',')
        } else {
          newData[key] = this.postData[key]
        }
      }
      newData['offset'] = (this.postData.page - 1) * this.postData.limit
      delete newData['page']
      return newData
    },
    'getPostData': function(page = 'page', limit = 'limit') {
      const newData = {}
      for (const key in this.postData) {
        if (key === 'page') {
          newData[page] = this.postData[key]
        } else if (key === 'limit') {
          newData[limit] = this.postData[key]
        } else {
          newData[key] = this.postData[key]
        }
      }
      return newData
    },
    // 点击打开搜索
    'openSearch': function() {
      this.searchView = !this.searchView
    },
    // 手动刷新列表
    'refresh': function() {
      this.$emit('refresh')
      this.selectItem = []
      if (this.single) {
        this.$emit('on-select', [])
        this.setHighlightRow(null)
      }
      this.getList()
    },
    searchMultiple(data, subKey = '', type = false) { // 触发搜索
      if (!type) {
        this.pageCurrent = 1
      }
      this.searchData = data
      if (this.cache) {
        const obj = {
          data: this.searchData,
          key: this.moduleName
        }
        this.$store.commit('SET_CACHE', obj)
      }
      this.getList()
    },
    // 多选 返回选中数组对象
    'onSelect': function(data) {
      this.$emit('on-select', data)
      this.selectItem = data
    },
    // 单选高亮 返回当前行数据
    'onCurrent': function(row) {
      this.$emit('on-current', row)
    },
    // 点击排序事件
    'onSortChange': function(data) {
      delete data['column']
      if (data.order !== null) {
        // 与原排序不同之处：
        // 1.原来只需在列表展示列增加sortable: 'custom'即可，现在需要在列的定义上增加:sortable="'custom'" prop="name"
        // 2.原来默认排序时 order的值为 normal、asc、desc，现在是 null、ascending、descending
        // 3.原来排序依据的指标是 key，现在是 prop
        this.sorting = {
          prop: data.prop,
          order: data.order === 'ascending' ? 'ASC' : 'DESC'
        }
      } else {
        this.sorting = {}
      }
      // this.sorting = data.order === null ? {} : data
      this.getList()
    },
    // 翻页
    'changePage': function(num) {
      this.pageCurrent = num
      if (this.cache) {
        const obj = {
          data: num,
          key: this.moduleName + 'pageCurrent'
        }
        this.$store.commit('SET_CACHE', obj)
      }
      this.getList()
    },
    // 更改pageSize
    'onPageSizeChange': function(pageSize) {
      localStorage.setItem('page-size', pageSize)
      this.pageSize = pageSize
      if (this.cache) {
        const obj = {
          data: pageSize,
          key: this.moduleName + 'pageSize'
        }
        this.$store.commit('SET_CACHE', obj)
      }
      this.getList()
    },
    'getList': function() {
      console.log(this.postData)
    },
    // 改变列显示触发
    onChangeCol(data, fixData) {
      this.columnsViewArr = data
      this.columnsObj = fixData
      this.$refs.tableView.datekey = Date.now()
      // 存入本地存储
      localStorage.setItem(this.localColKey, JSON.stringify(data))
      localStorage.setItem(this.localColKey + '_fix', JSON.stringify(fixData))
    },
    // 设置列表行高亮 row:高亮行数据 null为清空
    'setHighlightRow': function(row) {
      this.$refs['tableView'].setHighlightRow(row)
    },
    // 列表链接点击事件父级传递 name: 跳转路由名称 row：资源数据 params: 路由参数
    'linkEvent': function(name, row, params) {
      this.$emit('link-event', { name, row, params })
      this.$refs['tableView'].clearSelection()
      this.setHighlightRow(row)
      this.$refs['tableView'].toggleRowSelection(row)
    },
    // 清除选择
    'clearSelection': function() {
      this.$refs['tableView'].clearSelection()
    },
    // getList后调用此方法，重新显示已选中的项目
    'handleSelection': function() {
      if (this.tableTotal && !this.tableData.length && this.pageCurrent !== 1) {
        // 如翻页到无数据输出则返回第一页
        this.pageCurrent = 1
        this.changePage(1)
      } else {
        this.tableLoading = false
      }
      const saveLastData = JSON.parse(JSON.stringify(this.selectItem))
      // 显示默认选中的项目
      if (this.defaultSelectedArr.length) {
        this.tableData.forEach((item) => {
          if (this.defaultSelectedArr.includes(item[this.selectedKey])) {
            window.setTimeout(() => {
              this.setHighlightRow(item)
            }, 200)
          }
        })
      }
      // 不能勾选的项目 增加新判断 禁用判断值是主键 取主键参数 selectedKey 是禁用的值不是主键 notAllowedKey
      if (this.convertNotAllowedArr.length) {
        this.tableData.forEach((item) => {
          if (this.convertNotAllowedArr.includes(item[this.notAllowedKey || this.selectedKey])) {
            this.$set(item, 'custom-selected', true)
          }
        })
      }
      // 重新显示已选中项目
      this.$nextTick(() => {
        const arr = saveLastData.map(item => item[this.selectedKey]).concat(this.defaultSelectedArr)
        this.tableData.forEach((item, index) => {
          if (arr.indexOf(item[this.selectedKey]) > -1) {
            this.$refs['tableView'].toggleRowSelection(this.tableData[index])
          }
        })
        if (this.single) {
          window.setTimeout(() => {
            const data = this.tableData.find(val => val[this.selectedKey] === arr[0])
            if (data) {
              this.setHighlightRow(data)
            } else {
              this.setHighlightRow(null)
              this.$emit('on-select', [])
            }
          }, 200)
        }
      })
    },
    // 处理序号
    indexMethod(index) {
      return (this.pageCurrent - 1) * this.pageSize + index + 1
    }
  },
  mounted() {
    // 事件监听
    this.$bus.$on(this.moduleName + '_module', (type, data) => {
      this.tableDatas = null
      switch (type) {
        case 'reload':
          this.getList(false)
          break
        case 'reloadItem':
          this.reloadItem(data)
          break
        case 'reloadItemBat':
          for (const id in data) {
            this.reloadItem(id)
          }
          break
      }
    })
    // 读取本地存储列设置
    const localColData = JSON.parse(localStorage.getItem(this.localColKey))
    if (this.customColData.length) {
      this.columnsViewArr = this.customColData
    } else if (localColData) {
      this.columnsViewArr = localColData
    }
    const localColDataFix = JSON.parse(localStorage.getItem(this.localColKey + '_fix'))
    if (localColDataFix) {
      this.columnsObj = localColDataFix
    }
    if (this.$refs.tableView) {
      this.$refs.tableView.datekey = Date.now()
    }
    if (this.cache) {
      // 页面缓存回显
      if (this.$store.state.cache[this.moduleName]) {
        if (Object.keys(this.$store.state.cache[this.moduleName]).length !== 0 && !this.$store.state.cache[this.moduleName].searchShow) {
          this.openSearch()
        }
        const obj = {}
        for (const key in this.$store.state.cache[this.moduleName]) {
          if (key != 'searchShow') {
            obj[key] = this.$store.state.cache[this.moduleName][key]
          }
        }
        this.$bus.$emit('cache-search', obj)
      }
    }
    // 路由跳转是否带有搜索参数（params）
    if (this.$route.params.routeSearch && this.$route.params.moduleName && this.$route.params.moduleName === this.moduleName) {
      this.searchView = true
      // 是否携带多个搜索参数
      if (this.$route.params.multiple) {
        this.$bus.$emit('handle-router-search-multiple', this.$route.params.multipleData)
      } else {
        this.$bus.$emit('handle-router-search', { tagValue: this.$route.params.searchVal, tagKey: this.$route.params.searchKey })
      }
      return
    }
    // 路由跳转是否带有搜索参数（query）
    if (this.$route.query.routeSearch && this.$route.query.moduleName && this.$route.query.moduleName === this.moduleName) {
      this.searchView = true
      // 是否携带多个搜索参数
      if (this.$route.query.multiple) {
        this.$bus.$emit('handle-router-search-multiple', this.$route.query.multipleData)
      } else {
        this.$bus.$emit('handle-router-search', { tagValue: this.$route.query.searchVal, tagKey: this.$route.query.searchKey })
      }
      return
    }
    if (!this.cache) {
      this.getList()
    } else if (this.cache && !this.$store.state.cache[this.moduleName]) {
      this.getList()
    }
  },
  created() {
    if (this.cache) {
      this.pageCurrent = parseInt(this.$store.state.cache[this.moduleName + 'pageCurrent']) || 1
      this.pageSize = parseInt(this.$store.state.cache[this.moduleName + 'pageSize']) || parseInt(localStorage.getItem('page-size')) || 10
    }
  },
  beforeDestroy() {
    this.$bus.$off(this.moduleName + '_module')
  }
}
