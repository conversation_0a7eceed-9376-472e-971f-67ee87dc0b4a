<template>
  <div class="drawer-wrap">
    <selected-examPaper
      ref="table"
      :filter-data="{}"
      :height="null"
      :link="false"
      :single="true"
      @on-select="onSelect"
    />
    <div class="drawer-footer">
      <el-button :disabled="!selectedItem.length" type="primary" @click="confirm">确定</el-button>
      <el-button type="text" @click="close">取消</el-button>
    </div>
  </div>
</template>

<script>
import selectedExamPaper from '../selectedExamPaper/index.vue'
export default {
  components: {
    selectedExamPaper
  },
  data() {
    return {
      selectedItem: [],
      notAllowedArr: []
    }
  },
  methods: {
    'onSelect': function(data) {
      this.selectedItem = data
    },
    'close': function() {
      this.$emit('close')
    },
    'confirm': function() {
      this.$emit('call', 'confirm_examPaper', this.selectedItem)
    }
  }
}
</script>

