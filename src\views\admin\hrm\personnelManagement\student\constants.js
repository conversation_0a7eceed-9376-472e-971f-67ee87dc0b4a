// 课程类型
export const contentTypeArr = [
  { value: 1, label: '理论' },
  { value: 2, label: '仿真' }
]
export const contentTypeObj = contentTypeArr.reduce((acc, prev) => {
  acc[prev.value] = prev.label
  return acc
}, {})

// 学习进度
export const courseFinishArr = [
  { value: '0', label: '未完成' },
  { value: '1', label: '已完成' }
]
export const courseFinishObj = courseFinishArr.reduce((acc, prev) => {
  acc[prev.value] = prev.label
  return acc
}, {})

export const learnTypeArr = [
  { value: '0', label: '必修课' },
  { value: '1', label: '自学课' }
]
export const courseTypeArr = [
  { value: '0', label: '班级课程' },
  { value: '1', label: '学员课程' }
]
