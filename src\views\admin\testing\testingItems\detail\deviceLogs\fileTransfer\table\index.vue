<template>
  <div class="resource-table">
    <!-- 操作区 -->
    <div class="operation-wrap">
      <div class="operation-left">
        <slot name="action" />
        <el-button type="primary" icon="el-icon-refresh" @click="refresh" />
      </div>
      <div class="operation-right">
        <el-badge :value="searchBtnShowNum">
          <el-button icon="el-icon-search" @click="openSearch">搜索</el-button>
        </el-badge>
        <!-- 自定义表格列 -->
        <t-table-config
          v-if="!customColData.length"
          :data="columnsObj"
          :active-key-arr="columnsViewArr"
          @on-change-col="onChangeCol"
        />
      </div>
    </div>
    <!-- 搜索区 -->
    <t-search-box
      v-show="searchView"
      :search-key-list="searchKeyListView"
      default-placeholder="默认搜索测试任务"
      @search="searchMultiple"
    />
    <!-- 列表 -->
    <t-table-view
      ref="tableView"
      :height="height"
      :single="single"
      :loading="tableLoading"
      :data="tableData"
      :total="tableTotal"
      :page-size="pageSize"
      :current="pageCurrent"
      :select-item="selectItem"
      current-key="id"
      @on-select="onSelect"
      @on-current="onCurrent"
      @on-change="changePage"
      @on-sort-change="onSortChange"
      @on-page-size-change="onPageSizeChange"
    >
      <el-table-column
        v-for="item in columnsViewArr"
        :key="item"
        :min-width="colMinWidth"
        :label="columnsObj[item].title"
        :fixed="columnsObj[item].master ? 'left' : false"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span v-if="item === 'isSuccess'">
            {{ isSuccessMapping[scope.row[item]] || '-' }}
          </span>
          <span v-else>{{ scope.row[item] || "-" }}</span>
        </template>
      </el-table-column>
    </t-table-view>
  </div>
</template>
<script>
import tSearchBox from '@/packages/search-box/index.vue'
import mixinsPageTable from '@/packages/mixins/page_table'
import tTableView from '@/packages/table-view/index.vue'
import tTableConfig from '@/packages/table-config/table-col-config.vue'
import tableTdMultiCol from '@/packages/table-config/table-td-multi-col.vue'
import module from '../config.js'
import { getFileTransferRecordApi } from '@/api/testing/index'

export default {
  components: {
    tSearchBox,
    tTableView,
    tTableConfig,
    tableTdMultiCol
  },
  mixins: [mixinsPageTable],
  data() {
    return {
      moduleName: module.name,
      searchKeyList: [
        { key: 'taskName', label: '测试任务', placeholder: '请输入测试任务名称', master: true },
        { key: 'round', label: '测试轮次', placeholder: '请输入测试轮次' },
        { key: 'realName', label: 'ID', placeholder: '请输入ID' },
        { key: 'realName', label: '用户', placeholder: '请输入用户名称' },
        { key: 'remoteAddr', label: '远端地址', placeholder: '请输入远端地址' },
        { key: 'asset', label: '资产', placeholder: '请输入资产' },
        { key: 'account', label: '账号', placeholder: '请输入账号' },
        { key: 'orgId', label: '组织ID', placeholder: '请输入组织ID' },
        { key: 'operate', label: '操作', placeholder: '请输操作' },
        { key: 'filename', label: '文件名称', placeholder: '请输入文件名称' },
        { key: 'isSuccess', label: '成功', placeholder: '请选择', type: 'radio', valueList: [
          { label: '是', value: '1' },
          { label: '否', value: '0' }
        ] },
        { key: 'timeRange', label: '开始日期', type: 'time_range', placeholder: '请选择开始日期' }
      ],
      isSuccessMapping: {
        '1': '是',
        '0': '否'
      },
      columnsObj: {
        'taskName': {
          title: '测试任务', master: true
        },
        'round': {
          title: '测试轮次'
        },
        'id': {
          title: 'ID'
        },
        'realName': {
          title: '用户'
        },
        'remoteAddr': {
          title: '远端地址'
        },
        'asset': {
          title: '资产'
        },
        'account': {
          title: '账号'
        },
        'orgId': {
          title: '组织ID'
        },
        'operate': {
          title: '操作'
        },
        'filename': {
          title: '文件名称'
        },
        'isSuccess': {
          title: '成功'
        },
        'dateStart': {
          title: '开始日期'
        }
      },
      columnsViewArr: [
        'taskName',
        'round',
        'id',
        'realName',
        'remoteAddr',
        'asset',
        'account',
        'orgId',
        'operate',
        'filename',
        'isSuccess',
        'dateStart'
      ]
    }
  },
  methods: {
    getList: function(showLoading = true) {
      if (showLoading) {
        this.tableLoading = true
      }
      const params = this.getPostData('page', 'limit')
      params.projectId = this.$route.params.id
      if (params.timeRange) {
        params.dateStart = params.timeRange.split(',')[0]
        params.dateEnd = params.timeRange.split(',')[1]
        delete params.timeRange
      }
      getFileTransferRecordApi(params).then((res) => {
        this.tableData = (res.data && res.data.data) ? res.data.data.records : []
        this.tableTotal = res.data.data.total || 0
        this.tableLoading = false
        this.handleSelection()
      }).catch(() => {
        this.tableLoading = false
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.resource-table {
  height: 100%;
  padding: 15px 0 0 0;
}
</style>
