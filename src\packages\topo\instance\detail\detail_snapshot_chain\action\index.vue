<template>
  <div class="buttons-wrap detail-action-button">
    <el-button :disabled="!createAvailable" type="primary" @click="modalName = 'createSnapshot'" >创建快照</el-button>
    <el-button :disabled="singleDisabled" type="primary" @click="modalName = 'resumeSnapshot'" >恢复快照</el-button>
    <el-button :disabled="multipleDisabled" type="primary" @click="modalName = 'snapshotDel'" >删除</el-button>
    <el-dialog
      :title="titleMap[modalName]"
      :visible.sync="modalShow"
      :width="modalWidth"
      append-to-body
      @close="modalClose"
    >
      <transition name="el-fade-in-linear">
        <component
          :is="modalName"
          :data="selectItem"
          :active="modalName"
          :resource-id="resourceId"
          :resource-data="resourceData"
          @close="modalClose"
          @call="modalConfirmCall"
        />
      </transition>
    </el-dialog>
  </div>
</template>
<script>
import mixinsActionMenu from '../../../../mixins/action_menu.js'
import createSnapshot from '../../../../action/modal_create_snapshot.vue'
import snapshotDel from './modal_delete.vue'
import resumeSnapshot from './modal_resume_snapshot.vue'
export default {
  components: {
    createSnapshot,
    snapshotDel,
    resumeSnapshot
  },
  mixins: [
    mixinsActionMenu
  ],
  props: {
    selectItem: {
      type: Array,
      default: () => []
    },
    resourceId: {
      type: String
    },
    resourceData: {
      type: Object
    },
    type: {
      type: String
    }
  },
  data() {
    return {
      titleMap: {
        'createSnapshot': '创建快照',
        'resumeSnapshot': '恢复快照',
        'snapshotDel': '删除'
      }
    }
  },
  computed: {
    createAvailable() {
      const snapshotStatus = ['shutoff', 'running']
      return !this.resourceData.task && snapshotStatus.indexOf(this.resourceData.status.toLowerCase()) > -1
    }
  },
  methods: {
    // 模态框动态组件确定回调 {type}返回类型，规范为保持与模态框名称一致，{data}返回确认数据
    'modalConfirmCall': function(type, data) {
      if (type === 'close') {
        this.modalClose()
      }
    }
  }
}
</script>
