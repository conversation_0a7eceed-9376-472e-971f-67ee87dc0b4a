import CryptoJS from 'crypto-js'

const key = CryptoJS.enc.Utf8.parse('HfkdIzPG4/eZD0S=') // 16位的密钥
const iv = CryptoJS.enc.Utf8.parse('TBawpOJlNkb5K+9=') // 16位的初始化向量

export default {
  encrypt(word) {
    const srcs = CryptoJS.enc.Utf8.parse(word)
    const encrypted = CryptoJS.AES.encrypt(srcs, key, { iv: iv, mode: CryptoJS.mode.CBC, padding: CryptoJS.pad.Pkcs7 })
    return encrypted.toString()
  },
  decrypt(word) {
    const decrypt = CryptoJS.AES.decrypt(word, key, { iv: iv, mode: CryptoJS.mode.CBC, padding: CryptoJS.pad.Pkcs7 })
    return decrypt.toString(CryptoJS.enc.Utf8)
  }
}
