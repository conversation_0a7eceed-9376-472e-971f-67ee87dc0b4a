<template>
  <div class="resource-table">
    <top-detail
      :datas="topDetailData"
    />
    <!-- 操作区 -->
    <div class="operation-wrap">
      <div class="operation-left">
        <slot name="action" />
        <span v-if="options[0].className!==null">
          班级：<el-select v-model="value" filterable placeholder="请选择班级" @change="changeValue">
            <el-option
              v-for="(item,index) in options"
              :key="item.className"
              :label="item.className"
              :value="index"/>
          </el-select>
        </span>
        <el-button type="primary" icon="el-icon-refresh" @click="refresh" />
      </div>
      <div class="operation-right">
        <!-- 自定义表格列 -->
        <t-table-config
          v-if="!customColData.length"
          :data="columnsObj"
          :active-key-arr="columnsViewArr"
          @on-change-col="onChangeCol"
        />
      </div>
    </div>
    <!-- 列表 -->
    <t-table-view
      ref="tableView"
      :height="height"
      :single="true"
      :loading="tableLoading"
      :data="examQuestionList"
      :total="tableTotal"
      :page-size="pageSize"
      :current="pageCurrent"
      :select-item="selectItem"
      :multiple-page="false"
      :type="'list'"
      current-key="userId"
      @on-select="onSelect"
      @on-current="onCurrent"
      @on-change="changePage"
      @on-sort-change="onSortChange"
      @on-page-size-change="onPageSizeChange"
    >
      <el-table-column v-for="item in columnsViewArr" :key="item" :min-width="colMinWidth" :label="columnsObj[item].title" :fixed="columnsObj[item].master ? 'left' : false" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="item ==='groupCodeState'">{{ scope.row[item] !== null?(scope.row[item]==0? '未出勤' : '已出勤'):"-" }}</span>
          <span v-else >{{ scope.row[item] !== null?scope.row[item]:"-" }}</span>
        </template>
      </el-table-column>
    </t-table-view>
  </div>
</template>

<script>
import module from '../config.js'
import topDetail from '../../components/top-detail.vue'
import tSearchBox from '@/packages/search-box/index.vue'
import tTableView from '@/packages/table-view/index.vue'
import tTableConfig from '@/packages/table-config/table-col-config.vue'
import tableTdMultiCol from '@/packages/table-config/table-td-multi-col.vue'
import mixinsPageTable from '@/packages/mixins/page_table'
import { searchAffairTeachingTaskAPI } from '@/api/teacher/index.js'

export default {
  components: {
    tSearchBox,
    tTableView,
    tTableConfig,
    tableTdMultiCol,
    topDetail
  },
  mixins: [mixinsPageTable],
  data() {
    return {
      moduleName: module.name,
      // 搜索配置项
      // 所有可配置显示列 master：不可隐藏 title:列名称
      columnsObj: {
        'realname': {
          title: '学生姓名',
          master: true
        },
        'groupName': {
          title: '所在小组'
        },
        'groupUserScore': {
          title: '个人成绩'
        },
        'groupCodeState': {
          title: '出勤状态'
        }
      },
      // 当前显示列key表 默认，如果localStorage有数据将被覆盖
      columnsViewArr: [
        'realname',
        'groupName',
        'groupUserScore',
        'groupCodeState'
      ],
      pageSize: this.pageSize,
      pageNum: this.pageCurrent,
      examQuestionList: [],
      topDetailData: this.$route.query,
      contentTypeList: ['理论', '仿真'],
      difficultyList: ['初级', '中级', '高级'],
      options: [],
      changeData: null,
      value: 0,
      taskCode: '',
      schedulingCode: ''
    }
  },
  watch: {
    '$route': function(to, from) {
      this.topDetailData = this.$route.query
      this.options = JSON.parse(localStorage.getItem(`manageAffairs_resultList`) || '[]')
      if (![undefined, null, ''].includes(this.options[0].className)) {
        this.changeData = this.options[0]
      }
      this.getList()
    }
  },
  created() {
    this.options = JSON.parse(localStorage.getItem(`manageAffairs_resultList`) || '[]')
    if (![undefined, null, ''].includes(this.options[0].className)) {
      this.changeData = this.options[0]
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    gosee(row) {
      if (row.realNames) {
        this.$router.push({
          path: '/teacher/educational/tasks/details',
          query: {
            groupCode: row.groupCode,
            taskCode: this.curriculumCode,
            upAndDown: 0,
            taskName: this.$route.query.content,
            schedulingCode: this.$route.query.schedulingCode,
            resultList: this.options
          }
        })
      } else {
        this.$message.error('该小组没有加入学员，无法查看详情')
      }
    },
    getList: function(showLoading = true, changeData) {
      if (changeData) { this.changeData = changeData }
      const resultList = this.changeData instanceof Array ? [...this.changeData] : [this.changeData]
      if (showLoading) {
        this.tableLoading = true
      }
      this.topDetailData = this.$route.query
      const query = {
        classCode: this.$route.query.classCode,
        schedulingCode: this.$route.query.schedulingCode,
        curriculumCode: this.$route.query.curriculumCode,
        resultList: resultList }
      searchAffairTeachingTaskAPI(query).then(res => {
        this.examQuestionList = res.data.teacherTaskGroupUserVoList
        this.tableLoading = false
        this.schedulingCode = res.data.schedulingCode
        this.curriculumCode = res.data.curriculumCode
        this.tableTotal = res.data.teacherTaskGroupUserVoList.length
      }).catch(() => {
        this.tableLoading = false
      })
    },
    // 选择班级
    changeValue(val) {
      this.$emit('changeClass', this.options[val])
    },
    classificationType(param) {
      this.queryParameters = param
      this.getList()
    },
    // 查看
    handleDetailClick(row, type) {
      const { contentType, name, id } = row
      let path = ''
      if (contentType === '1') {
        path = '/teacher/testPaper/contain/basics'
      } else {
        path = '/teacher/testPaper/standAlone/basics'
      }
      if (!path) {
        return
      }
      this.$router.push({
        path,
        query: {
          curriculumName: '我的内容', oneLevelTitle: '课程管理', oneLevelName: 'teacherTeaching', twoLevelTitle: '课程详情', twoLevelName: 'courseDetailLibrary', courseId: this.$route.query.courseId,
          teachingName: name,
          id
        }
      })
    }

  }
}
</script>
