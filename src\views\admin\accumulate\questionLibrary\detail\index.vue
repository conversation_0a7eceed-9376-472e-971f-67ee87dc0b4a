<template>
  <detail-view
    :loading="loading"
    :data="data"
    :id="id"
    :view-item="viewItem"
    mode="drawer"
    title-key="questionDepotName"
  />
</template>
<script>
import moduleConf from '../config'
import detailView from '@/packages/detail-view/index'
import detailOverview from './detail-overview'
import detailQuestionBank from './questionBank/index.vue'
import { getQuestionDepot } from '@/api/accumulate/questionLibrary'

export default {
  components: {
    detailView,
    detailOverview,
    detailQuestionBank
  },
  data() {
    return {
      moduleName: moduleConf.name,
      id: null, // 资源ID
      data: null, // 资源数据对象
      questionList: [], // 试卷下题目列表
      loading: true,
      viewItem: [
        {
          transName: '概况',
          name: 'overview',
          component: detailOverview
        },
        {
          transName: '题目管理',
          name: 'questionBank',
          component: detailQuestionBank
        }
      ]
    }
  },
  provide() {
    return {
      'questionBankDeatail': this
    }
  },
  watch: {
    '$route': function(to, from) {
      const toId = to.params.hasOwnProperty('id') ? to.params.id : null
      const fromId = from.params.hasOwnProperty('id') ? from.params.id : null
      if (toId !== fromId) {
        this.loadBase()
      }
    }
  },
  created() {
    this.loadBase()
  },
  methods: {
    'loadBase': function() {
      this.id = this.$route.params.id
      this.loading = true
      const result = [this.getData(this.id)]
      Promise.all(result).then(() => {
        this.loading = false
      }).finally(() => {
        this.loading = false
      })
    },
    // 根据id获取详情数据
    'getData': function(id) {
      return new Promise((resolve, reject) => {
        getQuestionDepot({ id: id }).then(res => {
          this.data = res['data']
          resolve()
        }).catch(() => {
          this.data = null
          reject()
        })
      })
    }
  }
}
</script>
