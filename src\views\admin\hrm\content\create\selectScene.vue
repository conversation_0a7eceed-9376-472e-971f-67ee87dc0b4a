<template>
  <div class="select-sence-wrap">
    <!-- 搜索 -->
    <el-card class="select-sence-search mb-10">
      <el-select
        v-model="sceneTypeId"
        placeholder="请选择场景类别"
        style="width: 200px;"
        clearable
        @change="search">
        <el-option
          v-for="item in sceneTypeList"
          :key="item.id"
          :label="item.typeName"
          :value="item.id"/>
      </el-select>
      <el-select
        v-model="sceneDifficulty"
        placeholder="请选择场景难度"
        style="width: 200px;"
        clearable
        @change="search">
        <el-option
          v-for="(value, key) in difficultyObj"
          :key="key"
          :label="value.label"
          :value="key"
        />
      </el-select>
      <el-input
        v-model.trim="sceneName"
        placeholder="请输入名称"
        style="width: 200px;"
        @change="search" />
    </el-card>
    <!-- 列表卡片 -->
    <div v-loading="tableLoading" class="select-sence-list-wrap">
      <div v-if="tableData.length" class="select-sence-list">
        <el-row :gutter="10">
          <el-col v-for="item in tableData" :key="item.id" :span="12">
            <el-card
              :class="{ 'is-active': sceneData && sceneData.id == item.id }"
              class="mb-10"
              @click.native="onSelect(item)">
              <i v-if="editBack" class="el-icon-error" @click.stop="editBack = false,getSceneTypePage()"/>
              <div class="sence-list-item-top">
                <el-image
                  v-if="item.imageUrl"
                  :src="item.imageUrl"
                  :preview-src-list="[item.imageUrl]"
                  style="width: 55%;"
                  lazy/>
                <div v-else class="topology-null-text" style="width: 55%;">暂无拓扑图</div>
                <div style="width: 45%;">
                  <div v-overflow-tooltip class="ellipsis">类别：{{ sceneTypeList.filter(s => { return s.id === item.sceneTypeId }).length ? sceneTypeList.filter(s => { return s.id === item.sceneTypeId })[0].typeName : '-' }}</div>
                  <div v-overflow-tooltip class="ellipsis">难度：{{ difficultyObj[item.sceneDifficulty].label }}</div>
                  <div v-overflow-tooltip class="ellipsis">推荐时长：{{ item.suggestTime }}小时</div>
                  <div v-overflow-tooltip class="ellipsis">节点规模：{{ item.nodeScale }}</div>
                </div>
              </div>
              <strong v-overflow-tooltip class="ellipsis">{{ item.sceneName }}</strong>
              <div v-overflow-tooltip class="ellipsis">场景描述：{{ item.sceneDesc || '-' }}</div>
              <!-- <el-button type="text" @click="$router.push({ name: 'scenement_detail', params: { id: item.id, view: 'overview' } })">查看详情</el-button> -->
            </el-card>
          </el-col>
        </el-row>
      </div>
      <div v-else class="sence-no-data">暂无可用场景</div>
    </div>
    <!-- 分页 -->
    <div class="data-table-footer">
      <el-pagination
        :current-page.sync="page"
        :page-sizes="[10, 20, 30, 40, 50, 100, 200]"
        :pager-count="5"
        :page-size="limit"
        :total="tableTotal"
        background
        class="page-wrap"
        layout="sizes, prev, pager, next"
        @size-change="onPageSizeChange"
        @current-change="changePage"
      />
      <div class="data-table-total">
        共 {{ tableTotal }} 条
      </div>
    </div>
  </div>
</template>

<script>
import moduleConf from '../config'
import { sceneQueryPageWebAPI, sceneTypePageAPI } from '@/api/topo.js'
export default {
  props: {
    sourceSceneIdStr: {
      type: String,
      default: ''
    },
    topologyMode: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      loading: false,
      sceneName: '', // 搜索场景名称
      sceneDifficulty: '', // 搜索场景难度
      sceneTypeId: '', // 搜索场景类别
      difficultyObj: moduleConf.difficultyObj, // 场景难度map数据
      page: 1, // 当前页码
      limit: 10, // 单页数据量
      tableLoading: false, // 数据表loading可见状态
      tableData: [], // 数据表数据对象
      tableTotal: 0, // 数据表总量
      sceneTypeList: [],
      withoutIds: [],
      sceneData: null,
      editBack: false, // 回显
      once: 0 // 第一次
    }
  },
  created() {
    this.getSceneTypePage()
  },
  methods: {
    'search': function() {
      this.page = 1
      this.getList()
    },
    // 翻页
    'changePage': function(num) {
      this.page = num
      this.getList()
    },
    // 更改pageSize
    'onPageSizeChange': function(pageSize) {
      this.limit = pageSize
      this.getList()
    },
    getSceneTypePage() {
      sceneTypePageAPI({ pageType: 0 }).then(res => {
        this.sceneTypeList = res.data.records
        this.getList()
      })
    },
    'onSelect': function(val) {
      this.sceneData = val
      this.$emit('on-select', val)
    },
    getList: function(showLoading = true) {
      if (showLoading) {
        this.tableLoading = true
      }
      const params = {
        page: this.page,
        limit: this.limit,
        sceneDifficulty: this.sceneDifficulty,
        sceneTypeId: this.sceneTypeId,
        sceneName: this.sceneName
      }
      this.once++
      sceneQueryPageWebAPI(params).then(res => {
        // 重新显示已选中项目
        if (this.sourceSceneIdStr && this.once == 1) {
          this.editBack = true
        }
        // 仿真共享选择场景
        if (this.topologyMode == '1') {
          this.editBack = false
        }
        if (this.editBack == true) {
          this.tableData = res.data.records.filter(item => item.id == this.sourceSceneIdStr)
          this.tableTotal = Number(this.tableData.length)
        } else {
          this.tableData = res.data.records
          this.tableTotal = Number(res.data.total)
        }
        this.tableLoading = false
        this.sceneData = { id: this.sourceSceneIdStr }
      }).catch(() => {
        this.tableLoading = false
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.select-sence-wrap {
  height: 100%;
  display: flex;
  flex-direction: column;
  .select-sence-search {
    /deep/ .el-card__body {
      padding: 12px;
    }
  }
  .select-sence-list-wrap {
    flex: 1;
    min-height: 0;
    overflow-y: auto;
  }
  .select-sence-list {
    overflow: hidden;
    /deep/ .el-card {
      &:hover, &.is-active {
        border-color: var(--color-600);
      }
    }
    /deep/ .el-card__body {
      display: flex;
      flex-direction: column;
      padding: 10px 20px;
      position: relative;
      .el-icon-error{
        position:absolute;
        right:4px;
        top:4px;
        cursor:pointer
      }
      .sence-list-item-top {
        display: flex;
        .el-image, .topology-null-text {
          margin-right: 15px;
          border: 1px solid var(--neutral-300);
        }
        .topology-null-text {
          display: flex;
          justify-content: center;
          align-items: center;
          text-align: center;
          font-weight: 500;
          font-size: 14px
        }
        >div {
          height: 110px;
        }
      }
      div {
        margin-top: 5px;
        margin-bottom: 5px;
        font-size: 13px;
      }
      .el-button {
        position: absolute;
        right: 10px;
        top: 2px;
        font-size: 12px;
      }
    }
  }
  .sence-no-data {
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .data-table-footer {
    position: relative;
    margin-top: 10px;
    height: 32px;
    .page-wrap {
      position: absolute;
      right: 5px;
    }
    .data-table-total {
      position: absolute;
      left: 15px;
      line-height: 32px;
    }
  }
}
</style>
