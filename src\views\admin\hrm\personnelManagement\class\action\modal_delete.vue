<template>
  <div v-loading="loading" class="dialog-wrap">
    <el-alert :closable="false" type="warning">
      <div slot="title">
        <p>此操作将永久删除该班级，并移除班级内全部学员是否继续?</p >
      </div>
    </el-alert>
    <batch-template
      :data="data"
      :available-data="data"
      view-key="majorName"
    />
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button :disabled="!data.length" type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>

<script>
import batchTemplate from '@/packages/batch-delete/modal-bat-template.vue'
import modalMixins from '@/packages/mixins/modal_form'
import { deleteClass } from '@/api/admin/training/student'

export default {
  components: {
    batchTemplate
  },
  mixins: [modalMixins],
  props: {
    data: {
      type: Array,
      default: () => {
        return []
      }
    },
    professionlMajorName: {
      type: String || Number,
      default: () => {
        return ''
      }
    }
  },
  data() {
    return {
      loading: false
    }
  },
  methods: {
    close() {
      this.$emit('close')
    },
    saveJoinCourse(postData) {
      return new Promise((resolve, reject) => {
        deleteClass(postData).then(res => {
          resolve(res)
        })
      })
    },
    confirm: function() {
      const idArr = this.data.map(item => {
        return { id: item.id, majorCode: item.majorCode, majorName: item.majorName, professionalName: item.professionalName }
      })
      idArr.map((item, index) => {
        this.saveJoinCourse({ id: item.id, majorCode: item.majorCode, className: item.majorName, majorName: this.professionlMajorName })
          .then((res) => {
            this.$message.success(res.data.object)
          })
      })
      setTimeout(() => {
        this.$emit('call', 'refresh')
        this.close()
        this.loading = false
      }, 500)
    }
  }
}
</script>
