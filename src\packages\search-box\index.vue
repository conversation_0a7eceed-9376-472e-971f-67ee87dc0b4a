<template>
  <div class="t-search-box" tabindex="0" hidefocus="true">
    <div class="t-search-box-left-part">
      <!-- 已选标签 start -->
      <div
        v-for="(tagValue, tagKey) in searchObj"
        :key="tagKey"
      >
        <search-input
          v-if="editKey === tagKey"
          ref="editInput"
          :edit-key="editKey"
          :search-obj="searchObj"
          :default-placeholder="defaultPlaceholder"
          :search-key-list="searchKeyList"
          :input-max-length="inputMaxLength"
          @addSearchItem="addSearchItem"
          @editItem="editSearchItem"
        />
        <el-tag
          v-else
          class="search-tag"
          size="small"
          disable-transitions
          closable
          @click.stop="editSearchItemHandler(tagKey)"
          @close="deleteSearchItem(tagKey)"
        >
          <span :title="reTagText(tagValue, tagKey)" class="tag-wrap">
            {{ reTagText(tagValue, tagKey) }}
          </span>
        </el-tag>
      </div>
      <!-- 已选标签 end -->
      <search-input
        ref="createInput"
        :search-obj="searchObj"
        :default-placeholder="defaultPlaceholder"
        :search-key-list="searchKeyList"
        :input-max-length="inputMaxLength"
        @addSearchItem="addSearchItem"
      />
    </div>
    <div class="t-search-box-right-part">
      <i class="handle-btn el-icon-delete" @click="deleteSearchItem('delete-all')" />
      <el-divider direction="vertical" />
      <i class="handle-btn el-icon-search" @click="searchHanlder" />
    </div>
  </div>
</template>
<style lang="less">
.t-search-box {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 4px 10px;
  background-color: #fff;
  border: 1px solid #dbdde0;
  border-radius: 2px;
  transition: all 0.3s;
  outline: 0;
  margin-bottom: 15px;
  &:focus, &:hover {
    border-color: var(--color-600);
  }
  .t-search-box-left-part {
    flex: 1 1;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    overflow: hidden;
    .el-date-editor .el-range-input {
      font-size: 14px;
    }
    .tag-wrap {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      display: inline-block;
      vertical-align: middle;
      margin-top: -2px;
      max-width: 100%;
    }
    .search-input-wrap{
      display: flex;
      min-width: 300px;
      flex: 1 0 auto;
      padding: 0 8px;
      .search-value-wrap {
        flex: 1;
        .el-popover.el-popper {
          top: 30px;
        }
        .el-icon-time {
          display: none;
        }
        .el-range-separator {
          display: none;
        }
        .el-range-input {
          text-align: left;
          width: 175px;
        }
      }
      .search-key-text {
        vertical-align: middle;
        line-height: 24px;
        font-size: 14px;
      }
      .el-input__inner {
        border-color: transparent;
        appearance: none;
        -webkit-appearance: none;
        padding: 0;
        font-size: 14px;
        height: 24px;
        line-height: 24px;
      }
    }
    .search-input {
      flex: 1 1;
    }
    .search-tag {
      font-size: 14px;
      border-radius: 2px;
      padding-right: 25px;
      margin: 2px 4px 2px 0px;
      max-width: 300px;
      position: relative;
      cursor: pointer;
      .el-tag__close {
        position: absolute;
        right: 5px;
        top: 3px;
      }
    }
  }
  .t-search-box-right-part {
    flex: 0 0 auto;
    padding: 0 0 0 10px;
    .handle-btn {
      font-size: 14px;
      vertical-align: middle;
      cursor: pointer;
      &:hover {
        color: var(--color-600);
      }
    }
  }
}
.filter-input {
  margin-bottom: 5px;
  .el-input__inner {
    border-color: #DCDFE6 !important;
    padding: 0 10px !important;
    font-size: 12px !important;
    height: 28px !important;
    line-height: 28px !important;
  }
}
</style>
<script>
import lodash from 'lodash'
import searchInput from './search-input.vue'
export default {
  components: {
    searchInput
  },
  props: {
    // 搜索框placeholder
    defaultPlaceholder: {
      type: String,
      default: '默认搜索名称'
    },
    // 搜索key数据
    searchKeyList: {
      type: Array,
      default: () => {
        return []
      }
    },
    // 输入框最大允许输入字符长度
    inputMaxLength: {
      type: Number,
      default: 64
    }
  },
  data() {
    return {
      // 正在编辑key
      editKey: null,
      // 检索key:value集合
      searchObj: {}
    }
  },
  mounted() {
    // 路由跳转带有单个搜索参数
    this.$bus.$on('handle-router-search', (data) => {
      this.searchObj = {}
      this.$set(this.searchObj, data.tagKey, data.tagValue)
      this.$el.focus()
      this.debounceSearch()
    })
    // 路由跳转带有多个搜索参数
    this.$bus.$on('handle-router-search-multiple', (data) => {
      this.searchObj = {}
      data.forEach((item) => {
        this.$set(this.searchObj, item.searchKey, item.searchVal)
      })
      this.$el.focus()
      this.debounceSearch()
    })
    this.$bus.$on('cache-search', (obj) => {
      this.addSearchItems(obj)
    })
  },
  destroyed() {
    this.$bus.$off('handle-router-search')
    this.$bus.$off('handle-router-search-multiple')
    this.$bus.$off('cache-search')
  },
  methods: {
    'reTagText': function(tagValue, tagKey) {
      const itemObj = this.searchKeyList.find(item => item.key === tagKey)
      if (itemObj.hasOwnProperty('valueList')) {
        return (itemObj.label || tagKey) + '：' + this.transStatus(tagValue, itemObj)
      } else {
        return (itemObj.label || tagKey) + '：' + tagValue
      }
    },
    'debounceSearch': lodash.debounce(function(type) {
      this.$emit('search', this.searchObj, '', type)
    }, 100),
    // 翻译状态
    'transStatus': function(str, itemObj) {
      const viewArray = []
      str.split(',').forEach((item) => {
        viewArray.push(itemObj.valueList.find(val => val.value === item).label)
      })
      return viewArray.join()
    },
    // 点击搜索
    'searchHanlder': function() {
      if (this.$refs.hasOwnProperty('editInput') && this.$refs['editInput'].length) this.$refs['editInput'][0].clearActive()
      if (this.$refs.hasOwnProperty('createInput')) this.$refs['createInput'].clearActive()
      this.debounceSearch()
    },
    addSearchItems(obj) {
      this.searchObj = obj
      this.$el.focus()
      this.debounceSearch(true)
    },
    // 添加检索项目
    'addSearchItem': function({ key, value }) {
      this.$set(this.searchObj, key, value)
      this.$el.focus()
      this.debounceSearch()
    },
    // 编辑检索项目
    'editSearchItem': function({ key, value }) {
      const changed = (value !== this.searchObj[key])
      if (value) {
        this.$set(this.searchObj, key, value)
      } else {
        this.$delete(this.searchObj, key)
      }
      this.editKey = null
      this.$el.focus()
      if (changed) this.debounceSearch()
    },
    // 点击编辑检索项目
    'editSearchItemHandler': function(key) {
      this.editKey = key
    },
    // 删除检索项目
    'deleteSearchItem': function(key) {
      const noKey = Object.keys(this.searchObj).length === 0
      if (key === 'delete-all') {
        this.searchObj = {}
        if (this.$refs.hasOwnProperty('editInput') && this.$refs['editInput'].length) this.$refs['editInput'][0].clearActive()
        if (this.$refs.hasOwnProperty('createInput')) this.$refs['createInput'].clearActive()
      } else {
        this.$delete(this.searchObj, key)
      }
      if (!noKey) {
        this.debounceSearch()
      }
    }
  }
}
</script>
