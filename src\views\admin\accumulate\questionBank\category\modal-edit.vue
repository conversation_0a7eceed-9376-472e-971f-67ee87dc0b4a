<template>
  <div v-loading="loading" class="dialog-wrap">
    <el-form ref="form" :model="formData" :rules="rules" label-position="left" label-width="80px">
      <el-form-item label="修改项" prop="id">
        <el-select v-model="formData.id" style="width: 100%;">
          <el-option
            v-for="item in categoryList"
            :key="item.id"
            :label="item.categoryName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="新名称" prop="categoryName">
        <el-input v-model.trim="formData.categoryName"/>
      </el-form-item>
    </el-form>
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>
<script>
import module from './config.js'
import validate from '@/packages/validate'
import { editCategory } from '@/api/accumulate/category'
export default {
  props: {
    // 传入数据
    categoryList: {
      type: Array,
      default: () => {
        return []
      }
    },
    categoryType: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      moduleName: module.name,
      loading: false,
      validate: validate,
      formData: {
        id: '',
        categoryName: ''
      },
      rules: {
        id: [validate.required('change')],
        categoryName: [validate.required(), validate.base_name]
      }
    }
  },
  methods: {
    close: function() {
      this.$emit('close')
    },
    confirm: function() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.loading = true
          const postData = JSON.parse(JSON.stringify(this.formData))
          postData['categoryType'] = this.categoryType
          editCategory(postData).then(res => {
            this.$message.success('编辑分类成功')
            this.close()
          }).finally(() => {
            this.loading = false
            // 都完成之后（无论成功失败），才去刷新列表
            this.$bus.$emit(this.moduleName + '_module', 'reload')
          })
          // 点击确定，立刻关闭弹窗
        }
      })
    }
  }
}
</script>
