<template>
  <div class="optional-ctf">
    <div class="classification-list">
      <div class="classification-list-title">
        <div>
          题目分类：
        </div>
      </div>
      <div>
        <div :class="examClassValue == 'all' ? 'is-active classification-content' : 'classification-content'" @click="switchExamClass('all')">
          <div>
            全部
          </div>
          <div>
            ({{ data.questionList.length }})
          </div>
        </div>
        <div v-for="(item, index) in data.examClassifys" :key="index" :class="examClassValue == item ? 'is-active classification-content' : 'classification-content'" @click="switchExamClass(item)">
          <div v-overflow-tooltip class="classification-content-overflow">
            {{ item }}
          </div>
          <div>
            ({{ data.questionList.filter(i => { return i.examClassify == item }).length }})
          </div>
        </div>
      </div>
    </div>
    <div class="detail-question-bank">
      <div>
        <!-- 筛选 -->
        <div style="margin: 0 0 10px 0">
          题目类别：
          <el-select v-model="bankType" clearable placeholder="请选择" @change="screenQuestionType()">
            <el-option
              v-for="item in bankTypeList"
              :key="item.value"
              :label="item.name"
              :value="item.value"/>
          </el-select>
          <span class="ml-20"/>
          题型：
          <el-select v-model="questionType" clearable placeholder="请选择" @change="screenQuestionType()">
            <el-option
              v-for="item in questionTypeArr"
              :key="item.value"
              :label="item.label"
              :value="item.value"/>
          </el-select>
        </div>
        <!-- 题目列表 -->
        <div v-if="questionList.length" class="_question_list">
          <div
            v-for="(q, index) in questionList"
            :class="`_question_item`"
            :key="q.id"
          >
            <div>
              <div class="_question_item_content">{{ index + 1 }}.&nbsp;<span v-html="q.content"/></div>
              <div class="_question_item_type">
                <el-tag size="small">{{ questionConf.questionTypObj[q.questionType].label }}</el-tag>
              </div>
            </div>
            <div v-if="q.questionType == 1" class="_question_option">
              <el-radio-group
                v-for="(op, i) in JSON.parse(q.questionOptions)"
                :key="op"
                :value="q.questionAnswer"
                disabled
              >
                <el-radio :label="optionLabel[i]">{{ op }}</el-radio>
              </el-radio-group>
            </div>
            <div v-else-if="q.questionType == 2">
              <el-checkbox-group :value="q.questionAnswer.split('')" disabled>
                <div class="_question_option">
                  <el-checkbox
                    v-for="(op, i) in JSON.parse(q.questionOptions)"
                    :key="op"
                    :label="optionLabel[i]"
                  >{{ op }}</el-checkbox
                  >
                </div>
              </el-checkbox-group>
            </div>
            <div v-else-if="q.questionType == 3" class="_question_option">
              <el-radio-group :value="q.questionAnswer" disabled>
                <el-radio label="A">正确</el-radio>
                <el-radio label="B">错误</el-radio>
              </el-radio-group>
            </div>
            <div v-else class="_question_option">
              <span style="padding-bottom: 20px">{{ q.questionAnswer }}</span>
            </div>
            <div class="_question_score">
              <div class="flex-left">
                <div>该题：<span>{{ q.questionScore }}</span> 分</div>
                <div class="ml-20">
                  <span>备用：</span>
                  <el-radio
                    v-for="item in isSpareArr"
                    :key="item.value"
                    v-model="q.isSpare"
                    :label="item.value"
                    disabled
                    style="margin: 0 20px 0 0 !important;"
                  >{{ item.label }}</el-radio>
                </div>
                <div v-if="q.questionType == 4 || q.questionType == 5" class="ml-20">
                  <span>题目作答方式：</span>
                  <el-radio
                    v-for="item in answeringMethod"
                    :key="item.value"
                    v-model="q.questionAnswerMethod"
                    :label="item.value"
                    disabled
                    style="margin: 0 20px 0 0 !important;"
                  >{{ item.label }}</el-radio>
                </div>
              </div>
              <a href="javascript:;" @click="toDetail(q)">
                查看详情
              </a>
            </div>
          </div>
        </div>
        <el-empty
          v-else
          :image="img"
          :image-size="110"
          style="margin: 100px auto"
          description="暂无数据"
        />
      </div>
    </div>
  </div>
</template>

<script>
import questionConf from '../../questionBank/config.js'
export default {
  props: {
    id: {
      type: [Number, String]
    },
    data: {
      type: Object
    }
  },
  data() {
    return {
      questionConf: questionConf,
      optionLabel: Array.from({ length: 26 }, (_, index) => String.fromCharCode('A'.charCodeAt(0) + index)),
      questionType: '',
      questionList: [],
      examClassValue: 'all',
      questionTypeArr: [],
      bankTypeList: [{
        value: 1,
        name: '理论'
      }, {
        value: 2,
        name: '靶机'
      }, {
        value: 3,
        name: '仿真'
      }],
      bankType: '',
      img: require('@/assets/empty_state.png'),
      isSpareArr: [ // 备用题map
        { label: '是', value: 1 },
        { label: '否', value: 0 }
      ],
      answeringMethod: [
        { label: '提交flag', value: 1 },
        { label: '上传修补包', value: 2 }
      ]
    }
  },
  computed: {
    getScore() {
      let score = 0
      this.data.questionList.forEach(item => {
        // 组合题的分数计算的是各个小题的分数
        if (item.questionType == 10 && item.combinationQuestionBOS) {
          item.combinationQuestionBOS.forEach(comp => {
            comp.content.forEach(con => {
              if (con.questionScore) {
                score += con.questionScore
              }
            })
          })
        } else {
          if (item.questionScore) {
            score = score + item.questionScore
          }
        }
      })
      return score
    }
  },
  mounted() {
    this.screenQuestionType()
    this.handleClick()
  },
  methods: {
    toDetail(q) {
      const name = q.bankType == 1 ? 'theory' : q.bankType == 2 ? 'targetDevice' : 'simulation'
      this.$router.push({ name: `${name}Detail`, params: { id: q.id, view: 'overview' }})
    },
    switchExamClass(item) {
      this.examClassValue = item
      this.questionType = ''
      this.bankType = ''
      this.screenQuestionType()
    },
    screenQuestionType() {
      if (this.questionType && this.bankType) {
        if (this.examClassValue != 'all') {
          this.questionList = this.data.questionList.filter(i => { return i.examClassify == this.examClassValue && i.questionType == this.questionType && i.bankType == this.bankType })
        } else {
          this.questionList = this.data.questionList.filter(i => { return i.questionType == this.questionType && i.bankType == this.bankType })
        }
      } else if (this.questionType) {
        if (this.examClassValue != 'all') {
          this.questionList = this.data.questionList.filter(i => { return i.examClassify == this.examClassValue && i.questionType == this.questionType })
        } else {
          this.questionList = this.data.questionList.filter(i => { return i.questionType == this.questionType })
        }
      } else if (this.bankType) {
        if (this.examClassValue != 'all') {
          this.questionList = this.data.questionList.filter(i => { return i.examClassify == this.examClassValue && i.bankType == this.bankType })
        } else {
          this.questionList = this.data.questionList.filter(i => { return i.bankType == this.bankType })
        }
      } else {
        if (this.examClassValue != 'all') {
          this.questionList = this.data.questionList.filter(i => { return i.examClassify == this.examClassValue })
        } else {
          this.questionList = this.data.questionList
        }
      }
    },
    handleClick() {
      this.questionType = ''
      const typeMap = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
      const questionTypeArr = []
      typeMap.forEach(item => {
        const val = questionConf.questionTypeArr.find(val => val.value == item)
        if (val) {
          questionTypeArr.push(val)
        }
      })
      this.questionTypeArr = questionTypeArr
      this.screenQuestionType()
    }
  }
}
</script>
<style lang="scss" scoped>
.detail-question-bank {
  flex: 1;
  position: relative;
  overflow: auto;
  ._paper_search {
    position: absolute;
    right: 30px;
    display: flex;
    ._paper_search_1 {
      font-size: 14px;
      font-weight: 500;
      color: #333;
    }
  }
  ._question_list {
    ._question_item {
      padding: 15px 20px;
      min-height: 90px;
      background: #ffffff;
      display: flex;
      flex-direction: column;
      font-size: 14px;
      color: #4e5969;
      position: relative;
      border: 1px solid #e5e6eb;
      border-radius: 4px;
      margin-bottom: 10px;
      ._question_option {
        margin-top: 10px;
        margin-left: 15px;
        font-size: 14px;
        color: #4e5969;
        display: flex;
        flex-direction: column;
        line-height: 22px;
        word-break: break-all;
        .el-radio {
          margin-bottom: 8px;
          display: flex;
          align-items: flex-start;
          .el-radio__label {
            font-size: 14px;
            color: #4e5969;
            white-space: normal;
            word-break: break-all;
          }
        }
        .el-checkbox {
          margin-bottom: 8px;
          display: flex;
          align-items: flex-start;
          .el-checkbox__label {
            font-size: 14px;
            color: #4e5969;
            white-space: normal;
            word-break: break-all;
          }
        }
      }
      ._question_score {
        border-top: 1px solid #e5e6eb;
        padding: 10px 0 0 0 ;
        display: flex;
        align-items: center;
        justify-content: space-between;
        ._question_delete {
          color: #F56C6C;
          cursor: pointer;
        }
      }
      ._question_item_content {
        display: flex;
        // max-height: 200px;
        // overflow-y: auto;
        overflow-x: auto;
        margin-right: 55px;
      }
      ._question_item_type {
        position: absolute;
        right: 15px;
        top: 15px;
      }
      .combination-question-wrap {
        >div {
          border: 1px solid rgb(229, 230, 235);
          margin: 5px 0px 10px;
          padding: 15px 20px 5px;
          .comp-question {
            display: flex;
            // max-height: 200px;
            // overflow-y: auto;
            overflow-x: auto;
            >span {
              flex: 1;
              word-break: break-all;
            }
          }
          .comp-content-wrap {
            border: 1px solid #e5e6eb;
            margin: 5px 0 10px;
            padding: 15px 20px;
            >div:first-child {
              display: flex;
              // max-height: 200px;
              // overflow-y: auto;
              overflow-x: auto;
              margin-bottom: 10px;
              >span {
                flex: 1;
                word-break: break-all;
              }
            }
          }
        }
      }
    }
    ._question_item_check {
      border: 1px solid var(--color-600);
    }
  }
}
.optional-ctf {
  display: flex;
  .classification-list {
    width: 200px;
    height: 100%;
    padding-right: 15px;
    .classification-list-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .classification-list-add {
        .el-button--text {
          color: var(--color-600);
          i {
            font-size: 12px;
          }
        }
      }
    }
    .classification-content {
      padding: 5px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 10px;
      cursor: pointer;
      .classification-content-overflow {
        flex: 1;
        white-space: nowrap; /* 不换行 */
        overflow: hidden;    /* 隐藏超出部分 */
        text-overflow: ellipsis; /* 显示省略号 */
        margin-right: 10px;
      }
    }
    .classification-content:hover {
      font-weight: 700;
      background-color: var(--color-50) !important;
      color: var(--color-600);
    }
    .is-active {
      font-weight: 700;
      background-color: var(--color-50) !important;
      color: var(--color-600);
    }
  }
}
</style>

