<template>
  <div v-loading="loading" class="flex-col wrapper">
    <el-radio-group v-if="editMode" v-model="formData.operationManualType" class="mb-20">
      <el-radio :label="1">富文本</el-radio>
      <el-radio :label="2">文档</el-radio>
    </el-radio-group>
    <div v-if="formData.operationManualType == 2" class="file-wrap">
      <div v-if="editMode">
        <el-upload
          :data="{contentId:contentId,format:'mannua'}"
          :headers="{'Admin-Token':token}"
          :show-file-list="false"
          :file-list="fileList"
          :on-preview="handlePreview"
          :http-request="httpRequest"
          :before-upload="handleUpload"
          :drag="editMode"
          :accept="accept"
          :disabled="!editMode"
          :multiple="multipleMannua"
          class="upload-demo"
          action="/cepoTraining/api/training/content/uploadAttachment"
        >
          <div class="icon">
            <i class="icon-add iconfont"/>
          </div>
          <div class="el-upload__text">
            点击此处或拖拽文件到此处上传文件
            <div class="el-upload__tip">文件仅支持: {{ fileTypes.join("、") }}，大小限制在{{ maxFileSize }}MB以内</div>
          </div>
        </el-upload>
      </div>
      <!-- 下拉切换 -->
      <el-row v-if="fileList.length != 0 && !editMode" style="padding: 8px;">
        <el-col :span="24">
          <FileList :list="fileList" :deletable="editMode" :on-download="downloadReport" @click="handlePreview" @delete="deleteFile"/>
        </el-col>
      </el-row>
      <el-row v-if="fileList.length != 0" :gutter="0" class="file-view">
        <FileList v-if="editMode" v-model="currentFileIndex" :list="fileList" :deletable="editMode" :on-download="downloadReport" @click="handlePreview" @delete="deleteFile"/>
        <el-col :span="editMode ? 24 : 24" :style="`height: calc(100% - ${editMode ? '34px' : '0px'})`">
          <div class="grid-view">
            <div v-if="previewUrl" class="button-wrap">
              <el-link :underline="false" type="primary" size="small" @click="toggleFullscreen">
                {{ isFullscreen ? '退出全屏' : '全屏预览' }}
              </el-link>
            </div>
            <iframe id="frame" :src="previewUrl" allow="fullscreen" style="width: 100%;height:100%;border: 0px;margin:0;"/>
          </div>
        </el-col>
      </el-row>
      <el-empty
        v-if="fileList.length === 0"
        :image="img"
        :image-size="110"
        style="margin: 100px auto"
        description="暂无数据"
      />
    </div>
    <el-form v-else ref="form" :model="formData" :rules="rules" label-position="left" label-width="0">
      <el-form-item v-if="formData.operationManual || editMode" label="" prop="operationManual" class="operationManual">
        <myEditor
          :key="contentTimer"
          :content="formData.operationManual"
          :only-editor="!editMode"
          :is-read-only="!editMode"
          :is-editor-border="editMode"
          :height="editMode ? 'calc(100% - 81px)' : '100%'"
          id-prefix="operationManual"
          width="100%"
          class="operationManual-myEditor"
          @contentChange="contentChange('operationManual', $event)"
        />
      </el-form-item>
      <el-empty
        v-else
        :image="img"
        :image-size="110"
        style="margin: 100px auto"
        description="暂无数据"
      />
    </el-form>
  </div>
</template>
<script>
import { contentdetail, contentdetailDelete, uploadFileRequest, getContentById, uploadOperationManual } from '@/api/teacher/index.js'
import { getFileSuffix } from '@/utils'
import mixin from './mixin'
import FileList from '@/components/FileList/index.vue'
import { handlekkfilePreview } from './util'
import { api as fullscreen } from 'vue-fullscreen'
import myEditor from '@/packages/editor/index.vue'

export default {
  components: {
    myEditor,
    FileList
  },
  mixins: [mixin],
  data() {
    return {
      multipleMannua: false,
      fileTypes: ['pdf', 'doc', 'docx', 'xlsx', 'xls', 'ppt', 'pptx'],
      bool: false,
      formData: {
        operationManualType: 1, // 富文本、文档
        operationManual: '' // 富文本
      },
      rules: {
        operationManual: []
      },
      contentTimer: `operationManual` + new Date().getTime(),
      docxShow: false,
      pdfShow: false,
      curriculumManualUrl: '',
      token: '',
      noShow: false,
      maxFileSize: localStorage.getItem('maxFileSize'),
      fileList: [],
      fileName: '',
      currentFileIndex: 0,
      curriculumCoursewareUrldocx: '',
      curriculumCoursewareNamedocx: '',
      previewUrl: '',
      isFullscreen: false
    }
  },
  computed: {
    accept() {
      const arr = this.fileTypes.map((type) => '.' + type)
      return arr.join(',')
    }
  },
  mounted() {
    this.token = JSON.parse(localStorage.getItem('Admin-Token')).data
    this.searchCurriculum()
    this.getContentByIdAPI()
  },
  methods: {
    async handleIframepreview() {
      await this.$nextTick()
      handlekkfilePreview()
    },
    async toggleFullscreen() {
      const wrapperEl = this.$el.querySelector('.grid-view')
      await fullscreen.toggle(wrapperEl, {
        callback: (val) => {
          this.isFullscreen = val
        }
      })
      this.isFullscreen = fullscreen.isFullscreen
    },
    onSuccess(res, file, fileList) {
      if (file) {
        this.$set(file, 'status', 'success')
        this.$set(file, 'attachmentId', res.data.id)
        this.$set(file, 'attachmentUrl', res.data.attachmentUrl)
        const url = window.location.origin + res.data.attachmentUrl
        this.$set(file, 'url', url)
        // 默认显示第一个附件
        const data = this.fileList[0]
        if (data) {
          this.handlePreview(file, 0)
        }
      }
    },
    httpRequest(file) {
      this.$set(file, 'status', 'uploading')
      this.$set(file, 'name', file.file.name)
      if (this.multipleCourseware) {
        this.fileList.push(file)
      } else {
        if (this.fileList[0]) {
          this.deleteFile(this.fileList[0], false)
        }
        this.fileList = [file]
      }
      // 处理上传进度事件
      const onUploadProgress = (e) => {
        if (e.total) {
          e.percentage = e.loaded / e.total * 100
          this.$set(file, 'percentage', e.percentage)
        }
      }
      this.$emit('call', 'uploading', true)
      const formData = new FormData()
      formData.append('file', file.file)
      formData.append('contentId', this.contentId)
      formData.append('format', 'mannua')
      formData.append('name', file.file.name)
      uploadFileRequest(formData, onUploadProgress).then(res => {
        if (res.code == 0) {
          this.onSuccess(res, file, this.fileList)
          this.$message({
            message: '上传成功',
            type: 'success'
          })
        } else {
          this.$set(file, 'status', 'error')
          this.$message({
            message: '上传失败',
            type: 'warning'
          })
        }
      }).finally(() => {
        this.$emit('call', 'uploading', false)
      })
    },
    getContentByIdAPI() {
      const params = { id: this.contentId }
      getContentById(params).then(res => {
        if (res.code == 0) {
          this.formData.operationManualType = res.data.operationManualType || 1
          this.formData.operationManual = res.data.operationManual || ''
          this.contentTimer = `operationManual` + new Date().getTime()
        }
      }).finally(() => {
        this.loading = false
      })
    },
    searchCurriculum() {
      contentdetail({ contentId: this.contentId, format: 'mannua' }).then(res => {
        if (res.code == 0) {
          this.handleIframepreview()
          this.fileList = res.data
          this.fileList.map(item => {
            item.name = item.fileName || item.attachmentUrl
            item.url = window.location.origin + item.attachmentUrl
          })
          // 默认显示第一个附件
          const file = this.fileList[0]
          if (file) {
            this.handlePreview(file, 0)
          }
        }
      })
    },
    handlePreview(file, index) {
      this.currentFileIndex = index
      if (['pdf'].includes(getFileSuffix(file.url))) {
        this.previewUrl = this.viewFileUrl + encodeURIComponent(btoa(file.url)) // kkfile 预览
      } else {
        this.previewUrl = this.viewFileUrl + encodeURIComponent(btoa(file.url)) // kkfile 预览
      }
    },
    // 删除文件
    deleteFile(item, showMessage = true) {
      if (item.status == 'success') {
        contentdetailDelete({ attachmentId: item.attachmentId }).then(res => {
          if (res.code == 0) {
            if (showMessage) {
              this.$message.success('文件删除成功')
            }
            const index = this.fileList.findIndex(file => file.attachmentId == item.attachmentId)
            index > -1 && this.fileList.splice(index, 1)
          }
        })
      } else {
        // 删除未上传成功的文件
        const index = this.fileList.findIndex(file => file.uid == item.uid)
        index > -1 && this.fileList.splice(index, 1)
      }
    },
    // 下载文件
    downloadReport(item) {
      const fileTypes = ['doc', 'docx', 'xlsx', 'xls', 'ppt', 'pptx']
      const type = getFileSuffix(item.url)
      let newFileUrl = ''
      if (item.url) {
        if (fileTypes.includes(type)) {
          newFileUrl = new URL(item.url).pathname
        }
        const fileUrl = fileTypes.includes(type) ? newFileUrl : item.url
        console.log('fileUrl', fileUrl)
        fetch(fileUrl, {
          method: 'get',
          responseType: 'blob'
        }).then((response) => response.blob())
          .then((blob) => {
            const a = document.createElement('a')
            const URL = window.URL || window.webkitURL
            const herf = URL.createObjectURL(blob)
            a.href = herf
            a.download = item.fileName
            document.body.appendChild(a)
            a.click()
            document.body.removeChild(a)
            URL.revokeObjectURL(herf)
          })
      }
    },
    handleUpload(file) {
      this.fileName = file.name
      const maxSize = this.maxFileSize * 1024 * 1024
      if (file.size > maxSize) {
        this.$message.warning(`上传文件不能超过${this.maxFileSize}MB!`)
        return false
      }
      const fileArr = file.name.split('.')
      const fileType = fileArr[fileArr.length - 1]
      if (!this.fileTypes.includes(fileType.toLowerCase())) {
        this.$message.error('不支持该文件类型')
        return false
      }
      const promise = new Promise((resolve) => {
        this.$nextTick(function() {
          resolve(true)
        })
      })
      return promise
    },
    // 富文本编辑器改变内容时
    'contentChange': function(key, value) {
      if (this.delHtml(value)) {
        this.formData[key] = value
      } else {
        const imgStrs = value.match(/<img.*?>/g)
        if (imgStrs && imgStrs.length) { // 内容只有图片时
          this.formData[key] = value
        } else {
          this.formData[key] = ''
        }
      }
      if (String(this.delHtml(value)).length != 0) {
        this.$refs['form'].validateField(key)
      }
    },
    // 过滤html代码、空格、回车 空白字符
    'delHtml': function(str) {
      str = str.replace(/<("[^"]*"|'[^']*'|[^'">])*>/gi, '')
      str = str.replace(/[\r\n]/g, '')
      str = str.replace(/\s/g, '')
      str = str.replace(/&nbsp;/ig, '')
      return str
    },
    confirm(callback) {
      if (this.formData.operationManualType == 2) {
        this.bool = false
      } else {
        this.$refs['form'].validate((valid) => {
          if (valid) {
            this.bool = false
          } else {
            this.bool = true
          }
        })
      }
      if (this.bool) return
      const postData = JSON.parse(JSON.stringify(this.formData))
      postData['contentId'] = this.contentId
      uploadOperationManual(postData).then(res => {
        callback()
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.wrapper {
  height: 100%;
  .file-wrap {
    height: 100%;
    display: flex;
    flex-direction: column;
    .file-view {
      flex: 1;
      width: 100%;
      height: 100%;
      // min-height: 600px;
    }
  }
  .el-form {
    flex: 1;
    min-height: 0;
    .operationManual {
      height: 100%;
      ::v-deep .el-form-item__content {
        height: 100%;
      }
      .operationManual-myEditor {
        height: 100% !important;
      }
    }
  }
}
.upload-demo{
  width: 100%;
  ::v-deep{
    .el-upload {
      width: 100%;
      .el-upload-dragger{
        width: 100%;
        height: 102px;
        .icon {
          margin: 26px 0 18px;
        }
      }
    }
    .el-upload__tip{
      font-size: 12px;
      font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
      font-weight: 400;
      color: #86909C;
    }
  }
}
.grid-view {
  position: relative;
  width: 100%;
  height: 100%;
  .button-wrap {
    position: absolute;
    top: 8px;
    right: 60px;
    .el-link {
      color: var(--color-600);
    }
  }
}
</style>
