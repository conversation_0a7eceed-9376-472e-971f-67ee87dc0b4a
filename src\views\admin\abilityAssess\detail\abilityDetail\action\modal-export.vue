<template>
  <div v-loading="loading" class="dialog-wrap">
    <el-radio v-model="type" label="search">按搜索结果导出</el-radio>
    <div style="padding: 10px 0px 10px 24px;">
      <el-row>
        <el-col :span="3">搜索项:</el-col>
        <el-col :span="21">
          <template v-if="searchView.length">
            <el-tag
              v-for="item in searchView"
              :key="item.key"
              class="ellipsis mr-5"
              style="max-width: 190px;"
              size="small"
            >
              <span v-if="item.key === 'activityType'">
                {{ item.label }}：{{ activityTypeList[item.value - 1] }}
              </span>
              <span v-else>
                {{ item.label }}：{{ item.value }}
              </span>
            </el-tag>
          </template>
          <span v-else>无</span>
        </el-col>
      </el-row>
    </div>
    <el-radio v-model="type" label="all">导出全部</el-radio>
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>
<script>
import module from '../config.js'
import batchTemplate from '@/packages/batch-delete/modal-bat-template'
import modalMixins from '@/packages/mixins/modal_form'
import { exportPointsByActivities } from '@/api/accumulate/skillPoint.js'
import { downloadExcelWithResData } from '@/utils'

export default {
  name: 'Finish',
  components: { batchTemplate },
  mixins: [modalMixins],
  inject: ['tableVm'],
  data() {
    return {
      moduleName: module.name,
      loading: false,
      type: 'search',
      activityTypeList: ['实训', '攻防演练', '漏洞复现']
    }
  },
  computed: {
    'searchView': function() {
      const _data = []
      for (const key in this.tableVm.searchData) {
        _data.push({
          key: key,
          value: this.tableVm.searchData[key],
          label: this.tableVm.searchKeyList.find(item => item.key === key).label
        })
      }
      return _data
    }
  },
  methods: {
    close: function() {
      this.$emit('close')
    },
    confirm: function() {
      this.loading = true
      const params = Object.assign({}, this.tableVm.filterData, this.type === 'search' ? this.tableVm.searchData : {})
      params.userId = this.$route.params.id
      exportPointsByActivities(params).then((res) => {
        this.$message.success('导出能力评估成功')
        downloadExcelWithResData(res)
      })
      this.close()
    }
  }
}
</script>
