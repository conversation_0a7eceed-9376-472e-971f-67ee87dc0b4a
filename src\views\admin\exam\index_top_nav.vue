<template>
  <div>
    <el-tabs v-model="tabsActive" class="content-subs" type="card" style="margin-top: 20px;" @tab-click="handleTabClick">
      <el-tab-pane label="证书类" name="ca-exam">
        <router-link :to="{ name: 'ca-exam' }" />
      </el-tab-pane>
      <el-tab-pane label="非证书类" name="nonCertificate">
        <router-link :to="{ name: 'nonCertificate' }" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
export default {
  data() {
    return {
      tabsActive: ''
    }
  },
  created() {
    this.tabsActive = this.$route.name
  },
  methods: {
    'handleTabClick': function(data) {
      this.$router.push({ name: data.name })
    }
  }
}
</script>
