<template>
  <div v-loading="false" class="dialog-wrap">
    <div>请确认是否开始创建"{{ data[0].name }}"考试的快照?</div>
    <div v-if="false">
      <div>快照创建结果</div>
      <div><i class="el-icon-circle-check" style="color: #42aa2d;"/> 创建成功8个快照 </div>
      <div><i class="el-icon-circle-close" style="color: red;"/> 创建失败7个快照 <el-button size="mini" icon="el-icon-refresh-right" type="primary">重试</el-button></div>
    </div>
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>

<script>
import modalMixins from '@/packages/mixins/modal_form'
import { createSnap } from '@/api/exam/index.js'

export default {
  components: {
  },
  mixins: [modalMixins],
  props: {
    name: {
      type: String,
      default: ''
    },
    data: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {}
  },
  computed: {
  },
  methods: {
    close() {
      this.$emit('close')
    },
    confirm: function() {
      console.log('data', this.data)
      const params = {
        examId: this.data[0].id,
        paperId: this.data[0].paperId,
        examName: this.data[0].name
      }
      createSnap(params).then((res) => {
        if (res.code === 0) {
          this.$emit('call', 'refresh')
          this.close()
        }
      })
      this.$emit('call', 'setLoading', true)
    }
  }
}
</script>
