<template>
  <div class="details">
    <el-breadcrumb style="margin: 15px;" class="detail-breadcrumb" separator-class="el-icon-arrow-right">
      <el-breadcrumb-item @click.native="goBack"><a>{{ $route.meta.parentTitle }}</a></el-breadcrumb-item>
      <el-breadcrumb-item>{{ courseInfo.name }}</el-breadcrumb-item>
    </el-breadcrumb>
    <div style="height: 700px;">
      <courseDetail
        :one-level-name="$route.query.oneLevelName"
        :role-type="1"
        :type="1"
        :chapter-id="chapterId"
        :unit-id="unitId"
        :content-id="contentId"
        :edit-mode="1"
        :refresh="refresh"
        :content="content"
        @switch-courses="switchCourses"
      />
    </div>
    <coursInfo
      :id="$route.query.courseId"
      :role-type="1"
      :chapter-id-new="chapterId"
      :unit-id-new="unitId"
      :content-id-new="contentId"
      :content-new="content"
      :edit-mode="1"
      @switch-courses="switchCourses"
      @set-course-info="setCourseInfo"
    />
  </div>
</template>

<script>
import courseDetail from '@/components/courseDetail/index'
import coursInfo from '@/components/courseDetail/info'
export default {
  components: {
    courseDetail,
    coursInfo
  },
  data() {
    return {
      chapterId: '',
      unitId: '',
      contentId: '',
      content: {},
      courseInfo: {},
      refresh: false,
      type: this.$route.query.type
    }
  },
  methods: {
    switchCourses(chapterId, unitId, contentId, content) {
      this.chapterId = chapterId
      this.unitId = unitId
      this.contentId = contentId
      this.content = content
    },
    setCourseInfo(courseInfo) {
      this.refresh = !this.refresh
      this.courseInfo = courseInfo
    },
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style lang="scss" scoped>
.details {
  height: 100%;
  overflow: auto;
}
</style>
