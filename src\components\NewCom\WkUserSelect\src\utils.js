import { getUserListAPI, getUserByDeptAPI } from '@/api/usercenter/user'

// 根据请求获取搜索请求
export function getSearchRequestWithRequest(request) {
  const requestName = request ? request.name : null
  if (requestName) {
    return {
      getUserByDeptAPI: getUserListAPI
    }[requestName]
  }
  return null
}


// 根据搜索请求获取请求
export function getRequestWithSearchRequest(searchRequest) {
  const requestName = searchRequest ? searchRequest.name : null
  if (requestName) {
    return {
      getUserListAPI: getUserByDeptAPI
    }[requestName]
  }
  return null
}
