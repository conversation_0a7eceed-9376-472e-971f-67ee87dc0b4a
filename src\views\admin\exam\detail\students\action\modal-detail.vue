<template>
  <div class="dialog-wrap">
    <div class="editMsg">编辑信息<i style="color: #005ad4;" class="el-icon-edit-outline" @click="isEdit = false"/></div>
    <el-form v-if="isEdit" ref="form" :model="formData" label-position="left" label-width="80px">
      <el-form-item label="姓名">
        <div>{{ formData['name'] }}</div>
      </el-form-item>
      <el-form-item label="身份证号">
        <div>{{ formData['haveIdCard'] ? '已绑定身份证号，设置新的身份证号进行覆盖' : '未绑定身份证号' }}</div>
      </el-form-item>
      <el-form-item label="企业">
        <div>{{ formData['enterpriseName'] }}</div>
      </el-form-item>
      <el-form-item label="账号">
        <div>{{ formData['username'] }}</div>
      </el-form-item>
      <el-form-item label="手机号">
        <div>{{ formData['mobile'] }}</div>
      </el-form-item>
      <el-form-item label="邮箱">
        <div>{{ formData['mail'] }}</div>
      </el-form-item>
    </el-form>

    <el-form v-show="!isEdit" ref="form" :model="formData" :rules="rules" label-position="left" label-width="80px">
      <el-form-item label="姓名" prop="name">
        <el-input v-model.trim="formData.name" :disabled="isEdit" show-word-limit />
      </el-form-item>
      <el-form-item label="身份证号" prop="idNumber">
        <el-input v-model.trim="formData.idNumber" :disabled="isEdit" show-word-limit />
      </el-form-item>
      <el-form-item label="企业" prop="enterprise">
        <template>
          <wk-dep-select
            v-model="formData.enterprise"
            :disabled="isEdit"
            radio
            @change="depChange"
          />
        </template>
      </el-form-item>
      <el-form-item label="账号" prop="username">
        <el-input v-model.trim="formData.username" :disabled="isEdit" show-word-limit />
      </el-form-item>
      <el-form-item label="手机号" prop="mobile">
        <el-input v-model.trim="formData.mobile" :disabled="isEdit" show-word-limit />
      </el-form-item>
      <el-form-item v-if="!isEdit" label="密码" prop="password">
        <el-input v-model.trim="formData.password" show-password @change="$refs['form'].clearValidate(['confirmPsd'])"/>
      </el-form-item>
      <el-form-item v-if="!isEdit" :rules="[{ required: !!formData.password, message: '必填项' }, { validator: validatedConfirmPsd, trigger: 'change' }]" label="确认密码" prop="confirmPsd">
        <el-input v-model.trim="formData.confirmPsd" show-password/>
      </el-form-item>
      <el-form-item label="邮箱" prop="mail">
        <el-input v-model.trim="formData.mail" :disabled="isEdit" show-word-limit />
      </el-form-item>
    </el-form>

    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>

<script>
import validate from '@/packages/validate'
import modalMixins from '@/packages/mixins/modal_form'
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
import WkDepSelect from '@/components/NewCom/WkDepSelect'
import { studentDetail, studentUpdate } from '@/api/exam/index.js'

export default {
  components: {
    WkDepSelect
  },
  mixins: [modalMixins, mixinsActionMenu],
  props: {
    data: {
      type: Array,
      default: () => {
        return []
      }
    },
    studentDetailId: {
      type: Number
    }
  },
  data() {
    return {
      loading: false,
      validate: validate,
      formData: {
        name: '',
        idNumber: '',
        enterpriseName: '',
        enterprise: '',
        mobile: '',
        username: '',
        password: '',
        confirmPsd: '',
        mail: '',
        haveIdCard: null
      },
      rules: {
        name: [
          validate.required(),
          {
            type: 'string',
            pattern: /^[\u4e00-\u9fa5]{2,64}$/,
            message: '2-64个字符，仅支持中文字符',
            trigger: 'blur'
          }
        ],
        idNumber: [validate.idcard],
        enterprise: [validate.required()],
        mobile: [validate.required(), validate.mobilePhone],
        username: [
          { required: true, message: '账号不能为空', trigger: 'blur' },
          {
            pattern: /^(?!\s)[^\s]{2,30}(?<!\s)$/,
            message: '2-30个字符，不支持空格'
          }
        ],
        password: [{ pattern: /^[\da-zA-Z!@#$%^&*]{1,20}$/, message: '密码由9-密码由1-20位组成', trigger: 'change' }],
        // confirmPsd: [{ validator: this.validatedConfirmPsd, trigger: 'change' }],
        mail: [validate.required(), validate.email]
      },
      isEdit: true,
      userId: ''
    }
  },
  watch: {
    'isEdit'(newVal, oldVal) {
      this.isEdit ? this.formData.idNumber = this.formData.idNumber.replace(/^(.{6})(?:\d+)(.{4})$/, '\$1******\$2') : this.formData.idNumber
    }
  },
  created() {
    /* 身份证脱敏 */
    const reg = /^(.{6})(?:\d+)(.{4})$/
    this.formData.idNumber = this.formData.idNumber.replace(reg, '\$1******\$2')
    this.getEaxmById()
  },
  methods: {
    validatedConfirmPsd(rule, value, callback) {
      if (!this.formData.password) {
        return callback()
      }
      if (this.formData.password && value != this.formData.password) {
        callback(new Error('密码不一致，请重新输入'))
      } else {
        callback()
      }
    },
    /**
     * 编辑用户单选change
     */
    depChange(_, data) {
      const obj = data && data.length > 0 ? data[0] : null
      this.$set(this.formData, 'parentId', obj ? obj.ownerUserId : '')
    },
    getEaxmById() {
      this.tableLoading = true
      const id = this.studentDetailId
      studentDetail({ id: id }).then((res) => {
        if (res.code === 0) {
          this.formData.name = res.data.realname
          this.formData.idNumber = res.data.idCard
          this.formData.enterprise = res.data.enterpriseCode
          this.formData.enterpriseName = res.data.enterpriseName
          this.formData.mobile = res.data.mobile
          this.formData.username = res.data.username
          this.formData.mail = res.data.email
          this.userId = res.data.userId
          this.formData.haveIdCard = res.data.haveIdCard
          this.tableLoading = false
        }
      }).catch(() => {
        this.tableLoading = false
      })
    },
    close() {
      this.$emit('close')
    },
    confirm: function() {
      // 如果没有修改，点击确认直接关闭
      if (this.isEdit) {
        this.close()
      } else {
        this.$refs['form'].validate((valid) => {
          this.$refs['form'].validateField(['confirmPsd'])
          if (valid) {
            this.loading = true
            const params = {
              realname: this.formData.name,
              idCard: this.formData.idNumber,
              enterpriseCode: this.formData.enterprise,
              password: this.formData.password,
              confirmPassword: this.formData.confirmPsd,
              email: this.formData.mail,
              mobile: this.formData.mobile,
              id: this.studentDetailId,
              username: this.formData.username,
              userId: this.userId
            }
            studentUpdate(params).then((res) => {
              if (res.code === 0) {
                this.$message({
                  message: '更新成功',
                  type: 'success'
                })
                this.$emit('call', 'refresh')
                this.close()
              }
            }).catch(() => {
              this.loading = false
            })
          } else {
            return false
          }
        })
      }
    }
  }
}
</script>

<style scoped lang="scss">
.wk-dep-select {
  width: 100%;
}
.dialog-wrap {
  .editMsg{
    text-align: right;
    margin-bottom: 20px;
  }
}
</style>
