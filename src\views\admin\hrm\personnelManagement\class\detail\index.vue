<template>
  <div class="content-wrap-layout">
    <div class="vertical-wrap">
      <page-table
        ref="table"
        :default-selected-arr="defaultSelectedArr"
        :filter-data="{}"
        @refresh="refresh"
        @link-event="linkEvent"
        @on-select="tabelSelect"
        @on-current="tabelCurrent"
      >
        <action-menu
          slot="action"
          :module-name="moduleName"
          :select-item="selectItem"
          @call="actionHandler"
        />
      </page-table>
    </div>
  </div>
</template>

<script>
import moduleConf from './config'
import pageTable from './table/index.vue'
import actionMenu from './action/index.vue'
import contentHeader from '../detail-header.vue'
export default {
  // 学生管理
  name: 'StudentManage',
  components: {
    pageTable,
    actionMenu,
    contentHeader
  },
  data() {
    return {
      moduleName: moduleConf.name,
      selectItem: [],
      defaultSelectedArr: []
    }
  },
  watch: {
    '$route': function(to, from) {
      const toId = to.params.hasOwnProperty('classCode') ? to.params.classCode : null
      const fromId = from.params.hasOwnProperty('classCode') ? from.params.classCode : null
      if (toId !== fromId) {
        this.$refs['table'].getList()
      }
    }
  },
  methods: {
    // 列表点击
    linkEvent: function({ name, row, params }) {
      this.$router.push({ name: name, params: params })
    },
    // 返回已选
    tabelSelect: function(data) {
      this.selectItem = data
    },
    // 返回单选
    tabelCurrent: function(row) {
      this.selectItem = [row]
    },
    // action menu 事件
    actionHandler: function(type, data) {
      switch (type) {
        case 'refresh':
          this.$refs['table'].getList()
      }
    },
    refresh: function() {
    }
  }
}
</script>
<style lang="scss" scoped>
.content-wrap-layout {
  margin-top: 59px;
  border-top: 1px solid #E4E7ED;
  height: calc(100% - 60px);
}
</style>
