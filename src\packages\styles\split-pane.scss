.fold-wrap {
  position: absolute;
  top: calc(50% - 80px);
  width: 10px;
  height: 60px;
  line-height: 60px;;
  background-color: var(--color-600);
  overflow: hidden;
  cursor: pointer;
  z-index: 9;
  i {
    color: #fff;
    margin-left: -2px;
  }
}
body .paneR {
  height: 100%;
  border-left: 1px solid #dcdee2;
  position: relative;
}
body .vue-splitter-container {
  width: 100%;
  .splitter-pane.vertical.splitter-paneL {
    padding-right: 10px;
  }
  .splitter-pane.vertical.splitter-paneR {
    padding-left: 0px;
  }
  .splitter-pane.horizontal.splitter-paneL {
    padding-bottom: 10px;
  }
  .splitter-pane.horizontal.splitter-paneR {
    padding-top: 5px;
  }
  .splitter-pane-resizer.horizontal {
    height: 10px;
    margin: -10px 0 0 0;
    opacity: 1;
    &::after {
      transform: rotate(90deg);
      content: '|||';
      font-size: 18px;
      letter-spacing: -1.5px;
      color: var(--color-600);
      position: absolute;
      top: 0%;
      bottom: 0%;
      margin-top: -30px;
      left: 50%;
      right: 50%;
      height: 60px;
      line-height: 60px;
      width: 10px;
      background: rgba(2, 91, 212, 0.2);
      pointer-events: none;
    }
  }
  .splitter-pane-resizer.vertical {
    width: 10px;
    margin-left: -10px;
    opacity: 1;
    &::after {
      content: '|||';
      font-size: 18px;
      letter-spacing: -1.5px;
      color: var(--color-600);
      position: absolute;
      top: 50%;
      bottom: 50%;
      margin-top: -20px;
      left: -5px;
      right: 0;
      height: 60px;
      line-height: 60px;
      width: 10px;
      background: rgba(2, 91, 212, .2);
      pointer-events: none;
    }
  }
}