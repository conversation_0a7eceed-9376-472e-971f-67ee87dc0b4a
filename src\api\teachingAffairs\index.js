import request from '@/utils/request'
/**
 * 教学事务列表
 *
 */
export function backQueryAffairApi(data) {
  return request({
    url: `/training/pjtTeacherScheduling/backQueryAffair`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 教学事务新列表
 *
 */
export function manageQueryAffair(data) {
  return request({
    url: `/training/pjtTeacherScheduling/manageQueryAffair`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 教学事务-班级详情
 *
 */
export function affairDetailsApi(data) {
  return request({
    url: `/training/PjtSysUser/affairDetails`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export function affairDetailsOrderScore(data) {
  return request({
    url: `/training/PjtSysUser/affairDetailsOrderScore`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 教学事务-学员详情
 *
 */
export function studentAffairDetailsApi(data) {
  return request({
    url: `/training/PjtSysUser/studentAffairDetails`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}


/**
 * 排课学生列表
 *
 */
export function queryStudentManageList(data) {
  return request({
    url: 'training/studentManager/queryStudentManageList',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export function queryAllStudentList(data) {
  return request({
    url: '/training/assistantTeacher/assistant/query',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
export function addStudentRole(data) {
  return request({
    url: '/training/assistantTeacher/assistant/save',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
export function deleteStudentRole(data) {
  return request({
    url: '/training/assistantTeacher/assistant/delete',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export function exportStudentAPI(data) {
  return request({
    url: '/training/assistantTeacher/assistant/exportStudent',
    method: 'post',
    responseType: 'blob',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export function exportStudentNewAPI(data) {
  return request({
    url: '/training/assistantTeacher/assistant/exportStudentNew',
    method: 'post',
    responseType: 'blob',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
/**
 * 班级管理
 */
export function queryMajor(data) {
  return request({
    url: '/training/class/major/query',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
export function saveMajor(data) {
  return request({
    url: '/training/s',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
export function updateMajor(data) {
  return request({
    url: '/training/class/major/update',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
export function deleteMajor(data) {
  return request({
    url: '/training/class/major/delete',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export function classQuery(data) {
  return request({
    url: '/training/assistantTeacher/queryClass',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export function frontendQuery(data) {
  return request({
    url: '/training/class/allClass/query',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export function saveClass(data) {
  return request({
    url: '/training/assistantTeacher/save',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
export function deleteClass(data) {
  return request({
    url: '/training/assistantTeacher/delete',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
export function updateClass(data) {
  return request({
    url: '/training/assistantTeacher/update',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
export function queryClassDetail(data) {
  return request({
    url: '/training/class/detail/query',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
export function saveClassDetail(data) {
  return request({
    url: '/training/assistantTeacher/student/save',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
export function deleteClassDetail(data) {
  return request({
    url: '/training/assistantTeacher/student/delete',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
export function queryAssistantTeacher(data) {
  return request({
    url: 'training/assistantTeacher/query',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
/**
 * 查询专业/班级
 */
export function searchMajor(data) {
  return request({
    url: `/training/pjtTeacherScheduling/searchMajor`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    }
  })
}

/**
 * 查询教师列表
 */
export function queryTeacherManager(data) {
  return request({
    url: `/training/teacherManager/query`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 学员排课
 */
export function studentCourseSave(data) {
  return request({
    url: `/training/pjtHeadTeacherScheduling/student/course/save`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 检验内容是否被排过
 */
export function checkContentAPI(data) {
  return request({
    url: `/training/pjtHeadTeacherScheduling/checkContent`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 排课时间校验
 */
export function studentCourseQuery(data) {
  return request({
    url: `/training/pjtTeacherScheduling/student/course/query`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 查询教案下的课程
 */
export function queryCourseByPlanId(data) {
  return request({
    url: `/training/lessonPlan/lessonPlanCourse/query`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    }
  })
}

/**
 * 排课查询所有学员
 */
export function queryAllStudent(data) {
  return request({
    url: `/training/assistantTeacher/queryStudentListNoRepeat`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    }
  })
}

/**
 * 排课查询所有学员
 */
export function queryAffairs(data) {
  return request({
    url: `/training/affair/query`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 排课教学方案列表
 */
export function getLessonPlanSchedulingInfo(data) {
  return request({
    url: `/training/lessonPlan/getLessonPlanSchedulingInfoByManager`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    }
  })
}

/**
 * 排课课程库选择
 */
export function queryAssistantCourse(data) {
  return request({
    url: '/training/course/assistant/query',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
   * 助教-排课-试卷列表
   *
   */
export function pjtExamPage(data) {
  return request({
    url: '/training/pjtExam/coursePage',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
   * 助教-排课-班级人数
   *
   */
export function getStudentNum(data) {
  return request({
    url: `/training/assistantTeacher/getStudentNum/${data.classCode}`,
    method: 'get',
    data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    }
  })
}

export function insertCategoryAPI(data) {
  return request({
    url: `/training/pjtCategory/insertCategory`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
/** training/pjtCategory/updateCategory
 * 删除分类
 * @param {*} data
 * @returns
 */
export function delBaseType(data) {
  return request({
    url: '/training/pjtCategory/deleteCategory',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/** training/pjtCategory/updateCategory
 * 删除分类
 * @param {*} data
 * @returns
 */
export function updateCategory(data) {
  return request({
    url: '/training/pjtCategory/updateCategory',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
/* 教案列表，调试后删除 */
export function lessonPlanQuery(data) {
  return request({
    url: '/training/lessonPlan/query',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
export function queryCourseDetail(data) {
  return request({
    url: '/training/course/courseDetail/query',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}
// 排课查询课程内容-按课程章节排序
export function findContentByCourseIdAndChapterUnit(data) {
  return request({
    url: '/training/course/courseDetail/findContentByCourseIdAndChapterUnit',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}
export function lessonPlanDetailQuery(data) {
  return request({
    url: `training/lessonPlan/detail/query`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    }
  })
}

/**
 * 排课
 */
export function insertTeacherScheduling(data) {
  return request({
    url: `/training/pjtHeadTeacherScheduling/insertTeacherScheduling`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
/**
 * 班主任查看新地址
 */
export function teacherSchedulingQuery(data) {
  return request({
    url: '/training/pjtTeachingTask/searchTeachingTaskInSchedule',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
export function queryExamPage(data) {
  return request({
    url: `/ca-exam/exam/queryExamPage`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
export function insertClassSchedule(data) {
  return request({
    url: `/training/pjtClassSchedule/insertClassSchedule`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 课程修改
 *
 */
export function updateTeacherSchedulingTimeAPI(data) {
  return request({
    url: '/training/pjtHeadTeacherScheduling/updateTeacherSchedulingTime',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
/**
* 环境状态
*/
export function envStatusApi(data) {
  return request({
    url: `/training/vm/status/getTrainingUserVmStartStatus`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
export function openTrainingEnv(data) {
  return request({
    url: `/training/pjtTeacherScheduling/openTrainingEnv`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// "schedulingCode":"1716518327404287"
export function closeTrainingEnv(data) {
  return request({
    url: `/training/pjtTeacherScheduling/closeTrainingEnv`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 取消排课
export function delTeachingCodeList(data) {
  return request({
    url: `/training/pjtTeacherScheduling/delTeachingCodeList`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
