<template>
  <create-view v-loading="loading" :title="viewTitle" :goback="false" @goback="close(false)">
    <div slot="content" class="resource-table wrapper card-bg">
      <!-- 分类 -->
      <div class="_paper_top">
        <div class="_paper_top_title" style="display: flex;">
          <div style="color: #999;">题目来源：</div>
          <div>
            <el-radio-group v-model="categoryRadio" @change="changeRadio">
              <el-radio label="1">题库</el-radio>
              <el-radio label="2">试卷</el-radio>
            </el-radio-group>
          </div>
        </div>
        <div style="margin-top: 10px; display: flex;">
          <div style="color: #999; margin-top: 6px;">分类：</div>
          <el-tag
            v-for="(item,index) in categoryList" :key="index"
            :disable-transitions="true"
            closable
            style="margin:3px 5px 5px 0"
            @close="closeClassify(index, item)">
            {{ categoryRadio == '1' ? item.categoryName : item.examName }}
          </el-tag>
        </div>
        <el-button v-if="categoryRadio == '1'" class="mb-10" type="ghost" style="margin-top: 3px !important;" @click="drawerName = 'selectClassify', drawerShow = true">选择分类</el-button>
        <el-button v-if="categoryRadio == '2'" type="ghost" style="margin-top: 3px !important;" class="mb-10" @click="drawerName = 'seleceExaminationPaper', drawerShow = true">选择试卷</el-button>
      </div>
      <category @category-query="handleCategoryQuery"/>
      <!-- 操作区 -->
      <div class="operation-wrap">
        <div class="operation-left">
          <slot name="action" />
          <el-button type="primary" icon="el-icon-refresh" @click="refresh"/>
        </div>
        <div class="operation-right">
          <el-badge :value="searchBtnShowNum">
            <el-button icon="el-icon-search" @click="openSearch">搜索</el-button>
          </el-badge>
        </div>
      </div>
      <!-- 搜索区 -->
      <t-search-box
        v-show="searchView"
        :search-key-list="searchKeyListView"
        default-placeholder="默认搜索题干"
        @search="searchMultiple"
      />
      <div class="tools-wrap">
        <div class="buttons">
          <el-button type="default" @click="onCheckAll">全选</el-button>
          <el-button type="default" @click="onUnCheckAll" >取消全选</el-button>
        </div>
        <div class="_paper_search">
          <div class="line"/>
          <div class="_paper_search_1">
            总题数
            <span style="font-weight: bold; color: var(--color-600); font-size: 18px; margin-right: 10px;">{{
              questionTotal
            }}</span>
          </div>
          <div class="_paper_search_1">
            已选题数
            <span style="font-weight: bold; color: var(--color-600); font-size: 18px;">{{
              checkQuestionCodes.length
            }}</span>
          </div>
        </div>
      </div>
      <!-- 题目列表 -->
      <div v-loading="tableLoading" class="content">
        <div v-if="questionList.length && !tableLoading" class="_question_list">
          <div
            v-for="(q, index) in questionList"
            :class="`_question_item ${
              checkQuestionCodes.includes(q.questionCode) &&
              '_question_item_check'
            }`"
            :key="index"
            @click="handleCheckQuestion(q)"
          >
            <div style="display: flex;justify-content: space-between;align-items: flex-start;">
              <img v-if="checkQuestionCodes.includes(q.questionCode)" :src="select_actie" style="width: 16px; height: 16px" alt="">
              <img v-else :src="select" style="width: 16px; height: 16px" alt="">
              <div class="_question_item_content">
                {{ index + 1 }}.&nbsp;
                <div v-html="q.questionType != 10 ? q.content : q.questionName"/>
              </div>
              <div class="_question_item_type">
                <el-tag size="small">{{ questionTypeSimulationObj[q.questionType] }}</el-tag>
              </div>
            </div>
            <div v-if="q.questionType == 1" class="_question_option">
              <el-radio-group
                v-for="(op, i) in JSON.parse(q.questionOptions)"
                :key="i"
                :value="q.questionAnswer"
                disabled
              >
                <el-radio v-overflow-tooltip="{ content: op }" :label="optionLabel[i]">{{ optionLabel[i] }}. {{ op }}</el-radio>
              </el-radio-group>
            </div>
            <div v-if="q.questionType == '2'">
              <el-checkbox-group :value="q.questionAnswer.split('')" disabled>
                <div class="_question_option">
                  <el-checkbox
                    v-overflow-tooltip="{ content: op }"
                    v-for="(op, i) in JSON.parse(q.questionOptions)"
                    :key="i"
                    :label="optionLabel[i]"
                  >{{ optionLabel[i] }}. {{ op }}</el-checkbox
                  >
                </div>
              </el-checkbox-group>
            </div>
            <div v-if="q.questionType == 3" class="_question_option">
              <el-radio-group :value="q.questionAnswer" disabled>
                <el-radio label="A">正确</el-radio>
                <el-radio label="B">错误</el-radio>
              </el-radio-group>
            </div>
            <div v-else class="_question_option">
              <span>{{ q.questionAnswer }}</span>
            </div>
          </div>
        </div>
        <el-empty
          v-if="!questionList.length && !tableLoading"
          :image="img"
          :image-size="110"
          style="margin: 100px auto"
          description="暂无数据"
        />
      </div>
      <!-- 侧拉 -->
      <el-drawer
        :title="titleMapping[drawerName]"
        :visible.sync="drawerShow"
        :size="drawerWidth"
        append-to-body
        @close="drawerClose"
      >
        <transition v-if="drawerShow" name="el-fade-in-linear">
          <component
            :is="drawerName"
            :name="drawerName"
            @close="drawerClose"
            @call="drawerConfirmCall"
          />
        </transition>
      </el-drawer>
    </div>
    <div slot="footer">
      <el-button type="text" @click="close()">取消</el-button>
      <el-button :loading="submitLoading" type="primary" @click="confirm()">确定</el-button>
    </div>
  </create-view>
</template>
<script>
import createView from '@/packages/create-view/index'
import category from './category.vue'
import selectClassify from './select-classify.vue'
import seleceExaminationPaper from './selece-examination-paper.vue'
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
import mixinsPageTable from '@/packages/mixins/page_table'
import tSearchBox from '@/packages/search-box/index.vue'
import validate from '@/packages/validate'
import {
  questionByManualPaperAPI,
  queryQuestionPageByExamId,
  updatePracticeAPI,
  // savePractice,
  queryPractice
} from '@/api/teacher/index.js'
import { questionTypeObj, questionTypeSimulationObj } from './constants'

export default {
  components: {
    createView,
    category,
    selectClassify,
    tSearchBox,
    seleceExaminationPaper
  },
  mixins: [mixinsActionMenu, mixinsPageTable],
  data() {
    return {
      searchKeyList: [
        { key: 'content', label: '题干', master: true }
      ],
      questionTypeObj,
      loading: false,
      submitLoading: false,
      validate: validate,
      categoryList: [],
      categoryIds: [],
      evaluationCode: this.$route.query.evaluationCode,
      curriculumCode: this.$route.query.curriculumCode,
      id: this.$route.query.id,
      checkQuestionList: [],
      checkQuestionCodes: [],
      questionList: [],
      knowledgeList: [],
      optionLabel: Array.from({ length: 26 }, (_, index) => String.fromCharCode('A'.charCodeAt(0) + index)),
      knowledgeCheckList: [],
      isCheckAll: false,
      select_actie: require('@/assets/select_active.png'),
      select: require('@/assets/select.png'),
      img: require('@/assets/empty_state.png'),
      questionTotal: 0,
      selectTotal: 0,
      pageCurrent: 1,
      pageSize: 10,
      questionTypeSimulationObj,
      // questionQuantity: {
      //   CTF: 0,
      //   standAlone: 0,
      //   simulation: 0,
      //   choice: 0
      // },
      categoryQuery: {
        complexity: null,
        questionType: null
      },
      titleMapping: {
        'selectClassify': '分类列表',
        'seleceExaminationPaper': '试卷列表'
      },
      drawerWidth: '720px',
      drawerShow: false,
      drawerName: null,
      contentType: this.$route.query.contentType,
      categoryRadio: this.$route.query.questionSource || '1',
      examId: ''
    }
  },
  computed: {
    editMode() {
      return !!this.id
    },
    viewTitle() {
      return `选择题目`
    }
  },
  watch: {
    categoryList(data) {
      this.categoryIds = data.map(v => v.id)
      this.getList(false)
    },
    // categoryIds() {
    //   console.log(this.categoryIds)
    //   if (this.categoryIds.length != 0) {
    //     this.getList(false)
    //   } else {
    //     this.questionList = []
    //   }
    // },
    checkQuestionList: {
      handler(newVal) {
        if (newVal) {
          this.checkQuestionCodes = newVal.map(v => v.questionCode)
        }
      },
      deep: true
    }
  },
  mounted() {
    this.checkQuestionList = []
    const { categoryList, checkQuestionList } = this.$route.query
    this.categoryList = categoryList || []
    this.checkQuestionList = checkQuestionList || []
    this.queryPracticeCode()
  },
  methods: {
    changeRadio() {
      this.categoryList = []
      this.categoryIds = []
      this.examId = ''
      this.checkQuestionList = []
      this.questionTotal = 0
      this.getList(false)
    },
    onCheckAll() {
      this.checkQuestionList = this.questionList
      this.selectTotal = this.checkQuestionList.length
    },
    onUnCheckAll() {
      this.checkQuestionList = []
      this.selectTotal = this.checkQuestionList.length
    },
    /**
     * 更改每页展示数量
     */
    handleSizeChange(val) {
      this.pageSize = val
      this.getList(false)
    },
    /**
     * 更改当前页数
     */
    handleCurrentChange(val) {
      this.page = val
      this.getList(false)
    },
    closeClassify(index, item) {
      this.checkQuestionList = this.checkQuestionList.filter(i => { return i.categoryId !== item.id })
      this.categoryList.splice(index, 1)
    },
    updatePractice() {
      this.submitLoading = true
      updatePracticeAPI({
        evaluationCode: this.evaluationCode,
        questionCodeList: this.checkQuestionCodes,
        curriculumCode: this.curriculumCode
      }).then(res => {
        if (res.code == 0) {
          this.$message.success('修改成功')
          this.close()
        }
      }).finally(() => {
        this.submitLoading = false
      })
    },
    // 侧拉选择资源回调
    drawerConfirmCall: function(type, data) {
      if (type === 'confirm_classify') {
        this.categoryList = data
        this.drawerClose()
      }
    },
    drawerClose() {
      this.drawerShow = false
    },
    handleCategoryQuery(obj) {
      this.categoryQuery = { ...obj }
      if (obj.complexity == null) {
        this.categoryQuery.complexity = 0
      }
      if (obj.questionType == null) {
        this.categoryQuery.questionType = 0
      }
      this.getList(false)
    },
    evaluationCodeFn() {
      if (this.evaluationCode) {
        this.updatePractice()
      } else {
        this.handleManualBuildPaper()
      }
    },
    getList: function(showLoading = true) {
      if (showLoading) {
        this.tableLoading = true
      }
      this.handleQuestionSearch()
    },
    queryPracticeCode: function() {
      // 获取题目
      queryPractice({ contentId: this.id, isShowAnswers: true }).then(res => {
        // res.data.forEach(item => {
        //   this.handleCheckQuestion(item.questionCode)
        // })
      }).catch(() => {
      })
    },
    // 手动组卷
    handleManualBuildPaper() {
      if (this.checkQuestionList.length === 0) {
        return this.$message.error('请选择题目')
      }
      // this.close()
      // this.submitLoading = true
      // savePractice({
      //   contentId: this.id,
      //   examQuestionCode: this.checkQuestionList
      // }).then((ret) => {
      //   if (ret.code == 0) {
      //     this.$message.success('添加成功')
      //     this.close()
      //   } else {
      //     this.$message.error('添加失败')
      //   }
      // }).finally(() => {
      //   this.submitLoading = false
      // })
    },
    // 题目查询
    handleQuestionSearch() {
      const postData = this.getPostData('page', 'limit')
      let questionTypes = []
      let bankType
      let bankTypes
      if (this.contentType == 1) {
        questionTypes = this.categoryQuery.questionType ? [this.categoryQuery.questionType] : Object.keys(questionTypeObj)
        bankType = 1
      } else if (this.contentType == 2) {
        questionTypes = this.categoryQuery.questionType ? [this.categoryQuery.questionType] : Object.keys(questionTypeSimulationObj)
        bankTypes = [1, 2, 3]
      }
      // 是否选择题库或者试卷类型
      let typeApi
      if (this.categoryRadio == '2') {
        this.examId = this.categoryList[0] && this.categoryList[0].id
        typeApi = queryQuestionPageByExamId
      } else {
        typeApi = questionByManualPaperAPI
      }
      console.log('this.categoryIds:', this.categoryIds)
      if (this.categoryIds.length === 0) { //  && this.$route.query.type == '创建'
        this.questionList = []
        this.tableTotal = 0
        this.loading = false
        this.tableLoading = false
        return
      }
      typeApi({
        pageType: 0,
        content: postData.content,
        complexity: this.categoryQuery.complexity ? this.categoryQuery.complexity : null,
        knowledgeCodeList: this.knowledgeCheckList,
        categoryIds: this.categoryIds,
        examId: this.examId,
        questionTypes: questionTypes,
        bankType: bankType,
        bankTypes: bankTypes
      }).then((ret) => {
        if (ret.code !== 0) {
          this.$message.error('题目查询失败.')
          return
        }
        this.questionList = ret.data.records || []
        this.questionList.forEach(item => {
          if (!item.questionCode) {
            item.questionCode = item.id
          }
          // this.categoryList.push({
          //   id: item.categoryId,
          //   categoryName: item.categoryName
          // })
        })
        this.questionTotal = ret.data.total || 0
        if (this.$route.query.examCode && this.numBool == 1) {
          this.numBool = 2
          // 试卷题目回显
          this.getPaperDetails()
        }
      }).finally(() => {
        this.tableLoading = false
      })
    },
    // 选择题目
    handleCheckQuestion(q) {
      this.selectTotal = 0
      // if (typeof (q) != 'object') {
      //   this.checkQuestionList.push(q)
      //   this.selectTotal += 1
      //   return
      // }
      // 取消
      if (this.checkQuestionCodes.includes(q.questionCode)) {
        this.checkQuestionList = this.checkQuestionList.filter(
          (el) => el.questionCode !== q.questionCode
        )
        // this.selectTotal -= 1
        // if (q.questionType == 1 || q.questionType == 2) {
        //   this.questionQuantity.choice -= 1
        // }
        // if (q.questionType == 3) {
        //   this.questionQuantity.standAlone -= 1
        // }
        // if (q.questionType == 4) {
        //   this.questionQuantity.simulation -= 1
        // }
        // if (q.questionType == 5) {
        //   this.questionQuantity.CTF -= 1
        // }
        // if (this.selectTotal === 0) {
        //   this.isCheckAll = false
        // }
        return
      }
      // const num =
      //   Number(this.questionQuantity.CTF) +
      //   Number(this.questionQuantity.simulation) +
      //   Number(this.questionQuantity.standAlone)
      // if (this.questionQuantity.choice > 100 - num) {
      //   this.$message.warning('总题数不能大于100.')
      //   return
      // }
      // 选择
      this.checkQuestionList.push(q)
      // this.selectTotal += 1
      // if (q.questionType == 1 || q.questionType == 2) {
      //   this.questionQuantity.choice += 1
      // }
      // if (q.questionType == 3) {
      //   this.questionQuantity.standAlone += 1
      // }
      // if (q.questionType == 4) {
      //   this.questionQuantity.simulation += 1
      // }
      // if (q.questionType == 5) {
      //   this.questionQuantity.CTF += 1
      // }

      // 判断点击前是否已经全选
      // if (this.selectTotal === this.questionList.length) {
      //   this.isCheckAll = true
      // }
    },

    close(flag = false) {
      let data = { ...this.$route.query, noRefresh: '1' }
      if (flag) {
        // 点击确定时才传参回上一个页面回显
        data = { ...data, checkQuestionList: this.checkQuestionList, questionSource: this.categoryRadio, categoryList: this.categoryList }
      }
      this.$router.replace({ name: 'trainingContentCreate', query: { ...data }})
    },
    'confirm': function() {
      this.close(true)
      // this.evaluationCodeFn()
    }
  }
}
</script>
<style lang="scss" scoped>
@import './index.scss';
.resource-table {
  overflow: auto;
}
.wrapper {
.tools-wrap {
  margin-top: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
._paper_search {
    display: inline-flex;
    align-items: center;
    ._paper_search_1 {
      font-size: 16px;
      font-weight: 500;
      color: #333;
    }
  }
  &.card-bg {
    border-radius: 4px;
    padding: 15px;
    background-color: #FFFFFF;
  }
}
</style>
