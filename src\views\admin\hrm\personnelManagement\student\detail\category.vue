<template>
  <div class="category-wrap">
    <transverse-list
      :data="learnTypeArr"
      :allow-deletion="false"
      :allow-add="false"
      :all="false"
      :is-show-expand="false"
      :default-code="String(learnType) "
      v-bind="categoryProps"
      title="学习类型"
      style="border: none;"
      @node-click="handleNodeClick($event, 'learnType')"
    />
    <transverse-list
      v-if="categoryQuery.learnType == 0"
      :data="courseTypeArr"
      :allow-deletion="false"
      :allow-add="false"
      :all="false"
      :is-show-expand="false"
      :default-code="String(courseType) "
      v-bind="categoryProps"
      title="上课类型"
      @node-click="handleNodeClick($event, 'courseType')"
    />
  </div>
</template>
<script>
import transverseList from '@/packages/transverse-list/index.vue'
import { courseTypeArr, learnTypeArr } from '@/views/admin/hrm/personnelManagement/student/constants'

export default {
  components: {
    transverseList
  },
  props: {
    courseType: {
      type: [String, Number],
      default: 0
    },
    learnType: {
      type: [String, Number],
      default: 0
    }
  },
  data() {
    return {
      courseTypeArr: courseTypeArr,
      learnTypeArr: learnTypeArr,
      categoryProps: {
        label: 'label',
        idName: 'value'
      },
      categoryQuery: {
        courseType: this.courseType,
        learnType: this.learnType
      }
    }
  },
  mounted() {
  },
  methods: {
    handleNodeClick(item, key) {
      this.categoryQuery[key] = item.value
      this.$emit('category-query', this.categoryQuery)
    }
  }
}
</script>
