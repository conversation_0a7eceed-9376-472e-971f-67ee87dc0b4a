<template>
  <div v-loading="loading" class="dialog-wrap">
    <el-form ref="form" :model="addKnowledgeForm" :rules="formRules" @submit.native.prevent>
      <el-form-item :label-width="formLabelWidth" label="名称" prop="knowledgeName">
        <el-input v-model.trim="addKnowledgeForm.knowledgeName"/>
      </el-form-item>
    </el-form>
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>
<script>
import module from '../config.js'
import validate from '@/packages/validate'
import { addAPI, editAPI } from '@/api/admin/training/knowledge'
export default {
  components: {},
  mixins: [],
  props: {
    // 传入数据
    data: {
      type: Array,
      default: () => {
        return []
      }
    },
    name: {
      type: String
    }
  },
  data() {
    return {
      moduleName: module.name,
      loading: false,
      validate: validate,
      formData: {
        knowledgeName: ''
      },
      formRules: {
        knowledgeName: [validate.required(), validate.base_name]
      },
      formLabelWidth: '100px',
      addKnowledgeForm: {
        knowledgeName: ''
      },
      apiType: addAPI
    }
  },
  computed: {
    editMode: function() {
      return this.name === 'editKnowledge'
    },
    activeItem: function() {
      return this.data[0]
    }
  },
  created() {
    if (this.editMode && this.activeItem) {
      this.formData['knowledgeName'] = this.activeItem['knowledgeName']
      if (this.name !== 'addKnowledge') {
        this.addKnowledgeForm = { ...this.data[0] }
      }
    }
  },
  methods: {
    close: function() {
      this.$emit('close')
    },
    confirm: function() {
      // 校验表单
      this.$refs.form.validate((valid) => {
        if (valid) {
          if (this.name !== 'addKnowledge') {
            this.apiType = editAPI
          }
          this.apiType(this.addKnowledgeForm).then((ret) => {
            if (ret.code === 0) {
              // 添加成功刷新列表
              this.$message.success(`${this.name === 'addKnowledge' ? '添加' : '编辑'}成功`)
              this.$emit('call', 'refresh')
              this.close()
              return
            }
            this.$message.warning(`${this.name === 'addKnowledge' ? '添加' : '编辑'}失败`)
          }).finally(() => {
            this.loading = false
          })
        }
      })
    }
  }
}
</script>
