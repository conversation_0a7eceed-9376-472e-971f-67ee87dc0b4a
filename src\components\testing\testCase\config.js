// 优先级
const priorityArr = [
  { label: '低', value: '1', type: 'info' },
  { label: '中', value: '2', type: 'warning' },
  { label: '高', value: '3', type: 'danger' }
]
// 类型
const typeArr = [
  { label: '功能测试', value: '1', type: 'info' },
  { label: '性能测试', value: '2', type: 'warning' },
  { label: '安全测试', value: '3', type: 'danger' }
]
export default {
  name: 'testCaseList',
  priorityArr: priorityArr,
  typeArr: typeArr,
  get priorityNumArr() {
    return this.priorityArr.map(item => {
      return { label: item.label, value: Number(item.value), type: item.type }
    })
  },
  // 将优先级map数组转换为对象
  priorityObj: priorityArr.reduce((acc, prev) => {
    acc[prev.value] = prev
    return acc
  }, {}),
  // 将类型map数组转换为对象
  typeObj: typeArr.reduce((acc, prev) => {
    acc[prev.value] = prev
    return acc
  }, {})
}
