import request from '@/utils/request'
const _thisApi = window.NFVO_CONFIG.nfvo_api

// 查询赛制类型
export function getMatchType(data) {
  return request({
    url: 'scene/sceneType/getMatchType',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 创建阶段
export function scenePolicyCreate(data) {
  return request({
    url: 'scene/scenePolicy/create',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 查询阶段详情
export function getBySceneInstanceId(query) {
  return request({
    url: 'scene/sceneBasicInfoInstance/getBySceneInstanceId',
    method: 'get',
    params: query
  })
}

// 修改阶段信息
export function scenePolicyUpdate(data) {
  return request({
    url: 'scene/scenePolicy/update',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
// 阶段准备资源
export function generateTopology(data) {
  return request({
    url: 'scene/scenePolicy/generateTopology',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 阶段释放资源
export function releaseTopology(data) {
  return request({
    url: 'scene/scenePolicy/releaseTopology',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 分页查询阶段所有战队
export function queryVenueTeamPage(data) {
  return request({
    url: 'scene/sceneInstancePlayerRel/queryVenueTeamPage',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 分页查询阶段所有人员
export function queryVenuePlayerPage(data) {
  return request({
    url: 'scene/sceneInstancePlayerRel/queryVenuePlayerPage',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 分页阶段题目列表
export function sceneInstanceQuestionQueryPage(data) {
  return request({
    url: 'scene/sceneInstanceQuestion/queryPage',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 阶段题目开放
export function sceneInstanceQuestionEnable(data) {
  return request({
    url: 'scene/sceneInstanceQuestion/enable',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 阶段题目禁用
export function sceneInstanceQuestionDisable(data) {
  return request({
    url: 'scene/sceneInstanceQuestion/disable',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 阶段题目编辑
export function sceneInstanceQuestionEdit(data) {
  return request({
    url: 'scene/sceneInstanceQuestion/edit',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 分页查询阶段公告列表
export function noticeRecordQueryPage(data) {
  return request({
    url: 'scene/noticeRecord/queryPage',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 新增比赛公告
export function noticeRecordCreate(data) {
  return request({
    url: 'scene/noticeRecord/addSeasonNotice',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 编辑比赛公告
export function noticeRecordUpdate(data) {
  return request({
    url: 'scene/noticeRecord/editSeasonNotice',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 删除比赛公告
export function noticeRecordRemove(data) {
  return request({
    url: 'scene/noticeRecord/remove',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 查询救援列表
export function rescueRecordQueryPage(data) {
  return request({
    url: 'scene/rescueRecord/queryPage',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 处理救援
export function rescueRecordUpdate(data) {
  return request({
    url: 'scene/rescueRecord/update',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 公告上传文件接口
export function adminSysFileUpload(data) {
  return request({
    url: 'admin/adminSysFile/allMatchTypeUpload',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 公告发布
export function releaseSeasonNotice(data) {
  return request({
    url: 'scene/noticeRecord/releaseSeasonNotice',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 人工判分团队列表
export function queryManualScoringTeam(data) {
  return request({
    url: 'scene/sceneInstanceQuestion/queryManualScoringTeam',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 人工判分个人列表
export function queryManualScoringPlayer(data) {
  return request({
    url: 'scene/sceneInstanceQuestion/queryManualScoringPlayer',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 人工判分获取题目信息
export function getManualScoringQuestion(query) {
  return request({
    url: 'scene/sceneInstanceQuestion/getManualScoringQuestion',
    method: 'get',
    params: query
  })
}

// 人工判分团队操作
export function manualScoringTeam(data) {
  return request({
    url: 'scene/sceneInstanceQuestion/manualScoringTeam',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 人工判分个人操作
export function manualScoringPlayer(data) {
  return request({
    url: 'scene/sceneInstanceQuestion/manualScoringPlayer',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 查询write列表
export function writeUpQueryPage(data) {
  return request({
    url: 'scene/writeUpFile/queryPage',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 查询答题记录列表
export function taskResultSubmitPageVo(data) {
  return request({
    url: 'scene/taskResultSubmit/pageVo',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 裁判答题
export function adminSubmitAnswer(data) {
  return request({
    url: 'scene/scenePolicy/adminSubmitAnswer',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export function getNode(params) {
  return request({
    url: _thisApi + '/topology/nodes',
    method: 'get',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    params
  })
}

// 查询实例列表
export function queryNodeInstancePage(data) {
  return request({
    url: 'scene/sceneInstancePlayerResourceRel/queryNodeInstancePage',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 实例创建快照
export function createSnapshot(data) {
  return request({
    url: 'scene/restoreSnapshotRecord/createSnapshot',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 实例重置快照
export function resetSnapshot(data) {
  return request({
    url: 'scene/restoreSnapshotRecord/resetSnapshot',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 查询实例快照列表
export function getSnapshotList(data) {
  return request({
    url: 'scene/restoreSnapshotRecord/getSnapshotList',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 恢复快照
export function restoreSnapshot(data) {
  return request({
    url: 'scene/restoreSnapshotRecord/restoreSnapshot',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 查询阶段战队成绩（团队）
export function queryTeamCompetitionTeamPage(data) {
  return request({
    url: 'scene/sceneInstancePlayerRel/queryTeamCompetitionTeamPage',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 查询阶段个人成绩（团队）
export function queryTeamCompetitionPlayerPage(data) {
  return request({
    url: 'scene/sceneInstancePlayerRel/queryTeamCompetitionPlayerPage',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 阶段成绩 团队成绩导出（团队）
export function exportTeamCompetitionTeam(data) {
  return request({
    url: 'scene/sceneInstancePlayerRel/exportTeamCompetitionTeam',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data,
    responseType: 'blob'
  })
}

// 阶段成绩 个人成绩导出（团队）
export function exportTeamCompetitionPlayer(data) {
  return request({
    url: 'scene/sceneInstancePlayerRel/exportTeamCompetitionPlayer',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data,
    responseType: 'blob'
  })
}

// 查询阶段个人成绩（个人）
export function queryIndividualCompetitionPlayerPage(data) {
  return request({
    url: 'scene/sceneInstancePlayerRel/queryIndividualCompetitionPlayerPage',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 阶段成绩 个人成绩导出（个人）
export function exportIndividualCompetitionPlayer(data) {
  return request({
    url: 'scene/sceneInstancePlayerRel/exportIndividualCompetitionPlayer',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data,
    responseType: 'blob'
  })
}

// 比赛战队成绩导出
export function exportSeasonTeamScore(data) {
  return request({
    url: 'match/bigMatchSeasonTeamPlayerRelation/exportSeasonTeamScore',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data,
    responseType: 'blob'
  })
}

// 比赛个人成绩导出--新
export function exportSeasonTeamPlayerScore(data) {
  return request({
    url: 'match/bigMatchSeasonTeamPlayerRelation/exportSeasonTeamPlayerScore',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data,
    responseType: 'blob'
  })
}

// 查询阶段违规记录
export function violationRecordPage(data) {
  return request({
    url: 'scene/violationRecord/queryPage',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 编辑阶段违规记录
export function violationRecordUpdate(data) {
  return request({
    url: 'scene/violationRecord/update',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 阶段中加减分列表
export function pageAddAndSubtractTeam(data) {
  return request({
    url: 'scene/sceneScore/pageAddAndSubtractTeam',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 阶段中加减分置顶/取消置顶
export function updateAddAndSubtractSort(data) {
  return request({
    url: 'scene/sceneScore/updateAddAndSubtractSort',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 阶段中加减分 加减分处置操作
export function adminSetScore(data) {
  return request({
    url: 'scene/scenePolicy/adminSetScore',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 获取加减分原因
export function adminSetList(data) {
  return request({
    url: 'scene/sceneScore/adminSetList',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 裁判答题查询CTF/理论题目列表
export function queryQuestionListPage(data) {
  return request({
    url: 'scene/scenePolicy/queryQuestionListPage',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 裁判查询靶机列表
export function queryTargetLitsAllOld(data) {
  return request({
    url: 'scene/scenePolicy/queryTargetListAll',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 裁判查询靶机列表--新
export function queryTargetLitsAll(data) {
  return request({
    url: 'scene/scenePolicy/queryTargetListByAnswerQuestion',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 查询在线人员
export function getOnlineUserPage(data) {
  return request({
    url: 'scene/onlineUser/getOnlineUserPage',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 赛中 答题记录 战队/人员锁定
export function updateInstancePlayerLock(data) {
  return request({
    url: 'scene/scenePolicy/updateInstancePlayerLock',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 赛中 答题进度列表
export function answeringProgressList(data) {
  return request({
    url: '/scene/taskResultSubmit/querySubmitList',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
// 赛中 答题进度详情
export function answeringProgressDetails(data) {
  return request({
    url: '/scene/taskResultSubmit/querySubmitDetails',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 重置阶段状态
export function resetStageStatusApi(data) {
  return request({
    url: '/scene/scenePolicy/resetStatus',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 战队答题记录导出
export function expexportTeamTaskResultSubmit(data) {
  return request({
    url: '/scene/taskResultSubmit/exportTeam',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data,
    responseType: 'blob'
  })
}

// 个人答题记录导出
export function exportPlayerTaskResultSubmit(data) {
  return request({
    url: '/scene/taskResultSubmit/exportPlayer',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data,
    responseType: 'blob'
  })
}

// 查询轮次详情
export function queryTargetListByRound(query) {
  return request({
    url: 'scene/scenePolicy/queryTargetListByRound',
    method: 'get',
    params: query
  })
}

// 修改wp上传时间
export function updateWpTime(data) {
  return request({
    url: '/scene/sceneBasicInfoMatchInstance/updateWpTime',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 查询实例列表(资源分配)
export function queryNodeInstanceList(data) {
  return request({
    url: 'scene/sceneInstancePlayerResourceRel/queryNodeInstanceList',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 资源分配保存
export function resourceAllocation(data) {
  return request({
    url: 'scene/sceneInstancePlayerResourceRel/resourceAllocation',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 查询资源分配详情
export function getResourceAllocation(query) {
  return request({
    url: 'scene/sceneInstancePlayerResourceRel/getResourceAllocation',
    method: 'get',
    params: query
  })
}

// 查询资源分配详情（独享）
export function getQuestionResourceRel(query) {
  return request({
    url: 'scene/sceneInstancePlayerResourceRel/getQuestionResourceRel',
    method: 'get',
    params: query
  })
}

// 批量分配网络vlanId
export function batchAssignNetworkSegment(data) {
  return request({
    url: 'match/bigMatchSeasonTeamPlayerRelation/batchAssignNetworkSegment',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
