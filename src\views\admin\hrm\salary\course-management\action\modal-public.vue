<template>
  <div v-loading="loading" class="dialog-wrap">
    <el-alert :closable="false" type="warning">
      <div v-if="data.coursePrivacy == 0" slot="title">
        <p>使用权限公开后所有教师可见可用，若状态为“已发布”则同时展示在学员自选课程中心，请确认是否公开“{{ data.name }}”课程。</p >
      </div>
      <div v-else slot="title">
        <p>使用权限变为私有后不会继续展示在课程库，不影响历史已引用的教师使用，若状态为“已发布”则同时取消展示在学员自选课程中心，请确认是否私有“{{ data.name }}”课程。</p >
      </div>
    </el-alert>
    <el-checkbox v-model="checked">我已知晓上述风险</el-checkbox>
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button :disabled="!checked" type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>
<script>
import module from '../config.js'
import { updataCourseCategory } from '@/api/teacher/index.js'

export default {
  name: 'ModalPublic',
  components: {},
  mixins: [],
  props: {
    data: Object
  },
  data() {
    return {
      moduleName: module.name,
      loading: false,
      checked: false
    }
  },
  mounted() {

  },
  methods: {
    close: function() {
      this.$emit('close')
    },
    confirm: function() {
      console.log('this.data', this.data)
      const params = {
        id: this.data.id,
        coursePrivacy: this.data.coursePrivacy == 1 ? 0 : 1
      }
      this.loading = true
      updataCourseCategory(params).then(res => {
        if (res.code === 0) {
          this.$message.success(`${this.data.coursePrivacy == 1 ? '编辑私有成功' : '编辑公开成功'}`)
          this.$emit('call', 'refresh')
          this.close()
          this.loading = false
        }
      })
    }
  }
}
</script>
