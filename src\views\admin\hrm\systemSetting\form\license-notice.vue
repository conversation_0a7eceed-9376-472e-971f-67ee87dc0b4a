<template>
  <el-form ref="form" :model="formData" :rules="rules" label-position="left" label-width="120px">
    <el-form-item label="开启到期提醒" prop="enable">
      <el-switch v-if="editMode" v-model="formData.enable" />
      <span v-else>
        <el-badge :type="formData.enable ? 'success' : 'danger'" is-dot />{{ formData.enable ? '开' : '关' }}
      </span>
    </el-form-item>
    <el-form-item label="开始提醒事件" prop="days" class="license-notice-days-form">
      到期前 <el-input-number v-if="editMode" v-model="formData.days" />
      <span v-else>{{ formData.days }}</span> 天
    </el-form-item>
  </el-form>
</template>
<script>
import validate from '@/packages/validate'
export default {
  props: {
    // 传入数据
    data: {
      type: Object,
      default: () => null
    },
    editMode: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      loading: false,
      validate: validate,
      formData: {
        enable: true,
        days: 30
      },
      rules: {
        days: [
          validate.required('change'), validate.number_integer
        ]
      }
    }
  },
  watch: {
    data(val) {
      if (val) {
        this.formData['enable'] = val['enable']
        this.formData['days'] = val['days']
      }
    }
  },
  created() {
    if (this.data) {
      this.formData['enable'] = this.data['enable']
      this.formData['days'] = this.data['days']
    }
  }
}
</script>
<style lang="scss" scoped>
.license-notice-days-form {
  /deep/ .el-form-item__error {
    text-indent: 46px;
  }
}
</style>
