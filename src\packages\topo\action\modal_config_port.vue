<template>
  <div v-loading="loading" class="dialog-wrap" >
    <el-alert v-if="disabledMsg" :closable="false" type="warning">
      <div slot="title">
        <p>{{ disabledMsg }}</p>
      </div>
    </el-alert>
    <div class="edge-config-warp">
      <div id="edge-container"/>
      <el-form ref="form" :model="formData" :rules="rules" :disabled="!!disabledMsg" label-width="80px" >
        <el-form-item label="资源名称">
          <span>{{ sourceData.name }}</span>
        </el-form-item>
        <el-form-item v-if="showPort(sourceData)" label="选择端口" prop="sourcePortName">
          <el-select v-model="formData.sourcePortName" :disabled="name === 'viewEdge'" popper-class="config-port-select" style="width: 100%;" @change="changeSourcePort">
            <el-option
              v-for="(port) in sourceData.ports"
              :key="port.id"
              :value="port.name"
              :disabled="isDisabled(sourceData, port)"
            >
              <span>{{ viewLabel(sourceData, port) }}</span>
              <el-tag v-if="showShareTag(sourceData, port)" type="success" effect="dark">共享端口</el-tag>
            </el-option>
          </el-select>
        </el-form-item>
        <div v-if="isSwitch(sourceData)">
          <el-form-item label="CIDR" prop="source_cidr">
            <el-dropdown :disabled="sourceData.status !== 'pending'" trigger="click" placement="bottom-start" style="width: 100%;" @command="setCidr($event, 'source_cidr')">
              <el-input :disabled="name === 'viewEdge' || sourceData.status !== 'pending'" v-model.trim="formData.source_cidr"/>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="192.168.0.0/24">192.168.0.0/24</el-dropdown-item>
                <el-dropdown-item command="10.0.0.0/24">10.0.0.0/24</el-dropdown-item>
                <el-dropdown-item command="172.16.0.0/24">172.16.0.0/24</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </el-form-item>
          <el-form-item label="网关" prop="source_gateway">
            <el-input :disabled="name === 'viewEdge' || sourceData.status !== 'pending'" v-model.trim="formData.source_gateway"/>
          </el-form-item>
        </div>
        <div v-if="isExternal(sourceData) || isGlobalNetwork(sourceData)">
          <el-form-item label="CIDR">{{ sourceData.external_network && sourceData.external_network.cidr ? sourceData.external_network.cidr : '-' }}</el-form-item>
          <el-form-item label="网关">{{ sourceData.external_network && sourceData.external_network.gateway_ip ? sourceData.external_network.gateway_ip : '-' }}</el-form-item>
        </div>
        <div v-if="isQemu(sourceData) || isGlobalQemu(sourceData) || isDocker(sourceData)">
          <el-form-item label="IP地址" prop="source_ip">
            <el-input :disabled="name === 'viewEdge'" v-model.trim="formData.source_ip" placeholder="自动分配" />
          </el-form-item>
        </div>
        <div v-if="isRouter(sourceData)">
          <el-form-item label="选择端口" prop="source_device_port_type">
            <el-select v-model="formData.source_device_port_type" :disabled="name === 'viewEdge'" style="width: 100%;">
              <el-option value="lan" label="LAN口" />
              <el-option value="wan" label="WAN口" />
            </el-select>
          </el-form-item>
        </div>
        <div :style="{height: showPort(sourceData) ? (isVnf(sourceData) ? '87px' : '135px') : (isSwitch(sourceData) || isExternal(sourceData) || isGlobalNetwork(sourceData) ? '87px' : '183px')}"/>
        <el-form-item label="资源名称">
          <span>{{ targetData.name }}</span>
        </el-form-item>
        <el-form-item v-if="showPort(targetData)" label="选择端口" prop="targetPortName">
          <el-select v-model="formData.targetPortName" :disabled="name === 'viewEdge'" popper-class="config-port-select" style="width: 100%;" @change="changeTargetPort">
            <el-option
              v-for="(port) in targetData.ports"
              :key="port.id"
              :value="port.name"
              :disabled="isDisabled(targetData, port)"
            >
              <span>{{ viewLabel(targetData, port) }}</span>
              <el-tag v-if="showShareTag(targetData, port)" type="success" effect="dark">共享端口</el-tag>
            </el-option>
          </el-select>
        </el-form-item>
        <div v-if="isSwitch(targetData)">
          <el-form-item label="CIDR" prop="target_cidr">
            <el-dropdown :disabled="targetData.status !== 'pending'" trigger="click" placement="bottom-start" style="width: 100%;" @command="setCidr($event, 'target_cidr')" >
              <el-input :disabled="name === 'viewEdge' || targetData.status !== 'pending'" v-model.trim="formData.target_cidr"/>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="192.168.0.0/24">192.168.0.0/24</el-dropdown-item>
                <el-dropdown-item command="10.0.0.0/24">10.0.0.0/24</el-dropdown-item>
                <el-dropdown-item command="172.16.0.0/24">172.16.0.0/24</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </el-form-item>
          <el-form-item label="网关" prop="target_gateway">
            <el-input :disabled="name === 'viewEdge' || targetData.status !== 'pending'" v-model.trim="formData.target_gateway"/>
          </el-form-item>
        </div>
        <div v-else-if="isExternal(targetData) || isGlobalNetwork(targetData)">
          <el-form-item label="CIDR">{{ targetData.external_network && targetData.external_network.cidr ? targetData.external_network.cidr : '-' }}</el-form-item>
          <el-form-item label="网关">{{ targetData.external_network && targetData.external_network.gateway_ip ? targetData.external_network.gateway_ip : '-' }}</el-form-item>
        </div>
        <div v-if="isQemu(targetData) || isGlobalQemu(targetData) || isDocker(targetData)">
          <el-form-item label="IP地址" prop="target_ip">
            <el-input :disabled="name === 'viewEdge'" v-model.trim="formData.target_ip" placeholder="自动分配" />
          </el-form-item>
        </div>
        <div v-if="isRouter(targetData)">
          <el-form-item label="选择端口" prop="target_device_port_type">
            <el-select v-model="formData.target_device_port_type" :disabled="name === 'viewEdge'" style="width: 100%;">
              <el-option value="lan" label="LAN口" />
              <el-option value="wan" label="WAN口" />
            </el-select>
          </el-form-item>
        </div>
      </el-form>
    </div>
    <el-form ref="bottom-form" :model="formData" :rules="rules" :disabled="!!disabledMsg" label-width="80px" class="bottom-form" >
      <el-form-item label="网络类型" prop="network_type">
        <el-select v-model="formData.network_type" :disabled="name === 'viewEdge' || sourceData.status != 'pending' || targetData.status != 'pending'">
          <el-option label="VLAN" value="vlan" />
          <el-option label="VXLAN" value="vxlan" />
        </el-select>
      </el-form-item>
      <el-form-item v-if="formData.network_type === 'vlan'" label="VLAN ID" prop="segmentation_id">
        <el-input-number v-model="formData.segmentation_id" :disabled="name === 'viewEdge' || isExternal(sourceData) || isExternal(targetData) || isGlobalNetwork(sourceData) || isGlobalNetwork(targetData) || sourceData.status != 'pending' || targetData.status != 'pending'" placeholder="自动分配"/>
      </el-form-item>
    </el-form>
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button :disabled="name === 'viewEdge' || !!disabledMsg" type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>

<script>
import moduleConf from '../config'
import validate from '../../validate'
import CustomNode from '../tpl/CustomNode.vue'
import { Graph } from '@antv/x6'
import { register } from '@antv/x6-vue-shape'
import { isV4, inRange } from 'range_check'
import { getNodeItem } from '../api/orchestration'

register({
  shape: 'custom-vue-node',
  width: 100,
  height: 100,
  component: CustomNode
})

export default {
  props: {
    // 传入数据
    data: {
      type: Object,
      default: () => {
        return {}
      }
    },
    name: String
  },
  data() {
    return {
      loading: false,
      graph: null,
      edge: null,
      sourceData: null,
      targetData: null,
      selectedSourcePort: null,
      selectedTargetPort: null,
      formData: {
        'sourcePortName': '',
        'source_cidr': '',
        'source_gateway': '',
        'source_ip': '',
        'source_device_port_type': '',
        'targetPortName': '',
        'target_cidr': '',
        'target_gateway': '',
        'target_ip': '',
        'target_device_port_type': '',
        'network_type': '',
        'segmentation_id': undefined
      },
      rules: {
        'sourcePortName': [
          validate.required('change')
        ],
        'source_cidr': [
          validate.required(),
          validate.filterIpCIDR
        ],
        'source_gateway': [
          validate.required(),
          { validator: this.validateGateway, trigger: 'blur' }
        ],
        'source_ip': [
          { validator: this.validateIp, trigger: 'blur' }
        ],
        'source_device_port_type': [
          validate.required('change')
        ],
        'targetPortName': [
          validate.required('change')
        ],
        'target_cidr': [
          validate.required(),
          validate.filterIpCIDR
        ],
        'target_gateway': [
          validate.required(),
          { validator: this.validateGateway, trigger: 'blur' }
        ],
        'target_ip': [
          { validator: this.validateIp, trigger: 'blur' }
        ],
        'target_device_port_type': [
          validate.required('change')
        ],
        'network_type': [
          validate.required('change')
        ],
        'segmentation_id': [
          validate.number_integer,
          { type: 'number', min: 1, max: 4094, message: '输入范围：1-4094', trigger: 'change' }
        ]
      }
    }
  },
  computed: {
    /**
     * 连线限制:
     * 1. 云路由器仅可连接逻辑交换机和外部网络
     * 2. 物理设备不能连虚拟设备
     * 3. 外部网络未配置
     */
    disabledMsg() {
      let msg = ''
      if (
        (this.isRouter(this.sourceData) && !this.isSwitch(this.targetData) && !this.isExternal(this.targetData)) ||
        (this.isRouter(this.targetData) && !this.isSwitch(this.sourceData) && !this.isExternal(this.sourceData))
      ) {
        msg = '云路由器仅可连接逻辑交换机和外部网络'
      } else if (
        (this.isPnf(this.sourceData) && this.isVnf(this.targetData)) ||
        (this.isPnf(this.targetData) && this.isVnf(this.sourceData))
      ) {
        msg = '物理设备不能连虚拟设备'
      } else if (
        (this.isExternal(this.sourceData) && !this.sourceData.external_network) ||
        (this.isExternal(this.targetData) && !this.targetData.external_network)
      ) {
        msg = '请先配置外部网络'
      }
      return msg
    }
  },
  created() {
    const data = JSON.parse(JSON.stringify(this.data))
    this.sourceData = data.sourceNode.data
    this.targetData = data.targetNode.data
    this.formData.source_cidr = data.sourceNode.data.cidr
    this.formData.source_gateway = data.sourceNode.data.gateway
    this.formData.target_cidr = data.targetNode.data.cidr
    this.formData.target_gateway = data.targetNode.data.gateway
    if (this.isSwitch(this.sourceData) || this.isExternal(this.sourceData) || this.isGlobalNetwork(this.sourceData)) {
      this.getNodeItem(this.sourceData)
    }
    if (this.isSwitch(this.targetData) || this.isExternal(this.targetData) || this.isGlobalNetwork(this.targetData)) {
      this.getNodeItem(this.targetData)
    }
    // 查看连线配置回显端口
    if (this.name === 'viewEdge') {
      this.formData.sourcePortName = this.showPort(this.sourceData) ? data.edge.data.source.port_name : ''
      this.formData.source_ip = this.showPort(this.sourceData) ? data.edge.data.source.ip : ''
      this.formData.source_device_port_type = data.edge.data.source.device_port_type || ''
      this.formData.targetPortName = this.showPort(this.targetData) ? data.edge.data.dest.port_name : ''
      this.formData.target_ip = this.showPort(this.targetData) ? data.edge.data.dest.ip : ''
      this.formData.target_device_port_type = data.edge.data.dest.device_port_type || ''
      this.formData.network_type = data.edge.data.network_type || ''
    }
  },
  mounted() {
    this.graph = new Graph({
      container: document.getElementById('edge-container'),
      interacting: false,
      width: 100,
      height: 300,
      background: {
        color: '#fff'
      }
    })
    const source = this.graph.addNode({
      shape: 'custom-vue-node',
      x: 0,
      y: 0,
      width: 50,
      height: 50,
      data: Object.assign({}, this.data.sourceNode.data, {
        'configPortModal': true
      })
    })
    const target = this.graph.addNode({
      shape: 'custom-vue-node',
      x: 0,
      y: 230,
      width: 50,
      height: 50,
      data: Object.assign({}, this.data.targetNode.data, {
        'configPortModal': true
      })
    })
    this.edge = this.graph.addEdge({
      source,
      target,
      ...moduleConf.edgeConfig,
      labels: []
    })
    // // 图形设备与其他设备的连线默认为虚线
    // if (this.sourceData.type === 'inf' || this.targetData.type === 'inf') {
    //   this.edge.attr('line/strokeDasharray', '5 5')
    // }
  },
  methods: {
    // 校验IP地址
    'validateIp': function(rule, value, callback) {
      if (value) {
        if (!isV4(value)) {
          callback(new Error('请输入正确的IPv4地址'))
        }
      }
      callback()
    },
    // 校验网关地址
    'validateGateway': function(rule, value, callback) {
      const block = rule.field === 'source_gateway' ? this.formData['source_cidr'] : this.formData['target_cidr']
      if (value) {
        if (!isV4(value)) {
          callback(new Error('请输入正确的IPv4地址'))
        } else if (!block) {
          callback(new Error('请先输入CIDR'))
        } else if (!inRange(value, block)) {
          callback(new Error('IP地址不在允许范围内'))
        }
      }
      callback()
    },
    'isPnf': function(data) {
      return data.type === 'pnf'
    },
    'isVnf': function(data) {
      return data.type === 'vnf'
    },
    'isInf': function(data) {
      return data.type === 'inf'
    },
    'isBase': function(data) {
      return data.type === 'base'
    },
    'isQemu': function(data) {
      return data.type === 'vnf' && data.virtual_type === 'qemu'
    },
    'isDocker': function(data) {
      return data.type === 'vnf' && data.virtual_type === 'docker'
    },
    'isRouter': function(data) {
      return data.type === 'vnf' && data.virtual_type === 'cloud_router'
    },
    'isGlobalNetwork': function(data) {
      return data.resource_type === 'vnf' && data.virtual_type === 'global_network'
    },
    'isGlobalQemu': function(data) {
      return data.resource_type === 'vnf' && data.virtual_type === 'global_qemu'
    },
    'isSwitch': function(data) {
      return data.type === 'base' && data.virtual_type === 'logic_switch'
    },
    'isExternal': function(data) {
      return data.type === 'base' && data.virtual_type === 'external_switch'
    },
    'getType': function(data) {
      let type = ''
      if (this.isPnf(data) || this.isInf(data)) {
        type = data.type
      } else if (this.isVnf(data) || this.isBase(data)) {
        type = data.virtual_type
      }
      return type
    },
    // 展示的connected to标签
    'viewLabel': function(data, port) {
      let linkMes = ''
      if (this.isDisabled(data, port)) {
        if (data.type === 'pnf' && port.reserved) {
          linkMes = '（已预留）'
        } else if (port.topology_link_to) {
          linkMes = ` connected to ${port.topology_link_to.node_name} ${port.topology_link_to.port_name}`
        }
      }
      return port.name + linkMes
    },
    // 是否显示共享tag
    'showShareTag': function(data, port) {
      if (data.type === 'pnf') {
        const deviceItem = data.devicePorts.find(val => val.id === port.network_element_port_id)
        return deviceItem.shared
      } else {
        return false
      }
    },
    // 是否展示选择端口表单（图形设备、云路由器、逻辑交换机、外部网络、全局网络都不展示端口）
    'showPort': function(data) {
      return !(this.isInf(data) || this.isSwitch(data) || this.isExternal(data) || this.isRouter(data) || this.isGlobalNetwork(data))
    },
    // 是否置灰option
    'isDisabled': function(data, port) {
      if (data.type === 'pnf') {
        const deviceItem = data.devicePorts.find(val => val.id === port.network_element_port_id)
        return (!deviceItem.shared && !!port.topology_link_to) || port.reserved
      } else {
        return !!port.topology_link_to
      }
    },
    // 选择网络地址时
    'setCidr': function(value, name) {
      this.formData[name] = value
      this.$refs['form'].validateField(name)
    },
    'changeSourcePort': function(value) {
      this.selectedSourcePort = this.sourceData.ports.find(item => item.name === value)
      this.edge.setLabelAt(0, {
        attrs: { text: { text: value }},
        position: 0.15
      })
    },
    'changeTargetPort': function(value) {
      this.selectedTargetPort = this.targetData.ports.find(item => item.name === value)
      if (!this.selectedSourcePort) {
        this.edge.setLabelAt(0, {
          attrs: {
            text: { text: '' },
            bg: { refWidth: 0 }
          },
          position: 0.15
        })
      }
      this.edge.setLabelAt(1, {
        attrs: { text: { text: value }},
        position: 0.85
      })
    },
    // 重新获取node的端口
    'getNodeItem': function(data) {
      this.loading = true
      getNodeItem(data.node_id)
        .then(res => {
          // 回显逻辑交换机的网络类型
          if (this.isSwitch(data) && this.name !== 'viewEdge') {
            this.formData['network_type'] = res.data.data.network_type || ''
          }
          const ports = res.data.data.ports || []
          if (ports.length) {
            this.formData['segmentation_id'] = ports[0].segmentation_id || undefined
          }
          // 更新外部网络的网络信息
          this.$set(data, 'external_network', res.data.data.external_network || null)
          this.loading = false
        })
        .catch((error) => {
          this.loading = false
          console.log(error)
        })
    },
    'close': function() {
      this.$emit('close')
    },
    'confirm': function() {
      let topValid = true
      let bottomValid = true
      this.$refs['form'].validate((valid) => { topValid = valid })
      this.$refs['bottom-form'].validate((valid) => { bottomValid = valid })
      if (topValid && bottomValid) {
        this.selectedSourcePort && this.sourceData.ports.forEach(item => {
          if (item.id === this.selectedSourcePort.id) {
            item['topology_link_to'] = {
              'port_name': this.selectedTargetPort ? this.selectedTargetPort.name : '', // 对端port的name
              'node_name': this.targetData.name // 对端node的name
            }
          }
        })
        this.selectedTargetPort && this.targetData.ports.forEach(item => {
          if (item.id === this.selectedTargetPort.id) {
            item['topology_link_to'] = {
              'port_name': this.selectedSourcePort ? this.selectedSourcePort.name : '', // 对端port的name
              'node_name': this.sourceData.name // 对端node的name
            }
          }
        })
        const edgeLabels = this.edge.labels
        const postData = {
          edgeLabels,
          sourcePorts: this.sourceData.ports,
          targetPorts: this.targetData.ports,
          edgeData: {
            source: { 'type': this.getType(this.sourceData), 'node_name': this.sourceData.name, 'ip': this.formData.source_ip || null, 'device_port_type': this.formData.source_device_port_type || null },
            dest: { 'type': this.getType(this.targetData), 'node_name': this.targetData.name, 'ip': this.formData.target_ip || null, 'device_port_type': this.formData.target_device_port_type || null }
          }
        }
        if (this.selectedSourcePort) {
          postData['edgeData']['source']['port_name'] = this.selectedSourcePort.name
        }
        if (this.selectedTargetPort) {
          postData['edgeData']['dest']['port_name'] = this.selectedTargetPort.name
        }
        postData['source_cidr'] = this.formData.source_cidr
        postData['source_gateway'] = this.formData.source_gateway
        postData['target_cidr'] = this.formData.target_cidr
        postData['target_gateway'] = this.formData.target_gateway
        postData['edgeData']['network_type'] = this.formData.network_type
        postData['edgeData']['segmentation_id'] = this.formData.segmentation_id
        this.$emit('call', 'configPort', postData)
      }
    }
  }
}
</script>

<style lang="less" scoped>
.bottom-form {
  padding-top: 24px !important;
  margin-top: 24px;
  border-top: 1px solid #c8cacd;
}
.config-port-select {
  width: 252px;
  .el-tag {
    float: right;
    border-radius: 6px;
    height: 20px;
    line-height: 20px;
    margin-top: 7px;
    padding: 0 5px;
  }
}
.edge-config-warp {
  display: flex;
  .x6-graph {
    margin-top: 7px;
  }
  .el-form {
    flex: 1;
  }
  .custom-warning-icon {
    display: none;
  }
}
</style>
