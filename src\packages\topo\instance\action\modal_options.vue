<template>
  <div v-loading="loading" class="dialog-wrap">
    <batch-template :data="data" :available-data="availableArr"/>
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button :disabled="!availableArr.length" type="primary" @click="confirm" >确定</el-button>
    </div>
  </div>
</template>
<script>
import module from '../config.js'
import { start, stop, reboot, modalDelete, suspend, resume, rebuild, pause } from '../../api/orchestration'
import batchTemplate from '../../../batch-delete/modal-bat-template.vue'
import modalMixins from '../../../mixins/modal_form'
export default {
  components: {
    batchTemplate
  },
  mixins: [modalMixins],
  props: {
    // 传入数据
    name: {
      type: String
    },
    data: {
      type: Array
    }
  },
  data() {
    return {
      moduleName: module.name,
      modalDeleteStatus: ['error', 'shutoff', 'running', 'paused', 'suspended'], // 删除
      startStatus: ['shutoff'], // 开机 可操作的状态
      stopStatus: ['running'], // 关机 可操作的状态
      rebootStatus: ['running'], // 重启 可操作的状态
      suspendStatus: ['running'], // 挂起 可操作的状态
      resumeStatus: ['suspended', 'paused'], // 恢复 可操作的状态
      rebuildStatus: ['shutoff', 'running'], // 重建 可操作的状态
      pauseStatus: ['running'], // 暂停 可操作的状态
      snapshotStatus: ['shutoff', 'running'], // 创建快照 可操作的状态
      toImageStatus: ['shutoff', 'running', 'paused', 'suspended'], // 生成镜像 可操作的状态
      loading: false,
      apiObj: {
        'start': start,
        'stop': stop,
        'reboot': reboot,
        'modalDelete': modalDelete,
        'suspend': suspend,
        'resume': resume,
        'rebuild': rebuild,
        'pause': pause
      },
      titleMap: {
        'start': '开机',
        'stop': '关机',
        'reboot': '重启',
        'modalDelete': '删除',
        'suspend': '挂起',
        'resume': '恢复',
        'rebuild': '重建',
        'pause': '暂停'
      }
    }
  },
  computed: {
    // 筛选返回可操作数据
    'availableArr': function() {
      const _data = []
      this.data.forEach((item) => {
        if (this.handleAvailable(item)) {
          _data.push(item)
        }
      })
      return _data
    }
  },
  methods: {
    // 可操作数据判断逻辑
    handleAvailable(item) {
      return !item.task && this[this.name + 'Status'].indexOf(item.status.toLowerCase()) > -1
    },
    // 点击取消
    close() {
      this.$emit('close')
    },
    'confirm': function(modal) {
      this.loading = true
      this.$bus.$emit(
        'BAT_TASK_API',
        {
          taskName: this.titleMap[this.name],
          resource: this.availableArr,
          apiObj: this.apiObj[this.name],
          data: {},
          sucsessCallback: (res) => {
            this.$bus.$emit(this.moduleName + '_module', 'reload')
          },
          errorCallback: () => {}
        }
      )
      this.$emit('close')
    }
  }
}
</script>
