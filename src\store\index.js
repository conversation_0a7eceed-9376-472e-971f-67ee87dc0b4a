import Vue from 'vue'
import Vuex from 'vuex'
import user from './modules/user'
import permission from './modules/permission'
import app from './modules/app'
import getters from './getters'
import device from '@/packages/topo/store/device'
import socketListener from '@/packages/topo/store/socket_listener'
import websocketListener from './modules/websocketListener' // 靶场 websocket
import cache from './modules/cache' // 列表搜索缓存

Vue.use(Vuex)

const store = new Vuex.Store({
  modules: {
    app,
    permission,
    user,
    device,
    socketListener,
    websocketListener,
    cache
  },
  getters
})

export default store
