<template>
  <div class="drawer-wrap">
    <div style="padding: 15px 15px 0px 15px;">
      <div class="drawer-fliter-wrap">
        <div class="drawer-fliter-item">
          <div class="drawer-fliter-title">
            <span>设备类型</span>
          </div>
          <el-radio-group v-model="type" size="small">
            <el-radio-button :disabled="questionType == 9" label="pnf">物理设备</el-radio-button>
            <el-radio-button label="vnf">虚拟设备</el-radio-button>
          </el-radio-group>
        </div>
      </div>
    </div>
    <physical-table
      v-if="type === 'pnf'"
      ref="pnfTable"
      :custom-col-data="['icon', 'name', 'port_use']"
      :filter-data="{ type: 'pnf' }"
      :link="false"
      :single="true"
      :not-allowed-arr="[]"
      height="auto"
      @on-select="onSelect"
    />
    <!-- TODO 过滤掉云路由的虚拟设备 -->
    <virtual-table
      v-else
      ref="vnfTable"
      :custom-col-data="['icon', 'name', 'virtual_type', 'instance_count']"
      :filter-data="{ type: 'vnf' }"
      :link="false"
      :single="true"
      :not-allowed-arr="[]"
      height="auto"
      @on-select="onSelect"
    />
    <div class="drawer-footer">
      <el-button :disabled="!selectedItem.length" type="primary" @click="confirm">确定</el-button>
      <el-button type="text" @click="close">取消</el-button>
    </div>
  </div>
</template>

<script>
import virtualTable from '../../table/virtual'
import physicalTable from '../../table/physical'
export default {
  components: {
    virtualTable,
    physicalTable
  },
  props: {
    questionType: {
      type: String,
      default: '4'
    }
  },
  data() {
    return {
      type: 'pnf',
      selectedItem: []
    }
  },
  created() {
    if (this.questionType == 9) {
      this.type = 'vnf'
    }
  },
  methods: {
    'onSelect': function(data) {
      this.selectedItem = data
    },
    'close': function() {
      this.$emit('close')
    },
    'confirm': function() {
      this.$emit('call', 'confirm_NE', this.selectedItem)
    }
  }
}
</script>
