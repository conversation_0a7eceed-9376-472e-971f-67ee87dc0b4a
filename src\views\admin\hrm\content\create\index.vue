<template>
  <create-view
    :loading="loading"
    :goback = "true"
    :title="type === '编辑' ? '编辑课程内容' : '创建课程内容'"
  >
    <div :slot="isTopoStep ? 'topo' : 'content'" class="create-personal-wrap">
      <el-steps :active="active" :style="{'width': '100%', 'padding': '13px 5%', 'max-width': isTopoStep ? '69%' : '100%'}" simple space="10">
        <el-step
          v-for="(value, index) in stepList"
          :key="index" :title="value" :class="['step-item', { 'pointer': type == '编辑' && (!disabledPrev &&!disabledNext) }]" @click.native="toggleStep(index)"/>
      </el-steps>
      <div class="step-wrap">
        <Video v-if="active == 1" ref="compRef" @call="handleCall"/>
        <Report v-else-if="active == 2" ref="compRef" @call="handleCall"/>
        <Manual v-else-if="active == 4" ref="compRef" @call="handleCall"/>
        <component
          v-else
          ref="compRef"
          :key="+new Date()"
          :is="stepComponentMap[active]"
          :select-question-list="checkQuestionList"
          :dynamic-paper-data="dynamicPaperData"
          :data="buildForm"
          :topology-mode="buildForm.topologyMode"
          :source-scene-id-str="sourceSceneIdStr"
          @on-select="tableSelect"
          @call="handleCall"
        />
      </div>
    </div>
    <div slot="footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button v-if="active > 0" :disabled="disabledPrev" type="primary" @click="back()">上一步</el-button>
      <el-button
        v-if="active < stepTotal"
        :disabled="disabledNext"
        :loading="loading"
        type="primary"
        @click="next()"
      >下一步</el-button
      >
      <el-button
        v-if="active === stepTotal" :loading="submitLoading"
        type="primary"
        @click="confirm"
      >确定</el-button
      >
    </div>
  </create-view>
</template>
<script>
import createView from '@/packages/create-view/index'
import Info from './Info.vue'
import Video from './Video.vue'
import Report from './Report.vue'
import Practice from './newPractice.vue'
import Manual from './Manual.vue'
import Topo from './Topo.vue'
import selectScene from './selectScene.vue'
import mixin from './mixin'
import { debounce } from 'throttle-debounce'
import { pjtCourseCategoryList as contentCategory, searchPointApi, updateAnswerTimeById, queryPractice, savePractice, updateDynamicPractice, updatePracticeAPI, getContentById, updateSourceSceneId } from '@/api/teacher/index.js'

const stepAllComponentMap1 = {
  '基本信息': Info,
  '视频讲解': Video,
  '课件资料': Report,
  '随堂练习': Practice
}
const stepAllComponentMap2 = {
  '基本信息': Info,
  '视频讲解': Video,
  '课件资料': Report,
  '随堂练习': Practice,
  '操作手册': Manual,
  '拓扑编排': Topo
}
// 共享拓扑
const stepAllComponentMap3 = {
  '基本信息': Info,
  '视频讲解': Video,
  '课件资料': Report,
  '随堂练习': Practice,
  '操作手册': Manual,
  '选择场景': selectScene
}

export default {
  components: {
    createView,
    Info,
    Video,
    Report,
    Practice,
    Manual,
    Topo,
    selectScene
  },
  mixins: [mixin],
  data() {
    return {
      sourceSceneIdStr: '',
      submitLoading: false,
      categoryList: [], // 题目分类
      checkQuestionList: [],
      active: +this.$route.query.active || 0,
      loading: false,
      stepAllComponentMap1,
      stepAllComponentMap2,
      stepAllComponentMap3,
      stepComponentMap: stepAllComponentMap1,
      stepList: [],
      buildForm: {
        contentType: 1,
        topologyAllocation: 0,
        topologyMode: 0, // 拓扑方式 0:自定义拓扑 1:选择仿真场景
        name: '',
        contentCategoryId: '',
        contentLevel: '',
        contentPeriod: '',
        contentKnowledgeIdList: []
      },
      knowledgeCategoryAll: [],
      contentCategoryList: [], // 分类选项
      knowledgeList: [], // 知识点选项
      contentType: this.$route.query.contentType || 1,
      topologyAllocation: this.$route.query.topologyAllocation || 0,
      topologyMode: this.$route.query.topologyMode || 0,
      evaluationCode: this.$route.query.evaluationCode,
      curriculumCode: this.$route.query.curriculumCode,
      sourceSceneId: '',
      topologyTemplateId: '',
      sceneData: null,
      answerLimitSecond: 0,
      answerTime: 0,
      disabledPrev: false,
      disabledNext: false,
      isRequestedQs: false, // 是否请求过题目
      isExamMode: false,
      practiceActiveName: 'judge',
      practiceDeleteClick: false,
      checkedQuestionData: null,
      questionSource: '1', // 题目来源
      examType: this.$route.query.examType || null, // 试卷类型
      dynamicExamId: '', // 动态试卷id
      dynamicPaperData: {} // 动态试卷展示规则
    }
  },
  provide() {
    return {
      'parentVm': this
    }
  },
  computed: {
    stepTotal() {
      return Object.keys(this.stepComponentMap).length - 1
    },
    checkQuestionCodes() {
      let questionCodes = []
      if (Array.isArray(this.checkQuestionList) && this.checkQuestionList.length > 0) {
        questionCodes = this.checkQuestionList.map(v => v.questionCode)
      }
      return questionCodes
    },
    isTopoStep() {
      return this.active == 5 && this.stepList[this.active] == '拓扑编排'
    }
  },
  watch: {
    'active': {
      handler: function(newVal) {
        if (newVal == 3 && !this.isRequestedQs) { // 是随堂练习时才请求数据
          this.searchCurriculum()
        }
      },
      immediate: true
    },
    'buildForm.contentType': {
      handler(newValue, oldValue) {
        this.contentType = newValue
        const compArr = Object.values(
          newValue == 1 ? stepAllComponentMap1 : stepAllComponentMap2
        )
        this.stepList = Object.keys(
          newValue == 1 ? stepAllComponentMap1 : stepAllComponentMap2
        )
        const result = compArr.reduce((acc, prev, i) => {
          acc[i] = prev
          return acc
        }, {})
        this.stepComponentMap = result
      },
      deep: true,
      immediate: true
    },
    'buildForm.topologyAllocation': {
      handler(newValue, oldValue) {
        this.topologyAllocation = newValue
        if (this.contentType == 2) {
          const compArr = Object.values(
            newValue == '0' ? stepAllComponentMap2 : stepAllComponentMap3
          )
          this.stepList = Object.keys(
            newValue == '0' ? stepAllComponentMap2 : stepAllComponentMap3
          )
          const result = compArr.reduce((acc, prev, i) => {
            acc[i] = prev
            return acc
          }, {})
          this.stepComponentMap = result
        }
      },
      deep: true,
      immediate: true
    },
    // 仿真独享改变拓扑模式
    'buildForm.topologyMode': {
      handler(newValue, oldValue) {
        this.topologyMode = newValue
        if (this.contentType == 2) {
          // 仿真独享选择场景编辑时改变该场景拓扑
          if (this.type == '编辑' && this.topologyAllocation == '0' && newValue == '1') {
            const compArr = Object.values(stepAllComponentMap2)
            this.stepList = Object.keys(stepAllComponentMap2)
            const result = compArr.reduce((acc, prev, i) => {
              acc[i] = prev
              return acc
            }, {})
            this.stepComponentMap = result
          } else {
            const compArr = Object.values(
              newValue == '0' ? stepAllComponentMap2 : stepAllComponentMap3
            )
            this.stepList = Object.keys(
              newValue == '0' ? stepAllComponentMap2 : stepAllComponentMap3
            )
            const result = compArr.reduce((acc, prev, i) => {
              acc[i] = prev
              return acc
            }, {})
            this.stepComponentMap = result
          }
        }
      },
      deep: true,
      immediate: true
    }
  },
  async mounted() {
    // 先获取下拉数据，再获取详情数据，避免先回显id
    await this.getSelectOptions()
    if (this.contentId) {
      this.getContentByIdAPI()
    }
  },
  beforeDestroy() {
    localStorage.removeItem('dataId')
  },
  methods: {
    // 获取相关选项数据
    getSelectOptions() {
      searchPointApi({}).then(res => {
        if (res.code == 0) {
          this.knowledgeList = res.data || []
        }
      })
      contentCategory({ id: '' }).then(res => {
        if (res.code == 0) {
          this.contentCategoryList = res.data || []
        }
      })
    },
    // 返回已选
    tableSelect: function(data) {
      this.sourceSceneId = data.id
      this.topologyTemplateId = data.topologyTemplateId
    },
    // 设置限时时间
    setAnswerTime() {
      if (this.answerLimitSecond && this.active == this.stepTotal) {
        const params = {
          contentId: JSON.parse(localStorage.getItem('dataId')) || this.id,
          answerTime: this.answerLimitSecond
        }
        updateAnswerTimeById(params).then(res => {
          if (res.code === 0 || res.code === 200) {
            console.log('随堂联系答题时限设置成功')
          }
        })
      }
    },
    getContentByIdAPI() {
      if (this.$route.query['noRefresh'] != '1') {
        this.loading = true
      }
      const params = { id: this.contentId }
      getContentById(params).then(res => {
        if (res.code == 0) {
          for (const key in this.buildForm) {
            if (key == 'contentType') {
              this.buildForm['contentType'] = Number(res.data['contentType'])
            } else if (key == 'contentKnowledgeIdList') {
              const numArray = res.data['contentKnowledgeIdList'].split(',').map(str => Number(str))
              this.buildForm['contentKnowledgeIdList'] = numArray
            } else {
              this.buildForm[key] = res.data[key]
            }
          }
          this.sourceSceneId = res.data.sourceSceneIdStr
          this.sourceSceneIdStr = res.data.sourceSceneIdStr
          this.questionSource = res.data['questionSource'] || '1'
          this.answerTime = (res.data['answerTime'] / 60).toFixed(0) // 回显答题时长
          this.isExamMode = res.data.isExamMode || 0
          if (res.data['knowledgeVoList']) {
            this.categoryList = res.data['knowledgeVoList'].map(item => ({ id: item.knowledgeCode, [this.questionSource == '1' ? 'categoryName' : 'examName']: item.knowledgeName }))
          }
        }
      }).finally(() => {
        this.loading = false
      })
    },
    toggleStep(active) {
      if (this.active === active || this.disabledPrev || this.disabledNext) return
      // 创建不可切换、编辑可以点击切换
      if (this.type === '创建') {
        return
      }
      const compRef = this.$refs.compRef
      // 当前是基本信息时，回显最新基本信息数据
      if (this.active == 0) {
        for (const key in this.buildForm) {
          this.buildForm[key] = compRef['buildForm'][key]
        }
      }
      // 存在提交的步骤，需等提交成功后再切换到下一步
      if (compRef.confirm) {
        compRef.confirm(() => {
          this.active = active
        })
      } else {
        this.active = active
      }
    },
    back() {
      const compRef = this.$refs.compRef
      if (compRef.practicePaper) {
        compRef.confirmScore()
        if (compRef.bool) return
      }
      // 保存操作手册的富文本编辑器
      if (this.active == 4 && compRef.formData && compRef.formData.operationManualType) {
        compRef.confirm()
      }
      if (compRef.sceneData) {
        this.sourceSceneId = compRef.sceneData.id
        this.sourceSceneIdStr = compRef.sceneData.id
      }
      this.active--
    },
    next() {
      const compRef = this.$refs.compRef
      if (this.active == 0) {
        compRef.$refs['buildForm'].validate((valid) => {
          if (valid) {
            for (const key in this.buildForm) {
              this.buildForm[key] = compRef['buildForm'][key]
            }
            compRef.saveContentInfo(false)
          }
        })
      } else {
        if (this.contentType == '2' && this.active == 3) { // 拓扑随堂练习
          if (Array.isArray(this.checkQuestionList) && this.checkQuestionList.length > 0) {
            const zeroQuestionNum = this.checkQuestionList.filter(item => item.questionScore === 0).length
            if (zeroQuestionNum > 0) {
              this.zeroQuestionConfirm(zeroQuestionNum)
              return
            }
          }
        }
        // 保存操作手册的富文本编辑器
        if (this.active == 4 && compRef.formData && compRef.formData.operationManualType) {
          compRef.confirm()
        }
        if (compRef.sceneData) {
          this.sourceSceneId = compRef.sceneData.id
          this.sourceSceneIdStr = compRef.sceneData.id
        }
        this.active++
      }
    },
    handleCall(type, data) {
      switch (type) {
        case 'info':
          this.active++
          break
        case 'chooseQuestion':
          this.checkedQuestionData = data
          break
        case 'check-question-list-change':
          this.checkQuestionList = data
          break
        case 'uploading':
          this.disabledPrev = data
          this.disabledNext = data
          break
      }
    },
    // 题目为0分的提示
    zeroQuestionConfirm(num) {
      this.$confirm(`尚有${num}道题目为0分，请确认是否继续？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        if (this.contentType == '1') { // 理论
          this.evaluationCodeFn()
          if (this.contentType == '2' && this.buildForm.topologyAllocation == '1') { // 仿真共享
            this.updateSourceSceneId()
          }
          if (this.contentType == '2' && this.buildForm.topologyAllocation == '0' && this.buildForm.topologyMode == '1') { // 仿真独享选择场景
            this.updateSourceSceneId()
          }
        }
        if (this.contentType == '2') {
          const compRef = this.$refs.compRef
          // 保存操作手册的富文本编辑器
          if (this.active == 4 && compRef.formData && compRef.formData.operationManualType) {
            compRef.confirm()
          }
          if (compRef.sceneData) {
            this.sourceSceneId = compRef.sceneData.id
            this.sourceSceneIdStr = compRef.sceneData.id
          }
          this.active++
        }
      }).catch(() => {
      })
    },
    confirm: debounce(500, false, function() {
      if (this.contentType == '1') { // 理论
        if (Array.isArray(this.checkQuestionList) && this.checkQuestionList.length > 0) {
          const zeroQuestionNum = this.checkQuestionList.filter(item => item.questionScore === 0).length
          if (zeroQuestionNum > 0) {
            this.zeroQuestionConfirm(zeroQuestionNum)
            return
          }
        }
        this.evaluationCodeFn()
        return
      }
      if (this.contentType == '2' && this.buildForm.topologyAllocation == '1') { // 仿真共享
        this.updateSourceSceneId()
        return
      }
      if (this.contentType == '2' && this.buildForm.topologyAllocation == '0' && this.buildForm.topologyMode == '1' && !this.contentId) { // 仿真独享选择场景
        this.updateSourceSceneId()
        return
      }
      if ((this.contentType == '2' && this.buildForm.topologyAllocation == '0' && this.buildForm.topologyMode == '0') || this.contentId) {
        this.evaluationCodeFn()
      }
    }),
    close: function() {
      this.$router.push({
        name: 'trainingContent'
      })
    },
    // 保存场景id
    updateSourceSceneId() {
      if (this.sourceSceneId) {
        const postData = {
          contentId: JSON.parse(localStorage.getItem('dataId')) || this.id,
          sourceSceneId: this.sourceSceneId,
          topologyTemplateId: this.topologyTemplateId,
          topologyAllocation: this.topologyAllocation
        }
        updateSourceSceneId(postData).then(res => {
          if (res.code == 0) {
            this.evaluationCodeFn()
          }
        })
      } else {
        this.$message.error('请选择场景')
      }
    },
    // 提交题目
    evaluationCodeFn() {
      if (this.evaluationCode) {
        this.updatePractice()
      } else {
        // 编辑动态试卷
        if (this.type === '编辑' && this.examType === 1) {
          this.submitLoading = true
          const params = {
            contentId: this.contentId,
            examId: this.dynamicExamId,
            isExamMode: this.isExamMode
          }
          updateDynamicPractice(params).then(res => {
            this.$message.success('编辑成功')
            this.close()
            this.submitLoading = false
          }).finally(() => {
            this.submitLoading = false
          })
        } else {
          this.handleManualBuildPaper()
        }
      }
    },
    updatePractice() {
      this.submitLoading = true
      updatePracticeAPI({
        evaluationCode: this.evaluationCode,
        questionCodeList: this.checkQuestionCodes,
        curriculumCode: this.curriculumCode,
        isExamMode: this.isExamMode
      }).then(res => {
        if (res.code == 0) {
          this.$message.success('修改成功')
          this.close()
        }
      }).finally(() => {
        this.submitLoading = false
      })
    },
    // 手动组卷
    handleManualBuildPaper() {
      this.setAnswerTime()
      this.submitLoading = true
      const { questionSource = null, categoryList = [] } = this.type === '创建' ? (this.checkedQuestionData || {}) : this.$route.query
      const classifyId = categoryList.map(item => item.id)
      const params = {
        contentId: JSON.parse(localStorage.getItem('dataId')) || this.id || this.checkedQuestionData.id,
        examQuestionCode: this.checkQuestionCodes,
        questionSource: questionSource, // 题目来源
        knowledgeCategoryAll: classifyId.join(','),
        isExamMode: this.isExamMode
      }
      let question = []
      if (Array.isArray(this.checkQuestionList) && this.checkQuestionList.length > 0) {
        question = this.checkQuestionList.map((item) => {
          return {
            questionCode: item.questionCode,
            questionScore: item.questionScore
          }
        })
        params.question = question
      }
      if (this.examType === 1) {
        params.examType = this.examType
        params.examId = this.dynamicExamId
      }
      savePractice(params).then((ret) => {
        if (ret.code == 0) {
          this.$message.success('保存成功')
          this.close()
        } else {
          this.$message.error('保存失败')
        }
      }).finally(() => {
        this.submitLoading = false
      })
    },
    searchCurriculum: debounce(500, false, function() {
      this.loading = true
      // 这里放置需要防抖的操作
      let bankType
      let bankTypes
      if (this.$route.query.contentType == 1) {
        bankType = 1
      } else if (this.$route.query.contentType == 2) {
        bankTypes = [1, 2, 3]
      }
      const params = {
        contentId: this.contentId,
        bankType, bankTypes,
        isShowAnswers: true
      }
      if (!['', null, undefined].includes(this.examType)) {
        params.examType = this.examType
      }
      queryPractice(params).then(res => {
        if (res.code == 0) {
          this.isRequestedQs = true
          this.checkQuestionList = res.data || []
          // 动态试卷返回的对象
          if (Object.prototype.toString.call(res.data) === '[object Object]') {
            this.dynamicExamId = res.data.id
            this.dynamicPaperData = res.data || {}
          }
        }
      }).finally(() => {
        this.loading = false
      })
    })
  }
}
</script>
<style lang="scss" scoped>
@import "./common";
/deep/.step-item.pointer {
  .el-step__title {
    cursor: pointer;
  }
}
/deep/ .el-step__title {
  max-width: 100% !important;
}
/deep/ .create-body-topo {
  margin-top: 10px;
  .wrapper {
    padding: 0 !important;
  }
}
</style>
