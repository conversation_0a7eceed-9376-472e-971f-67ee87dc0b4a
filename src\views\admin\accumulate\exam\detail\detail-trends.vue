<template>
  <div class="_paper_container card-bg">
    <div class="_paper_header">
      <div class="_paper_search">
        <div class="_paper_search_1">
          总题数
          <span style="font-weight: bold; color: var(--color-600); font-size: 18px; margin-right: 10px;">
            {{ theoryScore.countPrimarySingle + theoryScore.countMiddleSingle + theoryScore.countSeniorSingle +
              theoryScore.countPrimaryMultiple + theoryScore.countMiddleMultiple + theoryScore.countSeniorMultiple +
              theoryScore.countPrimaryJudge + theoryScore.countMiddleJudge + theoryScore.countSeniorJudge +
              theoryScore.countPrimaryCTF + theoryScore.countMiddleCTF + theoryScore.countSeniorCTF +
              theoryScore.countPrimaryCompletion + theoryScore.countMiddleCompletion + theoryScore.countSeniorCompletion +
              theoryScore.countPrimaryShort + theoryScore.countMiddleShort + theoryScore.countSeniorShort +
              targetScore.countPrimaryCTF + targetScore.countMiddleCTF + targetScore.countSeniorCTF +
              targetScore.countPrimaryAWD + targetScore.countMiddleAWD + targetScore.countSeniorAWD +
              targetScore.countPrimaryBug + targetScore.countMiddleBug + targetScore.countSeniorBug +
              targetScore.countPrimaryOther + targetScore.countMiddleOther + targetScore.countSeniorOther +
              simulationScore.countPrimarySingle + simulationScore.countMiddleSingle + simulationScore.countSeniorSingle +
              simulationScore.countPrimaryMultiple + simulationScore.countMiddleMultiple + simulationScore.countSeniorMultiple +
              simulationScore.countPrimaryJudge + simulationScore.countMiddleJudge + simulationScore.countSeniorJudge +
              simulationScore.countPrimaryCTF + simulationScore.countMiddleCTF + simulationScore.countSeniorCTF +
              simulationScore.countPrimaryCompletion + simulationScore.countMiddleCompletion + simulationScore.countSeniorCompletion +
              simulationScore.countPrimaryShort + theoryScore.countMiddleShort + simulationScore.countSeniorShort +
            simulationScore.countPrimaryCombinatorial + simulationScore.countMiddleCombinatorial + simulationScore.countSeniorCombinatorial }}
          </span>
        </div>
        <div class="_paper_search_1">
          总分数
          <span style="font-weight: bold; color: var(--color-600); font-size: 18px;">
            {{ ((theoryScore.countPrimarySingle + theoryScore.countMiddleSingle + theoryScore.countSeniorSingle) * theoryRuleVO.scoreSingle) +
              ((theoryScore.countPrimaryMultiple + theoryScore.countMiddleMultiple + theoryScore.countSeniorMultiple) * theoryRuleVO.scoreMultiple) +
              ((theoryScore.countPrimaryJudge + theoryScore.countMiddleJudge + theoryScore.countSeniorJudge) * theoryRuleVO.scoreJudge) +
              ((theoryScore.countPrimaryCTF + theoryScore.countMiddleCTF + theoryScore.countSeniorCTF) * theoryRuleVO.scoreCTF) +
              ((theoryScore.countPrimaryCompletion + theoryScore.countMiddleCompletion + theoryScore.countSeniorCompletion) * theoryRuleVO.scoreCompletion) +
              ((theoryScore.countPrimaryShort + theoryScore.countMiddleShort + theoryScore.countSeniorShort) * theoryRuleVO.scoreShort) +
              ((targetScore.countPrimaryCTF + targetScore.countMiddleCTF + targetScore.countSeniorCTF) * targetRuleVO.scoreCTF) +
              ((targetScore.countPrimaryAWD + targetScore.countMiddleAWD + targetScore.countSeniorAWD) * targetRuleVO.scoreAWD) +
              ((targetScore.countPrimaryBug + targetScore.countMiddleBug + targetScore.countSeniorBug) * targetRuleVO.scoreBug) +
              ((targetScore.countPrimaryOther + targetScore.countMiddleOther + targetScore.countSeniorOther) * targetRuleVO.scoreOther) +
              ((simulationScore.countPrimarySingle + simulationScore.countMiddleSingle + simulationScore.countSeniorSingle) * simulationRuleVO.scoreSingle) +
              ((simulationScore.countPrimaryMultiple + simulationScore.countMiddleMultiple + simulationScore.countSeniorMultiple) * simulationRuleVO.scoreMultiple) +
              ((simulationScore.countPrimaryJudge + simulationScore.countMiddleJudge + simulationScore.countSeniorJudge) * simulationRuleVO.scoreJudge) +
              ((simulationScore.countPrimaryCTF + simulationScore.countMiddleCTF + simulationScore.countSeniorCTF) * simulationRuleVO.scoreCTF) +
              ((simulationScore.countPrimaryCompletion + simulationScore.countMiddleCompletion + simulationScore.countSeniorCompletion) * simulationRuleVO.scoreCompletion) +
              ((simulationScore.countPrimaryShort + simulationScore.countMiddleShort + simulationScore.countSeniorShort) * simulationRuleVO.scoreShort) +
            ((simulationScore.countPrimaryCombinatorial + simulationScore.countMiddleCombinatorial + simulationScore.countSeniorCombinatorial) * simulationRuleVO.scoreCombinatorial) }}
          </span>
        </div>
      </div>
    </div>
    <el-tabs v-model="activeName">
      <el-tab-pane label="理论" name="theory">
        <div class="mt-10 ">
          <div class="mt-10 trends-table">
            <div class="trends-content border-b" style="padding-bottom: 6px;">
              <div>难易程度</div>
              <div>单选题</div>
              <div>多选题</div>
              <div>判断题</div>
              <div>CTF题</div>
              <div>填空题</div>
              <div>简答题</div>
            </div>
            <div class="trends-content border-b">
              <div>初级</div>
              <div>{{ theoryScore.countPrimarySingle }}</div>
              <div>{{ theoryScore.countPrimaryMultiple }}</div>
              <div>{{ theoryScore.countPrimaryJudge }}</div>
              <div>{{ theoryScore.countPrimaryCTF }}</div>
              <div>{{ theoryScore.countPrimaryCompletion }}</div>
              <div>{{ theoryScore.countPrimaryShort }}</div>
            </div>
            <div class="trends-content border-b">
              <div>中级</div>
              <div>{{ theoryScore.countMiddleSingle }}</div>
              <div>{{ theoryScore.countMiddleMultiple }}</div>
              <div>{{ theoryScore.countMiddleJudge }}</div>
              <div>{{ theoryScore.countMiddleCTF }}</div>
              <div>{{ theoryScore.countMiddleCompletion }}</div>
              <div>{{ theoryScore.countMiddleShort }}</div>
            </div>
            <div class="trends-content border-b">
              <div>高级</div>
              <div>{{ theoryScore.countSeniorSingle }}</div>
              <div>{{ theoryScore.countSeniorMultiple }}</div>
              <div>{{ theoryScore.countSeniorJudge }}</div>
              <div>{{ theoryScore.countSeniorCTF }}</div>
              <div>{{ theoryScore.countSeniorCompletion }}</div>
              <div>{{ theoryScore.countSeniorShort }}</div>
            </div>
            <div class="trends-content  border-b">
              <div>合计</div>
              <div>{{ theoryScore.countPrimarySingle + theoryScore.countMiddleSingle + theoryScore.countSeniorSingle }}</div>
              <div>{{ theoryScore.countPrimaryMultiple + theoryScore.countMiddleMultiple + theoryScore.countSeniorMultiple }}</div>
              <div>{{ theoryScore.countPrimaryJudge + theoryScore.countMiddleJudge + theoryScore.countSeniorJudge }}</div>
              <div>{{ theoryScore.countPrimaryCTF + theoryScore.countMiddleCTF + theoryScore.countSeniorCTF }}</div>
              <div>{{ theoryScore.countPrimaryCompletion + theoryScore.countMiddleCompletion + theoryScore.countSeniorCompletion }}</div>
              <div>{{ theoryScore.countPrimaryShort + theoryScore.countMiddleShort + theoryScore.countSeniorShort }}</div>
            </div>
            <div class="trends-content border-b">
              <div>单题分数</div>
              <div>{{ theoryRuleVO.scoreSingle }}</div>
              <div>{{ theoryRuleVO.scoreMultiple }}</div>
              <div>{{ theoryRuleVO.scoreJudge }}</div>
              <div>{{ theoryRuleVO.scoreCTF }}</div>
              <div>{{ theoryRuleVO.scoreCompletion }}</div>
              <div>{{ theoryRuleVO.scoreShort }}</div>
            </div>
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane label="靶机" name="target">
        <div class="mt-10 trends-table">
          <div class="mt-10">
            <div class="trends-content border-b" style="padding-bottom: 6px;">
              <div>难易程度</div>
              <div>CTF题</div>
              <div>AWD题</div>
              <div>漏洞题</div>
              <div>其它题</div>
            </div>
            <div class="trends-content border-b">
              <div>初级</div>
              <div>{{ targetScore.countPrimaryCTF }}</div>
              <div>{{ targetScore.countPrimaryAWD }}</div>
              <div>{{ targetScore.countPrimaryBug }}</div>
              <div>{{ targetScore.countPrimaryOther }}</div>
            </div>
            <div class="trends-content border-b">
              <div>中级</div>
              <div>{{ targetScore.countMiddleCTF }}</div>
              <div>{{ targetScore.countMiddleAWD }}</div>
              <div>{{ targetScore.countMiddleBug }}</div>
              <div>{{ targetScore.countMiddleOther }}</div>
            </div>
            <div class="trends-content border-b">
              <div>高级</div>
              <div>{{ targetScore.countSeniorCTF }}</div>
              <div>{{ targetScore.countSeniorAWD }}</div>
              <div>{{ targetScore.countSeniorBug }}</div>
              <div>{{ targetScore.countSeniorOther }}</div>
            </div>
            <div class="trends-content  border-b">
              <div>合计</div>
              <div>{{ targetScore.countPrimaryCTF + targetScore.countMiddleCTF + targetScore.countSeniorCTF }}</div>
              <div>{{ targetScore.countPrimaryAWD + targetScore.countMiddleAWD + targetScore.countSeniorAWD }}</div>
              <div>{{ targetScore.countPrimaryBug + targetScore.countMiddleBug + targetScore.countSeniorBug }}</div>
              <div>{{ targetScore.countPrimaryOther + targetScore.countMiddleOther + targetScore.countSeniorOther }}</div>
            </div>
            <div class="trends-content border-b">
              <div>单题分数</div>
              <div>{{ targetRuleVO.scoreCTF }}</div>
              <div>{{ targetRuleVO.scoreAWD }}</div>
              <div>{{ targetRuleVO.scoreBug }}</div>
              <div>{{ targetRuleVO.scoreOther }}</div>
            </div>
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane label="仿真" name="simulation">
        <div class="mt-10 trends-table">
          <div class="mt-10">
            <div class="trends-content border-b" style="padding-bottom: 6px;">
              <div>难易程度</div>
              <div>单选题</div>
              <div>多选题</div>
              <div>判断题</div>
              <div>CTF题</div>
              <div>填空题</div>
              <div>简答题</div>
              <div>组合题</div>
            </div>
            <div class="trends-content border-b">
              <div>初级</div>
              <div>{{ simulationScore.countPrimarySingle }}</div>
              <div>{{ simulationScore.countPrimaryMultiple }}</div>
              <div>{{ simulationScore.countPrimaryJudge }}</div>
              <div>{{ simulationScore.countPrimaryCTF }}</div>
              <div>{{ simulationScore.countPrimaryCompletion }}</div>
              <div>{{ simulationScore.countPrimaryShort }}</div>
              <div>{{ simulationScore.countPrimaryCombinatorial }}</div>
            </div>
            <div class="trends-content border-b">
              <div>中级</div>
              <div>{{ simulationScore.countMiddleSingle }}</div>
              <div>{{ simulationScore.countMiddleMultiple }}</div>
              <div>{{ simulationScore.countMiddleJudge }}</div>
              <div>{{ simulationScore.countMiddleCTF }}</div>
              <div>{{ simulationScore.countMiddleCompletion }}</div>
              <div>{{ simulationScore.countMiddleShort }}</div>
              <div>{{ simulationScore.countMiddleCombinatorial }}</div>
            </div>
            <div class="trends-content border-b">
              <div>高级</div>
              <div>{{ simulationScore.countSeniorSingle }}</div>
              <div>{{ simulationScore.countSeniorMultiple }}</div>
              <div>{{ simulationScore.countSeniorJudge }}</div>
              <div>{{ simulationScore.countSeniorCTF }}</div>
              <div>{{ simulationScore.countSeniorCompletion }}</div>
              <div>{{ simulationScore.countSeniorShort }}</div>
              <div>{{ simulationScore.countSeniorCombinatorial }}</div>
            </div>
            <div class="trends-content  border-b">
              <div>合计</div>
              <div>{{ simulationScore.countPrimarySingle + simulationScore.countMiddleSingle + simulationScore.countSeniorSingle }}</div>
              <div>{{ simulationScore.countPrimaryMultiple + simulationScore.countMiddleMultiple + simulationScore.countSeniorMultiple }}</div>
              <div>{{ simulationScore.countPrimaryJudge + simulationScore.countMiddleJudge + simulationScore.countSeniorJudge }}</div>
              <div>{{ simulationScore.countPrimaryCTF + simulationScore.countMiddleCTF + simulationScore.countSeniorCTF }}</div>
              <div>{{ simulationScore.countPrimaryCompletion + simulationScore.countMiddleCompletion + simulationScore.countSeniorCompletion }}</div>
              <div>{{ simulationScore.countPrimaryShort + simulationScore.countMiddleShort + simulationScore.countSeniorShort }}</div>
              <div>{{ simulationScore.countPrimaryCombinatorial + simulationScore.countMiddleCombinatorial + simulationScore.countSeniorCombinatorial }}</div>
            </div>
            <div class="trends-content border-b">
              <div>单题分数</div>
              <div>{{ simulationRuleVO.scoreSingle }}</div>
              <div>{{ simulationRuleVO.scoreMultiple }}</div>
              <div>{{ simulationRuleVO.scoreJudge }}</div>
              <div>{{ simulationRuleVO.scoreCTF }}</div>
              <div>{{ simulationRuleVO.scoreCompletion }}</div>
              <div>{{ simulationRuleVO.scoreShort }}</div>
              <div>{{ simulationRuleVO.scoreCombinatorial }}</div>
            </div>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import questionConf from '../../questionBank/config.js'
import validate from '@/packages/validate'

export default {
  props: {
    id: {
      type: [Number, String]
    },
    data: {
      type: Object
    }
  },
  data() {
    return {
      questionConf: questionConf,
      validate: validate,
      activeName: 'theory',
      img: require('@/assets/empty_state.png'),
      // 理论题
      theoryRuleVO: {
        categoryVOList: [
          {
            categoryId: '',
            categoryVO: {},
            primarySingle: 0, // 初级单选题
            primaryMultiple: 0, // 初级多选题
            primaryJudge: 0, // 初级判断题
            primaryCTF: 0, // 初级CTF题
            primaryCompletion: 0, // 初级填空题
            primaryShort: 0, // 初级简答题
            primaryAWD: 0, // 初级awd题
            primaryBug: 0, // 初级bug题
            primaryCombinatorial: 0, // 初级组合题
            primaryOther: 0, // 初级其它题
            middleSingle: 0, // 中级单选题
            middleMultiple: 0, // 中级多选题
            middleJudge: 0, // 中级判断题
            middleCTF: 0, // 中级CTF题
            middleCompletion: 0, // 中级填空题
            middleShort: 0, // 中级简答题
            middleAWD: 0, // 中级awd题
            middleBug: 0, // 中级bug题
            middleCombinatorial: 0, // 中级组合题
            middleOther: 0, // 中级其它题
            seniorSingle: 0, // 高级单选题
            seniorMultiple: 0, // 高级多选题
            seniorJudge: 0, // 高级判断题
            seniorCTF: 0, // 高级CTF题
            seniorCompletion: 0, // 高级填空题
            seniorShort: 0, // 高级简答题
            seniorAWD: 0, // 高级awd题
            seniorBug: 0, // 高级bug题
            seniorCombinatorial: 0, // 高级组合题
            seniorOther: 0 // 高级其它题
          }
        ],
        scoreSingle: 0, // 单选题分数
        scoreMultiple: 0, // 多选题分数
        scoreJudge: 0, // 判断题分数
        scoreCTF: 0, // CTF题分数
        scoreCompletion: 0, // 填空题分数
        scoreShort: 0 // 简答题分数
      },
      // 靶机题
      targetRuleVO: {
        categoryVOList: [
          {
            categoryId: '',
            categoryVO: {},
            primarySingle: 0, // 初级单选题
            primaryMultiple: 0, // 初级多选题
            primaryJudge: 0, // 初级判断题
            primaryCTF: 0, // 初级CTF题
            primaryCompletion: 0, // 初级填空题
            primaryShort: 0, // 初级简答题
            primaryAWD: 0, // 初级awd题
            primaryBug: 0, // 初级bug题
            primaryCombinatorial: 0, // 初级组合题
            primaryOther: 0, // 初级其它题
            middleSingle: 0, // 中级单选题
            middleMultiple: 0, // 中级多选题
            middleJudge: 0, // 中级判断题
            middleCTF: 0, // 中级CTF题
            middleCompletion: 0, // 中级填空题
            middleShort: 0, // 中级简答题
            middleAWD: 0, // 中级awd题
            middleBug: 0, // 中级bug题
            middleCombinatorial: 0, // 中级组合题
            middleOther: 0, // 中级其它题
            seniorSingle: 0, // 高级单选题
            seniorMultiple: 0, // 高级多选题
            seniorJudge: 0, // 高级判断题
            seniorCTF: 0, // 高级CTF题
            seniorCompletion: 0, // 高级填空题
            seniorShort: 0, // 高级简答题
            seniorAWD: 0, // 高级awd题
            seniorBug: 0, // 高级bug题
            seniorCombinatorial: 0, // 高级组合题
            seniorOther: 0 // 高级其它题
          }
        ],
        scoreCTF: 0, // CTF题分数
        scoreAWD: 0, // AWD题分数
        scoreOther: 0, // 其它题分数
        scoreBug: 0 // 漏洞题分数
      },
      // 仿真题
      simulationRuleVO: {
        categoryVOList: [
          {
            categoryId: '',
            categoryVO: {},
            primarySingle: 0, // 初级单选题
            primaryMultiple: 0, // 初级多选题
            primaryJudge: 0, // 初级判断题
            primaryCTF: 0, // 初级CTF题
            primaryCompletion: 0, // 初级填空题
            primaryShort: 0, // 初级简答题
            primaryAWD: 0, // 初级awd题
            primaryBug: 0, // 初级bug题
            primaryCombinatorial: 0, // 初级组合题
            primaryOther: 0, // 初级其它题
            middleSingle: 0, // 中级单选题
            middleMultiple: 0, // 中级多选题
            middleJudge: 0, // 中级判断题
            middleCTF: 0, // 中级CTF题
            middleCompletion: 0, // 中级填空题
            middleShort: 0, // 中级简答题
            middleAWD: 0, // 中级awd题
            middleBug: 0, // 中级bug题
            middleCombinatorial: 0, // 中级组合题
            middleOther: 0, // 中级其它题
            seniorSingle: 0, // 高级单选题
            seniorMultiple: 0, // 高级多选题
            seniorJudge: 0, // 高级判断题
            seniorCTF: 0, // 高级CTF题
            seniorCompletion: 0, // 高级填空题
            seniorShort: 0, // 高级简答题
            seniorAWD: 0, // 高级awd题
            seniorBug: 0, // 高级bug题
            seniorCombinatorial: 0, // 高级组合题
            seniorOther: 0 // 高级其它题
          }
        ],
        scoreSingle: 0, // 单选题分数
        scoreMultiple: 0, // 多选题分数
        scoreJudge: 0, // 判断题分数
        scoreCTF: 0, // CTF题分数
        scoreCompletion: 0, // 填空题分数
        scoreShort: 0, // 简答题分数
        scoreCombinatorial: 0 // 组合题分数
      }
    }
  },
  computed: {
    // 理论题数量统计
    theoryScore() {
      const arr = {
        countPrimarySingle: this.theoryRuleVO.categoryVOList.map(item => { return item.primarySingle }).reduce((a, b) => a + b, 0),
        countMiddleSingle: this.theoryRuleVO.categoryVOList.map(item => { return item.middleSingle }).reduce((a, b) => a + b, 0),
        countSeniorSingle: this.theoryRuleVO.categoryVOList.map(item => { return item.seniorSingle }).reduce((a, b) => a + b, 0),
        countPrimaryMultiple: this.theoryRuleVO.categoryVOList.map(item => { return item.primaryMultiple }).reduce((a, b) => a + b, 0),
        countMiddleMultiple: this.theoryRuleVO.categoryVOList.map(item => { return item.middleMultiple }).reduce((a, b) => a + b, 0),
        countSeniorMultiple: this.theoryRuleVO.categoryVOList.map(item => { return item.seniorMultiple }).reduce((a, b) => a + b, 0),
        countPrimaryJudge: this.theoryRuleVO.categoryVOList.map(item => { return item.primaryJudge }).reduce((a, b) => a + b, 0),
        countMiddleJudge: this.theoryRuleVO.categoryVOList.map(item => { return item.middleJudge }).reduce((a, b) => a + b, 0),
        countSeniorJudge: this.theoryRuleVO.categoryVOList.map(item => { return item.seniorJudge }).reduce((a, b) => a + b, 0),
        countPrimaryCTF: this.theoryRuleVO.categoryVOList.map(item => { return item.primaryCTF }).reduce((a, b) => a + b, 0),
        countMiddleCTF: this.theoryRuleVO.categoryVOList.map(item => { return item.middleCTF }).reduce((a, b) => a + b, 0),
        countSeniorCTF: this.theoryRuleVO.categoryVOList.map(item => { return item.seniorCTF }).reduce((a, b) => a + b, 0),
        countPrimaryCompletion: this.theoryRuleVO.categoryVOList.map(item => { return item.primaryCompletion }).reduce((a, b) => a + b, 0),
        countMiddleCompletion: this.theoryRuleVO.categoryVOList.map(item => { return item.middleCompletion }).reduce((a, b) => a + b, 0),
        countSeniorCompletion: this.theoryRuleVO.categoryVOList.map(item => { return item.seniorCompletion }).reduce((a, b) => a + b, 0),
        countPrimaryShort: this.theoryRuleVO.categoryVOList.map(item => { return item.primaryShort }).reduce((a, b) => a + b, 0),
        countMiddleShort: this.theoryRuleVO.categoryVOList.map(item => { return item.middleShort }).reduce((a, b) => a + b, 0),
        countSeniorShort: this.theoryRuleVO.categoryVOList.map(item => { return item.seniorShort }).reduce((a, b) => a + b, 0)
      }
      return arr
    },
    // 靶机题数量统计
    targetScore() {
      const arr = {
        countPrimaryCTF: this.targetRuleVO.categoryVOList.map(item => { return item.primaryCTF }).reduce((a, b) => a + b, 0),
        countPrimaryAWD: this.targetRuleVO.categoryVOList.map(item => { return item.primaryAWD }).reduce((a, b) => a + b, 0),
        countPrimaryOther: this.targetRuleVO.categoryVOList.map(item => { return item.primaryOther }).reduce((a, b) => a + b, 0),
        countPrimaryBug: this.targetRuleVO.categoryVOList.map(item => { return item.primaryBug }).reduce((a, b) => a + b, 0),
        countMiddleCTF: this.targetRuleVO.categoryVOList.map(item => { return item.middleCTF }).reduce((a, b) => a + b, 0),
        countMiddleAWD: this.targetRuleVO.categoryVOList.map(item => { return item.middleAWD }).reduce((a, b) => a + b, 0),
        countMiddleOther: this.targetRuleVO.categoryVOList.map(item => { return item.middleOther }).reduce((a, b) => a + b, 0),
        countMiddleBug: this.targetRuleVO.categoryVOList.map(item => { return item.middleBug }).reduce((a, b) => a + b, 0),
        countSeniorCTF: this.targetRuleVO.categoryVOList.map(item => { return item.seniorCTF }).reduce((a, b) => a + b, 0),
        countSeniorAWD: this.targetRuleVO.categoryVOList.map(item => { return item.seniorAWD }).reduce((a, b) => a + b, 0),
        countSeniorOther: this.targetRuleVO.categoryVOList.map(item => { return item.seniorOther }).reduce((a, b) => a + b, 0),
        countSeniorBug: this.targetRuleVO.categoryVOList.map(item => { return item.seniorBug }).reduce((a, b) => a + b, 0)
      }
      return arr
    },
    // 仿真题数量统计
    simulationScore() {
      const arr = {
        countPrimarySingle: this.simulationRuleVO.categoryVOList.map(item => { return item.primarySingle }).reduce((a, b) => a + b, 0),
        countMiddleSingle: this.simulationRuleVO.categoryVOList.map(item => { return item.middleSingle }).reduce((a, b) => a + b, 0),
        countSeniorSingle: this.simulationRuleVO.categoryVOList.map(item => { return item.seniorSingle }).reduce((a, b) => a + b, 0),
        countPrimaryMultiple: this.simulationRuleVO.categoryVOList.map(item => { return item.primaryMultiple }).reduce((a, b) => a + b, 0),
        countMiddleMultiple: this.simulationRuleVO.categoryVOList.map(item => { return item.middleMultiple }).reduce((a, b) => a + b, 0),
        countSeniorMultiple: this.simulationRuleVO.categoryVOList.map(item => { return item.seniorMultiple }).reduce((a, b) => a + b, 0),
        countPrimaryJudge: this.simulationRuleVO.categoryVOList.map(item => { return item.primaryJudge }).reduce((a, b) => a + b, 0),
        countMiddleJudge: this.simulationRuleVO.categoryVOList.map(item => { return item.middleJudge }).reduce((a, b) => a + b, 0),
        countSeniorJudge: this.simulationRuleVO.categoryVOList.map(item => { return item.seniorJudge }).reduce((a, b) => a + b, 0),
        countPrimaryCTF: this.simulationRuleVO.categoryVOList.map(item => { return item.primaryCTF }).reduce((a, b) => a + b, 0),
        countMiddleCTF: this.simulationRuleVO.categoryVOList.map(item => { return item.middleCTF }).reduce((a, b) => a + b, 0),
        countSeniorCTF: this.simulationRuleVO.categoryVOList.map(item => { return item.seniorCTF }).reduce((a, b) => a + b, 0),
        countPrimaryCompletion: this.simulationRuleVO.categoryVOList.map(item => { return item.primaryCompletion }).reduce((a, b) => a + b, 0),
        countMiddleCompletion: this.simulationRuleVO.categoryVOList.map(item => { return item.middleCompletion }).reduce((a, b) => a + b, 0),
        countSeniorCompletion: this.simulationRuleVO.categoryVOList.map(item => { return item.seniorCompletion }).reduce((a, b) => a + b, 0),
        countPrimaryShort: this.simulationRuleVO.categoryVOList.map(item => { return item.primaryShort }).reduce((a, b) => a + b, 0),
        countMiddleShort: this.simulationRuleVO.categoryVOList.map(item => { return item.middleShort }).reduce((a, b) => a + b, 0),
        countSeniorShort: this.simulationRuleVO.categoryVOList.map(item => { return item.seniorShort }).reduce((a, b) => a + b, 0),
        countPrimaryCombinatorial: this.simulationRuleVO.categoryVOList.map(item => { return item.primaryCombinatorial }).reduce((a, b) => a + b, 0),
        countMiddleCombinatorial: this.simulationRuleVO.categoryVOList.map(item => { return item.middleCombinatorial }).reduce((a, b) => a + b, 0),
        countSeniorCombinatorial: this.simulationRuleVO.categoryVOList.map(item => { return item.seniorCombinatorial }).reduce((a, b) => a + b, 0)
      }
      return arr
    }
  },
  created() {
    this.theoryRuleVO = JSON.parse(this.data.dynamicPaperRules).theoryRuleVO
    this.targetRuleVO = JSON.parse(this.data.dynamicPaperRules).targetRuleVO
    this.simulationRuleVO = JSON.parse(this.data.dynamicPaperRules).simulationRuleVO
  },
  methods: {
  }
}
</script>

<style lang="scss" scoped>
._paper_container {
  display: flex;
  flex-direction: column;
  min-height: 100%;
  ._paper_header {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    position: relative;
    ._paper_search {
      position: absolute;
      display: flex;
      top: 6px;
      align-items: center;
      ._paper_search_1 {
        font-size: 16px;
        font-weight: 500;
        color: #333;
      }
    }
  }
  .border-b {
    border-bottom: 1px solid #d7d7d7;
  }
  .trends-table{
    width: 50%;
  }
  .trends-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 5px 0;
    // .category {
    //   display: flex;
    //   align-items: center;
    //   ::v-deep {
    //     .el-input-number {
    //       width: 60%;
    //       .el-input__inner {
    //         padding: 0;
    //       }
    //     }
    //     .el-input-number__decrease {
    //       display: none;
    //     }
    //     .el-input-number__increase {
    //       display: none;
    //     }
    //   }
    //   .el-form-item__content {
    //     display: flex;
    //   }
    // }
    > div {
      width: 10%;
      display: flex;
      justify-content: center;
    }
  }
   /deep/ .el-tabs {
    display: flex;
    flex-direction: column;
    height: 100%;
    .el-tabs__header {
      .el-tabs__item {
        margin-right: 0;
        border: none !important;
      }
    }
    .el-tabs__content {
      overflow-y: auto;
    }
    ._question_list {
      ._question_item {
        padding: 15px 20px;
        min-height: 90px;
        background: #ffffff;
        display: flex;
        flex-direction: column;
        font-size: 14px;
        color: #4e5969;
        position: relative;
        border: 1px solid #e5e6eb;
        border-radius: 4px;
        margin-bottom: 10px;
        ._question_option {
          margin-top: 10px;
          margin-left: 15px;
          font-size: 14px;
          color: #4e5969;
          display: flex;
          flex-direction: column;
          line-height: 22px;
          word-break: break-all;
          .el-radio {
            margin-bottom: 8px;
            display: flex;
            align-items: flex-start;
            .el-radio__label {
              font-size: 14px;
              color: #4e5969;
              white-space: normal;
              word-break: break-all;
            }
          }
          .el-checkbox {
            margin-bottom: 8px;
            display: flex;
            align-items: flex-start;
            .el-checkbox__label {
              font-size: 14px;
              color: #4e5969;
              white-space: normal;
              word-break: break-all;
            }
          }
        }
        ._question_score {
          border-top: 1px solid #e5e6eb;
          padding: 10px 0 0 0 ;
          display: flex;
          align-items: center;
          justify-content: space-between;
          ._question_delete {
            color: #F56C6C;
            cursor: pointer;
          }
        }
        ._question_item_content {
          display: flex;
          // max-height: 200px;
          // overflow-y: auto;
          overflow-x: auto;
          margin-right: 55px;
        }
        ._question_item_type {
          position: absolute;
          right: 15px;
          top: 15px;
        }
        .combination-question-wrap {
          >div {
            border: 1px solid rgb(229, 230, 235);
            margin: 5px 0px 10px;
            padding: 15px 20px 5px;
            .comp-question {
              display: flex;
              // max-height: 200px;
              // overflow-y: auto;
              overflow-x: auto;
              >span {
                flex: 1;
                word-break: break-all;
              }
            }
            .comp-content-wrap {
              border: 1px solid #e5e6eb;
              margin: 5px 0 10px;
              padding: 15px 20px;
              >div:first-child {
                display: flex;
                // max-height: 200px;
                // overflow-y: auto;
                overflow-x: auto;
                margin-bottom: 10px;
                >span {
                  flex: 1;
                  word-break: break-all;
                }
              }
            }
          }
        }
      }
      ._question_item_check {
        border: 1px solid var(--color-600);
      }
    }
  }
}
.add-classification {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px 0;
  border-color: #abdcff;
  background-color: #f0faff;
  color: #2d8cf0;
  cursor: pointer;
}
.card-bg {
  border-radius: 4px;
  padding: 15px;
  background-color: #FFFFFF;
}
</style>
