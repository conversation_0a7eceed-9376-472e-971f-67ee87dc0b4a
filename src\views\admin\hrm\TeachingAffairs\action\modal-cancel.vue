<template>
  <div v-loading="loading" class="dialog-wrap">
    <el-alert :closable="false" type="warning">
      <div slot="title">
        1.进行中的教学事务，取消后可能会影响正常上课；<br>
        2.已结束的教学事务，取消后会删除学员答题记录、任务成果以及成绩！
      </div>
    </el-alert>
    <batch-template
      :data="data"
      :available-data="availableData"
      post-key="schedulingCode"
      view-key="content"
    />
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button :disabled="!availableData.length" type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>

<script>
import batchTemplate from '@/packages/batch-delete/modal-bat-template.vue'
import modalMixins from '@/packages/mixins/modal_form'
import { delTeachingCodeList } from '@/api/teachingAffairs/index.js'

export default {
  components: {
    batchTemplate
  },
  mixins: [modalMixins],
  props: {
    data: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      loading: false
    }
  },
  computed: {
    availableData: function() {
      const tempArr = []
      this.data.forEach((item) => {
        tempArr.push(item)
      })
      return tempArr
    }
  },
  methods: {
    close: function() {
      this.$emit('close')
    },
    confirm: function() {
      this.loading = true
      const params = this.availableData.reduce((acc, cur) => {
        const data = cur.schedulingCodes || []
        acc.push(...data)
        return acc
      }, [])
      delTeachingCodeList(params).then(res => {
        if (res.code == 0) {
          this.loading = false
          this.$message.success('取消排课成功')
          this.$emit('call', 'refresh')
          this.close()
        }
      }).finally(() => {
        this.loading = false
      })
    }
  }
}
</script>
