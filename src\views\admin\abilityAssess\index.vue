<template>
  <div class="content-wrap-layout">
    <!-- 分类区 -->
    <page-table
      ref="table"
      :default-selected-arr="defaultSelectedArr"
      :cache-pattern="true"
      default-selected-key="userId"
      @refresh="refresh"
      @link-event="linkEvent"
      @on-select="tabelSelect"
      @on-current="tabelCurrent"
    >
      <action-menu
        slot="action"
        :module-name="moduleName"
        :select-item="selectItem"
        @call="actionHandler"
      />
    </page-table>
    <el-drawer
      :visible.sync="detailShow"
      :modal="false"
      size="75%"
      append-to-body
      class="detail-view"
      @close="closeDetail"
    >
      <t-detail v-if="detailShow" />
    </el-drawer>
  </div>
</template>

<script>
import moduleConf from './config'
import pageTable from './table/index.vue'
import actionMenu from './action/index.vue'
import tDetail from './detail/index.vue'
import moduleMixin from '@/packages/mixins/module_list'
export default {
  // 能力评估
  name: 'AbilityAssess',
  components: {
    pageTable,
    actionMenu,
    tDetail
  },
  mixins: [moduleMixin],
  data() {
    return {
      listRouterName: 'assess',
      moduleName: moduleConf.name,
      selectItem: [],
      defaultSelectedArr: [],
      categoryId: ''
    }
  },
  methods: {
    switchCategory: function(value) {
      this.categoryId = value
      this.$nextTick(() => {
        this.$refs['table'].getList()
      })
    },
    // 列表点击
    linkEvent: function({ name, row, params }) {
      this.$router.push({ name: name, params: params })
    },
    // 返回已选
    tabelSelect: function(data) {
      this.selectItem = data
    },
    // 返回单选
    tabelCurrent: function(row) {
      this.selectItem = [row]
    },
    // action menu 事件
    actionHandler: function(type, data) {
      switch (type) {
        case 'refresh':
          this.$refs['table'].getList()
      }
    },
    refresh: function() {}
  }
}
</script>
