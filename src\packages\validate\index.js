export default {
  // 必填
  'required': (trigger) => {
    return { required: true, message: '必填项', trigger: trigger }
  },
  // 典型通用名称
  'base_name': {
    type: 'string',
    pattern: /^[a-zA-Z0-9\u4E00-\u9FA5][a-zA-Z0-9\_\-\.\u4E00-\u9FA5]{0,63}$/,
    message: '1-64个字符，支持输入字母、中文字符、数字、以及特殊字符连字符“-”、下划线“_”、点“.”，不支持以特殊字符开头',
    trigger: 'blur'
  },
  // 描述
  'description': { type: 'string', max: 255, message: '不可超过255个字符', trigger: 'blur' },
  // 1-64个字符
  'name_64_char': {
    min: 1,
    max: 64,
    message: '1-64个字符',
    trigger: 'blur'
  },
  // 正整数校验
  'number_integer': {
    pattern: /^[0-9]*[1-9][0-9]*$/,
    message: '必须为正整数'
  },
  // 整数校验 包括0和正负数整数
  'all_number_integer': {
    pattern: /^(0|[1-9][0-9]*|-[1-9][0-9]*)$/,
    message: '必须为整数'
  },
  // ssh用户名、厂商、型号
  'ssh_name': {
    type: 'string',
    pattern: /^[a-z0-9][a-z0-9\_]{0,31}$/,
    message: '1-32个字符，支持输入小写字母、数字和下划线，不支持以特殊字符开头',
    trigger: 'blur'
  },
  // 手机号码
  'mobilePhone': {
    pattern: /(^1\d{10}$)|(^09\d{8}$)/,
    message: '请输入正确的手机号',
    trigger: 'blur'
  },
  // 邮箱
  'email': {
    pattern: /^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*\.[a-zA-Z0-9]{2,6}$/,
    message: '请输入正确的邮箱',
    trigger: 'blur'
  },
  // 系统密码要求 低
  'lowPassword': {
    min: 1,
    max: 16,
    message: '密码长度为1-16位',
    trigger: 'blur'
  },
  // 系统密码要求 中
  'mediumPassword': {
    pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[^]{8,16}$/,
    message: '必须同时包含大写字母、小写字母及数字且长度为8-16位',
    trigger: 'blur'
  },
  // 系统密码要求 高
  'highPassword': {
    pattern: /^(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])(?=.*[\W_]).{8,16}$/,
    message: '必须同时包含大写字母、小写字母、数字及特殊字符且长度为8-16位',
    trigger: 'blur'
  },
  // IPv4校验
  'filterIPAddress': {
    pattern: /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/,
    trigger: 'blur',
    message: '请输入正确的IPv4地址'
  },
  // 云主机登录密码
  'instancePassword': {
    pattern: /^(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])(?=.*[\W_]).{8,30}$/,
    message: '必须同时包含大写字母、小写字母、数字及特殊字符且长度为8-30位',
    trigger: 'blur'
  },
  // 创建网络CIDR
  'filterIpCIDR': {
    pattern: /^((25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])\.){3}(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])\/([1-9]|[1-2][0-9]|3[0-2])$/,
    message: '请输入正确的网络地址',
    trigger: 'blur'
  },
  'filter_mac': {
    pattern: /^([0-9a-fA-F]{2})(:[0-9a-fA-F]{2}){5}$/,
    message: '请输入正确的MAC地址',
    trigger: 'blur'
  },
  // IPv6前缀
  'filter_ipv6_prefix': {
    pattern: /^((\s*((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))(%.+)?\s*)(\/(([0-9])|([1-9][0-9])|(1[0-1][0-9]|12[0-8]))){0,1},?)*$/,
    message: '请输入正确的前缀',
    trigger: 'blur'
  },
  // IPv6
  'filter_ipv6': {
    pattern: /^((\s*((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(\.(25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))(%.+)?\s*)(\/(([0-9])|([1-9][0-9])|(1[0-1][0-9]|12[0-8]))){0,1})*$/,
    message: '请输入正确的IPv6地址',
    trigger: 'blur'
  },
  // 身份证
  'idcard': {
    // 目前仅支持的 1900.01.01-2099.12.31 年出生的人，后续如有需要可再次增加
    pattern: /^[1-9]\d{5}(19|20)\d{2}((((0[13578])|(1[02]))(0[1-9]|[12]\d|3[01]))|(((0[469])|11)(0[1-9]|[12]\d|30))|(((02)(0[1-9]|[12]\d))))\d{3}[0-9X]$/,
    message: '请输入正确的身份证号',
    trigger: 'blur'
  }
}
