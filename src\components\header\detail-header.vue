<template>
  <div class="content-header">
    <div class="detail-header-icon">
      <i :class="$route.meta.icon" />
    </div>
    <div>
      <div :title="config.name" class="header-list-title">{{ config.name }}</div>
      <div class="header-list-content">
        <span :title="config.description" class="header-list-description">{{ config.description || '暂无描述' }}</span>
        <!-- <span class="header-list-more">了解更多</span> -->
      </div>
    </div>
    <!-- TODO -->
    <div class="header-list-extra" />
  </div>
</template>

<script>
export default {
  props: {
    config: {
      type: Object,
      default: () => {
        return {}
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.content-header {
  display: flex;
  justify-content: flex-start;
  padding: 5px 24px 20px 24px;
  .detail-header-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    margin-right: 12px;
    background-color: var(--color-600);
    border-radius: 50%;
    flex-shrink: 0;
    i {
      color: var(--neutral-0);
      font-size: 20px;
    }
  }
  .header-list-title {
    color: var(--neutral-800);
    font-weight: 500;
    font-size: 24px;
    line-height: 32px;
  }
  .header-list-content {
    margin-top: 4px;
    color: var(--neutral-600);
    font-size: 12px;
    line-height: 20px;
    .header-list-more {
      color: var(--color-600);
      cursor: pointer;
    }
  }
}
</style>
