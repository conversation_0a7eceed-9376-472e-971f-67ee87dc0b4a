<template>
  <div v-if="total" class="course-container">
    <div class="grid-course">
      <div v-for="item in courseList" :key="item.id" class="grid-content bg-purple">
        <div class="course-list">
          <div class="course-details course-details_select" @click="goDetails(item)">
            <div class="course-details_bk">
              <img :src="item.courseCoverUrl || defaultCover" alt="" >
            </div>
            <div class="course-details_center">
              <div class="course-name-rate">
                <div v-overflow-tooltip class="course-name ellipsis"> {{ item.name }} </div>
                <div class="course-rate">
                  <div>评分:</div>
                  <el-rate v-if="item.rate" v-model="item.rate" disabled/>
                  <span v-else>暂无</span>
                </div>
              </div>
              <div class="course-create_name">
                <div v-overflow-tooltip class="course-user ellipsis">{{ item.userName || '暂无' }}</div>
                <div class="division" />
                <div v-overflow-tooltip class="course-period">
                  <span style="color: var(--color-600)">{{ item.coursePeriod || 0 }}</span>&nbsp;课时
                </div>
                <div class="division" />
                <div class="_scheme_div_operation">
                  <div class="opt-icon">
                    <i style="color: #86909c" class="el-icon-circle-plus-outline" @click.stop="goEditDetails(item)" />
                  </div>
                  <div class="division" />
                  <div class="opt-icon">
                    <i style="color: #86909c" class="el-icon-edit" @click.stop="editOne(item)"/>
                  </div>
                  <div class="division" />
                  <div class="opt-icon">
                    <i style="color: #86909c" class="el-icon-delete" @click.stop="deleteOne(item)"/>
                  </div>
                  <div class="division" />
                  <div class="opt-icon">
                    <i :class="{'el-icon-lock':item.coursePrivacy == 0,'el-icon-unlock':item.coursePrivacy == 1}" style="color: #86909c" @click.stop="handlePublic(item,'privacy')"/>
                  </div>
                </div>
              </div>
              <div class="tab-div">
                <div class="tab-div-left">
                  <div v-for="(type, index) in contentTypeList(item)" :key="index">
                    <el-tag v-if="contentTypeObj[type]" class="sanpack" size="small">{{ contentTypeObj[type] }}</el-tag>
                  </div>
                </div>
                <div class="tab-div-right">
                  <el-button v-if="item.coursePublish == 1" type="primary" @click.stop="handleCancel(item, '取消发布')">取消发布</el-button>
                  <el-button v-else type="primary" @click.stop="handlelimit(item, '发布')">发布</el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="perch" />
      <div class="perch" />
      <div class="perch" />
    </div>
    <publish-course
      :publish-name="publishName"
      :publish-control="publishControl"
      :publish-item="publishItem"
      @close="closePublish"
    />
    <!--页码 开始-->
    <div class="data-table-footer">
      <el-pagination
        :current-page.sync="current"
        :page-sizes="[10, 20, 30, 40, 50, 100, 200]"
        :pager-count="5"
        :page-size="pageSize"
        :total="Number(total)"
        background
        class="page-wrap"
        layout="sizes, prev, pager, next"
        @size-change="onPageSizeChange"
        @current-change="changePage"
      />
      <div class="data-table-total">
        共 {{ total }} 条
      </div>
    </div>
    <!--页码 结束-->
  </div>
  <div v-else>
    <el-empty
      :image="img"
      :image-size="178"
      style="margin: 100px auto"
      description="暂无数据"
    />
  </div>
</template>
<script>
import module from '../config.js'
import paging from '@/components/paging.vue'
import publishCourse from '../action/modal-publish.vue'
import { queryBackCourse } from '@/api/teacher/index.js'
import { contentTypeObj } from '@/views/admin/hrm/personnelManagement/student/constants.js'
export default {
  components: {
    paging,
    publishCourse
  },
  props: {
    isrefresh: {
      type: Boolean
    },
    storage: {
      type: Number
    },
    isAll: {
      type: Boolean
    },
    courseCategoryId: String,
    difficulty: [Number, String],
    courseName: Object,
    rankingRating: [Boolean, String]
  },
  data() {
    return {
      moduleName: module.name,
      contentTypeObj,
      defaultCover: require('@/assets/default-cover2.png'),
      img: require('@/packages/table-view/nodata.png'),
      courseList: [],
      current: 1,
      pageSize: 10,
      total: 0,
      publishName: '',
      publishControl: false,
      publishItem: {}
    }
  },
  watch: {
    isrefresh() {
      this.changePage(1)
    },
    storage() {
      this.getList()
    },
    isAll() {
      this.changePage(1)
    }
  },
  mounted() {
    if (this.$store.state.cache[this.moduleName + 'pageSize']) {
      this.pageSize = this.$store.state.cache[this.moduleName + 'pageSize'] || 10
    }
    if (this.$store.state.cache[this.moduleName + 'current']) {
      this.current = this.$store.state.cache[this.moduleName + 'current'] || 1
    }
  },
  methods: {
    contentTypeList(item) {
      let typeArr = []
      if (item.contentType == '0') {
        typeArr = ['1', '2']
      } else {
        typeArr = [item.contentType]
      }
      return typeArr
    },
    getList: function(showLoading = true) {
      if (showLoading) {
        this.tableLoading = true
      }
      const data = {
        limit: this.pageSize,
        page: this.current,
        courseCategoryId: this.courseCategoryId,
        difficulty: this.difficulty,
        name: this.courseName.name,
        userName: this.courseName.userName,
        createdBy: this.isAll ? JSON.parse(localStorage.getItem('loginUserInfo')).userId : '',
        flag: this.rankingRating
      }
      queryBackCourse(data).then(res => {
        this.courseList = res.data.records
        this.courseList.map((item) => {
          if (item.rate && item.num) {
            item.rate = Math.round(item.rate / item.num)
          }
        })
        this.total = res.data.total
      })
    },
    goEditDetails(item) {
      this.$router.push({
        name: 'courseDetailLibraryEdit',
        query: {
          courseId: item.id,
          courseName: item.name,
          des: item.courseDescription,
          isShowHandleButton: true,
          enterType: 'course'
        }
      })
    },
    goDetails(item) {
      const route = this.$router.resolve({
        name: 'courseDetailLibrary',
        query: {
          courseId: item.id,
          courseName: item.name,
          des: item.courseDescription,
          isShowHandleButton: true,
          enterType: 'course'
        }
      })
      window.open(route.href, '_blank')
    },
    editOne(item) {
      this.$bus.$emit('courseCardEdit', item)
    },
    deleteOne(item) {
      this.$bus.$emit('courseCardDelete', item)
    },
    handlePublic(item) {
      this.$bus.$emit('coursePrivacyEdit', item)
    },
    // 更改pageSize
    'onPageSizeChange': function(pageSize) {
      this.current = 1
      this.pageSize = pageSize
      const obj = {
        data: pageSize,
        key: this.moduleName + 'pageSize'
      }
      this.$store.commit('SET_CACHE', obj)
      this.getList()
    },
    // 翻页
    'changePage': function(num) {
      this.current = num
      const obj = {
        data: num,
        key: this.moduleName + 'current'
      }
      this.$store.commit('SET_CACHE', obj)
      this.getList()
    },
    handlelimit(item, type) {
      this.publishItem = item
      this.publishName = type
      this.publishControl = true
    },
    handleCancel(item, type) {
      this.publishItem = item
      this.publishName = type
      this.publishControl = true
    },
    closePublish(val) {
      this.publishControl = false
      if (val == 'refresh') {
        this.getList()
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.data-table-footer {
  position: relative;
  margin-top: 10px;
  background-color: #fff;
  height: 32px;
  .page-wrap {
    position: absolute;
    right: 5px;
  }
  .data-table-total {
    position: absolute;
    left: 15px;
    line-height: 32px;
  }
}
.course-container {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
  .grid-course {
    display: flex;
    justify-content: space-between;
    align-content: flex-start;
    flex-wrap: wrap;
    flex: 1;
    overflow-y: auto;
    column-gap: 10px;
    .grid-content {
      width: 280px;
      height: fit-content;
      margin-bottom: 15px;
      border-radius: 4px;
    }
    .perch {
      width: 280px;
      height: 0;
      padding: 0;
      margin: 0;
      background: transparent;
      border: none;
    }
  }
}
.course-list {
  width: 100%;
  &:hover {
    box-shadow: 0px 2px 5px 0px rgba(0,0,0,0.1);
  }
  .course-details {
    border: 1px solid #c9cdd4;
    background: #ffffff;
    border-radius: 4px;
    overflow: hidden;
    cursor: pointer;
    max-height: 400px;
    &:hover {
      border: 1px solid var(--color-600);
    }
    .course-details_bk {
      img {
        border-bottom: 1px solid #e5e6eb;
        width: 100%;
        height: 170px;
      }
    }
    .course-details_center {
      width: 100%;
      background: #fff;
      padding: 0 10px 10px 10px;
      .course-name-rate {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .course-rate {
          font-size: 13px;
          max-width: 155px;
          display: flex;
          color: #828384;
          ::v-deep .el-rate__icon {
            margin-right: 0;
          }
        }
      }
      .course-name {
        flex: 1;
        font-size: 14px;
        font-weight: 500;
        color: #1d2129;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .course-create_name {
        font-size: 13px;
        font-weight: 400;
        color: #828384;
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 4px;
        .course-period {
          min-width: 50px;
        }
        .division {
          width: 1px;
          height: 12px;
          background: #f2f3f5;
          border-radius: 1px;
          margin: 0 5px;
        }
        ._scheme_div_operation {
          margin-left: 5px;
          display: flex;
          align-items: center;
          .opt-icon {
            width: 18px;
            height: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 4px;
          }
          .opt-icon:hover {
            background: #f7f8fa;
          }
        }
      }
      .tab-div {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 5px;
        .tab-div-left {
          display: flex;
          font-size: 12px;
          font-weight: 400;
          border-radius: 4px;
          .sanpack {
            margin: 0 10px 0 0;
            border-radius: 4px;
          }
        }
        .tab-div-right {
          button  {
            height: 30px;
          }
        }
      }
    }
  }
}
</style>
