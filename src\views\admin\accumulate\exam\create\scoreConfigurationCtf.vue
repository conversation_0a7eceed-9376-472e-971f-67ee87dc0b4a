<template>
  <div class="card-bg optional-ctf">
    <div class="classification-list">
      <div class="classification-list-title">
        <div>
          题目分类：
        </div>
      </div>
      <div>
        <div v-for="(item, index) in examClassifys" :key="index" :class="examClassValue == item ? 'is-active classification-content' : 'classification-content'" @click="switchExamClass(item)">
          <div v-overflow-tooltip class="classification-content-overflow">
            {{ item }}
          </div>
        </div>
      </div>
    </div>
    <div class="_paper_container">
      <div class="_paper_header">
        <el-button type="primary" @click="drawerShow = true, drawerName = 'setScore'">批量设置分数</el-button>
        <div class="_paper_search">
          <div class="_paper_search_1">
            试卷总题数
            <span style="font-weight: bold; color: var(--color-600); font-size: 18px; margin-right: 10px;">
              {{ checkQuestionList.length }}
            </span>
          </div>
          <div class="_paper_search_1">
            总分数
            <span style="font-weight: bold; color: var(--color-600); font-size: 18px;">
              {{ getScore }}
            </span>
          </div>
        </div>
      </div>
      <el-tabs v-model="activeName">
        <el-tab-pane v-if="theoryQuestionList.length" :label="`理论（${ theoryQuestionList.length }）`" name="theory">
          <!-- 题目列表 -->
          <div v-if="theoryQuestionList.length" class="_question_list">
            <div
              v-for="(q, index) in theoryQuestionList"
              :class="`_question_item`"
              :key="q.id"
            >
              <div>
                <div class="_question_item_content">{{ index + 1 }}.&nbsp;<span v-html="q.content"/></div>
                <div class="_question_item_type">
                  <el-tag size="small">{{ questionConf.questionTypObj[q.questionType].label }}</el-tag>
                </div>
              </div>
              <div v-if="q.questionType == 1" class="_question_option">
                <el-radio-group
                  v-for="(op, i) in JSON.parse(q.questionOptions)"
                  :key="op"
                  :value="q.questionAnswer"
                >
                  <el-radio :label="optionLabel[i]" disabled>{{ op }}</el-radio>
                </el-radio-group>
              </div>
              <div v-else-if="q.questionType == 2" class="_question_option">
                <el-checkbox-group :value="q.questionAnswer.split('')">
                  <el-checkbox
                    v-for="(op, i) in JSON.parse(q.questionOptions)"
                    :key="op"
                    :label="optionLabel[i]" disabled
                  >{{ op }}</el-checkbox
                  >
                </el-checkbox-group>
              </div>
              <div v-else-if="q.questionType == 3" class="_question_option">
                <el-radio-group
                  :value="q.questionAnswer"
                >
                  <el-radio label="A" disabled>正确</el-radio>
                  <el-radio label="B" disabled>错误</el-radio>
                </el-radio-group>
              </div>
              <div v-else class="_question_option">
                <span style="padding-bottom: 10px">{{
                  q.questionAnswer
                }}</span>
              </div>
              <div class="_question_score">
                <div class="flex-left">
                  <div>该题：<el-input-number v-model="q.questionScore" :step="1" :min="0" :max="9999" :controls="false" step-strictly size="mini"/> 分</div>
                  <div class="ml-20">
                    <span>备用：</span>
                    <el-radio
                      v-for="item in isSpareArr"
                      :key="item.value"
                      v-model="q.isSpare"
                      :label="item.value"
                      style="margin: 0 20px 0 0 !important;"
                    >{{ item.label }}</el-radio>
                  </div>
                  <div v-if="q.questionType == 4 || q.questionType == 5" class="ml-20">
                    <span>题目作答方式：</span>
                    <el-radio
                      v-for="item in answeringMethod"
                      :key="item.value"
                      v-model="q.questionAnswerMethod"
                      :label="item.value"
                      style="margin: 0 20px 0 0 !important;"
                    >{{ item.label }}</el-radio>
                  </div>
                </div>
                <div class="_question_delete" @click="deleteQuestion(theoryQuestionList, q, index)">
                  <i class="el-icon-delete"/>
                </div>
              </div>
            </div>
          </div>
          <el-empty
            v-else
            :image="img"
            :image-size="110"
            style="margin: 100px auto"
            description="暂无数据"
          />
        </el-tab-pane>
        <el-tab-pane v-if="targetQuestionList.length" :label="`靶机（${ targetQuestionList.length }）`" name="target">
          <!-- 题目列表 -->
          <div v-if="targetQuestionList.length" class="_question_list">
            <div
              v-for="(q, index) in targetQuestionList"
              :class="`_question_item`"
              :key="q.id"
            >
              <div>
                <div class="_question_item_content">{{ index + 1 }}.&nbsp;<span v-html="q.content"/></div>
                <div class="_question_item_type">
                  <el-tag size="small">{{ questionConf.questionTypObj[q.questionType].label }}</el-tag>
                </div>
              </div>
              <div v-if="q.questionType == 1" class="_question_option">
                <el-radio-group
                  v-for="(op, i) in JSON.parse(q.questionOptions)"
                  :key="op"
                  :value="q.questionAnswer"
                >
                  <el-radio :label="optionLabel[i]" disabled>{{ op }}</el-radio>
                </el-radio-group>
              </div>
              <div v-else-if="q.questionType == 2" class="_question_option">
                <el-checkbox-group :value="q.questionAnswer.split('')">
                  <el-checkbox
                    v-for="(op, i) in JSON.parse(q.questionOptions)"
                    :key="op"
                    :label="optionLabel[i]" disabled
                  >{{ op }}</el-checkbox
                  >
                </el-checkbox-group>
              </div>
              <div v-else-if="q.questionType == 3" class="_question_option">
                <el-radio-group
                  :value="q.questionAnswer"
                >
                  <el-radio label="A" disabled>正确</el-radio>
                  <el-radio label="B" disabled>错误</el-radio>
                </el-radio-group>
              </div>
              <div v-else class="_question_option">
                <span style="padding-bottom: 10px">{{
                  q.questionAnswer
                }}</span>
              </div>
              <div class="_question_score">
                <div class="flex-left">
                  <div>该题：<el-input-number v-model="q.questionScore" :step="1" :min="0" :max="9999" :controls="false" step-strictly size="mini"/> 分</div>
                  <div class="ml-20">
                    <span>备用：</span>
                    <el-radio
                      v-for="item in isSpareArr"
                      :key="item.value"
                      v-model="q.isSpare"
                      :label="item.value"
                      style="margin: 0 20px 0 0 !important;"
                    >{{ item.label }}</el-radio>
                  </div>
                  <div v-if="q.questionType == 4 || q.questionType == 5" class="ml-20">
                    <span>题目作答方式：</span>
                    <el-radio
                      v-for="item in answeringMethod"
                      :key="item.value"
                      v-model="q.questionAnswerMethod"
                      :label="item.value"
                      style="margin: 0 20px 0 0 !important;"
                    >{{ item.label }}</el-radio>
                  </div>
                </div>
                <div class="_question_delete" @click="deleteQuestion(targetQuestionList, q, index)">
                  <i class="el-icon-delete"/>
                </div>
              </div>
            </div>
          </div>
          <el-empty
            v-else
            :image="img"
            :image-size="110"
            style="margin: 100px auto"
            description="暂无数据"
          />
        </el-tab-pane>
        <el-tab-pane v-if="emulationQuestionList.length" :label="`仿真（${ emulationQuestionList.length }）`" name="emulation">
          <!-- 题目列表 -->
          <div v-if="emulationQuestionList.length" class="_question_list">
            <div
              v-for="(q, index) in emulationQuestionList"
              :class="`_question_item`"
              :key="q.id"
            >
              <div>
                <div class="_question_item_content">{{ index + 1 }}.&nbsp;<span v-html="q.questionType != 10 ? q.content : q.questionName"/></div>
                <div class="_question_item_type">
                  <el-tag size="small">{{ questionConf.questionTypObj[q.questionType].label }}</el-tag>
                </div>
              </div>
              <div v-if="q.questionType == 1" class="_question_option">
                <el-radio-group
                  v-for="(op, i) in JSON.parse(q.questionOptions)"
                  :key="op"
                  :value="q.questionAnswer"
                >
                  <el-radio :label="optionLabel[i]" disabled>{{ op }}</el-radio>
                </el-radio-group>
              </div>
              <div v-else-if="q.questionType == 2" class="_question_option">
                <el-checkbox-group :value="q.questionAnswer.split('')">
                  <el-checkbox
                    v-for="(op, i) in JSON.parse(q.questionOptions)"
                    :key="op"
                    :label="optionLabel[i]" disabled
                  >{{ op }}</el-checkbox
                  >
                </el-checkbox-group>
              </div>
              <div v-else-if="q.questionType == 3" class="_question_option">
                <el-radio-group
                  :value="q.questionAnswer"
                >
                  <el-radio label="A" disabled>正确</el-radio>
                  <el-radio label="B" disabled>错误</el-radio>
                </el-radio-group>
              </div>
              <div v-else-if="q.questionType == 10" class="combination-question-wrap">
                <div v-for="(item, index) in q.combinationQuestionBOS" :key="index">
                  <div class="comp-question">
                    综合题{{ index + 1 }}.&nbsp;<span v-html="item.questionName"/>
                  </div>
                  <div v-for="(sub, subIndex) in item.content" :key="subIndex" class="comp-content-wrap">
                    <div>题目{{ subIndex + 1 }}.&nbsp;<span v-html="sub.contentName"/></div>
                    <div class="_question_score">
                      <div>该题：<el-input-number v-model="sub.questionScore" :min="0" :controls="false" size="mini"/> 分</div>
                    </div>
                  </div>
                </div>
              </div>
              <div v-else class="_question_option">
                <span style="padding-bottom: 10px">{{ q.questionAnswer }}</span>
              </div>
              <div v-if="q.questionType != 10" class="_question_score">
                <div class="flex-left">
                  <div>该题：<el-input-number v-model="q.questionScore" :step="1" :min="0" :max="9999" :controls="false" step-strictly size="mini"/> 分</div>
                  <div class="ml-20">
                    <span>备用：</span>
                    <el-radio
                      v-for="item in isSpareArr"
                      :key="item.value"
                      v-model="q.isSpare"
                      :label="item.value"
                      style="margin: 0 20px 0 0 !important;"
                    >{{ item.label }}</el-radio>
                  </div>
                  <div v-if="q.questionType == 4 || q.questionType == 5" class="ml-20">
                    <span>题目作答方式：</span>
                    <el-radio
                      v-for="item in answeringMethod"
                      :key="item.value"
                      v-model="q.questionAnswerMethod"
                      :label="item.value"
                      style="margin: 0 20px 0 0 !important;"
                    >{{ item.label }}</el-radio>
                  </div>
                </div>
                <div class="_question_delete" @click="deleteQuestion(emulationQuestionList, q, index)">
                  <i class="el-icon-delete"/>
                </div>
              </div>
              <div v-else class="combination-question-delete">
                <div @click="deleteQuestion(emulationQuestionList, q, index)">
                  <i class="el-icon-delete"/>
                </div>
              </div>
            </div>
          </div>
          <el-empty
            v-else
            :image="img"
            :image-size="110"
            style="margin: 100px auto"
            description="暂无数据"
          />
        </el-tab-pane>
      </el-tabs>
      <el-drawer
        :title="titleMapping[drawerName]"
        :visible.sync="drawerShow"
        :size="drawerWidth"
        append-to-body
        @close="drawerClose"
      >
        <transition v-if="drawerShow" name="el-fade-in-linear">
          <component
            :is="drawerName"
            :emulation-list="emulationQuestionList"
            :target-list="targetQuestionList"
            :theory-list="theoryQuestionList"
            :get-question-num="checkQuestionList.length"
            :get-score="getScore"
            :name="drawerName"
            @close="drawerClose"
            @call="drawerConfirmCall"
          />
        </transition>
      </el-drawer>
    </div>
  </div>
</template>

<script>
import questionConf from '../../questionBank/config.js'
import setScore from './setScore'

export default {
  components: {
    setScore
  },
  props: {
    selectQuestionList: {
      type: Array
    },
    examClassList: {
      type: Array
    }
  },
  data() {
    return {
      questionConf: questionConf,
      titleMapping: {
        'setScore': '设置分数'
      },
      drawerWidth: '720px',
      examClassValue: '',
      drawerShow: false,
      drawerName: null,
      examClassifys: [],
      activeName: 'theory',
      img: require('@/assets/empty_state.png'),
      optionLabel: Array.from({ length: 26 }, (_, index) => String.fromCharCode('A'.charCodeAt(0) + index)),
      questionType: '',
      theoryQuestionList: [],
      targetQuestionList: [],
      emulationQuestionList: [],
      checkQuestionList: [],
      bankType: '',
      isSpareArr: [ // 备用题map
        { label: '是', value: 1 },
        { label: '否', value: 0 }
      ],
      answeringMethod: [
        { label: '提交flag', value: 1 },
        { label: '上传修补包', value: 2 }
      ]
    }
  },
  computed: {
    getScore() {
      let score = 0
      this.checkQuestionList.forEach(item => {
        // 组合题的分数计算的是各个小题的分数
        if (item.questionType == 10 && item.combinationQuestionBOS) {
          item.combinationQuestionBOS.forEach(comp => {
            comp.content.forEach(con => {
              if (con.questionScore) {
                score += con.questionScore
              }
            })
          })
        } else {
          if (item.questionScore) {
            score = score + item.questionScore
          }
        }
      })
      return score
    }
  },
  created() {
    this.examClassifys = this.examClassList
    this.switchExamClass(this.examClassifys[0])
  },
  methods: {
    selectBankType(item) {
      this.bankType = item.id
      this.getList()
    },
    selectQuestionType(item) {
      this.questionType = item.id
      this.getList()
    },
    drawerClose() {
      this.drawerShow = false
    },
    // 侧拉选择资源回调
    drawerConfirmCall: function(type, data) {
      if (this.theoryQuestionList.length) {
        this.theoryQuestionList.forEach(item => {
          if (item.questionType === 1) {
            if (data.theoryVO.singleTypeNum || data.theoryVO.singleTypeNum == 0) {
              item.questionScore = data.theoryVO.singleTypeNum
            }
          }
          if (item.questionType === 2) {
            if (data.theoryVO.manyTypeNum || data.theoryVO.manyTypeNum == 0) {
              item.questionScore = data.theoryVO.manyTypeNum
            }
          }
          if (item.questionType === 3) {
            if (data.theoryVO.judgeTypeNum || data.theoryVO.judgeTypeNum == 0) {
              item.questionScore = data.theoryVO.judgeTypeNum
            }
          }
          if (item.questionType === 4) {
            if (data.theoryVO.ctfTypeNum || data.theoryVO.ctfTypeNum == 0) {
              item.questionScore = data.theoryVO.ctfTypeNum
            }
          }
          if (item.questionType === 7) {
            if (data.theoryVO.completionNum || data.theoryVO.completionNum == 0) {
              item.questionScore = data.theoryVO.completionNum
            }
          }
          if (item.questionType === 8) {
            if (data.theoryVO.saqTypeNum || data.theoryVO.saqTypeNum == 0) {
              item.questionScore = data.theoryVO.saqTypeNum
            }
          }
        })
      }
      if (this.targetQuestionList.length) {
        this.targetQuestionList.forEach(item => {
          if (item.questionType === 4) {
            if (data.targetVO.ctfTypeNum || data.targetVO.ctfTypeNum == 0) {
              item.questionScore = data.targetVO.ctfTypeNum
            }
          }
          if (item.questionType === 5) {
            if (data.targetVO.awdTypeNum || data.targetVO.awdTypeNum == 0) {
              item.questionScore = data.targetVO.awdTypeNum
            }
          }
          if (item.questionType === 9) {
            if (data.targetVO.bugTypeNum || data.targetVO.bugTypeNum == 0) {
              item.questionScore = data.targetVO.bugTypeNum
            }
          }
          if (item.questionType === 6) {
            if (data.targetVO.otherTypeNum || data.targetVO.otherTypeNum == 0) {
              item.questionScore = data.targetVO.otherTypeNum
            }
          }
        })
      }
      if (this.emulationQuestionList.length) {
        this.emulationQuestionList.forEach(item => {
          if (item.questionType === 1) {
            if (data.emulationVO.singleTypeNum || data.emulationVO.singleTypeNum == 0) {
              item.questionScore = data.emulationVO.singleTypeNum
            }
          }
          if (item.questionType === 2) {
            if (data.emulationVO.manyTypeNum || data.emulationVO.manyTypeNum == 0) {
              item.questionScore = data.emulationVO.manyTypeNum
            }
          }
          if (item.questionType === 3) {
            if (data.emulationVO.judgeTypeNum || data.emulationVO.judgeTypeNum == 0) {
              item.questionScore = data.emulationVO.judgeTypeNum
            }
          }
          if (item.questionType === 4) {
            if (data.emulationVO.ctfTypeNum || data.emulationVO.ctfTypeNum == 0) {
              item.questionScore = data.emulationVO.ctfTypeNum
            }
          }
          if (item.questionType === 7) {
            if (data.emulationVO.completionNum || data.emulationVO.completionNum == 0) {
              item.questionScore = data.emulationVO.completionNum
            }
          }
          if (item.questionType === 8) {
            if (data.emulationVO.saqTypeNum || data.emulationVO.saqTypeNum == 0) {
              item.questionScore = data.emulationVO.saqTypeNum
            }
          }
          if (item.questionType === 10) {
            if (data.emulationVO.combinatorialTypeNum || data.emulationVO.combinatorialTypeNum == 0) {
              item.combinationQuestionBOS.forEach(sub => {
                sub.content.forEach(val => {
                  val.questionScore = data.emulationVO.combinatorialTypeNum
                })
              })
            }
          }
        })
      }
    },
    deleteQuestion(arr, question, index) {
      arr.splice(index, 1)
      this.selectQuestionList = this.selectQuestionList.filter(item => item.id != question.id)
    },
    switchExamClass(item) {
      this.examClassValue = item
      this.checkQuestionList = this.selectQuestionList
      this.theoryQuestionList = this.checkQuestionList.filter(item => { return item.examClassify == this.examClassValue && item.bankType == 1 })
      this.targetQuestionList = this.checkQuestionList.filter(item => { return item.examClassify == this.examClassValue && item.bankType == 2 })
      this.emulationQuestionList = this.checkQuestionList.filter(item => { return item.examClassify == this.examClassValue && item.bankType == 3 })
      // 重置题型题目备用题目
      if (this.theoryQuestionList && this.theoryQuestionList.length > 0) {
        this.theoryQuestionList.map(item => {
          if ([null, undefined, ''].includes(item.isSpare)) {
            item.isSpare = 0
          }
          if (item.questionType == 4 || item.questionType == 5) {
            if ([null, undefined, ''].includes(item.questionAnswerMethod)) {
              item.questionAnswerMethod = 1
            }
          }
        })
      }
      if (this.targetQuestionList && this.targetQuestionList.length > 0) {
        this.targetQuestionList.map(item => {
          if ([null, undefined, ''].includes(item.isSpare)) {
            item.isSpare = 0
          }
          if (item.questionType == 4 || item.questionType == 5) {
            if ([null, undefined, ''].includes(item.questionAnswerMethod)) {
              item.questionAnswerMethod = 1
            }
          }
        })
      }
      if (this.emulationQuestionList && this.emulationQuestionList.length > 0) {
        this.emulationQuestionList.map(item => {
          if ([null, undefined, ''].includes(item.isSpare)) {
            item.isSpare = 0
          }
          if (item.questionType == 4 || item.questionType == 5) {
            if ([null, undefined, ''].includes(item.questionAnswerMethod)) {
              item.questionAnswerMethod = 1
            }
          }
        })
      }
      this.activeName = this.theoryQuestionList.length ? 'theory' : this.targetQuestionList.length ? 'target' : 'emulation'
    },
    // 选择题目
    handleCheckQuestion(q) {
      // 取消
      if (this.checkQuestionList.filter(item => { return item.id === q.id }).length !== 0) {
        this.checkQuestionList = this.checkQuestionList.filter(
          (el) => el.id !== q.id
        )
        return
      }
      // 选择
      this.checkQuestionList.push(q)
    }
  }
}
</script>

<style lang="scss" scoped>
._paper_container {
  border-left: 1px solid #C0C4CC;
  padding-left: 15px;
  display: flex;
  flex: 1;
  flex-direction: column;
  height: 100%;
  ._paper_header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    ._paper_search {
      display: flex;
      align-items: center;
      ._paper_search_1 {
        font-size: 16px;
        font-weight: 500;
        color: #333;
      }
    }
  }
  /deep/ .el-tabs {
    min-height: 0;
    flex: 1;
    display: flex;
    flex-direction: column;
    .el-tabs__content {
      overflow-y: auto;
    }
  }
  ._question_list {
    ._question_item {
      padding: 15px 20px;
      min-height: 90px;
      background: #ffffff;
      display: flex;
      flex-direction: column;
      font-size: 14px;
      color: #4e5969;
      position: relative;
      border: 1px solid #e5e6eb;
      border-radius: 4px;
      margin-bottom: 10px;
      ._question_option {
        margin-top: 10px;
        margin-left: 15px;
        font-size: 14px;
        color: #4e5969;
        display: flex;
        flex-direction: column;
        line-height: 22px;
        word-break: break-all;
        ::v-deep .el-radio {
          margin-bottom: 8px;
          display: flex;
          align-items: flex-start;
          .el-radio__label {
            font-size: 14px;
            color: #4e5969;
            white-space: normal;
            word-break: break-all;
          }
        }
        ::v-deep .el-checkbox {
          margin-bottom: 8px;
          display: flex;
          align-items: center;
          .el-checkbox__label {
            font-size: 14px;
            color: #4e5969;
            white-space: normal;
            word-break: break-all;
          }
        }
      }
      ._question_score {
        border-top: 1px solid #e5e6eb;
        padding: 10px 0 0 0 ;
        display: flex;
        align-items: center;
        justify-content: space-between;
        ._question_delete {
          color: #F56C6C;
          cursor: pointer;
        }
      }
      ._question_item_content {
        display: flex;
        // max-height: 200px;
        // overflow-y: auto;
        overflow-x: auto;
        margin-right: 55px;
      }
      ._question_item_type {
        position: absolute;
        right: 15px;
        top: 15px;
      }
      .combination-question-wrap {
        >div {
          border: 1px solid rgb(229, 230, 235);
          margin: 5px 0px 10px;
          padding: 15px 20px 5px;
          .comp-question {
            display: flex;
            // max-height: 200px;
            // overflow-y: auto;
            overflow-x: auto;
            >span {
              flex: 1;
              word-break: break-all;
            }
          }
          .comp-content-wrap {
            border: 1px solid #e5e6eb;
            margin: 5px 0 10px;
            padding: 15px 20px;
            >div:first-child {
              display: flex;
              // max-height: 200px;
              // overflow-y: auto;
              overflow-x: auto;
              margin-bottom: 10px;
              >span {
                flex: 1;
                word-break: break-all;
              }
            }
          }
        }
      }
      .combination-question-delete {
        color: #F56C6C;
        display: flex;
        justify-content: end;
        >div {
          cursor: pointer;
          width: 42px;
        }
      }
    }

    img {
      position: absolute;
      left: 20px;
      top: 23px;
    }

    ._question_item_check {
      border: 1px solid var(--color-600);
    }
  }
}
.card-bg {
  border-radius: 4px;
  padding: 15px;
  background-color: #FFFFFF;
}
.optional-ctf {
  display: flex;
  .classification-list {
    min-width: 200px;
    height: 100%;
    padding-right: 15px;
    .classification-list-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .classification-list-add {
        .el-button--text {
          color: var(--color-600);
          i {
            font-size: 12px;
          }
        }
      }
    }
    .classification-content {
      padding: 5px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 10px;
      cursor: pointer;
      .classification-content-overflow {
        flex: 1;
        white-space: nowrap; /* 不换行 */
        overflow: hidden;    /* 隐藏超出部分 */
        text-overflow: ellipsis; /* 显示省略号 */
        margin-right: 10px;
      }
    }
    .classification-content:hover {
      font-weight: 700;
      background-color: var(--color-50) !important;
      color: var(--color-600);
    }
    .is-active {
      font-weight: 700;
      background-color: var(--color-50) !important;
      color: var(--color-600);
    }
  }
}
</style>
