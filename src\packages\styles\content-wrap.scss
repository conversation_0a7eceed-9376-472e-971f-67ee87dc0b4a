// 内容区样式
.content-wrap-layout {
  height: 100%;
  display: flex;
  flex-direction: column;
}
// 内容区-操作栏样式
.operation-wrap {
  margin-bottom: 12px;
  position: relative;
  .buttons-wrap {
    display: inline-block;
  }
  .operation-left {
    float: left;
  }
  .operation-right {
    position: absolute;
    right: 0;
  }
  .el-button+.el-button {
    margin-left: 0;
  }
}
// 纵向分类wrap
.vertical-wrap {
  flex: 1;
  overflow: hidden;
  display: flex;
}
// 内容区-表格样式
.resource-table {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: 0;
  min-width: 0;
  padding: 15px;
  background-color: white;
}
