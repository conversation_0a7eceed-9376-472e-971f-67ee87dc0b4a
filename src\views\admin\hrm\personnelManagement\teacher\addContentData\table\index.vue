<template>
  <div class="resource-table">
    <!-- 操作区 -->
    <div class="operation-wrap">
      <div class="operation-left">
        <slot name="action" />
        <el-button type="primary" icon="el-icon-refresh" @click="refresh" />
      </div>
      <div class="operation-right">
        <el-badge :value="searchBtnShowNum">
          <el-button icon="el-icon-search" @click="openSearch">搜索</el-button>
        </el-badge>
        <!-- 自定义表格列 -->
        <t-table-config
          v-if="!customColData.length"
          :data="columnsObj"
          :active-key-arr="columnsViewArr"
          @on-change-col="onChangeCol"
        />
      </div>
    </div>
    <!-- 搜索区 -->
    <t-search-box
      v-show="searchView"
      :search-key-list="searchKeyListView"
      default-placeholder="默认搜索全部"
      @search="searchMultiple"
    />
    <!-- 列表 -->
    <t-table-view
      ref="tableView"
      :height="height"
      :single="single"
      :loading="tableLoading"
      :data="examQuestionList"
      :total="tableTotal"
      :page-size="pageSize"
      :current="pageCurrent"
      :select-item="selectItem"
      current-key="userId"
      @on-select="onSelect"
      @on-current="onCurrent"
      @on-change="changePage"
      @on-sort-change="onSortChange"
      @on-page-size-change="onPageSizeChange"
    >
      <el-table-column v-if="columnsViewArr.includes('realname')" key="realname" :min-width="colMinWidth" prop="realname" label="姓名" show-overflow-tooltip/>

      <el-table-column v-if="columnsViewArr.includes('sex')" key="sex" :min-width="colMinWidth" label="性别" prop="sex" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row['sex'] ? (scope.row['sex'] == 1 ? '男':'女') : "-" }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="columnsViewArr.includes('username')" key="username" :min-width="colMinWidth" prop="username" label="账号" show-overflow-tooltip/>
    </t-table-view>
  </div>
</template>

<script>
import module from '../config.js'
import tSearchBox from '@/packages/search-box/index.vue'
import tTableView from '@/packages/table-view/index.vue'
import tTableConfig from '@/packages/table-config/table-col-config.vue'
import tableTdMultiCol from '@/packages/table-config/table-td-multi-col.vue'
import mixinsPageTable from '@/packages/mixins/page_table'
import { queryAddTeacherManager } from '@/api/admin/training/student'

export default {
  components: {
    tSearchBox,
    tTableView,
    tTableConfig,
    tableTdMultiCol
  },
  mixins: [mixinsPageTable],
  data() {
    return {
      moduleName: module.name,
      // 搜索配置项
      searchKeyList: [
        { key: 'realname', label: '姓名', master: true },
        { key: 'userName', label: '账号' }
      ],
      // 所有可配置显示列 master：不可隐藏 title:列名称
      columnsObj: {
        'realname': {
          title: '姓名',
          master: true
        },
        'sex': {
          title: '性别'
        },
        'username': {
          title: '账号'
        }
      },
      // 当前显示列key表 默认，如果localStorage有数据将被覆盖
      columnsViewArr: [
        'realname',
        'sex',
        'username'
      ],
      examQuestionList: [],
      queryParameters: {}
    }
  },
  methods: {
    getList: function(showLoading = true) {
      if (showLoading) {
        this.tableLoading = true
      }
      const params = this.getPostData('page', 'limit')
      queryAddTeacherManager(params).then(res => {
        this.examQuestionList = res.data.records
        this.tableTotal = res.data.total
        this.tableLoading = false
      }).catch(() => {
        this.tableLoading = false
      })
    },
    classificationType(param) {
      this.queryParameters = param
      this.getList()
    }

  }
}
</script>
