// kkfilePreview 预览时，禁用对文件 复制图片、图片另存为，打印功能
export const handlekkfilePreview = (target = '#frame') => {
  const iframe = document.querySelector(target)
  iframe.onload = function() {
    // 获取iframe内部文档对象
    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document
    const body = iframeDoc.body
    // 禁用右键菜单
    body.addEventListener('contextmenu', (e) => {
      e.preventDefault()
    })
    // 隐藏预览 xlsx 文件时的打印按钮
    const buttonArea = body.querySelector('#button-area')
    buttonArea && (buttonArea.style.display = 'none')
  }
}
