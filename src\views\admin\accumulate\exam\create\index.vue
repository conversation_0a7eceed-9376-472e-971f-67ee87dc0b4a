<template>
  <create-view :loading="loading" :title="id ? '编辑试卷' : '创建试卷'">
    <div slot="content" class="create-personal-wrap">
      <el-steps :active="active" simple style="width: 80%;">
        <el-step title="基本信息" />
        <el-step title="选择题目" />
        <el-step v-if="formData.examType !== 1" title="配置题目" />
      </el-steps>
      <div class="step-wrap">
        <info v-if="active === 0 && !loading" ref="info" :data="formData" :category-list="categoryList"/>
        <intelligence v-if="active === 1 && (formData.generatingTestPaper === 1 && !id ) && formData.examType === 0" ref="intelligence" :class-list="classifyList" :intelligence-data="intelligenceData" @intelligence-data-change="intelligenceDataChange"/>
        <optional v-if="active === 1 && (formData.generatingTestPaper === 2 || id) && formData.examType === 0 && (formData.sceneTypeId != 5 || formData.generatingTestPaper === 1 && id)" ref="optional" :select-question-list="checkQuestionList" :class-list="classifyList" @check-question-list-change="checkQuestionListChange"/>
        <optional-ctf v-if="active === 1 && (formData.generatingTestPaper === 2 || id) && formData.examType === 0 && formData.sceneTypeId == 5 && formData.generatingTestPaper !== 1" ref="optionalCtf" :select-question-list="checkQuestionList" :class-obj="classifyObj" :exam-class-list="examClassifys" @check-question-list-change="checkQuestionListChange"/>
        <score-configuration v-if="active === 2 && formData.examType === 0 && (formData.sceneTypeId != 5 || formData.generatingTestPaper === 1)" ref="scoreConfiguration" :theory-list="theoryList" :target-list="targetList" :emulation-list="emulationList" />
        <score-configuration-ctf v-if="active === 2 && formData.examType === 0 && (formData.sceneTypeId == 5 && formData.generatingTestPaper !== 1)" ref="scoreConfigurationCtf" :exam-class-list="examClassifys" :select-question-list="checkQuestionList"/>
        <trendsExam v-if="active === 1 && formData.examType === 1" ref="trendsExam" :theory-v-o="theoryRuleVO" :target-v-o="targetRuleVO" :simulation-v-o="simulationRuleVO"/>
      </div>
    </div>
    <div slot="footer">
      <el-button type="text" @click="$router.go(-1)">取消</el-button>
      <el-button v-if="active !== 0" type="primary" @click="back()">上一步</el-button>
      <el-button v-if="active === 0 || (active === 1 && formData.examType !== 1)" :disabled="nextType" type="primary" @click="next()">下一步</el-button>
      <el-button v-if="active === 2" type="primary" @click="confirm">确定</el-button>
      <el-button v-if="active === 1 && formData.examType === 1" type="primary" @click="confirmTrends">确定</el-button>
    </div>
  </create-view>
</template>
<script>
import createView from '@/packages/create-view/index'
import info from './info.vue'
import intelligence from './intelligence.vue'
import scoreConfiguration from './scoreConfiguration.vue'
import optional from './optional.vue'
import optionalCtf from './optionalCtf.vue'
import trendsExam from './trendsExam.vue'
import { getCategory, categoryComplexityNumber } from '@/api/accumulate/category'
import { createExam, getExam, getQueryQuestionPageByExamId, updateExam } from '@/api/accumulate/exam'
import { getIntelligence } from '@/api/accumulate/questionBank'
import ScoreConfigurationCtf from './scoreConfigurationCtf.vue'

export default {
  components: {
    createView,
    info,
    intelligence,
    optional,
    trendsExam,
    scoreConfiguration,
    optionalCtf,
    ScoreConfigurationCtf
  },
  data() {
    return {
      active: 0,
      loading: false,
      sceneTypeList: [],
      formData: {
        examName: '',
        examDesc: '',
        difficulty: 1,
        suggestTime: 30,
        selectedTopic: 2,
        generatingTestPaper: 1,
        examType: 0,
        categoryId: '',
        sceneTypeId: '',
        examQuestionRelationBOS: [],
        examQuestionRelationTypes: []
      },
      classifyList: [],
      classifyObj: {
        bool: false
      },
      examClassifys: ['CRYPTO', 'MISC', 'PWN', 'REVERSE', 'WEB'],
      categoryList: [],
      checkQuestionList: [],
      theoryList: [],
      targetList: [],
      emulationList: [],
      id: this.$route.query.id,
      bankTypeList: ['理论题', '靶机题', '仿真题'],
      intelligenceNum: 0,
      intelligenceData: {
        'theoryVO': {
          singleTypeNum: 0,
          manyTypeNum: 0,
          judgeTypeNum: 0,
          ctfTypeNum: 0,
          completionNum: 0,
          saqTypeNum: 0
        },
        'targetVO': {
          ctfTypeNum: 0,
          awdTypeNum: 0,
          bugTypeNum: 0,
          otherTypeNum: 0
        },
        'emulationVO': {
          singleTypeNum: 0,
          manyTypeNum: 0,
          judgeTypeNum: 0,
          ctfTypeNum: 0,
          completionNum: 0,
          saqTypeNum: 0,
          combinatorialTypeNum: 0
        }
      },
      // 理论题
      theoryRuleVO: {
        categoryVOList: [
          {
            categoryId: '',
            categoryVO: {},
            primarySingle: 0, // 初级单选题
            primaryMultiple: 0, // 初级多选题
            primaryJudge: 0, // 初级判断题
            primaryCTF: 0, // 初级CTF题
            primaryCompletion: 0, // 初级填空题
            primaryShort: 0, // 初级简答题
            primaryAWD: 0, // 初级awd题
            primaryBug: 0, // 初级bug题
            primaryCombinatorial: 0, // 初级组合题
            primaryOther: 0, // 初级其它题
            middleSingle: 0, // 中级单选题
            middleMultiple: 0, // 中级多选题
            middleJudge: 0, // 中级判断题
            middleCTF: 0, // 中级CTF题
            middleCompletion: 0, // 中级填空题
            middleShort: 0, // 中级简答题
            middleAWD: 0, // 中级awd题
            middleBug: 0, // 中级bug题
            middleCombinatorial: 0, // 中级组合题
            middleOther: 0, // 中级其它题
            seniorSingle: 0, // 高级单选题
            seniorMultiple: 0, // 高级多选题
            seniorJudge: 0, // 高级判断题
            seniorCTF: 0, // 高级CTF题
            seniorCompletion: 0, // 高级填空题
            seniorShort: 0, // 高级简答题
            seniorAWD: 0, // 高级awd题
            seniorBug: 0, // 高级bug题
            seniorCombinatorial: 0, // 高级组合题
            seniorOther: 0 // 高级其它题
          }
        ],
        scoreSingle: 0, // 单选题分数
        scoreMultiple: 0, // 多选题分数
        scoreJudge: 0, // 判断题分数
        scoreCTF: 0, // CTF题分数
        scoreCompletion: 0, // 填空题分数
        scoreShort: 0 // 简答题分数
      },
      // 靶机题
      targetRuleVO: {
        categoryVOList: [
          {
            categoryId: '',
            categoryVO: {},
            primarySingle: 0, // 初级单选题
            primaryMultiple: 0, // 初级多选题
            primaryJudge: 0, // 初级判断题
            primaryCTF: 0, // 初级CTF题
            primaryCompletion: 0, // 初级填空题
            primaryShort: 0, // 初级简答题
            primaryAWD: 0, // 初级awd题
            primaryBug: 0, // 初级bug题
            primaryCombinatorial: 0, // 初级组合题
            primaryOther: 0, // 初级其它题
            middleSingle: 0, // 中级单选题
            middleMultiple: 0, // 中级多选题
            middleJudge: 0, // 中级判断题
            middleCTF: 0, // 中级CTF题
            middleCompletion: 0, // 中级填空题
            middleShort: 0, // 中级简答题
            middleAWD: 0, // 中级awd题
            middleBug: 0, // 中级bug题
            middleCombinatorial: 0, // 中级组合题
            middleOther: 0, // 中级其它题
            seniorSingle: 0, // 高级单选题
            seniorMultiple: 0, // 高级多选题
            seniorJudge: 0, // 高级判断题
            seniorCTF: 0, // 高级CTF题
            seniorCompletion: 0, // 高级填空题
            seniorShort: 0, // 高级简答题
            seniorAWD: 0, // 高级awd题
            seniorBug: 0, // 高级bug题
            seniorCombinatorial: 0, // 高级组合题
            seniorOther: 0 // 高级其它题
          }
        ],
        scoreCTF: 0, // CTF题分数
        scoreAWD: 0, // AWD题分数
        scoreOther: 0, // 其它题分数
        scoreBug: 0 // 漏洞题分数
      },
      // 仿真题
      simulationRuleVO: {
        categoryVOList: [
          {
            categoryId: '',
            categoryVO: {},
            primarySingle: 0, // 初级单选题
            primaryMultiple: 0, // 初级多选题
            primaryJudge: 0, // 初级判断题
            primaryCTF: 0, // 初级CTF题
            primaryCompletion: 0, // 初级填空题
            primaryShort: 0, // 初级简答题
            primaryAWD: 0, // 初级awd题
            primaryBug: 0, // 初级bug题
            primaryCombinatorial: 0, // 初级组合题
            primaryOther: 0, // 初级其它题
            middleSingle: 0, // 中级单选题
            middleMultiple: 0, // 中级多选题
            middleJudge: 0, // 中级判断题
            middleCTF: 0, // 中级CTF题
            middleCompletion: 0, // 中级填空题
            middleShort: 0, // 中级简答题
            middleAWD: 0, // 中级awd题
            middleBug: 0, // 中级bug题
            middleCombinatorial: 0, // 中级组合题
            middleOther: 0, // 中级其它题
            seniorSingle: 0, // 高级单选题
            seniorMultiple: 0, // 高级多选题
            seniorJudge: 0, // 高级判断题
            seniorCTF: 0, // 高级CTF题
            seniorCompletion: 0, // 高级填空题
            seniorShort: 0, // 高级简答题
            seniorAWD: 0, // 高级awd题
            seniorBug: 0, // 高级bug题
            seniorCombinatorial: 0, // 高级组合题
            seniorOther: 0 // 高级其它题
          }
        ],
        scoreSingle: 0, // 单选题分数
        scoreMultiple: 0, // 多选题分数
        scoreJudge: 0, // 判断题分数
        scoreCTF: 0, // CTF题分数
        scoreCompletion: 0, // 填空题分数
        scoreShort: 0, // 简答题分数
        scoreCombinatorial: 0 // 组合题分数
      }
    }
  },
  computed: {
    nextType() {
      if (this.id) {
        if (this.active === 1 && !this.checkQuestionList.length) {
          return true
        } else {
          return false
        }
      } else if (this.formData.sceneTypeId != 5 || this.formData.generatingTestPaper === 1) {
        if ((!this.checkQuestionList.length && this.active === 1 && this.formData.generatingTestPaper === 2) || (!this.intelligenceNum && this.active === 1 && this.formData.generatingTestPaper === 1)) {
          return true
        } else {
          return false
        }
      } else if (this.formData.sceneTypeId == 5 && this.formData.generatingTestPaper === 2) {
        if (this.examClassifys.length && this.checkQuestionList.length) {
          let bool = true
          this.examClassifys.forEach(item => {
            if (this.checkQuestionList.filter(i => { return item === i.examClassify }).length != 0) {
              bool = false
            }
          })
          return bool
        } else {
          return true
        }
      }
    }
  },
  watch: {
    'formData.sceneTypeId': {
      handler(newValue, oldValue) {
        if (oldValue) {
          this.examClassifys = ['CRYPTO', 'MISC', 'PWN', 'REVERSE', 'WEB']
          this.formData.examQuestionRelationBOS = []
          this.formData.examQuestionRelationTypes = []
          this.checkQuestionList = []
          this.theoryList = []
          this.targetList = []
          this.emulationList = []
          this.classifyList = []
          this.intelligenceNum = 0
          this.intelligenceData = {
            'theoryVO': {
              singleTypeNum: 0,
              manyTypeNum: 0,
              judgeTypeNum: 0,
              ctfTypeNum: 0,
              completionNum: 0,
              saqTypeNum: 0
            },
            'targetVO': {
              ctfTypeNum: 0,
              awdTypeNum: 0,
              bugTypeNum: 0,
              otherTypeNum: 0
            },
            'emulationVO': {
              singleTypeNum: 0,
              manyTypeNum: 0,
              judgeTypeNum: 0,
              ctfTypeNum: 0,
              completionNum: 0,
              saqTypeNum: 0,
              combinatorialTypeNum: 0
            }
          }
          this.theoryRuleVO = {
            categoryVOList: [
              {
                categoryId: '',
                categoryVO: {},
                primarySingle: 0, // 初级单选题
                primaryMultiple: 0, // 初级多选题
                primaryJudge: 0, // 初级判断题
                primaryCTF: 0, // 初级CTF题
                primaryCompletion: 0, // 初级填空题
                primaryShort: 0, // 初级简答题
                primaryAWD: 0, // 初级awd题
                primaryBug: 0, // 初级bug题
                primaryCombinatorial: 0, // 初级组合题
                primaryOther: 0, // 初级其它题
                middleSingle: 0, // 中级单选题
                middleMultiple: 0, // 中级多选题
                middleJudge: 0, // 中级判断题
                middleCTF: 0, // 中级CTF题
                middleCompletion: 0, // 中级填空题
                middleShort: 0, // 中级简答题
                middleAWD: 0, // 中级awd题
                middleBug: 0, // 中级bug题
                middleCombinatorial: 0, // 中级组合题
                middleOther: 0, // 中级其它题
                seniorSingle: 0, // 高级单选题
                seniorMultiple: 0, // 高级多选题
                seniorJudge: 0, // 高级判断题
                seniorCTF: 0, // 高级CTF题
                seniorCompletion: 0, // 高级填空题
                seniorShort: 0, // 高级简答题
                seniorAWD: 0, // 高级awd题
                seniorBug: 0, // 高级bug题
                seniorCombinatorial: 0, // 高级组合题
                seniorOther: 0 // 高级其它题
              }
            ],
            scoreSingle: 0, // 单选题分数
            scoreMultiple: 0, // 多选题分数
            scoreJudge: 0, // 判断题分数
            scoreCTF: 0, // CTF题分数
            scoreCompletion: 0, // 填空题分数
            scoreShort: 0 // 简答题分数
          }
          this.targetRuleVO = {
            categoryVOList: [
              {
                categoryId: '',
                categoryVO: {},
                primarySingle: 0, // 初级单选题
                primaryMultiple: 0, // 初级多选题
                primaryJudge: 0, // 初级判断题
                primaryCTF: 0, // 初级CTF题
                primaryCompletion: 0, // 初级填空题
                primaryShort: 0, // 初级简答题
                primaryAWD: 0, // 初级awd题
                primaryBug: 0, // 初级bug题
                primaryCombinatorial: 0, // 初级组合题
                primaryOther: 0, // 初级其它题
                middleSingle: 0, // 中级单选题
                middleMultiple: 0, // 中级多选题
                middleJudge: 0, // 中级判断题
                middleCTF: 0, // 中级CTF题
                middleCompletion: 0, // 中级填空题
                middleShort: 0, // 中级简答题
                middleAWD: 0, // 中级awd题
                middleBug: 0, // 中级bug题
                middleCombinatorial: 0, // 中级组合题
                middleOther: 0, // 中级其它题
                seniorSingle: 0, // 高级单选题
                seniorMultiple: 0, // 高级多选题
                seniorJudge: 0, // 高级判断题
                seniorCTF: 0, // 高级CTF题
                seniorCompletion: 0, // 高级填空题
                seniorShort: 0, // 高级简答题
                seniorAWD: 0, // 高级awd题
                seniorBug: 0, // 高级bug题
                seniorCombinatorial: 0, // 高级组合题
                seniorOther: 0 // 高级其它题
              }
            ],
            scoreCTF: 0, // CTF题分数
            scoreAWD: 0, // AWD题分数
            scoreOther: 0, // 其它题分数
            scoreBug: 0 // 漏洞题分数
          }
          this.simulationRuleVO = {
            categoryVOList: [
              {
                categoryId: '',
                categoryVO: {},
                primarySingle: 0, // 初级单选题
                primaryMultiple: 0, // 初级多选题
                primaryJudge: 0, // 初级判断题
                primaryCTF: 0, // 初级CTF题
                primaryCompletion: 0, // 初级填空题
                primaryShort: 0, // 初级简答题
                primaryAWD: 0, // 初级awd题
                primaryBug: 0, // 初级bug题
                primaryCombinatorial: 0, // 初级组合题
                primaryOther: 0, // 初级其它题
                middleSingle: 0, // 中级单选题
                middleMultiple: 0, // 中级多选题
                middleJudge: 0, // 中级判断题
                middleCTF: 0, // 中级CTF题
                middleCompletion: 0, // 中级填空题
                middleShort: 0, // 中级简答题
                middleAWD: 0, // 中级awd题
                middleBug: 0, // 中级bug题
                middleCombinatorial: 0, // 中级组合题
                middleOther: 0, // 中级其它题
                seniorSingle: 0, // 高级单选题
                seniorMultiple: 0, // 高级多选题
                seniorJudge: 0, // 高级判断题
                seniorCTF: 0, // 高级CTF题
                seniorCompletion: 0, // 高级填空题
                seniorShort: 0, // 高级简答题
                seniorAWD: 0, // 高级awd题
                seniorBug: 0, // 高级bug题
                seniorCombinatorial: 0, // 高级组合题
                seniorOther: 0 // 高级其它题
              }
            ],
            scoreSingle: 0, // 单选题分数
            scoreMultiple: 0, // 多选题分数
            scoreJudge: 0, // 判断题分数
            scoreCTF: 0, // CTF题分数
            scoreCompletion: 0, // 填空题分数
            scoreShort: 0, // 简答题分数
            scoreCombinatorial: 0 // 组合题分数
          }
          this.classifyObj = {
            bool: false
          }
        }
      },
      deep: true
    }
  },
  mounted() {
    this.getList()
    if (this.id) {
      this.loading = true
      this.getExamFn()
    }
  },
  methods: {
    checkQuestionListChange(data) {
      this.checkQuestionList = data
    },
    intelligenceDataChange(data) {
      this.intelligenceData = data
      this.intelligenceNum = 0
      for (const key in data) {
        for (const key2 in data[key]) {
          this.intelligenceNum = this.intelligenceNum + data[key][key2]
        }
      }
    },
    back() {
      if (this.active === 2 && this.formData.sceneTypeId != 5) {
        this.checkQuestionList = [...this.$refs['scoreConfiguration'].theoryQuestionList, ...this.$refs['scoreConfiguration'].targetQuestionList, ...this.$refs['scoreConfiguration'].emulationQuestionList]
      } else if (this.active === 2 && this.formData.sceneTypeId == 5 && this.formData.generatingTestPaper === 2) {
        this.checkQuestionList = this.$refs['scoreConfigurationCtf'].checkQuestionList
      } else if (this.active === 1 && this.formData.examType === 1) {
        this.theoryRuleVO = this.$refs['trendsExam'].theoryRuleVO
        this.targetRuleVO = this.$refs['trendsExam'].targetRuleVO
        this.simulationRuleVO = this.$refs['trendsExam'].simulationRuleVO
      }
      this.active = this.active - 1
    },
    next() {
      if (this.active === 0) {
        this.$refs['info'].$refs['form'].validate((valid) => {
          if (valid) {
            for (const key in this.formData) {
              if (key !== 'examQuestionRelationBOS') {
                this.formData[key] = this.$refs['info'].formData[key]
              }
            }
            this.active = 1
          }
        })
      } else if (this.active === 1 && (this.formData.generatingTestPaper === 2 || this.id) && (this.formData.sceneTypeId != 5 || (this.formData.generatingTestPaper === 1 && this.id))) {
        this.classifyList = this.$refs['optional'].classifyList
        this.checkQuestionList = this.$refs['optional'].checkQuestionList.sort((a, b) => {
          return a.questionType - b.questionType
        })
        this.theoryList = this.checkQuestionList.filter(item => { return item.bankType == 1 })
        this.targetList = this.checkQuestionList.filter(item => { return item.bankType == 2 })
        this.emulationList = this.checkQuestionList.filter(item => { return item.bankType == 3 })
        this.active = 2
      } else if (this.active === 1 && (this.formData.generatingTestPaper === 2 || this.id) && (this.formData.sceneTypeId == 5)) {
        this.active = 2
      } else if (this.active === 1 && (this.formData.generatingTestPaper === 1 && !this.id)) {
        this.$refs['intelligence'].$refs['form'].validate((valid) => {
          if (valid) {
            this.classifyList = this.$refs['intelligence'].classifyList
            this.intelligenceData = this.$refs['intelligence'].formData
            const obj = {
              selectedTopic: 2,
              categoryIds: [],
              theoryVO: this.intelligenceData.theoryVO,
              targetVO: this.intelligenceData.targetVO,
              emulationVO: this.intelligenceData.emulationVO
            }
            this.classifyList.forEach(item => {
              obj.categoryIds.push(item.id)
            })
            getIntelligence(obj).then(res => {
              let arr = []
              arr = res.data.sort((a, b) => {
                return a.questionType - b.questionType
              })
              arr.forEach(item => {
                if (item.questionType == 10) {
                  item.combinationQuestionBOS.forEach(sub => {
                    sub.content = JSON.parse(sub.content).map(val => {
                      return {
                        contentName: val,
                        questionScore: null
                      }
                    })
                  })
                }
              })
              this.theoryList = arr.filter(item => { return item.bankType == 1 })
              this.targetList = arr.filter(item => { return item.bankType == 2 })
              this.emulationList = arr.filter(item => { return item.bankType == 3 })
              this.active = 2
            })
          }
        })
      }
    },
    'getList': function() {
      getCategory({ pageType: 0, categoryType: 4 }).then(res => {
        this.categoryList = res.data.records
      })
    },
    getExamFn() {
      getExam({ id: this.id }).then(res => {
        this.formData = res.data
        if (res.data.examClassifys && res.data.examQuestionRelationTypes) {
          this.examClassifys = res.data.examClassifys
          res.data.examQuestionRelationTypes.forEach(item => {
            this.classifyObj[item.examClassify] = []
            item.knowledgeVoList.forEach(element => {
              this.classifyObj[item.examClassify].push({
                id: element.knowledgeCode,
                categoryName: element.knowledgeName
              })
            })
          })
        }
        if (this.formData.examType === 0) {
          if (res.data.knowledgeVoList) {
            res.data.knowledgeVoList.forEach(item => {
              this.classifyList.push({
                id: item.knowledgeCode,
                categoryName: item.knowledgeName
              })
            })
          }
        } else {
          this.theoryRuleVO = JSON.parse(res.data.dynamicPaperRules).theoryRuleVO
          this.theoryRuleVO.categoryVOList.forEach(item => {
            this.categorySelect(item.categoryId, item.skillPointId ? item.skillPointId : '', item)
          })
          this.targetRuleVO = JSON.parse(res.data.dynamicPaperRules).targetRuleVO
          this.targetRuleVO.categoryVOList.forEach(item => {
            this.categorySelect(item.categoryId, item.skillPointId ? item.skillPointId : '', item)
          })
          this.simulationRuleVO = JSON.parse(res.data.dynamicPaperRules).simulationRuleVO
          this.simulationRuleVO.categoryVOList.forEach(item => {
            this.categorySelect(item.categoryId, item.skillPointId ? item.skillPointId : '', item)
          })
        }
        this.loading = false
      })
      getQueryQuestionPageByExamId({ pageType: 0, examId: this.id }).then(res => {
        res.data.records.forEach(item => {
          if (item.questionType == 10) {
            const combinationPoints = JSON.parse(item.combinationPoints)
            item.combinationQuestionBOS.forEach((sub, index) => {
              sub.content = JSON.parse(sub.content).map((val, subIndex) => {
                return {
                  contentName: val,
                  questionScore: combinationPoints[index][subIndex]
                }
              })
            })
          }
        })
        this.checkQuestionList = res.data.records
      })
    },
    categorySelect(categoryId, skillPointId, item) {
      if (categoryId || skillPointId || item.questionDepotId) {
        item.questionDepotId = item.questionDepotId || ''
        const formData = new FormData()
        formData.append('categoryId', categoryId)
        formData.append('skillPointId', skillPointId)
        formData.append('questionDepotId', item.questionDepotId)
        categoryComplexityNumber(formData).then(res => {
          item.categoryVO = res.data
        })
      }
    },
    confirm: function() {
      this.loading = true
      if (this.formData.sceneTypeId != 5 || (this.formData.sceneTypeId == 5 && this.formData.generatingTestPaper == 1)) {
        this.checkQuestionList = [...this.$refs['scoreConfiguration'].theoryQuestionList, ...this.$refs['scoreConfiguration'].targetQuestionList, ...this.$refs['scoreConfiguration'].emulationQuestionList]
        const classifyId = []
        this.classifyList.forEach(item => {
          classifyId.push(item.id)
        })
        this.formData.knowledgeCategoryAll = classifyId.join(',')
        this.formData.score = this.$refs['scoreConfiguration'].getScore
        this.formData.questionNum = this.checkQuestionList.length
        this.checkQuestionList = this.checkQuestionList.sort((a, b) => {
          return a.questionType - b.questionType
        })
        this.formData.examQuestionRelationBOS = []
        this.checkQuestionList.forEach((item, index) => {
          // 组合题的分数特殊处理：[[1, 2], [3, 4]]
          let combinationPoints = []
          if (item.questionType == 10) {
            item.combinationQuestionBOS.forEach(sub => {
              combinationPoints.push(sub.content.map(val => val.questionScore))
            })
          }
          combinationPoints = JSON.stringify(combinationPoints)
          this.formData.examQuestionRelationBOS.push({
            questionId: item.id,
            isSpare: item.isSpare,
            questionAnswerMethod: item.questionAnswerMethod,
            questionScore: item.questionScore,
            combinationPoints: combinationPoints, // 组合题的分数
            ranking: index + 1
          })
        })
      }
      if (this.formData.sceneTypeId == 5 && this.formData.generatingTestPaper === 2) {
        this.checkQuestionList = [...this.$refs['scoreConfigurationCtf'].selectQuestionList]
        this.formData.examClassifys = this.examClassifys
        const arr = []
        for (const key in this.classifyObj) {
          if (key != 'bool') {
            arr.push({
              examClassify: key,
              knowledgeCategoryList: this.classifyObj[key],
              knowledgeCategoryAll: [],
              examQuestionRelations: []
            })
          }
        }
        arr.forEach(item => {
          item.knowledgeCategoryList.forEach(element => {
            item.knowledgeCategoryAll.push(element.id)
          })
        })
        arr.forEach(item => {
          item.knowledgeCategoryAll = item.knowledgeCategoryAll.join(',')
        })
        arr.forEach(item => {
          this.checkQuestionList.forEach(element => {
            if (element.examClassify == item.examClassify) {
              let combinationPoints = []
              if (element.questionType == 10) {
                element.combinationQuestionBOS.forEach(sub => {
                  combinationPoints.push(sub.content.map(val => val.questionScore))
                })
              }
              combinationPoints = JSON.stringify(combinationPoints)
              item.examQuestionRelations.push({
                questionId: element.id,
                isSpare: element.isSpare,
                questionAnswerMethod: item.questionAnswerMethod,
                questionScore: element.questionScore,
                examClassify: element.examClassify,
                combinationPoints: combinationPoints // 组合题的分数
              })
            }
          })
        })
        arr.forEach(item => {
          item.examQuestionRelations.forEach((element, index) => {
            element.ranking = index + 1
          })
        })
        this.formData.score = this.$refs['scoreConfigurationCtf'].getScore
        this.formData.questionNum = this.checkQuestionList.length
        this.formData.examQuestionRelationTypes = arr.filter(item => { return item.examQuestionRelations.length != 0 })
      }
      if (this.id) {
        this.formData.id = this.id
        updateExam(this.formData).then(res => {
          this.$message.success(`试卷编辑成功`)
          this.$router.go(-1)
          this.loading = false
        }).catch(() => {
          this.loading = false
        })
      } else {
        createExam(this.formData).then(res => {
          this.$message.success(`试卷创建成功`)
          this.$router.go(-1)
          this.loading = false
        }).catch(() => {
          this.loading = false
        })
      }
    },
    confirmTrends: function() {
      Promise.all([this.submitForm('theoryForm'), this.submitForm('targetForm'), this.submitForm('simulationForm'), this.submitForm('formSimulation'), this.submitForm('formTarget'), this.submitForm('formTheory')])
        .then(() => {
          let categoryBool = false
          this.$refs['trendsExam'].targetRuleVO.categoryVOList.forEach((item, index) => {
            if (item['questionDepotId'] || item['categoryId'] || item['skillPointId']) {
              categoryBool = true
            }
          })
          this.$refs['trendsExam'].simulationRuleVO.categoryVOList.forEach((item, index) => {
            if (item['questionDepotId'] || item['categoryId'] || item['skillPointId']) {
              categoryBool = true
            }
          })
          this.$refs['trendsExam'].theoryRuleVO.categoryVOList.forEach((item, index) => {
            if (item['questionDepotId'] || item['categoryId'] || item['skillPointId']) {
              categoryBool = true
            }
          })
          if (categoryBool) {
            this.loading = true
            const obj = {
              examName: this.formData.examName,
              examDesc: this.formData.examDesc,
              difficulty: this.formData.difficulty,
              suggestTime: this.formData.suggestTime,
              examType: this.formData.examType,
              categoryId: this.formData.categoryId,
              sceneTypeId: this.formData.sceneTypeId,
              score: this.$refs['trendsExam'].totalScore,
              questionNum: this.$refs['trendsExam'].total,
              dynamicPaperRules: JSON.stringify({
                targetRuleVO: this.$refs['trendsExam'].targetRuleVO,
                simulationRuleVO: this.$refs['trendsExam'].simulationRuleVO,
                theoryRuleVO: this.$refs['trendsExam'].theoryRuleVO
              })
            }
            if (this.id) {
              obj.id = this.id
              updateExam(obj).then(res => {
                this.$message.success(`试卷编辑成功`)
                this.$router.go(-1)
                this.loading = false
              }).catch(() => {
                this.loading = false
              })
            } else {
              createExam(obj).then(res => {
                this.$message.success(`试卷创建成功`)
                this.$router.go(-1)
                this.loading = false
              }).catch(() => {
                this.loading = false
              })
            }
          } else {
            this.$message.error('至少选择一个类型')
          }
        })
        .catch(() => {
          this.$message.error('验证失败')
        })
    },
    submitForm(formUser) {
      return new Promise((resolve, reject) => {
        if (formUser === 'theoryForm' || formUser === 'formTheory') {
          this.$refs['trendsExam'].activeName = 'theory'
        } else if (formUser === 'targetForm' || formUser === 'formTarget') {
          this.$refs['trendsExam'].activeName = 'target'
        } else if (formUser === 'simulationForm' || formUser === 'formSimulation') {
          this.$refs['trendsExam'].activeName = 'simulation'
        }
        this.$refs['trendsExam'].$refs[formUser].validate((valid) => {
          if (valid) {
            resolve()
          } else {
            reject(new Error('错误'))
          }
        })
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.create-personal-wrap {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  /deep/ .el-steps {
    margin-bottom: 12px;
  }
  .step-wrap {
    flex: 1;
    width: 100%;
    min-height: 0;
    overflow: auto;
  }
}
</style>
