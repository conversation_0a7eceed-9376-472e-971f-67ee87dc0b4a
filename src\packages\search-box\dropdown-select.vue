<template>
  <div class="dropdown-select">
    <div class="dropdown-select-list">
      <el-input v-model="filterKey" size="mini" placeholder="请输入过滤条件" class="filter-input" />
      <el-checkbox-group v-model="selectedList">
        <ul>
          <li v-for="item in filterData" :key="item.value">
            <el-checkbox :label="item.value" :title="item.label">{{ item.label }}</el-checkbox>
          </li>
        </ul>
      </el-checkbox-group>
    </div>
    <div class="dropdown-select-bottom">
      <el-button type="primary" size="mini" @click="confirm">确定</el-button>
      <el-button size="mini" type="text" @click="cancel">取消</el-button>
    </div>
  </div>
</template>
<style lang="less">
.dropdown-select {
  width: 150px;
  .dropdown-select-input {
    padding-bottom: 10px;
  }
  .dropdown-select-list {
    padding-bottom: 10px;
    ul {
      max-height: 200px;
      overflow: auto;
      overflow-x: hidden;
    }
    li {
      padding: 2px;
      .el-checkbox__label {
        display: inline-block;
        max-width: 126px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        vertical-align: middle;
      }
    }
  }
  .dropdown-select-bottom {
    margin: 0 -12px -12px -12px;
    border-top: solid 1px #ebeef5;
    padding: 8px 12px;
  }
}
</style>
<script>
export default {
  props: {
    value: {
      type: String,
      default: null
    },
    data: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      filterKey: '',
      searchKey: null,
      selectedList: []
    }
  },
  computed: {
    filterData: function() {
      if (this.filterKey) {
        return this.data.filter(item => item.label.indexOf(this.filterKey) > -1)
      }
      return this.data
    }
  },
  created() {
    if (this.value) {
      this.selectedList = this.value.split(',')
    }
  },
  methods: {
    'confirm': function() {
      this.$emit('confirm', this.selectedList)
    },
    'cancel': function() {
      this.$emit('cancel')
    }
  }
}
</script>
