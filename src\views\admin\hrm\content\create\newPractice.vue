<template>
  <div v-loading="loading" class="_paper_container card-bg">
    <div class="_paper_header">
      <div>
        <el-button type="primary" @click="addQuestion">添加</el-button>
        <el-button :disabled="!checkQuestionList.length" type="primary" @click="setScoreFn">设置分数</el-button>
        <span class="ml-15" style="margin-right: -5px;">答题时长：</span>
        <el-input
          v-model.trim="parentVm.answerTime"
          :min="0"
          style="width: 105px"
          class="inputNumber"
          size="mini"
          type="number"
          placeholder="请输入答题时间"
          oninput="value = value.replace(/[^0-9.]/g, '')"
          @change="changeAnswerTime"
        />
        <span class="mr-10">分钟</span>
        <span class="ml-10 mr-5">是否是考试</span>
        <el-switch v-model="parentVm.isExamMode" :active-value="1" :inactive-value="0" @change="changeMode"/>
      </div>
      <div v-if="checkQuestionList.length" class="_paper_search">
        <div class="_paper_search_1">
          总题数
          <span style="font-weight: bold; color: var(--color-600); font-size: 18px; margin-right: 10px;">
            {{ getQuestionNum }}
          </span>
        </div>
        <div class="_paper_search_1">
          题目总分数
          <span style="font-weight: bold; color: var(--color-600); font-size: 18px;">
            {{ getScore }}
          </span>
        </div>
      </div>
    </div>
    <!-- 静态试卷/题目 -->
    <el-tabs v-model="parentVm.practiceActiveName">
      <el-tab-pane v-if="singleQuestionList.length" :label="`单选题（${ singleQuestionList.length }）`" name="single">
        <div v-if="singleQuestionList.length" class="_question_list">
          <div
            v-for="(q, index) in singleQuestionList"
            :class="`_question_item`"
            :key="q.id"
          >
            <div>
              <div class="_question_item_content">{{ index + 1 }}.&nbsp;<span v-html="q.content"/></div>
              <div class="_question_item_type">
                <el-tag size="small">{{ questionConf.questionTypObj[q.questionType].label }}</el-tag>
              </div>
            </div>
            <div v-if="q.questionType == 1" class="_question_option">
              <el-radio-group
                v-for="(op, i) in JSON.parse(q.questionOptions)"
                :key="op"
                :value="q.questionAnswer"
              >
                <el-radio :label="optionLabel[i]" disabled>{{ op }}</el-radio>
              </el-radio-group>
            </div>
            <div class="_question_score">
              <div>
                题目分数：<el-input-number v-model.trim="q.questionScore" :step="1" :min="0" :max="9999" :controls="false" step-strictly size="mini"/> 分
              </div>
              <div class="_question_delete" @click="deleteQuestion(q,singleQuestionList, 'single')">
                <i class="el-icon-delete"/>
              </div>
            </div>
          </div>
        </div>
        <el-empty
          v-else
          :image="img"
          :image-size="110"
          style="margin: 100px auto"
          description="暂无数据"
        />
      </el-tab-pane>
      <el-tab-pane v-if="multiQuestionList.length" :label="`多选题（${ multiQuestionList.length }）`" name="multi">
        <div v-if="multiQuestionList.length" class="_question_list">
          <div
            v-for="(q, index) in multiQuestionList"
            :class="`_question_item`"
            :key="q.id"
          >
            <div>
              <div class="_question_item_content">{{ index + 1 }}.&nbsp;<span v-html="q.content"/></div>
              <div class="_question_item_type">
                <el-tag size="small">{{ questionConf.questionTypObj[q.questionType].label }}</el-tag>
              </div>
            </div>
            <div v-if="q.questionType == 2" class="_question_option">
              <el-checkbox-group :value="q.questionAnswer.split('')">
                <el-checkbox
                  v-for="(op, i) in JSON.parse(q.questionOptions)"
                  :key="op"
                  :label="optionLabel[i]" disabled
                >{{ op }}</el-checkbox
                >
              </el-checkbox-group>
            </div>
            <div class="_question_score">
              <div>
                题目分数：<el-input-number v-model.trim="q.questionScore" :step="1" :min="0" :max="9999" :controls="false" step-strictly size="mini"/> 分
              </div>
              <div class="_question_delete" @click="deleteQuestion(q,multiQuestionList, 'multi')">
                <i class="el-icon-delete"/>
              </div>
            </div>
          </div>
        </div>
        <el-empty
          v-else
          :image="img"
          :image-size="110"
          style="margin: 100px auto"
          description="暂无数据"
        />
      </el-tab-pane>
      <el-tab-pane v-if="judgeQuestionList.length" :label="`判断题（${ judgeQuestionList.length }）`" name="judge">
        <!-- 题目列表 -->
        <div v-if="judgeQuestionList.length" class="_question_list">
          <div
            v-for="(q, index) in judgeQuestionList"
            :class="`_question_item`"
            :key="q.id"
          >
            <div>
              <div class="_question_item_content">{{ index + 1 }}.&nbsp;<span v-html="q.content"/></div>
              <div class="_question_item_type">
                <el-tag size="small">{{ questionConf.questionTypObj[q.questionType].label }}</el-tag>
              </div>
            </div>
            <div v-if="q.questionType == 3" class="_question_option">
              <el-radio-group
                :value="q.questionAnswer"
              >
                <el-radio label="A" disabled>正确</el-radio>
                <el-radio label="B" disabled>错误</el-radio>
              </el-radio-group>
            </div>
            <div class="_question_score">
              <div>
                题目分数：<el-input-number v-model.trim="q.questionScore" :step="1" :min="0" :max="9999" :controls="false" step-strictly size="mini"/> 分
              </div>
              <div class="_question_delete" @click="deleteQuestion(q,judgeQuestionList, 'judge')">
                <i class="el-icon-delete"/>
              </div>
            </div>
          </div>
        </div>
        <el-empty
          v-else
          :image="img"
          :image-size="110"
          style="margin: 100px auto"
          description="暂无数据"
        />
      </el-tab-pane>
      <el-tab-pane v-if="ctfQuestionList.length" :label="`CTF题（${ ctfQuestionList.length }）`" name="CTF">
        <div v-if="ctfQuestionList.length" class="_question_list">
          <div
            v-for="(q, index) in ctfQuestionList"
            :class="`_question_item`"
            :key="q.id"
          >
            <div>
              <div class="_question_item_content">{{ index + 1 }}.&nbsp;<span v-html="q.content"/></div>
              <div class="_question_item_type">
                <el-tag size="small">{{ questionConf.questionTypObj[q.questionType].label }}</el-tag>
              </div>
            </div>
            <div class="_question_option">
              <span style="padding-bottom: 10px">{{
                q.questionAnswer
              }}</span>
            </div>
            <div class="_question_score">
              <div>
                题目分数：<el-input-number v-model.trim="q.questionScore" :step="1" :min="0" :max="9999" :controls="false" step-strictly size="mini"/> 分
              </div>
              <div class="_question_delete" @click="deleteQuestion(q,ctfQuestionList, 'CTF')">
                <i class="el-icon-delete"/>
              </div>
            </div>
          </div>
        </div>
        <el-empty
          v-else
          :image="img"
          :image-size="110"
          style="margin: 100px auto"
          description="暂无数据"
        />
      </el-tab-pane>
      <el-tab-pane v-if="awdQuestionList.length" :label="`AWD题（${ awdQuestionList.length }）`" name="AWD">
        <div v-if="awdQuestionList.length" class="_question_list">
          <div
            v-for="(q, index) in awdQuestionList"
            :class="`_question_item`"
            :key="q.id"
          >
            <div>
              <div class="_question_item_content">{{ index + 1 }}.&nbsp;<span v-html="q.content"/></div>
              <div class="_question_item_type">
                <el-tag size="small">{{ questionConf.questionTypObj[q.questionType].label }}</el-tag>
              </div>
            </div>
            <div class="_question_option">
              <span style="padding-bottom: 10px">{{
                q.questionAnswer
              }}</span>
            </div>
            <div class="_question_score">
              <div>
                题目分数：<el-input-number v-model.trim="q.questionScore" :step="1" :min="0" :max="9999" :controls="false" step-strictly size="mini"/> 分
              </div>
              <div class="_question_delete" @click="deleteQuestion(q,awdQuestionList, 'AWD')">
                <i class="el-icon-delete"/>
              </div>
            </div>
          </div>
        </div>
        <el-empty
          v-else
          :image="img"
          :image-size="110"
          style="margin: 100px auto"
          description="暂无数据"
        />
      </el-tab-pane>
      <el-tab-pane v-if="loopholeQuestionList.length" :label="`漏洞题（${ loopholeQuestionList.length }）`" name="loophole">
        <div v-if="loopholeQuestionList.length" class="_question_list">
          <div
            v-for="(q, index) in loopholeQuestionList"
            :class="`_question_item`"
            :key="q.id"
          >
            <div>
              <div class="_question_item_content">{{ index + 1 }}.&nbsp;<span v-html="q.content"/></div>
              <div class="_question_item_type">
                <el-tag size="small">{{ questionConf.questionTypObj[q.questionType].label }}</el-tag>
              </div>
            </div>
            <div class="_question_option">
              <span style="padding-bottom: 10px">{{
                q.questionAnswer
              }}</span>
            </div>
            <div class="_question_score">
              <div>
                题目分数：<el-input-number v-model.trim="q.questionScore" :step="1" :min="0" :max="9999" :controls="false" step-strictly size="mini"/> 分
              </div>
              <div class="_question_delete" @click="deleteQuestion(q,loopholeQuestionList, 'loophole')">
                <i class="el-icon-delete"/>
              </div>
            </div>
          </div>
        </div>
        <el-empty
          v-else
          :image="img"
          :image-size="110"
          style="margin: 100px auto"
          description="暂无数据"
        />
      </el-tab-pane>
      <el-tab-pane v-if="fillBlankQuestionList.length" :label="`填空题（${ fillBlankQuestionList.length }）`" name="fillBlank">
        <div v-if="fillBlankQuestionList.length" class="_question_list">
          <div
            v-for="(q, index) in fillBlankQuestionList"
            :class="`_question_item`"
            :key="q.id"
          >
            <div>
              <div class="_question_item_content">{{ index + 1 }}.&nbsp;<span v-html="q.content"/></div>
              <div class="_question_item_type">
                <el-tag size="small">{{ questionConf.questionTypObj[q.questionType].label }}</el-tag>
              </div>
            </div>
            <div class="_question_option">
              <span style="padding-bottom: 10px">{{
                q.questionAnswer
              }}</span>
            </div>
            <div class="_question_score">
              <div>
                题目分数：<el-input-number v-model.trim="q.questionScore" :step="1" :min="0" :max="9999" :controls="false" step-strictly size="mini"/> 分
              </div>
              <div class="_question_delete" @click="deleteQuestion(q,fillBlankQuestionList, 'fillBlank')">
                <i class="el-icon-delete"/>
              </div>
            </div>
          </div>
        </div>
        <el-empty
          v-else
          :image="img"
          :image-size="110"
          style="margin: 100px auto"
          description="暂无数据"
        />
      </el-tab-pane>
      <el-tab-pane v-if="otherQuestionList.length" :label="`其他题（${ otherQuestionList.length }）`" name="other">
        <div v-if="otherQuestionList.length" class="_question_list">
          <div
            v-for="(q, index) in otherQuestionList"
            :class="`_question_item`"
            :key="q.id"
          >
            <div>
              <div class="_question_item_content">{{ index + 1 }}.&nbsp;<span v-html="q.content"/></div>
              <div class="_question_item_type">
                <el-tag size="small">{{ questionConf.questionTypObj[q.questionType].label }}</el-tag>
              </div>
            </div>
            <div class="_question_option">
              <span style="padding-bottom: 10px">{{
                q.questionAnswer
              }}</span>
            </div>
            <div class="_question_score">
              <div>
                题目分数：<el-input-number v-model.trim="q.questionScore" :step="1" :min="0" :max="9999" :controls="false" step-strictly size="mini"/> 分
              </div>
              <div class="_question_delete" @click="deleteQuestion(q,otherQuestionList, 'other')">
                <i class="el-icon-delete"/>
              </div>
            </div>
          </div>
        </div>
        <el-empty
          v-else
          :image="img"
          :image-size="110"
          style="margin: 100px auto"
          description="暂无数据"
        />
      </el-tab-pane>
      <el-empty
        v-if="!checkQuestionList.length"
        :image="img"
        :image-size="110"
        style="margin: 100px auto"
        description="暂无数据"
      />
    </el-tabs>
    <!-- 动态试卷 -->
    <dynamic-exam-paper v-if="parentVm.examType == 1" ref="dynamicPaper" :data.sync="parentVm.dynamicPaperData" />
    <el-drawer
      :title="titleMapping[drawerName]"
      :visible.sync="drawerShow"
      :size="drawerWidth"
      @close="drawerClose"
    >
      <transition name="el-fade-in-linear">
        <component
          :is="drawerName"
          :question-list="checkQuestionList"
          :get-question-num="getQuestionNum"
          :get-score="getScore"
          :data="data"
          :name="drawerName"
          :choose-question-info="chooseQuestionInfo"
          @close="drawerClose"
          @call="drawerConfirmCall"
        />
      </transition>
    </el-drawer>
  </div>
</template>

<script>
import { queryPractice } from '@/api/teacher/index.js'
import { debounce } from 'throttle-debounce'
import mixin from './mixin'
import questionConf from './config.js'
import setScore from './setScore'
import chooseQuestion from './chooseQuestion.vue'
import dynamicExamPaper from '@/components/QuestionBank/dynamicPaper/index.vue'
import mixinsActionMenu from '@/packages/mixins/action_menu.js'

const questionListKeyMap = {
  '1': 'singleQuestionList',
  '2': 'multiQuestionList',
  '3': 'judgeQuestionList',
  '4': 'ctfQuestionList',
  '5': 'awdQuestionList',
  '6': 'otherQuestionList',
  '7': 'fillBlankQuestionList',
  '8': 'shortAnswerQuestionList',
  '9': 'loopholeQuestionList'
}

export default {
  components: {
    setScore,
    chooseQuestion,
    dynamicExamPaper
  },
  mixins: [mixin, mixinsActionMenu],
  inject: ['parentVm'],
  props: {
    selectQuestionList: {
      type: Array,
      default: () => {
        return []
      }
    },
    data: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      questionConf: questionConf,
      titleMapping: {
        'setScore': '设置分数',
        'chooseQuestion': '选择题目'
      },
      optionLabel: Array.from({ length: 26 }, (_, index) => String.fromCharCode('A'.charCodeAt(0) + index)),
      checkQuestionList: this.selectQuestionList,
      questionType: '',
      bankType: '',
      questionList: [],
      singleQuestionList: [],
      multiQuestionList: [],
      judgeQuestionList: [],
      ctfQuestionList: [],
      awdQuestionList: [],
      otherQuestionList: [],
      fillBlankQuestionList: [],
      shortAnswerQuestionList: [],
      loopholeQuestionList: [],
      practicePaper: true,
      bool: false,
      chooseQuestionInfo: {}
    }
  },
  computed: {
    questionAllList() {
      const arr = [
        ...this.singleQuestionList,
        ...this.multiQuestionList,
        ...this.judgeQuestionList,
        ...this.ctfQuestionList,
        ...this.awdQuestionList,
        ...this.otherQuestionList,
        ...this.fillBlankQuestionList,
        ...this.shortAnswerQuestionList,
        ...this.loopholeQuestionList
      ]
      return arr
    },
    questionTypes() {
      return [
        this.singleQuestionList.length > 0 ? 'single' : null,
        this.multiQuestionList.length > 0 ? 'multi' : null,
        this.judgeQuestionList.length > 0 ? 'judge' : null,
        this.ctfQuestionList.length > 0 ? 'CTF' : null,
        this.awdQuestionList.length > 0 ? 'AWD' : null,
        this.loopholeQuestionList.length > 0 ? 'loophole' : null,
        this.fillBlankQuestionList.length > 0 ? 'fillBlank' : null,
        this.otherQuestionList.length > 0 ? 'other' : null
      ]
    },
    numberedQuestionTypes() {
      return this.questionTypes.filter(type => type !== null).map((type, index) => ({ index, type }))
    },
    getQuestionNum() {
      return this.singleQuestionList.length +
      this.multiQuestionList.length +
      this.judgeQuestionList.length +
      this.ctfQuestionList.length +
      this.awdQuestionList.length +
      this.otherQuestionList.length +
      this.fillBlankQuestionList.length +
      this.shortAnswerQuestionList.length +
      this.loopholeQuestionList.length
    },
    getScore() {
      let score = 0
      this.singleQuestionList.forEach(item => {
        if (item.questionScore) {
          score = score + item.questionScore
        }
      })
      this.multiQuestionList.forEach(item => {
        if (item.questionScore) {
          score = score + item.questionScore
        }
      })
      this.judgeQuestionList.forEach(item => {
        if (item.questionScore) {
          score = score + item.questionScore
        }
      })
      this.ctfQuestionList.forEach(item => {
        if (item.questionScore) {
          score = score + item.questionScore
        }
      })
      this.awdQuestionList.forEach(item => {
        if (item.questionScore) {
          score = score + item.questionScore
        }
      })
      this.otherQuestionList.forEach(item => {
        if (item.questionScore) {
          score = score + item.questionScore
        }
      })
      this.fillBlankQuestionList.forEach(item => {
        if (item.questionScore) {
          score = score + item.questionScore
        }
      })
      this.shortAnswerQuestionList.forEach(item => {
        if (item.questionScore) {
          score = score + item.questionScore
        }
      })
      this.loopholeQuestionList.forEach(item => {
        if (item.questionScore) {
          score = score + item.questionScore
        }
      })
      return score
    }
  },
  watch: {
    numberedQuestionTypes: {
      handler(newVal) {
        if (newVal) {
          if (!this.parentVm.practiceDeleteClick) {
            this.parentVm.practiceActiveName = newVal[0].type
          }
        }
      },
      deep: true
    },
    checkQuestionList: {
      handler(newVal) {
        this.$emit('call', 'check-question-list-change', newVal)
      },
      deep: true
    }
  },
  created() {
    // 题目/静态试卷题目回显
    if (this.checkQuestionList && this.checkQuestionList.length) {
      this.checkQuestionList.forEach((item) => {
        this[questionListKeyMap[item.questionType]].push(item)
      })
    }
  },
  methods: {
    changeMode: function(flag) {
      this.parentVm.isExamMode = flag
    },
    confirmScore: function() {
      let str = ''
      this.bool = false
      if (this.judgeQuestionList.length) {
        this.judgeQuestionList.forEach((item, index) => {
          if (!item.questionScore && !this.bool) {
            this.bool = true
            str = `您的判断题的第${index + 1}道题还未配置分数`
            return
          }
        })
      }
      if (this.singleQuestionList.length) {
        this.singleQuestionList.forEach((item, index) => {
          if (!item.questionScore && !this.bool) {
            this.bool = true
            str = `您的单选题的第${index + 1}道题还未配置分数`
            return
          }
        })
      }
      if (this.multiQuestionList.length) {
        this.multiQuestionList.forEach((item, index) => {
          if (!item.questionScore && !this.bool) {
            this.bool = true
            str = `您的多选题的第${index + 1}道题还未配置分数`
            return
          }
        })
      }
      if (this.ctfQuestionList.length) {
        this.ctfQuestionList.forEach((item, index) => {
          if (!item.questionScore && !this.bool) {
            this.bool = true
            str = `您的CTF题的第${index + 1}道题还未配置分数`
            return
          }
        })
      }
      if (this.awdQuestionList.length) {
        this.awdQuestionList.forEach((item, index) => {
          if (!item.questionScore && !this.bool) {
            this.bool = true
            str = `您的AWD题的第${index + 1}道题还未配置分数`
            return
          }
        })
      }
      if (this.loopholeQuestionList.length) {
        this.loopholeQuestionList.forEach((item, index) => {
          if (!item.questionScore && !this.bool) {
            this.bool = true
            str = `您的漏洞题的第${index + 1}道题还未配置分数`
            return
          }
        })
      }
      if (this.fillBlankQuestionList.length) {
        this.fillBlankQuestionList.forEach((item, index) => {
          if (!item.questionScore && !this.bool) {
            this.bool = true
            str = `您的填空题题的第${index + 1}道题还未配置分数`
            return
          }
        })
      }
      if (this.bool) {
        this.$message.error(str)
      } else {
        // const question = this.checkQuestionList.map((item) => {
        //   return {
        //     questionCode: item.questionCode,
        //     questionScore: item.questionScore
        //   }
        // })
        // const params = {
        //   contentId: this.contentId,
        //   question: question
        // }
        // practiceUpdateScoreAPI(params).then(res => {
        //   if (res.code == 0 && this.checkQuestionList.length > 0) {
        //     this.$message.success(`设置分数成功`)
        //   }
        // })
      }
    },
    setScoreFn() {
      this.drawerName = 'setScore'
      this.drawerWidth = '720px'
    },
    addQuestion() {
      this.drawerName = 'chooseQuestion'
      this.drawerWidth = '820px'
      const { questionSource, categoryList } = (this.parentVm.checkedQuestionData && Object.keys(this.parentVm.checkedQuestionData).length > 0) ? this.parentVm.checkedQuestionData : this.$route.query
      this.chooseQuestionInfo = {
        ...this.$route.query,
        curriculumCode: this.$route.query.curriculumCode,
        evaluationCode: this.evaluationCode,
        id: this.contentId,
        active: this.parentVm.active,
        contentType: this.parentVm.contentType,
        checkQuestionList: this.questionAllList,
        questionSource: questionSource || this.parentVm['questionSource'],
        categoryList: categoryList || this.parentVm['categoryList']
      }
    },
    searchCurriculum: debounce(500, false, function() {
      this.loading = true
      // 这里放置需要防抖的操作
      let bankType
      let bankTypes
      if (this.$route.query.contentType == 1) {
        bankType = 1
      } else if (this.$route.query.contentType == 2) {
        bankTypes = [1, 2, 3]
      }
      const questionList = [
        ...this.singleQuestionList,
        ...this.multiQuestionList,
        ...this.judgeQuestionList,
        ...this.ctfQuestionList,
        ...this.awdQuestionList,
        ...this.otherQuestionList,
        ...this.fillBlankQuestionList,
        ...this.shortAnswerQuestionList,
        ...this.loopholeQuestionList
      ]
      queryPractice({ contentId: this.contentId, bankType, bankTypes }).then(res => {
        if (res.code == 0) {
          this.checkQuestionList = this.questionList = (res.data || []).map(item => {
            this[questionListKeyMap[item.questionType]].push(item)
            const data = questionList.find(q => q.id === item.id)
            if (data) {
            // 优先回显已填写的分数
              item.questionScore = data.questionScore || item.questionScore
            }
            return {
              ...item
            }
          })
        }
      }).finally(() => {
        this.loading = false
      })
    }),
    // 侧拉选择资源回调
    drawerConfirmCall: function(type, data) {
      if (type === 'chooseQuestion') {
        this.checkQuestionList = data.checkQuestionList
        this.$emit('call', 'chooseQuestion', data)
        if (data.questionEchoFlag) {
          this.$emit('call', 'check-question-list-change', this.checkQuestionList)
        }
        // 如果是动态试卷
        if (data && data.examType == 1) {
          this.parentVm.examType = data.examType
          this.parentVm.dynamicExamId = data.dynamicExamId
          this.parentVm.dynamicPaperData = JSON.parse(JSON.stringify(data.dynamicPaperData))
        }
      } if (type === 'close') {
        this.drawerClose()
      } else if (type === 'setScore') {
        if (this.checkQuestionList.length) {
          this.checkQuestionList.forEach(item => {
            if (item.questionType == '1') {
              if (data.theoryVO.singleTypeNum) {
                item.questionScore = data.theoryVO.singleTypeNum
              }
            }
            if (item.questionType == '2') {
              if (data.theoryVO.manyTypeNum) {
                item.questionScore = data.theoryVO.manyTypeNum
              }
            }
            if (item.questionType == '3') {
              if (data.theoryVO.judgeTypeNum) {
                item.questionScore = data.theoryVO.judgeTypeNum
              }
            }
            if (item.questionType == '4') {
              if (data.theoryVO.ctfTypeNum) {
                item.questionScore = data.theoryVO.ctfTypeNum
              }
            }
            if (item.questionType == '5') {
              if (data.theoryVO.awdTypeNum) {
                item.questionScore = data.theoryVO.awdTypeNum
              }
            }
            if (item.questionType == '7') {
              if (data.theoryVO.completionNum) {
                item.questionScore = data.theoryVO.completionNum
              }
            }
            if (item.questionType == '9') {
              if (data.theoryVO.bugTypeNum) {
                item.questionScore = data.theoryVO.bugTypeNum
              }
            }
            if (item.questionType == '6') {
              if (data.theoryVO.otherTypeNum) {
                item.questionScore = data.theoryVO.otherTypeNum
              }
            }
          })
        }
      }
    },
    // 动态获取已选中题目
    getCheckQuestionList() {
      const arr = [
        ...this.singleQuestionList,
        ...this.multiQuestionList,
        ...this.judgeQuestionList,
        ...this.ctfQuestionList,
        ...this.awdQuestionList,
        ...this.otherQuestionList,
        ...this.fillBlankQuestionList,
        ...this.shortAnswerQuestionList,
        ...this.loopholeQuestionList
      ]
      return arr
    },
    deleteQuestion(q, arr, activeName) {
      const idx = this.checkQuestionList.findIndex(item => item.questionCode == q.questionCode)
      if (idx > -1) {
        this.checkQuestionList.splice(idx, 1)
      }
      const index = arr.findIndex(item => item.questionCode == q.questionCode)
      arr.splice(index, 1)
      if (arr.length === 0) {
        this.parentVm.practiceDeleteClick = false
      } else {
        this.parentVm.practiceDeleteClick = true
        this.parentVm.practiceActiveName = activeName
      }
    },
    changeAnswerTime(val) {
      if (val < 0 || val > 14400) {
        this.$message.error(`请输入0-14400之间的整数`)
        this.parentVm.answerTime = 0
        return
      }
      this.parentVm.answerTime = val
      this.parentVm.answerLimitSecond = Number(val) * 60
    }
  }
}
</script>

<style lang="scss" scoped>
._paper_container {
  display: flex;
  flex-direction: column;
  height: 100%;
  ._paper_header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    ._paper_search {
      display: flex;
      align-items: center;
      ._paper_search_1 {
        font-size: 16px;
        font-weight: 500;
        color: #333;
      }
    }
  }
  /deep/ .el-tabs {
    min-height: 0;
    flex: 1;
    display: flex;
    flex-direction: column;
    .el-tabs__content {
      overflow-y: auto;
    }
  }
  ._question_list {
    ._question_item {
      padding: 15px 20px;
      min-height: 90px;
      background: #ffffff;
      display: flex;
      flex-direction: column;
      font-size: 14px;
      color: #4e5969;
      position: relative;
      border: 1px solid #e5e6eb;
      border-radius: 4px;
      margin-bottom: 10px;
      ._question_option {
        margin-top: 10px;
        margin-left: 15px;
        font-size: 14px;
        color: #4e5969;
        display: flex;
        flex-direction: column;
        line-height: 22px;
        word-break: break-all;
        ::v-deep .el-radio {
          margin-bottom: 8px;
          display: flex;
          align-items: flex-start;
          .el-radio__label {
            font-size: 14px;
            color: #4e5969;
            white-space: normal;
            word-break: break-all;
          }
        }
        ::v-deep .el-checkbox {
          margin-bottom: 8px;
          display: flex;
          align-items: center;
          .el-checkbox__label {
            font-size: 14px;
            color: #4e5969;
            white-space: normal;
            word-break: break-all;
          }
        }
      }
      ._question_score {
        border-top: 1px solid #e5e6eb;
        padding: 10px 0 0 0 ;
        display: flex;
        align-items: center;
        justify-content: space-between;
        ._question_delete {
          color: #F56C6C;
          cursor: pointer;
        }
      }
      ._question_item_content {
        display: flex;
        // max-height: 200px;
        // overflow-y: auto;
        overflow-x: auto;
        margin-right: 55px;
      }
      ._question_item_type {
        position: absolute;
        right: 15px;
        top: 15px;
      }
      .combination-question-wrap {
        >div {
          border: 1px solid rgb(229, 230, 235);
          margin: 5px 0px 10px;
          padding: 15px 20px 5px;
          .comp-question {
            display: flex;
            // max-height: 200px;
            // overflow-y: auto;
            overflow-x: auto;
            >span {
              flex: 1;
              word-break: break-all;
            }
          }
          .comp-content-wrap {
            border: 1px solid #e5e6eb;
            margin: 5px 0 10px;
            padding: 15px 20px;
            >div:first-child {
              display: flex;
              // max-height: 200px;
              // overflow-y: auto;
              overflow-x: auto;
              margin-bottom: 10px;
              >span {
                flex: 1;
                word-break: break-all;
              }
            }
          }
        }
      }
      .combination-question-delete {
        color: #F56C6C;
        display: flex;
        justify-content: end;
        >div {
          cursor: pointer;
          width: 42px;
        }
      }
    }

    img {
      position: absolute;
      left: 20px;
      top: 23px;
    }

    ._question_item_check {
      border: 1px solid var(--color-600);
    }
  }
}
.card-bg {
  border-radius: 4px;
  padding: 15px;
  background-color: #FFFFFF;
}
::v-deep .el-date-editor .el-input__inner {
  padding: 0 10px 0 30px !important;
}
</style>
<style lang="scss">
.inputNumber {
  input[type="number"]::-webkit-inner-spin-button,
  input[type="number"]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }
}
</style>
