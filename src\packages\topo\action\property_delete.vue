<template>
  <div v-loading="loading" class="dialog-wrap">
    <batch-template
      :data="list"
      :available-data="list"
      :show-delete-warning="false"
      view-key="name"
      post-key="nodeId"
    />
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button :disabled="!data.data" type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>

<script>
import batchTemplate from '../../batch-delete/modal-bat-template.vue'
import modalMixins from '../../mixins/modal_form'
import { createSceneRoleResourceRelAPI } from '../api/role'

export default {
  components: {
    batchTemplate
  },
  mixins: [modalMixins],
  props: {
    data: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      loading: false,
      list: [],
      typeObj: {
        'pnf': '物理设备',
        'vnf': '虚拟设备',
        'inf': '图形设备'
      }
    }
  },
  mounted() {
    this.list = this.data.data
  },
  methods: {
    close() {
      this.$emit('close')
    },
    confirm() {
      this.loading = true
      let newNetworkElementList = []
      if (this.data.data.length !== this.data.networkElementList.length) {
        this.data.data.forEach(item => {
          this.data.networkElementList = this.data.networkElementList.filter(network => { return network.nodeId != item.nodeId })
        })
        newNetworkElementList = this.data.networkElementList
      }
      const postData = { topologyTemplateId: this.data.topologyTemplateId, sceneId: this.data.sceneId, sceneRoleId: this.data.roleId, networkElementList: newNetworkElementList }
      createSceneRoleResourceRelAPI(postData).then(res => {
        this.$message.success(`资产解除成功`)
        this.$emit('call', 'sceneRoleProperty')
        this.close()
      }).catch(() => {
        this.loading = false
      })
    }
  }
}
</script>
