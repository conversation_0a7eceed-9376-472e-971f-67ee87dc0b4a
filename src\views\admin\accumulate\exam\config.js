// 难度map
const difficultyArr = [
  { label: '初级', value: '1' },
  { label: '中级', value: '2' },
  { label: '高级', value: '3' }
]
// 试卷类型mapping
const examTypeArr = [
  { label: '静态试卷', value: '0' },
  { label: '动态试卷', value: '1' }
]
const sceneTypeList = [
  { id: 1, value: '实训' },
  { id: 2, value: '考试中心' },
  { id: 3, value: '攻防演练' },
  { id: 4, value: '理论赛' },
  { id: 5, value: 'CTF赛' },
  { id: 6, value: 'AWD赛' },
  { id: 8, value: '实景攻防赛' },
  { id: 7, value: '其他' }
]
export default {
  name: 'exam',
  difficultyArr: difficultyArr,
  examTypeArr: examTypeArr,
  sceneTypeList: sceneTypeList,
  // 将难度map数组转换为对象
  difficultyObj: difficultyArr.reduce((acc, prev) => {
    acc[prev.value] = prev
    return acc
  }, {}),
  // 将试卷类型map数组转换为对象
  examTypeObj: examTypeArr.reduce((acc, prev) => {
    acc[prev.value] = prev
    return acc
  }, {}),
  sceneTypeObj: sceneTypeList.reduce((acc, prev) => {
    acc[prev.id] = prev
    return acc
  }, {})
}
