<template>
  <div v-loading="uploading" :class="{'focused': focused}" :style="styles" element-loading-text="文件上传中...">
    <!-- 工具栏 -->
    <component
      v-if="!onlyEditor"
      :is="hyToolbar"
      :mode="mode"
      :editor="cdnEditor"
      :default-config="toolbarConfig"
      :id="`${idPrefix}-toolbar-container`"
      style="border-bottom: 1px solid #DCDFE6;"
    />
    <!-- 编辑器 -->
    <component
      :is="hyEditor"
      v-model="wangEditorContent"
      :style="{
        height: height
      }"
      :default-config="propsEditorConfig"
      :mode="mode"
      :id="`${idPrefix}-editor-container`"
      @onCreated="onCreated"
      @onChange="onChange"
      @onFocus="focused = true"
      @onBlur="focused = false"
    />
    <!-- @click="showImg($event)" -->

    <!-- 富文本图片放大 -->
    <div v-show="imgPreview.show" class="imgDolg">
      <i
        id="imgDolgClose"
        class="el-icon-close"
        @click.stop="imgPreview.show = false"
      />
      <div class="img-container">
        <el-image
          ref="imgDolg"
          :src="imgPreview.img"
          class="img"
          fit="cover"
        />
      </div>
    </div>
  </div>
</template>
<script>
import Axios from 'axios'
import Vue from 'vue'
export default Vue.extend({
  props: {
    idPrefix: { // id前缀，一个界面中使用多个editor时传递
      type: String,
      default: ''
    },
    // 是否仅展示编辑区域
    onlyEditor: {
      type: Boolean,
      default: false
    },
    // 是否仅展示边框
    isEditorBorder: {
      type: Boolean,
      default: true
    },
    height: {
      type: String,
      default: '400px'
    },
    width: {
      type: String,
      default: '800px'
    },
    // 内容
    content: {
      type: String,
      default: '<p><br></p>'
    },
    // 编辑器配置
    editorConfig: {
      type: Object,
      default: () => {}
    },
    // 是否只是已读
    isReadOnly: {
      type: Boolean,
      default: false
    },
    mode: {
      type: String,
      default: 'default' // or 'simple'
    },
    // 图片类型限制
    imageTypes: {
      type: Array,
      default: () => ['jpg', 'jpeg', 'png', 'gif']
    },
    // 上传图片的地址
    uploadUrl: {
      type: String,
      default: ''
    },
    uploadImgApi: {
      type: String,
      default: 'scene/sceneFile/upload'
    },
    // 上传图片的请求头
    headers: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      focused: false,
      propsEditorConfig: {},
      defaultEditorConfig: {
        pasteFilterStyle: false,
        placeholder: '请输入内容...',
        readOnly: false,
        autoFocus: true,
        scroll: true,
        MENU_CONF: {},
        metaWithUrl: false
      },
      hyEditor: null, // 工具栏
      hyToolbar: null, // 编辑组件
      cdnToolbar: null,
      cdnEditor: null,
      uploading: false, // 文件上传中状态
      toolbarConfig: {
        toolbarKeys: [
          'headerSelect',
          'fontSize',
          'fontFamily',
          'lineHeight',
          '|',
          'color',
          'bgColor',
          'bold',
          'italic',
          'underline',
          '|',
          'justifyLeft',
          'justifyCenter',
          'justifyRight',
          'bulletedList',
          'numberedList',
          'todo',
          '|',
          {
            'key': 'group-image',
            'title': '图片',
            'iconSvg': '<svg viewBox=\'0 0 1024 1024\'><path d=\'M959.877 128l0.123 0.123v767.775l-0.123 0.122H64.102l-0.122-0.122V128.123l0.122-0.123h895.775zM960 64H64C28.795 64 0 92.795 0 128v768c0 35.205 28.795 64 64 64h896c35.205 0 64-28.795 64-64V128c0-35.205-28.795-64-64-64zM832 288.01c0 53.023-42.988 96.01-96.01 96.01s-96.01-42.987-96.01-96.01S682.967 192 735.99 192 832 234.988 832 288.01zM896 832H128V704l224.01-384 256 320h64l224.01-192z\'></path></svg>',
            'menuKeys': [
              'uploadImage'
            ]
          },
          'codeBlock',
          {
            'key': 'group-more-style',
            'title': '更多',
            'iconSvg': '<svg viewBox=\'0 0 1024 1024\'><path d=\'M204.8 505.6m-76.8 0a76.8 76.8 0 1 0 153.6 0 76.8 76.8 0 1 0-153.6 0Z\'></path><path d=\'M505.6 505.6m-76.8 0a76.8 76.8 0 1 0 153.6 0 76.8 76.8 0 1 0-153.6 0Z\'></path><path d=\'M806.4 505.6m-76.8 0a76.8 76.8 0 1 0 153.6 0 76.8 76.8 0 1 0-153.6 0Z\'></path></svg>',
            'menuKeys': [
              'code',
              'through',
              'divider',
              'sup',
              'sub',
              'indent',
              'delIndent',
              'clearStyle'
            ]
          },
          '|',
          'undo',
          'redo',
          'fullScreen'
        ]
      },
      // 图片放大
      imgPreview: {
        img: '',
        show: false
      }
    }
  },
  computed: {
    wangEditorContent: {
      get() {
        return this.handleFileImg(this.content)
      },
      set(val) {
        return val
      }
    },
    styles: function() {
      const style = {}
      style.width = this.width
      style.height = 'auto'
      style.borderRadius = '2px'
      style.border = this.isEditorBorder ? '1px solid' : 'none'
      style.borderColor = this.focused ? 'var(--color-600)' : '#DCDFE6'
      return style
    }
  },
  mounted() {
    if (this.isReadOnly) {
      this.defaultEditorConfig.placeholder = ''
    }
    // 编辑区配置（默认配置优先,用户配置覆盖）
    this.propsEditorConfig = {
      ...this.defaultEditorConfig,
      ...this.editorConfig
    }
    if (this.$options.propsData.hasOwnProperty('isReadOnly')) {
      this.propsEditorConfig.readOnly = this.isReadOnly
    }
    // 自定义图片上传
    this.propsEditorConfig.MENU_CONF['uploadImage'] = {
      customUpload: (file, insertFn) => this.customImageUpload(file, insertFn)
    }

    this.initCdnEditor()
  },
  beforeDestroy() {
    this.cdnEditor && this.cdnEditor.destroy() // 组件销毁时，及时销毁编辑器
  },
  methods: {
    handleFileImg(content) {
      let haveFileImg = false
      // 将HTML字符串转换为DOM对象
      const parser = new DOMParser()
      const doc = parser.parseFromString(content, 'text/html')
      // 获取所有的img标签
      const imgs = doc.querySelectorAll('img')
      // 遍历所有的img标签
      imgs.forEach(img => {
        // 检查src属性是否以file:开头
        if (img.src.startsWith('file:')) {
          // 移除这个img标签
          img.remove()
          haveFileImg = true
        }
      })
      if (haveFileImg && !this.isReadOnly) {
        this.$message.error(`图片粘贴失败，请单独复制图片，或使用“插入图片”功能`)
      }
      // 将修改后的DOM对象转换回字符串
      const modifiedHtml = doc.body.innerHTML
      return modifiedHtml
    },
    onCreated(editor) {
      this.cdnEditor = Object.seal(editor) // 一定要用 Object.seal() ，否则会报错
    },
    // 内容改变时
    onChange(e) {
      this.$emit('contentChange', e.getHtml())
    },

    initCdnEditor() {
      try {
        const editor = require('@wangeditor/editor-for-vue')
        this.hyEditor = editor.Editor
        this.hyToolbar = editor.Toolbar
      } catch (err) {
        console.log('err', err)
      }
    },
    // 自定义上传图片
    customImageUpload(file, insertFn) {
      const fileType = file.name.substr(file.name.lastIndexOf('.') + 1).toLowerCase()
      if (this.imageTypes.length && !this.imageTypes.includes(fileType)) {
        return this.$message.error(`图片格式支持：${this.imageTypes.join('、')}`)
      }
      this.uploading = true
      const formData = new FormData()
      formData.append('file', file)
      const URL = this.uploadUrl || (window.ADMIN_CONFIG ? window.ADMIN_CONFIG.BASE_API : window.WEB_CONFIG.BASE_API) + this.uploadImgApi // api 的 base_url
      Axios.post(URL, formData, {
        headers: this.headers
      }).then((res) => {
        const result = res.data
        if (result.code == 0) {
          const url = result.data
          insertFn(`${url}`)
        }
      }).finally(() => {
        this.uploading = false
      })
    },
    // 预览富文本图片
    showImg(e) {
      if (e.target.tagName == 'IMG') {
        this.imgPreview.img = e.target.src
        this.imgPreview.show = true
      }
    }
  }
})
</script>
<style src="@wangeditor/editor/dist/css/style.css"></style>
<style>
body .w-e-bar-divider {
  width: 0;
  height: 20px;
  margin: 6px 5px;
  border-left: 1px dotted #c4c4c4;
}
body .w-e-bar-item {
  padding: 0px;
  font-size: 12px;
  height: 32px;
}
body .w-e-bar svg {
  width: 12px;
  height: 12px;
}
body .w-e-bar-item.w-e-bar-item-group button {
  padding-right: 0;
}
body .w-e-bar-item button {
  padding: 0 5px;
  width: auto !important;
}
body .w-e-bar-item button[data-tooltip="标题"] {
  padding-right: 4px;
}
body .w-e-bar-item button[data-tooltip="字号"] {
  padding: 0 4px;
}
body .w-e-bar-item button[data-tooltip="字体"] {
  padding: 0 4px;
}
body .w-e-bar-item button[data-tooltip="行高"] {
  padding: 0 4px;
}
body .w-e-bar-item-group .w-e-bar-item-menus-container {
  margin-top: 32px;
}
body .w-e-bar-item-group .w-e-bar-item-menus-container .w-e-bar-item button {
  padding: 0 10px;
  width: 100% !important;
}
body .w-e-full-screen-container {
  z-index: 999;
}
body .w-e-text-placeholder {
  top: 0px;
}
body .w-e-text-container [data-slate-editor] {
  padding: 8px;
}
body .w-e-text-container [data-slate-editor] p {
  margin: 0;
}
</style>
<style lang="scss" scoped>
//富文本图片放大
.imgDolg {
  width: 100vw;
  height: 100vh;
  position: fixed;
  z-index: 9999;
  background-color: rgba(0, 0, 0, 0.5);
  top: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  #imgDolgClose {
    position: fixed;
    top: 35px;
    cursor: pointer;
    right: 7%;
    font-size: 50px;
    color: white;
  }
  .img-container{
    width: 80%;
    height: 90%;

    .img{
      width: 100%;
      height: 100%;
    }
  }
}
</style>
