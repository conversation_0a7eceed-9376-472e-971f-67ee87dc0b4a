<template>
  <div class="dropdown-select color-dropdown-select">
    <div class="dropdown-select-list">
      <el-checkbox-group v-model="selectedList">
        <ul>
          <li v-for="item in data" :key="item.value">
            <el-checkbox :label="item.value">
              <span :style="{ backgroundColor: item.value, color: item.color}" style="display: block; padding: 1px 6px; border-radius: 3px;">{{ item.label }}</span>
            </el-checkbox>
          </li>
        </ul>
      </el-checkbox-group>
    </div>
    <div class="dropdown-select-bottom">
      <el-button type="primary" size="mini" @click="confirm">确定</el-button>
      <el-button size="mini" type="text" @click="cancel">取消</el-button>
    </div>
  </div>
</template>
<style lang="less">
.dropdown-select.color-dropdown-select {
  width: 125px;
  .dropdown-select-input {
    padding-bottom: 10px;
  }
  .dropdown-select-list {
    padding-bottom: 10px;
    ul {
      max-height: 200px;
      overflow: auto;
      overflow-x: hidden;
    }
    li {
      padding: 2px;
    }
  }
  .dropdown-select-bottom {
    margin: 0 -12px -12px -12px;
    border-top: solid 1px #ebeef5;
    padding: 8px 12px;
  }
}
</style>
<script>
export default {
  props: {
    value: {
      type: String,
      default: null
    },
    data: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      searchKey: null,
      selectedList: []
    }
  },
  created() {
    if (this.value) {
      this.selectedList = this.value.split(',')
    }
  },
  methods: {
    'confirm': function() {
      this.$emit('confirm', this.selectedList)
    },
    'cancel': function() {
      this.$emit('cancel')
    }
  }
}
</script>
