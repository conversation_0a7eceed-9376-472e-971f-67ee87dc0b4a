export default {
  // 虚拟机列表
  virtualColumnsObj: {
    index: {
      title: '序号',
      master: true,
      colWidth: 70
    },
    deviceName: {
      title: '设备名称',
      master: true
    },
    imageName: {
      title: '镜像名称'
    },
    cpu: {
      title: 'CPU (核)'
    },
    memory: {
      title: '内存 (GB)'
    },
    system: {
      title: '系统盘 (GB)'
    },
    data: {
      title: '数据盘 (GB)'
    }
  },
  virtualColumnsViewArr: [
    'index',
    'deviceName',
    'imageName',
    'cpu',
    'memory',
    'system',
    'data'
  ],
  //  设备列表
  deviceColumnsObj: {
    index: {
      title: '序号',
      master: true,
      colWidth: 70
    },
    deviceName: {
      title: '设备名称',
      master: true
    },
    ipAddress: {
      title: 'IP地址/URL'
    },
    port: {
      title: '端口号'
    },
    credentials: {
      title: '用户名/密码'
    },
    notes: {
      title: '备注'
    }
  },
  deviceColumnsViewArr: [
    'index',
    'deviceName',
    'ipAddress',
    'port',
    'credentials',
    'notes'
  ],
  // 附件列表
  fileColumnsObj: {
    index: {
      title: '序号',
      master: true,
      colWidth: 70
    },
    fileName: {
      title: '文件名称',
      master: true
    },
    typeName: {
      title: '关联任务'
    },
    fileSize: {
      title: '文件大小'
    },
    handle: {
      title: '操作'
    }
  },
  fileColumnsViewArr: [
    'index',
    'fileName',
    'typeName',
    'fileSize',
    'handle'
  ]
}
