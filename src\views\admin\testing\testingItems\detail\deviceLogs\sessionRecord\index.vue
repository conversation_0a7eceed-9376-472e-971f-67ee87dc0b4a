<template>
  <page-table
    ref="table"
    :default-selected-arr="defaultSelectedArr"
    :cache-pattern="true"
    :data="data"
    default-selected-key="id"
    @refresh="refresh"
    @link-event="linkEvent"
    @on-select="tabelSelect"
    @on-current="tabelCurrent"
  >
    <action-menu
      slot="action"
      :module-name="moduleName"
      :select-item="selectItem"
      @call="actionHandler"
    />
  </page-table>
</template>

<script>
import moduleConf from './config'
import pageTable from './table/index.vue'
import actionMenu from './action/index.vue'
import { getEnvType } from '@/api/testing/index'
export default {
  // 会话记录
  name: 'SessionRecord',
  components: {
    pageTable,
    actionMenu
  },
  props: {
    data: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      moduleName: moduleConf.name,
      selectItem: [],
      defaultSelectedArr: [],
      projectEnvData: null,
      id: this.$route.params.id || ''
    }
  },
  created() {
    this.getProjectEnvType()
  },
  methods: {
    // 列表点击
    linkEvent: function({ name, row, params }) {
      this.$router.push({ name: name, params: params })
    },
    // 返回已选
    tabelSelect: function(data) {
      this.selectItem = data
    },
    // 返回单选
    tabelCurrent: function(row) {
      this.selectItem = [row]
    },
    // action menu 事件
    actionHandler: function(type, data) {
      switch (type) {
        case 'refresh':
          this.selectItem = []
          this.$refs['table'].getList(false)
      }
    },
    refresh: function() {},
    // 获取项目测试环境
    getProjectEnvType() {
      getEnvType(this.id).then(res => {
        const data = { ...res.data.data }
        this.$set(this, 'projectEnvData', data)
      })
    }
  }
}
</script>
