<template>
  <div class="buttons-wrap">
    <el-button :disabled="singleDisabled || selectItem[0].examStatus === '0' || !isManualScoringPaper" type="primary" @click="currentClickDrop('examReview')">阅卷</el-button>
    <el-button type="primary" @click="currentClickDrop('batchExport')">导入</el-button>
    <el-button type="primary" @click="currentClickDrop('exportAll')">导出</el-button>
    <!-- 中部弹窗 start-->
    <el-dialog
      :title="titleMapping[modalName]"
      :visible.sync="modalShow"
      :width="modalWidth"
      :destroy-on-close="true"
      append-to-body
      @close="modalClose"
    >
      <transition name="el-fade-in-linear">
        <component
          :is="modalName"
          :name="modalName"
          :data="selectItem"
          :search-name="searchName"
          :student-detail-id="studentDetailId"
          @close="modalClose"
          @call="confirmCall"
        />
      </transition>
    </el-dialog>
    <!-- 中部弹窗 end-->
  </div>
</template>
<script>
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
import exportAll from './modal-export'
import detail from './modal-detail'
import batchExport from './batch-export'
import { queryById, getPaperManualScoringApi } from '@/api/exam/index.js'

export default {
  components: {
    batchExport,
    exportAll,
    detail
  },
  mixins: [mixinsActionMenu],
  props: {
    searchName: {
      type: Object
    }
  },
  data() {
    return {
      // 弹窗title映射
      titleMapping: {
        batchExport: '导入',
        'examReview': '阅卷',
        'exportAll': '导出',
        'detail': '考生详情'
      },
      userId: '',
      dialogLoading: false,
      confirmDisabled: false,
      studentDetailId: '',
      markingPapersType: false,
      status: '',
      isManualScoringPaper: false, // 是否有人工判分题
      examId: this.$route.params.id || ''
    }
  },
  mounted() {
    this.userId = this.$store.state.user.userInfo.userId
    this.getPaperManualScoring()
    this.getEaxmById()
    this.$bus.on('studentDetail', (id) => {
      this.studentDetailId = id
      this.clickDrop('detail')
    })
  },
  methods: {
    // 查询试卷中是否有人工判分题
    getPaperManualScoring() {
      getPaperManualScoringApi({ examinationId: this.examId }).then((res) => {
        if ([0, 200].includes(res.code)) {
          this.isManualScoringPaper = res.data
        }
      })
    },
    getEaxmById() {
      const id = Number(this.$route.params.id)
      queryById({ id: id }).then((res) => {
        if (res.code === 0) {
          const { examStatus, markingPapersType } = res.data
          this.status = examStatus
          this.markingPapersType = markingPapersType
        }
      })
    },
    confirmCall: function(type, data) {
      if (type === 'close') {
        this.modalClose()
      } else if (type === 'refresh') {
        this.$emit('call', type)
      } else if (type === 'confirmDisabled') {
        this.confirmDisabled = data
      }
    },
    handleExam(name) {
      // 进入阅卷页面，把老师打的分数清除
      for (let i = 0; i < localStorage.length; i++) {
        if (localStorage.key(i).includes('soreList')) {
          localStorage.removeItem(localStorage.key(i))
        }
      }
      const routeName = this.$route.params.cerType == 2 ? 'examNonCertificateReview' : 'examReview'
      const oneLevelName = this.$route.params.cerType == 2 ? 'nonCertificate' : 'ca-exam'
      this.$router.push({
        name: routeName,
        query: {
          ...this.$route.query,
          userId: this.selectItem[0].userId,
          examCode: this.selectItem[0].examCode,
          evaluationCode: this.selectItem[0].evaluationCode,
          examinationId: this.$route.params.id,
          submitType: '1',

          oneLevelTitle: '考试管理',
          oneLevelName: oneLevelName,
          twoLevelTitle: this.$route.params.name,
          name: this.$route.params.name
        }
      })
    },
    currentClickDrop(name) {
      if (name === 'examReview') {
        this.handleExam(name)
        return
      }
      this.modalName = name
    }
  }
}
</script>
