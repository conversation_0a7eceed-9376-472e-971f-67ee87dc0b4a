const state = {
  'simulationExercises_changeSceneStatus_Socket': null, // 仿真-推送训练状态
  'simulationExercises_changeStageStatus_Socket': null, // 仿真-推送阶段状态
  'simulationExercises_taskRoundStatus_Socket': null, // 仿真-推送任务轮次状态
  'match_changeMatchResProcess_Socket': null, // 比赛资源准备进度
  'match_changeMatchSeasonStatus_Socket': null, // 比赛状态
  'match_changeSceneStatus_Socket': null, // 阶段状态
  'match_instanceQuestion_Socket': null // 题目状态推送
}
const actions = {
  'SOCKET_CR'({ dispatch, commit, rootState }, msg) {
    const data = JSON.parse(msg.data)
    commit('CHANGE_WEBSOCKETINFO_CR', data)
    console.log('打开了哥们!')
  }
}
const mutations = {
  CHANGE_WEBSOCKETINFO_CR(state, data = null) {
    const event = data['EventIdentification']
    const type = data['type']
    if (event && type) {
      state[`${event}_${type}_Socket`] = data.payload
    }
  }
}
export default {
  namespaced: true,
  state,
  actions,
  mutations
}
