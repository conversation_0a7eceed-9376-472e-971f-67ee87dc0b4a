const complexityArr = [
  { value: '1', label: '初级' },
  { value: '2', label: '中级' },
  { value: '3', label: '高级' }
]

const backTypeArr = [
  { value: '1', label: '理论题' },
  { value: '2', label: '靶机题' },
  { value: '3', label: '仿真题' }
]

export const questionTypeMapping = {
  '1': '单选题',
  '2': '多选题',
  '3': '判断题',
  '4': 'CTF题',
  '5': 'AWD题',
  '6': '其他',
  '7': '填空题',
  '8': '简答题',
  '9': '漏洞题',
  '10': '组合题'
}

export const searchKeyList = [
  { key: 'questionName', label: '题目名称', master: true },
  { key: 'questionType', label: '类型', master: true, type: 'radio', valueList: [
    { value: '1', label: '单选题' },
    { value: '2', label: '多选题' },
    { value: '3', label: '判断题' },
    { value: '4', label: 'CTF题' },
    { value: '5', label: 'AWD题' },
    { value: '6', label: '其他' },
    { value: '7', label: '填空题' },
    { value: '8', label: '简答题' },
    { value: '9', label: '漏洞题' },
    { value: '10', label: '组合题' }
  ]
  }
]

export const columnsObj = {
  'questionName': { title: '题目名称', master: true },
  'bankType': { title: '类别' },
  'questionType': { title: '类型' },
  'complexity': { title: '难度' }
}
export const columnsViewArr = Object.keys(columnsObj)

// 难度等级
export const complexityObj = complexityArr.reduce((acc, prev) => {
  acc[prev.value] = prev.label
  return acc
}, {})

// 题目类别
export const backTypeObj = backTypeArr.reduce((acc, prev) => {
  acc[prev.value] = prev.label
  return acc
}, {})

