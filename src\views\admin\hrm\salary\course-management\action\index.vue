<template>
  <div class="buttons-wrap">
    <el-button type="primary" icon="el-icon-plus" @click="clickDropEdit('addcontent')">创建</el-button>
    <!-- 中部弹窗 start-->
    <el-dialog
      :title="titleMapping[modalName]"
      :visible.sync="modalShow"
      :width="modalWidth"
      @close="modalClose"
    >
      <transition name="el-fade-in-linear">
        <component
          :is="modalName"
          :name="modalName"
          :data="editData"
          @close="modalClose"
          @call="confirmCall"
        />
      </transition>
    </el-dialog>
    <!-- 中部弹窗 end-->
  </div>
</template>
<script>
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
import addcontent from './modal-add.vue'
import editcontent from './modal-add.vue'
import editChecked from './modal-checked.vue'
import deleteCourse from './modal-delete.vue'
import modalPublic from './modal-public.vue'
import modalPrivacy from './modal-public.vue'
export default {
  name: 'ActionContent',
  components: {
    addcontent,
    deleteCourse,
    editChecked,
    modalPublic,
    modalPrivacy,
    editcontent
  },
  mixins: [mixinsActionMenu],
  data() {
    return {
      // 弹窗title映射
      titleMapping: {
        'addcontent': '创建',
        'editcontent': '编辑课程',
        'deleteCourse': '删除课程',
        'editChecked': '提示',
        'modalPublic': '设为公开',
        'modalPrivacy': '设为私有'
      },
      dialogLoading: false,
      confirmDisabled: false,
      editData: {}
    }
  },
  mounted() {
    this.$bus.on('courseCardEdit', (data) => {
      this.editData = data
      if (data.selective) {
        this.clickDrop('editChecked')
      } else {
        // 编辑
        const data = JSON.stringify(this.editData)
        this.$router.push({ name: 'myCourseCreate', query: { data }})
      }
    })
    this.$bus.on('courseCardDelete', (data) => {
      this.editData = [data]
      this.clickDrop('deleteCourse')
    })
    this.$bus.on('coursePrivacyEdit', (data) => {
      if (data.coursePrivacy == 1) {
        this.clickDrop('modalPrivacy')
      } else {
        this.clickDrop('modalPublic')
      }
      this.editData = data
    })
  },
  beforeDestroy() {
    this.$bus.off('courseCardEdit')
    this.$bus.off('deleteCourse')
    this.$bus.off('coursePrivacyEdit')
    this.editData = []
  },
  methods: {
    clickDropEdit() {
      this.editData = {}
      // this.clickDrop('addcontent')
      // 创建
      const data = JSON.stringify(this.editData)
      this.$router.push({ name: 'myCourseCreate', query: { data }})
    },
    confirmCall: function(type, data) {
      if (type === 'openEditModal') {
        // this.clickDrop('addContent')
        const data = JSON.stringify(this.editData)
        this.$router.push({ name: 'myCourseCreate', query: { data }})
      } else {
        this.$emit('call', type)
      }
    }
  }
}
</script>
