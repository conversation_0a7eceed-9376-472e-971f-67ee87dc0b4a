import request from '@/utils/request'

/*
*考试列表
*/
export function queryByPage(data) {
  return request({
    url: `/ca-exam/exam/queryByPage`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
* 新增考试
*/
export function insertExam(data) {
  return request({
    url: `/ca-exam/exam/insert`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
* 删除考试
*/
export function deleteById(data) {
  return request({
    url: `/ca-exam/exam/deleteById`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
* 更新考试
*/
export function updateById(data) {
  return request({
    url: `/ca-exam/exam/update`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
* 考试详情
*/
export function queryById(data) {
  return request({
    url: `/ca-exam/exam/queryById`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
* 克隆考试
*/
export function examClone(data) {
  return request({
    url: `/ca-exam/exam/clone`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
* 发布考试
*/
export function published(data) {
  return request({
    url: `/ca-exam/exam/published`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
* 取消发布考试
*/
export function cancelPublished(data) {
  return request({
    url: `/ca-exam/exam/cancelPublished`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
* 开启环境
*/
export function openExamEnv(data) {
  return request({
    url: `/ca-exam/exam/openExamEnv`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
* 关闭环境
*/
export function closeExamEnv(data) {
  return request({
    url: `/ca-exam/exam/closeExamEnv`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
* 学生列表
*/
export function studentList(data) {
  return request({
    url: `/ca-exam/exam/studentList`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
* 学生详情
*/
export function studentDetail(data) {
  return request({
    url: `/ca-exam/exam/studentDetail`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
* 学生详情修改
*/
export function studentUpdate(data) {
  return request({
    url: `/ca-exam/exam/apply/update`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
* 学生导出
*/
export function studentListExport(data) {
  return request({
    url: `/ca-exam/exam/studentListExport`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
* 学生导出
*/
export function queryExamNameList(data) {
  return request({
    url: `/ca-exam/exam/queryExamNameList`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
* 证书
*/
export function queryCaCertificateApi(data) {
  return request({
    url: '/ca-exam/caCertificate/queryList',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
* 创建快照
*/
export function createSnap(data) {
  return request({
    url: `/ca-exam/exam/createSnap`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
* 环境状态
*/
export function envStatus(data) {
  return request({
    url: `/ca-exam/exam/vm/status/${data.examId}`,
    method: 'get',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
* 快照状态
*/
export function snapStatus(data) {
  return request({
    url: `/ca-exam/exam/vm/status/${data.examId}/snapshot`,
    method: 'get',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
* 阅卷
*/
export function queryAnswer(data) {
  return request({
    url: `/ca-exam/exam/summary/marking/papers`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
/**
* 阅卷
*/
export function submitScoreApi(data) {
  return request({
    url: `/ca-exam/exam/summary/submit/paper/score`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 查询试卷中是否有人工判分题
export function getPaperManualScoringApi(data) {
  return request({
    url: '/ca-exam/exam/areThereManualScoring',
    method: 'get',
    params: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
* 试卷分类
*/
export function queryCategoryPage(data) {
  return request({
    url: `/ca-exam/exam/queryCategoryPage`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 非证书类接口
export function queryExamPage(data) {
  return request({
    url: `/ca-exam/exam/queryExamPage`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export function insertExamNon(data) {
  return request({
    url: `/ca-exam/exam/insertExam`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export function queryClassSchedule(data) {
  return request({
    url: `/training/pjtClassSchedule/queryClassSchedule`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 非证书导入
export function downloadExcelExam(data) {
  return request({
    url: `/ca-exam/exam/apply/downloadExamUserApplyExcelForNOCer`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    responseType: 'blob'
  })
}

export function importExamUserApply(data) {
  return request({
    url: `/ca-exam/exam/apply/importExamUserApply`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
export function downErrorExcelExam(data) {
  return request({
    url: `/ca-exam/exam/apply/downErrorExcel`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    responseType: 'blob'
  })
}

// 删除 错误数据表
export function delErrorExcel(data) {
  return request({
    url: 'admin/sysExamQuestion/delErrorExcel',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data
  })
}

export function downloadExamUserApplyExcelForCer(data) {
  return request({
    url: `/ca-exam/exam/apply/downloadExamUserApplyExcelForCer`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    responseType: 'blob'
  })
}
export function downloadExamUserApplyExcel(data) {
  return request({
    url: `/ca-exam/exam/apply/downloadExamUserApplyExcel`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    responseType: 'blob'
  })
}
