<template>
  <div class="buttons-wrap">
    <el-button type="primary" icon="el-icon-plus" @click="clickDrop('addStudent')">创建班级</el-button>
    <el-dropdown trigger="click" placement="bottom-start" @command="clickDrop">
      <el-button type="primary">
        操作 <i class="el-icon-arrow-down el-icon--right"/>
      </el-button>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item :disabled="singleDisabled" command="editStudent">编辑</el-dropdown-item>
        <el-dropdown-item :disabled="multipleDisabled" command="deleteStudent">删除</el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
    <!-- 中部弹窗 start-->
    <el-dialog
      :title="titleMapping[modalName]"
      :visible.sync="modalShow"
      :width="modalWidth"
      @close="modalClose"
    >
      <transition name="el-fade-in-linear">
        <component
          :is="modalName"
          :name="modalName"
          :data="selectItem"
          :is-edit="isEdit"
          :major-code="majorCode"
          :professionl-major-name="professionlMajorName"
          @close="modalClose"
          @call="confirmCall"
        />
      </transition>
    </el-dialog>
    <!-- 中部弹窗 end-->
  </div>
</template>
<script>
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
import deleteStudent from './modal_delete'
import addStudent from './modal_add.vue'
import editStudent from './modal_add.vue'
export default {
  components: {
    deleteStudent,
    addStudent,
    editStudent
  },
  mixins: [mixinsActionMenu],
  props: {
    majorCode: {
      type: [Number, String]
    },
    professionlMajorName: {
      type: [Number, String]
    }
  },
  data() {
    return {
      // 弹窗title映射
      titleMapping: {
        'addStudent': '创建班级',
        'editStudent': '编辑班级',
        'deleteStudent': '删除班级'
      },
      dialogLoading: false,
      confirmDisabled: false,
      isEdit: false
    }
  },
  mounted() {
    this.token = JSON.parse(localStorage.getItem('Admin-Token')).data
  },
  methods: {
    confirmCall: function(type, data) {
      if (type === 'close') {
        this.modalClose()
      } else if (type === 'refresh') {
        this.$emit('call', type)
      } else if (type === 'upload') {
        this.$emit('call', type)
      } else if (type === 'confirmDisabled') {
        this.confirmDisabled = data
      }
    },
    'clickDrop': function(name) {
      if (this.drawerAction && this.drawerAction.includes(name)) {
        this.drawerName = name
      } else {
        console.log('name', name)
        if (name === 'editStudent') {
          this.isEdit = true
        } else {
          this.isEdit = false
        }
        this.modalName = name
      }
    }
  }
}
</script>
