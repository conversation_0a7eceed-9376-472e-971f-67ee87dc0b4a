export default {
  name: 'NonCertificate',
  examStatus: [
    { value: 'BEGIN', label: '进行中' },
    { value: 'PREPARE', label: '未开始' },
    { value: 'END', label: '已结束' }
  ],
  examLevel: [
    { value: 1, label: '初级' },
    { value: 2, label: '中级' },
    { value: 3, label: '高级' }
  ],
  publicStatus: [
    { value: '1', label: '发布' },
    { value: '0', label: '未发布' }
  ],
  columnsObj: {
    'name': {
      title: '考试名称', master: true
    },
    // 'certificateName': {
    //   title: '证书名称'
    // },
    // 'certificateLevel': {
    //   title: '证书等级'
    // },
    'examStatus': {
      title: '考试状态'
    },
    'published': {
      title: '发布状态'
    },
    'beginTime': {
      title: '考试时间'
    },
    'distanceTime': {
      title: '时长'
    },
    'studentNum': {
      title: '考试人数/总人数'
    },
    'createAt': {
      title: '创建时间'
    }
  },
  // 当前显示列key表 默认，如果localStorage有数据将被覆盖
  columnsViewArr: [
    'name',
    // 'certificateName',
    // 'certificateLevel',
    'examStatus',
    'published',
    'beginTime',
    'distanceTime',
    'studentNum',
    'createAt'
  ],
  timeList: [
    { id: 0, name: '30min' },
    { id: 1, name: '1h' },
    { id: 2, name: '1h30min' },
    { id: 3, name: '2h' },
    { id: 4, name: '2h30min' },
    { id: 5, name: '3h' },
    { id: 6, name: '3h30min' },
    { id: 7, name: '4h' },
    { id: 8, name: '4h30min' },
    { id: 9, name: '5h' },
    { id: 10, name: '5h30min' },
    { id: 11, name: '6h' },
    { id: 12, name: '6h30min' },
    { id: 13, name: '7h' },
    { id: 14, name: '7h30min' },
    { id: 15, name: '8h' },
    { id: 16, name: '8h30min' },
    { id: 17, name: '9h' },
    { id: 18, name: '9h30min' },
    { id: 19, name: '10h' },
    { id: 20, name: '10h30min' },
    { id: 21, name: '11h' },
    { id: 22, name: '11h30min' },
    { id: 23, name: '12h' },
    { id: 24, name: '12h30min' },
    { id: 25, name: '13h' },
    { id: 26, name: '13h30min' },
    { id: 27, name: '14h' },
    { id: 28, name: '14h30min' },
    { id: 29, name: '15h' },
    { id: 30, name: '15h30min' },
    { id: 31, name: '16h' },
    { id: 32, name: '16h30min' },
    { id: 33, name: '17h' },
    { id: 34, name: '17h30min' },
    { id: 35, name: '18h' },
    { id: 36, name: '18h30min' },
    { id: 37, name: '19h' },
    { id: 38, name: '19h30min' },
    { id: 39, name: '20h' },
    { id: 40, name: '20h30min' },
    { id: 41, name: '21h' },
    { id: 42, name: '21h30min' },
    { id: 43, name: '22h' },
    { id: 44, name: '22h30min' },
    { id: 45, name: '23h' },
    { id: 46, name: '23h30min' },
    { id: 47, name: '24h' }
  ]
}
