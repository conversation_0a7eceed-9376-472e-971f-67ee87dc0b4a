<template>
  <div class="resource-table">
    <top-detail
      :datas="topDetailData"
    />
    <div class="operation-wrap">
      <div class="operation-left">
        <slot name="action" />
        <span v-if="options[0].className!==null">
          班级：<el-select v-model="value" filterable placeholder="请选择班级" @change="changeValue">
            <el-option
              v-for="(item,index) in options"
              :key="item.className"
              :label="item.className"
              :value="index"/>
          </el-select>
        </span>
        <el-button type="primary" icon="el-icon-refresh" @click="refresh" />
      </div>
      <div class="operation-right">
        <!-- 自定义表格列 -->
        <t-table-config
          v-if="!customColData.length"
          :data="columnsObj"
          :active-key-arr="columnsViewArr"
          @on-change-col="onChangeCol"
        />
      </div>
    </div>
    <!-- 列表 -->
    <t-table-view
      ref="tableView"
      :height="height"
      :single="true"
      :loading="tableLoading"
      :data="examQuestionList"
      :total="tableTotal"
      :page-size="pageSize"
      :current="pageCurrent"
      :select-item="selectItem"
      :multiple-page="false"
      :type="'list'"
      current-key="userId"
      @on-select="onSelect"
      @on-current="onCurrent"
      @on-change="changePage"
      @on-sort-change="onSortChange"
      @on-page-size-change="onPageSizeChange"
    >
      <el-table-column v-for="item in columnsViewArr" :key="item" :min-width="colMinWidth" :label="columnsObj[item].title" :fixed="columnsObj[item].master ? 'left' : false" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="item == 'groupName'">
            <a
              v-if="link"
              :href="getHref(scope.row)"
              @click.prevent="gosee(scope.row)"
            >
              {{ scope.row[item] }}
            </a>
            <!-- <span v-else>{{ scope.row[item] }}</span> -->
          </span>
          <span v-else-if="item == 'taskGroupReviewState'">{{ scope.row[item] === 0?'未批阅':"已批阅" }}</span>
          <span v-else-if="item == 'taskGroupScore'">
            <span>{{ scope.row.taskGroupReviewState === 0 ? '-' : scope.row.taskGroupScore ? scope.row.taskGroupScore : 0 }}</span>
          </span>
          <!-- <span v-else-if="item == 'complexity'">{{ difficultyList[scope.row[item]] || "-" }}</span>
          <span v-else-if="item == 'type'">{{ scope.row['schedulingType'] || "-" }}</span> -->
          <span v-else>{{ scope.row[item] !== null?scope.row[item]:"-" }}</span>
        </template>
      </el-table-column>
    </t-table-view>
  </div>
</template>

<script>
import module from '../config.js'
import topDetail from '../../components/top-detail.vue'
import tSearchBox from '@/packages/search-box/index.vue'
import tTableView from '@/packages/table-view/index.vue'
import tTableConfig from '@/packages/table-config/table-col-config.vue'
import tableTdMultiCol from '@/packages/table-config/table-td-multi-col.vue'
import mixinsPageTable from '@/packages/mixins/page_table'
import { searchAffairTeachingTaskAPI } from '@/api/teacher/index.js'

export default {
  components: {
    tSearchBox,
    tTableView,
    tTableConfig,
    tableTdMultiCol,
    topDetail
  },
  mixins: [mixinsPageTable],
  data() {
    return {
      moduleName: module.name,
      // 搜索配置项
      // 所有可配置显示列 master：不可隐藏 title:列名称
      columnsObj: {
        'groupName': {
          title: '项目小组',
          master: true
        },
        'realNames': {
          title: '小组成员'
        },
        'taskGroupReviewState': {
          title: '批阅状态'
        },
        'taskGroupScore': {
          title: '小组得分'
        }
      },
      // 当前显示列key表 默认，如果localStorage有数据将被覆盖
      columnsViewArr: [
        'groupName',
        'realNames',
        'taskGroupReviewState',
        'taskGroupScore'
      ],
      pageSize: this.pageSize,
      pageNum: this.pageCurrent,
      examQuestionList: [],
      topDetailData: this.$route.query,
      contentTypeList: ['理论', '仿真'],
      difficultyList: ['初级', '中级', '高级'],
      options: [],
      changeData: null,
      value: 0,
      taskCode: '',
      schedulingCode: ''
    }
  },
  watch: {
    '$route': function(to, from) {
      this.topDetailData = this.$route.query
      this.options = JSON.parse(localStorage.getItem(`manageAffairs_resultList`) || '[]')
      if (![undefined, null, ''].includes(this.options[0].className)) {
        this.changeData = this.options[0]
      }
      this.getList()
    }
  },
  created() {
    this.options = JSON.parse(localStorage.getItem(`manageAffairs_resultList`) || '[]')
    if (![undefined, null, ''].includes(this.options[0].className)) {
      this.changeData = this.options[0]
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    gosee(row) {
      this.$router.push({
        name: 'educationalTasksDetails',
        query: {
          groupCode: row.groupCode,
          taskCode: this.curriculumCode,
          upAndDown: 0,
          content: this.$route.query.content,
          schedulingCode: this.$route.query.schedulingCode || this.schedulingCode,
          resultList: this.options
        }
      })
    },
    getHref(row) {
      const route = this.$router.resolve({
        name: 'educationalTasksDetails',
        query: {
          groupCode: row.groupCode,
          taskCode: this.curriculumCode,
          upAndDown: 0,
          content: this.$route.query.content,
          schedulingCode: this.$route.query.schedulingCode || this.schedulingCode,
          resultList: this.options
        }
      })
      return route.href
    },
    getList: function(showLoading = true, changeData) {
      if (changeData) { this.changeData = changeData }
      const resultList = this.changeData instanceof Array ? [...this.changeData] : [this.changeData]
      if (showLoading) {
        this.tableLoading = true
      }
      this.topDetailData = this.$route.query
      const query = {
        classCode: this.$route.query.classCode,
        schedulingCode: this.$route.query.schedulingCode,
        curriculumCode: this.$route.query.curriculumCode,
        resultList: resultList }
      searchAffairTeachingTaskAPI(query).then(res => {
        this.examQuestionList = res.data.teacherTaskGroupVoList
        this.tableLoading = false
        this.schedulingCode = res.data.schedulingCode
        this.curriculumCode = res.data.curriculumCode
        this.tableTotal = res.data.teacherTaskGroupVoList.length
      }).catch(() => {
        this.tableLoading = false
      })
    },
    // 选择班级
    changeValue(val) {
      this.$emit('changeClass', this.options[val])
    },
    classificationType(param) {
      this.queryParameters = param
      this.getList()
    },
    // 查看
    handleDetailClick(row, type) {
      const { contentType, name, id } = row
      let path = ''
      if (contentType === '1') {
        path = '/teacher/testPaper/contain/basics'
      } else {
        path = '/teacher/testPaper/standAlone/basics'
      }
      if (!path) {
        return
      }
      this.$router.push({
        path,
        query: {
          curriculumName: '我的内容', oneLevelTitle: '课程管理', oneLevelName: 'teacherTeaching', twoLevelTitle: '课程详情', twoLevelName: 'courseDetailLibrary', courseId: this.$route.query.courseId,
          teachingName: name,
          id
        }
      })
    }

  }
}
</script>
