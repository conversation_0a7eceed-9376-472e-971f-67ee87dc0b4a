<template>
  <el-row v-loading="loading" style="padding: 20px;">
    <el-col :span="24">
      <detail-card title="基本信息">
        <el-form v-if="data" slot="content" label-position="left" label-width="130px">
          <el-form-item :label="getNameLabel">{{ data.name || '-' }}</el-form-item>
          <el-form-item :label="(data.resource_type === 'pnf' ? '物理' : '虚拟') + '设备名称：'">{{ deviceData.name || '-' }}</el-form-item>
          <el-form-item v-if="data.resource_type === 'pnf'" label="描述：">{{ data.description || '-' }}</el-form-item>
          <el-form-item v-if="data.resource_type === 'vnf'" label="实例状态：">
            <template v-if="data.status">
              <el-badge
                :type="data.resource_type === 'pnf' ? 'success' : getStatus(data.status)"
                is-dot />{{ data.resource_type === 'pnf' ? '已引入' : node_status_info[data.status.toLowerCase()] }}
            </template>
            <span v-else>-</span>
          </el-form-item>
          <el-form-item :label="(data.resource_type === 'pnf' ? '物理' : '') + '设备厂商：'">{{ deviceData.vendor || '-' }}</el-form-item>
          <el-form-item :label="(data.resource_type === 'pnf' ? '物理' : '') + '设备型号：'">{{ deviceData.model || '-' }}</el-form-item>
          <template v-if="data.resource_type === 'vnf'">
            <el-form-item label="CPU：">{{ data.cpu || '-' }}</el-form-item>
            <el-form-item label="内存：">{{ data.ram ? $options.filters['transStoreShowInt'](data.ram, 'MB') : '-' }}</el-form-item>
            <el-form-item label="系统盘大小：">{{ data.sys_disk_size ? $options.filters['transStoreShowInt'](data.sys_disk_size, 'GB') : '-' }}</el-form-item>
          </template>
          <el-form-item :label="(data.resource_type === 'pnf' ? '' : '实例') + '创建时间：'">{{ $options.filters['nfvoMoment'](data.created_at) }}</el-form-item>
          <el-form-item :label="(data.resource_type === 'pnf' ? '' : '实例') + '更新时间：'">{{ $options.filters['nfvoMoment'](data.updated_at) }}</el-form-item>
          <el-form-item label="产品端口：" class="port-form">
            <t-table-view
              ref="tableView"
              :height="null"
              :data="deviceData.ports || []"
              :total="deviceData.ports ? deviceData.ports.length : 0"
              :multiple-page="false"
              type="list"
            >
              <el-table-column label="端口名称" fixed="left" show-overflow-tooltip>
                <template slot-scope="scope">
                  {{ scope.row.port_name }}
                </template>
              </el-table-column>
              <el-table-column label="占用情况" show-overflow-tooltip>
                <template slot-scope="scope">
                  <el-popover
                    :disabled="!scope.row.link_to && !scope.row.imported"
                    popper-class="link-port-detail-popover"
                    placement="top"
                    width="300"
                    trigger="click">
                    <table class="description-table">
                      <tbody v-if="scope.row.imported && scope.row.imported.length == 1">
                        <tr>
                          <th>拓扑名称：</th>
                          <td>{{ scope.row.imported[0].topology_name || '-' }}</td>
                        </tr>
                        <tr>
                          <th>设备名称：</th>
                          <td>{{ scope.row.imported[0].node_info ? scope.row.imported[0].node_info.name : '-' }}</td>
                        </tr>
                      </tbody>
                      <tbody v-else-if="scope.row.imported && scope.row.imported.length > 1">
                        <div v-for="(item, index) in scope.row.imported" :key="index">
                          <tr>
                            <th>拓扑名称：</th>
                            <td>{{ item.topology_name || '-' }}</td>
                          </tr>
                          <tr>
                            <th>设备名称：</th>
                            <td>{{ item.node_info.name || '-' }}</td>
                          </tr>
                          <el-divider v-if="index + 1 != scope.row.imported.length" style="margin: 10px 0;"/>
                        </div>
                      </tbody>
                      <tbody v-else-if="scope.row.link_to">
                        <tr>
                          <th>设备名称：</th>
                          <td>{{ scope.row.link_to.device_name || '-' }}</td>
                        </tr>
                        <tr>
                          <th>关联端口：</th>
                          <td>{{ scope.row.link_to.port_name || '-' }}</td>
                        </tr>
                        <tr>
                          <th>设备厂商：</th>
                          <td>{{ scope.row.link_to.device_vendor || '-' }}</td>
                        </tr>
                        <tr>
                          <th>设备型号：</th>
                          <td>{{ scope.row.link_to.device_model || '-' }}</td>
                        </tr>
                        <tr>
                          <th>设备分类：</th>
                          <td>{{ scope.row.link_to.device_category }}</td>
                        </tr>
                      </tbody>
                    </table>
                    <div slot="reference">
                      <div v-if="scope.row.imported">
                        <a href="javascript:;">已引入</a>
                      </div>
                      <div v-else-if="scope.row.link_to">
                        <a href="javascript:;">已占用</a>
                      </div>
                      <span v-else>{{ scope.row.reserved ? '已预留' : '-' }}</span>
                    </div>
                  </el-popover>
                </template>
              </el-table-column>
            </t-table-view>
          </el-form-item>
        </el-form>
      </detail-card>
    </el-col>
  </el-row>
</template>
<script>
import { getNodeItem } from '../../api/orchestration'
import tTableView from '../../../table-view/index.vue'
import detailCard from '../../../detail-view/detail-card.vue'
export default {
  components: { tTableView, detailCard },
  props: {
    id: String
  },
  data() {
    return {
      'resource_type': {
        'pnf': '物理设备',
        'vnf': '虚拟设备',
        'inf': '图形设备',
        'base': '基础组件'
      },
      'node_status_info': {
        'pending': '添加成功',
        'starting': '启动中',
        'running': '运行中',
        'powering_on': '开机中',
        'shutoff': '关机',
        'powering_off': '关机中',
        'deleting': '删除中',
        'suspended': '挂起',
        'suspending': '挂起中',
        'paused': '暂停',
        'pausing': '暂停中',
        'rebooting': '重启中',
        'rebuilding': '重建中',
        'error': '错误',
        'resuming': '恢复中'
      },
      loading: true,
      data: null,
      deviceData: {}
    }
  },
  computed: {
    getNameLabel() {
      let label = '名称：'
      const data = this.data
      if (data.resource_type === 'vnf') {
        switch (data.virtual_type) {
          case 'qemu':
            label = '虚拟机实例名称：'
            break
          case 'docker':
            label = '容器实例名称：'
            break
          case 'cloud_router':
            label = '云路由实例名称：'
            break
          case 'global_qemu':
            label = '全局虚拟机实例名称：'
            break
          case 'global_network':
            label = '全局网络实例名称：'
            break
          default:
            break
        }
      }
      return label
    }
  },
  watch: {
    'id': function() {
      this.getData(this.id)
    }
  },
  created() {
    this.getData(this.id)
  },
  methods: {
    'getData': function(id) {
      this.loading = true
      this.data = null
      getNodeItem(id)
        .then(res => {
          this.data = res['data']['data']
          this.deviceData = res['data']['data'].network_element_data || {}
          console.log(this.data)
          console.log(this.deviceData)
          this.loading = false
        })
        .catch((error) => {
          console.log(error)
          this.loading = false
          this.data = null
        })
    },
    'getStatus': function(data) {
      let color = null
      switch (data.toLowerCase()) {
        case 'running':
          color = 'success'
          break
        case 'shutoff':
        case 'error':
          color = 'danger'
          break
        case 'pending':
          color = 'info'
          break
        default:
          color = 'warning'
          break
      }
      return color
    }
  }
}
</script>

<style lang="less">
.description-table .data-table-footer {
  display: none;
}
.port-form {
  th {
    padding: 0;
  }
}
</style>
<style scoped>
.el-divider--horizontal {
  margin: 10px 0 ;
}
</style>
