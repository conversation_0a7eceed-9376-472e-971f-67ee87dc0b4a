<!--
附件表格组件 - 本地数据版本
改造说明：
1. 将原来的实时API调用改为本地数据存储
2. 实现了本地分页功能，数据存储在 localAttachments 中
3. 通过 computed 属性 paginatedData 实现分页显示
4. 提供了 addAttachment, updateAttachment, removeAttachmentLocal 等方法操作本地数据
5. 通过 syncToParent 方法保持与父组件数据同步
6. 所有增删改操作都在本地进行，不再调用后端API
7. 数据只在最终提交时发送到后端
-->
<template>
  <div class="attachment-table" style="height: 100%; padding: 0;">
    <!-- 附件列表 -->
    <t-table-view
      ref="tableView"
      :height="height"
      :single="single"
      :loading="tableLoading"
      :data="paginatedData"
      :total="attachmentTableTotal"
      :page-size="pageSize"
      :current="pageCurrent"
      :select-item="selectItem"
      type="list"
      current-key="id"
      @on-select="onSelect"
      @on-current="onCurrent"
      @on-change="changePage"
      @on-sort-change="onSortChange"
      @on-page-size-change="onPageSizeChange"
    >
      <el-table-column
        v-for="item in columnsViewArr"
        :key="item"
        :min-width="colMinWidth"
        :width="columnsObj[item].colWidth"
        :label="columnsObj[item].title"
        :fixed="columnsObj[item].master ? 'left' : false"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <div v-if="item == 'index'">
            {{ getDisplayIndex(scope.$index) }}
          </div>
          <div v-else-if="item == 'fileSize'">
            {{ formatFileSize(scope.row.fileSize) }}
          </div>
          <div v-else-if="item == 'handle'">
            <el-button
              style="color: var(--color-600)"
              type="text"
              size="small"
              @click="previewFile(scope.row)"
            >查看</el-button>
            <el-button
              style="color: var(--color-600)"
              type="text"
              size="small"
              @click="updateFile(scope.row)"
            >更新</el-button>
            <el-button
              style="color: var(--color-600)"
              type="text"
              size="small"
              @click="removeFile(getOriginalIndex(scope.$index))"
            >删除</el-button>
          </div>
          <span v-else>{{ scope.row[item] || "-" }}</span>
        </template>
      </el-table-column>
    </t-table-view>
  </div>
</template>
<script>
import filePreview from '@/components/testing/utils/filePreview'
import mixinsPageTable from '@/packages/mixins/page_table'
import tTableConfig from '@/packages/table-config/table-col-config.vue'
import tableTdMultiCol from '@/packages/table-config/table-td-multi-col.vue'
import tTableView from '@/packages/table-view/index.vue'
import moduleConf from '../config'

export default {
  components: {
    tTableView,
    tTableConfig,
    tableTdMultiCol
  },
  mixins: [mixinsPageTable, filePreview],
  props: {
    projectId: {
      type: [String, Number],
      default: ''
    },
    activeAttachmentType: {
      type: [String, Number],
      default: ''
    }
  },
  inject: ['testSubmitVm'],
  data() {
    return {
      searchKeyList: [],
      columnsObj: moduleConf.fileColumnsObj,
      columnsViewArr: moduleConf.fileColumnsViewArr,
      // 本地附件数据存储（按任务分组）
      localAttachmentsByTask: {},
      // 下一个ID计数器（用于新增数据的临时ID）
      nextTempId: 1
    }
  },
  computed: {
    // 获取当前任务的附件列表
    currentTaskAttachments() {
      return this.localAttachmentsByTask[this.activeAttachmentType] || []
    },
    // 计算分页后的数据
    paginatedData() {
      const start = (this.pageCurrent - 1) * this.pageSize
      const end = start + this.pageSize
      return this.currentTaskAttachments.slice(start, end)
    },
    // 更新总数为当前任务附件的长度
    attachmentTableTotal() {
      return this.currentTaskAttachments.length
    }
  },
  watch: {
    // 监听当前任务类型变化
    activeAttachmentType: {
      handler(newVal) {
        if (newVal) {
          this.pageCurrent = 1 // 切换任务时回到第一页
        }
      },
      immediate: true
    },
    // 监听父组件attachmentList变化
    'testSubmitVm.attachmentList': {
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.initializeLocalData()
        }
      },
      deep: true
    }
  },
  methods: {
    // 初始化本地数据
    initializeLocalData() {
      // 从父组件获取现有的附件数据
      if (this.testSubmitVm.attachmentList && this.testSubmitVm.attachmentList.length > 0) {
        // 先保存一份当前数据的引用，避免完全清空导致界面闪烁
        const oldData = { ...this.localAttachmentsByTask }
        // 创建新的数据存储
        const newData = {}

        this.testSubmitVm.attachmentList.forEach(attachment => {
          // 兼容typeId和taskId两种字段名
          const taskId = attachment.typeId || attachment.taskId || this.activeAttachmentType

          if (!newData[taskId]) {
            newData[taskId] = []
          }

          // 添加到对应任务的数组中
          newData[taskId].push({
            ...attachment,
            typeId: taskId, // 确保typeId字段存在
            taskId: taskId, // 确保taskId字段存在
            projectId: this.projectId
          })
        })

        // 使用Vue的响应式更新方法更新数据
        Object.keys(newData).forEach(taskId => {
          this.$set(this.localAttachmentsByTask, taskId, newData[taskId])
        })

        // 删除不再存在的任务数据
        Object.keys(oldData).forEach(taskId => {
          if (!newData[taskId]) {
            this.$delete(this.localAttachmentsByTask, taskId)
          }
        })
      }
    },

    // 重写getList方法，改为本地数据处理
    getList: function(showLoading = true) {
      if (showLoading) {
        this.tableLoading = true
      }

      // 模拟异步加载
      setTimeout(() => {
        this.tableLoading = false
      }, 100)
    },

    // 添加附件到本地数据
    addAttachment(attachmentData) {
      // 确保附件数据包含正确的任务ID和项目ID
      const updatedData = {
        ...attachmentData,
        typeId: this.activeAttachmentType,
        taskId: this.activeAttachmentType,
        projectId: this.projectId || attachmentData.projectId
      }

      // 确保当前任务的附件数组存在
      if (!this.localAttachmentsByTask[this.activeAttachmentType]) {
        this.$set(this.localAttachmentsByTask, this.activeAttachmentType, [])
      }

      // 添加临时ID如果不存在
      if (!updatedData.id) {
        updatedData.id = 'temp_' + Date.now() + '_' + Math.floor(Math.random() * 1000)
      }

      // 添加到本地数据
      this.localAttachmentsByTask[this.activeAttachmentType].push(updatedData)

      // 同步到父组件
      this.syncToParent()

      // 如果当前页显示不下新数据，跳到最后一页
      const totalPages = Math.ceil(this.currentTaskAttachments.length / this.pageSize)
      if (this.pageCurrent < totalPages) {
        this.pageCurrent = totalPages
      }
    },

    // 更新附件
    updateAttachment(attachmentId, attachmentData) {
      // 确保附件数据包含正确的任务ID和项目ID
      const updatedData = {
        ...attachmentData,
        typeId: this.activeAttachmentType,
        taskId: this.activeAttachmentType,
        projectId: this.projectId || attachmentData.projectId
      }

      const taskAttachments = this.localAttachmentsByTask[this.activeAttachmentType] || []
      const index = taskAttachments.findIndex(att => att.id === attachmentId)

      if (index !== -1) {
        this.$set(taskAttachments, index, updatedData)
        this.syncToParent()
      }
    },

    // 从本地数据中删除附件
    removeAttachmentLocal(index) {
      const taskAttachments = this.localAttachmentsByTask[this.activeAttachmentType] || []
      if (index >= 0 && index < taskAttachments.length) {
        taskAttachments.splice(index, 1)

        // 同步到父组件
        this.syncToParent()

        // 如果当前页没有数据了，回到上一页
        if (this.paginatedData.length === 0 && this.pageCurrent > 1) {
          this.pageCurrent = this.pageCurrent - 1
        }
      }
    },

    // 同步本地数据到父组件
    syncToParent() {
      // 将所有任务的附件合并到一个数组
      const allAttachments = []

      Object.keys(this.localAttachmentsByTask).forEach(taskId => {
        const taskAttachments = this.localAttachmentsByTask[taskId] || []
        taskAttachments.forEach(attachment => {
          allAttachments.push({
            ...attachment,
            typeId: taskId,
            taskId: taskId,
            projectId: this.projectId || attachment.projectId
          })
        })
      })

      // 更新父组件的附件列表
      this.testSubmitVm.attachmentList = allAttachments
    },

    // 获取显示的序号（考虑分页）
    getDisplayIndex(pageIndex) {
      return (this.pageCurrent - 1) * this.pageSize + pageIndex + 1
    },

    // 获取原始数组中的索引
    getOriginalIndex(pageIndex) {
      return (this.pageCurrent - 1) * this.pageSize + pageIndex
    },

    // 重写changePage方法
    changePage(num) {
      this.pageCurrent = num
    },

    // 重写onPageSizeChange方法
    onPageSizeChange(pageSize) {
      this.pageSize = pageSize
      // 如果当前页超出了新的页数范围，调整到最后一页
      const maxPage = Math.ceil(this.currentTaskAttachments.length / pageSize)
      if (this.pageCurrent > maxPage && maxPage > 0) {
        this.pageCurrent = maxPage
      }
    },

    // 文件大小格式化
    formatFileSize(size) {
      if (size < 1024) {
        return size + ' B'
      } else if (size < 1024 * 1024) {
        return (size / 1024).toFixed(2) + ' KB'
      } else {
        return (size / 1024 / 1024).toFixed(2) + ' MB'
      }
    },

    // 预览文件
    previewFile(file) {
      this.$emit('previewFile', file)
    },

    // 更新文件
    updateFile(file) {
      this.$emit('updateFile', file)
    },

    // 删除文件
    removeFile(index) {
      this.$emit('removeFile', index)
    }
  }
}
</script>

<style scoped lang="scss">
.attachment-table {
  .el-button + .el-button {
    margin-left: 0;
  }
}
</style>
