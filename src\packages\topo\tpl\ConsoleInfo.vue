<template>
  <div v-loading="loading" :style="{'left': x + 'px', 'top': y + 'px' }" class="orchestration-context-menu">
    <div class="title" >
      {{ nodeData.name }}
    </div>
    <div class="content">
      <div>
        <div v-for="(item, index) in data.ports" v-show="item.ipaddr" :key="index" class="content-info">
          <div class="content-info-title">内网IP地址：</div>
          <div>{{ item.ipaddr }}</div>
        </div>
        <div v-if="data.console_info.length" class="content-info">
          <div class="content-info-title">控制台连接地址：</div>
          <div>
            <div v-for="item in data.console_info" :key="item.console_type">{{ item.console_type }}{{ item.ip }}:{{ item.port }}</div>
          </div>
        </div>
      </div>
      <div v-if="data.is_workstation" class="btn">
        <el-button type="primary" @click="consoleOperate(nodeData)">控制台登录</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { serial, vnc, getConsole } from '../api/orchestration'
import { getNodeItem } from '../api/orchestration'
import { Graph } from '@antv/x6'
export default {
  name: 'ConsoleInfo',
  props: {
    graph: {
      type: [Object, Graph]
    }
  },
  data() {
    return {
      panelColor: 'rgba(255, 255, 255, 1)',
      textColor: 'rgba(0, 0, 0, 1)',
      x: '',
      y: '',
      type: '',
      node: {},
      edge: {},
      nodeData: {},
      edgeData: {},
      batchData: [],
      nodeIdList: [],
      data: {},
      loading: false
    }
  },
  watch: {
    'node'(val) {
      if (val.data.virtual_type == 'panel') {
        this.panelColor = val.attrs.body.fill
      } else if (val.data.virtual_type == 'text') {
        this.textColor = val.attrs.text.fill
      }
    }
  },
  inject: ['topoVM'],
  methods: {
    consoleOperate(node) {
      switch (node.console_type) {
        case 'vnc':
          vnc(node.node_id)
            .then(res => {
              if (res.data.code == 0) {
                const url = res.data.data
                if (this.topoVM.consoleTarget == '_self') {
                  this.topoVM.consoleUrl('vnc', url, node.node_id)
                  return
                }
                const a = document.createElement('a')
                a.setAttribute('href', encodeURI(url))
                a.setAttribute('target', '_blank')
                a.setAttribute('id', 'camnpr')
                document.body.appendChild(a)
                a.click()
              }
            })
            .catch(() => {})
          break
        case 'serial':
          serial(node.node_id)
            .then(res => {
              const name = this.nodeData.name
              const url = res.data.data + '&title=' + name
              if (this.topoVM.consoleTarget == '_self') {
                this.topoVM.consoleUrl('serial', url, node.node_id)
                return
              }
              const a = document.createElement('a')
              a.setAttribute('href', encodeURI(url))
              a.setAttribute('target', '_blank')
              a.setAttribute('id', 'camnpr')
              document.body.appendChild(a)
              a.click()
            })
            .catch(() => {})
          break
        case 'rdp': {
          getConsole(node.node_id, { console_type: 'rdp' })
            .then(res => {
              const url = (window.location.hostname == 'localhost' ? (window.ADMIN_CONFIG ? window.ADMIN_CONFIG.VIP_URL : window.WEB_CONFIG.VIP_URL) : '') + res.data.data
              if (this.topoVM.consoleTarget == '_self') {
                this.topoVM.consoleUrl('rdp', url, node.node_id)
                return
              }
              const a = document.createElement('a')
              a.setAttribute('href', encodeURI(url))
              a.setAttribute('target', '_blank')
              a.setAttribute('id', 'camnpr')
              document.body.appendChild(a)
              a.click()
            })
            .catch(() => {})
          break
        }
        case 'ssh': {
          getConsole(node.node_id, { console_type: 'ssh' })
            .then(res => {
              const url = (window.location.hostname == 'localhost' ? (window.ADMIN_CONFIG ? window.ADMIN_CONFIG.VIP_URL : window.WEB_CONFIG.VIP_URL) : '') + res.data.data
              if (this.topoVM.consoleTarget == '_self') {
                this.topoVM.consoleUrl('ssh', url, node.node_id)
                return
              }
              const a = document.createElement('a')
              a.setAttribute('href', encodeURI(url))
              a.setAttribute('target', '_blank')
              a.setAttribute('id', 'camnpr')
              document.body.appendChild(a)
              a.click()
            })
            .catch(() => {})
          break
        }
        case 'webshell': {
          getConsole(node.node_id, { console_type: 'webshell' })
            .then(res => {
              const url = (window.location.hostname == 'localhost' ? (window.ADMIN_CONFIG ? window.ADMIN_CONFIG.VIP_URL : window.WEB_CONFIG.VIP_URL) : '') + res.data.data
              if (this.topoVM.consoleTarget == '_self') {
                this.topoVM.consoleUrl('webshell', url, node.node_id)
                return
              }
              const a = document.createElement('a')
              a.setAttribute('href', encodeURI(url))
              a.setAttribute('target', '_blank')
              a.setAttribute('id', 'camnpr')
              document.body.appendChild(a)
              a.click()
            })
            .catch(() => {})
          break
        }
      }
    },
    init(x, y, type, data) {
      this.x = parseInt(x) + ''
      this.y = y + ''
      if (type && data) {
        this.type = type
        if (type === 'node') {
          this.node = data
          this.nodeData = data.data
          this.getNodeItem()
        } else if (type === 'edge') {
          this.edge = data
          this.edgeData = data.data
        } else {
          this.batchData = data
        }
      }
    },
    'getNodeItem': function() {
      this.loading = true
      getNodeItem(this.nodeData.node_id)
        .then(res => {
          this.data = res.data.data
          this.loading = false
        })
        .catch((error) => {
          this.loading = false
          console.log(error)
        })
    }
  }
}
</script>

<style lang="less" scoped>
  .orchestration-context-menu {
    min-width: 300px;
    position: absolute;
    z-index: 999;
    padding: 5px 0;
    background-color: #fff;
    border: 1px solid #ebeef5;
    border-radius: 2px;
    box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
    .title {
      padding: 10px 18px;
    }
    .content {
      padding: 10px 18px;
      border-top: 1px solid #000;
      .content-info {
        display: flex;
        margin-bottom: 10px;
        .content-info-title {
          width: 140px;
        }
      }
    }
    .btn {
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
</style>
