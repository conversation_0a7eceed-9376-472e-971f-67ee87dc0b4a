
import { downloadFiles } from '../api/index.js'
// 预览fastfds文件服务器上的图片或视频
export function picturePreview(url) {
  return new Promise((resolve, reject) => {
    downloadFiles({ filePath: url }).then(res => {
      const URL = window.URL || window.webkitURL
      const blobUrl = URL.createObjectURL(new Blob([res.data]))
      resolve(blobUrl)
    }).catch(() => {
      reject('')
    })
  })
}

// 下载fastfds文件服务器上的文件
export function fileDownload(url, name) {
  return new Promise((resolve, reject) => {
    downloadFiles({ filePath: url }).then(res => {
      resolve(res)
      const aElement = document.createElement('a')
      aElement.setAttribute('download', name)
      aElement.href = window.URL.createObjectURL(new Blob([res.data]))
      aElement.setAttribute('target', '_blank')
      aElement.style.display = 'none'
      document.body.appendChild(aElement)
      aElement.click()
      document.body.removeChild(aElement)
    }).catch(() => {
      reject('')
    })
  })
}
