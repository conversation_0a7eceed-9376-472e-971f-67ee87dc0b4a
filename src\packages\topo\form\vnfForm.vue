<template>
  <div v-loading="loading" class="drawer-wrap orchestration-drawer-wrap">
    <div class="drawer-wrap-content">
      <el-form ref="form" :model="formData" :rules="rules" label-width="120px">
        <el-form-item label="虚拟设备名称" prop="deviceName">
          {{ formData.deviceName }}
        </el-form-item>
        <template v-if="formData.virtual_type === 'qemu' || formData.virtual_type === 'docker' || formData.virtual_type === 'global_network' || formData.virtual_type === 'global_qemu'">
          <el-form-item label="虚拟设备厂商" prop="vendor">
            {{ formData.vendor }}
          </el-form-item>
          <el-form-item label="虚拟设备型号" prop="model">
            {{ formData.model }}
          </el-form-item>
        </template>
        <el-form-item label="虚拟实例名称" prop="name">
          <el-input :disabled="disabledType" v-model.trim="formData.name"/>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input :disabled="disabledType" v-model.trim="formData.description" type="textarea"/>
        </el-form-item>
        <el-form-item label="控制台" prop="console_type">
          <el-select :disabled="disabledType" v-model="formData.console_type" multiple style="width: 100%;">
            <el-option v-for="(item, index) in consoleListView" :key="index" :label="item.label" :value="item.value"/>
          </el-select>
        </el-form-item>
        <template v-if="formData.virtual_type === 'qemu'">
          <el-form-item label="是否为工作站" prop="is_workstation">
            <el-switch :disabled="disabledType" v-model="formData.is_workstation"/>
          </el-form-item>
          <el-form-item label="CPU" prop="cpu">
            <el-input-number :disabled="disabledType" v-model="formData.cpu"/> 核
          </el-form-item>
          <el-form-item label="内存" prop="ram" style="display: inline-block;">
            <el-input-number :disabled="disabledType" v-model="formData.ram"/>
            <el-select :disabled="disabledType" v-model="ramUnit" style="width: 60px;" @change="changeUnit">
              <el-option v-for="item in ramUnitList" :key="item" :label="item" :value="item"/>
            </el-select>
          </el-form-item>
          <el-form-item label="系统盘大小" prop="sys_disk_size" style="display: inline-block;">
            <el-input-number :disabled="disabledType" v-model="formData.sys_disk_size"/> GB
          </el-form-item>
          <el-form-item label="登录用户名" prop="admin_user">
            <el-input v-model.trim="formData.admin_user" :disabled="formData.image_os_type === 'linux' || disabledType"/>
          </el-form-item>
          <el-form-item label="登录密码">
            <el-input :disabled="disabledType" v-model.trim="formData.admin_pass" show-password/>
          </el-form-item>
        </template>
        <el-form-item v-if="formData.virtual_type === 'qemu' || formData.virtual_type === 'docker'" label="产品端口" class="port-form" >
          <t-table-view
            ref="tableView"
            :height="null"
            :loading="loading"
            :data="formData.ports"
            :total="formData.ports.length"
            :multiple-page="false"
            type="list"
          >
            <el-table-column label="名称" show-overflow-tooltip>
              <template slot-scope="scope">
                {{ scope.row.name }}
              </template>
            </el-table-column>
            <el-table-column label="占用情况" show-overflow-tooltip>
              <template slot-scope="scope">
                {{ scope.row.link_to ? `${scope.row.link_to.node_name} / ${scope.row.link_to.port_name}` : '-' }}
              </template>
            </el-table-column>
            <el-table-column label="IP地址" show-overflow-tooltip>
              <template slot-scope="scope">
                <span>{{ scope.row.ipaddr || '-' }}</span>
              </template>
            </el-table-column>
          </t-table-view>
        </el-form-item>
        <el-form-item v-if="formData.virtual_type === 'cloud_router' || formData.virtual_type === 'global_network'" label="LAN口" class="port-form" prop="lan" >
          <t-table-view
            ref="tableView"
            :height="null"
            :loading="loading"
            :data="formData.ports"
            :total="formData.ports.length"
            :multiple-page="false"
            type="list"
          >
            <el-table-column label="CIDR" show-overflow-tooltip>
              <template slot-scope="scope">
                {{ scope.row.cidr || '-' }}
              </template>
            </el-table-column>
            <el-table-column label="网关" show-overflow-tooltip>
              <template slot-scope="scope">
                {{ scope.row.gateway || '-' }}
              </template>
            </el-table-column>
          </t-table-view>
        </el-form-item>
      </el-form>
    </div>
    <div class="drawer-footer">
      <el-button :disabled="disabledType" type="primary" @click="confirm">确定</el-button>
      <el-button type="text" @click="close">取消</el-button>
    </div>
  </div>
</template>
<script>
import validate from '../../validate'
import tTableView from '../../table-view/index.vue'
import { getNodeItem } from '../api/orchestration'
export default {
  components: { tTableView },
  props: {
    data: {
      type: Object,
      default: () => {
        return {}
      }
    },
    type: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      consoleList: [
        { type: ['qemu'], label: 'VNC', value: 'vnc' },
        { type: ['qemu'], label: '远程桌面', value: 'rdp' },
        { type: ['qemu', 'docker'], label: 'WebSSH', value: 'ssh' },
        { type: ['qemu'], label: '串口控制台', value: 'serial' },
        { type: ['docker'], label: '命令行', value: 'webshell' }
      ],
      loading: true,
      validate: validate,
      ramUnitList: ['MB', 'GB', 'TB'],
      ramUnit: 'GB', // 内存单位
      ramMax: 10485760, // ram的可输入最大值
      formData: {
        'virtual_type': '',
        'name': '',
        'deviceName': '',
        'vendor': '',
        'model': '',
        'ip_address': '',
        'ports': [],
        'is_workstation': false,
        'console_type': [],
        'cpu': undefined,
        'ram': undefined,
        'sys_disk_size': undefined,
        'admin_user': '',
        'admin_pass': '',
        'description': ''
      },
      rules: {
        'name': [
          validate.required(),
          validate.base_name
        ],
        'ip_address': [
          validate.filterIPAddress
        ],
        'cpu': [
          validate.required(),
          validate.number_integer,
          { type: 'number', min: 1, max: 100000, message: '输入范围：1-100000', trigger: 'change' }
        ],
        'ram': [
          validate.required(),
          validate.number_integer,
          { validator: this.ramValidate, trigger: 'change' }
        ],
        'sys_disk_size': [
          validate.number_integer,
          { type: 'number', min: 1, max: 10240, message: '输入范围：1-10240', trigger: 'change' }
        ],
        'admin_user': [
          validate.required()
        ],
        'description': [
          validate.description
        ]
      }
    }
  },
  computed: {
    consoleListView() {
      return this.consoleList.filter(item => item.type.includes(this.formData.virtual_type))
    },
    disabledType() {
      return this.type !== 'allPermissions' && this.type !== 'templatePermissions'
    }
  },
  created() {
    const data = this.data.node.data
    if (data.resource_type === 'vnf' && data.virtual_type === 'docker') {
      this.rules.cpu.shift()
      this.rules.ram.shift()
    }
    this.formData['virtual_type'] = data['virtual_type']
    this.formData['name'] = data['name']
    this.formData['deviceName'] = data['deviceName']
    this.formData['vendor'] = data['vendor']
    this.formData['model'] = data['model']
    this.formData['ports'] = data['ports']
    this.formData['cpu'] = data['cpu']
    this.formData['is_workstation'] = data['is_workstation'] || false
    this.formData['console_type'] = data['console_type'] ? data['console_type'].split(',') : []
    const unitArr = this.$options.filters['transStore'](data['ram'], 'MB').split(' ')
    this.ramUnit = unitArr[1]
    this.changeUnit(this.ramUnit)
    this.formData['ram'] = Number(unitArr[0])
    this.formData['sys_disk_size'] = data['sys_disk_size'] || undefined
    this.formData['admin_user'] = data['admin_user'] || ''
    this.formData['image_os_type'] = data['image_os_type']
    this.formData['admin_pass'] = data['admin_pass'] || ''
    this.formData['description'] = data['description'] || ''
    this.getNodeItem()
  },
  methods: {
    // 重新获取node的端口
    'getNodeItem': function() {
      getNodeItem(this.data.node.data.node_id)
        .then(res => {
          this.formData['ports'] = res.data.data.ports || []
          this.loading = false
        })
        .catch((error) => {
          this.loading = false
          console.log(error)
        })
    },
    // 大小验证
    'ramValidate': function(rule, value, callback) {
      if (value < 1 || value > this.ramMax) {
        callback(new Error('输入范围：1-' + this.ramMax))
      }
      callback()
    },
    // 改变单位改变ram的可输入最大值
    'changeUnit': function(value) {
      if (value === 'MB') {
        this.ramMax = 10 * 1024 * 1024
      } else if (value === 'GB') {
        this.ramMax = 10 * 1024
      } else if (value === 'TB') {
        this.ramMax = 10
      }
    },
    'getUnitNum': function(unit) {
      let num = null
      switch (unit) {
        case 'MB':
          num = 1
          break
        case 'GB':
          num = 1024
          break
        case 'TB':
          num = 1024 * 1024
          break
      }
      return num
    },
    'portAdd': function() {
      this.formData.port.push({ 'name': '' })
    },
    'portDelete': function(index) {
      this.formData.port.splice(index, 1)
    },
    'close': function() {
      this.$emit('close')
    },
    'confirm': function() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          const postData = {
            'name': this.formData['name'],
            'cpu': this.formData['cpu'],
            'is_workstation': this.formData['is_workstation'],
            'console_type': this.formData['console_type'].join(','),
            'ram': this.formData['ram'] * this.getUnitNum(this.ramUnit),
            'sys_disk_size': this.formData['sys_disk_size'] || null,
            'admin_user': this.formData['admin_user'],
            'admin_pass': this.formData['admin_pass'],
            'description': this.formData['description']
          }
          this.$emit('call', 'configNode', postData)
          this.close()
        }
      })
    }
  }
}
</script>
<style lang="less">
.orchestration-drawer-wrap {
  .el-form {
    & >.el-form-item > .el-form-item__content > .el-input,
    & >.el-form-item > .el-form-item__content > .el-select,
    & >.el-form-item > .el-form-item__content > .el-textarea {
      width: 90%;
    }
  }
  .port-form {
    th {
      padding: 0;
    }
    >.el-form-item__label {
      width: 70px !important;
    }
    >.el-form-item__content {
      margin-left: 70px !important;
      .data-table-footer {
        display: none;
      }
    }
  }
}
</style>
