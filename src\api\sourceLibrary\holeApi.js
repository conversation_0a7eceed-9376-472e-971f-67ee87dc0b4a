import request from '@/utils/request'

// 查询漏洞库列表
export function listHole(query) {
  return request({
    url: '/accumulate/hole/list',
    method: 'get',
    params: query
  })
}

// 查询漏洞库详细
export function getHole(bugId) {
  return request({
    url: '/accumulate/hole/' + bugId,
    method: 'get'
  })
}

// 新增漏洞库
export function addHole(data) {
  return request({
    url: '/accumulate/hole',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

// 修改漏洞库
export function updateHole(data) {
  return request({
    url: '/accumulate/hole',
    method: 'put',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

// 删除漏洞库
export function delHole(bugId) {
  return request({
    url: '/accumulate/hole/' + bugId,
    method: 'delete'
  })
}

// 漏洞模板下载
export function holeExportFile() {
  return request({
    url: '/accumulate/hole/exportFile',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    responseType: 'blob'
  })
}

// 漏洞库导入
export function importFile(data) {
  return request({
    url: '/accumulate/hole/importFile',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

// CNNVD漏洞库导入
export function importCnnvdFile(data) {
  return request({
    url: '/accumulate/hole/importCnnvd',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
