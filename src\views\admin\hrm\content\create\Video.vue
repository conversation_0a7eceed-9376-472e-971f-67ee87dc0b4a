<template>
  <div class="flex-col wrapper">
    <el-upload
      v-if="editMode"
      :data="{ contentId: contentId, format: 'video' }"
      :headers="{ 'Admin-Token': token }"
      :show-file-list="false"
      :file-list="fileList"
      :on-preview="handlePreview"
      :http-request="httpRequest"
      :before-upload="handleUpload"
      :drag="editMode"
      :disabled="!editMode"
      :accept="accept"
      :multiple="multiple"
      class="upload-demo"
      action="/cepoTraining/api/training/content/uploadAttachment"
    >
      <div class="icon">
        <i class="icon-add iconfont" />
      </div>
      <div class="el-upload__text">
        点击此处或拖拽文件到此处上传文件
        <div class="el-upload__tip">
          文件仅支持: {{ fileTypes.join("、") }}，大小限制在{{ maxFileSize }}MB以内
        </div>
      </div>
    </el-upload>
    <el-row v-if="fileList.length != 0" :gutter="10">
      <el-col :span="editMode ? 24 : 6" style="height: 100%">
        <FileList
          v-model="currentFileIndex"
          :list="fileList"
          :deletable="editMode"
          :on-download="downloadVideo"
          @click="handlePreview"
          @delete="deleteFile"
        />
      </el-col>
      <el-col :span="editMode ? 24 : 18" style="height: 100%">
        <div class="grid-view">
          <video
            v-if="curriculumVideoUrl"
            id="showVideo"
            :height="editMode ? '80%' : '90%'"
            controlsList="nodownload"
            style="height: auto; overflow-y: scroll"
            width="100%"
            controls
          >
            <source :src="curriculumVideoUrl" type="video/mp4" >
          </video>
        </div>
      </el-col>
    </el-row>
    <el-empty
      v-if="fileList.length === 0"
      :image="img"
      :image-size="110"
      style="margin: 100px auto"
      description="暂无数据"
    />
  </div>
</template>
<script>
import {
  contentdetail,
  contentdetailDelete,
  uploadFileRequest
} from '@/api/teacher/index.js'
import mixin from './mixin'
import FileList from '@/components/FileList/index.vue'

export default {
  components: {
    FileList
  },
  mixins: [mixin],
  data() {
    return {
      multiple: false,
      fileTypes: ['mp4', 'webm'],
      curriculumVideoUrl: '',
      name: this.$route.query.name,
      token: '',
      fileList: [],
      maxFileSize: localStorage.getItem('maxFileSize'),
      fileName: '',
      currentFileIndex: 0
    }
  },
  computed: {
    accept() {
      const arr = this.fileTypes.map((type) => '.' + type)
      return arr.join(',')
    }
  },
  created() {
    this.token = JSON.parse(localStorage.getItem('Admin-Token')).data
    this.searchCurriculum()
  },
  methods: {
    onSuccess(res, file, fileList) {
      if (file) {
        this.$set(file, 'status', 'success')
        this.$set(file, 'attachmentId', res.data.id)
        this.$set(file, 'attachmentUrl', res.data.attachmentUrl)
        const url = window.location.origin + res.data.attachmentUrl
        this.$set(file, 'url', url)
        // 默认显示第一个附件
        const data = this.fileList[0]
        if (data) {
          this.handlePreview(file, 0)
        }
      }
    },
    httpRequest(file) {
      this.$set(file, 'status', 'uploading')
      this.$set(file, 'name', file.file.name)
      if (this.multiple) {
        this.fileList.push(file)
      } else {
        if (this.fileList[0]) {
          this.deleteFile(this.fileList[0], false)
        }
        this.fileList = [file]
      }
      // 处理上传进度事件
      const onUploadProgress = (e) => {
        if (e.total) {
          e.percentage = e.loaded / e.total * 100
          this.$set(file, 'percentage', e.percentage)
        }
      }
      this.$emit('call', 'uploading', true)
      const formData = new FormData()
      formData.append('file', file.file)
      formData.append('contentId', this.contentId)
      formData.append('format', 'video')
      formData.append('name', file.file.name)
      uploadFileRequest(formData, onUploadProgress)
        .then((res) => {
          if (res.code == 0) {
            this.onSuccess(res, file, this.fileList)
            this.$message({
              message: '上传成功',
              type: 'success'
            })
          } else {
            this.$set(file, 'status', 'error')
            this.$message({
              message: '上传失败',
              type: 'warning'
            })
          }
        }).finally(() => {
          this.$emit('call', 'uploading', false)
        })
    },
    searchCurriculum() {
      contentdetail({ contentId: this.contentId, format: 'video' }).then(
        (res) => {
          this.fileList = res.data
          this.fileList.forEach((item) => {
            item.name = item.fileName || item.attachmentUrl
            item.url = window.location.origin + item.attachmentUrl
          })
          // 默认显示第一个附件
          const file = this.fileList[0]
          if (file) {
            this.handlePreview(file, 0)
          }
        }
      )
    },
    handlePreview(file, index) {
      this.currentFileIndex = index
      this.curriculumVideoUrl = file.url
      document.getElementById('showVideo').src = file.url
      document.getElementById('showVideo').play()
    },
    handleUpload(file) {
      this.fileName = file.name
      const maxSize = this.maxFileSize * 1024 * 1024
      if (file.size > maxSize) {
        this.$message.warning(`上传文件不能超过${this.maxFileSize}MB!`)
        return false
      }
      const fileArr = file.name.split('.')
      const fileType = fileArr[fileArr.length - 1]
      if (!this.fileTypes.includes(fileType.toLowerCase())) {
        this.$message.error('不支持该文件类型')
        return false
      }
      const promise = new Promise((resolve) => {
        this.$nextTick(function() {
          resolve(true)
        })
      })
      return promise
    },
    // 删除文件
    deleteFile(item, showMessage = true) {
      if (item.status == 'success') {
        contentdetailDelete({ attachmentId: item.attachmentId }).then((res) => {
          if (res.code == 0) {
            if (showMessage) {
              this.$message.success('文件删除成功')
            }
            const index = this.fileList.findIndex(file => file.attachmentId == item.attachmentId)
            index > -1 && this.fileList.splice(index, 1)
          }
        })
      } else {
        // 删除未上传成功的文件
        const index = this.fileList.findIndex(file => file.uid == item.uid)
        index > -1 && this.fileList.splice(index, 1)
      }
    },
    // 下载视频文件
    downloadVideo(file) {
      if (file.url) {
        fetch(file.url, {
          method: 'get',
          responseType: 'blob'
        })
          .then((response) => response.blob())
          .then((blob) => {
            const a = document.createElement('a')
            const URL = window.URL || window.webkitURL
            const herf = URL.createObjectURL(blob)
            a.href = herf
            a.download = file.fileName || file.name
            document.body.appendChild(a)
            a.click()
            document.body.removeChild(a)
            URL.revokeObjectURL(herf)
          })
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.upload-demo {
  width: 100%;
  ::v-deep {
    .el-upload {
      width: 100%;
      .el-upload-dragger {
        width: 100%;
        height: 102px;
        overflow: hidden;
        .icon {
          margin: 26px 0 18px;
        }
      }
    }
    .el-upload__tip {
      font-size: 12px;
      font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
      font-weight: 400;
      color: #86909c;
    }
  }
}
</style>
