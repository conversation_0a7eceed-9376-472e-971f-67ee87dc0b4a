<template>
  <div class="content-header">
    <el-breadcrumb class="detail-breadcrumb" separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ name: $route.meta.parentName }">{{ $route.meta.parentTitle }}</el-breadcrumb-item>
      <el-breadcrumb-item>{{ $route.params.majorName }}</el-breadcrumb-item>
    </el-breadcrumb>
    <detail-header
      :config="{ name: $route.params.majorName, description: $route.params.des }"
      style="border-bottom: solid 1px var(--neutral-300);" />
  </div>
</template>
<script>
import detailHeader from '@/components/header/detail-header.vue'
export default {
  components: {
    detailHeader
  },
  data() {
    return {
      labelName: '',
      showName: ''
    }
  },
  created() { },
  methods: {

  }
}
</script>

<style lang="less" scoped>
.detail-breadcrumb {
  padding: 12px 24px;
  font-size: 12px;
  line-height: 1.5715;
  /deep/ .el-breadcrumb__inner.is-link, .el-breadcrumb__inner a {
    font-weight: normal;
    color: rgba(0, 0, 0, 0.45);
    &:hover {
      color: var(--color-600)
    }
  }
  /deep/ .el-breadcrumb__item:last-child .el-breadcrumb__inner {
    color: rgba(0, 0, 0, 0.85);
  }
}
</style>
