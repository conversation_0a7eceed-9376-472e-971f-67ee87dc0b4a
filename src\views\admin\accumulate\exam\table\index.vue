<template>
  <div class="resource-table">
    <!-- 操作区 -->
    <div class="operation-wrap">
      <div class="operation-left">
        <slot name="action" />
        <el-button type="primary" icon="el-icon-refresh" @click="refresh" />
      </div>
      <div class="operation-right">
        <el-badge :value="searchBtnShowNum">
          <el-button icon="el-icon-search" @click="openSearch">搜索</el-button>
        </el-badge>
        <!-- 自定义表格列 -->
        <t-table-config
          v-if="!customColData.length"
          :data="columnsObj"
          :active-key-arr="columnsViewArr"
          @on-change-col="onChangeCol"
        />
      </div>
    </div>
    <!-- 搜索区 -->
    <t-search-box
      v-show="searchView"
      :search-key-list="searchKeyListView"
      default-placeholder="默认搜索名称"
      @search="searchMultiple"
    />
    <!-- 列表 -->
    <t-table-view
      ref="tableView"
      :height="height"
      :single="single"
      :loading="tableLoading"
      :data="tableData"
      :total="tableTotal"
      :page-size="pageSize"
      :current="pageCurrent"
      :select-item="selectItem"
      @on-select="onSelect"
      @on-current="onCurrent"
      @on-change="changePage"
      @on-sort-change="onSortChange"
      @on-page-size-change="onPageSizeChange"
    >
      <el-table-column
        v-for="item in columnsViewArr"
        :key="item"
        :min-width="colMinWidth"
        :label="columnsObj[item].title"
        :fixed="columnsObj[item].master ? 'left' : false"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span v-if="item === 'examName' && link">
            <a
              v-if="showExamNameLink"
              :href="`/baseResource/exam/detail/${scope.row.id}/overview`"
              @click.prevent="linkEvent('examDetail', scope.row, { id: scope.row.id, view: 'overview' })"
            >{{ scope.row[item] || "-" }}</a>
            <span v-else>{{ scope.row[item] || "-" }}</span>
          </span>
          <span v-else-if="item == 'difficulty'">
            {{ moduleConf.difficultyObj[scope.row[item]].label }}
          </span>
          <span v-else-if="item == 'examType'">
            {{ moduleConf.examTypeObj[scope.row[item]].label }}
          </span>
          <span v-else-if="item == 'score' || item == 'questionNum' || item == 'suggestTime' || item == 'citedNum'">{{ scope.row[item] || 0 }}</span>
          <span v-else>{{ scope.row[item] || "-" }}</span>
        </template>
      </el-table-column>
    </t-table-view>
  </div>
</template>

<script>
import moduleConf from '../config.js'
import tSearchBox from '@/packages/search-box/index.vue'
import tTableView from '@/packages/table-view/index.vue'
import tTableConfig from '@/packages/table-config/table-col-config.vue'
import tableTdMultiCol from '@/packages/table-config/table-td-multi-col.vue'
import mixinsPageTable from '@/packages/mixins/page_table'
import { getExamList } from '@/api/accumulate/exam.js'

export default {
  components: {
    tSearchBox,
    tTableView,
    tTableConfig,
    tableTdMultiCol
  },
  mixins: [mixinsPageTable],
  props: {
    showExamNameLink: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      moduleConf: moduleConf,
      moduleName: moduleConf.name,
      // 搜索配置项
      searchKeyList: [
        { key: 'examName', label: '名称', master: true, placeholder: '默认搜索名称' },
        {
          key: 'difficulty',
          label: '难度',
          type: 'radio',
          valueList: moduleConf.difficultyArr
        },
        { key: 'createByName', label: '创建人' },
        {
          key: 'examType',
          label: '试卷类型',
          type: 'radio',
          valueList: moduleConf.examTypeArr
        },
        { key: 'time', label: '创建时间', type: 'time_range' }
      ],
      // 所有可配置显示列 master：不可隐藏 title:列名称
      columnsObj: {
        'examName': { title: '名称', master: true },
        'categoryName': { title: '分类' },
        'difficulty': { title: '难度' },
        'score': { title: '分数' },
        'questionNum': { title: '题目数量' },
        'suggestTime': { title: '建议时长（分钟）' },
        'examType': { title: '试卷类型' },
        'citedNum': { title: '引用次数' },
        'createByName': { title: '创建人' },
        'createTime': { title: '创建时间' }
      },
      // 当前显示列key表 默认，如果localStorage有数据将被覆盖
      columnsViewArr: [
        'examName',
        'categoryName',
        'difficulty',
        'score',
        'questionNum',
        'suggestTime',
        'examType',
        'citedNum',
        'createByName',
        'createTime'
      ],
      generateList: {
        '1': '初级',
        '2': '中级',
        '3': '高级'
      }
    }
  },
  created() {
    // 获取当前页面的URL参数
    const searchParams = new URLSearchParams(window.location.search)
    const examName = searchParams.get('examName')
    if (examName) {
      const obj = {
        data: {
          examName
        },
        key: 'exam'
      }
      this.$store.commit('SET_CACHE', obj)
    }
  },
  methods: {
    getList: function(showLoading = true) {
      if (showLoading) {
        this.tableLoading = true
      }
      const params = this.getPostData('page', 'limit')
      if (params.time) {
        params.createTimeBegin = params.time.split(',')[0]
        params.createTimeEnd = params.time.split(',')[1]
      }
      getExamList(params).then((res) => {
        if (res.code === 0) {
          this.tableData = res.data.records
          this.tableTotal = res.data.total
          this.tableLoading = false
          this.handleSelection()
        }
      }).catch(() => {
        this.tableLoading = false
      })
    }
  }
}
</script>
