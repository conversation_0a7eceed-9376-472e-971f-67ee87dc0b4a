<template>
  <div class="buttons-wrap">
    <el-button
      :disabled="singleDisabled"
      type="primary"
      @click="clickDrop('modalReleaseTopology')"
    >释放资源</el-button>
    <el-button
      v-permission="'manage.testing.sample.sampleList.sampleRemove'"
      :disabled="multipleDisabled"
      type="primary"
      @click="clickDrop('modalDelete')"
    >删除</el-button>
    <!-- 中部弹窗 start-->
    <el-dialog
      :title="titleMapping[modalName]"
      :visible.sync="modalShow"
      :width="modalWidth"
      destroy-on-close
      @close="modalClose"
    >
      <transition name="el-fade-in-linear">
        <component
          :is="modalName"
          :name="modalName"
          :data="selectItem"
          :num="num"
          @close="modalClose"
          @call="confirmCall"
        />
      </transition>
    </el-dialog>
    <!-- 中部弹窗 end-->
  </div>
</template>

<script>
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
import modalDelete from './modal-delete.vue'
import modalReleaseTopology from '@/views/admin/testing/testingItems/action/modal-releaseTopology.vue'
import { getResourcesCountAPI } from '@/api/testing/index.js'
export default {
  components: {
    modalDelete,
    modalReleaseTopology
  },
  mixins: [mixinsActionMenu],
  props: {
    moduleName: {
      type: String,
      default: ''
    },
    selectItem: {
      type: Array,
      default: () => []
    },
    page: {
      type: String,
      default: 'list'
    }
  },
  data() {
    return {
      // 弹窗title映射
      titleMapping: {
        'modalDelete': '删除',
        'modalReleaseTopology': '释放资源'
      },
      num: 0
    }
  },
  computed: {},
  methods: {
    getResourcesCount(name) {
      const params = {}
      params.id = this.selectItem[0].id
      getResourcesCountAPI(params).then((res) => {
        if (res.data.code === 0 || res.data.code === 200) {
          this.num = res.data.data
          this.modalName = name
        }
      })
    },
    confirmCall(type, data) {
      if (type === 'close') {
        this.modalClose()
      } else if (type === 'refresh') {
        this.$emit('call', type)
      } else {
        this.$emit('call', type, data)
      }
    },
    async clickDrop(name) {
      if (name === 'modalReleaseTopology') {
        this.getResourcesCount('modalReleaseTopology')
      } else {
        this.modalWidth = '520px'
        this.modalName = name
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.buttons-wrap {
  display: inline-block;
}
</style>
