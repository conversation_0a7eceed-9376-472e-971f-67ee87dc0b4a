<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogVisible"
    :show-close="true"
    :before-close="_cancel"
    :lock-scroll="false"
    :append-to-body="true"
    width="50%"
  >
    <slot/>
    <span slot="footer" class="dialog-footer">
      <el-button
        :disabled="forbidDisabled" type="primary" @click="submit"
      >确 认</el-button
      >
      <el-button @click="_cancel">取 消</el-button>
    </span>
  </el-dialog>
</template>

<script>
// import { myDebounce } from '@/common/utils.js'
export default {
  name: 'MyDialog',
  props: {
    title: {
      required: false,
      type: String
    },
    dialogVisible: {
      type: Boolean,
      required: true
    },
    // 取消按钮方法
    cancel: {
      type: Function,
      required: true,
      default: null
    },
    showClose: {
      type: Boolean,
      required: true
    },
    confirm: {
      // 确认
      type: Function,
      required: true,
      default: null
    },
    countFlag: {
      // 禁用5s
      type: Boolean,
      required: false,
      default: false
    }
  },
  data() {
    return {
      forbidDisabled: false
    }
  },
  watch: {
    dialogVisible(newVal) {
      if (newVal === true && this.countFlag) {
        this.forbidDisabled = true
        setTimeout(() => {
          this.forbidDisabled = false
        }, 5000)
      }
    }
  },
  methods: {
    _cancel() {
      this.cancel()
    },
    submit() {
      // myDebounce(this.confirm(), 3, false)
      this.confirm()
    }
    // confirm() {
    //   this.forbidDisabled = true
    //   this.dialogVisible = false
    //   setTimeout(() => {
    //     this.forbidDisabled = false
    //   }, 5000)
    // },
  }
}
</script>
<style lang="scss" scoped >
.dialog-footer {
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
