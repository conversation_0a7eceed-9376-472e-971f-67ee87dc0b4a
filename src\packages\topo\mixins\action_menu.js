export default {
  data() {
    return {
      modalWidth: '520px',
      modalShow: false,
      modalName: null,
      drawerWidth: '720px',
      drawerShow: false,
      drawerName: null
    }
  },
  computed: {
    // 单选禁用
    'singleDisabled': function() {
      return !this.selectItem.length || this.selectItem.length > 1
    },
    // 多选禁用
    'multipleDisabled': function() {
      return !this.selectItem.length
    }
  },
  watch: {
    // 监听模态框动态组件，如置空则关闭模态框
    'modalName': function(val) {
      this.modalShow = (!!val)
    },
    // 监听抽屉框动态组件，如置空则关闭模态框
    'drawerName': function(val) {
      this.drawerShow = (!!val)
    }
  },
  methods: {
    // 弹窗取消 覆盖mixins
    'modalClose': function() {
      if (this.modalName === 'configPort') {
        this.graph.removeCell(this.activeData.edge.id)
        this.resetEdgePosition(this.activeData.edge)
      }
      this.modalName = null
      this.modalWidth = '520px'
    },
    'drawerClose': function() {
      this.drawerName = null
      this.drawerWidth = '720px'
    },
    'clickDrop': function(name) {
      if (this.drawerAction && this.drawerAction.includes(name)) {
        this.drawerName = name
      } else {
        this.modalName = name
      }
    }
  }
}
