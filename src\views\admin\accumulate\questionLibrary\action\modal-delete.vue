<template>
  <div v-loading="loading" class="dialog-wrap">
    <batch-template
      :data="data"
      :available-data="availableArr"
      view-key="questionDepotName"
    />
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button :disabled="!availableArr.length" type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>

<script>
import moduleConf from '../config.js'
import batchTemplate from '@/packages/batch-delete/modal-bat-template.vue'
import { deleteQuestionDepot } from '@/api/accumulate/questionLibrary.js'

export default {
  components: {
    batchTemplate
  },
  props: {
    data: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      moduleName: moduleConf.name,
      loading: false
    }
  },
  inject: ['questionLibraryAction'],
  computed: {
    // 筛选返回可操作数据
    'availableArr': function() {
      const _data = []
      this.data.forEach((item, index) => {
        if (!item.isCitedQuestion) {
          _data.push(item)
        }
      })
      return _data
    }
  },
  methods: {
    close() {
      this.$emit('close')
    },
    confirm: function() {
      this.loading = true
      const postData = this.availableArr.map(item => item.id)
      deleteQuestionDepot(postData).then(res => {
        if (res.code == 0) {
          this.$message.success(`删除题库成功`)
          this.$bus.$emit(this.moduleName + '_module', 'reload')
          this.close()
        }
      }).finally(() => {
        this.loading = false
      })
    }
  }
}
</script>
