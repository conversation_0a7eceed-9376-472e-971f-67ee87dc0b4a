// 画布配置
const graphConfig = {
  autoResize: true, // 是否监听容器大小改变，并自动更新画布大小
  mousewheel: { // 滚轮缩放画布
    enabled: true,
    modifiers: ['ctrl', 'meta'],
    minScale: 0.5,
    maxScale: 3
  },
  background: {
    // color: '#F2F7FA',
    color: '#fff'
  },
  grid: { // 网格
    visible: true,
    type: 'doubleMesh',
    args: [
      {
        color: '#eee', // 主网格线颜色
        thickness: 1 // 主网格线宽度
      },
      {
        color: '#ddd', // 次网格线颜色
        thickness: 1, // 次网格线宽度
        factor: 4 // 主次网格线间隔
      }
    ]
  },
  highlighting: false,
  connecting: {
    allowBlank: false, // 连接到画布空白位置的点
    allowLoop: false, // 循环连线
    allowNode: true, // 连接到节点
    allowEdge: false, // 边链接到另一个边
    allowPort: false, // 边链接到连接桩
    allowMulti: true, // 在相同的起始节点和终止之间创建多条边
    sourceAnchor: { // 源节点锚点
      name: 'nodeCenter',
      args: { dy: -10 }
    },
    targetAnchor: { // 目标节点锚点
      name: 'nodeCenter',
      args: { dy: -10 }
    },
    connectionPoint: { // 使用锚点作为连接点
      // name: 'boundary'
      name: 'anchor'
    },
    snap: { radius: 30 } // 自动吸附半径
  }
}
// 连线配置
const edgeConfig = {
  shape: 'edge',
  connector: {
    name: 'normal'
  },
  attrs: {
    line: {
      stroke: '#37668c',
      strokeWidth: 1,
      targetMarker: false
    }
  },
  defaultLabel: {
    markup: [
      {
        tagName: 'rect',
        selector: 'bg'
      },
      {
        tagName: 'text',
        selector: 'text'
      }
    ],
    attrs: {
      text: {
        fill: '#37668c',
        fontSize: 10,
        textAnchor: 'middle',
        textVerticalAnchor: 'middle',
        visibility: 'hidden'
      },
      bg: {
        ref: 'text',
        fill: '#fff',
        rx: 8,
        ry: 8,
        refWidth: 4,
        refHeight: 1,
        refX: -2,
        refY: 0,
        stroke: '#37668c',
        strokeWidth: 1,
        visibility: 'hidden'
      }
    }
  }
}
// 连线标签配置
const labelConfig = [
  {
    'attrs': {
      'text': {
        'text': '',
        'fill': '#37668c',
        'fontWeight': 'normal'
      },
      'bg': {
        'stroke': '#37668c',
        'strokeWidth': 1
      }
    },
    'position': 0.25
  },
  {
    'attrs': {
      'text': {
        'text': '',
        'fill': '#37668c',
        'fontWeight': 'normal'
      },
      'bg': {
        'stroke': '#37668c',
        'strokeWidth': 1
      }
    },
    'position': 0.75
  }
]
// 连接桩配置
const portsConfig = {
  groups: {
    bottom: {
      position: [0.5, 43],
      attrs: {
        circle: {
          r: 7,
          magnet: true,
          style: {
            opacity: 0,
            visibility: 'hidden'
          }
        }
      }
    }
  },
  items: [
    { group: 'bottom' }
  ]
}
// 导出的stylesheet
const stylesheet = `
.x6-edge-selected path, .x6-edge-selected rect {
  stroke: #37668c;
  stroke-width: 2;
}
.x6-edge-selected text {
  fill: #37668c;
  font-weight: normal;
}
.custom-node {
  margin-top: -8px;
  margin-left: -8px;
  height: 70px;
  display: flex;
  flex-direction: column;
  justify-content: start;
  align-items: center;
  cursor: pointer;
}
.custom-node .custom-node-icon {
  display: block;
  height: 50px;
  position: relative;
  text-align: center;
}
.custom-node .el-image {
  position: relative;
  display: inline-block;
  overflow: hidden;
  width: 50px;
  height: 50px;
}
.custom-node img {
  width: 50px;
  height: 50px;
}
.custom-node strong {
  font-family: 'MicrosoftYaHei', 'Avenir', Helvetica, Arial, sans-serif;
  text-align: center;
  display: block;
  font-size: 13px;
  font-weight: 100;
  word-break: break-all;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.6);
}
.custom-node .custom-status-icon {
  display: none;
}
.custom-node .custom-port-icon {
  display: none;
}
`
export default {
  name: 'orchestration',
  graphConfig, edgeConfig, labelConfig, portsConfig, stylesheet
}
