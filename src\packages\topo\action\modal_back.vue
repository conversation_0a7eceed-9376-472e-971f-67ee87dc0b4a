<template>
  <div v-loading="loading" class="dialog-wrap">
    <el-alert type="warning">
      <div slot="title">
        <p>画布有内容未保存，是否仍然要离开画布？</p>
      </div>
    </el-alert>
    <div class="dialog-footer">
      <el-button type="primary" @click="confirm(false)">仍然离开</el-button>
      <el-button type="primary" @click="confirm(true)">保存并返回</el-button>
    </div>
  </div>
</template>

<script>
import modalMixins from '../../mixins/modal_form'
export default {
  mixins: [modalMixins],
  props: {
    // 传入数据
    data: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      loading: false
    }
  },
  methods: {
    // modal点击确定
    'confirm': function(save) {
      this.$emit('call', 'back', save)
      this.close()
    },
    close() {
      this.$emit('close')
    }
  }
}
</script>
