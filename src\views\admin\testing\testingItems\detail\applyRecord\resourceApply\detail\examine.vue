<template>
  <create-view :loading="loading" :title="title">
    <div slot="content">
      <el-form ref="form" :model="formData" :rules="rules" label-position="left" label-width="150px">
        <el-card class="info-card">
          <el-divider content-position="left">项目信息</el-divider>

          <div class="info-row">
            <div class="info-item">
              <span class="label">项目名称：</span>
              <span class="value">{{ applyDetail.projectName || '-' }}</span>
            </div>
            <div class="info-item">
              <span class="label">厂商：</span>
              <span class="value">{{ applyDetail.vendorName || '-' }}</span>
            </div>
          </div>

          <div class="info-row">
            <div class="info-item">
              <span class="label">检测产品：</span>
              <span class="value">{{ applyDetail.productName || '-' }}</span>
            </div>
            <div class="info-item">
              <span class="label">版本号：</span>
              <span class="value">{{ applyDetail.productVersion }}</span>
            </div>
          </div>
        </el-card>


        <el-card v-if="applyDetail.vendorStatus === 1" class="form-card">
          <el-divider content-position="left">检测范围</el-divider>

          <el-form-item label="测试任务" prop="testingTasks">
            <el-tree
              ref="testingTaskTree"
              :data="testingTasksData"
              :props="testingTaskProps"
              show-checkbox
              node-key="id"
              default-expand-all
            />
          </el-form-item>
        </el-card>

        <el-card class="form-card">
          <el-divider content-position="left">环境信息</el-divider>
          <div v-if="!disableDetail" class="tip-info"><i class="el-icon-warning-outline"/><span style="margin-left: 4px;">请审核测试环境信息，并标记“审核结果”。</span></div>
          <div class="env-tabs">
            <div class="label-block">检测环境：</div>
            <div class="tab-buttons">
              {{ deployType === 'local' ? '本平台部署' : '外部部署' }}
            </div>
          </div>

          <!-- 本平台部署 -->
          <div v-if="deployType === 'local'" class="deploy-content">
            <div class="env-tabs">
              <div class="label-block">资源申请：</div>
              <div class="tab-buttons">
                {{ resourceType === 'virtual' ? '虚拟机资源' : '网络编排' }}
              </div>
            </div>

            <!-- 虚拟资源 -->
            <div v-if="resourceType === 'virtual'" class="resource-content">
              <div class="section-header">
                <span>虚拟机配置</span>
              </div>
              <!-- 虚拟机配置列表 -->
              <div class="resource-table" style="padding: 0; min-height: 332px;">
                <t-table-view
                  ref="tableView"
                  :height="height"
                  :single="single"
                  :loading="tableLoading"
                  :data="currentVirtualMachines"
                  :total="tableTotal"
                  :current="vmCurrentPage"
                  :page-size="vmPageSize"
                  :multiple-page="true"
                  :select-item="selectItem"
                  type="list"
                  current-key="id"
                  @on-select="onSelect"
                  @on-current="onCurrent"
                  @on-change="changeVmPage"
                  @on-sort-change="onSortChange"
                  @on-page-size-change="onVmPageSizeChange"
                >
                  <el-table-column
                    v-for="item in virtualColumnsViewArr"
                    :key="item"
                    :min-width="colMinWidth"
                    :width="virtualColumnsObj[item].colWidth"
                    :label="virtualColumnsObj[item].title"
                    :fixed="virtualColumnsObj[item].master ? 'left' : false"
                    show-overflow-tooltip
                  >
                    <template slot-scope="scope">
                      <div v-if="item == 'memory'">
                        {{ scope.row.memory ? `${scope.row.memory} ${scope.row.memUnit}` : '-' }}
                      </div>
                      <div v-else-if="item == 'workstation'">
                        <span v-if="scope.row.workstation === '1'">是</span>
                        <span v-else-if="scope.row.workstation === '0'">否</span>
                        <span v-else>-</span>
                      </div>
                      <span v-else>{{ scope.row[item] || "-" }}</span>
                    </template>
                  </el-table-column>
                </t-table-view>
              </div>
            </div>

            <!-- 网络编排 -->
            <div v-if="resourceType === 'network'" class="resource-content">
              <el-form-item label="仿真场景：" label-width="100px" prop="scenarioId" class="required-item">
                <el-tag
                  v-if="formData.scenarioId"
                  :disable-transitions="true"
                  style="margin-top: 5px;"
                  @click="drawerName = 'selectScenario'"
                  @close="formData.scenarioId = null">
                  {{ formData.scenarioName }}
                </el-tag>
              </el-form-item>

              <el-form-item label="场景描述：" label-width="100px" prop="scenarioDescription" class="required-item">
                <div v-if="formData.scenarioDescription" style="white-space: pre-line; line-height: 24px; padding-top: 8px;" v-html="formData.scenarioDescription" />
                <div v-else>-</div>
              </el-form-item>
            </div>
          </div>

          <!-- 外部部署 -->
          <div v-if="deployType === 'external'" class="deploy-content">
            <div class="section-header">
              <span>设备信息</span>
            </div>
            <!-- 设备配置列表 -->
            <div class="resource-table" style="padding: 0; min-height: 332px;">
              <t-table-view
                ref="device-tableView"
                :height="height"
                :single="single"
                :loading="tableLoading"
                :data="currentDevices"
                :total="deviceTableTotal"
                :current="deviceCurrentPage"
                :page-size="devicePageSize"
                :select-item="selectItem"
                type="list"
                current-key="id"
                @on-select="onSelect"
                @on-current="onCurrent"
                @on-change="changeDevicePage"
                @on-sort-change="onSortChange"
                @on-page-size-change="onDevicePageSizeChange"
              >
                <el-table-column
                  v-for="item in deviceColumnsViewArr"
                  :key="item"
                  :min-width="colMinWidth"
                  :width="deviceColumnsObj[item].colWidth"
                  :label="deviceColumnsObj[item].title"
                  :fixed="deviceColumnsObj[item].master ? 'left' : false"
                  show-overflow-tooltip
                >
                  <template slot-scope="scope">
                    <div v-if="item == 'index'">
                      {{ scope.$index + 1 }}
                    </div>
                    <div v-else-if="item == 'credentials'">
                      <div class="flex jc-between ai-center">
                        <div class="ellipsis overflow-tooltip">
                          <div class="ellipsis">{{ scope.row.credentials[0] || "-" }}</div>
                        </div>
                        <CountPopover :list="scope.row.credentials" />
                      </div>
                    </div>
                    <span v-else>{{ scope.row[item] || "-" }}</span>
                  </template>
                </el-table-column>
              </t-table-view>
            </div>
          </div>
          <!-- 外部部署附件 -->
          <el-form-item v-if="deployType === 'external'" label="" label-width="100px">
            <span slot="label">附件：
              <el-tooltip transfer placement="top">
                <i class="el-icon-warning-outline" style="color: var(--neutral-700);" />
                <div slot="content">详细的系统访问信息</div>
              </el-tooltip>
            </span>
            <div
              v-if="envAttachments && envAttachments.length"
              class="attachment-list"
            >
              <div
                v-for="(item, fileIndex) in envAttachments"
                :key="fileIndex"
                class="attachment-item mb-10"
              >
                <i class="el-icon-document" size="16" />
                <div class="attachment-name">{{ item.name }}（{{ formatFileSize(item.size) }}）</div>
                <div class="attachment-actions">
                  <el-button
                    class="primary"
                    type="text"
                    icon="el-icon-view"
                    @click="handleFileView(item)"
                  />
                  <el-button
                    class="primary"
                    type="text"
                    icon="el-icon-download"
                    @click="handleDownload(item)"
                  />
                </div>
              </div>
            </div>
            <span v-else>-</span>
          </el-form-item>
          <div class="env-tabs">
            <div class="label-block">审核结果：<span style="color: #f56c6c; margin-left: 4px;">*</span></div>
            <div class="tab-buttons">
              <el-button
                :type="deployType2 === 'local' ? 'primary' : ''"
                :disabled="deployType2 === 'local' && disableDetail ? true : false"
                @click="handleEnvStatusChange('local')">通过</el-button>
              <el-button
                :type="deployType2 === 'external' ? 'primary' : ''"
                :disabled="deployType2 === 'external' && disableDetail ? true : false"
                @click="handleEnvStatusChange('external')">不通过</el-button>
            </div>
          </div>

          <div v-if="auditEnvStatus === 2">
            <el-form-item label="原因：" label-width="100px" prop="rejectReason">
              <el-input :rows="4" v-model="formData.rejectReason" placeholder="请输入" style="width: 100%;" maxlength="1000" type="textarea"/>
            </el-form-item>
          </div>

          <!-- 网络选择 - 当选择本平台部署+虚拟机资源+审核通过时显示 -->
          <div v-if="globalNetworkEnable && deployType === 'local' && resourceType === 'virtual' && deployType2 === 'local'">
            <el-form-item :rules="[{ required: true, message: '请选择网络', trigger: 'change' }]" label-width="100px" label="选择网络：" prop="networkId">
              <el-select
                v-model="formData.networkId"
                filterable
                clearable
                placeholder="请选择网络"
                style="width: 155px;">
                <el-option
                  v-for="network in networkList"
                  :key="network.networkId"
                  :label="network.networkName"
                  :value="network.networkId"/>
              </el-select>
            </el-form-item>
          </div>
        </el-card>

        <el-card class="form-card" style="margin-bottom: 0;">
          <el-divider content-position="left">附件</el-divider>
          <div v-if="!disableDetail" class="tip-info"><i class="el-icon-warning-outline"/><span style="margin-left: 4px;">请审核各个测试任务下的附件，并标记"审核结果"。</span></div>
          <div class="attachment-section">
            <div v-if="processTasks.length > 0" class="switch-group">
              <radio-group
                v-model="activeAttachmentType"
                :options="processTasks"
                label-key="processName"
                value-key="id"
                @change="val => activeAttachmentType = val"
              />
            </div>

            <div class="resource-table" style="padding: 0; min-height: 332px;">
              <!-- 附件列表 -->
              <t-table-view
                ref="file-tableView"
                :height="height"
                :single="single"
                :loading="tableLoading"
                :data="currentAttachments"
                :total="fileTableTotal"
                :current="fileCurrentPage"
                :page-size="filePageSize"
                :select-item="selectItem"
                type="list"
                current-key="id"
                @on-select="onSelect"
                @on-current="onCurrent"
                @on-change="changeFilePage"
                @on-sort-change="onSortChange"
                @on-page-size-change="onFilePageSizeChange"
              >
                <el-table-column
                  v-for="item in fileColumnsViewArr"
                  :key="item"
                  :min-width="colMinWidth"
                  :width="fileColumnsObj[item].colWidth"
                  :label="fileColumnsObj[item].title"
                  :fixed="fileColumnsObj[item].master ? 'left' : false"
                  show-overflow-tooltip
                >
                  <template slot-scope="scope">
                    <div v-if="item == 'index'">
                      {{ scope.$index + 1 }}
                    </div>
                    <div v-else-if="item == 'fileSize'">
                      {{ formatFileSize(scope.row.fileSize) }}
                    </div>
                    <div v-else-if="item == 'handle'">
                      <el-button style="color:var(--color-600)" type="text" size="small" @click="handleFileView(scope.row)">查看</el-button>
                      <el-button style="color:var(--color-600)" type="text" size="small" @click="handleDownload(scope.row)">下载</el-button>
                    </div>
                    <span v-else>{{ scope.row[item] || "-" }}</span>
                  </template>
                </el-table-column>
              </t-table-view>
            </div>
          </div>
          <div class="env-tabs">
            <div class="label-block">审核结果：<span style="color: #f56c6c; margin-left: 4px;">*</span></div>
            <div class="tab-buttons">
              <el-button
                :type="deployType3 === 'local' ? 'primary' : ''"
                :disabled="deployType3 === 'local' && disableDetail ? true : false"
                @click="handleAttachmentStatusChange('local')">通过</el-button>
              <el-button
                :type="deployType3 === 'external' ? 'primary' : ''"
                :disabled="deployType3 === 'external' && disableDetail ? true : false"
                @click="handleAttachmentStatusChange('external')">不通过</el-button>
            </div>
          </div>

          <div v-if="auditAttachmentStatus === 2">
            <el-form-item label="原因：" label-width="100px" prop="fileRejectReason">
              <el-input :rows="4" v-model="formData.fileRejectReason" placeholder="请输入" style="width: 100%;" maxlength="1000" type="textarea"/>
            </el-form-item>
          </div>
        </el-card>
      </el-form>
    </div>
    <div slot="footer">
      <el-button type="text" @click="$router.go(-1)">取消</el-button>
      <template v-if="disableDetail">
        <el-button type="primary" @click="$router.go(-1)">确定</el-button>
      </template>
      <template v-else>
        <el-button type="primary" @click="handleSubmitConfirm">确定</el-button>
      </template>
    </div>
  </create-view>
</template>

<script>
import {
  auditResourceApplyAPI,
  getNetworkListAPI,
  getResourceApplyDetailAPI,
  processTaskAPI,
  queryAttachmentsAPI
} from '@/api/testing/index'
import CountPopover from '@/components/CountPopover/index'
import radioGroup from '@/components/commonRadioGroup/index.vue'
import filePreview from '@/components/testing/utils/filePreview'
import createView from '@/packages/create-view/index'
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
import mixinsPageTable from '@/packages/mixins/page_table'
import tTableConfig from '@/packages/table-config/table-col-config.vue'
import tTableView from '@/packages/table-view/index.vue'
import validate from '@/packages/validate/index'
import listModule from './config'
export default {
  name: 'TestingItemsSubmit',
  components: {
    createView,
    tTableView,
    tTableConfig,
    CountPopover,
    radioGroup
  },
  mixins: [mixinsActionMenu, filePreview, mixinsPageTable],
  data() {
    return {
      loading: false,
      deployType: 'local', // local-本平台部署, external-外部部署
      resourceType: 'virtual', // virtual-虚拟资源, network-网络编排
      activeAttachmentType: 678708226361989, // 使用第一个任务的ID作为初始值
      activeAttachmentTypeList: [],
      formData: {
        id: '',
        deployType: 'local',
        resourceType: 'virtual',
        devices: [],
        virtualMachines: [],
        scenarioId: '',
        scenarioName: '',
        scenarioCategory: '',
        scenarioDescription: '',
        rejectReason: '', // 环境审核不通过原因
        fileRejectReason: '', // 附件审核不通过原因
        networkId: '', // 网络ID
        testingTasks: [] // 选中的检测任务ID
      },
      rules: {
        rejectReason: [validate.required()],
        fileRejectReason: [validate.required()]
      },
      // 虚拟机分页
      vmCurrentPage: 1,
      vmPageSize: 10,
      currentVirtualMachines: [],

      // 设备分页
      deviceCurrentPage: 1,
      devicePageSize: 10,
      currentDevices: [],

      // 附件分页
      fileCurrentPage: 1,
      filePageSize: 10,
      currentAttachments: [],

      processTasks: [],
      displayTasks: [],
      testingTasksData: [], // 检测任务树形数据
      testingTaskProps: {
        label: 'name',
        children: 'children'
      },
      applyId: '',
      applyDetail: {}, // 资源申请详情数据

      // 审核状态
      deployType2: 'local', // 环境审核状态，默认通过
      deployType3: 'local', // 附件审核状态，默认通过
      auditStatus: 1, // 总体审核状态，默认通过
      auditEnvStatus: 1, // 环境审核状态，默认通过
      auditAttachmentStatus: 1, // 附件审核状态，默认通过
      auditComment: '', // 审核意见
      networkDialogVisible: false,
      networkList: [], // 网络列表
      title: '',
      disableDetail: false,
      virtualColumnsObj: listModule.virtualColumnsObj,
      virtualColumnsViewArr: listModule.virtualColumnsViewArr,
      deviceColumnsObj: listModule.deviceColumnsObj,
      deviceColumnsViewArr: listModule.deviceColumnsViewArr,
      fileColumnsObj: listModule.fileColumnsObj,
      fileColumnsViewArr: listModule.fileColumnsViewArr,
      globalNetworkEnable: 0,
      envAttachments: []
    }
  },
  computed: {
    // 判断是否禁用"审核通过"按钮 - 当任一审核结果为不通过时禁用
    disableApproveButton() {
      return this.deployType2 === 'external' || this.deployType3 === 'external'
    },

    // 判断是否禁用"审核不通过"按钮 - 当所有审核结果都为通过时禁用
    disableRejectButton() {
      return this.deployType2 === 'local' && this.deployType3 === 'local'
    }
  },
  watch: {
    activeAttachmentType(newVal) {
      this.activeAttachmentTypeList = []
      this.activeAttachmentTypeList = this.applyDetail.attachments.filter(item => item.typeId == newVal)
      this.fileTableTotal = this.activeAttachmentTypeList.length || 0
      this.fileCurrentPage = 1
      this.updateCurrentAttachments()
    },
    'formData.virtualMachines': {
      handler(newVal) {
        this.tableTotal = newVal.length || 0
        this.updateCurrentVirtualMachines()
      },
      deep: true
    },
    'formData.devices': {
      handler(newVal) {
        this.deviceTableTotal = newVal.length || 0
        this.updateCurrentDevices()
      },
      deep: true
    },
    'activeAttachmentTypeList': {
      handler(newVal) {
        this.fileTableTotal = newVal.length || 0
        this.updateCurrentAttachments()
      },
      deep: true
    },
    vmCurrentPage() {
      this.updateCurrentVirtualMachines()
    },
    vmPageSize() {
      this.updateCurrentVirtualMachines()
    },
    deviceCurrentPage() {
      this.updateCurrentDevices()
    },
    devicePageSize() {
      this.updateCurrentDevices()
    },
    fileCurrentPage() {
      this.updateCurrentAttachments()
    },
    filePageSize() {
      this.updateCurrentAttachments()
    }
  },
  created() {
    this.getProjectDetail()
    this.getNetworkList()
  },
  methods: {
    // 虚拟机分页方法
    updateCurrentVirtualMachines() {
      const startIndex = (this.vmCurrentPage - 1) * this.vmPageSize
      const endIndex = startIndex + this.vmPageSize
      this.currentVirtualMachines = this.formData.virtualMachines.slice(startIndex, endIndex)
    },
    changeVmPage(page) {
      this.vmCurrentPage = page
    },
    onVmPageSizeChange(size) {
      this.vmPageSize = size
      this.vmCurrentPage = 1
    },

    // 设备分页方法
    updateCurrentDevices() {
      const startIndex = (this.deviceCurrentPage - 1) * this.devicePageSize
      const endIndex = startIndex + this.devicePageSize
      this.currentDevices = this.formData.devices.slice(startIndex, endIndex)
    },
    changeDevicePage(page) {
      this.deviceCurrentPage = page
    },
    onDevicePageSizeChange(size) {
      this.devicePageSize = size
      this.deviceCurrentPage = 1
    },

    // 附件分页方法
    updateCurrentAttachments() {
      const startIndex = (this.fileCurrentPage - 1) * this.filePageSize
      const endIndex = startIndex + this.filePageSize
      this.currentAttachments = this.activeAttachmentTypeList.slice(startIndex, endIndex)
    },
    changeFilePage(page) {
      this.fileCurrentPage = page
    },
    onFilePageSizeChange(size) {
      this.filePageSize = size
      this.fileCurrentPage = 1
    },

    // 获取网络列表
    getNetworkList() {
      getNetworkListAPI().then(res => {
        if (res.data && res.data.code === 0) {
          this.networkList = res.data.data || []
        } else {
          this.$message.error(res.data.msg || '获取网络列表失败')
        }
      }).catch(error => {
        console.error('获取网络列表失败:', error)
        this.$message.error('获取网络列表失败')
      })
    },
    async getProjectDetail() {
      this.applyId = this.$route.params.id
      const id = this.$route.params.projectId
      this.projectId = id
      this.formData.id = id

      // 获取资源申请详情
      await getResourceApplyDetailAPI(this.applyId).then(res => {
        if (res.data && res.data.code === 0) {
          // 保存申请详情数据
          this.applyDetail = res.data.data || {}
          // 根据申请详情设置环境类型和资源类型
          if (this.applyDetail && this.applyDetail.projectEnvVO) {
            this.deployType = this.applyDetail.projectEnvVO.envType === 0 ? 'local' : 'external'
            this.globalNetworkEnable = this.applyDetail.projectEnvVO.globalNetworkEnable
            // 外部部署添加的附件
            if (this.applyDetail.projectEnvVO.envAttachments && this.applyDetail.projectEnvVO.envType === 1) {
              this.envAttachments = this.applyDetail.projectEnvVO.envAttachments.map(item => {
                this.$set(item, 'fileName', item.name)
                this.$set(item, 'fileUrl', item.path)
                return item
              })
            }
          }
          if (this.applyDetail.projectEnvVO.resourceType !== undefined) {
            if (this.applyDetail.projectEnvVO.resourceType === 0) {
              this.resourceType = 'virtual'
            } else if (this.applyDetail.projectEnvVO.resourceType === 1) {
              this.resourceType = 'network'
            }
          }
          // 设置场景信息
          if (this.applyDetail.projectEnvVO.projectEnvSceneVO) {
            this.formData.scenarioId = this.applyDetail.projectEnvVO.projectEnvSceneVO.sceneId || ''
            this.formData.scenarioName = this.applyDetail.projectEnvVO.projectEnvSceneVO.sceneName || ''
            this.formData.scenarioDescription = this.applyDetail.projectEnvVO.projectEnvSceneVO.description || ''
            // 设置网络ID（如果有）
            this.formData.networkId = this.applyDetail.projectEnvVO.projectEnvSceneVO.networkId || ''
          }
          if (this.applyDetail.projectEnvVO && Array.isArray(this.applyDetail.projectEnvVO.projectEnvDeviceVOList)) {
            const formattedDevices = this.applyDetail.projectEnvVO.projectEnvDeviceVOList.map(device => {
            // 处理账号信息，转换为显示格式
              const credentials = (device.deviceAccountVOList || [])
                .map(acc => `${acc.username} / ${acc.password}`)
              return {
                id: device.id || '',
                deviceName: device.name || '',
                ipAddress: device.target || '',
                port: device.targetPort || '',
                credentials: credentials,
                notes: device.remark || ''
              }
            })
            this.formData.devices = formattedDevices
            this.deviceTableTotal = this.formData.devices.length || 0
            this.updateCurrentDevices()
          } else {
            this.formData.devices = []
            this.deviceTableTotal = 0
          }
          if (this.applyDetail.projectEnvVO.projectEnvVmVOList) {
            const formattedVMs = this.applyDetail.projectEnvVO.projectEnvVmVOList.map(vm => ({
              id: vm.id || '',
              deviceName: vm.deviceName || '',
              imageName: vm.imageName || '',
              cpu: vm.cpuCount || '',
              memory: vm.memSize || '',
              memUnit: vm.memUnit || '',
              system: vm.systemDiskSize || '',
              data: vm.dataDiskSize || ''
            }))
            this.formData.virtualMachines = formattedVMs
            this.tableTotal = this.formData.virtualMachines.length || 0
            this.updateCurrentVirtualMachines()
          }

          // 设置附件信息
          if (this.applyDetail.attachments && Array.isArray(this.applyDetail.attachments)) {
            this.activeAttachmentTypeList = this.applyDetail.attachments.filter(item => item.typeId == this.activeAttachmentType)
            this.fileTableTotal = this.activeAttachmentTypeList.length || 0
            this.updateCurrentAttachments()
          }
        }
      }).catch(() => {

      })

      // 存储项目详情数据
      const taskRes = await processTaskAPI(this.applyId)
      // 处理任务数据
      const tasks = taskRes.data.data || []
      // 待审核和拒绝状态时，附件取检测任务的附件 1已通过  0待评审 2已拒绝
      if (this.applyDetail.auditStatus !== 1) {
        tasks.map((item) => {
          item.processName = item.name
          item.id = item.sourceTaskId
        })
      } else {
        tasks.map((item) => {
          item.processName = item.name
        })
      }
      if (this.applyDetail.auditStatus !== 0) {
        this.title = '查看详情'
        this.disableDetail = true
        this.deployType2 = this.applyDetail.auditEnvStatus === 1 ? 'local' : 'external'
        this.deployType3 = this.applyDetail.auditAttachmentStatus === 1 ? 'local' : 'external'
        // 设置原因信息
        this.formData.rejectReason = this.applyDetail.rejectReason || ''
        this.formData.fileRejectReason = this.applyDetail.fileRejectReason || ''
      } else {
        this.title = '审核项目资料'
        this.disableDetail = false
      }
      this.processTasks = this.getDisplayTasks(tasks)
      this.activeAttachmentType = this.processTasks.length > 0 ? this.processTasks[0].id : null
      this.testingTasksData = tasks
      // this.getAttachments(this.activeAttachmentType)
      this.loading = false
    },
    getDisplayTasks(tasks) {
      const result = []
      tasks.forEach(task => {
        if (Array.isArray(task.children) && task.children.length > 0) {
          task.children.forEach(child => {
            result.push({ id: child.sourceTaskId || child.id, processName: child.name })
          })
        } else {
          result.push({ id: task.sourceTaskId || task.id, processName: task.name })
        }
      })
      return result
    },
    // 获取附件列表
    getAttachments(taskId) {
      const params = {
        page: 1,
        limit: 999,
        taskId: taskId,
        projectId: this.projectId
      }
      queryAttachmentsAPI(params).then(res => {
        if (res.data.code === 0) {
          this.activeAttachmentTypeList = (res.data && res.data.data) ? res.data.data.records : []
          this.fileTableTotal = res.data.data.total || 0
        }
      })
    },

    // 处理环境审核状态变更
    handleEnvStatusChange(type) {
      if (this.disableDetail) return
      this.deployType2 = type
      this.auditEnvStatus = type === 'local' ? 1 : 2 // local=通过, external=不通过
    },

    // 处理附件审核状态变更
    handleAttachmentStatusChange(type) {
      if (this.disableDetail) return
      this.deployType3 = type
      this.auditAttachmentStatus = type === 'local' ? 1 : 2 // local=通过, external=不通过
    },

    // 确认提交前的验证
    handleSubmitConfirm() {
      this.$refs.form.validate(valid => {
        if (!valid) return
        // 检查是否需要选择网络
        if (
          this.globalNetworkEnable &&
          this.deployType === 'local' &&
          this.resourceType === 'virtual' &&
          !this.formData.networkId &&
          this.auditEnvStatus === 1
        ) {
          this.$message.warning('请先选择网络')
          return
        }

        // 确定审核状态
        let status = 1 // 默认通过
        if (this.auditEnvStatus === 2 || this.auditAttachmentStatus === 2) {
          status = 2 // 如果有任一不通过，则整体不通过
        }

        // 提交审核
        this.handleSubmit(status)
        // ...原有网络校验和提交逻辑...
      })
    },

    // 提交审核
    handleSubmit(status) {
      // 设置总体审核状态
      this.auditStatus = status

      this.loading = true

      // 构建请求参数
      const params = {
        applyId: parseInt(this.applyId),
        auditStatus: this.auditStatus,
        auditEnvStatus: this.auditEnvStatus,
        auditAttachmentStatus: this.auditAttachmentStatus,
        auditComment: this.auditComment,
        rejectReason: this.formData.rejectReason || '',
        fileRejectReason: this.formData.fileRejectReason || '',
        networkId: this.formData.networkId
      }

      // 调用审核API
      auditResourceApplyAPI(this.applyId, params).then(res => {
        if (res.data && res.data.code === 0) {
          this.$message.success('审核操作成功')
          this.loading = false
          this.$router.go(-1)
        } else {
          this.$message.error(res.data.msg || '审核操作失败')
          this.loading = false
        }
      }).catch(error => {
        console.error('审核操作失败:', error)
        this.$message.error('审核操作失败')
        this.loading = false
      })
    },
    handleDownload(item) {
      // 下载附件
      if (item.fileUrl) {
        fetch(item.fileUrl, {
          method: 'get',
          responseType: 'blob'
        })
          .then((response) => response.blob())
          .then((blob) => {
            const a = document.createElement('a')
            const URL = window.URL || window.webkitURL
            const href = URL.createObjectURL(blob)
            a.href = href
            a.download = item.fileName
            document.body.appendChild(a)
            a.click()
            document.body.removeChild(a)
            URL.revokeObjectURL(href)
          })
      } else {
        this.$message.warning('文件链接不存在，无法下载')
      }
    },

    formatFileSize(size) {
      if (size < 1024) {
        return size + ' B'
      } else if (size < 1024 * 1024) {
        return (size / 1024).toFixed(2) + ' KB'
      } else {
        return (size / 1024 / 1024).toFixed(2) + ' MB'
      }
    }

  }
}
</script>

<style scoped lang="scss">
.dialog-footer {
  text-align: right;
  padding: 14px 24px;
  background: transparent;
  border-top: 1px solid #f0f0f0;
  border-radius: 0 0 2px 2px;
  .el-button--text {
    padding: 4px 15px;
  }
}
.info-card, .form-card {
  margin-bottom: 10px;

  .card-header {
    font-weight: bold;
  }
  .tip-info {
    margin-bottom: 10px;
    color: #e7855b;
  }
}

.info-row {
  display: flex;
  margin-bottom: 15px;

  .info-item {
    flex: 1;
    display: flex;

    .label {
      color: var(--neutral-700);
      font-weight: 500;
      width: 100px;
      text-align: right;
      padding-right: 30px;
    }

    .value {
      flex: 1;
    }
  }
}

.env-tabs {
  display: flex;
  align-items: center;
  margin-bottom: 20px;

  .label-block {
    padding-right: 12px;
    width: 100px;
    text-align: left;
    color: var(--neutral-700);
    font-weight: 500;
    flex-shrink: 0;
  }
  .tab-buttons {
    display: flex;
    .el-button {
      padding: 8px 20px;
      border-radius: 0;
    }
    .is-disabled {
      background: #F6F8FA;
    }
    .el-button + .el-button {
      margin-left: 0;
    }

    .el-button:first-child {
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
    }

    .el-button:last-child {
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }
  }
}

.deploy-content, .resource-content {
  margin-bottom: 20px;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;

  span {
    width: 100px;
    text-align: left;
    color: var(--neutral-700);
    font-weight: 500;
  }
}

.attachment-section {
  margin-bottom: 20px;

  .switch-group {
    margin-bottom: 15px;
  }
}

.el-input {
  width: 66.6%;
}

.el-tabs--card > .el-tabs__header .el-tabs__item.is-active {
  border-bottom-color: var(--color-600);
}

.required-item {
  ::v-deep .el-form-item__label:before {
    content: '*';
    color: #f56c6c;
    margin-right: 4px;
  }
}

.align-items {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

::v-deep.el-tag {
  padding: 0 10px !important;
}
.el-tree {
  margin-left: 20px;
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e6e6e6;
  padding: 10px;
  border-radius: 4px;
}
.attachment-list {
  .attachment-item {
    display: flex;
    width: 100%;
    padding: 0 10px;
    align-items: center;
    border: 1px solid var(--color-601-border);
    background: var(--color-601-background);

    i {
      color: #909399;
      margin-right: 5px;
    }

    .attachment-name {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      color: #000000D9;
    }

    .attachment-actions {
      display: flex;

      .el-button {
        padding: 0 5px;
        color: var(--color-600);
      }
    }
  }
}
</style>
