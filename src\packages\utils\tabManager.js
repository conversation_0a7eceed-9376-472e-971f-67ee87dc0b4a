// 全局共享的浏览器标签页
export const tabManager = {
  getTabRef(id) {
    const refs = JSON.parse(localStorage.getItem('sharedTabRefs') || '{}')
    return refs[id] ? window.open('', refs[id].name) : null
  },

  setTabRef(id, ref) {
    const refs = JSON.parse(localStorage.getItem('sharedTabRefs') || '{}')
    refs[id] = { name: ref.name }
    localStorage.setItem('sharedTabRefs', JSON.stringify(refs))
  },

  clearTabRef(id) {
    const refs = JSON.parse(localStorage.getItem('sharedTabRefs') || '{}')
    delete refs[id]
    localStorage.setItem('sharedTabRefs', JSON.stringify(refs))
  }
}
