import request from '@/utils/request'
const headers = {
  'Content-Type': 'application/json;charset=UTF-8'
}

// 获取用户列表
export const getUserListAPI = (data) => {
  return request({
    url: '/adminUser/queryUserList',
    method: 'post',
    data,
    headers
  })
}

// 获取用户详情
export const getUserDetailAPI = (data) => {
  return request({
    url: '/adminUser/queryUserInfo',
    method: 'post',
    data
  })
}

// 获取登录用户信息
export function getLoginUserAPI(data) {
  return request({
    url: 'adminUser/queryLoginUser',
    method: 'post',
    data: data,
    headers
  })
}

// 查询部门员工列表
export function getUserByDeptAPI(deptId) {
  return request({
    url: `adminUser/queryDeptUserList/${deptId}`,
    method: 'post'
  })
}

// 添加用户
export const addUserAPI = (data) => {
  return request({
    url: '/adminUser/addUser',
    method: 'post',
    data,
    headers
  })
}

// 编辑用户
export const editUserAPI = (data) => {
  return request({
    url: '/adminUser/setUser',
    method: 'post',
    data,
    headers
  })
}

// 重置密码
export const resetPasswordAPI = (data) => {
  return request({
    url: '/adminUser/resetPassword',
    method: 'post',
    data,
    headers
  })
}

// 重置登录账号
export const resetUsernameAPI = (data) => {
  return request({
    url: '/adminUser/usernameEdit',
    method: 'post',
    data
  })
}

// 重置部门
export const setUserDeptAPI = (data) => {
  return request({
    url: 'adminUser/setUserDept',
    method: 'post',
    data: data,
    headers
  })
}

// 修改用户状态
export const changeUserStatusAPI = (data) => {
  return request({
    url: '/adminUser/setUserStatus',
    method: 'post',
    data,
    headers
  })
}

// 用户导入模板
export function userImportTemplateAPI(data) {
  return request({
    url: 'adminUser/downloadExcel',
    method: 'post',
    data: data,
    responseType: 'blob'
  })
}

// 用户导入
export function userExcelImportAPI(data) {
  var param = new FormData()
  Object.keys(data).forEach(key => {
    param.append(key, data[key])
  })
  return request({
    url: 'adminUser/excelImport',
    method: 'post',
    data: param,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    timeout: 60000
  })
}

// 下载用户导入错误数据
export function userErrorExcelDownAPI(data) {
  return request({
    url: 'adminUser/downExcel',
    method: 'post',
    data: data,
    responseType: 'blob'
  })
}
