import request from '@/packages/request'
const _thisApi = window.NFVO_CONFIG.nfvo_api
const headers = { 'Content-Type': 'application/json', 'x-access-module': 'ADMIN' }

export function getCategory(params) {
  return request({
    url: _thisApi + '/networkelement/category',
    method: 'get',
    params,
    headers
  })
}

export function addCategory(data) {
  return request({
    url: _thisApi + '/networkelement/category',
    method: 'post',
    data,
    headers
  })
}

export function editCategory(id, data) {
  return request({
    url: _thisApi + '/networkelement/category/' + id,
    method: 'put',
    data,
    headers
  })
}

export function deleteCategory(id) {
  return request({
    url: _thisApi + '/networkelement/category/' + id,
    method: 'delete',
    headers
  })
}
