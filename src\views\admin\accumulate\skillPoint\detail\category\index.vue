<template>
  <div class="category-wrap">
    <transverse-list
      :data="complexityArr"
      :allow-deletion="false"
      :allow-add="false"
      :all="true"
      :is-show-expand="false"
      v-bind="categoryProps"
      title="难度"
      @node-click="handleNodeClick($event, 'complexity')"
    />
  </div>
</template>
<script>
import transverseList from '@/packages/transverse-list/index.vue'
import module from '../config.js'

export default {
  components: {
    transverseList
  },
  props: {
    complexity: [String, Number]
  },
  data() {
    return {
      moduleName: module.name,
      complexityArr: [
        { value: '1', label: '初级' },
        { value: '2', label: '中级' },
        { value: '3', label: '高级' }
      ],
      categoryProps: {
        label: 'label',
        idName: 'value'
      },
      categoryQuery: {
        complexity: this.complexity
      }
    }
  },
  mounted() {
  },
  methods: {
    handleNodeClick(item, key) {
      this.categoryQuery[key] = item.value
      this.$emit('category-query', this.categoryQuery)
    }
  }
}
</script>
