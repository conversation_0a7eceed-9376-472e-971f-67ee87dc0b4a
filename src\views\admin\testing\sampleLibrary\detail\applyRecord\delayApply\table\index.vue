<template>
  <div class="resource-table">
    <!-- 操作区 -->
    <div class="operation-wrap">
      <div class="operation-left">
        <el-button type="primary" icon="el-icon-refresh" @click="refresh" />
      </div>
      <div class="operation-right">
        <el-badge :value="searchBtnShowNum">
          <el-button icon="el-icon-search" @click="openSearch">搜索</el-button>
        </el-badge>
        <!-- 自定义表格列 -->
        <t-table-config
          v-if="!customColData.length"
          :data="columnsObj"
          :active-key-arr="columnsViewArr"
          @on-change-col="onChangeCol"
        />
      </div>
    </div>
    <!-- 搜索区 -->
    <t-search-box
      v-show="searchView"
      :search-key-list="searchKeyListView"
      default-placeholder="默认搜索申请人"
      @search="searchMultiple"
    />
    <!-- 列表 -->
    <t-table-view
      ref="tableView"
      :height="height"
      :single="single"
      :loading="tableLoading"
      :data="tableData"
      :total="tableTotal"
      :page-size="pageSize"
      :current="pageCurrent"
      :select-item="selectItem"
      type="list"
      current-key="id"
      @on-select="onSelect"
      @on-current="onCurrent"
      @on-change="changePage"
      @on-sort-change="onSortChange"
      @on-page-size-change="onPageSizeChange"
    >
      <el-table-column
        v-for="item in columnsViewArr"
        :key="item"
        :min-width="columnsObj[item].colMinWidth"
        :width="columnsObj[item].colWidth"
        :label="columnsObj[item].title"
        :fixed="columnsObj[item].master ? 'left' : false"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span v-if="item == 'sort'">
            {{ scope.$index + 1 }}
          </span>
          <div v-else-if="item == 'delayItem'">
            <span v-if="scope.row.delayItem === 0">提交资料</span>
            <span v-else-if="scope.row.delayItem === 1">部署环境</span>
            <span v-else-if="scope.row.delayItem === 2">整改复测</span>
            <span v-else>-</span>
          </div>
          <div v-else-if="item == 'auditStatus'">
            <el-badge :type="statusMapping[scope.row.auditStatus]" is-dot />
            {{ statusObj[scope.row[item]].label || '-' }}
          </div>
          <div v-else-if="item === 'option'">
            <el-link :underline="false" type="primary" @click.stop="handleDetail(scope.row)">查看详情</el-link>
          </div>
          <span v-else>{{ scope.row[item] || "-" }}</span>
        </template>
      </el-table-column>
    </t-table-view>
  </div>
</template>
<script>
import module from '../config.js'
import tSearchBox from '@/packages/search-box/index.vue'
import mixinsPageTable from '@/packages/mixins/page_table'
import tTableView from '@/packages/table-view/index.vue'
import tTableConfig from '@/packages/table-config/table-col-config.vue'
import tableTdMultiCol from '@/packages/table-config/table-td-multi-col.vue'
import { getSampleDelayApplyPage } from '@/api/testing/index'
import { mapGetters } from 'vuex'
import statusConf from '../../config.js'
export default {
  components: {
    tSearchBox,
    tTableView,
    tTableConfig,
    tableTdMultiCol
  },
  mixins: [mixinsPageTable],
  props: {
    projectId: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    return {
      moduleName: module.name,
      statusObj: statusConf.statusObj,
      statusMapping: statusConf.statusMapping,
      searchKeyList: [
        { key: 'applyName', label: '申请人', placeholder: '请输入', master: true },
        { key: 'auditName', label: '审核人', placeholder: '请输入' },
        { key: 'auditStatus', label: '审核状态', type: 'select', placeholder: '请选择', valueList: statusConf.statusArr },
        { key: 'delayItem', label: '延期事项', type: 'select', placeholder: '请选择', valueList: [
          { label: '提交资料', value: '0' },
          { label: '部署环境', value: '1' },
          { label: '整改复测', value: '2' }
        ] },
        { key: 'applyTime', label: '申请时间', type: 'time_range', placeholder: '请选择' },
        { key: 'auditTime', label: '审核时间', type: 'time_range', placeholder: '请选择' },
        { key: 'delayAt', label: '预计延期至', type: 'time_range', placeholder: '请选择' }
      ],
      columnsObj: {
        'sort': {
          title: '序号', master: true, colWidth: 50
        },
        'delayItem': {
          title: '延期事项', master: true, colMinWidth: 80
        },
        'delayAt': {
          title: '预计延期至', colMinWidth: 100
        },
        'auditStatus': {
          title: '审核状态', colMinWidth: 80
        },
        'applyName': {
          title: '申请人', colMinWidth: 80
        },
        'applyTime': {
          title: '申请时间', colMinWidth: 100
        },
        'auditUserName': {
          title: '审核人', colMinWidth: 80
        },
        'auditTime': {
          title: '审核时间', colMinWidth: 100
        },
        'option': {
          title: '操作', colMinWidth: 100
        }
      },
      columnsViewArr: [
        'sort',
        'delayItem',
        'delayAt',
        'auditStatus',
        'applyName',
        'applyTime',
        'auditUserName',
        'auditTime',
        'option'
      ],
      delayApplyAuth: null // 延期申请权限数据
    }
  },
  computed: {
    ...mapGetters(['manage'])
  },
  created() {
    this.delayApplyAuth = this.manage.testing.project.projectDetail.applyRecord.delayApply
  },
  methods: {
    getList: function(showLoading = true) {
      if (showLoading) {
        this.tableLoading = true
      }

      // 构建查询参数
      const params = this.getPostData('page', 'limit')
      params.pageType = 1 // 分页
      params.projectId = this.projectId || this.$route.params.id

      // 处理日期范围参数
      if (params.applyTime) {
        const dateRange = params.applyTime.split(',')
        if (dateRange.length === 2) {
          params.applyTimeBegin = dateRange[0]
          params.applyTimeEnd = dateRange[1]
        }
        delete params.applyTime
      }

      if (params.auditTime) {
        const dateRange = params.auditTime.split(',')
        if (dateRange.length === 2) {
          params.auditTimeBegin = dateRange[0]
          params.auditTimeEnd = dateRange[1]
        }
        delete params.auditTime
      }
      if (params.delayAt) {
        params.delayTimeBegin = params.delayAt.split(',')[0]
        params.delayTimeEnd = params.delayAt.split(',')[1]
        delete params.delayAt
      }
      // 调用接口获取数据
      getSampleDelayApplyPage(params).then((res) => {
        if (res.data && res.data.code === 0) {
          // 处理返回的数据
          this.tableData = res.data.data ? res.data.data.records : []
          this.tableTotal = res.data.data ? res.data.data.total : 0
        }
        this.tableLoading = false
        this.handleSelection()
      }).catch(() => {
        this.tableLoading = false
        this.tableData = []
        this.tableTotal = 0
      })
    },
    // 打开侧拉查看详情
    handleDetail(row) {
      const params = Object.assign({
        showDrawer: true,
        projectId: this.projectId || this.$route.params.id
      }, row)
      this.$emit('show-drawer-detail', params)
    }
  }
}
</script>
<style lang="scss" scoped>
.resource-table {
  height: 100%;
  padding: 15px 0 0 0;
}
</style>
