<template>
  <div class="resource-table" style="padding: 0; margin-top: -15px;">
    <!-- 题目难度 -->
    <category
      :complexity="complexity"
      style="margin-bottom: 12px;margin-left: -15px;"
      @category-query="categoryQuery"
    />
    <!-- 操作区 -->
    <div class="operation-wrap">
      <div class="operation-left">
        <slot name="action" />
        <el-button type="primary" icon="el-icon-refresh" @click="refresh" />
      </div>
      <div class="operation-right">
        <el-badge :value="searchBtnShowNum">
          <el-button icon="el-icon-search" @click="openSearch">搜索</el-button>
        </el-badge>
        <!-- 自定义表格列 -->
        <t-table-config
          v-if="!customColData.length"
          :data="columnsObj"
          :active-key-arr="columnsViewArr"
          @on-change-col="onChangeCol"
        />
      </div>
    </div>
    <!-- 搜索区 -->
    <t-search-box
      v-show="searchView"
      :search-key-list="searchKeyListView"
      default-placeholder="默认搜索题目名称"
      @search="searchMultiple"
    />
    <!-- 列表 -->
    <t-table-view
      ref="tableView"
      :height="height"
      :single="single"
      :loading="tableLoading"
      :data="tableData"
      :total="tableTotal"
      :page-size="pageSize"
      :current="pageCurrent"
      :select-item="selectItem"
      current-key="questionId"
      type="list"
      @on-select="onSelect"
      @on-current="onCurrent"
      @on-change="changePage"
      @on-sort-change="onSortChange"
      @on-page-size-change="onPageSizeChange"
    >
      <el-table-column
        v-for="item in columnsViewArr"
        :key="item"
        :min-width="colMinWidth"
        :label="columnsObj[item].title"
        :fixed="columnsObj[item].master ? 'left' : false"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span v-if="item == 'bankType'">
            <span>{{ backTypeObj[scope.row.bankType]|| '-' }}</span>
          </span>
          <span v-else-if="item == 'complexity'">
            <span>{{ complexityObj[scope.row.complexity]|| '-' }}</span>
          </span>
          <span v-else-if="item == 'questionType'">
            <span>{{ questionTypeMapping[scope.row.questionType] || '-' }}</span>
          </span>
          <span v-else>{{ scope.row[item] || '-' }}</span>
        </template>
      </el-table-column>
    </t-table-view>
  </div>
</template>

<script>
import moduleConf from '../config'
import tSearchBox from '@/packages/search-box/index.vue'
import tTableView from '@/packages/table-view/index.vue'
import tTableConfig from '@/packages/table-config/table-col-config.vue'
import mixinsPageTable from '@/packages/mixins/page_table'
import category from '../category/index.vue'
import { getSkillPointDetailApi } from '@/api/accumulate/skillPoint.js'
import { searchKeyList, columnsObj, columnsViewArr, complexityObj, questionTypeMapping, backTypeObj } from './utils'

export default {
  components: {
    tSearchBox,
    tTableView,
    tTableConfig,
    category
  },
  mixins: [mixinsPageTable],
  data() {
    return {
      moduleName: moduleConf.name,
      // 搜索配置项
      searchKeyList: [...searchKeyList],
      // 所有可配置显示列 master：不可隐藏 title:列名称
      columnsObj: { ...columnsObj },
      // 当前显示列key表 默认，如果localStorage有数据将被覆盖
      columnsViewArr: [...columnsViewArr],
      complexityObj,
      backTypeObj,
      questionTypeMapping,
      skillPointId: this.$route.params.id,
      complexity: ''
    }
  },
  watch: {
    '$route': function(to, from) {
      const toId = to.params.hasOwnProperty('id') ? to.params.id : null
      const fromId = from.params.hasOwnProperty('id') ? from.params.id : null
      if (toId !== fromId) {
        this.skillPointId = to.params.id
        this.getList()
      }
    }
  },
  methods: {
    categoryQuery(obj) {
      this.complexity = obj.complexity
      this.getList()
    },
    getList: function(showLoading = true) {
      if (showLoading) {
        this.tableLoading = true
      }
      const params = {
        ...this.getPostData(),
        skillPointId: this.skillPointId,
        complexity: this.complexity
      }
      getSkillPointDetailApi(params).then((res) => {
        if (res.code === 0) {
          this.tableData = res.data.records || []
          this.tableTotal = res.data.total || 0
          this.handleSelection()
        }
      }).finally(() => {
        this.tableLoading = false
      })
    }
  }
}
</script>
