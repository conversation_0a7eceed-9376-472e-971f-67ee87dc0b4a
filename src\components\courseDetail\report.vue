<template>
  <div class="container h-100">
    <el-row v-if="fileList.length != 0" style="width: 100%;height: 100%;">
      <el-col
        v-if="fileList.length"
        style="height: 100%;">
        <div class="grid-view">
          <div v-if="previewUrl && fileList[0].type === 'pdf'" class="button-wrap">
            <el-link :underline="false" type="primary" size="small" @click="toggleFullscreen">
              {{ isFullscreen ? '退出全屏' : '全屏预览' }}
            </el-link>
          </div>
          <iframe id="frame" :src="previewUrl" allow="fullscreen" style="width: 100%;height:100%;border: 0px;margin:0;"/>
        </div>
      </el-col>
    </el-row>
    <el-empty
      v-if="!loading && fileList.length === 0"
      :image="img"
      :image-size="110"
      style="margin: 100px auto"
      description="暂无数据"
    />
  </div>
</template>
<script>
import { contentdetail } from '@/api/teacher/index.js'
import { getFileSuffix } from '@/utils'
import { api as fullscreen } from 'vue-fullscreen'

export default {
  props: {
    showPackage: Boolean, // 是否显示附件下载
    // 是否可以下载
    isDownload: {
      type: Boolean,
      default: true
    },
    id: [String, Number]
  },
  data() {
    return {
      fileList: [],
      fileList1: [],
      loading: true,
      img: require('@/assets/empty_state.png'),
      previewUrl: '',
      isFullscreen: false // 是否全屏
    }
  },
  computed: {
    contentId() {
      const dataId = JSON.parse(localStorage.getItem('dataId'))
      return dataId || this.id
    }
  },
  watch: {
    id: {
      handler() {
        this.searchCurriculum()
        this.searchPackage()
      },
      immediate: true
    }
  },
  mounted() {
    this.searchCurriculum()
    this.searchPackage()
  },
  methods: {
    async handleIframepreview() {
      await this.$nextTick()
    },
    async toggleFullscreen() {
      const wrapperEl = this.$el.querySelector('.grid-view')
      await fullscreen.toggle(wrapperEl, {
        callback: (val) => {
          this.isFullscreen = val
        }
      })
      this.isFullscreen = fullscreen.isFullscreen
    },
    searchPackage() {
      contentdetail({ contentId: this.contentId, format: 'package' }).then(res => {
        if (res.code == 0 && res.data) {
          this.fileList1 = res.data
          this.fileList1.map(item => {
            item.name = item.fileName
            const fileArr = item.fileName.split('.')
            item.type = fileArr[fileArr.length - 1]
            item.url = window.location.origin + item.attachmentUrl
          })
        }
      })
    },
    searchCurriculum() {
      this.loading = true
      contentdetail({ contentId: this.contentId, format: 'courseware' }).then(res => {
        if (res.code == 0) {
          this.handleIframepreview()
          this.fileList = res.data
          this.fileList.map(item => {
            item.name = item.attachmentUrl
            const fileArr = item.fileName.split('.')
            item.type = fileArr[fileArr.length - 1]
            item.url = window.location.origin + item.attachmentUrl
          })
          // 默认显示第一个附件
          const file = this.fileList[0]
          if (file) {
            // file.url = 'http://**********:18080' + file.url
            this.handlePreview(file, 0)
          }
          this.loading = false
        }
      }).catch(() => {
        this.loading = false
      })
    },
    handlePreview(file) {
      if (['pdf'].includes(getFileSuffix(file.url))) {
        this.previewUrl = this.viewFileUrl + encodeURIComponent(btoa(file.url)) // kkfile 预览
      } else {
        this.previewUrl = this.viewFileUrl + encodeURIComponent(btoa(file.url)) // kkfile 预览
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.container {
  .wrapper {
    border-radius: 4px;
    padding: 15px;
    background-color: #ffffff;
  }
  .upload-demo{
    width: 100%;
    ::v-deep{
      .el-upload {
        width: 100%;
        .el-upload-dragger{
          width: 100%;
          height: 102px;
          .icon {
            margin: 26px 0 18px;
          }
        }
      }
      .el-upload__tip{
        font-size: 12px;
        font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
        font-weight: 400;
        color: #86909C;
      }
    }
  }
  ._c_report_container {
    font-size: 14px;
    height: 100%;
    overflow-y: auto;
  }
  .file-name {
    width: 200px;
    margin-right: 10px;
  }
  .download-list {
    border-right: 1px solid #eee;
    padding-right: 15px;
    margin-right: 15px;
  }
  .grid-file {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: auto;
    overflow: auto;
    // background: red;
    .el-icon-delete {
      display: none;
      cursor: pointer;
    }

    &:hover {
      .el-icon-delete {
        display: inline-block;
      }
    }

    .file-name {
      // width: 120px;
      display: inline-block;
      flex: 1;
      line-height: 30px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      &.is-selected {
        color: #337ce4;
      }
    }
    .file-name:not(.not-hover):hover {
      color: #337ce4;
      cursor: pointer;
    }
  }
  .grid-view {
    position: relative;
    width: 100%;
    height: 100%;
    .button-wrap {
      position: absolute;
      top: 10px;
      right: 30px;
      .el-link {
        color: var(--color-600);
      }
    }
  }
}
.upload-demo1 {
  position: relative;
  .status-tag {
    position: absolute;
    left: 85px;
    top: 5px;
    text-align: center;
    height: 32px;
    line-height: 32px;
    margin-left: 15px;
    padding: 0 10px !important;
  }
}
</style>
