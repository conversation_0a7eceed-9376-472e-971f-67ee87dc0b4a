<template>
  <div class="resource-table">
    <!-- 操作区 -->
    <div class="operation-wrap">
      <div class="operation-left">
        <slot name="action" />
        <el-button type="primary" icon="el-icon-refresh" @click="refresh" />
      </div>
      <div class="operation-right">
        <el-badge :value="searchBtnShowNum">
          <el-button icon="el-icon-search" @click="openSearch">搜索</el-button>
        </el-badge>
        <!-- 自定义表格列 -->
        <t-table-config
          v-if="!customColData.length"
          :data="columnsObj"
          :active-key-arr="columnsViewArr"
          style="padding-left: 5px;"
          @on-change-col="onChangeCol"
        />
      </div>
    </div>
    <!-- 搜索区 -->
    <t-search-box
      v-show="searchView"
      :search-key-list="searchKeyListView"
      default-placeholder="默认搜索附件名称"
      @search="searchMultiple"
    />
    <!-- 列表 -->
    <t-table-view
      ref="tableView"
      :height="height"
      :single="single"
      :loading="tableLoading"
      :data="tableData"
      :total="tableTotal"
      :page-size="pageSize"
      :current="pageCurrent"
      :select-item="selectItem"
      current-key="id"
      type="list"
      @on-select="onSelect"
      @on-current="onCurrent"
      @on-change="changePage"
      @on-sort-change="onSortChange"
      @on-page-size-change="onPageSizeChange"
    >
      <el-table-column
        v-for="item in columnsViewArr"
        :key="item"
        :min-width="colMinWidth"
        :width="columnsObj[item].width"
        :label="columnsObj[item].title"
        :fixed="columnsObj[item].master ? 'left' : false"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span v-if="item === 'index'">{{ indexMethod(scope.$index) }}</span>
          <div v-else-if="item === 'fileSize'">{{ formatFileSize(scope.row[item]) }}</div>
          <span v-else-if="item === 'operation'">
            <el-link
              :underline="false"
              type="primary"
              @click="handleView(scope.row)"
            >
              查看
            </el-link>
            <el-link
              :underline="false"
              type="primary"
              @click="handleDownload(scope.row)"
            >
              下载
            </el-link>
          </span>
          <span v-else>{{ scope.row[item] || "-" }}</span>
        </template>
      </el-table-column>
    </t-table-view>
    <!-- 中部弹窗 start-->
    <el-dialog
      :title="titleMapping[modalName]"
      :visible.sync="modalShow"
      :width="modalWidth"
      :destroy-on-close="true"
      append-to-body
      @close="modalClose"
    >
      <transition name="el-fade-in-linear">
        <component
          :is="modalName"
          :name="modalName"
          :data="selectItem"
          @close="modalClose"
          @call="confirmCall"
        />
      </transition>
    </el-dialog>
    <!-- 中部弹窗 end-->
  </div>
</template>
<script>
import {
  getAttachmentsPage,
  downloadAttachmentAPI
} from '@/api/testing/index'
import mixinsActionMenu from '../../applyRecord/delayApply/table/action_menu.js'
import mixinsPageTable from '@/packages/mixins/page_table'
import tSearchBox from '@/packages/search-box/index.vue'
import tTableConfig from '@/packages/table-config/table-col-config.vue'
import tableTdMultiCol from '@/packages/table-config/table-td-multi-col.vue'
import tTableView from '@/packages/table-view/index.vue'
import module from '../config.js'
import { commonArchiveTypes } from '@/utils/index.js'
export default {
  components: {
    tSearchBox,
    tTableView,
    tTableConfig,
    tableTdMultiCol
  },
  mixins: [mixinsPageTable, mixinsActionMenu],
  data() {
    return {
      moduleName: module.name,
      searchKeyList: [
        { key: 'fileName', label: '附件名称', placeholder: '请输入', master: true },
        { key: 'taskName', label: '测试任务', placeholder: '请输入' },
        { key: 'createBy', label: '提交人', placeholder: '请输入' },
        { key: 'createAt', label: '提交时间', placeholder: '请选择', type: 'time_range' },
        { key: 'updateBy', label: '最后更新人', placeholder: '请输入' },
        { key: 'updateAt', label: '更新时间', placeholder: '请选择', type: 'time_range' }
      ],
      columnsObj: {
        'index': { title: '序号', width: 50, master: true },
        'fileName': {
          title: '附件名称'
        },
        'typeName': {
          title: '测试任务'
        },
        'fileSize': {
          title: '文件大小'
        },
        'createByName': {
          title: '提交人'
        },
        'createAt': {
          title: '提交时间'
        },
        'updateByName': {
          title: '最后更新人'
        },
        'updateAt': {
          title: '最后更新时间'
        },
        'operation': {
          title: '操作'
        }
      },
      columnsViewArr: [
        'index',
        'fileName',
        'typeName',
        'fileSize',
        'createByName',
        'createAt',
        'updateByName',
        'updateAt',
        'operation'
      ],
      // 弹窗title映射
      titleMapping: {},
      projectId: this.$route.params.id || '',
      isUpdating: false, // 添加更新状态标志
      commonArchiveTypes
    }
  },
  created() {
    // 监听父组件的processTasks变化
    this.$parent.$watch('processTasks', (tasks) => {
      if (tasks && tasks.length > 0) {
        console.log('表格组件接收到processTasks:', tasks)
        // 可以在这里根据任务更新一些UI元素
      }
    })
  },
  methods: {
    handleView(row) {
      const suffix = String(row.fileName).substring(String(row.fileName).lastIndexOf('.') + 1)
      if (row && row.fileName && this.commonArchiveTypes.includes(suffix)) {
        this.$message.warning('该文件不支持预览，请下载查看')
        return
      }
      if (row && row.fileUrl) {
        const fileUrl = window.ADMIN_CONFIG.VIP_URL + row.fileUrl
        if (suffix == 'xlsx') {
          this.previewUrl = this.viewFileUrl + encodeURIComponent(btoa(fileUrl)) + '&officePreviewType=html'
        } else {
          this.previewUrl = this.viewFileUrl + encodeURIComponent(btoa(fileUrl))
        }
        window.open(this.previewUrl)
      }
    },
    handleDownload(item) {
      const params = {}
      params.id = item.id
      downloadAttachmentAPI(params).then((res) => {
        if (res.data.data) {
          fetch(res.data.data, {
            method: 'get',
            responseType: 'blob'
          })
            .then((response) => response.blob())
            .then((blob) => {
              const a = document.createElement('a')
              const URL = window.URL || window.webkitURL
              const href = URL.createObjectURL(blob)
              a.href = href
              a.download = item.fileName
              document.body.appendChild(a)
              a.click()
              document.body.removeChild(a)
              URL.revokeObjectURL(href)
            })
        }
      })
    },
    // 根据任务ID获取任务名称
    getTaskNameById(taskId) {
      if (this.$parent.processTasks && this.$parent.processTasks.length > 0) {
        const task = this.$parent.processTasks.find(t => t.id === taskId)
        return task ? task.name : '-'
      }
      return '-'
    },

    // 格式化文件大小
    formatFileSize(size) {
      if (!size) return '0 B'

      if (size < 1024) {
        return size + ' B'
      } else if (size < 1024 * 1024) {
        return (size / 1024).toFixed(2) + ' KB'
      } else {
        return (size / 1024 / 1024).toFixed(2) + ' MB'
      }
    },
    getList: function(showLoading = true, id) {
      if (showLoading) {
        this.tableLoading = true
      }
      const params = this.getPostData('page', 'limit')
      params.taskId = id
      params.projectId = this.projectId
      if (params.createAt) {
        params.createAtStart = params.createAt.split(',')[0]
        params.createAtEnd = params.createAt.split(',')[1]
        delete params.createAt
      }
      if (params.updateAt) {
        params.updateAtStart = params.updateAt.split(',')[0]
        params.updateAtEnd = params.updateAt.split(',')[1]
        delete params.updateAt
      }
      getAttachmentsPage(params).then(res => {
        if (res.data.code === 0) {
          this.tableData = res.data.data.records || []
          this.tableTotal = Number(res.data.data.total) || 0
          this.tableLoading = false
        }
      })
    },
    confirmCall: function(type, data) {
      if (type === 'close') {
        this.modalClose()
      } else if (type === 'refresh') {
        this.$emit('call', type)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.resource-table {
  height: 100%;
  padding: 0 15px;
  .operation-wrap {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    .operation-left {
      display: flex;
      align-items: center;
    }
    .operation-right {
      display: flex;
      align-items: center;
    }
  }
  // 点击查看按钮
  ::v-deep .click-view {
    cursor: pointer;
    color: var(--color-600);
    &:hover {
      text-decoration: underline;
    }
  }
}
</style>
