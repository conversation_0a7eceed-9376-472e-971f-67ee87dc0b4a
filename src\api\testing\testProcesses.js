import request from '@/utils/request'
const headers = { 'Content-Type': 'application/json;charset=UTF-8' }

// 获取检测流程列表
export function getTestProcessesListApi(data) {
  return request({
    url: '/testing/testProcesses/page',
    method: 'post',
    data,
    headers
  })
}

// 新增检测流程
export function addTestProcessesApi(data) {
  return request({
    url: '/testing/testProcesses/create',
    method: 'post',
    data,
    headers
  })
}

// 编辑检测流程
export function editTestProcessesApi(data) {
  return request({
    url: '/testing/testProcesses/update',
    method: 'post',
    data,
    headers
  })
}

// 查询检测流程信息
export function getTestProcessesByIdApi(id) {
  return request({
    url: `/testing/testProcesses/get/${id}`,
    method: 'get',
    headers
  })
}

// 查询检测流程任务信息
export function getTaskProcessesByIdApi(id) {
  return request({
    url: `/testing/testProcessTask/get/${id}`,
    method: 'get',
    headers
  })
}

// 启用/禁用检测流程
export function handleTestProcessesStatusApi(testProcessId, status) {
  return request({
    url: `/testing/testProcesses/${testProcessId}/${status}/status`,
    method: 'post',
    headers
  })
}

// 删除检测流程
export function deleteTestProcessesApi(data) {
  return request({
    url: '/testing/testProcesses/delete',
    method: 'post',
    data,
    headers
  })
}

// 上传附件
export function uploadTestProcessesFile(data, config = {}) {
  return request({
    url: '/testing/testProcesses/uploadFile',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    ...config
  })
}

// 根据id获取附件
export function getTestProcessesFileById(data) {
  return request({
    url: '/testing/testProcesses/getFileBySource',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 删除附件
export function deleteTestProcessesFile(data) {
  return request({
    url: '/testing/testProcesses/deleteFile',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

