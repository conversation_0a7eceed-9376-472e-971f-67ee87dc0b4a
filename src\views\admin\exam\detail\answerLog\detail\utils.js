// 题型map
const questionTypeArr = [
  { label: '单选题', value: '1' },
  { label: '多选题', value: '2' },
  { label: '判断题', value: '3' },
  { label: 'CTF题', value: '4' },
  { label: 'AWD题', value: '5' },
  { label: '其他', value: '6' },
  { label: '填空题', value: '7' },
  { label: '简答题', value: '8' },
  { label: '漏洞题', value: '9' },
  { label: '组合题', value: '10' }
]
// 难度map
const complexityArr = [
  { label: '初级', value: '1' },
  { label: '中级', value: '2' },
  { label: '高级', value: '3' }
]
// 用途map
const usesArr = [
  { label: '实训', value: '1' },
  { label: '竞赛', value: '2' },
  { label: '攻防演练', value: '3' }
]
export default {
  name: 'questionBank',
  questionTypeArr: questionTypeArr,
  complexityArr: complexityArr,
  usesArr: usesArr,
  // 将题型map数组转换为对象
  questionTypObj: questionTypeArr.reduce((acc, prev) => {
    acc[prev.value] = prev
    return acc
  }, {}),
  // 将难度map数组转换为对象
  complexityObj: complexityArr.reduce((acc, prev) => {
    acc[prev.value] = prev
    return acc
  }, {}),
  // 将用途map数组转换为对象
  usesObj: usesArr.reduce((acc, prev) => {
    acc[prev.value] = prev
    return acc
  }, {})
}
