<template>
  <div class="resource-table" style="height: 100%;">
    <!--    操作区 start-->
    <div class="operation-wrap">
      <div class="operation-left">
        <slot name="action"/>
        <el-button type="primary" icon="el-icon-refresh" @click="refresh"/>
      </div>
      <div class="operation-right">
        <el-badge :value="searchBtnShowNum">
          <el-button icon="el-icon-search" @click="openSearch">搜索</el-button>
        </el-badge>
      </div>
    </div>
    <!--    搜索标签显示区 start-->
    <t-search-box
      v-show="searchView"
      :search-key-list="searchKeyListView"
      default-placeholder="默认搜索名称"
      @search="searchMultiple"
    />
    <!--    搜索标签显示区 end-->
    <!--    操作区 end-->
    <!--    列表 start-->
    <t-table-view
      ref="tableView"
      :single="single"
      :loading="tableLoading"
      :data="tableData"
      :total="tableTotal"
      :page-size="pageSize"
      :current="pageCurrent"
      :select-item="selectItem"
      :multiple-page="false"
      current-key="id"
      @on-select="onSelect"
      @on-current="onCurrent"
      @on-change="changePage"
      @on-page-size-change="onPageSizeChange"
    >
      <!-- 创建时间 -->
      <el-table-column :min-width="colMinWidth" :show-overflow-tooltip="true" fixed="left" label="创建时间">
        <template slot-scope="scope">
          {{ $options.filters['nfvoMoment'](scope.row.created_at) }}
        </template>
      </el-table-column>
      <!-- 名称 -->
      <el-table-column :min-width="colMinWidth" :show-overflow-tooltip="true" label="名称">
        <template slot-scope="scope">
          {{ scope.row.name }}
        </template>
      </el-table-column>
      <!-- 状态 -->
      <el-table-column :min-width="colMinWidth" :show-overflow-tooltip="true" label="状态">
        <template slot-scope="scope">
          <el-badge
            :type="getStatus(scope.row.state)"
            is-dot />{{ snapshot_status_info[scope.row.state.toLowerCase()] }}
        </template>
      </el-table-column>
      <!-- ID -->
      <el-table-column :min-width="colMinWidth" :show-overflow-tooltip="true" label="ID" >
        <template slot-scope="scope">
          {{ scope.row.id }}
        </template>
      </el-table-column>
      <!-- 描述 -->
      <el-table-column :min-width="colMinWidth" :show-overflow-tooltip="true" label="描述">
        <template slot-scope="scope">
          {{ scope.row.description || '-' }}
        </template>
      </el-table-column>
    </t-table-view>
    <!--    列表 end-->
  </div>
</template>
<script>
import { mapState } from 'vuex'
import moduleConf from '../config'
// 列表列配置
import mixinsPageTable from '../../../../../mixins/page_table.js'
// 设置列表列UI组件
import tTableView from '../../../../../table-view/index.vue'
import tSearchBox from '../../../../../search-box/index.vue'
import { getSnapshot } from '../../../../api/orchestration'

export default {
  components: {
    tTableView,
    tSearchBox
  },
  mixins: [
    mixinsPageTable
  ],
  props: {
    resourceId: {
      type: String
    }
  },
  data() {
    return {
      'snapshot_status_info': {
        'creating': '正在创建中',
        'created': '创建成功',
        'deleting': '正在删除中',
        'deleted': '已删除',
        'rollback-start': '快照恢复中',
        'rollback-end': '快照恢复成功',
        'available': '可用'
      },
      // 模块名称 用于翻译
      moduleName: moduleConf.name,
      // 列表数据请求对象
      // 搜索项配置
      searchKeyList: [
        { key: 'name', label: '名称', master: true, placeholder: '默认搜索名称' }
      ]
    }
  },
  computed: {
    ...mapState('socketListener', [
      'snapshotSocket'
    ])
  },
  watch: {
    'resourceId': function() {
      this.getList()
    },
    'snapshotSocket': function(nval, oval) {
      this.socketHandle(nval, oval)
      const payload = nval.payload
      if (payload.state === 'created') {
        this.getList()
      }
    }
  },
  methods: {
    // socket消息处理
    'socketHandle': function(nval, oval) {
      const option = nval.payload.hasOwnProperty('option') ? nval.payload['option'] : (nval.payload.hasOwnProperty('event_type') ? nval.payload['event_type'] : null)
      const status = nval.payload.hasOwnProperty('state') ? nval.payload['state'] : null
      const id = nval.payload.hasOwnProperty('resource_id') ? nval.payload['resource_id'] : null
      if (option === 'delete' || option === 'created' || status === 'created') {
        this.getList(false)
        return
      } else {
        this.reloadItem(id)
        return
      }
    },
    'reloadItem': function(id) {
      const reloadTimestamp = new Date().getTime()
      const tableIdArr = this.tableData.map(item => item['id'])
      if (!tableIdArr.includes(id)) {
        return
      }
      getSnapshot({ 'node_id': this.resourceId })
        .then(res => {
          const saveLastData = JSON.parse(JSON.stringify(this.selectItem))
          const _itemData = res['data']['data']['result'].find(val => val.id === id)
          for (let i = 0; i < this.tableData.length; i++) {
            if (this.tableData[i]['id'] === id) {
              if (!this.tableData[i].hasOwnProperty('reload_timestamp') || (reloadTimestamp > this.tableData[i]['reload_timestamp'])) {
                _itemData['reload_timestamp'] = reloadTimestamp
                this.$set(this.tableData, i, _itemData)
              }
              break
            }
          }
          const arr = saveLastData.map(item => item[this.defaultSelectedKey]).concat(this.defaultSelectedArr)
          this.tableData.forEach((item, index) => {
            if (arr.indexOf(item[this.defaultSelectedKey]) > -1) {
              this.$refs['tableView'].toggleRowSelection(this.tableData[index], true)
            }
          })
        })
    },
    'getStatus': function(data) {
      let color = null
      switch (data.toLowerCase()) {
        case 'available':
          color = 'success'
          break
        case 'in-use':
          color = 'warning'
          break
        case 'error':
          color = 'danger'
          break
        default:
          color = 'info'
          break
      }
      return color
    },
    getList: function(showLoading = true) {
      if (showLoading) {
        this.tableLoading = true
      }
      const params = this.getPostData('page', 'limit')
      params['offset'] = (params['page'] - 1) * params['limit']
      delete params['page']
      getSnapshot(params).then((res) => {
        this.tableData = res.data.data
        this.tableTotal = res.data.data.length
        this.tableLoading = false
        this.handleSelection()
      }).catch(() => {
        this.tableLoading = false
      })
    }
  }
}
</script>
