import request from '@/packages/request'
const headers = { 'Content-Type': 'application/json;charset=UTF-8' }

// 检测项目列表
export function testingItemsQueryPageAPI(data) {
  return request({
    url: '/testing/project/query',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 检测项目详情
export function testingItemsDetailAPI(id) {
  return request({
    url: `/testing/project/${id}`,
    method: 'get',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 创建检测项目
export function testingItemsCreateAPI(data) {
  return request({
    url: '/testing/project/create',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 更新检测项目
export function testingItemsUpdateAPI(id, data) {
  return request({
    url: `/testing/project/${id}/update`,
    method: 'put',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 删除检测项目
export function testingItemsDeleteAPI(id) {
  return request({
    url: `/testing/project/${id}/remove`,
    method: 'delete',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 检测任务列表
export function testingTaskQueryPageAPI(data) {
  return request({
    url: '/api/testing/task/page',
    method: 'post',
    data
  })
}

// 创建检测任务
export function testingTaskCreateAPI(data) {
  return request({
    url: '/api/testing/task/create',
    method: 'post',
    data
  })
}

// 更新检测任务
export function testingTaskUpdateAPI(data) {
  return request({
    url: '/api/testing/task/update',
    method: 'post',
    data
  })
}

// 删除检测任务
export function testingTaskDeleteAPI(data) {
  return request({
    url: '/api/testing/task/delete',
    method: 'post',
    data
  })
}

// 项目送审
export function testingItemsSubmitAPI(data) {
  return request({
    url: '/api/testing/items/submit',
    method: 'post',
    data
  })
}

// 查询检测申请列表
export function fetchApplicationListAPI(data) {
  return request({
    url: '/testing/testApplication/query',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 查询项目负责人列表
export function fetchManagerListAPI(data) {
  return request({
    url: '/testing/user/manager/query',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 项目申请复测
export function retestApplyAPI(id, data) {
  return request({
    url: `/testing/project/${id}/retest_apply`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 项目释放资源
export function releaseResourcesAPI(id, data) {
  return request({
    url: `/testing/project/${id}/releaseResources`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 项目获取资源数量
export function getResourcesCountAPI(data) {
  return request({
    url: `/testing/project/${data.id}/getResourcesCount`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 归档检测项目
export function archiveProjectAPI(id, data) {
  return request({
    url: `/testing/project/${id}/archive`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 归档检测项目zip下载
export function archiveDownZipAPI(id, data) {
  return request({
    url: `/testing/project/${id}/downZip?type=${data.type}`,
    method: 'get',
    data,
    headers,
    responseType: 'blob'
  })
}

// 申请项目延期
export function delayApplyAPI(id, data) {
  return request({
    url: `/testing/project/${id}/delay_apply`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 挂起检测项目
export function suspendProjectAPI(id, data) {
  return request({
    url: `/testing/project/${id}/pause`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 取消挂起检测项目
export function unsuspendProjectAPI(id, data) {
  return request({
    url: `/testing/project/${id}/unpause`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 获取项目环境类型
export function getProjectEnvAPI(id) {
  return request({
    url: `/testing/project/${id}/env`,
    method: 'get',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 查询检测流程任务
export function processTaskQueryAPI(id) {
  return request({
    url: `/testing/testProcessTask/get/${id}`,
    method: 'get',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 查询检测流程任务
export function processTaskAPI(applyId) {
  return request({
    url: `/testing/test_apply/${applyId}/tasks`,
    method: 'get',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 项目资料送审
export function projectTestApplyAPI(id, data) {
  return request({
    url: `/testing/project/${id}/test_apply`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 创建虚拟机配置
export function createVirtualMachineAPI(id, data) {
  return request({
    url: `/testing/vm/create`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 编辑虚拟机配置
export function editVirtualMachineAPI(id, data) {
  return request({
    url: `/testing/vm/${id}`,
    method: 'put',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 查询虚拟机配置
export function queryVirtualMachinesAPI(data) {
  return request({
    url: `/testing/vm/query`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 查询镜像列表
export function queryImagesAPI(params) {
  return request({
    url: window.NFVO_CONFIG.nfvo_api + '/image/vm',
    method: 'get',
    params
  })
}

// 删除虚拟机配置
export function removeVirtualMachineAPI(id) {
  return request({
    url: `/testing/vm/${id}/remove`,
    method: 'delete',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 创建设备信息
export function createDeviceAPI(id, data) {
  return request({
    url: `/testing/device/create`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 编辑设备信息
export function editDeviceAPI(deviceId, data) {
  return request({
    url: `/testing/device/${deviceId}`,
    method: 'put',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 查询设备信息
export function queryDevicesAPI(data) {
  return request({
    url: `/testing/device/query`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 删除设备信息
export function removeDeviceAPI(id) {
  return request({
    url: `/testing/device/${id}/remove`,
    method: 'delete',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 上传检测项目附件
export function uploadAttachmentAPI(data) {
  return request({
    url: 'testing/attachment/create',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 查询检测项目附件
export function queryAttachmentsAPI(data) {
  return request({
    url: '/testing/attachment/query',
    method: 'post',
    data,
    headers
  })
}

// 查询检测项目附件
export function attachmentPageAPI(data) {
  return request({
    url: `/testing/attachment/page`,
    method: 'post',
    data,
    headers
  })
}

// 查询资料送审附件列表
export function queryAttachmentsByIdListApi(data) {
  return request({
    url: `/testing/attachment/queryByIdList`,
    method: 'post',
    data,
    headers
  })
}

// 删除检测项目附件
export function removeAttachmentAPI(id, taskId, projectId) {
  return request({
    url: `/testing/attachment/${id}/delete?taskId=${taskId}&projectId=${projectId}`,
    method: 'delete',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 下载检测项目附件
export function downloadAttachmentAPI(data) {
  return request({
    url: `/testing/attachment/${data.id}/download`,
    method: 'get',
    data,
    headers
  })
}

// 更新检测项目附件
export function updateAttachmentAPI(data) {
  return request({
    url: '/testing/attachment/update',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 获取检测范围树形数据
export function getTestingTaskTreeAPI(projectId) {
  return request({
    url: `/testing/project/${projectId}/tasks/tree`,
    method: 'get',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 获取厂商及产品信息
export function getVendorInfoAPI(projectId, vendorId) {
  return request({
    url: `/testing/vendor/get/${vendorId}`,
    method: 'get',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 查询厂商列表
export function queryVendorListAPI(data) {
  return request({
    url: '/testing/vendor/query',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 获取所有厂商信息
export function getAllVendorsAPI() {
  return request({
    url: '/testing/vendor/list',
    method: 'get',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 根据ID获取厂商信息
export function getVendorByIdAPI(id) {
  return request({
    url: `/testing/vendor/get/${id}`,
    method: 'get',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 查询检测流程列表
export function queryTestProcessListAPI(data) {
  return request({
    url: '/testing/testProcesses/page',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 获取检测流程详情
export function getTestProcessDetailAPI(id) {
  return request({
    url: `/testing/testProcesses/${id}`,
    method: 'get',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 项目测试任务
export function testingTaskQueryList(data) {
  return request({
    url: `/testing/project/${data.id}/task/query`,
    method: 'post',
    headers,
    data
  })
}

// 分配测试人员
export function testingTaskAssignById(data) {
  return request({
    url: `/testing/task/${data.id}/assign`,
    method: 'post',
    headers,
    data
  })
}

// 查询分配测试人员
export function testingUserTesterQuery(data) {
  return request({
    url: `/testing/user/tester/query`,
    method: 'post',
    data
  })
}

// 开始测试
export function testingTaskStart(data) {
  return request({
    url: `/testing/task/${data.id}/start`,
    method: 'put',
    headers,
    data
  })
}

// 结束测试
export function testingTaskOver(data) {
  return request({
    url: `/testing/task/${data.id}/over`,
    method: 'put',
    headers,
    data
  })
}

// 问题清单列表
export function projectProblemQuery(data) {
  return request({
    url: `/testing/problem/query`,
    method: 'post',
    headers,
    data
  })
}

// 问题清单导出
export function projectProblemExport(data) {
  return request({
    url: `/testing/problem/export`,
    method: 'post',
    headers,
    data,
    responseType: 'blob'
  })
}

// 查询项目测试报告列表
export function testingReportProQuery(data) {
  return request({
    url: `/testing/testingReport/pro/query`,
    method: 'post',
    headers,
    data
  })
}

// 上传项目测试报告
export function testingReportProCreate(data) {
  return request({
    url: `/testing/testingReport/pro/create`,
    method: 'post',
    headers,
    data
  })
}

// 下载项目测试报告模板
export function testingReportProModelDownload(data) {
  return request({
    url: `/testing/testingReport/pro/modelDownload/${data.projectId}`,
    method: 'post',
    headers,
    data
  })
}

// 生成项目测试报告模板
export function generateWordReport(data) {
  return request({
    url: `/testing/testingReport/generateWordReport/${data.projectId}`,
    method: 'post',
    headers,
    data
  })
}

// 更新检测项目测试报告
export function testingReportUpdate(data) {
  return request({
    url: `/testing/testingReport/update`,
    method: 'post',
    headers,
    data
  })
}

// 删除检测项目测试报告
export function testingReportDeleteById(data) {
  return request({
    url: `/testing/testingReport/delete/${data.id}`,
    method: 'post',
    headers,
    data
  })
}

// 下载检测项目测试报告
export function testingReportDownloadById(data) {
  return request({
    url: `/testing/testingReport/download/${data.id}`,
    method: 'post',
    data
  })
}

// 审核检测项目测试报告
export function testingReportAudit(data) {
  return request({
    url: `/testing/testingReport/audit`,
    method: 'post',
    headers,
    data
  })
}

// 根据ID获取检测项目测试报告
export function testingReportById(data) {
  return request({
    url: `/testing/testingReport/${data.id}`,
    method: 'post',
    data
  })
}

// 查看检测项目测试报告审核信息
export function testingReportShowAuditById(data) {
  return request({
    url: `/testing/testingReport/showAudit/${data.id}`,
    method: 'post',
    headers,
    data
  })
}

// 查看项目关联的所有任务
export function testingReportQueryTask(data) {
  return request({
    url: `/testing/testingReport/queryTask/${data.projectId}`,
    method: 'post',
    headers,
    data
  })
}

// 查看项目关联的所有任务
export function testingReportQueryLastRoundTask(data) {
  return request({
    url: `/testing/testingReport/queryLastRoundTask/${data.projectId}`,
    method: 'post',
    data
  })
}

// 上传检测项目测试报告文件
export function testingFileUploadReport(data) {
  return request({
    url: `/testing/testingFile/uploadReport`,
    method: 'post',
    headers,
    data
  })
}

// 查询任务测试报告列表
export function testingReportTaskQuery(data) {
  return request({
    url: `/testing/testingReport/task/query`,
    method: 'post',
    headers,
    data
  })
}

// 上传任务测试报告
export function testingReportTaskCreate(data) {
  return request({
    url: `/testing/testingReport/task/create`,
    method: 'post',
    headers,
    data
  })
}

// 下载任务测试报告模板
export function testingReportTaskModelDownload(data) {
  return request({
    url: `/testing/testingReport/task/modelDownload/${data.projectId}/${data.processTaskId}`,
    method: 'post',
    headers,
    data
  })
}

// 查询检测项目任务
export function testingTaskQuery(data) {
  return request({
    url: `/testing/task/query`,
    method: 'post',
    headers,
    data
  })
}

// 根据ID查询检测项目任务详情
export function testingTaskQueryDetail(data) {
  return request({
    url: `/testing/task/${data.id}/detail`,
    method: 'get',
    headers,
    data
  })
}

// 分页查询资源申请记录
export function queryResourceApplyAPI(data) {
  return request({
    url: '/testing/test_apply/query',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 查询拓扑节点列表
export function getTopologyNodes(topologyId, data) {
  return request({
    url: `/testing/scene/${topologyId}/nodes`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 获取资源申请详情
export function getResourceApplyDetailAPI(id) {
  return request({
    url: `/testing/test_apply/${id}`,
    method: 'get'
  })
}

// 获取资源申请详情(重新送审)
export function getResourceApplyInfoApi(id) {
  return request({
    url: `/testing/test_apply/${id}`,
    method: 'get'
  })
}

// 查询场景拓扑信息
export function getSceneInfoTopo(projectId) {
  return request({
    url: `/testing/scene/info/?projectId=${projectId}`,
    method: 'get',
    headers
  })
}

// 查询虚拟机列表
export function instanceQuery(data) {
  return request({
    url: `/testing/vm/instance/query`,
    method: 'post',
    data,
    headers
  })
}

// 项目进入控制台
export function instanceConsole(data) {
  return request({
    url: `/testing/vm/instance/${data.vmId}/console?projectId=${data.projectId}&ip=${data.ip}`,
    method: 'get',
    data,
    headers
  })
}

// 测试任务进入控制台
export function taskInstanceConsole(data) {
  return request({
    url: `/testing/vm/instance/${data.vmId}/console?projectId=${data.projectId}&taskId=${data.taskId}&ip=${data.ip}`,
    method: 'get',
    data,
    headers
  })
}

// 查询环境类型
export function getEnvType(projectId) {
  return request({
    url: `/testing/env/${projectId}`,
    method: 'get',
    headers
  })
}

// 分页查询复测申请记录
export function queryRetestApplyAPI(data) {
  return request({
    url: '/testing/retest_apply/query',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 审核复测申请
export function auditRetestApplyAPI(id, data) {
  return request({
    url: `/testing/retest_apply/${id}/audit`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 分页查询延期申请记录
export function queryDelayApplyAPI(data) {
  return request({
    url: '/testing/delay_apply/query',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 审核延期申请
export function auditDelayApplyAPI(id, data) {
  return request({
    url: `/testing/delay_apply/${id}/audit`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 根据厂商ID查询联系人信息
export function getVendorContactByVendorIdAPI(vendorId) {
  return request({
    url: `/testing/vendorContact/getByVendorId/${vendorId}`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 审核资源申请
export function auditResourceApplyAPI(id, data) {
  return request({
    url: `/testing/test_apply/${id}/audit`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export function sceneQueryPageAPI(data) {
  return request({
    url: 'scene/sceneBasicInfo/queryPage',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

export function sceneTypePageAPI(data) {
  return request({
    url: 'scene/sceneType/queryPage',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

// 查询第一次申请检测任务
export function getFirstTestTaskAPI(projectId) {
  return request({
    url: '/testing/task/first',
    method: 'get',
    params: {
      projectId
    },
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 查询当前轮次申请检测任务
export function getCurrentTestTaskAPI(projectId) {
  return request({
    url: '/testing/task/current',
    method: 'get',
    params: {
      projectId
    },
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 测试任务下载报告模版
export function downModelByTaskId(data) {
  return request({
    url: `/testing/testingReport/task/downModelBy/${data.taskId}`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 问题管理api ↓
// 获取项目检测分页查询
export function getProblemProjectPage(data) {
  return request({
    url: '/testing/problem/projectPage',
    method: 'post',
    data,
    headers
  })
}

// 获取测试任务分页查询
export function getProblemTestTaskPage(data) {
  return request({
    url: '/testing/problem/testTaskPage',
    method: 'post',
    data,
    headers
  })
}

// 获取测试用例分页查询
export function getProblemTestCasePage(data) {
  return request({
    url: '/testing/problem/testCasePage',
    method: 'post',
    data,
    headers
  })
}

// 获取漏洞情报分页查询
export function getProblemLoopholeGenPage(data) {
  return request({
    url: '/testing/problem/loopholeGenPage',
    method: 'post',
    data,
    headers
  })
}

// 问题管理导入下载错误信息
export function problemDownExcel(data) {
  return request({
    url: '/testing/problem/downExcel',
    method: 'post',
    data,
    headers,
    responseType: 'blob'
  })
}

// 获取分类树结构
export function problemTree(data) {
  return request({
    url: `/testing/problem/tree?${data.type}`,
    method: 'get',
    data,
    headers
  })
}
// 问题管理api ↑

// 获取操作记录
export function operaRecordQuery(data) {
  return request({
    url: '/testing/operaRecord/query',
    method: 'post',
    data,
    headers
  })
}

// 新增操作记录备注
export function operaRecordAddTaskRemark(data) {
  return request({
    url: '/testing/operaRecord/addTaskRemark',
    method: 'post',
    data,
    headers
  })
}

// 编辑操作记录备注
export function operaRecordUpdateTaskRemark(data) {
  return request({
    url: '/testing/operaRecord/updateTaskRemark',
    method: 'post',
    data,
    headers
  })
}

// 获取网络列表
export function getNetworkListAPI() {
  return request({
    url: '/testing/scene/networks',
    method: 'get',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 根据项目ID查询最新的一条资源申请记录
export function getLatestResourceApplication(projectIds) {
  return request({
    url: '/testing/test_apply/latest',
    method: 'post',
    data: projectIds,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}


// 检测项目详情 => 设备日志
// 获取会话记录列表
export function getSessionRecordApi(data) {
  return request({
    url: '/testing/deviceLog/sessionRecords',
    method: 'post',
    data,
    headers
  })
}

// 导出会话记录
export function exportSessionRecordsApi(data) {
  return request({
    url: '/testing/deviceLog/sessionRecordsExport',
    method: 'post',
    headers,
    data,
    responseType: 'blob'
  })
}

// 下载会话
export function downloadSessionApi(sessionId) {
  return request({
    url: `/testing/deviceLog/downloadSession/${sessionId}`,
    method: 'post',
    responseType: 'blob'
  })
}

// 回看会话记录
export function replaySessionRecordApi(sessionId, projectId) {
  return request({
    url: `/testing/vm/instance/replay/${sessionId}?projectId=${projectId}`,
    method: 'get',
    headers
  })
}

// 获取命令记录列表
export function getCommandRecordApi(data) {
  return request({
    url: '/testing/deviceLog/commandRecords',
    method: 'post',
    data,
    headers
  })
}

// 导出命令记录
export function exportCommandRecordApi(data) {
  return request({
    url: '/testing/deviceLog/commandRecordsExport',
    method: 'post',
    headers,
    data,
    responseType: 'blob'
  })
}

// 获取文件传输记录列表
export function getFileTransferRecordApi(data) {
  return request({
    url: '/testing/deviceLog/fileTransferRecords',
    method: 'post',
    data,
    headers
  })
}

// 导出文件传输记录
export function exportFileTransferApi(data) {
  return request({
    url: '/testing/deviceLog/fileTransferRecordsExport',
    method: 'post',
    headers,
    data,
    responseType: 'blob'
  })
}

// 获取设备详情(网络编排类型)
export function getNodeItemDetailApi(id) {
  return request({
    url: `/testing/scene/node/${id}/info`,
    method: 'get',
    headers
  })
}

// 快照列表(虚拟机类型)
export function getVmSnapshotListApi(data) {
  return request({
    url: '/testing/vm/instance/snapshots/list',
    method: 'post',
    data,
    headers
  })
}

// 快照列表(网络编排类型)
export function getNetSnapshotListApi(data) {
  return request({
    url: '/testing/scene/node/snapshots/list',
    method: 'post',
    data,
    headers
  })
}

// 查询快照列表(虚拟机类型)
export function createSnapshotApi(data) {
  return request({
    url: '/testing/vm/instance/snapshots/create',
    method: 'post',
    data,
    headers
  })
}

// 异步恢复虚拟机快照(虚拟机类型)
export function asyncRestoreSnapshotApi(data) {
  return request({
    url: '/testing/vm/instance/snapshots/restore/async',
    method: 'post',
    data,
    headers
  })
}

// 获取虚拟机实例详情(虚拟机类型)
export function getVirtualDetailApi(vmId) {
  return request({
    url: `/testing/vm/instance/${vmId}/info`,
    method: 'post',
    headers
  })
}

// 获取未修复问题列表
export function getPageUnrepairedApi(data) {
  return request({
    url: '/testing/problem/pageUnrepaired',
    method: 'post',
    data,
    headers
  })
}

// 任务id获取任务状态
export function getTaskStatusApi(data) {
  return request({
    url: `/testing/task/${data.id}/getStatus`,
    method: 'get',
    headers,
    data
  })
}

// 项目id获取项目状态
export function getProjectStatusApi(data) {
  return request({
    url: `/testing/project/${data.id}/getStatus`,
    method: 'get',
    headers,
    data
  })
}

// 根据taskId获取任务负责人列表
export function getTaskQueryPersonApi(data) {
  return request({
    url: `/testing/task/${data.taskId}/queryPerson`,
    method: 'post',
    data,
    headers
  })
}

// 获取项目负责人所属角色的接口
export function getProjectCommanderRoleIds(data) {
  return request({
    url: '/testing/project/getProjectCommanderRoleIds',
    method: 'post',
    headers,
    data
  })
}

// 测试环境设为已部署
export function setEnvAsDeployedApi(projectId) {
  return request({
    url: `/testing/env/deployed?projectId=${projectId}`,
    method: 'get',
    headers
  })
}

// 档案库-获取档案列表
export function getSamplePage(data) {
  return request({
    url: '/testing/sample/page',
    method: 'post',
    data,
    headers
  })
}

// 档案库-删除样本
export function sampleRemove(data) {
  return request({
    url: '/testing/sample/remove',
    method: 'post',
    data,
    headers
  })
}

// 档案库-档案库详情
export function getSampleProjectById(data) {
  return request({
    url: `/testing/sample/project/${data.id}`,
    method: 'get',
    data,
    headers
  })
}

// 档案库-厂商及产品信息详情
export function getSampleVendorById(data) {
  return request({
    url: `/testing/sample/vendor/${data.id}`,
    method: 'get',
    data,
    headers
  })
}

// 档案库-测试任务-查询检测项目任务
export function getSampleTaskPage(data) {
  return request({
    url: `/testing/sample/taskPage`,
    method: 'post',
    data,
    headers
  })
}

// 档案库-测试报告-查看项目关联的所有任务
export function getSampleQueryTask(data) {
  return request({
    url: `/testing/sample/queryTask/${data.projectId}`,
    method: 'post',
    data,
    headers
  })
}

// 档案库-测试报告-查询项目测试报告列表
export function getSampleProQuery(data) {
  return request({
    url: `/testing/sample/pro/query`,
    method: 'post',
    data,
    headers
  })
}

// 档案库-测试报告-查询任务测试报告列表
export function getSampleTaskQuery(data) {
  return request({
    url: `/testing/sample/task/query`,
    method: 'post',
    data,
    headers
  })
}

// 档案库-问题清单-样本库问题分页查询
export function getSampleProblemPage(data) {
  return request({
    url: `/testing/sample/problemPage`,
    method: 'post',
    data,
    headers
  })
}

// 档案库-申请记录-分页查询资源申请记录
export function getSampleTestApplyPage(data) {
  return request({
    url: `/testing/sample/testApplyPage`,
    method: 'post',
    data,
    headers
  })
}

// 档案库-申请记录-分页查询复测申请记录
export function getSampleRetestApplyPage(data) {
  return request({
    url: `/testing/sample/retestApplyPage`,
    method: 'post',
    data,
    headers
  })
}

// 档案库-申请记录-分页查询延期申请记录
export function getSampleDelayApplyPage(data) {
  return request({
    url: `/testing/sample/delayApplyPage`,
    method: 'post',
    data,
    headers
  })
}

// 档案库-设备日志-分页查询会话记录列表
export function getSampleSessionRecordPage(data) {
  return request({
    url: `/testing/sample/sessionRecordPage`,
    method: 'post',
    data,
    headers
  })
}

// 档案库-设备日志-分页查询命令记录列表
export function getSampleCommandRecordPage(data) {
  return request({
    url: `/testing/sample/commandRecordPage`,
    method: 'post',
    data,
    headers
  })
}

// 档案库-设备日志-分页查询文件传输记录列表
export function getSampleFileTransferRecordPage(data) {
  return request({
    url: `/testing/sample/fileTransferRecordPage`,
    method: 'post',
    data,
    headers
  })
}

// 档案库-附件-查询第一次申请检测任务
export function getSampleFirst(data) {
  return request({
    url: `/testing/sample/first`,
    method: 'get',
    data,
    headers
  })
}

// 档案库-查询资料送审附件列表
export function getSampleListAttachments(taskId, projectId) {
  return request({
    url: `/testing/sample/listAttachments?taskId=${taskId}&projectId=${projectId}`,
    method: 'get',
    headers
  })
}

// 档案库-分页查询附件列表
export function getAttachmentsPage(data) {
  return request({
    url: `/testing/sample/pageAttachments`,
    method: 'post',
    data,
    headers
  })
}

// 档案库-测试任务-获取任务详情
export function getSampleTaskDetail(data) {
  return request({
    url: `/testing/sample/task/${data.id}/detail`,
    method: 'get',
    data,
    headers
  })
}


// 档案库-测试任务-测试用例-获取分类树结构
export function getSampleCaseCategoryTree(data) {
  return request({
    url: `/testing/sample/caseCategoryTree`,
    method: 'get',
    data,
    headers
  })
}

// 档案库-测试任务-测试用例-分页查询测试用例列表
export function getSampleCasePage(data) {
  return request({
    url: `/testing/sample/casePage`,
    method: 'post',
    data,
    headers
  })
}
// 检测项目分配选择人员
export function taskAssignPage(data) {
  return request({
    url: `/testing/task/assign/page`,
    method: 'post',
    data,
    headers
  })
}
