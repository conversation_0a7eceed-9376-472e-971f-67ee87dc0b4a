<template>
  <div v-loading="loading" class="drawer-wrap orchestration-drawer-wrap">
    <div class="drawer-wrap-content">
      <el-form ref="form" :model="formData" :rules="rules" label-width="120px">
        <el-form-item v-if="!disabledType" label="虚拟设备名称" prop="deviceName">
          {{ formData.deviceName }}
        </el-form-item>
        <template v-if="!disabledType && (formData.virtual_type === 'qemu' || formData.virtual_type === 'docker' || formData.virtual_type === 'global_network' || formData.virtual_type === 'global_qemu')">
          <el-form-item label="虚拟设备厂商" prop="vendor">
            {{ formData.vendor }}
          </el-form-item>
          <el-form-item label="虚拟设备型号" prop="model">
            {{ formData.model }}
          </el-form-item>
        </template>
        <el-form-item :label="disabledType ? '名称' : '虚拟实例名称'" prop="name">
          {{ formData.name || '-' }}
        </el-form-item>
        <el-form-item label="描述" prop="description">
          {{ formData.description || '-' }}
        </el-form-item>
        <el-form-item v-if="!disabledType" label="控制台" prop="console_type">
          {{ viewConsoleType || '-' }}
        </el-form-item>
        <el-form-item label="控制台连接地址" prop="description">
          <div v-if="formData.console_info && formData.console_info.length">
            <div v-for="item in formData.console_info" :key="item.console_type">{{ item.console_type }}{{ item.ip }}:{{ item.port }}</div>
          </div>
          <span v-else>-</span>
        </el-form-item>
        <template v-if="!disabledType && formData.virtual_type === 'qemu'">
          <el-form-item label="是否为工作站" prop="is_workstation">
            {{ formData.is_workstation ? '是' : '否' }}
          </el-form-item>
          <el-form-item label="CPU" prop="cpu">
            {{ formData.cpu }} 核
          </el-form-item>
          <el-form-item label="内存" prop="ram">
            {{ formData.ram }} {{ ramUnit }}
          </el-form-item>
          <el-form-item label="系统盘大小" prop="sys_disk_size">
            {{ formData.sys_disk_size }} GB
          </el-form-item>
          <el-form-item label="登录用户名" prop="admin_user">
            {{ formData.admin_user }}
          </el-form-item>
          <el-form-item label="登录密码">
            {{ formData.admin_pass }}
          </el-form-item>
        </template>
        <el-form-item v-if="formData.virtual_type === 'qemu' || formData.virtual_type === 'docker'" label="产品端口" class="port-form" >
          <t-table-view
            ref="tableView"
            :height="null"
            :loading="loading"
            :data="formData.ports"
            :total="formData.ports.length"
            :multiple-page="false"
            type="list"
          >
            <el-table-column label="名称" show-overflow-tooltip>
              <template slot-scope="scope">
                {{ scope.row.name }}
              </template>
            </el-table-column>
            <el-table-column label="占用情况" show-overflow-tooltip>
              <template slot-scope="scope">
                {{ scope.row.link_to ? `${scope.row.link_to.node_name} / ${scope.row.link_to.port_name}` : '-' }}
              </template>
            </el-table-column>
            <el-table-column label="IP地址" show-overflow-tooltip>
              <template slot-scope="scope">
                <span>{{ scope.row.ipaddr || '-' }}</span>
              </template>
            </el-table-column>
          </t-table-view>
        </el-form-item>
        <el-form-item v-if="formData.virtual_type === 'cloud_router'" label="LAN口" class="port-form" prop="lan" >
          <t-table-view
            ref="tableView"
            :height="null"
            :loading="loading"
            :data="formData.ports"
            :total="formData.ports.length"
            :multiple-page="false"
            type="list"
          >
            <el-table-column label="CIDR" show-overflow-tooltip>
              <template slot-scope="scope">
                {{ scope.row.cidr || '-' }}
              </template>
            </el-table-column>
            <el-table-column label="网关" show-overflow-tooltip>
              <template slot-scope="scope">
                {{ scope.row.gateway || '-' }}
              </template>
            </el-table-column>
          </t-table-view>
        </el-form-item>
      </el-form>
    </div>
    <div class="drawer-footer">
      <el-button type="primary" @click="close">确定</el-button>
    </div>
  </div>
</template>
<script>
import tTableView from '../../table-view/index.vue'
import { getNodeItem } from '../api/orchestration'
export default {
  components: { tTableView },
  props: {
    data: {
      type: Object,
      default: () => {
        return {}
      }
    },
    type: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      consoleMap: {
        'vnc': 'VNC',
        'rdp': '远程桌面',
        'ssh': 'WebSSH',
        'serial': '串口控制台',
        'webshell': '命令行'
      },
      loading: true,
      ramUnit: 'GB', // 内存单位
      ramMax: 10485760, // ram的可输入最大值
      formData: {
        'virtual_type': '',
        'name': '',
        'deviceName': '',
        'vendor': '',
        'model': '',
        'ip_address': '',
        'ports': [],
        'is_workstation': false,
        'console_type': '',
        'cpu': undefined,
        'ram': undefined,
        'sys_disk_size': undefined,
        'admin_user': '',
        'admin_pass': '',
        'description': ''
      },
      rules: {}
    }
  },
  computed: {
    viewConsoleType() {
      let str = ''
      this.formData.console_type && this.formData.console_type.split(',').forEach((item, index) => {
        str += this.consoleMap[item] + (index < this.formData.console_type.split(',').length - 1 ? '、' : '')
      })
      return str
    },
    disabledType() {
      return this.type !== 'allPermissions' && this.type !== 'templatePermissions'
    }
  },
  created() {
    const data = this.data.node.data
    this.formData['virtual_type'] = data['virtual_type']
    this.formData['name'] = data['name']
    this.formData['deviceName'] = data['deviceName']
    this.formData['vendor'] = data['vendor']
    this.formData['model'] = data['model']
    this.formData['ports'] = data['ports']
    this.formData['cpu'] = data['cpu']
    this.formData['is_workstation'] = data['is_workstation'] || false
    this.formData['console_type'] = data['console_type']
    this.formData['console_info'] = data['console_info']
    const unitArr = this.$options.filters['transStore'](data['ram'], 'MB').split(' ')
    this.ramUnit = unitArr[1]
    this.changeUnit(this.ramUnit)
    this.formData['ram'] = Number(unitArr[0])
    this.formData['sys_disk_size'] = data['sys_disk_size'] || undefined
    this.formData['admin_user'] = data['admin_user'] || ''
    this.formData['image_os_type'] = data['image_os_type']
    this.formData['admin_pass'] = data['admin_pass'] || ''
    this.formData['description'] = data['description'] || ''
    this.getNodeItem()
  },
  methods: {
    // 重新获取node的端口
    'getNodeItem': function() {
      getNodeItem(this.data.node.data.node_id)
        .then(res => {
          this.formData['ports'] = res.data.data.ports || []
          this.formData['console_info'] = res.data.data.console_info || []
          this.loading = false
        })
        .catch((error) => {
          this.loading = false
          console.log(error)
        })
    },
    // 改变单位改变ram的可输入最大值
    'changeUnit': function(value) {
      if (value === 'MB') {
        this.ramMax = 10 * 1024 * 1024
      } else if (value === 'GB') {
        this.ramMax = 10 * 1024
      } else if (value === 'TB') {
        this.ramMax = 10
      }
    },
    'close': function() {
      this.$emit('close')
    }
  }
}
</script>
<style lang="less">
.orchestration-drawer-wrap {
  .el-form {
    & >.el-form-item > .el-form-item__content > .el-input,
    & >.el-form-item > .el-form-item__content > .el-select,
    & >.el-form-item > .el-form-item__content > .el-textarea {
      width: 90%;
    }
  }
  .port-form {
    th {
      padding: 0;
    }
    >.el-form-item__label {
      width: 70px !important;
    }
    >.el-form-item__content {
      margin-left: 70px !important;
      .data-table-footer {
        display: none;
      }
    }
  }
}
</style>
