<template>
  <div class="content-wrap-layout">
    <category :style="{'height': fold ? '0' : 'unset', 'overflow': fold ? 'hidden' : 'unset'}" :category-name="moduleName" @classificationType="classificationType"/>
    <page-table
      ref="table"
      :default-selected-arr="defaultSelectedArr"
      :filter-data="{ 'contentCategoryId': String(queryParameters.contentCategoryId || ''), 'contentType': String(queryParameters.contentType || ''), 'contentLevel': String(queryParameters.contentLevel || '')}"
      :cache-pattern="true"
      default-selected-key="id"
      @refresh="refresh"
      @link-event="linkEvent"
      @on-select="tabelSelect"
      @on-current="tabelCurrent"
    >
      <action-menu
        slot="action"
        :module-name="moduleName"
        :select-item="selectItem"
        @call="actionHandler"
      />
    </page-table>
    <el-drawer
      :visible.sync="detailShow"
      :modal="false"
      size="75%"
      append-to-body
      class="detail-view"
      @close="closeDetail"
    >
      <t-detail v-if="detailShow" />
    </el-drawer>
  </div>
</template>

<script>
import moduleConf from './config'
import pageTable from './table/index.vue'
import actionMenu from './action/index.vue'
import category from './category/index.vue'
import tDetail from '@/views/admin/hrm/salary/CourseEditor.vue'
import moduleMixin from '@/packages/mixins/module_list'
import lodash from 'lodash'
export default {
  name: 'Content',
  components: {
    pageTable,
    actionMenu,
    category,
    tDetail
  },
  mixins: [moduleMixin],
  data() {
    return {
      fold: false,
      listRouterName: 'trainingContent',
      moduleName: moduleConf.name,
      selectItem: [],
      defaultSelectedArr: [],
      num: 0,
      queryParameters: {}
    }
  },
  methods: {
    // 列表点击
    linkEvent: function({ name, row, params }) {
      this.$router.push({ name: name, params: params })
    },
    classificationType: lodash.debounce(function(param) {
      this.queryParameters = param
      if (!this.$store.state.cache[this.moduleName]) {
        const obj = {
          data: { searchShow: true },
          key: this.moduleName
        }
        this.$store.commit('SET_CACHE', obj)
      }
      this.$nextTick(() => {
        if (this.num != 0) {
          this.$refs['table']['pageCurrent'] = 1
        } else {
          this.num = this.num + 1
        }
        this.$refs['table'].getList()
      })
    }, 500),
    // 返回已选
    tabelSelect: function(data) {
      this.selectItem = data
    },
    // 返回单选
    tabelCurrent: function(row) {
      this.selectItem = [row]
    },
    // action menu 事件
    actionHandler: function(type, data) {
      switch (type) {
        case 'refresh':
          this.$refs['table'].getList(data)
          break
        case 'fold':
          this.fold = data
          break
      }
    },
    refresh: function() {}
  }
}
</script>
