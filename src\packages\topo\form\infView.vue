<template>
  <div v-loading="loading" class="drawer-wrap orchestration-drawer-wrap">
    <div class="drawer-wrap-content">
      <el-form ref="form" :model="formData" :rules="rules" label-width="140px">
        <el-form-item v-if="!disabledType" label="图形设备名称" prop="deviceName">
          {{ formData.deviceName }}
        </el-form-item>
        <el-form-item :label="disabledType ? '名称' : '图形名称'" prop="name">
          {{ formData.name || '-' }}
        </el-form-item>
        <el-form-item label="描述" prop="description">
          {{ formData.description || '-' }}
        </el-form-item>
      </el-form>
    </div>
    <div class="drawer-footer">
      <el-button type="primary" @click="close">确定</el-button>
    </div>
  </div>
</template>
<script>
export default {
  components: {},
  props: {
    data: {
      type: Object,
      default: () => {
        return {}
      }
    },
    type: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      loading: false,
      formData: {
        'name': '',
        'deviceName': '',
        'description': ''
      },
      rules: {}
    }
  },
  computed: {
    disabledType() {
      return this.type !== 'allPermissions' && this.type !== 'templatePermissions'
    }
  },
  created() {
    const data = this.data.node.data
    this.formData['name'] = data['name']
    this.formData['deviceName'] = data['deviceName']
    this.formData['description'] = data['description'] || ''
  },
  methods: {
    'close': function() {
      this.$emit('close')
    }
  }
}
</script>
<style lang="less">
.orchestration-drawer-wrap {
  .el-form {
    & >.el-form-item > .el-form-item__content > .el-input,
    & >.el-form-item > .el-form-item__content > .el-select,
    & >.el-form-item > .el-form-item__content > .el-textarea {
      width: 90%;
    }
  }
  .port-form {
    th {
      padding: 0;
    }
    >.el-form-item__label {
      width: 70px !important;
    }
    >.el-form-item__content {
      margin-left: 70px !important;
      // margin-top: 40px;
      .data-table-footer {
        display: none;
      }
    }
  }
}
</style>
