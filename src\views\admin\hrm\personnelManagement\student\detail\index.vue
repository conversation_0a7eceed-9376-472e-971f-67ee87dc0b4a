<template>
  <detail-view
    :loading="loading"
    :data="data"
    :id="id"
    :view-item="viewItem"
    title-key="realname"
    mode="drawer"
    desc-key="description"
    class="learningProcess-wrap"
  />
</template>
<script>
import moduleConf from '../config'
import detailView from '@/packages/detail-view/index'
import detailTable from './detail-table'
export default {
  components: {
    detailView,
    detailTable
  },
  data() {
    return {
      moduleName: moduleConf.name,
      id: null, // 资源ID
      data: null, // 资源数据对象
      loading: false,
      viewItem: [
        {
          transName: '',
          name: 'process',
          component: detailTable
        }
      ]
    }
  },
  mounted() {
    this.id = this.$route.query.processId
    this.data = {
      realname: this.$route.query.realname,
      description: `${this.$route.query.realname}的学习历程`
    }
  }
}
</script>
<style lang="scss" scoped>
/deep/.el-tabs--card > .el-tabs__header .el-tabs__item.is-active {
  display: none;
}
</style>
