<template>
  <div class="drawer-wrap">
    <div class="resource-table">
      <!-- 操作区 -->
      <div class="operation-wrap">
        <div class="operation-left">
          <slot name="action" />
          <el-button type="primary" icon="el-icon-refresh" @click="refresh" />
        </div>
        <div class="operation-right">
          <el-badge :value="searchBtnShowNum">
            <el-button icon="el-icon-search" @click="openSearch">搜索</el-button>
          </el-badge>
        </div>
      </div>
      <!-- 搜索区 -->
      <t-search-box
        v-show="searchView"
        :search-key-list="searchKeyListView"
        default-placeholder="默认搜索名称"
        @search="searchMultiple"
      />
      <!-- 列表 -->
      <t-table-view
        ref="tableView"
        :height="height"
        :single="single"
        :loading="tableLoading"
        :data="tableData"
        :total="tableTotal"
        :page-size="pageSize"
        :current="pageCurrent"
        :select-item="selectItem"
        current-key="id"
        @on-select="onSelect"
        @on-current="onCurrent"
        @on-change="changePage"
        @on-sort-change="onSortChange"
        @on-page-size-change="onPageSizeChange"
      >
        <el-table-column v-for="item in columnsViewArr" :key="item" :min-width="colMinWidth" :label="columnsObj[item].title" :fixed="columnsObj[item].master ? 'left' : false" show-overflow-tooltip>
          <template slot-scope="scope">
            <span >{{ scope.row[item] || "-" }}</span>
          </template>
        </el-table-column>
      </t-table-view>
    </div>
    <!-- 底部 -->
    <div class="drawer-footer">
      <el-button :disabled="!selectItem.length" type="primary" @click="confirm">确定</el-button>
      <el-button type="text" @click="close">取消</el-button>
    </div>
  </div>
</template>

<script>
import module from '../config'
import tSearchBox from '@/packages/search-box/index.vue'
import tTableView from '@/packages/table-view/index.vue'
import tableTdMultiCol from '@/packages/table-config/table-td-multi-col.vue'
import mixinsPageTable from '@/packages/mixins/page_table'
import { getCategory } from '@/api/accumulate/category'
export default {
  components: {
    tSearchBox,
    tTableView,
    tableTdMultiCol
  },
  mixins: [mixinsPageTable],
  data() {
    return {
      moduleName: module.name,
      // 搜索配置项
      searchKeyList: [
        { key: 'categoryName', label: '分类名称', master: true }
      ],
      // 所有可配置显示列 master：不可隐藏 title:列名称
      columnsObj: {
        'categoryName': {
          title: '分类名称',
          master: true
        }
      },
      // 当前显示列key表 默认，如果localStorage有数据将被覆盖
      columnsViewArr: [
        'categoryName'
      ],
      tableData: []
    }
  },
  methods: {
    getList: function(showLoading = true) {
      if (showLoading) {
        this.tableLoading = true
      }
      const params = this.getPostData('page', 'limit')
      params.categoryTypes = [1, 2, 3]
      getCategory(params).then(res => {
        this.tableData = res.data.records
        this.tableTotal = res.data.total
        this.tableLoading = false
      }).catch(() => {
        this.tableLoading = false
      })
    },
    'close': function() {
      this.$emit('close')
    },
    'confirm': function() {
      this.$emit('call', 'confirm_classify', this.selectItem)
    }
  }
}
</script>
