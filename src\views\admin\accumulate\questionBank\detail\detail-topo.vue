<template>
  <Topo v-if="topologyId" ref="topo" :topo-id="topologyId" topo-type="firingPermissions"/>
</template>

<script>
import Topo from '@/packages/topo/index'
import { getQuestionBankItem } from '@/api/accumulate/questionBank'
export default {
  components: {
    Topo
  },
  data() {
    return {
      topologyId: ''
    }
  },
  created() {
    this.getData(this.$route.params.id)
  },
  methods: {
    // 根据id获取详情数据
    'getData': function(id) {
      return new Promise((resolve, reject) => {
        getQuestionBankItem({ id: id }).then(res => {
          this.topologyId = res['data']['topologyTemplateId']
          resolve()
        }).catch(() => {
          this.data = null
          reject()
        })
      })
    }
  }
}
</script>
