<template>
  <el-row :gutter="20">
    <el-col :span="12" style="max-height: 100%;">
      <detail-card title="基本信息" class="detail-wrap">
        <el-form slot="content" ref="form" :model="formData" label-position="left" label-width="110px">
          <el-form-item label="考试名称:">
            <div>{{ formData.examName }}</div>
          </el-form-item>
          <el-form-item v-if="formData.credentialName" label="证书名称:">
            <div>{{ formData.credentialName }}</div>
          </el-form-item>
          <el-form-item v-if="formData.credentialName" label="证书等级:">
            <div>{{ level }}</div>
          </el-form-item>
          <el-form-item label="考试试卷:">
            <a
              v-if="formData.selectedExamPaper"
              :href="'/baseResource/exam'"
              @click.prevent="goToPaperDetail(formData)"
            >
              {{ formData.selectedExamPaper }}
            </a>
            <span v-else>-</span>
          </el-form-item>
          <el-form-item label="报名时间:">
            <div>{{ formData.startEndTime }}</div>
          </el-form-item>
          <el-form-item label="考试开始时间:">
            <div>{{ formData.date1 }}</div>
          </el-form-item>
          <el-form-item label="考试时长:" >
            <div>{{ formData.time }}</div>
          </el-form-item>
          <el-form-item v-if="$route.params.cerType === '1'" label="考试链接:" >
            <div v-if="formData.published == '1'">
              <span disabled>{{ `${formData.examUrl}` }}</span>
              <i class="el-icon-document-copy ml-3" style="color: #33CCFF;cursor:pointer" @click="copyMsg(formData.examUrl)"/>
            </div>
            <div v-else>-</div>
          </el-form-item>
          <el-form-item label="考试须知:">
            <div v-html="formData.notice"/>
          </el-form-item>
          <el-form-item label="备注:">
            <div>{{ formData.description }}</div>
          </el-form-item>
        </el-form>
      </detail-card>
    </el-col>
    <el-col :span="12"/>
  </el-row>
</template>

<script>
import module from './config.js'
import detailCard from '@/packages/detail-view/detail-card.vue'
import myEditor from '@/packages/editor/index.vue'
import { queryById } from '@/api/exam/index.js'

export default {
  name: module.name,
  components: {
    detailCard,
    myEditor
  },
  mixins: [],
  data() {
    return {
      loading: false,
      formData: {
        id: '',
        examName: '',
        credentialName: '',
        level: '',
        selectedExamPaper: '',
        date1: '',
        time: '',
        startEndTime: '',
        notice: '',
        description: '',
        published: 0,
        examUrl: ''
      },
      examLevel: [
        { value: 1, label: '初级' },
        { value: 2, label: '中级' },
        { value: 3, label: '高级' }
      ],
      level: ''
    }
  },
  created() {
    this.getEaxmById()
  },
  methods: {
    getEaxmById() {
      const id = Number(this.$route.params.id)
      queryById({ id: id }).then((res) => {
        if (res.code === 0) {
          this.formData.id = res.data.id
          this.formData.examName = res.data.name
          this.formData.credentialName = res.data.certificateName
          this.formData.level = res.data.certificateLevel
          this.formData.published = res.data.published
          if (this.formData.published == '1') {
            const devExamLink = location.origin.replace(location.port, '8080') + `/exam/examOperations/${this.formData.id}`
            const prodExamLink = location.origin.replace(location.port, '18080') + `/exam/examOperations/${this.formData.id}`
            this.formData.examUrl = process.env.NODE_ENV == 'development' ? devExamLink : prodExamLink
          }
          this.formData.selectedExamPaper = res.data.paperName
          this.formData.date1 = res.data.beginTime.split(' ')[0] + ' ' + res.data.beginTime.split(' ')[1]
          this.formData.time = res.data.distanceTime
          this.formData.startEndTime = (res.data.regBeginTime && res.data.regEndTime) ? res.data.regBeginTime + ' 至 ' + res.data.regEndTime : '-'
          this.formData.notice = res.data.description
          this.formData.description = res.data.mark
          this.level = this.examLevel[this.formData.level - 1].label
        }
      })
    },
    copyMsg(data) {
      const url = data
      const oInput = document.createElement('input')
      oInput.value = url
      document.body.appendChild(oInput)
      oInput.select()
      document.execCommand('Copy')
      this.$message({
        message: '已成功复制到剪切板',
        type: 'success'
      })
      oInput.remove()
    },
    // 试卷详情
    goToPaperDetail(data) {
      window.open(`/baseResource/exam?examName=${data.selectedExamPaper}`, '_blank')
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep .detail-wrap .detail-card-body {
  max-height: 87vh;
  overflow: auto;
  img {
    margin-right: 10px;
  }
}
</style>
