<template>
  <div class="detail-tabs-content">
    <div v-if="coursewareList.length" class="courseware-wrap">
      <div class="buttons-wrap">
        <el-button type="primary" @click="downloadCourseware(coursewareList[0])">下载课件</el-button>
      </div>
      <div class="grid-view">
        <div v-if="coursewareUrl" class="button-wrap">
          <el-link :underline="false" type="primary" size="small" @click="toggleFullscreen">{{ isFullscreen ? '退出全屏' : '全屏预览' }}</el-link>
        </div>
        <iframe id="frame" :src="coursewareUrl" allow="fullscreen" />
      </div>
    </div>
    <el-empty v-else :image="emptyImg" :image-size="178" class="empty-data">
      <div slot="description">暂无数据</div>
    </el-empty>
  </div>
</template>

<script>
import detailCard from '@/packages/detail-view/detail-card.vue'
import FileList from '@/components/FileList/index.vue'
import { contentdetail } from '@/api/teacher/index.js'
import { getFileSuffix } from '@/utils'
import { handlekkfilePreview } from '@/views/admin/hrm/content/create/util.js'
import { api as fullscreen } from 'vue-fullscreen'
export default {
  components: {
    detailCard,
    FileList
  },
  props: {
    id: {
      type: String | Number,
      default: ''
    },
    teachingName: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      emptyImg: require('@/packages/table-view/nodata.png'),
      coursewareList: [],
      coursewareUrl: '', // 课件资料预览
      isFullscreen: false
    }
  },
  created() {
    this.getCourseware()
  },
  methods: {
    // 获取课件资料
    getCourseware() {
      contentdetail({ contentId: this.id, format: 'courseware' }).then(res => {
        if (res.code == 0) {
          this.handleIframepreview()
          this.coursewareList = res.data
          this.coursewareList.map(item => {
            item.name = item.fileName || item.attachmentUrl
            item.url = window.location.origin + item.attachmentUrl
          })
          // 默认显示第一个附件
          const file = this.coursewareList[0]
          if (file) {
            this.coursewareUrl = this.viewFileUrl + encodeURIComponent(btoa(file.url)) // kkfile 预览
          }
        }
      })
    },
    // kkfile预览
    async handleIframepreview() {
      await this.$nextTick()
      handlekkfilePreview()
    },
    // 全屏预览
    async toggleFullscreen() {
      const wrapperEl = this.$el.querySelector('.grid-view')
      await fullscreen.toggle(wrapperEl, {
        callback: (val) => {
          this.isFullscreen = val
        }
      })
      this.isFullscreen = fullscreen.isFullscreen
    },
    // 下载课件资料
    downloadCourseware(item) {
      const fileTypes = ['doc', 'docx', 'xlsx', 'xls', 'ppt', 'pptx']
      const type = getFileSuffix(item.url)
      let newFileUrl = ''
      if (item.url) {
        if (fileTypes.includes(type)) {
          newFileUrl = new URL(item.url).pathname
        }
        const fileUrl = fileTypes.includes(type) ? newFileUrl : item.url
        fetch(fileUrl, {
          method: 'get',
          responseType: 'blob'
        }).then((response) => response.blob())
          .then((blob) => {
            const a = document.createElement('a')
            const URL = window.URL || window.webkitURL
            const herf = URL.createObjectURL(blob)
            a.href = herf
            a.download = this.teachingName + '.' + type
            document.body.appendChild(a)
            a.click()
            document.body.removeChild(a)
            URL.revokeObjectURL(herf)
          })
      }
    }
  }
}
</script>
<style scoped lang="less">
.courseware-wrap {
  height: 100%;
  display: flex;
  flex-direction: column;
  .buttons-wrap {
    text-align: right;
  }
  .grid-view {
    flex: 1;
    min-height: 0;
    position: relative;
    width: 100%;
    margin-top: 10px;
    .button-wrap {
      position: absolute;
      top: 8px;
      right: 60px;
      .el-link {
        color: var(--color-600);
      }
    }
    #frame {
      width: 100%;
      height: 100%;
      border: 0px;
      margin: 0;
    }
  }
}
::v-deep .empty-data {
  padding: 20px 0;
  .el-empty__description {
    margin-top: 0;
    font-size: 14px;
    color: var(--neutral-600);
  }
}

</style>
