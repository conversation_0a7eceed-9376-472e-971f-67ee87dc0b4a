<template>
  <div v-loading="loading" class="dialog-wrap">
    <el-alert :closable="false" type="warning">
      <div slot="title">
        <p>请确认环境已部署完成，可以开始测试。</p >
      </div>
    </el-alert>
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>

<script>
import modalMixins from '@/packages/mixins/modal_form'
import { setEnvAsDeployedApi } from '@/api/testing/index'
export default {
  mixins: [modalMixins],
  props: {
    data: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      loading: false
    }
  },
  methods: {
    close() {
      this.$emit('close')
    },
    confirm: function() {
      this.loading = true
      const projectId = this.$route.params.id || ''
      setEnvAsDeployedApi(projectId).then((res) => {
        this.$message.success('操作成功')
        this.$emit('call', 'refreshDeploy')
        this.close()
      }).catch(() => {
        this.close()
        this.loading = false
      })
    }
  }
}
</script>
