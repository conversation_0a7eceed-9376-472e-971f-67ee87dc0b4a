<template>
  <div class="modal-batch-view">
    <el-alert v-if="showDeleteWarning" :closable="false" type="warning">
      <div slot="title">
        <p>删除后将无法恢复，请确认</p >
      </div>
    </el-alert>
    <div v-if="slotShow" class="modal-action-select" >
      <slot>已选择 {{ data.length }} 项，{{ data.length - availableData.length }} 项不可操作（置灰）</slot>
    </div>
    <overflow-tooltip>
      <ul class="modal-batch-ul">
        <li
          v-for="(item, index) in data"
          :key="item[postKey]"
          class="overflow-tooltip"
        >
          <span v-if="!bgColor" :class="{ 'disabled': filterData(item) }">{{
            item[viewKey]
          }}</span>
          <span v-else :class="{ 'disabled': filterData(item) }">
            <i :style="{ backgroundColor: item.color, color: colors[index] }">
              {{ item[viewKey] }}
            </i>
          </span>
        </li>
      </ul>
    </overflow-tooltip>
  </div>
</template>
<style lang="less">
.modal-batch-view {
  margin-bottom: 24px;
  .modal-action-select {
    margin-bottom: 8px;
    font-size: 14px;
    color: #252525;
  }
  .modal-batch-ul {
    display: flex;
    flex-wrap: wrap;
    padding: 12px 0 0;
    background-color: #f5f7fa;
    margin-bottom: 20px;
    max-height: 200px;
    overflow-y: auto;
    & > li {
      display: inline-block;
      width: 50%;
      padding: 0 12px;
      margin-bottom: 12px;
      & > span {
        font-size: 14px;
        color: #252525;
        display: block;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        &:before {
          content: "";
          display: inline-block;
          border-radius: 2px;
          width: 4px;
          height: 4px;
          margin-right: 8px;
          background: #c9cacd;
          position: relative;
          top: -2px;
        }
        i {
          display: inline-block;
          padding: 2px 6px;
          border-radius: 3px;
          font-style: normal;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 200px;
          vertical-align: -8px;
        }
      }
      & > .disabled {
        color: #c9cacd;
      }
    }
  }
}
</style>
<script>
import overflowTooltip from '../overflow-tooltip/overflow-tooltip'
export default {
  components: {
    overflowTooltip
  },
  props: {
    // 传入的原数据
    data: {
      type: Array,
      default: () => []
    },
    // 过滤的数据
    availableData: {
      type: Array,
      default: () => []
    },
    // 展示key
    viewKey: {
      type: String,
      default: 'name'
    },
    // 发送识别key
    postKey: {
      type: String,
      default: 'id'
    },
    // 是否显示提示
    slotShow: {
      type: Boolean,
      default: true
    },
    // 背景色
    bgColor: {
      type: Boolean,
      default: false
    },
    // 字体颜色
    colors: {
      type: Array,
      default: () => []
    },
    // 是否显示删除提示框(不需要删除框无需传输)
    showDeleteWarning: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {}
  },
  methods: {
    filterData(item) {
      let sign = true
      this.availableData.some((val, index) => {
        if (item[this.postKey] === val[this.postKey]) {
          sign = false
          return
        }
      })
      return sign
    }
  }
}
</script>
