<template>
  <el-row :gutter="20" class="detail-tabs-content">
    <el-col :span="12">
      <detail-card title="基本信息">
        <el-form slot="content" label-position="left" label-width="110px">
          <el-form-item label="名称：">{{ data.name || '-' }}</el-form-item>
          <el-form-item label="类型：">{{ contentTypeMapping[data.contentType] || '-' }}</el-form-item>
          <el-form-item v-if="data.contentType == '2'" label="场景分配：">
            {{ topoTypeMapping[data.topologyAllocation] || '-' }}
          </el-form-item>
          <el-form-item v-if="data.sourceSceneName" label="场景：">{{ data.sourceSceneName || '-' }}</el-form-item>
          <el-form-item label="分类：">{{ categoryName || '-' }}</el-form-item>
          <el-form-item label="难度：">{{ complexityObj[data.contentLevel] }}</el-form-item>
          <el-form-item label="课时：">{{ data.contentPeriod || '-' }}</el-form-item>
          <el-form-item label="包含知识点：">
            <el-tooltip
              v-for="(item, index) in knowledgeLabel"
              :key="index" :content="item" effect="dark" placement="top-start">
              <el-tag size="small" class="mr-5 mb-5">{{ item }}</el-tag>
            </el-tooltip>
          </el-form-item>
        </el-form>
      </detail-card>
    </el-col>
    <el-col :span="12">
      <detail-card title="附件">
        <div v-if="packageList.length" slot="content">
          <FileList :list="packageList" :deletable="false" @download="downloadPackage"/>
        </div>
        <el-empty v-else slot="content" :image="emptyImg" :image-size="178" class="empty-data">
          <div slot="description">暂无数据</div>
        </el-empty>
      </detail-card>
    </el-col>
  </el-row>
</template>

<script>
import detailCard from '@/packages/detail-view/detail-card.vue'
import FileList from '@/components/FileList/index.vue'
import { pjtCourseCategoryList, searchPointApi } from '@/api/teacher/index.js'
import { contentdetail } from '@/api/teacher/index.js'
export default {
  components: {
    detailCard,
    FileList
  },
  props: {
    data: {
      type: Object
    }
  },
  data() {
    return {
      emptyImg: require('@/packages/table-view/nodata.png'),
      packageList: [],
      complexityObj: {
        1: '初级',
        2: '中级',
        3: '高级'
      },
      contentTypeMapping: {
        1: '理论',
        2: '仿真'
      },
      topoTypeMapping: {
        0: '独享',
        1: '共享'
      },
      contentCategoryList: [],
      categoryName: '',
      knowledgeLabel: []
    }
  },
  created() {
    this.getCategoryList()
    this.getKnowledgeList()
    this.getPackage()
  },
  methods: {
    getCategoryList() {
      pjtCourseCategoryList({ id: '' }).then(res => {
        if (res.code === 0 || res.code === 200) {
          this.contentCategoryList = res.data.filter(item => item.id == this.data.contentCategoryId)
          if (this.contentCategoryList && this.contentCategoryList.length > 0) {
            this.categoryName = this.contentCategoryList[0].name
          }
        }
      })
    },
    getKnowledgeList() {
      this.knowledgeLabel = []
      searchPointApi({}).then(res => {
        if (res.code === 0 || res.code === 200) {
          const contentKnowledgeIdList = this.data.contentKnowledgeIdList.split(',')
          this.$nextTick(() => {
            res.data.map(item => {
              contentKnowledgeIdList.map(each => {
                if (item.knowledgeCode == each) {
                  this.knowledgeLabel.push(item.knowledgeName)
                }
              })
            })
          })
        }
      })
    },
    // 获取附件
    getPackage() {
      contentdetail({ contentId: this.data.id, format: 'package' }).then(res => {
        if (res.code == 0 && res.data) {
          this.packageList = res.data || []
          this.packageList.forEach((item) => {
            item.name = item.fileName
            item.url = window.location.origin + item.attachmentUrl
          })
        }
      })
    },
    // 下载附件
    async downloadPackage(item) {
      const fileUrl = item.url
      const response = await fetch(fileUrl, {
        method: 'get',
        responseType: 'blob'
      })
      const blob = await response.blob()
      const a = document.createElement('a')
      const URL = window.URL || window.webkitURL
      const herf = URL.createObjectURL(blob)
      a.href = herf
      a.download = item.fileName
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(herf)
    }
  }
}
</script>

<style scoped lang="less">
::v-deep .detail-card {
  .detail-card-body {
    padding: 15px;
  }
}
::v-deep .empty-data {
  padding: 20px 0;
  .el-empty__description {
    margin-top: 0;
    font-size: 14px;
    color: var(--neutral-600);
  }
}
</style>
