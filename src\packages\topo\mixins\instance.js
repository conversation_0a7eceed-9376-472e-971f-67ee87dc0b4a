import instanceTable from '../instance/table/index.vue'
import actionMenu from '../instance/action/index.vue'
import tInstanceDetail from '../instance/detail/index.vue'

export default {
  components: {
    instanceTable,
    actionMenu,
    tInstanceDetail
  },
  data() {
    return {
      selectItem: [], // 选中的云主机
      insDetailId: null,
      insDetailShow: false,
      defaultSelectedArr: []
    }
  },
  watch: {
    'selectItem': function(val) {
      if (!val.length) {
        this.closeInsDetail()
      }
    }
  },
  methods: {
    // 列表刷新动作
    'refresh': function() {
      this.closeInsDetail()
    },
    // 详情请求错误动作
    'detailError': function(error) {
      console.log(error)
      if (error.response.status === 404) {
        const id = this.$route.params.id
        this.$bus.$emit('SHOW_NOTICE', {
          type: 'error',
          message: '资源' + id + '不存在'
        })
      }
      this.closeInsDetail()
    },
    'showInsDetail': function(row, view) {
      this.insDetailId = row.id
      this.insDetailShow = true
      if (view) {
        this.$nextTick(() => {
          this.$refs['insDetail'].$refs['detail'].tabsActive = view
        })
      }
    },
    'closeInsDetail': function(type) {
      this.insDetailId = null
      this.insDetailShow = false
      if (this.$refs.hasOwnProperty('table') && this.$refs['table']) {
        this.$refs['table'].setHighlightRow(null)
      }
      this.defaultSelectedArr = []
    },
    // 返回已选
    'tabelSelect': function(data) {
      this.selectItem = data
    },
    // 返回单选
    'tabelCurrent': function(row) {
      this.selectItem = [row]
    },
    // action menu 事件
    'actionHandler': function(type, data) {
      switch (type) {
        case 'refresh':
          this.$refs['table'].getList()
      }
    }
  }
}
