// 题型map
const questionTypeArr = [
  { label: '单选题', value: '1' },
  { label: '多选题', value: '2' },
  { label: '判断题', value: '3' },
  { label: 'CTF题', value: '4' },
  { label: 'AWD题', value: '5' },
  { label: '其他', value: '6' },
  { label: '填空题', value: '7' },
  { label: '漏洞题', value: '9' }
]


export default {
  name: 'questionBank',
  questionTypeArr: questionTypeArr,
  // 将题型map数组转换为对象
  questionTypObj: questionTypeArr.reduce((acc, prev) => {
    acc[prev.value] = prev
    return acc
  }, {})
}
