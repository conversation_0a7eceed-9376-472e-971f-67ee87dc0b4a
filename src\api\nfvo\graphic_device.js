import request from '@/packages/request'
const _thisApi = window.NFVO_CONFIG.nfvo_api
const headers = { 'Content-Type': 'application/json', 'x-access-module': 'ADMIN' }

export function getListApi(params) {
  return request({
    url: _thisApi + '/networkelement',
    method: 'get',
    params,
    headers
  })
}

export function getItem(id) {
  return request({
    url: _thisApi + '/networkelement/' + id,
    method: 'get',
    headers
  })
}

export function addDevice(data) {
  return request({
    url: _thisApi + '/networkelement',
    method: 'post',
    data,
    headers
  })
}

export function editDevice(id, data) {
  return request({
    url: _thisApi + '/networkelement/' + id,
    method: 'put',
    data,
    headers
  })
}

export function deleteDevice(id) {
  return request({
    url: _thisApi + '/networkelement/' + id,
    method: 'DELETE',
    headers
  })
}

export function getIcon(params) {
  return request({
    url: _thisApi + '/icon',
    method: 'get',
    params,
    headers
  })
}
