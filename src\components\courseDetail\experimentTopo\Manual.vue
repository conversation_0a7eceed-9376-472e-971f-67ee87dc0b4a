<template>
  <div v-loading="loading" class="grid-view">
    <div class="title">{{ contentName }}</div>
    <div class="bottom">
      <div v-if="operationManualType == 1" style="padding: 10px;">
        <myEditor
          :key="contentTimer"
          :content="operationManual"
          :only-editor="true"
          :editor-config="{ readOnly: true }"
          :is-editor-border="false"
          id-prefix="operationManual"
          width="100%"
          height="100%"
        />
      </div>
      <div v-else>
        <div v-if="previewUrl" class="button-wrap">
          <el-link :underline="false" type="primary" size="small" @click="toggleFullscreen">
            {{ isFullscreen ? '退出全屏' : '全屏预览' }}
          </el-link>
        </div>
        <iframe id="frame" :src="previewUrl" allow="fullscreen" style="width: 100%;height:100%;border: 0px;margin:0;"/>
      </div>
    </div>
  </div>
</template>
<script>
import { contentdetail, getContentById } from '@/api/teacher/index.js'
import { api as fullscreen } from 'vue-fullscreen'
import myEditor from '@/packages/editor/index.vue'

export default {
  components: {
    myEditor
  },
  props: {
    contentId: { // 课程内容id
      type: String,
      default: ''
    },
    courseName: { // 课程名称
      type: String,
      default: ''
    }
  },
  data() {
    return {
      contentTimer: `operationManual` + new Date().getTime(),
      loading: true,
      fileList: [],
      previewUrl: '',
      contentName: '',
      operationManualType: 1,
      operationManual: '',
      isFullscreen: false
    }
  },
  mounted() {
    this.getContentByIdAPI()
    this.searchCurriculum()
  },
  methods: {
    // 全屏/取消全屏切换
    async toggleFullscreen() {
      const wrapperEl = this.$el
      await fullscreen.toggle(wrapperEl, {
        callback: (val) => {
          this.isFullscreen = val
        }
      })
      this.isFullscreen = fullscreen.isFullscreen
    },
    getContentByIdAPI() {
      const params = { id: this.contentId }
      getContentById(params).then(res => {
        if (res.code == 0) {
          this.contentName = res.data.name
          this.operationManualType = res.data.operationManualType || 1
          this.operationManual = res.data.operationManual || ''
          this.contentTimer = `operationManual` + new Date().getTime()
        }
      })
    },
    searchCurriculum() {
      contentdetail({ contentId: this.contentId, format: 'mannua' })
        .then(res => {
          if (res.code == 0) {
            this.loading = false
            this.fileList = res.data
            this.fileList.map(item => {
              item.name = item.attachmentUrl
              item.url = window.location.origin + item.attachmentUrl
            })
            // 默认显示第一个附件
            const file = this.fileList[0]
            if (file) {
              this.handlePreview(file)
            }
          }
        }).catch(() => {
          this.loading = false
        })
    },
    handlePreview(file) {
      this.previewUrl = this.viewFileUrl + encodeURIComponent(btoa(file.url)) // kkfile 预览
    }
  }
}
</script>
<style lang="scss" scoped>
.grid-view {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  .title {
    height: 54px;
    line-height: 54px;
    padding-left: 30px;
    border-bottom: 1px solid #dcdee2;
    font-size: 18px;
    font-weight: 600;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .bottom {
    flex: 1;
    min-height: 0;
    >div {
      overflow: auto;
      position: relative;
      width: 100%;
      height: 100%;
    }
    .button-wrap {
      position: absolute;
      top: 10px;
      right: 30px;
      .el-link {
        color: var(--color-600);
      }
    }
  }
}
</style>
