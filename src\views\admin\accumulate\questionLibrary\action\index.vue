<template>
  <div class="buttons-wrap">
    <el-button type="primary" icon="el-icon-plus" @click="clickDrop('modalCreate')">{{ titleMapping['modalCreate'] }}</el-button>
    <el-dropdown trigger="click" placement="bottom-start" @command="handleCommand">
      <el-button type="primary">
        操作<i class="el-icon-arrow-down el-icon--right" />
      </el-button>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item :disabled="singleDisabled" command="modalEdit">编辑</el-dropdown-item>
        <el-dropdown-item :disabled="multipleDisabled" command="modalDelete">删除</el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
    <!-- 中部弹窗 start-->
    <el-dialog
      :title="titleMapping[modalName]"
      :visible.sync="modalShow"
      :width="modalWidth"
      destroy-on-close
      @close="modalClose"
    >
      <transition name="el-fade-in-linear">
        <component
          :is="modalName"
          :name="modalName"
          :data="selectItem"
          @close="modalClose"
          @call="confirmCall"
        />
      </transition>
    </el-dialog>
    <!-- 中部弹窗 end-->
  </div>
</template>
<script>
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
import modalCreate from './modal-edit.vue'
import modalEdit from './modal-edit.vue'
import modalDelete from './modal-delete'
export default {
  components: {
    modalDelete,
    modalCreate,
    modalEdit
  },
  mixins: [mixinsActionMenu],
  data() {
    return {
      // 弹窗title映射
      titleMapping: {
        'modalCreate': '创建题库',
        'modalEdit': '编辑题库',
        'modalDelete': '删除题库'
      }
    }
  },
  provide() {
    return {
      'questionLibraryAction': this
    }
  },
  methods: {
    confirmCall: function(type, data) {
      if (type === 'close') {
        this.modalClose()
      } else if (type === 'refresh') {
        this.$emit('call', type)
      }
    },
    'handleCommand': function(name) {
      // 只选一项且为删除操作时，提示错误信息
      if (name === 'modalDelete' && this.selectItem.length === 1 && this.selectItem[0].isCitedQuestion === 1) {
        this.$message.error('存在被引用的题目，不允许删除！')
        return
      } else {
        this.clickDrop(name)
      }
    }
  }
}
</script>
