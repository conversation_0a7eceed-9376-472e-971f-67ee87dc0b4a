<template>
  <div class="buttons-wrap">
    <el-button type="primary" icon="el-icon-plus" @click="clickDrop('addStudent')">添加</el-button>
    <el-dropdown trigger="click" placement="bottom-start" @command="clickDrop">
      <el-button type="primary">
        操作 <i class="el-icon-arrow-down el-icon--right"/>
      </el-button>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item :disabled="multipleDisabled" command="deleteStudent">删除</el-dropdown-item>
        <el-dropdown-item v-show="selectItem.length >= 1" command="exportStudent">导出</el-dropdown-item>
        <el-dropdown-item v-show="selectItem.length == 0" command="allStudent">导出</el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
    <!-- 中部弹窗 start-->
    <el-dialog
      :title="titleMapping[modalName]"
      :visible.sync="modalShow"
      :width="modalWidth"
      @close="modalClose"
    >
      <transition name="el-fade-in-linear">
        <component
          :is="modalName"
          :name="modalName"
          :data="selectItem"
          :upload-data="uploadData"
          @close="modalClose"
          @call="confirmCall"
          @exportOut="exportDetails"
        />
      </transition>
    </el-dialog>
    <!-- 中部弹窗 end-->
    <!-- 侧拉弹窗 start -->
    <el-drawer
      :title="titleMapping[drawerName]"
      :visible.sync="drawerShow"
      :size="drawerWidth"
      @close="drawerClose"
    >
      <transition name="el-fade-in-linear">
        <component
          :is="drawerName"
          :name="drawerName"
          :data="selectItem"
          @close="drawerClose"
          @call="confirmCall"
          @exportOut="exportDetails"
        />
      </transition>
    </el-drawer>
    <!-- 侧拉弹窗 end -->
  </div>
</template>
<script>
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
import exportStudent from './modal_export.vue'
import deleteStudent from './modal_delete'
import addStudent from './modal_add.vue'
import allStudent from '@/components/modal-export-all.vue'
import { exportStudentNewAPI, deleteStudentRole } from '@/api/admin/training/student'
import { downloadExcelWithResDataWithName } from '@/utils'
import { formatDate } from '@/utils/index'
export default {
  components: {
    deleteStudent,
    addStudent,
    exportStudent,
    allStudent
  },
  mixins: [mixinsActionMenu],
  props: {
    uploadData: {
      type: Object
    },
    timeParams: {
      type: Object
    },
    paramsData: {
      type: Object
    },
    transmitTime: {
      type: Object
    }

  },
  data() {
    return {
      // 弹窗title映射
      titleMapping: {
        'exportStudent': '导出学员',
        'addStudent': '添加学员',
        'deleteStudent': '删除学员',
        'allStudent': '导出'
      },
      drawerAction: ['addStudent'], // 需要侧拉打开的操作
      // 弹窗title映射
      dialogLoading: false,
      confirmDisabled: false
    }
  },
  mounted() {
    this.token = JSON.parse(localStorage.getItem('Admin-Token')).data
  },
  methods: {
    confirmCall: function(type, data) {
      if (type === 'close') {
        this.modalClose()
      } else if (type === 'refresh') {
        this.$emit('call', type)
      }
    },
    'clickDrop': function(name) {
      if (this.drawerAction && this.drawerAction.includes(name)) {
        this.drawerName = name
      } else if (name == 'exportDetails') {
        this.exportDetails()
      } else if (name == 'deleteStudent') {
        if (this.selectItem.some(item => item.inSchedule)) {
          this.modalName = name
        } else {
          // 没有未完成任务的助教直接删除
          const idArr = this.selectItem.map(item => {
            return { userId: item.userId, realname: item.realname }
          })
          idArr.map((item, index) => {
            this.saveJoinCourse({ userId: item.userId, realname: item.realname }).then((res) => {
              this.$message.success(res.data)
              this.$emit('call', 'refresh')
            })
          })
        }
      } else {
        this.modalName = name
      }
    },
    saveJoinCourse(postData) {
      return new Promise((resolve, reject) => {
        deleteStudentRole(postData).then(res => {
          resolve(res)
        })
      })
    },
    exportDetails() {
      this.loading = true
      const query = {
        ...this.timeParams,
        pjtSysUsers: []
      }
      const exportArr = this.selectItem.map(item => {
        return { userId: item.userId }
      })
      query.pjtSysUsers = exportArr
      delete query.pageNum
      delete query.pageSize
      if (this.selectItem.length == 0) {
        query.pjtSysUsers = []
      }
      console.log('exportArr', exportArr)
      exportStudentNewAPI(query).then((res) => {
        this.loading = false
        downloadExcelWithResDataWithName(res, `学员列表_<${formatDate(new Date(), 'yy-mm-dd')}>.xls`)
        this.$message.success('导出成功')
      }).catch(() => {
        this.loading = false
        this.$message.warning('导出失败')
      })
    }
  }
}
</script>
