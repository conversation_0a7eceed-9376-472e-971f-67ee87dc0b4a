import request from '@/packages/request'
const _thisApi = window.NFVO_CONFIG.nfvo_api
const headers = { 'Content-Type': 'application/json', 'x-access-module': 'ADMIN' }

export function getListApi(params) {
  return request({
    url: _thisApi + '/image/vm',
    method: 'get',
    params,
    headers
  })
}

export function createImage(data) {
  return request({
    url: _thisApi + '/image/vm',
    method: 'post',
    data,
    headers
  })
}

export function editImage(id, data) {
  return request({
    url: _thisApi + '/image/vm/' + id,
    method: 'put',
    data,
    headers
  })
}

export function deleteImage(id) {
  return request({
    url: _thisApi + '/image/vm/' + id,
    method: 'DELETE',
    headers
  })
}

export function upload(id, oversize, newname, newsize, newmd5, newdate, newpath) {
  return _thisApi + '/image/vm/' + id + '/upload?bytes_written=' + (oversize || 0) + (newname ? '&file_name=' + newname : '') + (newsize ? '&file_size=' + newsize : '') + (newmd5 ? '&md5=' + newmd5 : '') + (newdate ? '&file_date=' + newdate : '') + (newpath ? '&file_path=' + newpath : '')
}
