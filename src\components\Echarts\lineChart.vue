<template>
  <div id="line-chart" ref="lineChart" />
</template>
<script>
import * as echarts from 'echarts'
export default {
  props: {
    data: {
      type: Array,
      default: () => []
    },
    xAxis: {
      type: Array,
      default: () => []
    },
    yAxisName: {
      type: String,
      default: ''
    }
  },
  data() {
    return {}
  },
  watch: {
    data: {
      handler(newVal) {
        if (newVal) {
          this.initLineChart()
        }
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {
    this.initLineChart()
  },
  methods: {
    initLineChart() {
      let myChart = null
      if (!this.$refs.lineChart) {
        return
      }
      myChart = echarts.init(this.$refs.lineChart)
      const legendArr = this.data.map(item => {
        return item.className
      })
      const Top5Color = ['#40D0BF', '#574C95', '#11C0F2', '#FADC19', '#F76560']
      const option = {
        tooltip: {
          show: true,
          trigger: 'axis',
          position: 'right',
          confine: true,
          axisPointer: {
            type: 'shadow'
          },
          enterable: true,
          extraCssText: 'max-width:340px; max-height:83%; overflow:auto'
        },
        legend: {
          x: '87%',
          y: 'center',
          orient: 'vertical',
          data: legendArr,
          itemWidth: 15,
          itemHeight: 10,
          itemGap: 12,
          borderRadius: 4,
          formatter: function(name) {
            if (name.length > 5) {
              return name.substring(0, 5) + '...'
            }
            return name
          },
          tooltip: { // 精华2
            show: true
          },
          textStyle: {
            color: '#303133',
            fontSize: 12,
            fontWeight: 400
          }
        },
        grid: {
          left: '1%',
          right: '15%',
          top: '10%',
          bottom: '0%',
          containLabel: true
        },
        toolbox: {
          show: false,
          feature: {
            saveAsImage: {}
          }
        },
        xAxis: {
          type: 'category',
          boundaryGap: true,
          name: '月份',
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            interval: 0, // 强制显示所有标签
            rotate: 45, // 可选的，如果标签过长可以通过旋转来显示
            show: true,
            textStyle: {
              color: '#303133',
              fontSize: 14
            }
          },
          data: this.xAxis
        },
        yAxis: {
          type: 'value',
          name: this.yAxisName,
          nameTextStyle: {
            color: '#303133',
            fontSize: 14,
            align: 'left'
            // padding: [0, 0, 3, 30] // 四个数字分别为上右下左与原位置距离
          },
          nameGap: 8, // 表现为上下位置
          axisLine: {
            show: true,
            lineStyle: {
              color: '#eeeeee'
            }
          },
          axisTick: {
            show: false
          },
          axisLabel: {
            color: '#303133',
            fontSize: 14
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#eeeeee'
            }
          }
        },
        series: this.data.map((item, index) => {
          const formattedData = item.avgScoreList.map(function(value) {
            return parseFloat(value).toFixed(1)
          })
          return {
            name: item.className,
            type: 'line',
            color: Top5Color[index],
            data: formattedData
          }
        })
      }
      myChart.setOption(option)
      window.onresize = function() {
        myChart.resize()
      }
    }
  }
}
</script>



