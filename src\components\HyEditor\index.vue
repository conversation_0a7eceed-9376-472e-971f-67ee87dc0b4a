<template>
  <div style="border: 1px solid #ccc;">
    <component
      :is="hyToolbar"
      :mode="mode"
      :editor="editor"
      :default-config="toolbarConfig"
      style="border-bottom: 1px solid #ccc"
    />
    <component
      v-loading="loading"
      :is="hyEditor"
      v-model="wangEditorContent"
      :style="{
        height: height,
        overflowY: 'hidden'
      }"
      :default-config="propsEditorConfig"
      :mode="mode"
      element-loading-text="文件上传中..."
      @onCreated="onCreated"
      @onChange="onChange"
    />
  </div>
</template>

<script>
import { RichTextUpload } from '@/api/admin/file.js'
export default {
  // components: { Editor, Toolbar },
  props: {
    height: {
      type: Number | String,
      default: '300px'
    },
    content: {
      type: String,
      default: ''
    },
    editorConfig: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      timer: null,
      loading: false,
      editor: null,
      html: '<p>hello</p>',
      toolbarConfig: {
        excludeKeys: ['fullScreen', 'emotion', 'insertTable', 'codeBlock', 'group-video', 'insertImage']
      },
      propsEditorConfig: {},
      defaultEditorConfig: {
        placeholder: '请输入内容...',
        readOnly: false,
        autoFocus: true,
        scroll: true,
        MENU_CONF: {}
      },
      hyEditor: null, // 工具栏
      hyToolbar: null, // 编辑组件
      mode: 'default' // or 'simple'
    }
  },
  computed: {
    wangEditorContent: {
      get() {
        return this.content
      },
      set(val) {
        return this.content
      }
    }
  },
  mounted() {
    // 图片上传设置
    this.defaultEditorConfig.MENU_CONF.uploadImage = {
      customUpload: this.customImageUpload
    }
    // 编辑区配置（默认配置优先,用户配置覆盖）
    this.propsEditorConfig = {
      ...this.defaultEditorConfig,
      ...this.editorConfig
    }
    this.init()
  },
  beforeDestroy() {
    const editor = this.editor
    if (editor == null) return
    editor.destroy() // 组件销毁时，及时销毁编辑器
  },
  methods: {
    onCreated(editor) {
      this.editor = Object.seal(editor) // 一定要用 Object.seal() ，否则会报错
    },
    // 内容改变触发
    onChange(editor) {
      const html = editor.getHtml()
      this.$emit('update:content', html)
      this.$emit('contentChange', html)
    },
    init() {
      const editor = require('@wangeditor/editor-for-vue')
      this.hyEditor = editor.Editor
      this.hyToolbar = editor.Toolbar
    },
    // 统一设置loading
    loadingFun(fun) {
      return async(file, insertFn) => {
        // 开启菊花图
        this.loading = true
        this.editor.disable()
        await fun(file, insertFn)
        // 关闭菊花图
        this.loading = false
        this.editor.enable()
      }
    },
    async customImageUpload(file, insertFn) {
      // 自定义上传
      this.loading = true
      const limit = file.size / 1024 / 1024
      var reg = /(.*)\.(jpg|jpeg|png)$/gi
      if (!reg.test(file.name)) {
        this.$message({
          offset: 100,
          type: 'warning',
          message: '仅支持JPG/JPEG/PNG'
        })
        this.loading = false
        return
      }
      // 大于2M
      if (limit > 2) {
        this.$message({
          offset: 100,
          type: 'warning',
          message: '您上传的文件超过了2M了'
        })
        this.loading = false
        return
      }
      const formData = new FormData()
      formData.append('file', file)
      RichTextUpload(formData).then((res) => {
        if (res.code == 0) {
          insertFn(res.data.url)
          this.loading = false
        } else {
          this.$message({
            offset: 100,
            type: 'error',
            message: '图片上传失败'
          })
          this.loading = false
        }
      }).finally(() => {
        this.loading = false
      })
    }
  }
}
</script>
<style src="@wangeditor/editor/dist/css/style.css"></style>
