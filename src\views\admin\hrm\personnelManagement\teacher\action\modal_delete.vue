<template>
  <div v-loading="loading" class="dialog-wrap">
    <el-alert :closable="false" type="warning">
      <div slot="title">如果选中的教师课程表中有未完成的教学任务，不允许删除</div>
    </el-alert>
    <batch-template
      :data="data"
      :available-data="data"
      view-key="realname"
    />
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button :disabled="!data.length" type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>

<script>
import batchTemplate from '@/packages/batch-delete/modal-bat-template.vue'
import modalMixins from '@/packages/mixins/modal_form'
import { deleteTeacherManager } from '@/api/admin/training/student'

export default {
  components: {
    batchTemplate
  },
  mixins: [modalMixins],
  props: {
    data: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      loading: false
    }
  },
  methods: {
    close() {
      this.$emit('close')
    },
    saveJoinCourse(postData) {
      return new Promise((resolve, reject) => {
        deleteTeacherManager(postData).then(res => {
          resolve(res)
        })
      })
    },
    confirm: function() {
      this.loading = true
      const idArr = this.data.map(item => {
        return { userId: item.userId, realname: item.realname }
      })
      console.log('idArr', idArr)
      idArr.map((item, index) => {
        this.saveJoinCourse({ userId: item.userId, realname: item.realname })
          .then((res) => {
            this.$message.success(res.data.data)
          })
      })
      setTimeout(() => {
        this.$emit('call', 'refresh')
        this.close()
        this.loading = false
      }, 500)
    }
  }
}
</script>
