<template>
  <div class="buttons-wrap">
    <div :class="{ 'folded': fold }" class="category-fold-wrap" @click="handleFold">
      <div v-if="fold">展开<i class="el-icon-d-arrow-left rotate-90deg" /></div>
      <div v-else>折叠<i class="el-icon-d-arrow-left rotate90deg" /></div>
    </div>
    <el-button type="primary" icon="el-icon-plus" @click="$router.push({ name: 'examCreate' })">创建试卷</el-button>
    <el-dropdown trigger="click" placement="bottom-start" @command="clickDrop">
      <el-button type="primary">
        操作<i class="el-icon-arrow-down el-icon--right" />
      </el-button>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item :disabled="singleDisabled || selectItem[0].citedNum != 0" command="edit">编辑</el-dropdown-item>
        <el-dropdown-item :disabled="multipleDisabled" command="modalDelete">删除</el-dropdown-item>
        <el-dropdown-item :disabled="singleDisabled" command="clone">克隆</el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
    <!-- 中部弹窗 start-->
    <el-dialog
      :title="titleMapping[modalName]"
      :visible.sync="modalShow"
      :width="modalWidth"
      :destroy-on-close="true"
      @close="modalClose"
    >
      <transition name="el-fade-in-linear">
        <component
          :is="modalName"
          :name="modalName"
          :data="selectItem"
          @close="modalClose"
          @call="confirmCall"
        />
      </transition>
    </el-dialog>
    <!-- 中部弹窗 end-->
  </div>
</template>
<script>
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
import modalDelete from './modal-delete'
import clone from './modal-clone.vue'
export default {
  components: {
    modalDelete,
    clone
  },
  mixins: [mixinsActionMenu],
  data() {
    return {
      fold: false,
      // 弹窗title映射
      titleMapping: {
        'modalDelete': '删除',
        'clone': '克隆'
      }
    }
  },
  methods: {
    handleFold() {
      this.fold = !this.fold
      this.$emit('call', 'fold', this.fold)
    },
    confirmCall: function(type, data) {
      if (type === 'close') {
        this.modalClose()
      } else if (type === 'refresh') {
        this.$emit('call', type)
      }
    },
    'clickDrop': function(name) {
      if (name === 'edit') {
        this.$router.push({ name: 'examCreate', query: { id: this.selectItem[0].id }})
        return
      }
      if (this.drawerAction && this.drawerAction.includes(name)) {
        this.drawerName = name
      } else {
        this.modalName = name
      }
    }
  }
}
</script>
