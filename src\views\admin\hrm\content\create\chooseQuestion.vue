<template>
  <div v-loading="loading" class="drawer-wrap">
    <div class="resource-table">
      <!-- 分类 -->
      <div class="_paper_top">
        <div class="_paper_top_title" style="display: flex;">
          <div style="color: #999; margin-top: 3px; font-size: 13px;">来源：</div>
          <div>
            <el-radio-group v-model="categoryRadio" @change="changeRadio">
              <el-radio label="1">题库</el-radio>
              <el-radio label="2">试卷</el-radio>
            </el-radio-group>
          </div>
        </div>
        <div class="category-container">
          <div class="title">分类：</div>
          <div class="tag-list">
            <el-tag
              v-for="(item,index) in categoryList" :key="index"
              :disable-transitions="true"
              closable
              style="margin:3px 5px 5px 0"
              @close="closeClassify(index, item)">
              {{ categoryRadio == '1' ? item.categoryName : item.examName }}
            </el-tag>
            <el-button v-if="categoryRadio == '1'" type="ghost" style="margin-top: 3px !important;" @click="drawerName = 'selectClassify', drawerShow = true">选择分类</el-button>
            <el-button v-if="categoryRadio == '2'" type="ghost" style="margin-top: 3px !important;" @click="drawerName = 'seleceExaminationPaper', drawerShow = true">选择试卷</el-button>
          </div>
        </div>
      </div>
      <category v-if="categoryRadio != '2'" :content-type="data.contentType" @category-query="handleCategoryQuery"/>
      <!-- 操作区 -->
      <div v-if="categoryRadio != '2'" class="operation-wrap">
        <div class="operation-left">
          <slot name="action" />
          <el-button type="primary" icon="el-icon-refresh" class="mr-5" @click="refresh"/>
        </div>
        <div class="operation-right">
          <el-badge :value="searchBtnShowNum">
            <el-button icon="el-icon-search" @click="openSearch">搜索</el-button>
          </el-badge>
        </div>
      </div>
      <!-- 搜索区 -->
      <t-search-box
        v-if="categoryRadio != '2'"
        v-show="searchView"
        :search-key-list="searchKeyListView"
        style="margin-bottom:0px;"
        default-placeholder="默认搜索题干"
        @search="searchMultiple"
      />
      <div v-if="categoryRadio != '2'" class="tools-wrap">
        <div class="buttons" style="margin-top: -2px !important;">
          <el-button type="default" @click="onCheckAll">全选</el-button>
          <el-button type="default" @click="onUnCheckAll" >取消全选</el-button>
        </div>
        <div class="_paper_search">
          <div class="line"/>
          <div class="_paper_search_1">
            总题数
            <span style="font-weight: bold; color: var(--color-600); font-size: 18px; margin-right: 10px;">{{
              questionTotal
            }}</span>
          </div>
          <div class="_paper_search_1">
            已选题数
            <span style="font-weight: bold; color: var(--color-600); font-size: 18px;">{{
              checkQuestionCodes.length
            }}</span>
          </div>
        </div>
      </div>
      <!-- 题目列表 -->
      <div v-loading="tableLoading" v-if="categoryRadio != '2'" class="question-content">
        <div v-if="questionList.length && !tableLoading" class="_question_list">
          <div
            v-for="(q, index) in questionList"
            :class="`_question_item ${
              checkQuestionCodes.includes(q.questionCode) &&
              '_question_item_check'
            }`"
            :key="index"
            @click="handleCheckQuestion(q)"
          >
            <div style="display: flex;justify-content: space-between;align-items: flex-start;">
              <el-checkbox :value="checkQuestionCodes.includes(q.questionCode)" style="position: absolute; left: 12px;" />
              <div class="_question_item_content">
                {{ index + 1 }}.&nbsp;
                <div v-html="q.questionType != 10 ? q.content : q.questionName"/>
              </div>
              <div class="_question_item_type">
                <el-tag size="small">{{ questionTypeSimulationObj[q.questionType] }}</el-tag>
              </div>
            </div>
            <div v-if="q.questionType == 1" class="_question_option">
              <el-radio-group
                v-for="(op, i) in JSON.parse(q.questionOptions)"
                :key="i"
                :value="q.questionAnswer"
                disabled
              >
                <el-radio v-overflow-tooltip="{ content: op }" :label="optionLabel[i]">{{ optionLabel[i] }}. {{ op }}</el-radio>
              </el-radio-group>
            </div>
            <div v-if="q.questionType == '2'">
              <el-checkbox-group :value="q.questionAnswer.split('')" disabled>
                <div class="_question_option">
                  <el-checkbox
                    v-overflow-tooltip="{ content: op }"
                    v-for="(op, i) in JSON.parse(q.questionOptions)"
                    :key="i"
                    :label="optionLabel[i]"
                  >{{ optionLabel[i] }}. {{ op }}</el-checkbox
                  >
                </div>
              </el-checkbox-group>
            </div>
            <div v-if="q.questionType == 3" class="_question_option">
              <el-radio-group :value="q.questionAnswer" disabled>
                <el-radio label="A">正确</el-radio>
                <el-radio label="B">错误</el-radio>
              </el-radio-group>
            </div>
            <div v-else class="_question_option">
              <span>{{ q.questionAnswer }}</span>
            </div>
          </div>
        </div>
        <el-empty
          v-if="!questionList.length && !tableLoading"
          :image="img"
          :image-size="110"
          style="margin: auto"
          description="暂无数据"
        />
      </div>
      <!-- 侧拉 -->
      <el-drawer
        :title="titleMapping[drawerName]"
        :visible.sync="drawerShow"
        :size="drawerWidth"
        append-to-body
        @close="drawerClose"
      >
        <transition v-if="drawerShow" name="el-fade-in-linear">
          <component
            :is="drawerName"
            :name="drawerName"
            @close="drawerClose"
            @call="drawerConfirmCall"
          />
        </transition>
      </el-drawer>
    </div>
    <div class="drawer-footer">
      <el-button type="primary" @click="confirm">确定</el-button>
      <el-button type="text" @click="close">取消</el-button>
    </div>
  </div>
</template>
<script>
import category from '../practice/category.vue'
import selectClassify from '../practice/select-classify.vue'
import seleceExaminationPaper from '../practice/selece-examination-paper.vue'
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
import mixinsPageTable from '@/packages/mixins/page_table'
import tSearchBox from '@/packages/search-box/index.vue'
import validate from '@/packages/validate'
import {
  questionByManualPaperAPI,
  queryQuestionPageByExamId,
  updatePracticeAPI,
  queryPractice
} from '@/api/teacher/index.js'
import { questionTypeObj, questionTypeSimulationObj } from '../practice/constants'

export default {
  components: {
    category,
    selectClassify,
    tSearchBox,
    seleceExaminationPaper
  },
  mixins: [mixinsActionMenu, mixinsPageTable],
  inject: ['parentVm'],
  props: {
    chooseQuestionInfo: {
      type: Object,
      default: () => {}
    },
    data: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      searchKeyList: [
        { key: 'content', label: '题干', master: true },
        { key: 'questionDepotName', label: '题库' }
      ],
      questionTypeObj,
      loading: false,
      submitLoading: false,
      validate: validate,
      categoryList: [],
      categoryIds: [],
      evaluationCode: this.chooseQuestionInfo.evaluationCode,
      curriculumCode: this.chooseQuestionInfo.curriculumCode,
      id: this.chooseQuestionInfo.id,
      checkQuestionList: [],
      checkQuestionCodes: [],
      questionList: [],
      knowledgeList: [],
      optionLabel: Array.from({ length: 26 }, (_, index) => String.fromCharCode('A'.charCodeAt(0) + index)),
      knowledgeCheckList: [],
      noData: false,
      isCheckAll: false,
      select_actie: require('@/assets/select_active.png'),
      select: require('@/assets/select.png'),
      img: require('@/assets/empty_state.png'),
      questionTotal: 0,
      pageCurrent: 1,
      pageSize: 10,
      questionTypeSimulationObj,
      categoryQuery: {
        complexity: null,
        questionType: null
      },
      titleMapping: {
        'selectClassify': '分类列表',
        'seleceExaminationPaper': '试卷列表'
      },
      drawerWidth: '820px',
      drawerShow: false,
      drawerName: null,
      contentType: this.chooseQuestionInfo.contentType,
      categoryRadio: this.chooseQuestionInfo.questionSource || '1',
      examId: '', // 试卷id
      examType: null, // 试卷类型
      dynamicPaperData: {} // 动态试卷展示规则
    }
  },
  computed: {
    editMode() {
      return !!this.id
    }
  },
  watch: {
    categoryList(data) {
      this.categoryIds = data.map(v => v.id)
      this.getList(false)
    },
    checkQuestionList: {
      handler(newVal) {
        if (newVal) {
          this.checkQuestionCodes = newVal.map(v => v.questionCode)
        }
      },
      deep: true
    }
  },
  mounted() {
    this.checkQuestionList = []
    const { categoryList, checkQuestionList } = this.chooseQuestionInfo || {}
    this.categoryList = categoryList || []
    this.checkQuestionList = checkQuestionList || []
    this.queryPracticeCode()
  },
  methods: {
    changeRadio() {
      this.categoryList = []
      this.categoryIds = []
      this.examId = ''
      this.checkQuestionList = []
      this.questionTotal = 0
      this.getList(false)
    },
    onCheckAll() {
      this.questionList.forEach(q => {
        if (this.checkQuestionList.filter(item => { return item.questionCode === q.questionCode }).length === 0) {
          this.checkQuestionList.push(q)
        }
      })
    },
    onUnCheckAll() {
      this.questionList.forEach(q => {
        this.checkQuestionList = this.checkQuestionList.filter(
          (el) => el.questionCode !== q.questionCode
        )
      })
    },
    /**
     * 更改每页展示数量
     */
    handleSizeChange(val) {
      this.pageSize = val
      this.getList(false)
    },
    /**
     * 更改当前页数
     */
    handleCurrentChange(val) {
      this.page = val
      this.getList(false)
    },
    closeClassify(index, item) {
      this.checkQuestionList = this.checkQuestionList.filter(i => { return i.categoryId !== item.id })
      this.categoryList.splice(index, 1)
    },
    updatePractice() {
      this.submitLoading = true
      updatePracticeAPI({
        evaluationCode: this.evaluationCode,
        questionCodeList: this.checkQuestionCodes,
        curriculumCode: this.curriculumCode
      }).then(res => {
        if (res.code == 0) {
          this.$message.success('修改成功')
          this.close()
        }
      }).finally(() => {
        this.submitLoading = false
      })
    },
    // 侧拉选择资源回调
    drawerConfirmCall: function(type, data) {
      if (type === 'confirm_classify') {
        data.forEach(item => {
          if (this.categoryList.filter(classify => { return classify.id == item.id }).length === 0) {
            this.categoryList.push(item)
          }
        })
        this.drawerClose()
      } else if (type === 'selece_examination_paper') {
        this.categoryList = data
        this.drawerClose()
      }
    },
    drawerClose() {
      this.drawerShow = false
    },
    handleCategoryQuery(obj) {
      this.categoryQuery = { ...obj }
      if (obj.complexity == null) {
        this.categoryQuery.complexity = 0
      }
      if (obj.questionType == null) {
        this.categoryQuery.questionType = 0
      }
      this.getList(false)
    },
    evaluationCodeFn() {
      if (this.evaluationCode) {
        this.updatePractice()
      } else {
        this.handleManualBuildPaper()
      }
    },
    getList: function(showLoading = true) {
      if (showLoading) {
        this.tableLoading = true
      }
      this.handleQuestionSearch()
    },
    queryPracticeCode: function() {
      // 获取题目
      queryPractice({ contentId: this.id, isShowAnswers: true }).then(res => {
        // res.data.forEach(item => {
        //   this.handleCheckQuestion(item.questionCode)
        // })
        this.noData = true
      }).catch(() => {
        this.noData = true
      })
    },
    // 手动组卷
    handleManualBuildPaper() {
      if (this.checkQuestionList.length === 0) {
        return this.$message.error('请选择题目')
      }
    },
    // 题目查询
    handleQuestionSearch() {
      const postData = this.getPostData('page', 'limit')
      let questionTypes = []
      let bankType
      let bankTypes
      if (this.contentType == 1) {
        questionTypes = this.categoryQuery.questionType ? [this.categoryQuery.questionType] : Object.keys(questionTypeObj)
        bankType = 1
      } else if (this.contentType == 2) {
        questionTypes = this.categoryQuery.questionType ? [this.categoryQuery.questionType] : Object.keys(questionTypeSimulationObj)
        bankTypes = [1, 2, 3]
      }
      // 是否选择题库或者试卷类型
      let typeApi
      const params = {}
      if (this.categoryRadio == '2') {
        this.examId = this.categoryList[0] && this.categoryList[0].id
        this.examType = this.categoryList[0] && this.categoryList[0].examType
        params.examId = this.examId
        params.examType = this.examType
        typeApi = queryQuestionPageByExamId
      } else {
        typeApi = questionByManualPaperAPI
      }
      if (this.categoryIds.length === 0) { //  && this.chooseQuestionInfo.type == '创建'
        this.questionList = []
        this.tableTotal = 0
        this.loading = false
        this.tableLoading = false
        // 清空已选分类题目和已选题目数
        this.questionTotal = 0
        this.$set(this, 'checkQuestionCodes', [])
        return
      }
      params.pageType = 0
      params.content = postData.content
      params.questionDepotName = postData.questionDepotName
      params.complexity = this.categoryQuery.complexity ? this.categoryQuery.complexity : null
      params.knowledgeCodeList = this.knowledgeCheckList
      params.categoryIds = this.categoryIds
      params.questionTypes = questionTypes
      params.bankType = bankType
      params.bankTypes = bankTypes
      typeApi(params).then((ret) => {
        if (ret.code !== 0) {
          this.$message.error('题目查询失败.')
          return
        }
        this.questionList = ret.data.records || []
        if (this.categoryRadio == '2') {
          this.dynamicPaperData = ret.data || {}
        }
        this.questionList.forEach(item => {
          if (!item.questionCode) {
            item.questionCode = item.id
          }
        })
        this.questionTotal = ret.data.total || 0
        if (this.chooseQuestionInfo.examCode && this.numBool == 1) {
          this.numBool = 2
          // 试卷题目回显
          this.getPaperDetails()
        }
      }).finally(() => {
        this.tableLoading = false
      })
    },
    // 选择题目
    handleCheckQuestion(q) {
      if (this.checkQuestionCodes.includes(q.questionCode)) {
        this.checkQuestionList = this.checkQuestionList.filter(
          (el) => el.questionCode !== q.questionCode
        )
        return
      }
      // 选择
      this.checkQuestionList.push(q)
    },
    close() {
      this.$emit('close')
    },
    'confirm': function() {
      let data = { ...this.chooseQuestionInfo, noRefresh: '1' }
      data = {
        ...data,
        checkQuestionList: this.categoryRadio == '2' ? this.questionList : this.checkQuestionList, // 试卷类型直接在随堂练习回显
        questionSource: this.categoryRadio,
        categoryList: this.categoryList
      }
      // 动态试卷组卷规则,如果存在静态题目需清空
      if (this.categoryRadio == '2' && this.examType === 1) {
        data.dynamicPaperData = this.dynamicPaperData
        data.dynamicExamId = this.examId
        this.parentVm.checkQuestionList = []
        data.checkQuestionList = []
      }
      // 如果已存在动态试卷，选择静态试卷，需清空动态试卷
      if (data.checkQuestionList.length >= 0 && this.parentVm.dynamicPaperData.examType === 1) {
        this.parentVm.dynamicPaperData = {}
        // 兼容编辑情况
        this.parentVm.dynamicExamId = ''
        // 清除动态试卷后让题目回显
        data.questionEchoFlag = true
      }
      if (this.categoryRadio == 2) {
        data.examType = data.categoryList[0].examType == undefined ? 1 : data.categoryList[0].examType
        this.parentVm.examType = data.categoryList[0].examType == undefined ? 1 : data.categoryList[0].examType
        this.parentVm.dynamicPaperData.examType = data.categoryList[0].examType == undefined ? 1 : data.categoryList[0].examType
      } else {
        data.examType = 0
        this.parentVm.examType = 0
        this.parentVm.dynamicPaperData.examType = 0
      }
      this.$emit('call', 'chooseQuestion', data)
      this.$emit('close')
    }
  }
}
</script>
<style lang="scss" scoped>
@import '../practice/index.scss';
.category-container {
  display: flex;
  flex-wrap: wrap;
  margin-top: 10px;
}
.category-container {
  display: flex;
  margin-top: 10px;
  .title {
    width: 40px;
    color: #999;
    margin-top: 9px;
    font-size: 13px;
  }
  .tag-list {
    flex: 1;
  }
}
.resource-table {
  overflow: auto;
}
.resource-table {
  .tools-wrap {
    margin-top: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  ._paper_search {
    display: inline-flex;
    align-items: center;
    ._paper_search_1 {
      font-size: 16px;
      font-weight: 500;
      color: #333;
    }
  }
  &.card-bg {
    border-radius: 4px;
    padding: 15px;
    background-color: #FFFFFF;
  }
}
.question-content {
  flex: 1;
  overflow-y: auto;
  padding-right: 15px;
}
</style>
