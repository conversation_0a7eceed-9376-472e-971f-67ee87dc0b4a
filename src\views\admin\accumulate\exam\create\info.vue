<template>
  <el-form ref="form" :model="formData" :rules="rules" label-position="left" label-width="150px">
    <el-card class="mb-10">
      <el-form-item label="名称" prop="examName">
        <el-input v-model.trim="formData.examName" style="width: 320px;"/>
      </el-form-item>
      <el-form-item label="分类" prop="categoryId">
        <el-select v-model="formData.categoryId" style="width: 320px;">
          <el-option
            v-for="item in categoryList"
            :key="item.id"
            :label="item.categoryName"
            :value="item.id"/>
        </el-select>
      </el-form-item>
      <el-form-item label="描述" prop="examDesc">
        <el-input v-model.trim="formData.examDesc" type="textarea" style="width: 320px;"/>
      </el-form-item>
      <el-form-item label="难度" prop="difficulty">
        <el-radio
          v-for="item in moduleConf.difficultyArr"
          :key="item.value"
          v-model="formData.difficulty"
          :label="Number(item.value)"
        >{{ item.label }}</el-radio>
      </el-form-item>
      <el-form-item label="推荐时长" prop="suggestTime">
        <el-input-number v-model="formData.suggestTime" :min="1"/><span style="margin-left:10px;">分钟</span>
      </el-form-item>
      <el-form-item label="用途" prop="sceneTypeId">
        <el-select v-model="formData.sceneTypeId" style="width: 320px;">
          <el-option
            v-for="item in moduleConf.sceneTypeList"
            :key="item.id"
            :label="item.value"
            :value="item.id"/>
        </el-select>
      </el-form-item>
    </el-card>
    <el-card>
      <el-form-item prop="examType">
        <span slot="label">
          <span>试卷类型</span>
          <el-tooltip transfer>
            <i class="el-icon-warning-outline" />
            <div slot="content">
              <div>静态试卷：创建时生成试卷，每个答题者的试卷内容相同；</div>
              <div>动态试卷：设置抽题规则，使用时按照规则随机生成试卷，每个答题者的试卷内容不同。</div>
            </div>
          </el-tooltip>
        </span>
        <el-radio-group v-model="formData.examType" size="small">
          <el-radio-button :label="0">静态试卷</el-radio-button>
          <el-radio-button :label="1">动态试卷</el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="formData.examType === 0" label="选题方式" prop="selectedTopic">
        <el-radio-group v-model="formData.selectedTopic" size="small">
          <!-- <el-radio-button :label="1">基于知识点</el-radio-button> -->
          <el-radio-button :label="2">基于分类</el-radio-button>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="formData.examType === 0" label="组卷方式" prop="generatingTestPaper">
        <el-radio-group v-model="formData.generatingTestPaper" size="small">
          <el-radio-button :disabled="id ? true : false" :label="1">智能组卷</el-radio-button>
          <el-radio-button :disabled="id ? true : false" :label="2">手动组卷</el-radio-button>
        </el-radio-group>
      </el-form-item>
    </el-card>
  </el-form>
</template>
<script>
import moduleConf from '../config.js'
import validate from '@/packages/validate'
export default {
  props: {
    data: {
      type: Object
    },
    categoryList: {
      type: Array
    }
  },
  data() {
    return {
      moduleConf: moduleConf,
      validate: validate,
      id: this.$route.query.id,
      formData: {
        examName: '',
        examDesc: '',
        difficulty: 1,
        suggestTime: 30,
        selectedTopic: 2,
        generatingTestPaper: 1,
        categoryId: '',
        examType: 0,
        sceneTypeId: ''
      },
      sceneTypeList: [
        { id: 1, value: '实训' },
        { id: 2, value: '考试中心' },
        { id: 3, value: '攻防演练' },
        { id: 4, value: '理论赛' },
        { id: 5, value: 'CTF赛' },
        { id: 6, value: 'AWD赛' },
        { id: 7, value: '其他' }
      ],
      rules: {
        examName: [validate.required(), validate.name_64_char],
        categoryId: [validate.required('change')],
        examDesc: [validate.description],
        difficulty: [validate.required('change')],
        suggestTime: [validate.number_integer],
        sceneTypeId: [validate.required('change')]
      }
    }
  },
  created() {
    for (const key in this.formData) {
      if (key !== 'list') {
        this.formData[key] = this.data[key]
      }
    }
  }
}
</script>

