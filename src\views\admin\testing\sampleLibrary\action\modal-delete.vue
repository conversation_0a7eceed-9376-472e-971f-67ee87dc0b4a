<template>
  <div v-loading="loading" class="dialog-wrap">
    <el-alert
      :closable="false"
      type="warning"
      title="删除后数据将无法恢复，请谨慎操作。"
    />
    <batch-template
      :data="data"
      :available-data="availableData"
      view-key="name"
    />
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>

<script>
import batchTemplate from '@/packages/batch-delete/modal-bat-template.vue'
import modalMixins from '@/packages/mixins/modal_form'
import { sampleRemove } from '@/api/testing/index'

export default {
  components: {
    batchTemplate
  },
  mixins: [modalMixins],
  props: {
    data: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      loading: false
      // availableData: []
    }
  },
  computed: {
    availableData() {
      return this.data.filter(item => item)
    }
  },
  mounted() {
  },
  methods: {
    close() {
      this.$emit('close')
    },
    confirm: function() {
      this.loading = true
      const params = {}
      params.ids = this.data.map(item => {
        return item.id
      })
      sampleRemove(params.ids).then((res, error) => {
        if (res.data.code == 0) {
          this.close()
          this.$emit('call', 'refresh')
          this.loading = false
          this.$message.success('删除成功')
        }
      }).catch(() => {
        this.close()
        this.loading = false
      })
    }
  }
}
</script>
