<!--使用范例-->
<!--<table-td-multi-col :data="scope.row.attachments" :number="scope.row.attachments.length">-->
<!--<div slot="reference">-->
<!--  <a href="javascript:;" @click.stop="linkEvent('instances', scope.row.attachments[0])">-->
<!--    <Icon type="t2-cloud-instances"></Icon>-->
<!--    <span>{{scope.row.attachments[0].server_name}}</span>-->
<!--  </a>-->
<!--</div>-->
<!--<div v-for="instanceItem in scope.row.attachments" :key="instanceItem.server_id">-->
<!--  <a href="javascript:;" @click.stop="linkEvent('instances', instanceItem)">-->
<!--    <Icon type="t2-cloud-instances"></Icon>-->
<!--    <span>{{instanceItem.server_name}}</span>-->
<!--  </a>-->
<!--</div>-->
<!--</table-td-multi-col>-->
<template>
  <el-popover
    :disabled="number < 2 && !showEllipsis"
    trigger="hover">
    <div slot="reference" class="table-td-multi">
      <div class="table-td-multi-reference" @mouseover="handleMouseover($el)"><slot name="reference" /></div>
      <div v-if="number > 1" class="table-td-multi-number-wrap"><span class="table-td-multi-number">{{ number }}</span></div>
    </div>
    <div class="table-td-multi-content"><slot /></div>
  </el-popover>
</template>

<style lang="less">
.table-td-multi {
  display: flex;
  flex-direction: row;
}
.table-td-multi-content {
  max-height: 250px;
  overflow-y: auto;
  & > div {
    line-height: 20px;
    margin-right: 10px;
  }
}
.table-td-multi-reference {
  display: inline-block;
  flex: 1;
  overflow: hidden;
  & > div {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    word-break: break-all;
  }
}
.table-td-multi-number-wrap {
  display: inline-block;
  padding-left: 5px;
}
.table-td-multi-number {
  display: inline-block;
  background-color: #e9eff9;
  padding: 0 8px;
  border-radius: 50%;
  font-weight: 500;
}
</style>
<script>
export default {
  props: {
    number: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      // 是否显示了省略号，用来判断是否禁用tooltip
      showEllipsis: false
    }
  },
  methods: {
    handleMouseover(e) {
      const cellChild = e.querySelector('.table-td-multi-reference').firstChild
      this.showEllipsis = cellChild.scrollWidth > cellChild.offsetWidth
    }
  }
}
</script>
