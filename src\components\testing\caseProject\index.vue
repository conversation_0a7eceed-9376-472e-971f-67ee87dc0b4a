<template>
  <div class="resource-table">
    <!-- 操作区 -->
    <div class="operation-wrap">
      <div class="operation-left">
        <slot name="action" />
        <el-button type="primary" icon="el-icon-refresh" @click="refresh" />
      </div>
      <div class="operation-right">
        <el-badge :value="searchBtnShowNum">
          <el-button icon="el-icon-search" @click="openSearch">搜索</el-button>
        </el-badge>
        <!-- 自定义表格列 -->
        <t-table-config
          :data="columnsObj"
          :active-key-arr="columnsViewArr"
          @on-change-col="onChangeCol"
        />
      </div>
    </div>
    <!-- 搜索区 -->
    <t-search-box
      v-show="searchView"
      :search-key-list="searchKeyListView"
      :default-placeholder="'默认搜索项目名称'"
      @search="searchMultiple"
    />
    <!-- 列表 -->
    <t-table-view
      ref="tableView"
      :height="height"
      :single="true"
      :loading="tableLoading"
      :data="tableData"
      :total="tableTotal"
      :page-size="pageSize"
      :current="pageCurrent"
      :select-item="selectItem"
      current-key="id"
      @on-select="onSelect"
      @on-current="onCurrent"
      @on-change="changePage"
      @on-sort-change="onSortChange"
      @on-page-size-change="onPageSizeChange"
    >
      <el-table-column
        v-for="item in columnsViewArr"
        :key="item"
        :min-width="columnsObj[item].colMinWidth || 100"
        :width="columnsObj[item].colWidth"
        :label="columnsObj[item].title"
        :fixed="columnsObj[item].master ? 'left' : false"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span v-if="item === 'projectName'">
            <a :href="`/testing/testing/detail/${scope.row.id}/overview`" target="_blank">
              {{ scope.row[item] || '-' }}
            </a>
          </span>
          <span v-else-if="item === 'status'">
            <el-badge :type="getStatusClass(scope.row[item])" is-dot/>
            {{ getStatusInfo(scope.row[item], 'label') }}
            <el-tooltip v-if="scope.row.status === 9" transfer>
              <i
                style="color: #E7A502; position: absolute; right: 7px; top: 11px; font-size: 16px; cursor: pointer;"
                class="el-icon-warning-outline"
              />
              <div slot="content">
                <div>{{ scope.row.pendingComment || '项目已挂起' }}</div>
              </div>
            </el-tooltip>
          </span>
          <span v-else>{{ scope.row[item] || '-' }}</span>
        </template>
      </el-table-column>
    </t-table-view>
  </div>
</template>
<script>
import tSearchBox from '@/packages/search-box/index.vue'
import mixinsPageTable from '@/packages/mixins/page_table'
import tTableView from '@/packages/table-view/index.vue'
import tTableConfig from '@/packages/table-config/table-col-config.vue'
import tableTdMultiCol from '@/packages/table-config/table-td-multi-col.vue'
import module from './config.js'
import { caseProjectPage } from '@/api/testing/testCase.js'

// 引入封装的方法
export default {
  components: {
    tSearchBox,
    tTableView,
    tTableConfig,
    tableTdMultiCol
  },
  mixins: [mixinsPageTable],
  data() {
    return {
      module,
      moduleName: module.name,
      searchKeyList: [
        { key: 'projectName', label: '项目名称', master: true, placeholder: '请输入' },
        { key: 'testProduct', label: '检测产品', placeholder: '请输入' },
        { key: 'versionNumber', label: '版本号', placeholder: '请输入' },
        { key: 'statusList', label: '状态', type: 'select', placeholder: '请选择', valueList: [
          // 项目状态 projectStatus
          { label: '待测试', value: '0', type: 'info' },
          { label: '测试中', value: '1', type: 'warning' },
          { label: '测试通过', value: '2', type: 'success' },
          { label: '测试不通过', value: '3', type: 'danger' },
          // 项目状态 projectStatus
          { label: '待送审资料', value: '4', type: 'info' },
          { label: '待审核资料', value: '5', type: 'info' },
          { label: '待部署环境', value: '6', type: 'info' },
          // 挂起状态 pendingStatus
          { label: '已挂起', value: '9', type: 'info' }
        ] },
        { key: 'vendorName', label: '厂商名称', placeholder: '请输入' }
      ],
      columnsObj: {
        'projectName': {
          title: '项目名称', master: true
        },
        'testProduct': {
          title: '检测产品'
        },
        'versionNumber': {
          title: '版本号'
        },
        'status': {
          title: '状态'
        },
        'vendorName': {
          title: '厂商名称'
        }
      },
      columnsViewArr: [
        'projectName',
        'testProduct',
        'versionNumber',
        'status',
        'vendorName'
      ]
    }
  },
  methods: {
    getStatusInfo(status, key) {
      const statusItem = this.module.testStatusArr.find(item => item.value === Number(status))
      if (statusItem) {
        return statusItem[key]
      }
      return key === 'label' ? '-' : 'info'
    },
    getStatusClass(status) {
      const statusMap = {
        0: 'info', // 待测试
        1: 'warning', // 进行中
        2: 'success', // 通过
        3: 'danger', // 不通过
        4: 'info', // 待送审资料
        5: 'info', // 待审核
        6: 'info', // 待部署
        8: 'info', // 取消挂起
        9: 'info' // 挂起
      }
      return statusMap[String(status)] || 'info'
    },
    getList: function(showLoading = true) {
      if (showLoading) {
        this.tableLoading = true
      }
      const params = this.getPostData('page', 'limit')
      caseProjectPage(params).then((res) => {
        const data = { ...res.data }
        if (data && data.records) {
          data.records.forEach((record) => {
            if (record.pendingStatus === 9) {
              record.status = 9
            } else {
              record.status = record.projectStatus
            }
          })
          this.tableData = data.records
        }
        this.tableTotal = Number(data.total) || 0
        this.tableLoading = false
        this.handleSelection()
      }).catch(() => {
        this.tableLoading = false
      })
    }
  }
}
</script>
