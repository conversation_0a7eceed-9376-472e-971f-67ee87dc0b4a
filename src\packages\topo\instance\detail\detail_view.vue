<template>
  <!--  侧拉显示模式-->
  <div class="topo-detail-tabs-wrap">
    <template v-if="data">
      <el-tabs v-model="tabsActive" type="card" class="topo-detail-tabs-top">
        <el-tab-pane v-for="item in viewItem" :label="item.transName" :name="item.name" :key="item.name" />
      </el-tabs>
      <component
        v-for="item in viewItem"
        v-if="tabsActive === item.name"
        :key="item.name"
        :is="item.component"
        :id="id"
        :data="data"
        :type="type"
        :tab-name="item.name"
        class="topo-detail-tabs-content"
      />
    </template>
    <div v-else class="topo-no-data-tooltip"><span>暂无数据</span></div>
  </div>
</template>
<script>
export default {
  props: {
    // 资源ID
    id: String,
    // 资源数据对象
    data: {
      type: Object,
      default: () => {
        return null
      }
    },
    // 显示标签页
    viewItem: {
      type: Array,
      default: () => {
        return []
      }
    },
    type: String
  },
  data() {
    return {
      tabsActive: 'overview' // 标签页默认Key
    }
  }
}
</script>

<style lang="scss">
.topo-detail-tabs-wrap {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding-top: 20px;
  .topo-detail-tabs-content {
    flex: 1;
    min-height: 0;
    overflow-x: hidden;
    overflow-y: auto;
  }
}
</style>
