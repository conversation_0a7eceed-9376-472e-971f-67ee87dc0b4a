import request from '@/utils/request'
const headers = { 'Content-Type': 'application/json;charset=UTF-8' }

// 分页查询团体演练列表
export function sceneBasicInfoInstance(data) {
  return request({
    url: 'scene/sceneBasicInfoInstance/queryPage',
    method: 'post',
    headers,
    data
  })
}

// 上传封面
export function sceneFileUpload(data) {
  return request({
    url: 'scene/sceneFile/upload',
    method: 'post',
    headers,
    data
  })
}

// 根据场景id获取团体演练中的角色/阶段数据
export function sceneBasicInfoInstanceGetBySceneId(params) {
  return request({
    url: 'scene/sceneBasicInfoInstance/getBySceneId',
    method: 'get',
    headers,
    params
  })
}

// 创建团体演练
export function sceneBasicInfoInstanceCreate(data) {
  return request({
    url: 'scene/sceneBasicInfoInstance/create',
    method: 'post',
    headers,
    data
  })
}

// 根据团体演练id获取团体演练中的角色/阶段数据
export function sceneBasicInfoInstanceGetBySceneInstanceId(params) {
  return request({
    url: 'scene/sceneBasicInfoInstance/getBySceneInstanceId',
    method: 'get',
    headers,
    params
  })
}

// 编辑团体演练
export function sceneBasicInfoInstanceUpdate(data) {
  return request({
    url: 'scene/sceneBasicInfoInstance/update',
    method: 'post',
    headers,
    data
  })
}

// 团体演练分页查询人员管理列表
export function sceneInstancePlayerRel(data) {
  return request({
    url: 'scene/sceneInstancePlayerRel/queryPlayerPage',
    method: 'post',
    headers,
    data
  })
}

// 团体演练新增人员列表
export function sceneInstancePlayerRelCreate(data) {
  return request({
    url: 'scene/sceneInstancePlayerRel/create',
    method: 'post',
    headers,
    data
  })
}

// 团体演练新增人员列表战队
export function sceneInstancePlayerRelAddTerm(data) {
  return request({
    url: 'scene/sceneInstancePlayerRel/addTerm',
    method: 'post',
    headers,
    data
  })
}

// 团体演练删除人员列表
export function sceneInstancePlayerRelRemove(data) {
  return request({
    url: 'scene/sceneInstancePlayerRel/remove',
    method: 'post',
    headers,
    data
  })
}

// 团体演练人员批量导入模板下载 个人
export function downExcelImportPlayerModel(data) {
  return request({
    url: 'scene/sceneInstancePlayerRel/downExcelImportPlayerModel',
    method: 'post',
    headers,
    data,
    responseType: 'blob'
  })
}

// 团体演练人员批量导入模板下载 团队
export function downExcelImportTeamModel(data) {
  return request({
    url: 'scene/sceneInstancePlayerRel/downExcelImportTeamModel',
    method: 'post',
    headers,
    data,
    responseType: 'blob'
  })
}

// 团体演练人员批量导入接口 个人
export function sceneInstancePlayerRelExcelImportPlayer(data) {
  return request({
    url: 'scene/sceneInstancePlayerRel/excelImportPlayer',
    method: 'post',
    headers,
    data
  })
}

// 团体演练人员批量导入接口 团队
export function sceneInstancePlayerRelExcelImportTeam(data) {
  return request({
    url: 'scene/sceneInstancePlayerRel/excelImportTeam',
    method: 'post',
    headers,
    data
  })
}

// 团体演练人员批量导入失败下载失败表格接口 个人
export function downImportPlayerErrorExcel(data) {
  return request({
    url: 'scene/sceneInstancePlayerRel/downImportPlayerErrorExcel',
    method: 'post',
    headers,
    data,
    responseType: 'blob'
  })
}

// 团体演练人员批量导入失败下载失败表格接口 团队
export function downImportTeamErrorExcel(data) {
  return request({
    url: 'scene/sceneInstancePlayerRel/downImportTeamErrorExcel',
    method: 'post',
    headers,
    data,
    responseType: 'blob'
  })
}

// 团体演练人员查询人员列表
export function queryUserWithoutPlayer(data) {
  return request({
    url: 'scene/sceneInstancePlayerRel/queryUserWithoutPlayer',
    method: 'post',
    headers,
    data
  })
}

// 团体演练查看 该场景下角色关联用户数量
export function trainingPlayerCount(params) {
  return request({
    url: 'scene/sceneBasicInfoInstance/trainingPlayerCount',
    method: 'get',
    headers,
    params
  })
}

// 批量导出团体演练人员列表
export function exportTrainingRecord(data) {
  return request({
    url: 'scene/sceneInstancePlayerRel/exportPlayer',
    method: 'post',
    headers,
    data,
    responseType: 'blob'
  })
}

// 批量导出团体演练个人成绩列表
export function exportPersonRanking(data) {
  return request({
    url: 'scene/sceneInstancePlayerRel/exportPersonRanking',
    method: 'post',
    headers,
    data,
    responseType: 'blob'
  })
}

// 批量导出团体演练团队成绩列表
export function exportTeamRanking(data) {
  return request({
    url: 'scene/sceneInstancePlayerRel/exportTeamRanking',
    method: 'post',
    headers,
    data,
    responseType: 'blob'
  })
}

// 删除团体演练
export function sceneBasicInfoInstanceRemove(data) {
  return request({
    url: 'scene/sceneBasicInfoInstance/remove',
    method: 'post',
    headers,
    data
  })
}

// 开始团体演练
export function sceneBasicInfoInstanceStart(data) {
  return request({
    url: 'scene/sceneBasicInfoInstance/begin',
    method: 'post',
    headers,
    data
  })
}

// 结束团体演练
export function sceneBasicInfoInstanceFinish(data) {
  return request({
    url: 'scene/sceneBasicInfoInstance/finish',
    method: 'post',
    headers,
    data
  })
}

// 暂停团体演练
export function sceneBasicInfoInstancePause(data) {
  return request({
    url: 'scene/sceneBasicInfoInstance/pause',
    method: 'post',
    headers,
    data
  })
}

// 恢复团体演练
export function sceneBasicInfoInstanceRestore(data) {
  return request({
    url: 'scene/sceneBasicInfoInstance/restore',
    method: 'post',
    headers,
    data
  })
}

// 团体演练查询战队列表
export function sceneInstancePlayerRelQueryTeam(data) {
  return request({
    url: 'scene/sceneInstancePlayerRel/queryTeam',
    method: 'post',
    headers,
    data
  })
}

// 团体演练根据战队id查询选手列表
export function sceneInstancePlayerRelQueryPlayerByTeam(data) {
  return request({
    url: 'scene/sceneInstancePlayerRel/queryPlayerByTeam',
    method: 'post',
    headers,
    data
  })
}

// 团体演练根据战队id团队实例id查询选手列表
export function sceneInstancePlayerRelQueryByTeamAndInstance(data) {
  return request({
    url: 'scene/sceneInstancePlayerRel/queryByTeamAndInstance',
    method: 'post',
    headers,
    data
  })
}

// 团体演练查询训练成绩
export function sceneInstancePlayerRelQueryPage(data) {
  return request({
    url: 'scene/sceneInstancePlayerRel/queryPage',
    method: 'post',
    headers,
    data
  })
}

// 团体演练 查询所有列表人员
export function queryPlayerBySceneInstance(params) {
  return request({
    url: 'scene/sceneInstancePlayerRel/queryPlayerBySceneInstance',
    method: 'get',
    headers,
    params
  })
}

// 删除团体演练 错误数据表
export function delTeamErrorExcel(data) {
  return request({
    url: 'scene/sceneInstancePlayerRel/delErrorExcel',
    method: 'post',
    headers,
    data
  })
}
