<template>
  <div class="content-wrap-layout">
    <div v-loading="loading" class="system-setting-wrap">
      <!-- 基本配置start -->
      <el-divider content-position="left">
        <span style="font-size: 16px;">基本配置</span>
        <a href="javascript:;" @click="openEditModal('baseConfig')">
          <i class="el-icon-edit-outline" />
        </a>
      </el-divider>
      <el-form label-position="left" label-width="120px">
        <el-form-item label="后台管理地址">
          {{ configData.configName || '-' }}
        </el-form-item>
      </el-form>
      <!-- 基本配置end -->
      <!-- LOGO配置start -->
      <el-divider content-position="left">
        <span style="font-size: 16px;">LOGO配置</span>
        <a href="javascript:;" @click="openEditModal('logoConfig')">
          <i class="el-icon-edit-outline" />
        </a>
      </el-divider>
      <el-form label-position="left" label-width="120px">
        <el-form-item label="导航栏logo" style="margin-bottom: 15px;">
          <img v-src="configData.configUrl || ''" style="width: 300px;">
        </el-form-item>
        <el-form-item label="登录页logo" style="margin-bottom: 15px;">
          <img v-src="configData.linkUrl || ''" style="width: 300px;">
        </el-form-item>
        <el-form-item label="缩略站点logo">
          <img v-src="configData.iconUrl || ''" style="width: 60px;">
        </el-form-item>
      </el-form>
      <!-- LOGO配置start -->
      <!-- 登录页配置start -->
      <el-divider content-position="left">
        <span style="font-size: 16px;">登录页配置</span>
        <a href="javascript:;" @click="openEditModal('loginConfig')">
          <i class="el-icon-edit-outline" />
        </a>
      </el-divider>
      <el-form label-position="left" label-width="120px">
        <el-form-item label="产品名称">
          {{ configData.productName || '-' }}
        </el-form-item>
        <el-form-item label="产品描述">
          <div v-if="configData.productDesc" style="white-space: pre-line" v-html="configData.productDesc" />
          <div v-else>-</div>
        </el-form-item>
        <el-form-item label="单位信息">
          <div v-if="configData.unitInformation" style="white-space: pre-line" v-html="configData.unitInformation" />
          <div v-else>-</div>
        </el-form-item>
      </el-form>
      <!-- 登录页配置start -->
    </div>
    <!-- 中部弹窗 start-->
    <el-dialog
      :title="titleMapping[modalName]"
      :visible.sync="modalShow"
      :width="modalWidth"
      @close="modalClose"
    >
      <transition name="el-fade-in-linear">
        <component
          :is="modalName"
          :name="modalName"
          :data="configData"
          @close="modalClose"
          @call="confirmCall"
        />
      </transition>
    </el-dialog>
    <!-- 中部弹窗 end-->
  </div>
</template>

<script>
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
import baseConfig from './form/baseConfig.vue'
import logoConfig from './form/baseConfig.vue'
import loginConfig from './form/baseConfig.vue'
import { queryBaseInfoConfig } from '@/api/admin/config'
export default {
  components: {
    baseConfig,
    logoConfig,
    loginConfig
  },
  mixins: [mixinsActionMenu],
  data() {
    return {
      loading: false,
      // 弹窗title映射
      titleMapping: {
        'baseConfig': '基本设置',
        'logoConfig': 'LOGO配置',
        'loginConfig': '登录页配置'
      },
      configData: null
    }
  },
  created() {
    this.getConfigDetail()
  },
  methods: {
    // 打开弹窗
    'openEditModal': function(name) {
      this.modalName = name
      this.modalWidth = '600px'
    },
    'confirmCall': function(type, data) {
      if (type === 'close') {
        this.modalClose()
      } else if (type === 'refresh') {
        this.getConfigDetail()
      }
    },
    // 获取配置详情
    getConfigDetail() {
      this.loading = true
      queryBaseInfoConfig().then((res) => {
        if (res.data.length && res.code == 0) {
          this.configData = res.data[0]
          this.$store.commit('SET_WEBSITEINFO', this.configData)
          this.loading = false
        }
      }).finally(() => {
        this.loading = false
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.system-setting-wrap {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
  /deep/ .el-form {
    margin-left: 32px;
    padding-bottom: 20px;
    .el-form-item {
      margin-bottom: 0;
      .el-form-item__label:after {
        display: none;
      }
    }
  }
  /deep/ .el-divider--horizontal {
    margin: 15px 0;
    .el-divider__text.is-left {
      left: 0;
      padding-left: 0px;
    }
  }
}
</style>
