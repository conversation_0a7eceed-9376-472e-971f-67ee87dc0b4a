<template>
  <div style="height: 92%">
    <div class="resource-table drawer-wrap">
      <el-alert :closable="false" type="warning">
        <div slot="title">
          <p>选择拓扑模板后，将完全替换当前拓扑内容，请谨慎操作。</p>
        </div>
      </el-alert>
      <!-- 操作区 -->
      <div class="operation-wrap">
        <div class="operation-left">
          <slot name="action" />
          <el-button type="primary" icon="el-icon-refresh" @click="refresh" />
        </div>
        <div class="operation-right">
          <el-badge :value="searchBtnShowNum">
            <el-button icon="el-icon-search" @click="openSearch">搜索</el-button>
          </el-badge>
        </div>
      </div>
      <!-- 搜索区 -->
      <t-search-box
        v-show="searchView"
        :search-key-list="searchKeyListView"
        default-placeholder="默认搜索名称"
        @search="searchMultiple"
      />
      <div v-for="(item, index) in errList" :key="index">
        设备{{ item.nodeName }}已被角色{{ item.roleName }}的任务{{ item.taskName }}关联，请先解绑后再删除
      </div>
      <!-- 列表 -->
      <t-table-view
        ref="tableView"
        :height="height"
        :single="true"
        :loading="tableLoading"
        :data="tableData"
        :total="tableTotal"
        :page-size="pageSize"
        :current="pageCurrent"
        :select-item="selectItem"
        current-key="id"
        @on-select="onSelect"
        @on-current="onCurrent"
        @on-change="changePage"
        @on-sort-change="onSortChange"
        @on-page-size-change="onPageSizeChange"
      >
        <el-table-column
          v-for="item in columnsViewArr"
          :key="item"
          :min-width="colMinWidth"
          :label="columnsObj[item].title"
          :fixed="columnsObj[item].master ? 'left' : false"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <span v-if="item === 'created_at'">{{ new Date(scope.row[item]).toLocaleString() }}</span>
            <span v-else>{{ scope.row[item] || "-" }}</span>
          </template>
        </el-table-column>
      </t-table-view>
    </div>
    <!-- 底部 -->
    <div class="drawer-footer">
      <el-button :disabled="!selectItem.length" type="primary" @click="confirm">确定</el-button>
      <el-button type="text" @click="close">取消</el-button>
    </div>
  </div>
</template>

<script>
import module from '../config'
import tSearchBox from '../../search-box/index.vue'
import { removeByNodeIdListAPI } from '../api/role'
import tTableView from '../../table-view/index.vue'
import tableTdMultiCol from '../../table-config/table-td-multi-col.vue'
import mixinsPageTable from '../../mixins/page_table'
import { getListApi } from '../api/orchestration'
export default {
  components: {
    tSearchBox,
    tTableView,
    tableTdMultiCol
  },
  mixins: [mixinsPageTable],
  props: {
    data: {
      type: Object,
      default: () => {
        return {}
      }
    },
    id: {
      type: String
    }
  },
  data() {
    return {
      moduleName: module.name,
      errList: [],
      // 搜索配置项
      searchKeyList: [
        { key: 'name', label: '名称', master: true },
        { key: 'description', label: '描述' },
        { key: 'time_range', type: 'time_range', label: '创建时间', placeholder: '选择时间范围搜索' }
      ],
      // 所有可配置显示列 master：不可隐藏 title:列名称
      columnsObj: {
        'name': {
          title: '名称', master: true
        },
        'description': {
          title: '描述'
        },
        'created_at': {
          title: '创建时间'
        }
      },
      // 当前显示列key表 默认，如果localStorage有数据将被覆盖
      columnsViewArr: [
        'name',
        'description',
        'created_at'
      ]
    }
  },
  methods: {
    getList: function(showLoading = true) {
      if (showLoading) {
        this.tableLoading = true
      }
      const postData = this.getPostData('page', 'limit')
      postData.type = 'template'
      postData['offset'] = (postData['page'] - 1) * postData['limit']
      delete postData['page']
      getListApi(postData).then((res) => {
        if (res.data.code === 0) {
          this.tableTotal = res['data']['data']['total']
          this.tableData = res['data']['data']['result']
          this.tableLoading = false
          this.handleSelection()
        }
      }).catch(() => {
        this.tableLoading = false
      })
    },
    'close': function() {
      this.$emit('close')
    },
    'confirm': function() {
      const arr = []
      console.log(this.data)
      this.data.cells.forEach(item => {
        if (item.data.node_id) {
          arr.push(item.data.node_id)
        }
      })
      if (arr.length) {
        removeByNodeIdListAPI(arr).then(res => {
          this.$emit('call', 'selectTemplate', this.selectItem)
          this.close()
        }).catch(err => {
          this.errList = err.data
        })
      } else {
        this.$emit('call', 'selectTemplate', this.selectItem)
        this.close()
      }
    }
  }
}
</script>
