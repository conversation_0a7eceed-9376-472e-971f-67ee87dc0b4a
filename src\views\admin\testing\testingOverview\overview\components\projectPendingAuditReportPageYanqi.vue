<template>
  <div class="resource-table" style="height: 100%;">
    <!-- 操作区 -->
    <div class="operation-wrap">
      <div class="operation-left">
        <slot name="action" />
        <el-button type="primary" icon="el-icon-refresh" @click="refresh" />
      </div>
      <div class="operation-right">
        <el-badge :value="searchBtnShowNum">
          <el-button icon="el-icon-search" @click="openSearch">搜索</el-button>
        </el-badge>
        <!-- 自定义表格列 -->
        <t-table-config
          :data="columnsObj"
          :active-key-arr="columnsViewArr"
          @on-change-col="onChangeCol"
        />
      </div>
    </div>
    <!-- 搜索区 -->
    <t-search-box
      v-show="searchView"
      :search-key-list="searchKeyListView"
      :default-placeholder="'默认搜索项目名称'"
      @search="searchMultiple"
    />
    <!-- 列表 -->
    <t-table-view
      ref="tableView"
      :height="height"
      :single="true"
      :loading="tableLoading"
      :data="tableData"
      :total="tableTotal"
      :page-size="pageSize"
      :current="pageCurrent"
      :select-item="selectItem"
      type="list"
      current-key="id"
      @on-select="onSelect"
      @on-current="onCurrent"
      @on-change="changePage"
      @on-sort-change="onSortChange"
      @on-page-size-change="onPageSizeChange"
    >
      <el-table-column
        v-for="item in columnsViewArr"
        :key="item"
        :width="columnsObj[item].colWidth"
        :label="columnsObj[item].title"
        :fixed="columnsObj[item].master ? 'left' : false"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span v-if="item === 'name'">
            <a :href="`/testing/testing/detail/${scope.row.id}/overview`" target="_blank">{{ scope.row.name||'-' }}</a>
          </span>
          <span v-else-if="item === 'projectStatus'">
            <el-badge :type="statusMap[scope.row.projectStatus].type" is-dot/>{{ statusMap[scope.row.projectStatus].label }}
          </span>
          <span v-else>{{ scope.row[item] || '-' }}</span>
        </template>
      </el-table-column>
    </t-table-view>
  </div>
</template>
<script>
import tSearchBox from '@/packages/search-box/index.vue'
import mixinsPageTable from '@/packages/mixins/page_table'
import tTableView from '@/packages/table-view/index.vue'
import tTableConfig from '@/packages/table-config/table-col-config.vue'
import tableTdMultiCol from '@/packages/table-config/table-td-multi-col.vue'
import { projectPendingAuditReportPage } from '@/api/testing/testingOverview.js'
import { mapGetters } from 'vuex'

// 引入封装的方法
export default {
  components: {
    tSearchBox,
    tTableView,
    tTableConfig,
    tableTdMultiCol
  },
  mixins: [mixinsPageTable],
  props: {
    processId: {
      type: [String, Number],
      default: ''
    },
    currentRole: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      statusMap: {
        '0': { label: '待测试', value: '0', type: 'info' },
        '1': { label: '测试中', value: '1', type: 'warning' },
        '2': { label: '测试通过', value: '2', type: 'success' },
        '3': { label: '测试不通过', value: '3', type: 'danger' },
        '4': { label: '待送审资料', value: '4', type: 'info' },
        '5': { label: '待审核资料', value: '5', type: 'info' },
        '6': { label: '待部署环境', value: '6', type: 'info' },
        '8': { label: '取消挂起', value: '8', type: 'info' },
        '9': { label: '已挂起', value: '9', type: 'info' }
      },
      searchKeyList: [
        { key: 'name', label: '项目名称', master: true, placeholder: '请输入' },
        { key: 'productName', label: '检测产品', placeholder: '请输入' },
        { key: 'productVersion', label: '版本号', placeholder: '请输入' },
        { key: 'vendorName', label: '厂商名称', placeholder: '请输入' },
        { key: 'createAt', label: '创建时间', placeholder: '请选择', type: 'time_range' }
      ],
      columnsObj: {
        'name': {
          title: '项目名称', master: true
        },
        'productName': {
          title: '检测产品'
        },
        'productVersion': {
          title: '版本号'
        },
        'vendorName': {
          title: '厂商名称'
        },
        'projectStatus': {
          title: '状态'
        },
        'createAt': {
          title: '创建时间'
        }
      },
      columnsViewArr: [
        'name',
        'productName',
        'productVersion',
        'vendorName',
        'projectStatus',
        'createAt'
      ]
    }
  },
  computed: {
    ...mapGetters(['userInfo'])
  },
  methods: {
    getList: function(showLoading = true) {
      if (showLoading) {
        this.tableLoading = true
      }
      const params = this.getPostData('page', 'limit')
      params.processId = this.processId
      if (this.currentRole == '181251') {
        params.projectManagerId = this.userInfo.userId
      }
      params.pendingStatus = '3'
      if (params.projectStatus) {
        params.statusList = params.projectStatus
      }
      if (params.createAt) {
        params.createTimeStart = params.createAt.split(',')[0]
        params.createTimeEnd = params.createAt.split(',')[1]
        delete params.createAt
      }
      projectPendingAuditReportPage(params).then((res) => {
        const data = { ...res.data }
        this.tableData = data ? data.records : []
        this.tableTotal = Number(data.total) || 0
        this.tableLoading = false
        this.handleSelection()
      }).catch(() => {
        this.tableLoading = false
      })
    }
  }
}
</script>
