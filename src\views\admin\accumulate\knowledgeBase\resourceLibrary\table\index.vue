<template>
  <div class="resource-table">
    <el-row :gutter="24">
      <el-col :class="{ 'collapse-transition': true }" :span="isCollapsed ? 0 : 4" style="padding-right: 0;" >
        <div class="tree-container">
          <tree
            :i-search="false"
            :tree-data="treeData"
            @currentTreeNode="currentTreeNode"
            @clearCurrentNode="clearCurrentNode"
          />
        </div>
      </el-col>
      <el-col :class="{ 'collapse-transition': true }" :span="isCollapsed ? 24 : 20" class="_el-col-18">
        <!-- 操作区 -->
        <div class="operation-wrap">
          <div class="operation-left">
            <slot name="action" />
            <el-button type="primary" icon="el-icon-refresh" @click="refresh" />
          </div>
          <div class="operation-right">
            <el-badge :value="searchBtnShowNum" style="margin-right: 4px;">
              <el-button icon="el-icon-search" @click="openSearch">搜索</el-button>
            </el-badge>
            <t-table-config
              v-if="!customColData.length"
              :data="columnsObj"
              :active-key-arr="columnsViewArr"
              @on-change-col="onChangeCol"
            />
          </div>
        </div>
        <div class="collapse-btn" @click="toggleCollapse">
          <i :class="isCollapsed ? 'el-icon-arrow-right' : 'el-icon-arrow-left'"/>
        </div>
        <!-- 搜索区 -->
        <t-search-box
          v-show="searchView"
          :search-key-list="searchKeyListView"
          default-placeholder="请输入测试任务"
          @search="searchMultiple"
        />
        <!-- 列表 -->
        <t-table-view
          ref="tableView"
          :height="height"
          :single="single"
          :loading="tableLoading"
          :data="tableData"
          :total="tableTotal"
          :page-size="pageSize"
          :current="pageCurrent"
          :select-item="selectItem"
          current-key="id"
          @on-select="onSelect"
          @on-current="onCurrent"
          @on-change="changePage"
          @on-sort-change="onSortChange"
          @on-page-size-change="onPageSizeChange"
        >
          <el-table-column v-for="item in columnsViewArr" :key="item" :min-width="colMinWidth" :label="columnsObj[item].title" :fixed="columnsObj[item].master ? 'left' : false" :show-overflow-tooltip="true">
            <template slot-scope="scope">
              <span v-if="item === 'projectName'">
                <a href="javascript:;" @click="openNewPageInTab('testing_detail', scope.row, { id: scope.row.id, view: 'overview' })">
                  {{ scope.row[item] || '-' }}
                </a>
              </span>
              <span v-else>
                {{ scope.row[item] || '-' }}
              </span>
            </template>
          </el-table-column>
        </t-table-view>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import module from '../config.js'
import tree from '@/packages/tree/index.vue'
import tSearchBox from '@/packages/search-box/index.vue'
import tTableView from '@/packages/table-view/index.vue'
import tTableConfig from '@/packages/table-config/table-col-config.vue'
import mixinsPageTable from '@/packages/mixins/page_table'
import { testingItemsQueryPageAPI } from '@/api/testing/index'

export default {
  components: {
    tree,
    tSearchBox,
    tTableView,
    tTableConfig
  },
  mixins: [mixinsPageTable],
  data() {
    return {
      module,
      moduleName: module.name,
      isCollapsed: false,
      // 搜索配置项
      searchKeyList: [
        { key: 'projectName', label: '测试任务', master: true },
        { key: 'status', label: '状态', type: 'radio', valueList: module.statusArr },
        { key: 'testProduct', label: '优先级', type: 'radio', valueList: module.levelArr },
        { key: 'result', label: '执行结果', type: 'radio', valueList: module.resultArr },
        { key: 'time', label: '创建时间', type: 'time_range' }
      ],
      // 所有可配置显示列
      columnsObj: {
        'projectName': {
          title: '资源名称',
          master: true
        },
        'status': {
          title: '资源类型',
          colMinWidth: 150
        },
        'testProduct': {
          title: '来源'
        },
        'actualStartTime': {
          title: '知识标签'
        },
        'actualEndTime': {
          title: '分类'
        },
        'result': {
          title: '创建人'
        },
        'createTime': {
          title: '创建时间'
        }
      },
      // 当前显示列
      columnsViewArr: [
        'projectName',
        'status',
        'testProduct',
        'actualStartTime',
        'actualEndTime',
        'result',
        'createTime'
      ],
      currentNodeId: '',
      treeData: [{
        label: '一级 1',
        nodeKey: '1111111111111111111111',
        children: [{
          label: '二级 1-1',
          nodeKey: '22222222222222222222',
          children: [{
            label: '三级 1-1-1',
            nodeKey: '33333333333333333333'
          }]
        }]
      }, {
        label: '一级 2',
        children: [{
          label: '二级 2-1',
          children: [{
            label: '三级 2-1-1'
          }]
        }, {
          label: '二级 2-2',
          children: [{
            label: '三级 2-2-1'
          }]
        }]
      }, {
        label: '一级 3',
        children: [{
          label: '二级 3-1',
          children: [{
            label: '三级 3-1-1'
          }]
        }, {
          label: '二级 3-2',
          children: [{
            label: '三级 3-2-1'
          }]
        }]
      }]
    }
  },
  methods: {
    toggleCollapse() {
      this.isCollapsed = !this.isCollapsed
    },
    openNewPageInTab(name, row, params) {
      this.$router.push({
        name,
        params
      })
    },
    clearCurrentNode(e) {
      this.currentNodeId = ''
      this.getList()
    },
    currentTreeNode(data, node) {
      this.currentNodeId = data['nodeKey']
      this.getList()
    },
    getList(showLoading = true) {
      if (showLoading) {
        this.tableLoading = true
      }
      const params = this.getPostData('page', 'limit')
      if (params.time) {
        params.createTimeBegin = params.time.split(',')[0]
        params.createTimeEnd = params.time.split(',')[1]
      }
      if (this.currentNodeId) {
        params.nodeId = this.currentNodeId
      }
      testingItemsQueryPageAPI(params).then(res => {
        this.tableData = res.data.records || []
        this.tableTotal = Number(res.data.total) || 0
        this.tableLoading = false
        this.handleSelection()
      }).catch(() => {
        this.tableLoading = false
        // 使用模拟数据
        this.tableData = [
          {
            id: '1000',
            projectName: '测试用例1',
            testProduct: '2', // 中
            version: 'v1.0',
            status: '1',
            testTimes: 2,
            result: '1', // 通过
            planStartDate: '2023-12-01',
            actualStartTime: '李四',
            actualEndTime: '李四',
            createByName: '张三',
            createTime: '2025-04-09 09:00:00'
          },
          {
            id: '10001',
            projectName: '测试用例2',
            testProduct: '3', // 高
            version: 'v2.1',
            status: '2',
            testTimes: 1,
            result: '2', // 失败
            planStartDate: '2023-12-15',
            actualStartTime: '李四',
            actualEndTime: '王五',
            createByName: '李四',
            createTime: '2025-04-09 09:00:00'
          },
          {
            id: '10002',
            projectName: '测试用例3',
            testProduct: '2', // 中
            version: 'v2.1',
            status: '3',
            testTimes: 1,
            result: '3', // 阻塞
            planStartDate: '2023-12-15',
            actualStartTime: '王五',
            actualEndTime: '王五',
            createByName: '王五',
            createTime: '2025-04-01 09:00:00'
          },
          {
            id: '10003',
            projectName: '测试用例4',
            testProduct: '1', // 低
            version: 'v3.0',
            status: '4',
            testTimes: 0,
            result: '4', // 未执行
            planStartDate: '2023-12-20',
            actualStartTime: '王五',
            actualEndTime: '-',
            createByName: '赵六',
            createTime: '-'
          },
          {
            id: '10003',
            projectName: '测试用例4',
            testProduct: '1', // 低
            version: 'v3.0',
            status: '4',
            testTimes: 0,
            result: '4', // 未执行
            planStartDate: '2023-12-20',
            actualStartTime: '王五',
            actualEndTime: '-',
            createByName: '赵六',
            createTime: '-'
          },
          {
            id: '10003',
            projectName: '测试用例4',
            testProduct: '1', // 低
            version: 'v3.0',
            status: '4',
            testTimes: 0,
            result: '4', // 未执行
            planStartDate: '2023-12-20',
            actualStartTime: '王五',
            actualEndTime: '-',
            createByName: '赵六',
            createTime: '-'
          },
          {
            id: '10003',
            projectName: '测试用例4',
            testProduct: '1', // 低
            version: 'v3.0',
            status: '4',
            testTimes: 0,
            result: '4', // 未执行
            planStartDate: '2023-12-20',
            actualStartTime: '王五',
            actualEndTime: '-',
            createByName: '赵六',
            createTime: '-'
          },
          {
            id: '10003',
            projectName: '测试用例4',
            testProduct: '1', // 低
            version: 'v3.0',
            status: '4',
            testTimes: 0,
            result: '4', // 未执行
            planStartDate: '2023-12-20',
            actualStartTime: '王五',
            actualEndTime: '-',
            createByName: '赵六',
            createTime: '-'
          }
        ]
        this.tableTotal = 4
      })
    }
  }
}
</script>

<style scoped>
.operation-right {
  display: flex;
  align-items: center;
}

.operation-left {
  display: flex;
  align-items: center;
}

.collapse-btn {
  z-index: 200;
  position: absolute;
  top: 50%;
  left: -10px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: #FFF;
  color: #9a9a9a;
  border: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.collapse-transition {
  transition: all 0.3s ease;
}

.result-statistics {
  font-size: 14px;
}

.resource-table {
  height: 100%;
  display: flex;
  flex-direction: column;
}

._el-col-18 {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.el-row {
  height: 100%;
}

.el-col {
  height: 100%;
}

.tree-container {
  height: 100%;
  overflow-y: auto;
  border: 1px solid #E4E7ED;
}

.operation-wrap {
  flex-shrink: 0;
}

.t-table-view {
  flex: 1;
  overflow: hidden;
}

.success {
  color: #67C23A;
}

.warning {
  color: #E6A23C;
}

.primary {
  color: var(--color-600);
}

.danger {
  color: #F56C6C;
}
</style>
