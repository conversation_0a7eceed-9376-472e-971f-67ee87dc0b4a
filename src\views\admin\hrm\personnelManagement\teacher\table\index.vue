<template>
  <div class="resource-table">
    <!-- 操作区 -->
    <div class="operation-wrap">
      <div class="operation-left">
        <slot name="action" />
        <el-button type="primary" icon="el-icon-refresh" @click="refresh" />
      </div>
      <div class="operation-right">
        <el-badge :value="searchBtnShowNum">
          <el-button icon="el-icon-search" @click="openSearch">搜索</el-button>
        </el-badge>
        <!-- 自定义表格列 -->
        <t-table-config
          v-if="!customColData.length"
          :data="columnsObj"
          :active-key-arr="columnsViewArr"
          @on-change-col="onChangeCol"
        />
      </div>
    </div>
    <!-- 搜索区 -->
    <t-search-box
      v-show="searchView"
      :search-key-list="searchKeyListView"
      default-placeholder="默认搜索姓名"
      @search="searchMultiple"
    />
    <!-- 列表 -->
    <t-table-view
      ref="tableView"
      :height="height"
      :single="single"
      :loading="tableLoading"
      :data="tableData"
      :total="tableTotal"
      :page-size="pageSize"
      :current="pageCurrent"
      :select-item="selectItem"
      current-key="userId"
      @on-select="onSelect"
      @on-current="onCurrent"
      @on-change="changePage"
      @on-sort-change="onSortChange"
      @on-page-size-change="onPageSizeChange"
    >
      <el-table-column v-for="item in columnsViewArr" :key="item" :min-width="colMinWidth" :label="columnsObj[item].title" :fixed="columnsObj[item].master ? 'left' : false" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="item == 'sex'">{{ scope.row[item] ? (scope.row[item] == 1 ? '男':'女') : "-" }}</span>
          <span v-else>{{ scope.row[item] || "-" }}</span>
        </template>
      </el-table-column>
    </t-table-view>
  </div>
</template>

<script>
import module from '../config.js'
import tSearchBox from '@/packages/search-box/index.vue'
import tTableView from '@/packages/table-view/index.vue'
import tTableConfig from '@/packages/table-config/table-col-config.vue'
import tableTdMultiCol from '@/packages/table-config/table-td-multi-col.vue'
import mixinsPageTable from '@/packages/mixins/page_table'
import { queryTeacherManager } from '@/api/admin/training/student'

export default {
  components: {
    tSearchBox,
    tTableView,
    tTableConfig,
    tableTdMultiCol
  },
  mixins: [mixinsPageTable],
  data() {
    return {
      moduleName: module.name,
      // 搜索配置项
      searchKeyList: [
        { key: 'realname', label: '姓名', master: true },
        { key: 'username', label: '账号' }
      ],
      // 所有可配置显示列 master：不可隐藏 title:列名称
      columnsObj: {
        'realname': {
          title: '姓名',
          master: true
        },
        'username': {
          title: '账号'
        },
        'sex': {
          title: '性别'
        },
        'createTime': {
          title: '添加时间'
        }

      },
      // 当前显示列key表 默认，如果localStorage有数据将被覆盖
      columnsViewArr: [
        'realname',
        'username',
        'sex',
        'createTime'
      ]
    }
  },
  methods: {
    getList: function(showLoading = true) {
      if (showLoading) {
        this.tableLoading = true
      }
      const params = this.getPostData('page', 'limit')
      this.$emit('transmitTime', params)
      queryTeacherManager(params).then((res) => {
        if (res.code === 0) {
          this.tableData = res.data.records
          this.tableTotal = res.data.total
          this.tableLoading = false
        }
      }).catch(() => {
        this.tableLoading = false
      })
    }
  }
}
</script>
