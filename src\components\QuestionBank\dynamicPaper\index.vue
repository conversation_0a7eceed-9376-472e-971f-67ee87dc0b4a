<template>
  <div class="_paper_container card-bg">
    <div class="_paper_header">
      <div class="_paper_search">
        <div class="_paper_search_1">
          总题数
          <span style="font-weight: bold; color: var(--color-600); font-size: 18px; margin-right: 10px;">
            {{ total }}
          </span>
        </div>
        <div class="_paper_search_1">
          总分数
          <span style="font-weight: bold; color: var(--color-600); font-size: 18px;">
            {{ totalScore }}
          </span>
        </div>
      </div>
    </div>
    <el-tabs v-model="activeName">
      <el-tab-pane label="理论" name="theory">
        <el-form ref="theoryForm" :model="theoryRuleVO" disabled label-position="left" label-width="0" class="dialog-form">
          <div v-for="(item, index) in theoryRuleVO.categoryVOList" :key="index" class="category-wrap mb-10">
            <div class="select-wrap">
              <div class="left">
                <div class="select-title">筛选范围：</div>
                <el-form-item v-if="item.questionDepotId" label="题库" label-width="40px" style="margin-left: 20px;">
                  <el-select v-model="item.questionDepotId" filterable clearable style="width: 220px;">
                    <el-option
                      v-for="item in theoryQuestionCategoryList"
                      :key="item.id"
                      :label="item.questionDepotName"
                      :value="+item.id"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item v-if="item.categoryId" label="分类" label-width="40px" style="margin-left: 20px;">
                  <el-select v-model="item.categoryId" clearable style="width: 220px;">
                    <el-option
                      v-for="item in theoryCategoryList"
                      :key="item.id"
                      :label="item.categoryName"
                      :value="item.id"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item v-if="item.skillPointId" label="技能点" label-width="55px" style="margin-left: 20px;">
                  <el-select v-model="item.skillPointId" clearable style="width: 220px;">
                    <el-option
                      v-for="item in skillPointList"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    />
                  </el-select>
                </el-form-item>
              </div>
            </div>
            <div>
              <div class="trends-content border-b" style="padding-bottom: 6px;">
                <div>难易程度</div>
                <div>单选题</div>
                <div>多选题</div>
                <div>判断题</div>
                <div>CTF题</div>
                <div>填空题</div>
                <div>简答题</div>
              </div>
              <div class="trends-content border-b">
                <div>初级</div>
                <el-form-item :prop="`categoryVOList[${index}][primarySingle]`" class="category">
                  {{ item.primarySingle }}<span v-if="isShowCompletePercent"> / {{ item.categoryVO.primarySingle || 0 }}</span>
                </el-form-item>
                <el-form-item :prop="`categoryVOList[${index}][primaryMultiple]`" class="category">
                  {{ item.primaryMultiple }}<span v-if="isShowCompletePercent"> / {{ item.categoryVO.primaryMultiple || 0 }}</span>
                </el-form-item>
                <el-form-item :prop="`categoryVOList[${index}][primaryJudge]`" class="category">
                  {{ item.primaryJudge }}<span v-if="isShowCompletePercent"> / {{ item.categoryVO.primaryJudge || 0 }}</span>
                </el-form-item>
                <el-form-item :prop="`categoryVOList[${index}][primaryCTF]`" class="category">
                  {{ item.primaryCTF }}<span v-if="isShowCompletePercent"> / {{ item.categoryVO.primaryCTF || 0 }}</span>
                </el-form-item>
                <el-form-item :prop="`categoryVOList[${index}][primaryCompletion]`" class="category">
                  {{ item.primaryCompletion }}<span v-if="isShowCompletePercent"> / {{ item.categoryVO.primaryCompletion || 0 }}</span>
                </el-form-item>
                <el-form-item :prop="`categoryVOList[${index}][primaryShort]`" class="category">
                  {{ item.primaryShort }}<span v-if="isShowCompletePercent"> / {{ item.categoryVO.primaryShort || 0 }}</span>
                </el-form-item>
              </div>
              <div class="trends-content border-b">
                <div>中级</div>
                <el-form-item :prop="`categoryVOList[${index}][middleSingle]`" class="category">
                  {{ item.middleSingle }}<span v-if="isShowCompletePercent"> / {{ item.categoryVO.middleSingle || 0 }}</span>
                </el-form-item>
                <el-form-item :prop="`categoryVOList[${index}][middleMultiple]`" class="category">
                  {{ item.middleMultiple }}<span v-if="isShowCompletePercent"> / {{ item.categoryVO.middleMultiple || 0 }}</span>
                </el-form-item>
                <el-form-item :prop="`categoryVOList[${index}][middleJudge]`" class="category">
                  {{ item.middleJudge }}<span v-if="isShowCompletePercent"> / {{ item.categoryVO.middleJudge || 0 }}</span>
                </el-form-item>
                <el-form-item :prop="`categoryVOList[${index}][middleCTF]`" class="category">
                  {{ item.middleCTF }}<span v-if="isShowCompletePercent"> / {{ item.categoryVO.middleCTF || 0 }}</span>
                </el-form-item>
                <el-form-item :prop="`categoryVOList[${index}][middleCompletion]`" class="category">
                  {{ item.middleCompletion }}<span v-if="isShowCompletePercent"> / {{ item.categoryVO.middleCompletion || 0 }}</span>
                </el-form-item>
                <el-form-item :prop="`categoryVOList[${index}][middleShort]`" class="category">
                  {{ item.middleShort }}<span v-if="isShowCompletePercent"> / {{ item.categoryVO.middleShort || 0 }}</span>
                </el-form-item>
              </div>
              <div class="trends-content">
                <div>高级</div>
                <el-form-item :prop="`categoryVOList[${index}][seniorSingle]`" class="category">
                  {{ item.seniorSingle }}<span v-if="isShowCompletePercent"> / {{ item.categoryVO.seniorSingle || 0 }}</span>
                </el-form-item>
                <el-form-item :prop="`categoryVOList[${index}][seniorMultiple]`" class="category">
                  {{ item.seniorMultiple }}<span v-if="isShowCompletePercent"> / {{ item.categoryVO.seniorMultiple || 0 }}</span>
                </el-form-item>
                <el-form-item :prop="`categoryVOList[${index}][seniorJudge]`" class="category">
                  {{ item.seniorJudge }}<span v-if="isShowCompletePercent"> / {{ item.categoryVO.seniorJudge || 0 }}</span>
                </el-form-item>
                <el-form-item :prop="`categoryVOList[${index}][seniorCTF]`" class="category">
                  {{ item.seniorCTF }}<span v-if="isShowCompletePercent"> / {{ item.categoryVO.seniorCTF || 0 }}</span>
                </el-form-item>
                <el-form-item :prop="`categoryVOList[${index}][seniorCompletion]`" class="category">
                  {{ item.seniorCompletion }}<span v-if="isShowCompletePercent"> / {{ item.categoryVO.seniorCompletion || 0 }}</span>
                </el-form-item>
                <el-form-item :prop="`categoryVOList[${index}][seniorShort]`" class="category">
                  {{ item.seniorShort }}<span v-if="isShowCompletePercent"> / {{ item.categoryVO.seniorShort || 0 }}</span>
                </el-form-item>
              </div>
            </div>
          </div>
        </el-form>
        <div class="mt-15 category-wrap">
          <div style="font-size: 15px; font-weight: bold;">题目总计：</div>
          <div class="mt-10">
            <div class="trends-content border-b" style="padding-bottom: 6px;">
              <div>难易程度</div>
              <div>单选题</div>
              <div>多选题</div>
              <div>判断题</div>
              <div>CTF题</div>
              <div>填空题</div>
              <div>简答题</div>
            </div>
            <div class="trends-content border-b">
              <div>初级</div>
              <div>{{ theoryScore.countPrimarySingle }}</div>
              <div>{{ theoryScore.countPrimaryMultiple }}</div>
              <div>{{ theoryScore.countPrimaryJudge }}</div>
              <div>{{ theoryScore.countPrimaryCTF }}</div>
              <div>{{ theoryScore.countPrimaryCompletion }}</div>
              <div>{{ theoryScore.countPrimaryShort }}</div>
            </div>
            <div class="trends-content border-b">
              <div>中级</div>
              <div>{{ theoryScore.countMiddleSingle }}</div>
              <div>{{ theoryScore.countMiddleMultiple }}</div>
              <div>{{ theoryScore.countMiddleJudge }}</div>
              <div>{{ theoryScore.countMiddleCTF }}</div>
              <div>{{ theoryScore.countMiddleCompletion }}</div>
              <div>{{ theoryScore.countMiddleShort }}</div>
            </div>
            <div class="trends-content border-b">
              <div>高级</div>
              <div>{{ theoryScore.countSeniorSingle }}</div>
              <div>{{ theoryScore.countSeniorMultiple }}</div>
              <div>{{ theoryScore.countSeniorJudge }}</div>
              <div>{{ theoryScore.countSeniorCTF }}</div>
              <div>{{ theoryScore.countSeniorCompletion }}</div>
              <div>{{ theoryScore.countSeniorShort }}</div>
            </div>
            <div class="trends-content  border-b">
              <div>合计</div>
              <div>{{ theoryScore.countPrimarySingle + theoryScore.countMiddleSingle + theoryScore.countSeniorSingle }}</div>
              <div>{{ theoryScore.countPrimaryMultiple + theoryScore.countMiddleMultiple + theoryScore.countSeniorMultiple }}</div>
              <div>{{ theoryScore.countPrimaryJudge + theoryScore.countMiddleJudge + theoryScore.countSeniorJudge }}</div>
              <div>{{ theoryScore.countPrimaryCTF + theoryScore.countMiddleCTF + theoryScore.countSeniorCTF }}</div>
              <div>{{ theoryScore.countPrimaryCompletion + theoryScore.countMiddleCompletion + theoryScore.countSeniorCompletion }}</div>
              <div>{{ theoryScore.countPrimaryShort + theoryScore.countMiddleShort + theoryScore.countSeniorShort }}</div>
            </div>
            <el-form ref="formTheory" :model="theoryRuleVO" label-position="left" label-width="0" class="dialog-form">
              <div class="trends-content">
                <div>单题分数</div>
                <el-form-item prop="scoreSingle" class="category">
                  {{ theoryRuleVO.scoreSingle }} 分
                </el-form-item>
                <el-form-item prop="scoreMultiple" class="category">
                  {{ theoryRuleVO.scoreMultiple }} 分
                </el-form-item>
                <el-form-item prop="scoreJudge" class="category">
                  {{ theoryRuleVO.scoreJudge }} 分
                </el-form-item>
                <el-form-item prop="scoreCTF" class="category">
                  {{ theoryRuleVO.scoreCTF }} 分
                </el-form-item>
                <el-form-item prop="scoreCompletion" class="category">
                  {{ theoryRuleVO.scoreCompletion }} 分
                </el-form-item>
                <el-form-item prop="scoreShort" class="category">
                  {{ theoryRuleVO.scoreShort }} 分
                </el-form-item>
              </div>
            </el-form>
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane label="靶机" name="target">
        <el-form ref="targetForm" :model="targetRuleVO" disabled label-position="left" label-width="0" class="dialog-form">
          <div v-for="(item, index) in targetRuleVO.categoryVOList" :key="index" class="mb-10 category-wrap">
            <div class="select-wrap">
              <div class="left">
                <div class="select-title">筛选范围：</div>
                <el-form-item v-if="item.questionDepotId" label="题库" label-width="40px" style="margin-left: 20px;">
                  <el-select v-model="item.questionDepotId" filterable clearable style="width: 220px;">
                    <el-option
                      v-for="item in targetQuestionCategoryList"
                      :key="item.id"
                      :label="item.questionDepotName"
                      :value="+item.id"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item v-if="item.categoryId" label="分类" label-width="40px" style="margin: 0 25px 0 20px;">
                  <el-select v-model="item.categoryId" clearable style="width: 220px;">
                    <el-option
                      v-for="item in targetCategoryList"
                      :key="item.id"
                      :label="item.categoryName"
                      :value="item.id"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item v-if="item.skillPointId" label="技能点" label-width="55px">
                  <el-select v-model="item.skillPointId" clearable style="width: 220px;">
                    <el-option
                      v-for="item in skillPointList"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    />
                  </el-select>
                </el-form-item>
              </div>
            </div>
            <div>
              <div class="trends-content border-b" style="padding-bottom: 6px;">
                <div>难易程度</div>
                <div>CTF题</div>
                <div>AWD题</div>
                <div>漏洞题</div>
                <div>其它题</div>
              </div>
              <div class="trends-content border-b">
                <div>初级</div>
                <el-form-item :prop="`categoryVOList[${index}][primaryCTF]`" class="category">
                  {{ item.primaryCTF }}<span v-if="isShowCompletePercent"> / {{ item.categoryVO.primaryCTF || 0 }}</span>
                </el-form-item>
                <el-form-item :prop="`categoryVOList[${index}][primaryAWD]`" class="category">
                  {{ item.primaryAWD }}<span v-if="isShowCompletePercent"> / {{ item.categoryVO.primaryAWD || 0 }}</span>
                </el-form-item>
                <el-form-item :prop="`categoryVOList[${index}][primaryBug]`" class="category">
                  {{ item.primaryBug }}<span v-if="isShowCompletePercent"> / {{ item.categoryVO.primaryBug || 0 }}</span>
                </el-form-item>
                <el-form-item :prop="`categoryVOList[${index}][primaryOther]`" class="category">
                  {{ item.primaryOther }}<span v-if="isShowCompletePercent"> / {{ item.categoryVO.primaryOther || 0 }}</span>
                </el-form-item>
              </div>
              <div class="trends-content border-b">
                <div>中级</div>
                <el-form-item :prop="`categoryVOList[${index}][middleCTF]`" class="category">
                  {{ item.middleCTF }}
                  <span v-if="isShowCompletePercent"> / {{ item.categoryVO.middleCTF || 0 }}</span>
                </el-form-item>
                <el-form-item :prop="`categoryVOList[${index}][middleAWD]`" class="category">
                  {{ item.middleAWD }}
                  <span v-if="isShowCompletePercent"> / {{ item.categoryVO.middleAWD || 0 }}</span>
                </el-form-item>
                <el-form-item :prop="`categoryVOList[${index}][middleBug]`" class="category">
                  {{ item.middleBug }}
                  <span v-if="isShowCompletePercent"> / {{ item.categoryVO.middleBug || 0 }}</span>
                </el-form-item>
                <el-form-item :prop="`categoryVOList[${index}][middleOther]`" class="category">
                  {{ item.middleOther }}
                  <span v-if="isShowCompletePercent"> / {{ item.categoryVO.middleOther || 0 }}</span>
                </el-form-item>
              </div>
              <div class="trends-content">
                <div>高级</div>
                <el-form-item :prop="`categoryVOList[${index}][seniorCTF]`" class="category">
                  {{ item.seniorCTF }}
                  <span v-if="isShowCompletePercent"> / {{ item.categoryVO.seniorCTF || 0 }}</span>
                </el-form-item>
                <el-form-item :prop="`categoryVOList[${index}][seniorAWD]`" class="category">
                  {{ item.seniorAWD }}
                  <span v-if="isShowCompletePercent"> / {{ item.categoryVO.seniorAWD || 0 }}</span>
                </el-form-item>
                <el-form-item :prop="`categoryVOList[${index}][seniorBug]`" class="category">
                  {{ item.seniorBug }}
                  <span v-if="isShowCompletePercent"> / {{ item.categoryVO.seniorBug || 0 }}</span>
                </el-form-item>
                <el-form-item :prop="`categoryVOList[${index}][seniorOther]`" class="category">
                  {{ item.seniorOther }}
                  <span v-if="isShowCompletePercent"> / {{ item.categoryVO.seniorOther || 0 }}</span>
                </el-form-item>
              </div>
            </div>
          </div>
        </el-form>
        <div class="mt-15 category-wrap">
          <div style="font-size: 15px; font-weight: bold;">题目总计：</div>
          <div class="mt-10">
            <div class="trends-content border-b" style="padding-bottom: 6px;">
              <div>难易程度</div>
              <div>CTF题</div>
              <div>AWD题</div>
              <div>漏洞题</div>
              <div>其它题</div>
            </div>
            <div class="trends-content border-b">
              <div>初级</div>
              <div>{{ targetScore.countPrimaryCTF }}</div>
              <div>{{ targetScore.countPrimaryAWD }}</div>
              <div>{{ targetScore.countPrimaryBug }}</div>
              <div>{{ targetScore.countPrimaryOther }}</div>
            </div>
            <div class="trends-content border-b">
              <div>中级</div>
              <div>{{ targetScore.countMiddleCTF }}</div>
              <div>{{ targetScore.countMiddleAWD }}</div>
              <div>{{ targetScore.countMiddleBug }}</div>
              <div>{{ targetScore.countMiddleOther }}</div>
            </div>
            <div class="trends-content border-b">
              <div>高级</div>
              <div>{{ targetScore.countSeniorCTF }}</div>
              <div>{{ targetScore.countSeniorAWD }}</div>
              <div>{{ targetScore.countSeniorBug }}</div>
              <div>{{ targetScore.countSeniorOther }}</div>
            </div>
            <div class="trends-content  border-b">
              <div>合计</div>
              <div>{{ targetScore.countPrimaryCTF + targetScore.countMiddleCTF + targetScore.countSeniorCTF }}</div>
              <div>{{ targetScore.countPrimaryAWD + targetScore.countMiddleAWD + targetScore.countSeniorAWD }}</div>
              <div>{{ targetScore.countPrimaryBug + targetScore.countMiddleBug + targetScore.countSeniorBug }}</div>
              <div>{{ targetScore.countPrimaryOther + targetScore.countMiddleOther + targetScore.countSeniorOther }}</div>
            </div>
            <el-form ref="formTarget" :model="targetRuleVO" label-position="left" label-width="0" class="dialog-form">
              <div class="trends-content">
                <div>单题分数</div>
                <el-form-item prop="scoreCTF" class="category">
                  {{ targetRuleVO.scoreCTF }} 分
                </el-form-item>
                <el-form-item prop="scoreAWD" class="category">
                  {{ targetRuleVO.scoreAWD }} 分
                </el-form-item>
                <el-form-item prop="scoreBug" class="category">
                  {{ targetRuleVO.scoreBug }} 分
                </el-form-item>
                <el-form-item prop="scoreOther" class="category">
                  {{ targetRuleVO.scoreOther }} 分
                </el-form-item>
              </div>
            </el-form>
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane label="仿真" name="simulation">
        <el-form ref="simulationForm" :model="simulationRuleVO" disabled label-position="left" label-width="0" class="dialog-form">
          <div v-for="(item, index) in simulationRuleVO.categoryVOList" :key="index" class="category-wrap mb-10">
            <div class="select-wrap">
              <div class="left">
                <div class="select-title">筛选范围：</div>
                <el-form-item v-if="item.questionDepotId" label="题库" label-width="40px" style="margin-left: 20px">
                  <el-select v-model="item.questionDepotId" filterable clearable style="width: 220px;">
                    <el-option
                      v-for="item in simulationQuestionCategoryList"
                      :key="item.id"
                      :label="item.questionDepotName"
                      :value="+item.id"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item v-if="item.categoryId" label="分类" label-width="40px" style="margin: 0 25px 0 20px;">
                  <el-select v-model="item.categoryId" clearable style="width: 220px;">
                    <el-option
                      v-for="item in simulationCategoryList"
                      :key="item.id"
                      :label="item.categoryName"
                      :value="item.id"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item v-if="item.skillPointId" label="技能点" label-width="55px">
                  <el-select v-model="item.skillPointId" clearable style="width: 220px;">
                    <el-option
                      v-for="item in skillPointList"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    />
                  </el-select>
                </el-form-item>
              </div>
            </div>
            <div>
              <div class="trends-content border-b" style="padding-bottom: 6px;">
                <div>难易程度</div>
                <div>单选题</div>
                <div>多选题</div>
                <div>判断题</div>
                <div>CTF题</div>
                <div>填空题</div>
                <div>简答题</div>
                <div>组合题</div>
              </div>
              <div class="trends-content border-b">
                <div>初级</div>
                <el-form-item :prop="`categoryVOList[${index}][primarySingle]`" class="category">
                  {{ item.primarySingle }}
                  <span v-if="isShowCompletePercent"> / {{ item.categoryVO.primarySingle || 0 }}</span>
                </el-form-item>
                <el-form-item :prop="`categoryVOList[${index}][primaryMultiple]`" class="category">
                  {{ item.primaryMultiple }}
                  <span v-if="isShowCompletePercent"> / {{ item.categoryVO.primaryMultiple || 0 }}</span>
                </el-form-item>
                <el-form-item :prop="`categoryVOList[${index}][primaryJudge]`" class="category">
                  {{ item.primaryJudge }}
                  <span v-if="isShowCompletePercent"> / {{ item.categoryVO.primaryJudge || 0 }}</span>
                </el-form-item>
                <el-form-item :prop="`categoryVOList[${index}][primaryCTF]`" class="category">
                  {{ item.primaryCTF }}
                  <span v-if="isShowCompletePercent"> / {{ item.categoryVO.primaryCTF || 0 }}</span>
                </el-form-item>
                <el-form-item :prop="`categoryVOList[${index}][primaryCompletion]`" class="category">
                  {{ item.primaryCompletion }}
                  <span v-if="isShowCompletePercent"> / {{ item.categoryVO.primaryCompletion || 0 }}</span>
                </el-form-item>
                <el-form-item :prop="`categoryVOList[${index}][primaryShort]`" class="category">
                  {{ item.primaryShort }}
                  <span v-if="isShowCompletePercent"> / {{ item.categoryVO.primaryShort || 0 }}</span>
                </el-form-item>
                <el-form-item :prop="`categoryVOList[${index}][primaryCombinatorial]`" class="category">
                  {{ item.primaryCombinatorial }}
                  <span v-if="isShowCompletePercent"> / {{ item.categoryVO.primaryCombinatorial || 0 }}</span>
                </el-form-item>
              </div>
              <div class="trends-content border-b">
                <div>中级</div>
                <el-form-item :prop="`categoryVOList[${index}][middleSingle]`" class="category">
                  {{ item.middleSingle }}
                  <span v-if="isShowCompletePercent"> / {{ item.categoryVO.middleSingle || 0 }}</span>
                </el-form-item>
                <el-form-item :prop="`categoryVOList[${index}][middleMultiple]`" class="category">
                  {{ item.middleMultiple }}
                  <span v-if="isShowCompletePercent"> / {{ item.categoryVO.middleMultiple || 0 }}</span>
                </el-form-item>
                <el-form-item :prop="`categoryVOList[${index}][middleJudge]`" class="category">
                  {{ item.middleJudge }}
                  <span v-if="isShowCompletePercent"> / {{ item.categoryVO.middleJudge || 0 }}</span>
                </el-form-item>
                <el-form-item :prop="`categoryVOList[${index}][middleCTF]`" class="category">
                  {{ item.middleCTF }}
                  <span v-if="isShowCompletePercent"> / {{ item.categoryVO.middleCTF || 0 }}</span>
                </el-form-item>
                <el-form-item :prop="`categoryVOList[${index}][middleCompletion]`" class="category">
                  {{ item.middleCompletion }}
                  <span v-if="isShowCompletePercent"> / {{ item.categoryVO.middleCompletion || 0 }}</span>
                </el-form-item>
                <el-form-item :prop="`categoryVOList[${index}][middleShort]`" class="category">
                  {{ item.middleShort }}
                  <span v-if="isShowCompletePercent"> / {{ item.categoryVO.middleShort || 0 }}</span>
                </el-form-item>
                <el-form-item :prop="`categoryVOList[${index}][middleCombinatorial]`" class="category">
                  {{ item.middleCombinatorial }}
                  <span v-if="isShowCompletePercent"> / {{ item.categoryVO.middleCombinatorial || 0 }}</span>
                </el-form-item>
              </div>
              <div class="trends-content">
                <div>高级</div>
                <el-form-item :prop="`categoryVOList[${index}][seniorSingle]`" class="category">
                  {{ item.seniorSingle }}
                  <span v-if="isShowCompletePercent"> / {{ item.categoryVO.seniorSingle || 0 }}</span>
                </el-form-item>
                <el-form-item :prop="`categoryVOList[${index}][seniorMultiple]`" class="category">
                  {{ item.seniorMultiple }}
                  <span v-if="isShowCompletePercent"> / {{ item.categoryVO.seniorMultiple || 0 }}</span>
                </el-form-item>
                <el-form-item :prop="`categoryVOList[${index}][seniorJudge]`" class="category">
                  {{ item.seniorJudge }}
                  <span v-if="isShowCompletePercent"> / {{ item.categoryVO.seniorJudge || 0 }}</span>
                </el-form-item>
                <el-form-item :prop="`categoryVOList[${index}][seniorCTF]`" class="category">
                  {{ item.seniorCTF }}
                  <span v-if="isShowCompletePercent"> / {{ item.categoryVO.seniorCTF || 0 }}</span>
                </el-form-item>
                <el-form-item :prop="`categoryVOList[${index}][seniorCompletion]`" class="category">
                  {{ item.seniorCompletion }}
                  <span v-if="isShowCompletePercent"> / {{ item.categoryVO.seniorCompletion || 0 }}</span>
                </el-form-item>
                <el-form-item :prop="`categoryVOList[${index}][seniorShort]`" class="category">
                  {{ item.seniorShort }}
                  <span v-if="isShowCompletePercent"> / {{ item.categoryVO.seniorShort || 0 }}</span>
                </el-form-item>
                <el-form-item :prop="`categoryVOList[${index}][seniorCombinatorial]`" class="category">
                  {{ item.seniorCombinatorial }}
                  <span v-if="isShowCompletePercent"> / {{ item.categoryVO.seniorCombinatorial || 0 }}</span>
                </el-form-item>
              </div>
            </div>
          </div>
        </el-form>
        <div class="mt-15 category-wrap">
          <div style="font-size: 15px; font-weight: bold;">题目总计：</div>
          <div class="mt-10">
            <div class="trends-content border-b" style="padding-bottom: 6px;">
              <div>难易程度</div>
              <div>单选题</div>
              <div>多选题</div>
              <div>判断题</div>
              <div>CTF题</div>
              <div>填空题</div>
              <div>简答题</div>
              <div>组合题</div>
            </div>
            <div class="trends-content border-b">
              <div>初级</div>
              <div>{{ simulationScore.countPrimarySingle }}</div>
              <div>{{ simulationScore.countPrimaryMultiple }}</div>
              <div>{{ simulationScore.countPrimaryJudge }}</div>
              <div>{{ simulationScore.countPrimaryCTF }}</div>
              <div>{{ simulationScore.countPrimaryCompletion }}</div>
              <div>{{ simulationScore.countPrimaryShort }}</div>
              <div>{{ simulationScore.countPrimaryCombinatorial }}</div>
            </div>
            <div class="trends-content border-b">
              <div>中级</div>
              <div>{{ simulationScore.countMiddleSingle }}</div>
              <div>{{ simulationScore.countMiddleMultiple }}</div>
              <div>{{ simulationScore.countMiddleJudge }}</div>
              <div>{{ simulationScore.countMiddleCTF }}</div>
              <div>{{ simulationScore.countMiddleCompletion }}</div>
              <div>{{ simulationScore.countMiddleShort }}</div>
              <div>{{ simulationScore.countMiddleCombinatorial }}</div>
            </div>
            <div class="trends-content border-b">
              <div>高级</div>
              <div>{{ simulationScore.countSeniorSingle }}</div>
              <div>{{ simulationScore.countSeniorMultiple }}</div>
              <div>{{ simulationScore.countSeniorJudge }}</div>
              <div>{{ simulationScore.countSeniorCTF }}</div>
              <div>{{ simulationScore.countSeniorCompletion }}</div>
              <div>{{ simulationScore.countSeniorShort }}</div>
              <div>{{ simulationScore.countSeniorCombinatorial }}</div>
            </div>
            <div class="trends-content  border-b">
              <div>合计</div>
              <div>{{ simulationScore.countPrimarySingle + simulationScore.countMiddleSingle + simulationScore.countSeniorSingle }}</div>
              <div>{{ simulationScore.countPrimaryMultiple + simulationScore.countMiddleMultiple + simulationScore.countSeniorMultiple }}</div>
              <div>{{ simulationScore.countPrimaryJudge + simulationScore.countMiddleJudge + simulationScore.countSeniorJudge }}</div>
              <div>{{ simulationScore.countPrimaryCTF + simulationScore.countMiddleCTF + simulationScore.countSeniorCTF }}</div>
              <div>{{ simulationScore.countPrimaryCompletion + simulationScore.countMiddleCompletion + simulationScore.countSeniorCompletion }}</div>
              <div>{{ simulationScore.countPrimaryShort + simulationScore.countMiddleShort + simulationScore.countSeniorShort }}</div>
              <div>{{ simulationScore.countPrimaryCombinatorial + simulationScore.countMiddleCombinatorial + simulationScore.countSeniorCombinatorial }}</div>
            </div>
            <el-form ref="formSimulation" :model="simulationRuleVO" label-position="left" label-width="0" class="dialog-form">
              <div class="trends-content">
                <div>单题分数</div>
                <el-form-item prop="scoreSingle" class="category">
                  {{ simulationRuleVO.scoreSingle }} 分
                </el-form-item>
                <el-form-item :prop="scoreMultiple" class="category">
                  {{ simulationRuleVO.scoreMultiple }} 分
                </el-form-item>
                <el-form-item prop="scoreJudge" class="category">
                  {{ simulationRuleVO.scoreJudge }} 分
                </el-form-item>
                <el-form-item prop="scoreCTF" class="category">
                  {{ simulationRuleVO.scoreCTF }} 分
                </el-form-item>
                <el-form-item prop="scoreCompletion" class="category">
                  {{ simulationRuleVO.scoreCompletion }} 分
                </el-form-item>
                <el-form-item prop="scoreShort" class="category">
                  {{ simulationRuleVO.scoreShort }} 分
                </el-form-item>
                <el-form-item prop="scoreCombinatorial" class="category">
                  {{ simulationRuleVO.scoreCombinatorial }} 分
                </el-form-item>
              </div>
            </el-form>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import { getCategory } from '@/api/accumulate/category'
import { getSkillPoint } from '@/api/accumulate/skillPoint.js'
import { getQuestionCategory } from '@/api/accumulate/questionLibrary'

export default {
  name: 'DynamicPaper',
  props: {
    data: {
      type: Object,
      default: () => {}
    },
    // 是否显示完整分数，例：1/2 ，默认显示1
    isShowCompletePercent: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      activeName: 'theory',
      img: require('@/assets/empty_state.png'),
      theoryCategoryList: [],
      targetCategoryList: [],
      simulationCategoryList: [],
      theoryQuestionCategoryList: [],
      targetQuestionCategoryList: [],
      simulationQuestionCategoryList: [],
      skillPointList: [],
      // 理论题
      theoryRuleVO: {
        categoryVOList: [
          {
            categoryId: '',
            skillPointId: '',
            categoryVO: {},
            primarySingle: 0, // 初级单选题
            primaryMultiple: 0, // 初级多选题
            primaryJudge: 0, // 初级判断题
            primaryCTF: 0, // 初级CTF题
            primaryCompletion: 0, // 初级填空题
            primaryShort: 0, // 初级简答题
            primaryAWD: 0, // 初级awd题
            primaryBug: 0, // 初级bug题
            primaryCombinatorial: 0, // 初级组合题
            primaryOther: 0, // 初级其它题
            middleSingle: 0, // 中级单选题
            middleMultiple: 0, // 中级多选题
            middleJudge: 0, // 中级判断题
            middleCTF: 0, // 中级CTF题
            middleCompletion: 0, // 中级填空题
            middleShort: 0, // 中级简答题
            middleAWD: 0, // 中级awd题
            middleBug: 0, // 中级bug题
            middleCombinatorial: 0, // 中级组合题
            middleOther: 0, // 中级其它题
            seniorSingle: 0, // 高级单选题
            seniorMultiple: 0, // 高级多选题
            seniorJudge: 0, // 高级判断题
            seniorCTF: 0, // 高级CTF题
            seniorCompletion: 0, // 高级填空题
            seniorShort: 0, // 高级简答题
            seniorAWD: 0, // 高级awd题
            seniorBug: 0, // 高级bug题
            seniorCombinatorial: 0, // 高级组合题
            seniorOther: 0 // 高级其它题
          }
        ],
        scoreSingle: 0, // 单选题分数
        scoreMultiple: 0, // 多选题分数
        scoreJudge: 0, // 判断题分数
        scoreCTF: 0, // CTF题分数
        scoreCompletion: 0, // 填空题分数
        scoreShort: 0 // 简答题分数
      },
      // 靶机题
      targetRuleVO: {
        categoryVOList: [
          {
            categoryId: '',
            skillPointId: '',
            categoryVO: {},
            primarySingle: 0, // 初级单选题
            primaryMultiple: 0, // 初级多选题
            primaryJudge: 0, // 初级判断题
            primaryCTF: 0, // 初级CTF题
            primaryCompletion: 0, // 初级填空题
            primaryShort: 0, // 初级简答题
            primaryAWD: 0, // 初级awd题
            primaryBug: 0, // 初级bug题
            primaryCombinatorial: 0, // 初级组合题
            primaryOther: 0, // 初级其它题
            middleSingle: 0, // 中级单选题
            middleMultiple: 0, // 中级多选题
            middleJudge: 0, // 中级判断题
            middleCTF: 0, // 中级CTF题
            middleCompletion: 0, // 中级填空题
            middleShort: 0, // 中级简答题
            middleAWD: 0, // 中级awd题
            middleBug: 0, // 中级bug题
            middleCombinatorial: 0, // 中级组合题
            middleOther: 0, // 中级其它题
            seniorSingle: 0, // 高级单选题
            seniorMultiple: 0, // 高级多选题
            seniorJudge: 0, // 高级判断题
            seniorCTF: 0, // 高级CTF题
            seniorCompletion: 0, // 高级填空题
            seniorShort: 0, // 高级简答题
            seniorAWD: 0, // 高级awd题
            seniorBug: 0, // 高级bug题
            seniorCombinatorial: 0, // 高级组合题
            seniorOther: 0 // 高级其它题
          }
        ],
        scoreCTF: 0, // CTF题分数
        scoreAWD: 0, // AWD题分数
        scoreOther: 0, // 其它题分数
        scoreBug: 0 // 漏洞题分数
      },
      // 仿真题
      simulationRuleVO: {
        categoryVOList: [
          {
            categoryId: '',
            skillPointId: '',
            categoryVO: {},
            primarySingle: 0, // 初级单选题
            primaryMultiple: 0, // 初级多选题
            primaryJudge: 0, // 初级判断题
            primaryCTF: 0, // 初级CTF题
            primaryCompletion: 0, // 初级填空题
            primaryShort: 0, // 初级简答题
            primaryAWD: 0, // 初级awd题
            primaryBug: 0, // 初级bug题
            primaryCombinatorial: 0, // 初级组合题
            primaryOther: 0, // 初级其它题
            middleSingle: 0, // 中级单选题
            middleMultiple: 0, // 中级多选题
            middleJudge: 0, // 中级判断题
            middleCTF: 0, // 中级CTF题
            middleCompletion: 0, // 中级填空题
            middleShort: 0, // 中级简答题
            middleAWD: 0, // 中级awd题
            middleBug: 0, // 中级bug题
            middleCombinatorial: 0, // 中级组合题
            middleOther: 0, // 中级其它题
            seniorSingle: 0, // 高级单选题
            seniorMultiple: 0, // 高级多选题
            seniorJudge: 0, // 高级判断题
            seniorCTF: 0, // 高级CTF题
            seniorCompletion: 0, // 高级填空题
            seniorShort: 0, // 高级简答题
            seniorAWD: 0, // 高级awd题
            seniorBug: 0, // 高级bug题
            seniorCombinatorial: 0, // 高级组合题
            seniorOther: 0 // 高级其它题
          }
        ],
        scoreSingle: 0, // 单选题分数
        scoreMultiple: 0, // 多选题分数
        scoreJudge: 0, // 判断题分数
        scoreCTF: 0, // CTF题分数
        scoreCompletion: 0, // 填空题分数
        scoreShort: 0, // 简答题分数
        scoreCombinatorial: 0 // 组合题分数
      }
    }
  },
  computed: {
    // 理论题数量统计
    theoryScore() {
      const arr = {
        countPrimarySingle: this.theoryRuleVO.categoryVOList.map(item => { return item.primarySingle }).reduce((a, b) => this.isNaNs(a, b), 0),
        countMiddleSingle: this.theoryRuleVO.categoryVOList.map(item => { return item.middleSingle }).reduce((a, b) => this.isNaNs(a, b), 0),
        countSeniorSingle: this.theoryRuleVO.categoryVOList.map(item => { return item.seniorSingle }).reduce((a, b) => this.isNaNs(a, b), 0),
        countPrimaryMultiple: this.theoryRuleVO.categoryVOList.map(item => { return item.primaryMultiple }).reduce((a, b) => this.isNaNs(a, b), 0),
        countMiddleMultiple: this.theoryRuleVO.categoryVOList.map(item => { return item.middleMultiple }).reduce((a, b) => this.isNaNs(a, b), 0),
        countSeniorMultiple: this.theoryRuleVO.categoryVOList.map(item => { return item.seniorMultiple }).reduce((a, b) => this.isNaNs(a, b), 0),
        countPrimaryJudge: this.theoryRuleVO.categoryVOList.map(item => { return item.primaryJudge }).reduce((a, b) => this.isNaNs(a, b), 0),
        countMiddleJudge: this.theoryRuleVO.categoryVOList.map(item => { return item.middleJudge }).reduce((a, b) => this.isNaNs(a, b), 0),
        countSeniorJudge: this.theoryRuleVO.categoryVOList.map(item => { return item.seniorJudge }).reduce((a, b) => this.isNaNs(a, b), 0),
        countPrimaryCTF: this.theoryRuleVO.categoryVOList.map(item => { return item.primaryCTF }).reduce((a, b) => this.isNaNs(a, b), 0),
        countMiddleCTF: this.theoryRuleVO.categoryVOList.map(item => { return item.middleCTF }).reduce((a, b) => this.isNaNs(a, b), 0),
        countSeniorCTF: this.theoryRuleVO.categoryVOList.map(item => { return item.seniorCTF }).reduce((a, b) => this.isNaNs(a, b), 0),
        countPrimaryCompletion: this.theoryRuleVO.categoryVOList.map(item => { return item.primaryCompletion }).reduce((a, b) => this.isNaNs(a, b), 0),
        countMiddleCompletion: this.theoryRuleVO.categoryVOList.map(item => { return item.middleCompletion }).reduce((a, b) => this.isNaNs(a, b), 0),
        countSeniorCompletion: this.theoryRuleVO.categoryVOList.map(item => { return item.seniorCompletion }).reduce((a, b) => this.isNaNs(a, b), 0),
        countPrimaryShort: this.theoryRuleVO.categoryVOList.map(item => { return item.primaryShort }).reduce((a, b) => this.isNaNs(a, b), 0),
        countMiddleShort: this.theoryRuleVO.categoryVOList.map(item => { return item.middleShort }).reduce((a, b) => this.isNaNs(a, b), 0),
        countSeniorShort: this.theoryRuleVO.categoryVOList.map(item => { return item.seniorShort }).reduce((a, b) => this.isNaNs(a, b), 0)
      }
      return arr
    },
    // 靶机题数量统计
    targetScore() {
      const arr = {
        countPrimaryCTF: this.targetRuleVO.categoryVOList.map(item => { return item.primaryCTF }).reduce((a, b) => this.isNaNs(a, b), 0),
        countPrimaryAWD: this.targetRuleVO.categoryVOList.map(item => { return item.primaryAWD }).reduce((a, b) => this.isNaNs(a, b), 0),
        countPrimaryOther: this.targetRuleVO.categoryVOList.map(item => { return item.primaryOther }).reduce((a, b) => this.isNaNs(a, b), 0),
        countPrimaryBug: this.targetRuleVO.categoryVOList.map(item => { return item.primaryBug }).reduce((a, b) => this.isNaNs(a, b), 0),
        countMiddleCTF: this.targetRuleVO.categoryVOList.map(item => { return item.middleCTF }).reduce((a, b) => this.isNaNs(a, b), 0),
        countMiddleAWD: this.targetRuleVO.categoryVOList.map(item => { return item.middleAWD }).reduce((a, b) => this.isNaNs(a, b), 0),
        countMiddleOther: this.targetRuleVO.categoryVOList.map(item => { return item.middleOther }).reduce((a, b) => this.isNaNs(a, b), 0),
        countMiddleBug: this.targetRuleVO.categoryVOList.map(item => { return item.middleBug }).reduce((a, b) => this.isNaNs(a, b), 0),
        countSeniorCTF: this.targetRuleVO.categoryVOList.map(item => { return item.seniorCTF }).reduce((a, b) => this.isNaNs(a, b), 0),
        countSeniorAWD: this.targetRuleVO.categoryVOList.map(item => { return item.seniorAWD }).reduce((a, b) => this.isNaNs(a, b), 0),
        countSeniorOther: this.targetRuleVO.categoryVOList.map(item => { return item.seniorOther }).reduce((a, b) => this.isNaNs(a, b), 0),
        countSeniorBug: this.targetRuleVO.categoryVOList.map(item => { return item.seniorBug }).reduce((a, b) => this.isNaNs(a, b), 0)
      }
      return arr
    },
    // 仿真题数量统计
    simulationScore() {
      const arr = {
        countPrimarySingle: this.simulationRuleVO.categoryVOList.map(item => { return item.primarySingle }).reduce((a, b) => this.isNaNs(a, b), 0),
        countMiddleSingle: this.simulationRuleVO.categoryVOList.map(item => { return item.middleSingle }).reduce((a, b) => this.isNaNs(a, b), 0),
        countSeniorSingle: this.simulationRuleVO.categoryVOList.map(item => { return item.seniorSingle }).reduce((a, b) => this.isNaNs(a, b), 0),
        countPrimaryMultiple: this.simulationRuleVO.categoryVOList.map(item => { return item.primaryMultiple }).reduce((a, b) => this.isNaNs(a, b), 0),
        countMiddleMultiple: this.simulationRuleVO.categoryVOList.map(item => { return item.middleMultiple }).reduce((a, b) => this.isNaNs(a, b), 0),
        countSeniorMultiple: this.simulationRuleVO.categoryVOList.map(item => { return item.seniorMultiple }).reduce((a, b) => this.isNaNs(a, b), 0),
        countPrimaryJudge: this.simulationRuleVO.categoryVOList.map(item => { return item.primaryJudge }).reduce((a, b) => this.isNaNs(a, b), 0),
        countMiddleJudge: this.simulationRuleVO.categoryVOList.map(item => { return item.middleJudge }).reduce((a, b) => this.isNaNs(a, b), 0),
        countSeniorJudge: this.simulationRuleVO.categoryVOList.map(item => { return item.seniorJudge }).reduce((a, b) => this.isNaNs(a, b), 0),
        countPrimaryCTF: this.simulationRuleVO.categoryVOList.map(item => { return item.primaryCTF }).reduce((a, b) => this.isNaNs(a, b), 0),
        countMiddleCTF: this.simulationRuleVO.categoryVOList.map(item => { return item.middleCTF }).reduce((a, b) => this.isNaNs(a, b), 0),
        countSeniorCTF: this.simulationRuleVO.categoryVOList.map(item => { return item.seniorCTF }).reduce((a, b) => this.isNaNs(a, b), 0),
        countPrimaryCompletion: this.simulationRuleVO.categoryVOList.map(item => { return item.primaryCompletion }).reduce((a, b) => this.isNaNs(a, b), 0),
        countMiddleCompletion: this.simulationRuleVO.categoryVOList.map(item => { return item.middleCompletion }).reduce((a, b) => this.isNaNs(a, b), 0),
        countSeniorCompletion: this.simulationRuleVO.categoryVOList.map(item => { return item.seniorCompletion }).reduce((a, b) => this.isNaNs(a, b), 0),
        countPrimaryShort: this.simulationRuleVO.categoryVOList.map(item => { return item.primaryShort }).reduce((a, b) => this.isNaNs(a, b), 0),
        countMiddleShort: this.simulationRuleVO.categoryVOList.map(item => { return item.middleShort }).reduce((a, b) => this.isNaNs(a, b), 0),
        countSeniorShort: this.simulationRuleVO.categoryVOList.map(item => { return item.seniorShort }).reduce((a, b) => this.isNaNs(a, b), 0),
        countPrimaryCombinatorial: this.simulationRuleVO.categoryVOList.map(item => { return item.primaryCombinatorial }).reduce((a, b) => this.isNaNs(a, b), 0),
        countMiddleCombinatorial: this.simulationRuleVO.categoryVOList.map(item => { return item.middleCombinatorial }).reduce((a, b) => this.isNaNs(a, b), 0),
        countSeniorCombinatorial: this.simulationRuleVO.categoryVOList.map(item => { return item.seniorCombinatorial }).reduce((a, b) => this.isNaNs(a, b), 0)
      }
      return arr
    },
    total() {
      return this.theoryScore.countPrimarySingle + this.theoryScore.countMiddleSingle + this.theoryScore.countSeniorSingle +
      this.theoryScore.countPrimaryMultiple + this.theoryScore.countMiddleMultiple + this.theoryScore.countSeniorMultiple +
      this.theoryScore.countPrimaryJudge + this.theoryScore.countMiddleJudge + this.theoryScore.countSeniorJudge +
      this.theoryScore.countPrimaryCTF + this.theoryScore.countMiddleCTF + this.theoryScore.countSeniorCTF +
      this.theoryScore.countPrimaryCompletion + this.theoryScore.countMiddleCompletion + this.theoryScore.countSeniorCompletion +
      this.theoryScore.countPrimaryShort + this.theoryScore.countMiddleShort + this.theoryScore.countSeniorShort +
      this.targetScore.countPrimaryCTF + this.targetScore.countMiddleCTF + this.targetScore.countSeniorCTF +
      this.targetScore.countPrimaryAWD + this.targetScore.countMiddleAWD + this.targetScore.countSeniorAWD +
      this.targetScore.countPrimaryBug + this.targetScore.countMiddleBug + this.targetScore.countSeniorBug +
      this.targetScore.countPrimaryOther + this.targetScore.countMiddleOther + this.targetScore.countSeniorOther +
      this.simulationScore.countPrimarySingle + this.simulationScore.countMiddleSingle + this.simulationScore.countSeniorSingle +
      this.simulationScore.countPrimaryMultiple + this.simulationScore.countMiddleMultiple + this.simulationScore.countSeniorMultiple +
      this.simulationScore.countPrimaryJudge + this.simulationScore.countMiddleJudge + this.simulationScore.countSeniorJudge +
      this.simulationScore.countPrimaryCTF + this.simulationScore.countMiddleCTF + this.simulationScore.countSeniorCTF +
      this.simulationScore.countPrimaryCompletion + this.simulationScore.countMiddleCompletion + this.simulationScore.countSeniorCompletion +
      this.simulationScore.countPrimaryShort + this.theoryScore.countMiddleShort + this.simulationScore.countSeniorShort +
      this.simulationScore.countPrimaryCombinatorial + this.simulationScore.countMiddleCombinatorial + this.simulationScore.countSeniorCombinatorial
    },
    totalScore() {
      return ((this.theoryScore.countPrimarySingle + this.theoryScore.countMiddleSingle + this.theoryScore.countSeniorSingle) * (this.theoryRuleVO.scoreSingle ? this.theoryRuleVO.scoreSingle : 0)) +
      ((this.theoryScore.countPrimaryMultiple + this.theoryScore.countMiddleMultiple + this.theoryScore.countSeniorMultiple) * (this.theoryRuleVO.scoreMultiple ? this.theoryRuleVO.scoreMultiple : 0)) +
      ((this.theoryScore.countPrimaryJudge + this.theoryScore.countMiddleJudge + this.theoryScore.countSeniorJudge) * (this.theoryRuleVO.scoreJudge ? this.theoryRuleVO.scoreJudge : 0)) +
      ((this.theoryScore.countPrimaryCTF + this.theoryScore.countMiddleCTF + this.theoryScore.countSeniorCTF) * (this.theoryRuleVO.scoreCTF ? this.theoryRuleVO.scoreCTF : 0)) +
      ((this.theoryScore.countPrimaryCompletion + this.theoryScore.countMiddleCompletion + this.theoryScore.countSeniorCompletion) * (this.theoryRuleVO.scoreCompletion ? this.theoryRuleVO.scoreCompletion : 0)) +
      ((this.theoryScore.countPrimaryShort + this.theoryScore.countMiddleShort + this.theoryScore.countSeniorShort) * (this.theoryRuleVO.scoreShort ? this.theoryRuleVO.scoreShort : 0)) +
      ((this.targetScore.countPrimaryCTF + this.targetScore.countMiddleCTF + this.targetScore.countSeniorCTF) * (this.targetRuleVO.scoreCTF ? this.targetRuleVO.scoreCTF : 0)) +
      ((this.targetScore.countPrimaryAWD + this.targetScore.countMiddleAWD + this.targetScore.countSeniorAWD) * (this.targetRuleVO.scoreAWD ? this.targetRuleVO.scoreAWD : 0)) +
      ((this.targetScore.countPrimaryBug + this.targetScore.countMiddleBug + this.targetScore.countSeniorBug) * (this.targetRuleVO.scoreBug ? this.targetRuleVO.scoreBug : 0)) +
      ((this.targetScore.countPrimaryOther + this.targetScore.countMiddleOther + this.targetScore.countSeniorOther) * (this.targetRuleVO.scoreOther ? this.targetRuleVO.scoreOther : 0)) +
      ((this.simulationScore.countPrimarySingle + this.simulationScore.countMiddleSingle + this.simulationScore.countSeniorSingle) * (this.simulationRuleVO.scoreSingle ? this.simulationRuleVO.scoreSingle : 0)) +
      ((this.simulationScore.countPrimaryMultiple + this.simulationScore.countMiddleMultiple + this.simulationScore.countSeniorMultiple) * (this.simulationRuleVO.scoreMultiple ? this.simulationRuleVO.scoreMultiple : 0)) +
      ((this.simulationScore.countPrimaryJudge + this.simulationScore.countMiddleJudge + this.simulationScore.countSeniorJudge) * (this.simulationRuleVO.scoreJudge ? this.simulationRuleVO.scoreJudge : 0)) +
      ((this.simulationScore.countPrimaryCTF + this.simulationScore.countMiddleCTF + this.simulationScore.countSeniorCTF) * (this.simulationRuleVO.scoreCTF ? this.simulationRuleVO.scoreCTF : 0)) +
      ((this.simulationScore.countPrimaryCompletion + this.simulationScore.countMiddleCompletion + this.simulationScore.countSeniorCompletion) * (this.simulationRuleVO.scoreCompletion ? this.simulationRuleVO.scoreCompletion : 0)) +
      ((this.simulationScore.countPrimaryShort + this.simulationScore.countMiddleShort + this.simulationScore.countSeniorShort) * (this.simulationRuleVO.scoreShort ? this.simulationRuleVO.scoreShort : 0)) +
      ((this.simulationScore.countPrimaryCombinatorial + this.simulationScore.countMiddleCombinatorial + this.simulationScore.countSeniorCombinatorial) * (this.simulationRuleVO.scoreCombinatorial ? this.simulationRuleVO.scoreCombinatorial : 0))
    }
  },
  created() {
    if (this.data && Object.keys(this.data).length > 0) {
      this.theoryRuleVO = JSON.parse(this.data.dynamicPaperRules).theoryRuleVO
      this.targetRuleVO = JSON.parse(this.data.dynamicPaperRules).targetRuleVO
      this.simulationRuleVO = JSON.parse(this.data.dynamicPaperRules).simulationRuleVO
    }
    this.getCategoryFn()
  },
  methods: {
    isNaNs(a, b) {
      if (isNaN(b)) {
        return a
      }
      return a + b
    },
    getCategoryFn() {
      // 获取题库列表
      getQuestionCategory({ categoryTypes: [1], pageType: 0 }).then(res => {
        this.theoryQuestionCategoryList = res.data || []
      })
      getQuestionCategory({ categoryTypes: [2], pageType: 0 }).then(res => {
        this.targetQuestionCategoryList = res.data || []
      })
      getQuestionCategory({ categoryTypes: [3], pageType: 0 }).then(res => {
        this.simulationQuestionCategoryList = res.data || []
      })
      // 获取技能点
      getSkillPoint({ page: 1, limit: 9999 }).then((res) => {
        if (res.code === 0 || res.code === 200) {
          this.skillPointList = res.data.records
        }
      })
      getCategory({ categoryTypes: [1], pageType: 0 }).then(res => {
        this.theoryCategoryList = res.data.records
      })
      getCategory({ categoryTypes: [2], pageType: 0 }).then(res => {
        this.targetCategoryList = res.data.records
      })
      getCategory({ categoryTypes: [3], pageType: 0 }).then(res => {
        this.simulationCategoryList = res.data.records
      })
    }
  }
}
</script>

<style lang="scss" scoped>
._paper_container {
  display: flex;
  flex-direction: column;
  min-height: 100%;
  ._paper_header {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    position: relative;
    ._paper_search {
      position: absolute;
      display: flex;
      top: 6px;
      align-items: center;
      ._paper_search_1 {
        font-size: 16px;
        font-weight: 500;
        color: #333;
      }
    }
  }
  .border-b {
    border-bottom: 1px solid #d7d7d7;
  }
  .trends-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 5px 0;
    .el-form-item {
      margin: 0;
    }
    .category {
      display: flex;
      align-items: center;
      .el-form-item__content {
        display: flex;
      }
    }
    .score-form {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    > div {
      width: 10%;
      display: flex;
      justify-content: center;
    }
  }
}
.category-wrap {
  border: 1px solid #C0C4CC;
  border-radius: 5px;
  padding: 10px 15px;
}
.select-wrap {
  height: 36px;
  display: flex;
  justify-content: space-between;
  .left {
    display: flex;
    justify-content: flex-start;
    .select-title {
      height: 36px;
      line-height: 40px;
      font-size: 15px;
      font-weight: bold;
    }
    ::v-deep .el-form-item {
      .el-form-item__label {
        color: #000;
      }
      .el-input__inner {
        background-color: #fff;
        border: none;
      }
      .el-input__suffix {
        display: none;
      }
    }
  }
}
.card-bg {
  border-radius: 4px;
  padding: 15px;
  background-color: #FFFFFF;
}
</style>
