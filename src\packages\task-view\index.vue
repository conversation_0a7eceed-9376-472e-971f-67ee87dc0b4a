<template>
  <div class="header-task">
    <el-drawer
      :visible.sync="drawerShow"
      :size="'640px'"
      :before-close="drawerClose"
      title="请求详情"
      append-to-body
      direction="rtl"
    >
      <transition name="el-fade-in-linear">
        <api-result-view v-if="drawerShow" :data="drawerResultView" />
      </transition>
    </el-drawer>
  </div>
</template>
<style lang="less">
.header-task {
  float: right;
}
.task-notice {
  .el-notification__group {
    flex: 1;
    overflow: hidden;
  }
  .el-notification__title {
    white-space:nowrap;
    overflow:hidden;
    text-overflow:ellipsis;
    width: 100%;
  }
}
</style>
<script>
import apiResultView from './task-view-api-result.vue'
import { v4 as uuidv4 } from 'uuid'
import { mapState } from 'vuex'
export default {
  components: {
    apiResultView
  },
  data() {
    return {
      drawerResultView: null, // 侧拉框结果预览缓存
      drawerShow: false, // 侧拉开关
      closeTime: 3000, // notice 成功后关闭事件
      taskObj: {} // 任务对象
    }
  },
  computed: {
    ...mapState('socketListener', [
      'globalSocket'
    ]),
    // 运行中的任务
    'activeTask': function() {
      const outData = {}
      for (const taskKey in this.taskObj) {
        if (this.taskObj[taskKey]['response']['taskEnd'] === false) {
          outData[taskKey] = this.taskObj[taskKey]
        }
      }
      return outData
    }
  },
  watch: {
    // websocket异步监听
    'globalSocket': function(nval, oval) {
      this.socketHandle(nval, oval)
    }
  },
  beforeDestroy() {
    this.$bus.$off('SHOW_NOTICE')
    this.$bus.$off('SINGLE_TASK_API')
    this.$bus.$off('BAT_TASK_API')
  },
  created() {
    this.$bus.$on('SHOW_NOTICE', ({ type = 'info', title, message }) => {
      const titleType = {
        'success': '成功',
        'warning': '警告',
        'error': '失败',
        'info': '提示'
      }
      let viewMsg = null
      if (type === 'error') {
        viewMsg = `<p class="el-link el-link--danger">${typeof message === 'string' ? message || '捕获到未知异常！' : '捕获到未知异常！'}</p>`
      } else if (type === 'success') {
        viewMsg = `<p class="el-link el-link--success">${message}</p>`
      } else if (type === 'warning') {
        viewMsg = `<p class="el-link el-link--warning">${message}</p>`
      } else {
        viewMsg = `<p class="el-link el-link--primary">${message}</p>`
      }
      this.$notify({
        type: type,
        title: title || titleType[type],
        dangerouslyUseHTMLString: true,
        message: viewMsg,
        duration: type === 'error' ? 0 : this.closeTime
      })
    })
    /** SINGLE_TASK_API事件【单一请求】
     * @param taskName            string      任务名称
     * @param resource            object      资源对象
     * @param apiObj              object      API请求对象
     * @param data                object      post数据
     * @param sucsessCallback     function    成功回调
     * @param errorCallback       function    错误回调
     * @param key                 object      默认名称/ID key配置，任务会使用该key查找资源对应名称与ID
     * @param count               number      单接口批量创建的数量
     */
    this.$bus.$on('SINGLE_TASK_API', ({ taskName, contentType, resource, apiObj, data, sucsessCallback, errorCallback, keys = { 'name': 'name', 'id': 'id' }, count }) => {
      this.taskAdd({ type: 'default', contentType, taskName, resource, apiObj, data, sucsessCallback, errorCallback, keys, count })
    })
    /** BAT_TASK_API事件【单一请求】
     * @param taskName            string                    任务名称
     * @param resource            string                    资源列表
     * @param apiObj              object                    API请求对象
     * @param data                object                    post数据
     * @param sucsessCallback     function                  成功回调
     * @param errorCallback       function                  错误回调
     * @param key                 object                    默认名称/ID key配置，任务会使用该key查找资源对应名称与ID
     * @param batParam            function(resourceItem)    批量操作中用于返回每项携带的参数，
     */
    this.$bus.$on('BAT_TASK_API', ({ taskName, resource, apiObj, data, sucsessCallback, errorCallback, keys, batParam }) => {
      this.taskAdd({ type: 'bat', taskName, resource, apiObj, data, sucsessCallback, errorCallback, keys, batParam })
    })
  },
  methods: {
    /** 展示提示
     * @param type              string         添加类型 可选参数default[默认] | bat[批量] | sync[websocket异步]
     * @param taskName          string         任务名称
     * @param msg               string         任务消息
     */
    'taskShowMsg': function({ type = 'success', title, msg }) {
      this.$notify({
        duration: 3,
        type: 'loading',
        customClass: 'task-notice',
        title: title,
        dangerouslyUseHTMLString: true,
        message: '<p class="el-link el-link--primary">进行中..</p>'
      })
    },
    /** 添加任务
     * @param type              string                     添加类型 可选参数default[默认] | bat[批量] | sync[websocket异步]
     * @param contentType       string                     请求格式 default、formData
     * @param taskName          string                     任务名称
     * @param resource          string/array               type[default/sync]资源名称 string, type[bat]资源对象列表 Array
     * @param apiObj            object                     API请求对象
     * @param data              object                     post数据
     * @param sucsessCallback   function                   成功回调
     * @param errorCallback     function                   错误回调
     * @param keys              object                     批量数据中对应key 默认key为名称为name, APIID参数为id，如需自定义可传值
     * @param batParam          function(resourceItem)     批量操作中用于返回每项携带的参数，
     * @param count             number                     单接口批量创建的数量
     */
    'taskAdd': function({ type = 'default', contentType = 'default', taskName, resource, apiObj, data = {}, sucsessCallback, errorCallback, keys = { 'name': 'name', 'id': 'id' }, batParam, count }) {
      // 批量处理结果
      const response = {
        title: taskName, // 任务名称
        count: count, // 批量数量
        type: type, // 类型 default:单一 bat:批量
        contentType: contentType, // 请求格式 default、formData
        taskEnd: false, // 是否已完成
        complete: 0, // 批量完成数量
        error: 0, // 批量错误数量
        result: {}, // 批量处理结果 result: { '资源ID': { end: false, message: '错误' } }
        cache: {} // socket消息缓存，存储已完成资源ID { '资源ID': { 'msg':'******' }}
      }
      // 任务ID 前端自动生成UUID
      const taskId = uuidv4()
      // 生成通知element obj
      const taskEle = this.$notify({
        duration: 0,
        type: 'loading',
        customClass: 'task-notice',
        title: taskName,
        dangerouslyUseHTMLString: true,
        message: '<p class="el-link el-link--primary">进行中..</p>',
        onClose: () => {
          this.taskObj[taskId]['response']['taskEnd'] = true
          this.checkTaskFinish(this.taskObj[taskId], '关闭Notice')
        }
      })
      // 记录通知对象
      this.$set(this.taskObj, taskId, { 'response': response, 'element': taskEle, 'sucsessCallback': sucsessCallback, 'errorCallback': errorCallback })
      if (type === 'default') {
        if (response.count && response.count > 1) {
          this.taskBatMsg(taskEle, response.count, response['complete'], response['error'])
        }
        // 单API请求 ===========================================================
        if (contentType === 'formData') {
          const postData = new FormData()
          postData.append('topologyId', data.id)
          apiObj(postData)
            .then(res => {
              if (res['status'] === 200) {
                // 单一同步任务 状态码为200
                this.taskObj[taskId]['response']['taskEnd'] = true
                delete this.taskObj[taskId]['response']['cache']
                this.taskSuccess(taskEle, count)
                sucsessCallback(res)
              } else {
                // 单一异步任务 状态码为202
                const resData = res['data']
                if (resData) {
                  if (Array.isArray(resData)) {
                    // 单接口批量监听（返回data为Array）
                    resData.forEach((resDataItem) => {
                      this.$set(this.taskObj[taskId]['response']['result'], resDataItem[keys['id']], { name: resDataItem[keys['name']], end: false, message: null })
                    })
                    // this.checkTaskFinish(this.taskObj[taskId], '单一API异步')
                    // this.taskBatMsg(taskEle, resData.length, this.taskObj[taskId]['response']['complete'], this.taskObj[taskId]['response']['error'])
                  } else {
                    if (resData.hasOwnProperty('id') && resData.hasOwnProperty('name')) {
                      // 单接口单个资源监听（返回data为Object）
                      this.$set(this.taskObj[taskId]['response']['result'], resData[keys['id']], { name: resData[keys['name']], end: false, message: null })
                    } else {
                      // 返回data无数据，使用默认resource参数中的id/name
                      this.$set(this.taskObj[taskId]['response']['result'], resource[keys['id']], { name: resource[keys['name']], end: false, message: null })
                    }
                  }
                } else {
                  // 单接口单个监听
                  this.$set(this.taskObj[taskId]['response']['result'], resource[keys['id']], { name: resource[keys['name']], end: false, message: null })
                }
                this.checkTaskFinish(this.taskObj[taskId], '单一API异步')
              }
            })
            .catch((error) => {
              errorCallback(error)
              this.taskObj[taskId]['response']['taskEnd'] = true
              delete this.taskObj[taskId]['response']['cache']
              if (error.message === 'Network Error') {
                response['result']['message'] = '网络错误'
                this.taskError(taskEle, '<p class="el-link el-link--primary">网络错误</p>')
                return
              }
              const errorMeg = error.response.data.message
              if (typeof errorMeg !== 'object') {
                this.taskError(taskEle, errorMeg ? '<p class="el-link el-link--danger">' + errorMeg + '</p>' : '<p class="el-link el-link--primary">失败</p>')
                response['result']['message'] = errorMeg
              } else {
                const pre = '<p class="el-link el-link--danger">'
                const next = '</p>'
                // const msg = pre + '未知错误' + next
                let msg = ''
                for (const key in errorMeg) {
                  msg += pre + errorMeg[key] + next
                }
                this.taskError(taskEle, msg)
                response['result']['message'] = msg
              }
            })
        } else {
          apiObj(...Object.values(data))
            .then(res => {
              if (res['status'] === 200) {
                // 单一同步任务 状态码为200
                this.taskObj[taskId]['response']['taskEnd'] = true
                delete this.taskObj[taskId]['response']['cache']
                this.taskSuccess(taskEle, count)
                sucsessCallback(res)
              } else {
                // 单一异步任务 状态码为202
                const resData = res['data']
                if (resData) {
                  if (Array.isArray(resData)) {
                    // 单接口批量监听（返回data为Array）
                    resData.forEach((resDataItem) => {
                      this.$set(this.taskObj[taskId]['response']['result'], resDataItem[keys['id']], { name: resDataItem[keys['name']], end: false, message: null })
                    })
                    // this.checkTaskFinish(this.taskObj[taskId], '单一API异步')
                    // this.taskBatMsg(taskEle, resData.length, this.taskObj[taskId]['response']['complete'], this.taskObj[taskId]['response']['error'])
                  } else {
                    if (resData.hasOwnProperty('id') && resData.hasOwnProperty('name')) {
                      // 单接口单个资源监听（返回data为Object）
                      this.$set(this.taskObj[taskId]['response']['result'], resData[keys['id']], { name: resData[keys['name']], end: false, message: null })
                    } else {
                      // 返回data无数据，使用默认resource参数中的id/name
                      this.$set(this.taskObj[taskId]['response']['result'], resource[keys['id']], { name: resource[keys['name']], end: false, message: null })
                    }
                  }
                } else {
                  // 单接口单个监听
                  this.$set(this.taskObj[taskId]['response']['result'], resource[keys['id']], { name: resource[keys['name']], end: false, message: null })
                }
                this.checkTaskFinish(this.taskObj[taskId], '单一API异步')
              }
            })
            .catch((error) => {
              errorCallback(error)
              this.taskObj[taskId]['response']['taskEnd'] = true
              delete this.taskObj[taskId]['response']['cache']
              if (error.message === 'Network Error') {
                response['result']['message'] = '网络错误'
                this.taskError(taskEle, '<p class="el-link el-link--primary">网络错误</p>')
                return
              }
              const errorMeg = error.response.data.message
              if (typeof errorMeg !== 'object') {
                this.taskError(taskEle, errorMeg ? '<p class="el-link el-link--danger">' + errorMeg + '</p>' : '<p class="el-link el-link--primary">失败</p>')
                response['result']['message'] = errorMeg
              } else {
                const pre = '<p class="el-link el-link--danger">'
                const next = '</p>'
                // const msg = pre + '未知错误' + next
                let msg = ''
                for (const key in errorMeg) {
                  msg += pre + errorMeg[key] + next
                }
                this.taskError(taskEle, msg)
                response['result']['message'] = msg
              }
            })
        }
      } else if (type === 'bat') {
        // 批量API请求 ===========================================================
        if (resource.length > 1) {
          this.taskBatMsg(taskEle, resource.length, response['complete'], response['error'])
        }
        Promise.all(
          resource.map((item, index) => {
            return new Promise((resolve, reject) => {
              let batParamData = null
              if (batParam) batParamData = batParam(item)
              this.$set(this.taskObj[taskId]['response']['result'], item[keys['id']], { name: item[keys['name']], end: false, message: null })
              apiObj(item[keys['id']], ...Object.values(data), batParamData)
                .then(res => {
                  if (res['status'] === 200) {
                    // 单一同步任务 状态码为200
                    this.taskObj[taskId]['response']['complete']++
                    this.taskObj[taskId]['response']['result'][item[keys['id']]]['end'] = true
                    this.taskObj[taskId]['response']['result'][item[keys['id']]]['message'] = '成功'
                    this.taskBatMsg(taskEle, resource.length, this.taskObj[taskId]['response']['complete'], this.taskObj[taskId]['response']['error'])
                  } else {
                    // 批量异步任务 状态码为202
                    this.checkTaskFinish(this.taskObj[taskId], '批量异步')
                  }
                  resolve()
                })
                .catch((error) => {
                  this.taskObj[taskId]['response']['result'][item[keys['id']]]['end'] = true
                  this.taskObj[taskId]['response']['result'][item[keys['id']]]['message'] = error.message === 'Network Error' ? '网络错误' : error.response.data.message
                  this.taskObj[taskId]['response']['error']++
                  this.checkTaskFinish(this.taskObj[taskId], '批量API错误')
                  resolve()
                })
            })
          })
        ).then(() => {
          this.checkTaskFinish(this.taskObj[taskId], '批量API完成汇总')
        })
      }
    },
    // 批量任务消息显示
    'taskBatMsg': function(task, total, completeNum, errorNum) {
      task.message = `<p class="el-link el-link--primary">已完成：${completeNum + errorNum} / ${total}</p>`
    },
    // 任务完成
    'taskSuccess': function(task, count) {
      task.message = `<p class="el-link el-link--success">成功 ${count ? (count + ' / ' + count) : ''}</p>`
      task.type = 'success'
      setTimeout(() => {
        task.close()
      }, this.closeTime)
    },
    // 关闭侧拉
    'drawerClose': function() {
      this.drawerShow = false
      this.drawerResultView = null
    },
    // 任务失败
    'taskError': function(task, error) {
      task.message = error
      task.type = 'error'
      task.onClick = () => {
        // 错误信息点击触发（可添加错误报告链接）
        // this.drawerShow = true
        // task.close()
      }
    },
    // 批量任务失败
    'batTaskEnd': function(task) {
      const batComplete = task['response']['complete']
      const batError = task['response']['error']
      const element = task['element']
      if (batError > 0) {
        element.message = `<p class="el-link el-link--danger">失败 ${batError}</p> <p style="margin-left: 10px;" class="el-link el-link--success">成功 ${batComplete}</p><p style="margin-left: 40px;" class="el-link el-link--primary">[ 查看详细 ]</p>`
        element.type = 'error'
        element.onClick = () => {
          // 错误信息点击触发（可添加错误报告链接）
          this.drawerShow = true
          this.drawerResultView = task
          // element.close()
        }
      } else {
        element.message = `<p class="el-link el-link--success">成功 ${batComplete} / ${batComplete}</p>`
        element.type = 'success'
        setTimeout(() => {
          element.close()
        }, this.closeTime)
      }
    },
    // socket监听
    'socketHandle': function(nval, oval) {
      // 批量处理结果 result: { '资源ID': { end: false, message: '错误' } }
      const payload = nval.payload
      if (payload.hasOwnProperty('websocket_type') && payload['websocket_type'] === 'message') {
        // console.log(payload['message_level'], payload['name'], payload['message'])
        this.$bus.$emit('SHOW_NOTICE', {
          type: payload['message_level'],
          message: payload['message']
        })
      }
      const socketEnd = payload.hasOwnProperty('task_status') ? payload['task_status'] : null
      const socketMsg = payload.hasOwnProperty('message') ? payload['message'] : null
      const socketType = nval['message_type']
      if (!socketEnd) return
      const resourceId = payload['resource_id']
      // console.log(socketEnd, socketMsg, resourceId)
      for (const activeTaskKey in this.activeTask) {
        if (socketEnd === 'error') {
          this.$set(this.taskObj[activeTaskKey]['response']['cache'], resourceId, { 'error': true, 'message': socketMsg || '错误', 'type': socketType })
        } else {
          this.$set(this.taskObj[activeTaskKey]['response']['cache'], resourceId, { 'error': false, 'message': '成功', 'type': socketType })
        }
        this.checkTaskFinish(this.activeTask[activeTaskKey], 'Socket')
      }
    },
    // 关闭窗口
    'closeNotice': function(taskItem) {
      delete taskItem['response']['cache']
      delete taskItem['element']
      delete taskItem['errorCallback']
      delete taskItem['sucsessCallback']
    },
    // 检查任务是否全部完成
    'checkTaskFinish': function(taskItem, type) {
      if (taskItem['response']['taskEnd']) {
        this.closeNotice(taskItem)
        return
      }
      // 任务项目列表
      const taskList = taskItem['response']['result']
      // 任务总数
      const taskTotal = Object.keys(taskList).length
      // 完成数量
      let endNum = 0
      // 错误数量
      let errorNum = 0
      // socket cache中寻找对应结果
      for (const taskId in taskList) {
        if (taskItem['response']['cache'].hasOwnProperty(taskId)) {
          if (taskItem['response']['cache'][taskId]['error']) {
            taskItem['response']['result'][taskId]['message'] = taskItem['response']['cache'][taskId]['message']
          } else {
            taskItem['response']['result'][taskId]['message'] = '成功'
          }
          taskItem['response']['result'][taskId]['end'] = true
        }
      }
      // 同步统计数量
      for (const key in taskList) {
        if (taskList[key]['end']) endNum++
        if (taskList[key]['end'] && taskList[key]['message'] !== '成功') errorNum++
      }
      taskItem['response']['error'] = errorNum
      taskItem['response']['complete'] = endNum - errorNum
      // 批量任务监听更新提示已完成数量
      if (taskTotal > 1) {
        this.taskBatMsg(taskItem['element'], taskTotal, (endNum - errorNum), errorNum)
      }
      // console.log(type, '总数：' + taskTotal, '已完成：' + endNum, '成功：' + (endNum - errorNum), '失败：' + errorNum, '数据:', taskItem['response'])
      // 任务完成
      if (taskTotal !== 0 && taskTotal === endNum) {
        // 删除任务socket缓存
        delete taskItem['response']['cache']
        // 设置状态完成 （activeTask中会响应移除）
        taskItem['response']['taskEnd'] = true
        if (taskTotal === 1) {
          // 单一任务
          if (errorNum > 0) {
            const errorMeg = taskItem['response']['result'][Object.keys(taskItem['response']['result'])[0]]['message']
            this.taskError(taskItem['element'], '<p class="el-link el-link--danger">' + errorMeg + '</p>')
            taskItem.errorCallback(taskList)
          } else {
            this.taskSuccess(taskItem['element'])
            taskItem.sucsessCallback(taskList)
          }
        } else {
          // 批量任务
          if (errorNum > 0) {
            this.batTaskEnd(taskItem)
            taskItem.errorCallback(taskList)
          } else {
            this.batTaskEnd(taskItem)
            // this.taskSuccess(taskItem['element'])
            taskItem.sucsessCallback(taskList)
          }
        }
      }
    }
  }
}
</script>
