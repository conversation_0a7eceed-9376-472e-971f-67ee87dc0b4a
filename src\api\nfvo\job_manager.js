import request from '@/packages/request'
const _thisApi = window.NFVO_CONFIG.nfvo_api
const headers = { 'Content-Type': 'application/json', 'x-access-module': 'ADMIN' }

export function getList(params) {
  return request({
    url: _thisApi + '/job',
    method: 'get',
    params,
    headers
  })
}

export function getItem(id) {
  return request({
    url: _thisApi + '/job/' + id,
    method: 'get',
    headers
  })
}
