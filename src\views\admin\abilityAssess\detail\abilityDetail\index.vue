<template>
  <page-table
    ref="table"
    :default-selected-arr="defaultSelectedArr"
    :task-instance-v-o-list="taskInstanceVOList"
    @refresh="refresh"
    @link-event="linkEvent"
    @on-select="tabelSelect"
    @on-current="tabelCurrent"
  >
    <action-menu
      slot="action"
      :module-name="moduleName"
      :select-item="selectItem"
      @call="actionHandler"
    />
  </page-table>
</template>

<script>
import moduleConf from './config'
import pageTable from './table/index'
import actionMenu from './action/index'
export default {
  components: {
    pageTable,
    actionMenu
  },
  props: {
    id: String,
    data: {
      type: Object
    }
  },
  data() {
    return {
      moduleName: moduleConf.name,
      selectItem: [],
      defaultSelectedArr: [], // 默认选中的列表数据id集合
      options: [],
      value: [],
      initInstanceRoles: [],
      taskInstanceVOList: []
    }
  },
  mounted() {
  },
  methods: {
    // 列表点击
    linkEvent: function({ name, row, params }) {
      this.$router.push({ name: name, params: params })
    },
    // 返回已选
    tabelSelect: function(data) {
      this.selectItem = data
    },
    // 返回单选
    tabelCurrent: function(row) {
      this.selectItem = [row]
    },
    // action menu 事件
    actionHandler: function(type, data) {
      switch (type) {
        case 'refresh':
          this.$refs['table'].getList()
      }
    },
    refresh: function() {}
  }
}
</script>
