import questionConf from '@/views/admin/accumulate/questionBank/config.js' // 题目配置

// 题目类别
const questionCategoryArr = [
  { label: '理论', value: '1', moduleName: 'theory' },
  { label: '靶机', value: '2', moduleName: 'targetDevice' },
  { label: '仿真', value: '3', moduleName: 'simulation' }
]

export default {
  ...questionConf,
  name: 'questionBankDeatail',
  questionCategoryArr: questionCategoryArr,
  questionCategoryObj: questionCategoryArr.reduce((acc, prev) => {
    acc[prev.value] = prev
    return acc
  }, {})
}
