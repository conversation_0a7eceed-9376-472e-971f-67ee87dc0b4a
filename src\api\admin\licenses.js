import request from '@/utils/request'
const headers = { 'Content-Type': 'application/json;charset=UTF-8' }

// 获取授权信息
export function getLicenseInfo(params) {
  return request({
    url: 'license/getLicenseInfo',
    method: 'get',
    headers,
    params
  })
}

// 下载key
export function getLicenseKey(params) {
  return request({
    url: 'license/getLicenseKey',
    method: 'get',
    headers,
    params
  })
}

// 更新授权
export function uploadLicense(data) {
  return request({
    url: 'license/uploadLicense',
    method: 'post',
    data,
    headers
  })
}

// 授权记录
export function licenseHistory(data) {
  return request({
    url: 'license/queryList',
    method: 'post',
    data,
    headers
  })
}
