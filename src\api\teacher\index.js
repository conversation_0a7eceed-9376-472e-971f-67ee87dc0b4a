import request from '@/utils/request'
const headers = { 'Content-Type': 'application/json;charset=UTF-8' }

// 课程内容随堂联系修改答题时间
export function updateAnswerTimeById(data) {
  return request({
    url: '/training/content/updateAnswerTimeById',
    method: 'POST',
    headers,
    data
  })
}

/**
 * 获取课程安排
 *
 */
export function pjtTeacherSchedulingApi(data) {
  return request({
    url: '/training/pjtTeacherScheduling/searchList' + `?userId=${data.userId}&startTime=${data.startTime}&endTime=${data.endTime}&classCode=${data.classCode}`,
    method: 'get',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 题库上传错误列表导出
 * @param {*} data
 */
export function exportErrorQuestion(data) {
  return request({
    url: '/training/pjtExamQuestion/exportErrorQuestionTemplate',
    method: 'post',
    data: data,
    responseType: 'blob',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    timeout: 60000
  })
}

/**
 * 增加排课中的内容下拉
 *
 */
export function searchMainCourseApi(data) {
  return request({
    url: '/training/pjtTeacherScheduling/searchMainCourse',
    method: 'post',
    data: data
  })
}
/**
 * 增加排课中的内容下拉
 *
 */
export function searchMainCourseThreeApi(data) {
  return request({
    url: '/training/pjtTeacherScheduling/searchMainCourseThree',
    method: 'post',
    data: data
  })
}

/**
 * 增加排课的班级下拉
 *
 */
export function searchMajorApi(data) {
  return request({
    url: '/training/pjtTeacherScheduling/searchMajor',
    method: 'post',
    data: data
  })
}

/**
 * 课程表新增排班计划
 *
 */
export function insertTeacherSchedulingApi(data) {
  return request({
    url: '/training/pjtTeacherScheduling/insertTeacherScheduling',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 概览人员管理统计
 *
 */
export function getPersonnelManagement(data) {
  return request({
    url: `/training/overview/getPersonnelManagement`,
    method: 'get'
  })
}

/**
 * 概览近一年内班级项目成绩趋势
 *
 */
export function getSearchTaskScore(data) {
  return request({
    url: `/training/overview/searchTaskScore`,
    method: 'get'
  })
}

/**
 * 概览近一年内班级考试成绩趋势
 *
 */
export function getSearchTestScore(data) {
  return request({
    url: `/training/overview/searchTestScore`,
    method: 'get'
  })
}

/**
 * 概览自学课程top5
 *
 */
export function getStudentSelectiveTop5(data) {
  return request({
    url: `/training/overview/studentSelectiveTop5`,
    method: 'get'
  })
}

/**
 * 概览热门排课课程top5
 *
 */
export function getSchedulingCourseTop5(data) {
  return request({
    url: `/training/overview/schedulingCourseTop5`,
    method: 'get'
  })
}

/**
 * 概览获取配置页面布局
 *
 */
export function layoutInfo(data) {
  return request({
    url: `/training/overview/config/layout/info?roleId=${data}`,
    method: 'get'
  })
}

/**
 * 概览配置页面布局
 *
 */
export function layoutSave(data) {
  return request({
    url: '/training/overview/config/layout/save',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 试卷下拉菜单
 *
 */
export function paperListAPI(data) {
  return request({
    url: '/training/pjtExam/searchExamPaper',
    method: 'post',
    data
  })
}

/**
 * 基础教学查询
 *
 */
export function searchBasicsTeachingApi(data) {
  return request({
    url: '/training/pjtBasicsTeaching/searchBasicsTeaching',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 基础教学-分类
 *
 */
export function searchCategoryTypeApi(data) {
  return request({
    url: '/training/pjtCategory/searchCategoryType',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 基础教学-课程列表类型下拉
 *
 */
export function searchPackageTypeApi(data) {
  return request({
    url: '/training/pjtTeachingPlanUnitAttribute/searchPackageType',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 基础教学的课程列表查询
 *
 */
export function searchBasicsPackageApi(data) {
  return request({
    url: '/training/pjtTeachingCurriculum/searchBasicsCurriculum',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 基础教学-增加排课中的内容下拉
 *
 */
export function searchBasicsMainCourseApi(data) {
  return request({
    url: '/training/pjtTeacherScheduling/searchBasicsMainCourse',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 基础教学-新增批量排课
 *
 */
export function insertBasicsTeacherSchedulingApi(data) {
  return request({
    url: '/training/pjtTeacherScheduling/insertBasicsTeacherScheduling',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 教学方案上传url
 *
 */
export function upTeachingCoverApi(data) {
  return request({
    url: '/training/pjtTeachingPlan/upTeachingCover',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}


/**
 * 基础教学的单元查询
 *
 */
export function searchPjtBasicsTeachingUnitApi(data) {
  return request({
    url: '/training/pjtBasicsTeachingUnit/searchPjtBasicsTeachingUnit',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}


/**
 * 课程分布
 *
 */
export function searchSchedulingNumApi(data) {
  return request({
    url: '/training/pjtTeacherScheduling/searchSchedulingNum',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
/**
 * 基础成绩趋势
 *
 */
export function searchBasicScoreApi(data) {
  return request({
    url: '/training/pjtTeacherScheduling/searchBasicScore',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
/**
 * 考试成绩趋势
 *
 */
export function searchTestScoreApi(data) {
  return request({
    url: '/training/pjtTeacherScheduling/searchTestScore',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 获取课程统计
 *
 */
export function pjtTeachingPlanApi(data) {
  return request({
    url: '/training/pjtTeachingPlan/searchcourseStatistics',
    method: 'post',
    data: data
  })
}
/**
 * 课程中心
 *
 */
export function queryBasicsApi(data) {
  return request({
    url: 'training/basicsTeaching/queryBasics',
    method: 'post',
    data: data
  })
}

/**
 * 首页课程数据
 *
 */
export function questCurriculumApi(data) {
  return request({
    url: `training/pjtMyBasicsTeachingRelation/questCurriculum?roleType=${data.roleType}`,
    method: 'get',
    data: data
  })
}
/**
 * 班级管理-班级下拉
 *
 */
export function searchMajorClassApi(data) {
  return request({
    url: 'training/pjtMajorClass/searchMajorClass',
    method: 'post',
    data: data
  })
}
/**
 * 新增专业或班级
 *
 */
export function insertMajorClassApi(data) {
  return request({
    url: 'training/pjtMajorClass/insertMajorClass',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
/**
 * 编辑专业或班级
 *
 */
export function updateMajorClassApi(data) {
  return request({
    url: 'training/pjtMajorClass/updateMajorClass',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
/**
 * 删除专业或班级
 *
 */
export function deletedMajorClassApi(data) {
  return request({
    url: 'training/pjtMajorClass/deletedMajorClass',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
/**
 * 新增班级专业的下拉
 *
 */
export function searchMajorsApi(data) {
  return request({
    url: 'training/pjtMajorClass/searchMajors',
    method: 'post',
    data: data
  })
}

/**
 * 教学方案分页列表
 *
 */
export function backSearchTeachingPlanApi(data) {
  return request({
    url: 'training/pjtTeachingPlan/backSearchTeachingPlan',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 教学方案列表
 *
 */
export function lessonPlanQuery(data) {
  return request({
    url: 'training/lessonPlan/query',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 创建教学方案
 *
 */
export function lessonPlanSave(data) {
  return request({
    url: 'training/lessonPlan/save' + `?source=${data.source}`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 删除教学方案
 *
 */
export function lessonPlanDelete(data) {
  return request({
    url: `training/lessonPlan/delete` + `?source=${data.source}`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    }
  })
}

/**
 * 编辑教学方案
 *
 */
export function lessonPlanUpdate(data) {
  return request({
    url: `training/lessonPlan/update`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 创建体系
 *
 */
export function lessonPlanHierarchySave(data) {
  return request({
    url: `training/lessonPlan/hierarchy/save`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    }
  })
}

/**
 * 教案详情
 *
 */
export function lessonPlanDetailQuery(data) {
  return request({
    url: `training/lessonPlan/detail/query`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    }
  })
}

/**
 * 教案详情
 *
 */
export function lessonPlanByUserIdCourse(data) {
  return request({
    url: `training/lessonPlan/lessonPlanByUserIdCourse/query`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    }
  })
}

/**
 * 编辑体系
 *
 */
export function lessonPlanHierarchyUpdate(data) {
  return request({
    url: `training/lessonPlan/hierarchy/update`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    }
  })
}

/**
 * 编辑体系
 *
 */
export function lessonPlanHierarchyDelete(data) {
  return request({
    url: `training/lessonPlan/hierarchy/delete`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    }
  })
}

/**
 * 添加课程
 *
 */
export function lessonPlanHierarchyCourseSave(data) {
  return request({
    url: `training/lessonPlan/hierarchyCourse/save`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    }
  })
}

/**
 * 删除课程
 *
 */
export function lessonPlanHierarchyCourseDelete(data) {
  return request({
    url: `training/lessonPlan/hierarchyCourse/delete`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    }
  })
}

/**
 * 查询教案详情
 *
 */
export function courseQuery(data) {
  return request({
    url: `training/course/query`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}


export function saveContentJoinCourse(data) {
  return request({
    url: `/training/course/contentJoinCourse/save`,
    method: 'post',
    data: Object.assign({ flag: '1' }, data),
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}
/**
 * 修改教学方案私有或公开状态
 *
 */
export function backUpdateTeachingPlanStateApi(data) {
  return request({
    url: 'training/pjtTeachingPlan/backUpdateTeachingPlanState',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 删除教学方案接口
 *
 */
export function deleteTeachingPlanApi(data) {
  return request({
    url: `training/pjtTeachingPlan/deleteTeachingPlan`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 查询新增教学方案继承的下拉菜单
 *
 */
export function backSearchTeachingPlanVoApi(data) {
  return request({
    url: '/training/pjtTeachingPlan/backSearchTeachingPlanVo',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 新增教学方案
 *
 */
export function insertTeachingPlanApi(data) {
  return request({
    url: '/training/pjtTeachingPlan/insertTeachingPlan',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 教学方案修改
 *
 */
export function backUpdatePlanApi(data) {
  return request({
    url: '/training/pjtTeachingPlan/backUpdatePlan',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 教学方案课程分页查询
 *
 */
export function searchPjtTeachingPlanUnitApi(data) {
  return request({
    url: '/training/pjtTeachingPlanUnit/searchPjtTeachingPlanUnit',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 查询单元属性下拉列表
 *
 */
export function searchTeachingPlanUnitAttributeApi(data) {
  return request({
    url: '/training/pjtTeachingPlanUnitAttribute/searchTeachingPlanUnitAttribute',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 查询学期列表
 *
 */
export function searchWeekTypeApi(data) {
  return request({
    url: '/training/pjtTeachingPlanUnitAttribute/searchWeekType',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 教学方案课程查询
 *
 */
export function searchPjtTeachingPackageApi(data) {
  return request({
    url: '/training/pjtTeachingCurriculum/searchPjtTeachingCurriculum',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 教学方案课程删除接口
 *
 */
export function backDeletePlanCurriculumRelationApi(data) {
  return request({
    url: '/training/pjtTeachingUnitCurriculumRelation/backDeletePlanCurriculumRelation',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 单元时序表查询接口
 *
 */
export function searchPjtTeachingPlanUnitSequentialApi(data) {
  return request({
    url: '/training/pjtTeachingPlanUnit/searchPjtTeachingPlanUnitSequential',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 教学方案名称-单元修改
 *
 */
export function backUpdateByPjtTeachingPlanUnitApi(data) {
  return request({
    url: '/training/pjtTeachingPlanUnit/backUpdateByPjtTeachingPlanUnit',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 教学方案名称-单元删除
 *
 */
export function backDeleteByPjtTeachingPlanUnitApi(data) {
  return request({
    url: '/training/pjtTeachingPlanUnit/backDeleteByPjtTeachingPlanUnit',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}


/**
 * 教学方案-添加课程的课程查询
 *
 */
export function searchPlanPackageApi(data) {
  return request({
    url: '/training/pjtTeachingPlan/searchPlanCurriculum',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}


/**
 * 教学方案-新建课程
 *
 */
export function backInsertPlanCurriculumRelationApi(data) {
  return request({
    url: '/training/pjtTeachingUnitCurriculumRelation/backInsertPlanCurriculumRelation',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 查询学员实验报告
 *
 */
export function searchExperimentReportApi(data) {
  return request({
    url: '/training/pjtCurriculumReport/searchExperimentReport',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}


/**
 * 新增单元
 *
 */
export function backInsertPjtTeachingPlanUnitValueApi(data) {
  return request({
    url: '/training/pjtTeachingPlanUnit/backInsertPjtTeachingPlanUnitValue',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 学生账号查询
 *
 */
export function searchMajorStudentApi(data) {
  return request({
    url: '/training/PjtSysUser/searchClassStudent', // 原先是searchMajorStudent改为searchClassStudent
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
/**
 * 学生账号-修改状态接口
 *
 */
export function updatePjtUserMajorRelationStatusApi(data) {
  return request({
    url: 'training/PjtSysUser/updatePjtUserMajorRelationStatus',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
/**
   * 学生账号-删除接口
   *
   */
export function deletePjtUserMajorRelationApi(data) {
  return request({
    url: 'training/PjtSysUser/deletePjtUserMajorRelation',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
/**
   * 学生账号-修改学生信息接口
   *
   */
export function updatePjtUserMajorRelationApi(data) {
  return request({
    url: 'training/PjtSysUser/updatePjtUserMajorRelation',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
/**
   * 学生账号-添加学员的查询接口
   *
   */
export function searchMajorStudentNoApi(data) {
  return request({
    url: '/training/PjtSysUser/searchMajorStudentNo',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
/**
   * 学生管理-查询导出接口
   *
   */
export function searchMajorStudentExportApi(data) {
  return request({
    url: '/training/PjtSysUser/searchMajorStudentExport',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json'
    },
    responseType: 'blob'
  })
}
/**
   * 学生管理-批量导出接口
   *
   */
export function exportSearchMajorStudentApi(data) {
  return request({
    url: '/training/PjtSysUser/exportSearchMajorStudent',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json'
    },
    responseType: 'blob'
  })
}
/**
   * 学生账号-添加学员的增加接口
   *
   */
export function insertPjtUserMajorRelationApi(data) {
  return request({
    url: 'training/PjtSysUser/insertPjtUserMajorRelation',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 查看课程拓扑
 *
 */
export function getTopoDataByCourseId(data) {
  return request({
    url: `/topology/cepoSysTopology/getCourseTopo/${data}`,
    method: 'get'
  })
}

/**
 * 修改试卷分数
 *
 */
export function updateQuestionScoreAPI(data) {
  return request({
    url: '/training/pjtExamQuestionRelation/updateQuestionRelation',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 启动拓扑环境
 *
 */
export function envApplyAPI(data) {
  return request({
    url: `/topology/cepoSysTopology/startCourseTopo/${data}`,
    method: 'GET',
    data
  })
}

/**
 * 关闭拓扑环境
 *
 */
export function envCloseAPI(data) {
  return request({
    url: `/topology/cepoSysTopology/releaseCourseTopo/${data}`,
    method: 'GET',
    data
  })
}

/**
 * VNC
 *
 */
export function vncAPI(data) {
  return request({
    url: `/topology/tcloud/inner/cloud_host/cloudHostVnc/${data}`,
    method: 'GET'
  })
}

/**
 * 教务-获取课程类型
 *
 */
export function searchCurriculumTypeApi(data) {
  return request({
    url: `/training/pjtTeachingPlanUnitAttribute/searchCurriculumType`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}


/**
 * 教学事务-详情
 *
 */
export function affairDetailsApi(data) {
  return request({
    url: `/training/PjtSysUser/affairDetails`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
/**
 * 教学事务-分析
 *
 */
export function examAnalysisApi(data) {
  return request({
    url: `/training/pjtExam/examAnalysis`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 教学事务-重考
export function practiceAnswerDetailsResit(data) {
  return request({
    url: `/training/pjtPracticeAnswerDetails/resit`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
   * 教师考试管理-试卷管理-分类列表
   *
   */
export function queryCategoryListApi(data) {
  return request({
    url: '/training/pjtCategory/query' + `?categoryType=${data.categoryType}`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
   * 教师考试管理-试卷管理-添加分类
   *
   */
export function addCategoryListApi(data) {
  return request({
    url: '/training/pjtCategory/insertCategory',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
/**
   * 教师考试管理-试卷管理-删除分类
   *
   */
export function deleteCategoryListApi(data) {
  return request({
    url: '/training/pjtCategory/deleteCategory',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
   * 教师考试管理-试卷管理-设为私有
   *
   */
export function backUpdateExamPrivateApi(data) {
  return request({
    url: '/training/pjtExam/backUpdateExamPrivate',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
   * 教师考试管理-试卷管理-删除一个
   *
   */
export function deleteSinglePaperApi(data) {
  return request({
    url: '/training/pjtExam/updateExam',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
   * 教师考试管理-试卷管理-删除多个
   *
   */
export function backDeleteExamQuestionApi(data) {
  return request({
    url: '/training/pjtExam/backDeleteExamQuestion',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
   * 教师考试管理-试卷管理-排课
   *
   */
export function addExamScheduling(data) {
  return request({
    url: '/training/pjtTeacherScheduling/addExamScheduling',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 试卷详情
 */
export function paperViewAPI(params) {
  return request({
    url: '/training/pjtExam/queryTeacherExam',
    params
  })
}

/**
 * 基础教学新增的知识点下拉
 *
 */
export function searchPointApi(data) {
  return request({
    url: '/training/pjtKnowledgePoint/searchPoint',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 修改试卷分数
 *
 */
export function backUpdateQuestionRelationAPI(data) {
  return request({
    url: '/training/pjtExamQuestionRelation/backUpdateQuestionRelation',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 修改试卷信息
 *
 */
export function updatePaperInfoAPI(data) {
  return request({
    url: '/training/pjtExam/updateExam',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
   * 教师考试管理-试卷管理-试卷删除题目
   *
   */
export function deleteQuestionAPI(data) {
  return request({
    url: '/training/pjtExamQuestionRelation/deleteQuestionRelation',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 试卷分类
 *
 */
export function paperCategoryAPI() {
  return request({
    url: '/training/pjtCategory/query?categoryType=3',
    method: 'post'
  })
}

/**
 * 知识点题目数量
 *
 */
export function questionByKnowledgeAPI(data) {
  return request({
    url: '/training/pjtExamQuestion/knowledgeTypeNumber',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 智能组卷
 *
 */
export function autoBuildPaperAPI(data) {
  return request({
    url: '/training/pjtExam/intelligence',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 试卷继承
 *
 */
export function extendsExamAPI(data) {
  return request({
    url: '/training/pjtExam/extendsExam',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}


/**
 * 手动组卷题目查询
 *
 */
export function questionByManualPaperAPI(data) {
  return request({
    url: '/training/pjtExamQuestion/queryBank',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 手动组卷
 *
 */
export function manualBuildPaperAPI(data) {
  return request({
    url: '/training/pjtExam/manual',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 统一修改分数
 *
 */
export function updateAllScore(data) {
  return request({
    url: '/training/pjtExamQuestionRelation/updateQuestionRelation',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 课程删除
 *
 */
export function deleteTeacherSchedulingTimeAPI(data) {
  return request({
    url: '/training/pjtTeacherScheduling/deleteTeacherSchedulingTime',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 课程修改
 *
 */
export function updateTeacherSchedulingTimeAPI(data) {
  return request({
    url: '/training/pjtTeacherScheduling/updateTeacherSchedulingTime',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
/**
 * 教学事务-随堂
 *
 */
export function queryPracticeAffairAPI(data) {
  return request({
    url: '/training/pjtPracticeAnswerDetails/queryPracticeAffair',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export function queryExamInfoAPI(data) {
  return request({
    url: '/training/pjtPracticeAnswerDetails/queryExamInfo',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 教学事务-考试查看
 *
 */
export function queryExamAffairAPI(data) {
  return request({
    url: '/training/pjtExamAnswerDetails/queryExamAffair',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
/**
 * 场景新增
 *
 */
export function insertSceneAPI(data) {
  return request({
    url: '/topology/cepoSysScene/insertScene',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
/**
 * 场景修改
 *
 */
export function updateSceneAPI(data) {
  return request({
    url: '/topology/cepoSysScene/updateScene',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
/**
 * 分页查询
 *
 */
export function querySceneAPI(data) {
  return request({
    url: '/topology/cepoSysScene/queryScene',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
/**
 * 上传场景图片
 *
 */
export function upSceneCoverAPI(data) {
  return request({
    url: '/topology/cepoSysScene/upSceneCover',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
/**
 * 删除场景
 *
 */
export function deleteSceneAPI(data) {
  return request({
    url: '/topology/cepoSysScene/deleteScene' + `?sceneCode=${data.sceneCode}`,
    method: 'GET',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
/**
 * 场景类型下拉
 *
 */
export function querySceneTypeAPI(data) {
  return request({
    url: '/topology/cepoSysScene/querySceneType',
    method: 'GET',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
/**
 * 场景类型下拉
 *
 */
export function judgeSceneNameAPI(data) {
  return request({
    url: '/topology/cepoSysScene/judgeSceneName' + `?sceneName=${data.sceneName}`,
    method: 'GET',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 比赛启动接口
 *
 */
export function searchMatchNumberAPI(data) {
  return request({
    url: '/topology/cepoSysScene/searchMatchNumber',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 试卷管理知识点列表
 *
 */
export function searchKnwledgeAPI(data) {
  return request({
    url: '/training/pjtKnowledgePoint/queryLike',
    method: 'post',
    data
  })
}

/**
 * 分类题目数量
 *
 */
export function questionByCategoryAPI(data) {
  return request({
    url: '/training/pjtExamQuestion/categoryTypeNumber',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 试卷管理分类列表
 *
 */
export function searchCategoryAPI(data) {
  return request({
    url: '/training/pjtCategory/queryLike',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
/**
 * 内容库
 */
export function pjtContentQuery(data) {
  return request({
    url: '/training/content/back/query',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
export function getContentById(data) {
  return request({
    url: '/training/content/back/getById',
    method: 'get',
    params: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
export function pjtMyContentQuery(data) {
  return request({
    url: '/training/content/myContent/query',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
export function pjtCourseCategoryList(data) {
  return request({
    url: '/training/contentCategory/list',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
export function updatePracticeAPI(data) {
  return request({
    url: '/training/pjtPracticePlanCurriculumRelation/updatePractice',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 编辑动态试卷课程内容
export function updateDynamicPractice(data) {
  return request({
    url: '/training/content/dynamicPractice/update',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
    }
  })
}

export function addContent(data) {
  return request({
    url: '/training/content/save',
    method: 'post',
    data: Object.assign({ flag: '1' }, data),
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export function addContentTopo(data) {
  return request({
    url: '/training/content/saveWithTopo',
    method: 'post',
    data: Object.assign({ flag: '1' }, data),
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export function contentTopology(data) {
  return request({
    url: '/training/content/topology/query',
    method: 'post',
    data: data
  })
}


export function updateContent(data) {
  return request({
    url: '/training/content/update',
    method: 'post',
    data: Object.assign({ flag: '1' }, data),
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
export function deleteContent(data) {
  return request({
    url: '/training/content/delete',
    method: 'post',
    data: Object.assign({ flag: '1' }, data),
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}
export function updatePjtCourseCategory(data) {
  return request({
    url: '/training/contentCategory/update',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
export function deletePjtCourseCategory(data) {
  return request({
    url: '/training/contentCategory/delete',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}
export function addPjtCourseCategory(data) {
  return request({
    url: '/training/contentCategory/save',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 内容库-操作手册\课件和视频、拓补Id
 *
 */
export function contentdetail(params) {
  return request({
    url: `/training/content/detail/query/${params.contentId}/${params.format}`,
    method: 'get',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
export function contentdetailDelete(params) {
  return request({
    url: `/training/content/detail/delete/file/${params.attachmentId}`,
    method: 'get',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
export function liveSave(data) {
  return request({
    url: '/training/content/live/save',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}
export function queryPractice(data) {
  return request({
    url: `/training/content/practice/query`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
export function savePractice(data) {
  return request({
    url: `/training/content/practice/save`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
export function updatePractice(data) {
  return request({
    url: `/training/content/practice/update`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}

/**
 * 课程管理
 */
export function saveCourse(data) {
  return request({
    url: '/training/course/save',
    method: 'post',
    data: Object.assign({ flag: '1' }, data),
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
export function queryCourseCategory(data) {
  return request({
    url: '/training/courseCategory/query',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
export function deleteCourse(data) {
  return request({
    url: '/training/course/delete',
    method: 'post',
    data: Object.assign({ flag: '1' }, data),
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}
export function updataCourseCategory(data) {
  return request({
    url: '/training/course/update',
    method: 'post',
    data: Object.assign({ flag: '1' }, data),
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
export function queryCourseDetail(data) {
  return request({
    url: '/training/course/courseDetail/query',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}
export function saveJoinCourse(data) {
  return request({
    url: '/training/content/joinCourse/save',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}
export function cloneCourse(data) {
  return request({
    url: '/training/course/clone',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}

export function saveContentLeaveCourse(data) {
  return request({
    url: '/training/course/contentLeaveCourse/save',
    method: 'post',
    data: Object.assign({ flag: '1' }, data),
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}
export function saveCourseCategory(data) {
  return request({
    url: '/training/courseCategory/save',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
export function updateCourseCategory(data) {
  return request({
    url: '/training/courseCategory/update',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
export function deleteCourseCategory(data) {
  return request({
    url: '/training/courseCategory/delete',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}
/**
 * 课程管理
 */
export function queryCourse(data) {
  return request({
    url: '/training/course/query',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
export function queryBackCourse(data) {
  return request({
    url: '/training/course/back/query',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
/**
 * 教学事务的任务详情
 *
 */
export function searchAffairTeachingTaskAPI(data) {
  return request({
    url: '/training/pjtTeachingTask/searchAffairTeachingTask',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
/**
 * 教学事务的小组任务详情列表
 *
 */
export function searchAffairTeachingTaskDetailAPI(data) {
  return request({
    url: '/training/pjtTeachingTask/searchAffairTeachingTaskDetail',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
/**
 * 任务详情的流程环节查询(带小组成绩)
 *
 */
export function searchTaskProcessLinkStudentAPI(data) {
  return request({
    url: '/training/pjtTaskProcessLink/searchTaskProcessLinkStudent',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
/**
 * 教学事务的小组修改分数
 *
 */
export function updateTeachingTaskScoreAPI(data) {
  return request({
    url: '/training/pjtTeachingTaskGroup/updateTeachingTaskScore',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}


export function uploadFileRequest(data, onUploadProgress) {
  return request({
    url: '/training/content/uploadAttachment',
    method: 'post',
    data,
    onUploadProgress,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

export function topologyQueryTask(params) {
  return request({
    url: '/training/pjtTeachingTask/topology/query',
    method: 'get',
    params
  })
}
export function topologyQueryGroup(params) {
  return request({
    url: '/training/pjtTeachingTask/topology/queryGroup',
    method: 'get',
    params
  })
}
export function practiceUpdateScoreAPI(data) {
  return request({
    url: '/training/content/practice/practiceUpdateScore',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
export function practiceContentDelete(data) {
  return request({
    url: `/training/content/practice/practiceContentDelete`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}
// 分页查询全部试卷
export function getExamList(data) {
  return request({
    url: `admin/sysExam/queryPage`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
export function getCategory(data) {
  return request({
    url: `admin/sysExamCategory/queryPage`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
export function queryQuestionPageByExamId(data) {
  return request({
    url: '/training/pjtExamQuestion/queryQuestionPageByExamId',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 随堂练习-老师评分
export function teacherEditPracticeScore(data) {
  return request({
    url: '/training/pjtPracticeAnswerDetails/assessScore',
    method: 'POST',
    headers,
    data
  })
}

// 随堂练习-老师评分(模拟练习)
export function teacherEditExamPracticeScore(data) {
  return request({
    url: '/training/pjtExamAnswerDetails/assessScore ',
    method: 'POST',
    headers,
    data
  })
}

// 获取章节树
export function getChapterListApi(data) {
  return request({
    url: '/training/pjtChapterUnit/getCourseList',
    method: 'post',
    data,
    headers
  })
}

// 添加章节/单元
export function addChapterApi(data) {
  return request({
    url: '/training/pjtChapterUnit/save',
    method: 'post',
    data,
    headers
  })
}

// 往单元中添加内容
export function addUnitContentRelApi(data) {
  return request({
    url: '/training/pjtChapterUnit/addUnitContentRel',
    method: 'post',
    data,
    headers
  })
}

// 删除章节、单元
export function deleteChapterApi(data) {
  return request({
    url: '/training/pjtChapterUnit/delete',
    method: 'post',
    data,
    headers
  })
}

// 删除课程内容
export function deleteContentRelApi(data) {
  return request({
    url: '/training/pjtChapterUnit/removeUnitContentRel',
    method: 'post',
    data,
    headers
  })
}

// 编辑章节/单元
export function updateChapterApi(data) {
  return request({
    url: '/training/pjtChapterUnit/update',
    method: 'post',
    data,
    headers
  })
}

// 章节/单元排序
export function pjtChapterUnitSortApi(data) {
  return request({
    url: '/training/pjtChapterUnit/sort',
    method: 'post',
    data,
    headers
  })
}

// 跨章节单元/课程排序
export function crossChapterSortApi(data) {
  return request({
    url: '/training/pjtChapterUnit/crossChapterSort',
    method: 'post',
    data,
    headers
  })
}

// 保存操作手册
export function uploadOperationManual(data) {
  return request({
    url: `/training/content/uploadOperationManual`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 保存SceneId
export function updateSourceSceneId(data) {
  return request({
    url: `/training/content/updateSourceSceneId`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 随堂练习-考试模式-获取是否开始考试状态
export function getIsAnswerStatus(data) {
  return request({
    url: '/training/pjtTeacherScheduling/getIsAnswerStatus',
    method: 'POST',
    headers,
    data
  })
}

export function studentAnswer(data) {
  return request({
    url: `/training/content/practice/studentAnswer`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}


export function studentSelfStudyAnswer(data) {
  return request({
    url: `/training/content/practice/studentSelfStudyAnswer`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}

export function queryPracticeUser(data) {
  return request({
    url: `/training/content/practice/userQuery`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 获取直播地址
export function queryLive(params) {
  return request({
    url: `/training/pjtTeacherScheduling/queryLive/${params.roleType}/${params.schedulingCode}`,
    method: 'get',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
export function getCourseInfo(data) {
  return request({
    url: `/training/course/getCourseInfo`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export function getContentInfo(data) {
  return request({
    url: `/training/content/getContentInfo`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 根据拓扑id获取实例id
 */
export function getTopologyStart(params) {
  return request({
    url: `/training/content/topology/start`,
    method: 'get',
    params
  })
}

export function networkelement(data) {
  return request({
    url: `/nfvo/api/networkelement/${data.ElementId}`,
    method: 'get',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export function machineStatus(data) {
  return request({
    url: `/nfvo/api/topology/nodes/${data.nodeId}`,
    method: 'get',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export function examTopoStart(data) {
  return request({
    url: `/nfvo/api/topology/nodes/${data.nodeId}/start`,
    method: 'POST',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
export function examTopoStop(data) {
  return request({
    url: `/nfvo/api/topology/nodes/${data.nodeId}/stop`,
    method: 'POST',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
