import request from '@/utils/request'
/**
 * 分页查询场景列表
 *
 */
export function sceneQueryPageAPI(data) {
  return request({
    url: 'scene/sceneBasicInfo/queryPage',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

/**
 * 分页查询场景列表 权限
 *
 */
export function sceneQueryPageWebAPI(data) {
  return request({
    url: 'scene/sceneBasicInfo/queryPageWeb',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

/**
 * 分页查询场景类型
 *
 */
export function sceneTypePageAPI(data) {
  return request({
    url: 'scene/sceneType/queryPage',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

/**
 * 通过主键查询单条数据
 *
 */
export function getSceneTypeAPI(data) {
  return request({
    url: 'scene/sceneType/get?id=' + data.id,
    method: 'get',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

/**
 * 新增场景第一步
 *
 */
export function createSceneInfoAPI(data) {
  return request({
    url: 'scene/sceneBasicInfo/create',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

/**
 * 通过主键查询单条数据 场景第一步
 *
 */
export function getSceneBasicInfoAPI(data) {
  return request({
    url: 'scene/sceneBasicInfo/get?id=' + data.id,
    method: 'get',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 修改场景数据第一步
 *
 */
export function updateSceneBasicInfoAPI(data) {
  return request({
    url: '/scene/sceneBasicInfo/update',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

/**
 * 共享模式
 *
 */
export function sharingModelBasicInfoAPI(data) {
  return request({
    url: '/scene/sceneBasicInfo/sharingModel',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

export function sceneTypeInitRole(data) {
  return request({
    url: '/scene/sceneTypeInitRole/queryPage',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

export function removeSceneBasicInfo(data) {
  return request({
    url: '/scene/sceneBasicInfo/remove',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

export function extraTimeOfScene(data) {
  return request({
    url: '/scene/sceneBasicInfoInstance/extraTimeOfScene',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

// 批量释放、延时、持久化场景
export function batchSettingSceneApi(data) {
  return request({
    url: '/scene/scencNodeCleanTask/batchSetting',
    method: 'post',
    headers: {
      // 'Content-Type': 'application/json;charset=UTF-8'
      'Content-Type': 'multipart/form-data'
    },
    data
  })
}

// 查询场景状态
export function getTopologyStatusApi(data) {
  return request({
    url: '/scene/scencNodeCleanTask/queryTopologyStatus',
    method: 'post',
    headers: {
      // 'Content-Type': 'application/json;charset=UTF-8'
      'Content-Type': 'multipart/form-data'
    },
    data
  })
}
