<template>
  <div>
    <el-tabs v-model="tabsActive" class="content-subs" type="card" style="margin-top: 20px;" @tab-click="handleTabClick">
      <el-tab-pane label="理论" name="theory">
        <router-link :to="{ name: 'theory' }" />
      </el-tab-pane>
      <el-tab-pane label="靶机" name="targetDevice">
        <router-link :to="{ name: 'targetDevice' }" />
      </el-tab-pane>
      <el-tab-pane label="仿真" name="simulation">
        <router-link :to="{ name: 'simulation' }" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
export default {
  data() {
    return {
      tabsActive: ''
    }
  },
  created() {
    this.tabsActive = this.$route.name
  },
  methods: {
    'handleTabClick': function(data) {
      this.$router.push({ name: data.name })
    }
  }
}
</script>
