import request from '@/utils/request'

/**
 * 考试分类列表
 */
export function pjtCategoryQueryAPI(data) {
  return request({
    url: `/training/pjtCategory/query?categoryType=${data.categoryType}`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 创建考试分类
 */
export function insertCategoryAPI(data) {
  return request({
    url: `/training/pjtCategory/insertCategory`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 题库题目查询
 */
export function queryQuestionBankPageAPI(data) {
  return request({
    url: `training/pjtExamQuestion/queryQuestionBankPage`,
    method: 'post',
    data: data
  })
}

/* 题库根据id获取拓扑id */
export function topologyQuery(params) {
  return request({
    url: '/training/pjtExamQuestion/topology/query',
    method: 'get',
    params
  })
}

/**
 * 下载题目导出模板
 */
export function exportQuestionAPI(data) {
  return request({
    url: `training/pjtExamQuestion/exportQuestion`,
    method: 'post',
    responseType: 'blob',
    data: data
  })
}

/**
 * 上传题目
 */
export function questionUploadAPI(data) {
  return request({
    url: `training/upload/questionUpload`,
    method: 'get',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}


/**
 * 新增题目
 */
export function insertNewExamQuestionAPI(data) {
  return request({
    url: `training/pjtExamQuestion/insertNewExamQuestion`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
/**
 * 查询单个小题
 */
export function selectOneAPI(data) {
  return request({
    url: `/training/pjtExamQuestion/selectOne?questionCode=${data.questionCode}`,
    method: 'get',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
/**
 * 删除小题
 */
export function deleteExamQuestionAPI(data) {
  return request({
    url: `/training/pjtExamQuestion/deleteExamQuestion`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 修改题目
 */
export function updateExamQuestionAPI(data) {
  return request({
    url: `/training/pjtExamQuestion/updateExamQuestion`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * Zip上传
 */
export function upTeachingCoverAPI(data) {
  return request({
    url: `/training/pjtExamQuestion/upCover`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}


/**
 * 下载导入选择题模板
 */
export function downloadMould() {
  return request({
    url: `/training/pjtExamQuestion/exportQuestion`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    responseType: 'blob'
  })
}

