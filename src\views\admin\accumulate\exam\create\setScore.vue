<template>
  <div v-loading="loading" class="drawer-wrap">
    <div class="drawer-wrap-content">
      <el-form ref="form" :model="formData" :rules="rules" label-position="left" label-width="150px" class="dialog-form">
        <div v-if="theoryList.length">
          <div>
            理论
          </div>
          <div class="p-10">
            <el-form-item
              v-if="theoryList.filter(item => { return item.questionType === 1 }).length"
              :label="`单选题（${theoryList.filter(item => { return item.questionType === 1 }).length}）`"
              prop="theoryVO.singleTypeNum"
            >
              <el-input-number
                v-model.trim="formData.theoryVO.singleTypeNum"
                :controls="false"
                placeholder="请输入分数"
                style="width: 300px"
              /> 分
            </el-form-item>
            <el-form-item
              v-if="theoryList.filter(item => { return item.questionType === 2 }).length"
              :label="`多选题（${theoryList.filter(item => { return item.questionType === 2 }).length}）`"
              prop="theoryVO.manyTypeNum"
            >
              <el-input-number
                v-model.trim="formData.theoryVO.manyTypeNum"
                :controls="false"
                placeholder="请输入分数"
                style="width: 300px"
              /> 分
            </el-form-item>
            <el-form-item
              v-if="theoryList.filter(item => { return item.questionType === 3 }).length"
              :label="`判断题（${theoryList.filter(item => { return item.questionType === 3 }).length}）`"
              prop="theoryVO.judgeTypeNum"
            >
              <el-input-number
                v-model.trim="formData.theoryVO.judgeTypeNum"
                :controls="false"
                placeholder="请输入分数"
                style="width: 300px"
              /> 分
            </el-form-item>
            <el-form-item
              v-if="theoryList.filter(item => { return item.questionType === 4 }).length"
              :label="`CTF题（${theoryList.filter(item => { return item.questionType === 4 }).length}）`"
              prop="theoryVO.ctfTypeNum"
            >
              <el-input-number
                v-model.trim="formData.theoryVO.ctfTypeNum"
                :controls="false"
                placeholder="请输入分数"
                style="width: 300px"
              /> 分
            </el-form-item>
            <el-form-item
              v-if="theoryList.filter(item => { return item.questionType === 7 }).length"
              :label="`填空题（${theoryList.filter(item => { return item.questionType === 7 }).length}）`"
              prop="theoryVO.completionNum"
            >
              <el-input-number
                v-model.trim="formData.theoryVO.completionNum"
                :controls="false"
                placeholder="请输入分数"
                style="width: 300px"
              /> 分
            </el-form-item>
            <el-form-item
              v-if="theoryList.filter(item => { return item.questionType === 8 }).length"
              :label="`简答题（${theoryList.filter(item => { return item.questionType === 8 }).length}）`"
              prop="theoryVO.saqTypeNum"
            >
              <el-input-number
                v-model.trim="formData.theoryVO.saqTypeNum"
                :controls="false"
                placeholder="请输入分数"
                style="width: 300px"
              /> 分
            </el-form-item>
          </div>
        </div>
        <div v-if="targetList.length">
          <div>
            靶机
          </div>
          <div class="p-10">
            <el-form-item
              v-if="targetList.filter(item => { return item.questionType === 4 }).length"
              :label="`CTF题（${targetList.filter(item => { return item.questionType === 4 }).length}）`"
              prop="targetVO.ctfTypeNum"
            >
              <el-input-number
                v-model.trim="formData.targetVO.ctfTypeNum"
                :controls="false"
                placeholder="请输入分数"
                style="width: 300px"
              /> 分
            </el-form-item>
            <el-form-item
              v-if="targetList.filter(item => { return item.questionType === 5 }).length"
              :label="`AWD题（${targetList.filter(item => { return item.questionType === 5 }).length}）`"
              prop="targetVO.awdTypeNum"
            >
              <el-input-number
                v-model.trim="formData.targetVO.awdTypeNum"
                :controls="false"
                placeholder="请输入分数"
                style="width: 300px"
              /> 分
            </el-form-item>
            <el-form-item
              v-if="targetList.filter(item => { return item.questionType === 9 }).length"
              :label="`漏洞题（${targetList.filter(item => { return item.questionType === 9 }).length}）`"
              prop="targetVO.bugTypeNum"
            >
              <el-input-number
                v-model.trim="formData.targetVO.bugTypeNum"
                :controls="false"
                placeholder="请输入分数"
                style="width: 300px"
              /> 分
            </el-form-item>
            <el-form-item
              v-if="targetList.filter(item => { return item.questionType === 6 }).length"
              :label="`其它（${targetList.filter(item => { return item.questionType === 6 }).length}）`"
              prop="targetVO.otherTypeNum"
            >
              <el-input-number
                v-model.trim="formData.targetVO.otherTypeNum"
                :controls="false"
                placeholder="请输入分数"
                style="width: 300px"
              /> 分
            </el-form-item>
          </div>
        </div>
        <div v-if="emulationList.length">
          <div>
            仿真
          </div>
          <div class="p-10">
            <el-form-item
              v-if="emulationList.filter(item => { return item.questionType === 1 }).length"
              :label="`单选题（${emulationList.filter(item => { return item.questionType === 1 }).length}）`"
              prop="emulationVO.singleTypeNum"
            >
              <el-input-number
                v-model.trim="formData.emulationVO.singleTypeNum"
                :controls="false"
                placeholder="请输入分数"
                style="width: 300px"
              /> 分
            </el-form-item>
            <el-form-item
              v-if="emulationList.filter(item => { return item.questionType === 2 }).length"
              :label="`多选题（${emulationList.filter(item => { return item.questionType === 2 }).length}）`"
              prop="emulationVO.manyTypeNum"
            >
              <el-input-number
                v-model.trim="formData.emulationVO.manyTypeNum"
                :controls="false"
                placeholder="请输入分数"
                style="width: 300px"
              /> 分
            </el-form-item>
            <el-form-item
              v-if="emulationList.filter(item => { return item.questionType === 3 }).length"
              :label="`判断题（${emulationList.filter(item => { return item.questionType === 3 }).length}）`"
              prop="emulationVO.judgeTypeNum"
            >
              <el-input-number
                v-model.trim="formData.emulationVO.judgeTypeNum"
                :controls="false"
                placeholder="请输入分数"
                style="width: 300px"
              /> 分
            </el-form-item>
            <el-form-item
              v-if="emulationList.filter(item => { return item.questionType === 4 }).length"
              :label="`CTF题（${emulationList.filter(item => { return item.questionType === 4 }).length}）`"
              prop="emulationVO.ctfTypeNum"
            >
              <el-input-number
                v-model.trim="formData.emulationVO.ctfTypeNum"
                :controls="false"
                placeholder="请输入分数"
                style="width: 300px"
              /> 分
            </el-form-item>
            <el-form-item
              v-if="emulationList.filter(item => { return item.questionType === 7 }).length"
              :label="`填空题（${emulationList.filter(item => { return item.questionType === 7 }).length}）`"
              prop="emulationVO.completionNum"
            >
              <el-input-number
                v-model.trim="formData.emulationVO.completionNum"
                :controls="false"
                placeholder="请输入分数"
                style="width: 300px"
              /> 分
            </el-form-item>
            <el-form-item
              v-if="emulationList.filter(item => { return item.questionType === 8 }).length"
              :label="`简答题（${emulationList.filter(item => { return item.questionType === 8 }).length}）`"
              prop="emulationVO.saqTypeNum"
            >
              <el-input-number
                v-model.trim="formData.emulationVO.saqTypeNum"
                :controls="false"
                placeholder="请输入分数"
                style="width: 300px"
              /> 分
            </el-form-item>
            <el-form-item
              v-if="emulationList.filter(item => { return item.questionType === 10 }).length"
              :label="`综合题（${emulationComNum}）`"
              prop="emulationVO.combinatorialTypeNum"
            >
              每小题 <el-input-number
                v-model.trim="formData.emulationVO.combinatorialTypeNum"
                :controls="false"
                placeholder="请输入分数"
                style="width: 255px"
              /> 分，共 {{ emulationComContentNum }} 道小题
            </el-form-item>
          </div>
        </div>
      </el-form>
    </div>
    <div class="drawer-footer">
      <el-button type="primary" @click="confirm">确定</el-button>
      <el-button type="text" @click="close">取消</el-button>
    </div>
  </div>
</template>
<script>
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
import validate from '@/packages/validate'
export default {
  components: {
  },
  mixins: [mixinsActionMenu],
  props: {
    // 传入数据
    data: {
      type: Array,
      default: () => {
        return []
      }
    },
    name: {
      type: String
    },
    theoryList: {
      type: Array
    },
    targetList: {
      type: Array
    },
    emulationList: {
      type: Array
    },
    getQuestionNum: {
      type: Number
    },
    getScore: {
      type: Number
    }
  },
  data() {
    return {
      loading: false,
      validate: validate,
      formData: {
        'theoryVO': {
          singleTypeNum: undefined,
          manyTypeNum: undefined,
          judgeTypeNum: undefined,
          ctfTypeNum: undefined,
          completionNum: undefined,
          saqTypeNum: undefined
        },
        'targetVO': {
          ctfTypeNum: undefined,
          awdTypeNum: undefined,
          bugTypeNum: undefined,
          otherTypeNum: undefined
        },
        'emulationVO': {
          singleTypeNum: undefined,
          manyTypeNum: undefined,
          judgeTypeNum: undefined,
          ctfTypeNum: undefined,
          completionNum: undefined,
          saqTypeNum: undefined,
          combinatorialTypeNum: undefined
        }
      },
      rules: {
        'theoryVO.singleTypeNum': [validate.all_number_integer, { type: 'number', min: 0, max: 9999, message: '输入范围：0-9999', trigger: 'change' }],
        'theoryVO.manyTypeNum': [validate.all_number_integer, { type: 'number', min: 0, max: 9999, message: '输入范围：0-9999', trigger: 'change' }],
        'theoryVO.judgeTypeNum': [validate.all_number_integer, { type: 'number', min: 0, max: 9999, message: '输入范围：0-9999', trigger: 'change' }],
        'theoryVO.ctfTypeNum': [validate.all_number_integer, { type: 'number', min: 0, max: 9999, message: '输入范围：0-9999', trigger: 'change' }],
        'theoryVO.completionNum': [validate.all_number_integer, { type: 'number', min: 0, max: 9999, message: '输入范围：0-9999', trigger: 'change' }],
        'theoryVO.saqTypeNum': [validate.all_number_integer, { type: 'number', min: 0, max: 9999, message: '输入范围：0-9999', trigger: 'change' }],
        'targetVO.ctfTypeNum': [validate.all_number_integer, { type: 'number', min: 0, max: 9999, message: '输入范围：0-9999', trigger: 'change' }],
        'targetVO.awdTypeNum': [validate.all_number_integer, { type: 'number', min: 0, max: 9999, message: '输入范围：0-9999', trigger: 'change' }],
        'targetVO.bugTypeNum': [validate.all_number_integer, { type: 'number', min: 0, max: 9999, message: '输入范围：0-9999', trigger: 'change' }],
        'targetVO.otherTypeNum': [validate.all_number_integer, { type: 'number', min: 0, max: 9999, message: '输入范围：0-9999', trigger: 'change' }],
        'emulationVO.singleTypeNum': [validate.all_number_integer, { type: 'number', min: 0, max: 9999, message: '输入范围：0-9999', trigger: 'change' }],
        'emulationVO.manyTypeNum': [validate.all_number_integer, { type: 'number', min: 0, max: 9999, message: '输入范围：0-9999', trigger: 'change' }],
        'emulationVO.judgeTypeNum': [validate.all_number_integer, { type: 'number', min: 0, max: 9999, message: '输入范围：0-9999', trigger: 'change' }],
        'emulationVO.ctfTypeNum': [validate.all_number_integer, { type: 'number', min: 0, max: 9999, message: '输入范围：0-9999', trigger: 'change' }],
        'emulationVO.completionNum': [validate.all_number_integer, { type: 'number', min: 0, max: 9999, message: '输入范围：0-9999', trigger: 'change' }],
        'emulationVO.saqTypeNum': [validate.all_number_integer, { type: 'number', min: 0, max: 9999, message: '输入范围：0-9999', trigger: 'change' }],
        'emulationVO.combinatorialTypeNum': [validate.all_number_integer, { type: 'number', min: 0, max: 9999, message: '输入范围：0-9999', trigger: 'change' }]
      }
    }
  },
  computed: {
    // 综合题的数量
    emulationComNum() {
      let num = 0
      this.emulationList.forEach(item => {
        if (item.questionType == 10 && item.combinationQuestionBOS) {
          num += item.combinationQuestionBOS.length
        }
      })
      return num
    },
    // 综合题-题目的数量
    emulationComContentNum() {
      let num = 0
      this.emulationList.forEach(item => {
        if (item.questionType == 10 && item.combinationQuestionBOS) {
          item.combinationQuestionBOS.forEach(sub => {
            num += sub.content.length
          })
        }
      })
      return num
    }
  },
  methods: {
    close: function() {
      this.$emit('close')
    },
    confirm: function() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.$emit('call', 'setScore', this.formData)
          this.close()
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.p-10 {
  padding: 10px 20px;
}
</style>
