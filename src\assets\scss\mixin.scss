//复用flex

@mixin flex($hov:space-between,$col:center,$derection:row) {
	display: flex;
	flex-direction: $derection;
	justify-content: $hov;
	align-items: $col;
}
//背景图
@mixin bgImg($w:0,$h:0,$img:'',$size:contain) {
	display: inline-block;
	width: $w;
	height: $h;
	background: url($img) no-repeat;
	background-size: $size;
}
//一行...
@mixin ellipsisTxt() {
	overflow: hidden;
	text-overflow:ellipsis;
	white-space: nowrap;
}
//多行...
@mixin numEllipsisTxt($num:3) {
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: $num;
	overflow: hidden;
}
//placeholder 样式
@mixin placeholder($cor:#8FABCF,$algin:left,$size:14px) {
	&{
		.el-input__inner{
			color: $cor;
		}
	}
	::-webkit-input-placeholder {
		color: $cor;
		text-align: $algin;
		font-size: $size;
	}
	::-moz-input-placeholder {
		color:$cor;
		text-align: $algin;
		font-size: $size;
	}
	:-moz-input-placeholder {
		color: $cor;
		text-align: $algin;
		font-size: $size;
	}
	:-ms-input-placeholder {
		color: $cor;
		text-align: $algin;
		font-size: $size;
	}
}
//滚动条样式
@mixin scrollbar() {
	::-webkit-scrollbar-track {
		-webkit-box-shadow: inset 0 0 6px rgba(232, 232, 232, .4) !important;
		background-color: rgba(232, 232, 232, .4) !important;
	}
	*::-webkit-scrollbar-thumb {
		background-color: rgba(51, 149, 240, .4) !important;
		// border-radius: 8px !important;
		-webkit-box-shadow: inset 0 0 6px rgba(51, 149, 240, .4) !important;
	}
	::-webkit-scrollbar {
		width: 5px;
		height: 5px;
		// display: none;
	}
}
//分页样式
@mixin pagination($col:$colorHome,$mtop:74px) {
	.el-pagination::v-deep{
		margin-top: $mtop;
		@include flex(flex-end,center);
		.el-pager{
			li{
				border-radius:5px;
				&.active, &:hover{
					background-color: $col;
					color: $colorHeader;
				}
			}
		}
	}
}

//字体渐变
@mixin txtLinear($deg:0deg,$col:rgba(0,114,255,1),$col1:rgba(0,234,255,1),$col2:rgba(1,170,255,1)){
	background: linear-gradient($deg,$col 0%,$col1 48.8525390625%,$col2  100%);
	-webkit-background-clip: text;
	color: transparent;
}

@mixin paddingBoxSizing($padding:0) {
	padding: $padding;
	box-sizing: border-box;
}
