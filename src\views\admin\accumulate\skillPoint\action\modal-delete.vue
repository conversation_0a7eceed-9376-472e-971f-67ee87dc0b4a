<template>
  <div v-loading="loading" class="dialog-wrap">
    <el-alert :closable="false" type="warning">
      <div slot="title">已关联题目的技能点不允许删除</div>
    </el-alert>
    <batch-template
      :data="data"
      :available-data="availableArr"
      view-key="name"
    />
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button :disabled="!availableArr.length" type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>

<script>
import moduleConf from '../config.js'
import batchTemplate from '@/packages/batch-delete/modal-bat-template.vue'
import { deleteSkillPoint } from '@/api/accumulate/skillPoint.js'

export default {
  components: {
    batchTemplate
  },
  props: {
    data: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      moduleName: moduleConf.name,
      loading: false
    }
  },
  computed: {
    // 筛选返回可操作数据
    'availableArr': function() {
      const _data = []
      this.data.forEach((item, index) => {
        // 没有使用数量 或 使用数量为0允许删除
        if (!item.count) {
          _data.push(item)
        }
      })
      return _data
    }
  },
  methods: {
    close() {
      this.$emit('close')
    },
    confirm: function() {
      this.loading = true
      const postData = this.availableArr.map(item => item.id)
      deleteSkillPoint(postData).then(res => {
        this.$message.success('删除知识点成功')
        this.$bus.$emit(this.moduleName + '_module', 'reload')
        this.close()
      }).catch(() => {
        this.$bus.$emit(this.moduleName + '_module', 'reload')
        this.close()
      })
    }
  }
}
</script>
