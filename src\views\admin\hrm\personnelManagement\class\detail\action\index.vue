<template>
  <div class="buttons-wrap">
    <el-button type="primary" @click="clickDrop('addStudent')">添加学员</el-button>
    <el-button :disabled="multipleDisabled" type="primary" @click="clickDrop('deleteStudent')">移除学员</el-button>
    <!-- 中部弹窗 start-->
    <el-dialog
      :title="titleMapping[modalName]"
      :visible.sync="modalShow"
      :width="modalWidth"
      append-to-body
      @close="modalClose"
    >
      <transition name="el-fade-in-linear">
        <component
          :is="modalName"
          :name="modalName"
          :data="selectItem"
          :upload-data="uploadData"
          @close="modalClose"
          @call="confirmCall"
        />
      </transition>
    </el-dialog>
    <!-- 中部弹窗 end-->
    <!-- 侧拉弹窗 start -->
    <el-drawer
      :title="titleMapping[drawerName]"
      :visible.sync="drawerShow"
      :size="drawerWidth"
      append-to-body
      @close="drawerClose"
    >
      <transition name="el-fade-in-linear">
        <component
          :is="drawerName"
          :name="drawerName"
          :class-code="classCode"
          :data="selectItem"
          @close="drawerClose"
          @call="confirmCall"
        />
      </transition>
    </el-drawer>
    <!-- 侧拉弹窗 end -->
  </div>
</template>
<script>
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
import deleteStudent from './modal_delete'
import addStudent from './modal_add.vue'
export default {
  components: {
    deleteStudent,
    addStudent
  },
  mixins: [mixinsActionMenu],
  props: {
    uploadData: {
      type: Object
    }
  },
  data() {
    return {
      // 弹窗title映射
      titleMapping: {
        'addStudent': '添加学员',
        'deleteStudent': '移除学员'
      },
      drawerAction: ['addStudent'], // 需要侧拉打开的操作
      // 弹窗title映射
      dialogLoading: false,
      confirmDisabled: false,
      classCode: ''
    }
  },
  mounted() {
    this.token = JSON.parse(localStorage.getItem('Admin-Token')).data
    this.classCode = this.$route.params.classCode
  },
  methods: {
    confirmCall: function(type, data) {
      if (type === 'close') {
        this.modalClose()
      } else if (type === 'refresh') {
        this.$emit('call', type)
      }
    },
    'clickDrop': function(name) {
      if (this.drawerAction && this.drawerAction.includes(name)) {
        this.drawerName = name
      } else {
        this.modalName = name
      }
    }
  }
}
</script>
