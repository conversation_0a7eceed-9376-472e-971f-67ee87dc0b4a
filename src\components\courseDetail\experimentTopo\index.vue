<template>
  <div class="experiment-topo">
    <div class="cr-header">
      <div class="cr-left">
        <img :src="config.configUrl" class="cr-image" alt="">
      </div>
      <div class="cr-right">
        <div style="margin-right: 10px;">
          <el-tag
            v-for="(node, index) in openedConsoleShow"
            :key="index"
            :type="workstation && workstation.id == node.id ? '' : 'info'"
            effect="dark"
            closable
            @click="clickDrop(null, node)"
            @close="closeConsole(node.id)"
          ><i v-if="workstation && workstation.id == node.id" class="cr-icon-diannao" />{{ node.name }}</el-tag>
        </div>
        <el-dropdown v-if="openedConsole.length" placement="bottom-start" trigger="click" class="console-fold" @command="clickDrop(null, $event)">
          <div>展开<i class="el-icon-d-arrow-left rotate-90deg" /></div>
          <el-dropdown-menu slot="dropdown">
            <div v-for="(node, index) in openedConsole" :key="index">
              <el-dropdown-item :style="{'background': workstation && workstation.id == node.id ? 'var(--color-50)' : '' }" :command="node">
                <i v-if="workstation && workstation.id == node.id" class="cr-icon-diannao" style="font-size: 14px; font-weight: bold;" />{{ node.name }}
              </el-dropdown-item>
            </div>
          </el-dropdown-menu>
        </el-dropdown>
        <el-divider v-if="openedConsole.length" direction="vertical" />
        <el-tag v-if="topologyNodesConsole.length" :type="topologyNodesConsole.length && showList ? '' : 'info'" effect="dark" class="right-item" @click="switchShowList">
          <i class="el-icon-menu" />控制台
        </el-tag>
        <el-divider v-if="topologyNodesConsole.length" direction="vertical" />
        <!-- 显示topo -->
        <el-tag v-if="topologyId" :type="topologyId && showTopo ? '' : 'info'" effect="dark" class="right-item" @click="switchShowTopo">
          <i class="cr-icon-tuopumuban-copy" style="font-size: 14px;" />场景拓扑
        </el-tag>
      </div>
    </div>
    <div class="topo-content">
      <!-- 折叠 -->
      <div v-if="!fold" :style="{'left': `calc(${percent}% - 10px)`}" class="fold-wrap" @click="handleFold(true)">
        <i class="el-icon-caret-left" />
      </div>
      <!-- 展开 -->
      <div v-if="fold" :style="{'left': `calc(${percent}% - 10px)`}" class="fold-wrap" @click="handleFold(false)">
        <i class="el-icon-caret-right" />
      </div>
      <split-pane ref="split-pane" :min-percent="minPercent" :default-percent="percent" split="vertical" @resize="resize">
        <Manual slot="paneL" :content-id="contentId" :course-name="courseName" style="border-right: 1px solid #dcdee2;" />
        <div v-loading="loading" slot="paneR" class="paneR">
          <!-- vnc、ssh、rdp -->
          <div v-if="openedConsole.length" :style="{ visibility: workstation ? 'visible' : 'hidden'}" class="console-wrap">
            <div v-for="(node) in openedConsole" :key="node.id" :style="{'z-index': workstation && workstation.id === node.id ? '9' : '1'}" class="console-container">
              <!-- 在未启动的情况下点击控制台强制的打开这个空提示 -->
              <el-empty v-if="node.status != 'running'" :image="img" :image-size="180" description="工作站未启动" style="padding-top: 15%;" />
              <iframe
                v-else
                :id="node.id"
                :src="node.workstationUrl"
                :title="node.name"
                frameborder="0"
                allowfullscreen
                style="width: 100%;height:100%;"
                @mouseover="setFocus(node)"
              />
            </div>
          </div>
          <!-- 拓扑 -->
          <Topo
            v-if="topologyId && showTopo"
            :scene-id="topologyAllocation == 0 ? '' : sourceSceneId"
            :topo-id="topologyId"
            :role-property="topologyAllocation == 0 ? 0 : 1"
            :topo-type="getTopoType"
            :role-filterate="enterType == 'student' && roleId ? 3 : 0"
            :user-identity="enterType == 'student' && roleId ? roleId : ''"
            :is-training-module="true"
            :zoom-to-fit="true"
            style="width: 100%; height: 100%;"
            console-target="_self"
            @console-url="consoleUrl"
          >
            <!-- 课程详情进入共享课程的拓扑：不展示全屏按钮（因为只展示场景拓扑模版） -->
            <template v-if="!(topologyAllocation == 1 && enterType == 'course')" slot="rightIcon">
              <a href="javascript:;">
                <el-tooltip content="鼠标右键点击网元图标可查看网元信息、登录控制台（如开启）等" placement="bottom">
                  <i class="cr-icon-info" style="font-size: 19px;" />
                </el-tooltip>
              </a>
            </template>
          </Topo>
          <!-- 未启动拓扑 -->
          <div v-else-if="!topoRunning" class="topo-start-wrap">
            <template v-if="enterType == 'student' && topologyAllocation == 1">
              <div class="start-title">请等待老师启动拓扑</div>
              <div class="start-button notext">
                <img src="./startBg.jpg" alt="">
              </div>
            </template>
            <template v-else>
              <div class="start-title">点击下方环境窗口，开启实战</div>
              <div class="start-button" @click="startTopology">
                <el-button type="text">启动实验环境</el-button>
                <img src="./startBg.jpg" alt="">
              </div>
            </template>
          </div>
          <!-- 如果有控制台节点，但是未选择进哪个工作站时 -->
          <div v-else-if="topologyNodesConsole.length && !workstation" class="console-list">
            <div class="start-title">当前实操环境共 {{ topologyNodesConsole.length }} 个设备开启控制台登录</div>
            <div class="console-button">
              <div v-for="node in topologyNodesConsole" :key="node.id" class="console-item">
                <div v-overflow-tooltip class="ellipsis">{{ node.name }}</div>
                <img src="./startBg.jpg" alt="">
                <div class="console-view">
                  <el-popover placement="top" width="300" trigger="click" popper-class="view-wrap" @show="showView(node)">
                    <div v-loading="viewLoading" class="view-content">
                      <div>
                        <div>状态：</div>
                        <div>{{ nodeStatusInfo[node.status] }}</div>
                      </div>
                      <div>
                        <div>内网IP地址：</div>
                        <div><div v-for="(port, index) in node.ports" v-show="port.ip" :key="index">{{ port.ip }}</div></div>
                      </div>
                      <template v-if="node.console_info && node.console_info.length">
                        <div v-for="item in node.console_info" :key="item.console_type">
                          <div>{{ item.console_type === 'rdp' ? '远程桌面地址' : 'SSH地址' }}：</div>
                          <div>{{ item.ip }}:{{ item.port }}</div>
                        </div>
                      </template>
                    </div>
                    <el-button slot="reference" type="primary">查看</el-button>
                  </el-popover>
                  <el-dropdown placement="bottom" trigger="hover" @command="clickDrop($event, node)">
                    <div>远程访问<i class="el-icon-arrow-down el-icon--right" /></div>
                    <el-dropdown-menu slot="dropdown" class="console-list-dropdown">
                      <div v-for="(console, index) in node.console_type.split(',')" :key="index">
                        <el-dropdown-item :command="console">
                          <el-tooltip :content="consoleMap[console].tooltip" effect="dark" placement="top">
                            <i class="cr-icon-diannao" />
                          </el-tooltip>
                          {{ consoleMap[console].label }}
                        </el-dropdown-item>
                      </div>
                    </el-dropdown-menu>
                  </el-dropdown>
                </div>
              </div>
              <div class="perch" />
              <div class="perch" />
            </div>
          </div>
          <!-- 暂无数据 -->
          <el-empty v-else-if="description" :image="img" :image-size="180" :description="description" style="padding-top: 15%;" />
          <!-- 启动拓扑的萌版 -->
          <div v-if="showMasking" :class="{ 'percentage-0': percentage === 0 }" class="topo-masking">
            <i v-if="percentage === 100 && percentageData.error > 0" class="el-icon-close topo-masking-close" @click="showMasking = false" />
            <el-progress
              :text-inside="true"
              :stroke-width="20"
              :percentage="percentage"
              color="var(--color-600)"
            />
            <div v-if="percentage === 100 && percentageData.error > 0" style="margin-top: 5px;">启动实验环境失败</div>
            <div v-else style="margin-top: 5px;">{{ percentageLabel }}</div>
          </div>
        </div>
      </split-pane>
    </div>
  </div>
</template>

<script>
import splitPane from 'vue-splitpane'
import detailCard from '@/packages/detail-view/detail-card.vue'
import serialConsole from '@/packages/topo/tpl/serial_console/index.vue'
import Topo from '@/packages/topo/index'
import Manual from './Manual.vue'
import { queryBaseInfoConfig } from '@/packages/layout/api/config'
import { getSceneBasicInfoAPI, getTopologyIdBySceneInstanceId, topologyQueryById } from '@/api/topo.js'
import { contentTopology, getContentById, getTopologyStart } from '@/api/teacher/index.js'
import { getItem, sceneStartTopoApi, vnc, serial, getConsole } from '@/packages/topo/api/orchestration'
import { mapState } from 'vuex'
import { getNodeItem } from '@/packages/topo/api/orchestration'
import { tabManager } from '@/packages/utils/tabManager.js'

export default {
  name: 'HandleTopo',
  components: {
    splitPane,
    detailCard,
    serialConsole,
    Topo,
    Manual
  },
  data() {
    return {
      fold: false, // 是否折叠
      minPercent: 0, // 指导书的最小宽度
      percent: 30, // 指导书的宽度
      config: {}, // 头部logo
      loading: true,
      viewLoading: false,
      img: require('@/packages/table-view/nodata.png'), // 暂无数据图片
      description: '', // 暂无数据文字
      showList: true,
      showTopo: false, // 是否展示拓扑
      showMasking: false, // 是否展示蒙版
      percentageLabel: '', // 启动和释放的文字
      percentageData: { // 启动和释放的数据
        total: 0,
        success: 0,
        error: 0
      },

      enterType: this.$route.query.enterType || 'student', // student:学生, teacher:老师
      contentId: this.$route.query.id, // 课程内容id
      courseId: this.$route.query.courseId, // 课程name
      courseName: this.$route.query.courseName, // 课程name
      topologyAllocation: this.$route.query.topologyAllocation, // 独享还是共享
      topologyId: '', // 拓扑id
      roleId: this.$route.query.roleId || '', // 角色id
      sourceSceneId: '', // 场景id
      sceneInstanceId: this.$route.query.sceneInstanceId || '', // 场景实例id

      topoRunning: true, // 拓扑是否启动
      topologyNodes: [], // 拓扑中的节点
      workstation: null, // 当前工作站节点信息
      topologyPermissionMapping: {
        'course0': 'firingPermissions', // 课程内容详情（独享）
        'course1': 'consolePermissions', // 课程内容详情（共享）
        'student0': 'firingPermissions', // 学生（独享）
        'student1': 'consolePermissions', // 学生（共享）
        'teacher': 'firingPermissions' // 教师
      },
      openedConsole: [], // 已经打开控制台的工作站
      nodeStatusInfo: {
        'pending': '添加成功',
        'starting': '启动中',
        'running': '运行中',
        'powering_on': '开机中',
        'shutoff': '关机',
        'powering_off': '关机中',
        'deleting': '删除中',
        'suspended': '挂起',
        'suspending': '挂起中',
        'paused': '暂停',
        'pausing': '暂停中',
        'rebooting': '重启中',
        'rebuilding': '重建中',
        'error': '错误',
        'resuming': '恢复中'
      },
      consoleMap: {
        'vnc': { label: 'VNC', tooltip: '远程访问图形化桌面，支持跨平台远程控制' },
        'rdp': { label: '远程桌面', tooltip: '远程访问图形化桌面，提供完整的桌面体验' },
        'ssh': { label: 'WebSSH', tooltip: '远程访问命令行界面' },
        'serial': { label: '串口控制台', tooltip: '通过串行接口连接设备' },
        'webshell': { label: '命令行', tooltip: '免登录远程访问命令行界面' }
      }
    }
  },
  computed: {
    ...mapState('socketListener', [
      'instanceSocket',
      'topologySocket'
    ]),
    // 拓扑权限
    getTopoType() {
      return this.topologyPermissionMapping[(this.enterType == 'student' || this.enterType == 'course') ? (this.enterType + this.topologyAllocation) : this.enterType]
    },
    // 可以打开控制台的节点列表
    topologyNodesConsole() {
      return this.topologyNodes.filter(item => item.console_type && item.resource_type == 'vnf' && (item.virtual_type === 'qemu' || item.virtual_type === 'docker'))
    },
    openedConsoleShow() {
      return this.openedConsole.slice(0, 4)
    },
    // 进度百分比
    'percentage': function() {
      return this.percentageData.total ? Math.floor(((this.percentageData.success + this.percentageData.error) / this.percentageData.total) * 100) : 0
    }
  },
  watch: {
    // 监听instance的websocket，只用于来更新画布上node的状态
    'instanceSocket': function(nval, oval) {
      this.socketHandle(nval, oval)
    },
    'topologySocket': function(val) {
      const payload = val.payload
      const message = payload.message ? JSON.parse(payload.message) : null
      if (payload.resource_id === this.topologyId && payload.option === 'progress') {
        this.percentageData = message
        if (message.success === message.total) { // 启动完成
          if (message.error > 0) {
            this.description = '启动实验环境失败'
          }
          this.showMasking = false
          this.topoRunning = true
          this.openedConsole = []
          this.workstation = null
          this.showList = true
          this.showTopo = false
        }
      }
    }
  },
  mounted() {
    window.addEventListener('beforeunload', this.handleBeforeUnload)
    this.minPercent = (10 / this.$refs['split-pane'].$el.offsetWidth) * 100
    this.getDetail()
    if (this.topologyAllocation == 0) { // 独享
      if (this.enterType === 'student') { // 学员：调用start接口去实例化拓扑实例id
        this.getTopologyStart()
      } else { // 教师、助教：去获取课程的拓扑id
        this.contentTopology()
      }
    } else { // 共享
      if (this.enterType === 'course') {
        this.getContentByIdFn()
      } else {
        this.getTopologyBySceneInstanceId()
      }
    }
  },
  beforeDestroy() {
    window.removeEventListener('beforeunload', this.handleBeforeUnload)
  },
  methods: {
    handleBeforeUnload(e) {
      e.preventDefault()
      e.returnValue = '关闭此页面后，控制台会话可能会丢失。'
      // 清除全局共享的浏览器tab页签
      tabManager.clearTabRef(`experiment-${this.courseId}-${this.contentId}`)
      return e.returnValue
    },
    showView(node) {
      this.viewLoading = true
      getNodeItem(node.id)
        .then(res => {
          this.$set(node, 'console_info', res.data.data.console_info)
          this.viewLoading = false
        })
        .catch(() => {
          this.viewLoading = false
        })
    },
    setFocus(node) {
      document.getElementById(node.id).contentWindow.focus()
    },
    switchShowList() {
      this.workstation = null
      this.showList = true
      this.showTopo = false
    },
    // 展示/隐藏拓扑
    switchShowTopo() {
      this.workstation = null
      this.showList = false
      this.showTopo = true
    },
    // 折叠面板时
    handleFold(flag) {
      this.fold = flag
      if (flag) {
        this.percent = this.minPercent
      } else {
        this.percent = 30
      }
    },
    // 拖拽面板时
    resize(val) {
      this.percent = val
    },
    // 拓扑中节点右键控制台的回调
    consoleUrl(type, url, nodeId) {
      const node = this.topologyNodesConsole.find(item => item.id === nodeId)
      this.workstation = node
      this.showList = false
      this.showTopo = false
      const hasNode = this.openedConsole.find(item => item.id === nodeId)
      // 如果node没有点击过，则插入数组
      if (!hasNode) {
        this.openedConsole.unshift(node)
      }
      // 如果点击的node的控制台类型跟之前不一样，或者没有打开过node，则去请求控制台地址
      if ((hasNode && hasNode.workstationType !== type) || !hasNode) {
        this.$set(node, 'workstationUrl', url)
        this.$set(node, 'workstationType', type)
        this.showTopo = false
      }
    },
    clickDrop(console_type, node) {
      this.workstation = node
      this.showList = false
      this.showTopo = false
      // 如果header中点击已经存在node，则不重新获取控制台链接
      if (!console_type) {
        return
      }
      const hasNode = this.openedConsole.find(item => item.id === node.id)
      // 如果node没有点击过，则插入数组
      if (!hasNode) {
        this.openedConsole.unshift(node)
        if (node.status != 'running') {
          this.$message.warning(`操作机${node.name}未处于运行中状态`)
          return
        }
      }
      // 如果点击的node的控制台类型跟之前不一样，或者没有打开过node，则去请求控制台地址
      if ((hasNode && hasNode.workstationType !== console_type) || !hasNode) {
        if (console_type === 'vnc') {
          this.loading = true
          this.getVnc(node.id)
        } else if (console_type === 'rdp' || console_type === 'ssh' || console_type === 'webshell') {
          this.loading = true
          this.getConsole(node.id, console_type)
        } else if (console_type === 'serial') {
          this.loading = true
          this.getSerial(node.id, node.name)
        }
      }
    },
    closeConsole(nodeId) {
      if (this.workstation && this.workstation.id === nodeId) {
        this.workstation = null
        this.showList = true
        this.showTopo = false
      }
      const index = this.openedConsole.findIndex(item => item.id === nodeId)
      this.openedConsole.splice(index, 1)
    },
    // socket消息处理
    'socketHandle': function(nval, oval) {
      const option = nval.payload.hasOwnProperty('option') ? nval.payload['option'] : (nval.payload.hasOwnProperty('event_type') ? nval.payload['event_type'] : null)
      const status = nval.payload.hasOwnProperty('state') ? nval.payload['state'] : null
      const id = nval.payload.hasOwnProperty('resource_id') ? nval.payload['resource_id'] : null
      if (option === 'delete' || option === 'created' || status === 'created') {
        return
      } else {
        if (status) {
          this.topologyNodes.forEach(item => {
            if (item.id === id) {
              this.$set(item, 'status', status)
            }
          })
        }
      }
    },
    // （共享）根据场景实例id获取topologyId
    getTopologyBySceneInstanceId() {
      if (!this.sceneInstanceId) {
        this.loading = false
        return this.$message.warning(`课程内容【${this.$route.query.name}】暂无场景数据`)
      }
      getTopologyIdBySceneInstanceId(this.sceneInstanceId).then(res => {
        const postData = new FormData()
        const topologyId = res.data.result
        postData.append('topologyId', res.data.result)
        topologyQueryById(postData).then(res => {
          if (res.data) {
            this.loading = false
            this.sourceSceneId = this.sceneInstanceId
            this.topologyId = topologyId
            this.getTopoNodes()
          } else {
            this.loading = false
            this.$message.error('当前课程内容缺少场景，无法正常加载。请确认场景状态或联系管理员。')
            this.topologyId = ''
          }
        })
      }).catch(() => {
        this.loading = false
        this.description = '暂无数据'
      })
    },
    // （共享）获取场景模版id
    getContentByIdFn() {
      const params = { id: this.contentId }
      getContentById(params).then(res => {
        if (res.code === 0) {
          this.sourceSceneId = res.data.sourceSceneIdStr
          this.contentTopologyFn()
        }
      })
    },
    // （共享）根据场景模版id获取topologyId
    contentTopologyFn() {
      if (this.sourceSceneId) {
        getSceneBasicInfoAPI(this.sourceSceneId).then(res => {
          if (res.data) {
            const postData = new FormData()
            const topologyId = res.data.topologyTemplateId
            postData.append('topologyId', res.data.topologyTemplateId)
            topologyQueryById(postData).then(res => {
              if (res.data) {
                this.loading = false
                this.topologyId = topologyId
                this.showTopo = true
                this.topoWidth = '100%'
                this.topoHeight = '100%'
              } else {
                this.loading = false
                this.$message.error('当前课程内容缺少场景，无法正常加载。请确认场景状态或联系管理员。')
                this.topologyId = ''
              }
            })
          } else {
            this.loading = false
            this.$message.error('当前课程内容缺少场景，无法正常加载。请确认场景状态或联系管理员。')
            this.topologyId = ''
          }
        })
      } else {
        this.loading = false
        this.topologyId = ''
      }
    },
    // （独享）(获取拓扑课程内容的拓扑id)
    contentTopology() {
      contentTopology({ contentId: this.contentId, schedulingCode: this.$route.query.schedulingId }).then(res => {
        const postData = new FormData()
        const topologyId = res.data.topologyInsId || res.data.topologyId
        postData.append('topologyId', res.data.topologyInsId || res.data.topologyId)
        topologyQueryById(postData).then(res => {
          if (res.data) {
            this.topologyId = topologyId
            this.getTopoNodes()
          } else {
            this.$message.error('当前课程内容缺少场景，无法正常加载。请确认场景状态或联系管理员。')
            this.loading = false
            this.topologyId = ''
          }
        })
      }).catch(() => {
        this.loading = false
        this.description = '暂无数据'
      })
    },
    // （独享）(实例化新的拓扑实例id)
    getTopologyStart() {
      getTopologyStart({ contentId: this.contentId, schedulingCode: this.$route.query.schedulingId }).then(res => {
        const postData = new FormData()
        const topologyId = res.data.topologyInsId || res.data.topologyId
        postData.append('topologyId', res.data.topologyInsId || res.data.topologyId)
        topologyQueryById(postData).then(res => {
          if (res.data) {
            this.topologyId = topologyId
            this.getTopoNodes()
          } else {
            this.$message.error('当前课程内容缺少场景，无法正常加载。请确认场景状态或联系管理员。')
            this.loading = false
            this.topologyId = ''
          }
        })
      }).catch(() => {
        this.loading = false
        this.description = '暂无数据'
      })
    },
    // 获取网站信息详情
    getDetail() {
      queryBaseInfoConfig().then((res) => {
        this.config = res.data.data[0]
      })
    },
    // 获取拓扑中的节点
    getTopoNodes() {
      return new Promise((resolve, reject) => {
        getItem(this.topologyId)
          .then(res => {
            this.topologyNodes = res.data.data.nodes
            const topologyNodesConsole = this.topologyNodes.filter(item => item.console_type && item.resource_type == 'vnf' && (item.virtual_type === 'qemu' || item.virtual_type === 'docker'))
            this.topoRunning = topologyNodesConsole.some(item => item.status == 'running')
            if (!topologyNodesConsole.length) {
              this.loading = false
              this.description = '暂未有控制台节点'
            } else {
              this.loading = false
            }
            resolve()
          })
          .catch(() => {
            reject()
          })
      })
    },
    // 启动拓扑
    startTopology() {
      this.loading = false
      this.showMasking = true
      this.percentageLabel = '启动实验环境中'
      this.percentageData = {
        total: 0,
        success: 0,
        error: 0
      }
      const postData = new FormData()
      postData.append('topologyId', this.topologyId)
      sceneStartTopoApi(postData)
        .then(res => {})
        .catch(() => {
          this.$message.error('启动实验环境失败')
          this.description = '启动实验环境失败'
          this.loading = false
        })
    },
    // 获取vnc
    getVnc(id) {
      const current = this.openedConsole.find(item => item.id === id)
      this.$set(current, 'workstationType', 'vnc')
      vnc(id)
        .then(res => {
          this.$set(current, 'workstationUrl', res.data.data)
          this.showTopo = false
          this.loading = false
        })
        .catch(() => {
          this.$set(current, 'workstationUrl', '')
          this.description = '打开控制台失败'
          this.loading = false
        })
    },
    // 获取rdp、ssh
    getConsole(id, console_type) {
      const current = this.openedConsole.find(item => item.id === id)
      this.$set(current, 'workstationType', console_type)
      getConsole(id, { console_type })
        .then(res => {
          this.$set(current, 'workstationUrl', (window.location.hostname == 'localhost' ? window.WEB_CONFIG.VIP_URL : '') + res.data.data)
          this.showTopo = false
          this.loading = false
        })
        .catch(() => {
          this.$set(current, 'workstationUrl', '')
          this.description = '打开控制台失败'
          this.loading = false
        })
    },
    // 获取serial
    getSerial(id, name) {
      const current = this.openedConsole.find(item => item.id === id)
      this.$set(current, 'workstationType', 'serial')
      serial(id).then(res => {
        this.$set(current, 'workstationUrl', res.data.data + '&title=' + name)
        this.showTopo = false
        this.loading = false
      }).catch(() => {
        this.$set(current, 'workstationUrl', '')
        this.description = '打开控制台失败'
        this.loading = false
      })
    }
  }
}
</script>

<style lang="scss">
.el-dropdown-menu.el-popper {
  max-height: 400px;
  overflow-y: auto;
}
.el-dropdown-menu.el-popper[x-placement^="left"] {
  margin-right: 1px;
}
.el-dropdown-menu.console-list-dropdown {
  &.el-popper[x-placement^=bottom] {
    margin-top: 5px !important;
  }
  width: 150px;
  background: var(--color-400);
  border: 1px solid var(--color-400);
  .el-dropdown-menu__item {
    color: #fff !important;
    font-size: 14px !important;
    padding: 0 10px !important;
    font-weight: 500;
    i {
      margin-right: 2px;
      font-size: 14px;
      font-weight: 700;
    }
    &:focus, &:not(.is-disabled):hover {
      background-color: var(--color-600) !important;
      color: #fff !important;
    }
  }
}
.view-wrap {
  padding: 0;
  .view-content {
    padding: 5px 12px;
    >div {
      width: 100%;
      color: #333;
      font-weight: 500;
      margin: 10px 0;
      display: flex;
      >div:first-child {
        width: 105px;
      }
    }
  }
  .ip-wrap {
    display: flex;
  }
}
</style>
<style lang="scss" scoped>
.experiment-topo {
  height: 100%;
  display: flex;
  flex-direction: column;
  .cr-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 60px;
    border-bottom: 1px solid #e6e6e6;
    background-color: #fff;
    .cr-left {
      display: flex;
      align-items: center;
      color: var(--neutral-0);
      font-size: 14px;
      font-weight: 900;
      .cr-image {
        margin-left: 24px;
        margin-right: 8px;
        height: 42px;
        max-width: 300px;
      }
    }
    .cr-right {
      margin-right: 10px;
      flex: 1;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      color: var(--neutral-700);
      .console-fold {
        margin-right: 15px;
        color: var(--neutral-700);
        &:hover {
          color: var(--color-600);
        }
        cursor: pointer;
        i {
          font-weight: bold;
          margin-left: 3px;
        }
        .rotate90deg {
          transform: rotate(90deg);
        }
        .rotate-90deg {
          transform: rotate(-90deg);
        }
      }
      .right-item {
        font-size: 14px;
        padding: 0 10px !important;
        i {
          margin-right: 3px;
          font-size: 16px;
        }
      }
      ::v-deep .el-tag {
        cursor: pointer;
        font-weight: 700;
        display: inline-block;
        vertical-align: top;
        max-width: 180px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        position: relative;
        padding: 0 24px 0 30px;
        margin: 0 10px;
        &.el-tag--dark.el-tag--info {
          padding-left: 10px;
          background-color: transparent;
          border-color: var(--color-600);
          color: var(--color-600);
          .el-tag__close {
            color: var(--color-600);
            &:hover {
              background-color: var(--color-100);
            }
          }
        }
        .cr-icon-diannao {
          font-size: 16px;
          font-weight: bold;
          position: absolute;
          top: 6px;
          left: 8px;
        }
        .el-tag__close {
          position: absolute;
          top: 7px;
          right: 5px;
        }
      }
      .el-divider {
        height: 20px;
        width: 2px;
      }
    }
  }
  .topo-content {
    position: relative;
    flex: 1;
    min-height: 0;
    .fold-wrap {
      position: absolute;
      top: calc(50% - 80px);
      width: 10px;
      height: 60px;
      line-height: 60px;;
      background-color: var(--color-600);
      overflow: hidden;
      cursor: pointer;
      z-index: 9;
      i {
        color: #fff;
        margin-left: -2px;
      }
    }
    .paneR {
      height: 100%;
      border-left: 1px solid #dcdee2;
      position: relative;
      .console-wrap {
        position: absolute;
        width: 100%;
        height: 100%;
        .console-container {
          position: absolute;
          width: 100%;
          height: 100%;
          background: #fff;
          overflow: hidden;
        }
      }
      .console-list {
        background: #f0f4f9;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        position: absolute;
        overflow-y: auto;
        .start-title {
          color: #333;
          font-size: 24px;
          font-weight: bold;
          text-align: center;
          margin: 70px 0;
        }
        .console-button {
          border: 1px solid var(--color-600);
          border-radius: 8px;
          padding: 50px 20px 20px 20px;
          width: 90%;
          flex: 1;
          min-height: 0;
          overflow: auto;
          margin: 0 auto 50px auto;
          display: flex;
          justify-content: center;
          flex-wrap: wrap;
          column-gap: 30px;
          font-size: 15px;
          cursor: default;
          .console-item {
            width: 350px;
            height: fit-content;
            background: rgba(7, 128, 155, .1);
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-direction: column;
            border: 1px solid var(--color-600);
            border-radius: 8px;
            padding-bottom: 15px;
            margin-bottom: 30px;
            img {
              padding: 15px 15px 0 15px;
              width: 100%;
            }
            >div {
              width: 100%;
              color: #333;
              font-weight: 500;
              margin-top: 10px;
              padding: 0 15px;
              &:first-child {
                margin-top: 0px;
                font-size: 16px;
                border-bottom: 1px solid var(--color-600);
                padding: 10px 15px;
                font-weight: bold;
              }
            }
            .console-view {
              display: flex;
              justify-content: space-between;
              width: 100%;
              > span {
                width: 47%;
              }
              .el-button {
                width: 100%;
                height: 36px;
                font-weight: bold;
              }
              .el-dropdown {
                color: #fff;
                background: var(--color-600);
                width: 47%;
                height: 36px;
                line-height: 36px;
                border-radius: 4px;
                text-align: center;
                cursor: pointer;
              }
            }
          }
          .perch {
            width: 350px;
          }
        }
      }
      .topo-start-wrap {
        background: #383838;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;
        position: absolute;
        .start-title {
          color: #fff;
          font-size: 24px;
          opacity: .8;
          text-align: center;
        }
        .start-button {
          width: 200px;
          border: 1px solid #2e7eee;
          border-radius: 8px;
          padding: 1px;
          display: flex;
          flex-direction: column;
          margin: 0 auto;
          cursor: pointer;
          &.console-button {
            width: 350px;
            cursor: default;
            padding: 12px 10px;
            background: rgba(52, 186, 255, .1);
            border-color: var(--color-700);
            &:hover {
              border-width: 1px;
            }
          }
          &:hover {
            border-width: 3px;
          }
          .el-button {
            margin: 2px 0;
            width: 100%;
            color: #2e7eee;
          }
          img {
            width: 100%;
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
          }
          &.notext img {
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
          }
          .console-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 4px 15px;
            background: rgba(7, 128, 155, .1);
            margin-bottom: 5px;
            >div {
              flex: 1;
              min-width: 0;
              color: #fff;
              font-weight: 500;
            }
            .el-button {
              width: auto;
              color: var(--color-600);
            }
          }
        }
      }
    }
    ::v-deep .vue-splitter-container {
      width: 100%;
      .splitter-pane.vertical.splitter-paneL {
        padding-right: 10px;
      }
      .splitter-pane.vertical.splitter-paneR {
        padding-left: 0px;
      }
      .splitter-pane.horizontal.splitter-paneL {
        padding-bottom: 10px;
      }
      .splitter-pane.horizontal.splitter-paneR {
        padding-top: 5px;
      }
      .splitter-pane-resizer.horizontal {
        height: 10px;
        margin: -10px 0 0 0;
        opacity: 1;
        &::after {
          transform: rotate(90deg);
          content: '|||';
          font-size: 18px;
          letter-spacing: -1.5px;
          color: var(--color-600);
          position: absolute;
          top: 0%;
          bottom: 0%;
          margin-top: -30px;
          left: 50%;
          right: 50%;
          height: 60px;
          line-height: 60px;
          width: 10px;
          background: rgba(2, 91, 212, 0.2);
          pointer-events: none;
        }
      }
      .splitter-pane-resizer.vertical {
        width: 10px;
        margin-left: -10px;
        opacity: 1;
        &::after {
          content: '|||';
          font-size: 18px;
          letter-spacing: -1.5px;
          color: var(--color-600);
          position: absolute;
          top: 50%;
          bottom: 50%;
          margin-top: -20px;
          left: -5px;
          right: 0;
          height: 60px;
          line-height: 60px;
          width: 10px;
          background: rgba(2, 91, 212, .2);
          pointer-events: none;
        }
      }
    }
  }
  .topo-masking {
    position: absolute;
    height: 100%;
    width: 100%;
    background: linear-gradient(rgba(0, 0, 0, 0.15), rgba(0, 0, 0, 0.15)), rgba(255, 255, 255, 0.9);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 999;
    .topo-masking-close {
      position: absolute;
      top: 15px;
      right: 15px;
      font-size: 20px;
      cursor: pointer;
    }
    ::v-deep .el-progress {
      width: 50%;
      margin-top: -90px;
    }
    &.percentage-0 {
      ::v-deep .el-progress-bar__innerText {
        color: rgba(0, 0, 0, 0.85);
      }
    }
  }
  ::v-deep .orchestration-create-warp {
    position: absolute;
    right: 0;
    top: 0;
    z-index: 99;
  }
  ::v-deep .serial-console {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    .terminal {
      flex: 1;
      min-height: 0;
    }
    .status {
      height: 30px;
      line-height: 20px;
      padding-left: 15px;
    }
  }
  ::v-deep .el-empty__description {
    margin-top: 0;
    color: var(--neutral-600);
  }
}
</style>
