<template>
  <div id="top5-chart" ref="topFiveChart" />
</template>
<script>
import * as echarts from 'echarts'
export default {
  props: {
    data: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {}
  },
  watch: {
    data: {
      handler(newVal) {
        if (newVal) {
          this.initChart()
        }
      },
      deep: true,
      immediate: true
    }
  },
  mounted() {
    this.initChart()
  },
  methods: {
    initChart() {
      let myChart = null
      if (!this.$refs.topFiveChart) {
        return
      }
      myChart = echarts.init(this.$refs.topFiveChart)
      const option = {
        backgroundColor: '#fff',
        grid: {
          top: 0,
          left: 180,
          right: 40,
          bottom: 0,
          containLabel: true
        },
        xAxis: {
          type: 'value',
          show: false
        },
        yAxis: {
          show: true,
          inverse: true,
          type: 'category',
          axisLine: {
            show: false
          }
        },
        series: [
          {
            name: 'label',
            type: 'bar',
            barWidth: 20,
            yAxisIndex: 0,
            label: {
              show: true,
              position: [-180, 30],
              color: '#333',
              fontSize: 16,
              align: 'left'
            },
            data: this.data.map((item) => {
              return {
                value: 0,
                label: {
                  formatter: function() {
                    return '{a|' + item.content + '}{b|' + item.createdBy + '}'
                  },
                  rich: {
                    a: {
                      width: 110,
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap',
                      color: '#333',
                      fontSize: 16
                    }
                  }
                }
              }
            })
          },
          {
            name: 'value',
            type: 'bar',
            barWidth: 25,
            barMinHeight: 20, // 最小高度
            yAxisIndex: 0,
            label: {
              show: true,
              position: ['100%', '110%'],
              fontSize: 16,
              offset: [10, -22]
            },
            itemStyle: {
              barBorderRadius: [0, 0, 0, 0]
            },
            data: this.data.map(({
              value
            }) => {
              return {
                value,
                label: {
                  color: '#333',
                  formatter() {
                    return `${value}次`
                  }
                },
                itemStyle: {
                  color: '#4080FF'
                }
              }
            })
          }
        ]
      }
      myChart.setOption(option)
      window.onresize = function() {
        myChart.resize()
      }
    }
  }
}
</script>



