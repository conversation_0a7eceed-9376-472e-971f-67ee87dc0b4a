<template>
  <div class="content-wrap-layout">
    <top-nav />
    <div class="vertical-wrap">
      <tree :category-name="moduleName" @setClassCode="setClassCode"/>
      <page-table
        ref="table"
        :default-selected-arr="defaultSelectedArr"
        :filter-data="{ 'majorCode': majorCode, 'majorType': majorType }"
        :cache-pattern="true"
        @refresh="refresh"
        @link-event="linkEvent"
        @on-select="tabelSelect"
        @on-current="tabelCurrent"
      >
        <action-menu
          slot="action"
          :major-code="majorCode"
          :professionl-major-name="majorName"
          :module-name="moduleName"
          :select-item="selectItem"
          @call="actionHandler"
        />
      </page-table>
    </div>
    <el-drawer
      :visible.sync="detailShow"
      :modal="false"
      size="75%"
      append-to-body
      class="detail-view"
      @close="closeDetail"
    >
      <t-detail v-if="detailShow" />
    </el-drawer>
  </div>
</template>

<script>
import moduleConf from './config'
import topNav from '../index_top_nav'
import pageTable from './table/index.vue'
import actionMenu from './action/index.vue'
import tree from './tree/index.vue'
import tDetail from './detail/index.vue'
import moduleMixin from '@/packages/mixins/module_list'
export default {
  name: 'ClassManage',
  components: {
    topNav,
    pageTable,
    actionMenu,
    tree,
    tDetail
  },
  mixins: [moduleMixin],
  data() {
    return {
      listRouterName: 'classManage',
      moduleName: moduleConf.name,
      selectItem: [],
      defaultSelectedArr: [],
      majorCode: '',
      majorName: '',
      uploadData: {},
      majorType: 0
    }
  },
  methods: {
    'setClassCode': function(value, value2, type) {
      this.majorCode = value
      this.majorName = value2
      this.majorType = type
      this.$nextTick(() => {
        this.$refs['table'].getList()
      })
    },
    // 列表点击
    linkEvent: function({ name, row, params }) {
      this.$router.push({ name: name, params: params })
    },
    // 返回已选
    tabelSelect: function(data) {
      console.log(data)
      this.selectItem = data
    },
    // 返回单选
    tabelCurrent: function(row) {
      this.selectItem = [row]
    },
    // action menu 事件
    actionHandler: function(type, data) {
      switch (type) {
        case 'refresh':
          this.$refs['table'].getList()
      }
    },
    refresh: function() {
    }
  }
}
</script>
