<template>
  <!-- 搜索 -->
  <div class="btn-div">
    <div class="search_box">
      <el-input
        v-model.trim="input"
        placeholder="搜索关键字"
        clearable
        @change="change"
        @clear="clear"
      />
      <div class="search_btn">
        <i class="el-icon-search"/>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      input: ''
    }
  },
  methods: {
    change() {
      this.$emit('search', this.input)
    },
    clear() {
      this.$emit('clear', this.input)
    }
  }
}
</script>

<style lang="scss" scoped>
.btn-div {
  border: 1px;
  ::v-deep {
    .el-input__inner {
      padding-right: 60px;
    }
  }
  .search_box {
    display: flex;
    margin: 10px 0;
    ::v-deep {
      .el-input {
        width: 246px;
      }
    }
    .search_btn {
      width: 64px;
      height: 32px;
      background: var(--color-600);
      border-radius: 0px 4px 4px 0px;
      opacity: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      color: #ffffff;
    }
    .search_btn:hover {
      background: #0e42d2;
    }
  }
}
</style>
