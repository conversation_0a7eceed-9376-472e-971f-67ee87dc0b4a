<template>
  <div :class="role != 2 ? 'live_wrap' : 'live_wrap_ro'">
    <div v-if="type === '编辑'||type === '创建'" class="dialog-wrap">
      <el-form ref="form" :model="formData" :rules="rules" label-position="left" label-width="80px">
        <el-form-item label="直播地址" prop="realcontent">
          <el-input v-model.trim="formData.realcontent"/>
        </el-form-item>
      </el-form>
      <div class="dialog-footer">
        <el-button type="primary" @click="saveLiveUrl">确定</el-button>
      </div>
    </div>
    <div v-else :class="role != 2 ? 'live_context' : 'live_context_ro'">
      <div v-if="liveUrl && role != 2">
        <div>
          直播地址：
          <span>{{ liveUrl }}</span>
          <i class="el-icon-document-copy ml-3" style="color: #33CCFF;cursor:pointer" @click="copyMsg(liveUrl)"/>
        </div>
      </div>
      <div v-else-if="liveUrl && role == 2" style="height: 100%;width: 100%;">
        <iframe v-if="liveStatus == 1" :src="liveUrl" width="100%" height="100%" frameborder="0" allowfullscreen="true" webkitallowfullscreen="true" mozallowfullscreen="true" oallowfullscreen="true" msallowfullscreen="true" />
        <div v-else class="no-live-wrap">
          <div>{{ contentName }}</div>
          <el-empty :image="img" :image-size="230" class="no-live">
            <div slot="description">
              <div style="font-size: 16px;">{{ liveText }}</div>
            </div>
          </el-empty>
        </div>
      </div>
      <div v-else>直播地址：无</div>
    </div>
  </div>
</template>

<script>
import { liveSave } from '@/api/teacher/index.js'
export default {
  name: 'LiveTelecastUrl',
  components: {},
  props: {
    type: String,
    // 1教师2学生3班主任
    role: {
      type: Number,
      default: 2
    },
    id: [Number, String],
    liveUrl: {
      type: String,
      default: ''
    },
    liveStatus: [Number, String],
    schedulingCode: {
      type: String,
      default: ''
    },
    contentName: {
      type: String,
      default: ''
    },
    beginTime: {
      type: String,
      default: ''
    },
    endTime: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      img: require('./noLive.png'),
      teacherUrl: '',
      studentUrl: '',
      formData: {
        realcontent: ''
      },
      rules: {
        realcontent: [
          { required: true, message: '直播地址不能为空', trigger: 'blur' },
          {
            required: false,
            message: '请输入正确url地址',
            type: 'url',
            trigger: 'change'
          }
        ]
      }
    }
  },
  liveText() {
    if (this.beginTime && this.endTime) {
      const beginTime = new Date(this.beginTime).getTime()
      const endTime = new Date(this.endTime).getTime()
      const nowTime = new Date().getTime()
      if (beginTime > nowTime) { // 未开始
        return '尚未开始'
      } else if (endTime > nowTime) { // 进行中
        return '暂停直播'
      } else if (endTime < nowTime) { // 已结束
        return '直播结束'
      }
    } else {
      return '尚未开始'
    }
  },
  watch: {
    liveUrl: {
      handler(newVal) {
        // 对象发生变化时的处理逻辑
        this.liveUrl = newVal
        this.formData.realcontent = newVal
      },
      deep: true
    }
  },
  mounted() {
    this.studentUrl = this.teacherUrl = this.formData.realcontent = this.liveUrl
  },
  methods: {
    copyMsg(data) {
      const url = data
      const oInput = document.createElement('input')
      oInput.value = url
      document.body.appendChild(oInput)
      oInput.select()
      document.execCommand('Copy')
      this.$message({
        message: '已成功复制到剪切板',
        type: 'success'
      })
      oInput.remove()
    },
    saveLiveUrl() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          liveSave({ contentId: this.id || this.$route.query.id, liveUrl: this.formData.realcontent }).then(res => {
            this.$message.success(`编辑成功`)
            this.$emit('setUrl', this.formData.realcontent)
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.no-live-wrap {
  display: flex;
  flex-direction: column;
  align-items: center;
  >div:first-child {
    font-size: 22px;
    margin: 80px 0 10px 0;
  }
  >div:nth-child(2) {
    font-size: 15px;
    margin: 0px 0 10px 0;
  }
}
.no-live {
  height: 100%;
  color: var(--neutral-700);
}
.live_wrap_ro {
  color: #fff;
  height:100%;
  .live_context_ro {
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
.live_wrap {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  font-size: 18px;
  .live_title {}
  .live_context {
    margin: 15px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 50px;
    font-size: 14px;
    .link {
      max-width: 800px;
      font-size: 16px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}
.change-live-status {
  margin-top: 30px;
  width: 100%;
  display: flex;
  justify-content: space-around;
}
</style>

