<template>
  <div class="resource-table">
    <!-- 操作区 -->
    <div class="operation-wrap">
      <div class="operation-left">
        <slot name="action" />
        <el-button type="primary" icon="el-icon-refresh" @click="refresh" />
      </div>
      <div class="operation-right">
        <el-badge :value="searchBtnShowNum">
          <el-button icon="el-icon-search" @click="openSearch">搜索</el-button>
        </el-badge>
        <!-- 自定义表格列 -->
        <t-table-config
          v-if="!customColData.length"
          :data="columnsObj"
          :active-key-arr="columnsViewArr"
          @on-change-col="onChangeCol"
        />
      </div>
    </div>
    <!-- 搜索区 -->
    <t-search-box
      v-show="searchView"
      :search-key-list="searchKeyListView"
      default-placeholder="默认搜索姓名"
      @search="searchMultiple"
    />
    <!-- 列表 -->
    <t-table-view
      ref="tableView"
      :height="height"
      :single="single"
      :loading="tableLoading"
      :data="tableData"
      :total="tableTotal"
      :page-size="pageSize"
      :current="pageCurrent"
      :select-item="selectItem"
      row-key="userId"
      current-key="userId"
      @on-select="onSelect"
      @on-current="onCurrent"
      @on-change="changePage"
      @on-sort-change="onSortChange"
      @on-page-size-change="onPageSizeChange"
    >
      <el-table-column
        label="序号"
        fixed="left"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span>{{ getIndex(scope.$index) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        v-for="item in columnsViewArr"
        :key="item"
        :min-width="colMinWidth"
        :label="columnsObj[item].title"
        :fixed="columnsObj[item].master ? 'left' : false"
        :show-overflow-tooltip="item !== 'pointNames'"
      >
        <template slot-scope="scope">
          <!-- 名称 -->
          <span v-if="item === 'realName' && link">
            <a
              :href="`/analysis/ability/detail/${scope.row.userId}/overview`"
              @click.prevent="linkEvent(`abilityDetail`, scope.row, { id: scope.row.userId, view: 'overview' })"
            >{{ scope.row[item] || "-" }}</a>
          </span>
          <span v-else-if="item === 'sex' && scope.row[item]">{{ scope.row[item] === 1 ? '男' : '女' }}</span>
          <span v-else-if="item === 'sex' && !scope.row[item]">-</span>
          <span v-else-if="item === 'pointNames' && scope.row[item]">
            <table-td-multi-col :data="scope.row.pointNames.split(',')" :number="scope.row.pointNames.split(',').length">
              <div slot="reference">{{ scope.row.pointNames.split(',')[0] }}</div>
              <div v-for="val in scope.row.pointNames.split(',')" :key="val">{{ val }}</div>
            </table-td-multi-col>
          </span>
          <span v-else>{{ scope.row[item] || "-" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span :class="scope.row.pointNames && scope.row.pointNames.split(',').length >= 3 ? 'allow-atlas':'no-atlas'" @click="openSkillMap(scope.row)">技能图谱</span>
        </template>
      </el-table-column>
    </t-table-view>
    <!-- 侧拉弹窗 start -->
    <el-drawer
      :visible.sync="drawerShow"
      size="70%"
      title="技能图谱"
      append-to-body
    >
      <skill-map v-if="drawerShow" :type="1" :id="String(userId)"/>
    </el-drawer>
    <!-- 侧拉弹窗 end -->
  </div>
</template>

<script>
import moduleConf from '../config.js'
import tSearchBox from '@/packages/search-box/index.vue'
import tTableView from '@/packages/table-view/index.vue'
import skillMap from '../action/skillMap'
import tTableConfig from '@/packages/table-config/table-col-config.vue'
import tableTdMultiCol from '@/packages/table-config/table-td-multi-col.vue'
import mixinsPageTable from '@/packages/mixins/page_table'
import { getUserSkillPointList } from '@/api/accumulate/skillPoint.js'

export default {
  components: {
    tSearchBox,
    tTableView,
    skillMap,
    tTableConfig,
    tableTdMultiCol
  },
  mixins: [mixinsPageTable],
  data() {
    return {
      moduleConf: moduleConf,
      moduleName: moduleConf.name,
      userId: '',
      drawerShow: false,
      // 搜索配置项
      searchKeyList: [
        { key: 'realName', label: '姓名', master: true, placeholder: '默认搜索姓名' },
        { key: 'userName', label: '账号' },
        { key: 'sex', label: '性别', type: 'radio', valueList: [{ label: '男', value: '1' }, { label: '女', value: '2' }] }
      ],
      // 所有可配置显示列 master：不可隐藏 title:列名称
      columnsObj: {
        'realName': { title: '姓名', master: true },
        'userName': { title: '账号' },
        'sex': { title: '性别' },
        'pointNames': { title: '技能点' }
      },
      // 当前显示列key表 默认，如果localStorage有数据将被覆盖
      columnsViewArr: [
        'realName',
        'userName',
        'sex',
        'pointNames'
      ]
    }
  },
  methods: {
    getIndex(index) {
      const params = this.getPostData('page', 'limit')
      return (params.page - 1) * params.limit + index + 1
    },
    openSkillMap(row) {
      if (row.pointNames.split(',').length < 3) {
        return
      }
      this.drawerShow = true
      this.userId = row.userId
    },
    getList: function(showLoading = true) {
      if (showLoading) {
        this.tableLoading = true
      }
      const params = this.getPostData('page', 'limit')
      getUserSkillPointList(params).then((res) => {
        if (res.code === 0) {
          this.tableData = res.data.records
          this.tableTotal = res.data.total
          this.tableLoading = false
          this.handleSelection()
        }
      }).catch(() => {
        this.tableLoading = false
      })
    }
  }
}
</script>
<style scoped>
.allow-atlas {
  cursor: pointer;
  color: var(--color-600);
}
.no-atlas {
  cursor: no-drop;
  color: #999;
}
</style>
