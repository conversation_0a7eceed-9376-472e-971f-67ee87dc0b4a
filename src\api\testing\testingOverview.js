import request from '@/utils/request'
const headers = { 'Content-Type': 'application/json;charset=UTF-8' }

// 检测项目数量统计接口
export function projectCount(params) {
  return request({
    url: '/testing/overview/project/count',
    method: 'get',
    params,
    headers
  })
}
// 查询检查流程列表
export function processList(data) {
  return request({
    url: '/testing/overview/process/list',
    method: 'post',
    data,
    headers
  })
}
// 待确认申请分页
export function unconfirmedApplication(data) {
  return request({
    url: '/testing/overview/unconfirmed/application',
    method: 'post',
    data,
    headers
  })
}
// 未关联项目的检测申请分页
export function unrelatedApplication(data) {
  return request({
    url: '/testing/overview/unrelated/application',
    method: 'post',
    data,
    headers
  })
}
// 待审核的检测项目分页
export function projectPendingAuditReportPage(data) {
  return request({
    url: '/testing/overview/project/pending/audit/report/page',
    method: 'post',
    data,
    headers
  })
}
// 问题数量统计接口
export function problemCount(params) {
  return request({
    url: '/testing/overview/problem/count',
    method: 'get',
    params,
    headers
  })
}
// 主管待办
export function managerTodo(params) {
  return request({
    url: '/testing/overview/manager/todo',
    method: 'get',
    params,
    headers
  })
}
// 测试人员代办
export function testerTodo(params) {
  return request({
    url: '/testing/overview/tester/todo',
    method: 'get',
    params,
    headers
  })
}
// 测试人员待办任务分页接口
export function testerTodoPage(data) {
  return request({
    url: '/testing/overview/tester/todo/page',
    method: 'post',
    data,
    headers
  })
}
// 测试人员待办问题分页
export function testerTodoIssuePage(data) {
  return request({
    url: '/testing/overview/tester/todo/issue/page',
    method: 'post',
    data,
    headers
  })
}
// 厂商人员待办
export function vendorTodo(params) {
  return request({
    url: '/testing/overview/vendor/todo',
    method: 'get',
    params,
    headers
  })
}
// 厂商人员待提交分页
export function vendorTodoPage(data) {
  return request({
    url: '/testing/overview/vendor/todo/page',
    method: 'post',
    data,
    headers
  })
}
// 检测项目负责人待办
export function projectManagerTodo(params) {
  return request({
    url: '/testing/overview/projectManager/todo',
    method: 'get',
    params,
    headers
  })
}
// 厂商人员检查项目分页
export function vendorProjectPage(data) {
  return request({
    url: '/testing/overview/vendor/project/page',
    method: 'post',
    data,
    headers
  })
}
// 检测申请统计接口
export function detectionApplicationCount(params) {
  return request({
    url: '/testing/overview/detection/application/count',
    method: 'get',
    params,
    headers
  })
}
// 未完成检测项目统计接口
export function unFinishProject(params) {
  return request({
    url: '/testing/overview/unFinish/project',
    method: 'get',
    params,
    headers
  })
}
// 在测项目进度图表接口
export function projectProgress(params) {
  return request({
    url: '/testing/overview/project/progress',
    method: 'get',
    params,
    headers
  })
}
// 未关闭问题图表接口
export function unclosedIssues(params) {
  return request({
    url: '/testing/overview/unclosed/issues',
    method: 'get',
    params,
    headers
  })
}
// 执行测试用例统计接口
export function executeCase(params) {
  return request({
    url: '/testing/overview/execute/case',
    method: 'get',
    params,
    headers
  })
}
// 问题统计接口
export function problemStatistics(params) {
  return request({
    url: '/testing/overview/problem/statistics',
    method: 'get',
    params,
    headers
  })
}
// 项目问题数量排行接口
export function projectProblemRanking(params) {
  return request({
    url: '/testing/overview/project/problem/ranking',
    method: 'get',
    params,
    headers
  })
}
// 我参与项目的任务统计接口
export function participatedProjectsTask(params) {
  return request({
    url: '/testing/overview/participated/projects/task',
    method: 'get',
    params,
    headers
  })
}
// 检测项目负责人我参与项目
export function managerParticipatedProjectsTask(params) {
  return request({
    url: '/testing/overview/manager/participated/projects/task',
    method: 'get',
    params,
    headers
  })
}
// 厂商人员检查项目
export function manufacturerProject(params) {
  return request({
    url: '/testing/overview/manufacturer/project',
    method: 'get',
    params,
    headers
  })
}
// 获取配置页面布局
export function configLayoutInfo(params) {
  return request({
    url: '/testing/overview/config/layout/info',
    method: 'get',
    params,
    headers
  })
}
// 厂商人员检查项目
export function configLayoutSave(data) {
  return request({
    url: '/testing/overview/config/layout/save',
    method: 'post',
    data,
    headers
  })
}
