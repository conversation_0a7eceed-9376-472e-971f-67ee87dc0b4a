<template>
  <div class="detail-tabs-content">
    <div class="buttons-wrap">
      <el-button type="primary" @click="downloadVideo(videoList[0])">下载视频</el-button>
    </div>
    <div class="video-wrap">
      <video
        v-if="videoUrl"
        id="showVideo"
        width="100%"
        height="100%"
        controls
      >
        <source :src="videoUrl" type="video/mp4" >
      </video>
      <el-empty v-else :image="emptyImg" :image-size="178" class="empty-data">
        <div slot="description">暂无数据</div>
      </el-empty>
    </div>
  </div>
</template>

<script>
import detailCard from '@/packages/detail-view/detail-card.vue'
import FileList from '@/components/FileList/index.vue'
import { contentdetail } from '@/api/teacher/index.js'
import { getFileSuffix } from '@/utils'
export default {
  components: {
    detailCard,
    FileList
  },
  props: {
    id: {
      type: String | Number,
      default: ''
    },
    teachingName: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      emptyImg: require('@/packages/table-view/nodata.png'),
      videoList: [],
      videoUrl: '' // 视频url
    }
  },
  created() {
    this.getVideo()
  },
  methods: {
    // 获取视频
    getVideo() {
      contentdetail({ contentId: this.id, format: 'video' }).then(
        (res) => {
          this.videoList = res.data
          this.videoList.forEach((item) => {
            item.name = item.fileName || item.attachmentUrl
            item.url = window.location.origin + item.attachmentUrl
          })
          // 默认显示第一个附件
          const file = this.videoList[0]
          if (file) {
            this.videoUrl = file.url
            document.getElementById('showVideo').src = file.url
            document.getElementById('showVideo').play()
          }
        }
      )
    },
    // 下载视频文件
    downloadVideo(file) {
      if (file.url) {
        const type = getFileSuffix(file.url)
        fetch(file.url, { method: 'get', responseType: 'blob' })
          .then((response) => response.blob())
          .then((blob) => {
            const a = document.createElement('a')
            const URL = window.URL || window.webkitURL
            const herf = URL.createObjectURL(blob)
            a.href = herf
            a.download = this.teachingName + '.' + type
            document.body.appendChild(a)
            a.click()
            document.body.removeChild(a)
            URL.revokeObjectURL(herf)
          })
      }
    }
  }
}
</script>
<style scoped lang="less">
.detail-tabs-content {
  display: flex;
  flex-direction: column;
  .buttons-wrap {
    margin-bottom: 10px;
    text-align: right;
  }
  .video-wrap {
    flex: 1;
    min-height: 0;
    width: 100%;
    video {
      background: #000;
    }
  }
  ::v-deep .empty-data {
    padding: 20px 0;
    .el-empty__description {
      margin-top: 0;
      font-size: 14px;
      color: var(--neutral-600);
    }
  }
}
</style>
