<template>
  <div v-loading="" class="skill-map">
    <el-row :gutter="24">
      <el-col :span="12">
        <div>
          <div class="title">
            技能点 <i class="el-icon-edit-outline" @click="drawerName = 'selectSkill', drawerShow = true"/>
          </div>
          <div class="p-20">
            <div v-for="(item, index) in showSkillPoint" :key="index" class="content">
              <div>
                技能{{ index + 1 }}：
              </div>
              <div>
                {{ item.pointName }}
              </div>
            </div>
          </div>
        </div>
      </el-col>
      <el-col :span="12">
        <div>
          <div class="title">
            图谱
          </div>
          <div id="main"/>
        </div>
      </el-col>
    </el-row>
    <el-drawer
      :title="titleMapping[drawerName]"
      :visible.sync="drawerShow"
      :size="drawerWidth"
      append-to-body
      @close="drawerClose"
    >
      <transition v-if="drawerShow" name="el-fade-in-linear">
        <component
          :is="drawerName"
          :data="skillPointList"
          :show-data="showSkillPoint"
          :name="drawerName"
          @close="drawerClose"
          @call="drawerConfirmCall"
        />
      </transition>
    </el-drawer>
  </div>
</template>
<script>
import { getUserSkillPointInfo, getUserSkillPointDetailsInfo } from '@/api/accumulate/skillPoint.js'
import * as echarts from 'echarts'
import selectSkill from './table'
export default {
  components: {
    selectSkill
  },
  props: {
    id: {
      type: String
    },
    // 1用户所有的技能点 2用户某项活动的技能点
    type: {
      type: Number,
      default: 1
    },
    activityId: {
      type: String | Number
    },
    activityType: {
      type: String | Number
    }
  },
  data() {
    return {
      skillPointList: [],
      showSkillPoint: [],
      drawerName: null,
      drawerWidth: '720px',
      drawerShow: false,
      titleMapping: {
        'selectSkill': '技能点'
      },
      abilityValueList: []
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    getList(showLoading = true) {
      if (showLoading) {
        this.skillLoading = true
      }
      if (this.type === 1) {
        getUserSkillPointInfo({ userId: this.id }).then(res => {
          this.skillLoading = false
          this.skillPointList = res.data
          this.showSkillPoint = res.data.filter((item, index) => { return index < 8 })
          this.initAxis()
        })
      } else {
        getUserSkillPointDetailsInfo({ userId: this.id, activityType: this.activityType, activityId: this.activityId }).then(res => {
          this.skillLoading = false
          this.skillPointList = res.data
          this.showSkillPoint = res.data.filter((item, index) => { return index < 8 })
          this.initAxis()
        })
      }
    },
    // 侧拉选择资源回调
    drawerConfirmCall: function(type, data) {
      if (type === 'confirm_skill') {
        this.showSkillPoint = []
        data.forEach(item => {
          this.skillPointList = this.skillPointList.filter(i => { return i.pointId !== item.pointId })
          this.showSkillPoint.push(item)
        })
        this.showSkillPoint.forEach(item => {
          this.skillPointList.unshift(item)
        })
        this.initAxis()
        this.drawerClose()
      }
    },
    initAxis() {
      var chartDom = document.getElementById('main')
      var myChart = echarts.init(chartDom)
      const res = []
      this.abilityValueList = []
      this.showSkillPoint.forEach(item => {
        res.push({ text: item.pointName, max: 100 })
        this.abilityValueList.push(Math.round(item.abilityValue))
      })
      var option
      option = {
        title: {
          text: ' '
        },
        radar: {
          // shape: 'circle',
          indicator: res
        },
        tooltip: {
          trigger: 'item'
        },
        series: [
          {
            type: 'radar',
            data: [
              {
                value: this.abilityValueList,
                name: '技能点'
              }
            ],
            selected: false
          }
        ]
      }
      option && myChart.setOption(option)
    },
    drawerClose() {
      this.drawerShow = false
    }
  }
}
</script>
<style scoped lang="scss">
.skill-map {
  padding: 20px;
  #main {
    height: 500px;
  }
  .title {
    font-size: 18px;
    color: #333;
    i {
      cursor: pointer;
    }
  }
  .content {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    font-size: 16px;
    color: #333;
    font-weight: 400;
  }
  .el-row {
    margin-bottom: 20px;
    &:last-child {
      margin-bottom: 0;
    }
  }
  .el-col {
    border-radius: 4px;
  }
}
</style>
