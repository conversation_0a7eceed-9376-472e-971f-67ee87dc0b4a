<template>
  <div class="data-table-config">
    <el-button icon="el-icon-s-tools" @click="handleOpen">列</el-button>
    <el-drawer
      :size="600"
      :visible.sync="drawer"
      :before-close="handleClose"
      title="自定义列表项"
      append-to-body>
      <div class="drawer-wrap">
        <div class="el-drawer-form-body">
          <div class="container">
            <div class="title">
              <div>
                已选(<span class="font-w-600">{{ activeArr.length }}</span>)
              </div>
              <i class="el-icon-delete" @click="deleteAll()"/>
            </div>
            <div ref="container" class="content">
              <div v-for="(item, index) in activeArr" :key="index" :class="dataArr[item].master ? 'master flex' : ' flex'">
                <div class="flex">
                  <svg width="16px" class="btn" height="16px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" style="fill: currentcolor;">
                    <g id="通用/icon-图标/系统&amp;操作/drag__mSZfSbzH" stroke="none" stroke-width="1" fill-rule="evenodd">
                      <path d="M16,17 C17.104,17 18,17.896 18,19 C18,20.104 17.104,21 16,21 C14.896,21 14,20.104 14,19 C14,17.896 14.896,17 16,17 Z M8,17 C9.104,17 10,17.896 10,19 C10,20.104 9.104,21 8,21 C6.896,21 6,20.104 6,19 C6,17.896 6.896,17 8,17 Z M16,10 C17.104,10 18,10.896 18,12 C18,13.104 17.104,14 16,14 C14.896,14 14,13.104 14,12 C14,10.896 14.896,10 16,10 Z M8,10 C9.104,10 10,10.896 10,12 C10,13.104 9.104,14 8,14 C6.896,14 6,13.104 6,12 C6,10.896 6.896,10 8,10 Z M16,3 C17.104,3 18,3.896 18,5 C18,6.104 17.104,7 16,7 C14.896,7 14,6.104 14,5 C14,3.896 14.896,3 16,3 Z M8,3 C9.104,3 10,3.896 10,5 C10,6.104 9.104,7 8,7 C6.896,7 6,6.104 6,5 C6,3.896 6.896,3 8,3 Z" />
                    </g>
                  </svg>
                  <svg
                    v-if="!dataArr[item].master"
                    title="固定"
                    class="fix-icon"
                    t="1745833171167"
                    viewBox="0 0 1024 1024"
                    version="1.1"
                    xmlns="http://www.w3.org/2000/svg"
                    p-id="22704"
                    width="16"
                    height="16"
                    @click="handleFixed(item, true)"
                  >
                    <path d="M187.485091 493.917091c70.516364-44.125091 160.279273-52.130909 237.986909-19.2a34.862545 34.862545 0 0 0 35.118545-4.608l215.016728-168.12218199a34.955636 34.955636 0 0 0 12.544-35.23490901 122.786909 122.786909 0 0 1 11.636363-85.829818l166.539637 166.562909a122.600727 122.600727 0 0 1-85.829818 11.636364 34.909091 34.909091 0 0 0-35.211637 12.544l-172.613818 220.76509c-7.377455 9.472-9.402182 21.992727-5.399273 33.163637a258.816 258.816 0 0 1-24.506182 223.604363l-177.640727-177.617454 0-0.046546-0.046545 0-177.594182-177.64072699z m-45.405091 450.024727l198.353455-198.376727 181.59709 181.666909a34.909091 34.909091 0 0 0 51.2-1.954909c71.842909-83.735273 96.32581801-198.795636 65.815273-304.384l148.526546-189.952a191.325091 191.325091 0 0 0 156.392727-55.202909 34.909091 34.909091 0 0 0 0-49.361455L720.919273 103.330909a34.909091 34.909091 0 0 0-49.384728 0 191.185455 191.185455 0 0 0-55.202909 156.392727l-183.156363 143.173819c-109.242182-38.120727-233.425455-15.290182-321.815273 60.50909a35.141818 35.141818 0 0 0-12.125091 25.181091l0 1.349819c0 9.262545 3.630545 18.129455 10.19345501 24.66909l181.643636 181.620364-198.37672701 198.376727a34.816 34.816 0 0 0 0 49.338182 34.909091 34.909091 0 0 0 49.384727 0z" fill="var(--color-600)" p-id="22705" />
                  </svg>
                  <svg
                    v-if="dataArr[item].master"
                    title="取消固定"
                    class="fix-icon"
                    style="right: 20px;"
                    t="1745833209987"
                    viewBox="0 0 1820 1024"
                    version="1.1"
                    xmlns="http://www.w3.org/2000/svg"
                    p-id="25468"
                    width="33"
                    height="33"
                    @click="handleFixed(item, false)"
                  >
                    <path d="M1283.602963 374.802963c9.671111-9.671111 9.671111-33.943704 0-43.614815L1085.060741 132.740741c-9.671111-9.671111-33.943704-9.671111-43.614815 0-33.943704 33.943704-53.285926 87.134815-48.45037 135.585185L823.561481 403.911111c-91.97037-29.013333-193.706667-4.835556-266.334814 58.121482-9.671111 4.835556-14.506667 14.506667-14.506667 19.342222 0 9.671111 4.835556 19.342222 9.671111 24.177778l159.762963 159.762963-174.364444 174.459259C528.118519 849.445926 528.118519 863.952593 528.118519 873.623704c4.835556 9.671111 9.671111 19.342222 24.177777 24.177777 9.671111 4.835556 24.177778 0 29.013334-9.671111L755.674074 713.765926l62.957037 58.121481c0-9.671111 4.835556-19.342222 9.671111-33.943703-4.835556-19.342222-4.835556-33.943704-4.835555-53.285926l-43.614815-43.614815L620.088889 486.21037c62.957037-33.943704 135.585185-43.614815 198.542222-19.342222 9.671111 4.835556 19.342222 0 29.013333-4.835555l193.706667-154.927408c9.671111-9.671111 14.506667-19.342222 9.671111-29.013333-4.835556-24.177778 0-53.285926 9.671111-77.463704L1210.785185 350.72c-24.177778 14.506667-48.45037 14.506667-77.463704 9.671111-9.671111-4.835556-24.177778 0-29.013333 9.671111L1007.502222 495.881481c24.177778 0 48.45037 0 72.628148 4.835556l62.957037-77.463704c53.285926 4.835556 106.571852-14.506667 140.515556-48.45037z m0 0" fill="var(--color-600)" p-id="25469" />
                    <path d="M1022.103704 510.482963c-101.736296 0-179.2 82.299259-179.2 179.2s82.299259 179.2 179.2 179.2c101.736296 0 179.2-82.299259 179.2-179.2-0.094815-96.900741-82.394074-179.2-179.2-179.2zM891.354074 689.588148c0-19.342222 4.835556-43.614815 14.506667-58.121481l198.542222 164.693333c-24.177778 19.342222-53.285926 29.013333-82.299259 29.013333-72.628148-4.835556-130.74963-62.957037-130.74963-135.585185z m246.897778 62.957037L939.70963 587.946667c24.177778-19.342222 53.285926-29.013333 82.299259-29.013334 72.628148 0 130.74963 58.121481 130.74963 130.74963 0.094815 24.177778-4.740741 43.52-14.506667 62.862222z m0 0" p-id="25470" fill="#005bb4" />
                  </svg>
                  {{ data[item].title }}
                </div>
                <div>
                  <i v-if="!dataArr[item].master" class="el-icon-close" @click="removeSelected(index)" />
                </div>
              </div>
            </div>
          </div>
          <div class="el-drawer-form-content" style="padding: 20px;">
            <div class="title">
              全部列(<span class="font-w-600">{{ activeArr.length }}/{{ Object.keys(data).length }}</span>)
            </div>
            <el-alert v-if="activeArr.length <= minCol" type="warning" show-icon >显示列不可少于{{ minCol }}列</el-alert>
            <div class="content">
              <el-checkbox-group v-model="activeArr">
                <ul class="table-col-config">
                  <li v-for="(item, key) in data" :key="key">
                    <el-checkbox :label="key" :disabled="viewMaster(key)">{{ item.title }}</el-checkbox>
                  </li>
                </ul>
              </el-checkbox-group>
            </div>
          </div>
        </div>
        <div class="drawer-footer">
          <el-button :disabled="overMin" type="primary" @click="submit" >确定</el-button>
          <el-button type="text" @click="handleClose">取消</el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>
<script>
import Sortable from 'sortablejs'
export default {
  props: {
    // 传入数据
    data: {
      type: Object,
      default: () => {
        return {}
      }
    },
    // 已激活显示项目配置数组
    activeKeyArr: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      drawer: false,
      // 已选列
      activeArr: [],
      // 最小列数
      minCol: 0,
      dataArr: []
    }
  },
  computed: {
    'overMin': function() {
      return this.activeArr.length <= this.minCol
    },
    'activeArrOut': function() {
      const outArr = []
      Object.keys(this.data).forEach((key) => {
        if (this.activeArr.includes(key)) {
          outArr.push(key)
        }
      })
      return outArr
    }
  },
  created() {
    this.activeArr = JSON.parse(JSON.stringify(this.activeKeyArr))
    this.dataArr = JSON.parse(JSON.stringify(this.data))
  },
  mounted() {
  },
  methods: {
    handleFixed: function(item, flag) {
      this.$set(this.dataArr[item], 'master', flag)
    },
    'handleOpen': function() {
      this.activeArr = JSON.parse(JSON.stringify(this.activeKeyArr))
      this.dataArr = JSON.parse(JSON.stringify(this.data))
      this.drawer = true
      this.row()
    },
    'handleClose': function() {
      this.drawer = false
    },
    // 判断是否达到最小可显示列 key: 当前KEY
    'viewMaster': function(key) {
      return this.dataArr[key]['master']
    },
    'submit': function(modal) {
      // 传递到父级组件
      this.$emit('on-change-col', this.activeArr, this.dataArr)
      this.handleClose()
    },
    removeSelected(index) {
      this.activeArr.splice(index, 1)
    },
    deleteAll() {
      this.activeArr.splice(1)
    },
    row() {
      this.$nextTick(() => {
        const el = this.$refs.container
        Sortable.create(el, {
          animation: 200,
          group: 'name',
          sort: true,
          handle: '.btn',
          filter: '.master',
          onEnd: (evt) => {
            this.activeArr.splice(evt.newIndex, 0, this.activeArr.splice(evt.oldIndex, 1)[0])
            var newArray = this.activeArr.slice(0)
            this.activeArr = []
            this.$nextTick(() => {
              this.activeArr = newArray
            })
          },
          onMove: (evt) => {
            if (evt.related.classList.contains('master')) {
              return false
            }
          }
        })
      })
    }
  }
}
</script>

<style lang="less" scoped>
.data-table-config {
  display: inline-block;
  vertical-align: middle;
}
.container {
  min-width: 280px;
  height: auto;
  padding: 20px;
  .title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #333;
    font-weight: 500;
  }
  .content{
    position: relative;
    border-radius: 4px;
    margin-top: 12px;
    padding: 12px 8px;
    background: #F5F7FA;
    height: 94%;
    display: flex;
    overflow: auto;
    flex-direction: column;
  }
}
.el-drawer-form-body {
  display: flex;
  height: 100%;
}
.flex{
  display: flex;
  align-items: center;
  font-size: 14px;
  line-height: 34px;
  justify-content: space-between;
  .btn {
    cursor: move;
    margin-right: 4px;
  }
  .fix-icon {
    position: absolute;
    right: 28px;
    cursor: pointer;
    &.fixed path {
      fill: #C8CACD;
    }
  }
}
.master {
  color: #C8CACD !important;
  .btn {
    cursor: not-allowed !important;
  }
}
.el-icon-close{
  cursor: pointer;
}
.el-drawer-form-content{
  height: auto;
  .title {
    margin-bottom: 24px;
  }
  .content{
    height: 94%;
    overflow: auto;
    .table-col-config{
      li {
        margin-bottom: 10px;
      }
    }
  }
}
.font-w-600 {
  font-weight: 600;
}
</style>
