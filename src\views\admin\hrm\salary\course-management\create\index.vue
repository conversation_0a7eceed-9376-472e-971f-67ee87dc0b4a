<template>
  <create-view v-loading="loading" :title="viewTitle">
    <div slot="content">
      <el-form ref="formLabelAlign" :rules="rules" :model="formLabelAlign" label-position="left" label-width="150px" class="form-wrap">
        <el-card class="mb-10">
          <el-form-item label="名称" prop="teachingName">
            <el-input v-model.trim="formLabelAlign.teachingName" style="width: 320px;"/>
          </el-form-item>
          <el-form-item label="封面">
            <span slot="label">
              <span>封面</span>
              <el-tooltip transfer>
                <i class="el-icon-warning-outline" />
                <div slot="content">{{ fileUploadTip }}</div>
              </el-tooltip>
            </span>
            <div class="upload-div">
              <div v-if="imgUrl">
                <img :src="imgUrl" :style="imgStyle" alt="" @click="handleUpload">
              </div>
              <div v-else>
                <el-button type="ghost" class="btn" @click="handleUpload">
                  上传图片
                </el-button>
              </div>
              <input ref="fileRef" :accept="accept" type="file" style="display: none" @change="isSize" >
            </div>
          </el-form-item>
          <el-form-item label="分类" prop="categoryCode">
            <el-select v-model="formLabelAlign.categoryCode" clearable filterable placeholder="请选择" style="width: 320px;">
              <el-option
                v-for="item in categoryCodeList"
                :key="item.id"
                :label="item.name"
                :value="item.id"/>
            </el-select>
          </el-form-item>
          <el-form-item label="难度" prop="difficulty">
            <el-select v-model="formLabelAlign.difficulty" size="medium" clearable placeholder="请选择" style="width: 320px;">
              <el-option
                v-for="item in contentLevelList"
                :key="item.value"
                :label="item.label"
                :value="item.value"/>
            </el-select>
          </el-form-item>
          <el-form-item label="描述" prop="teachingDescribe">
            <el-input v-model.trim="formLabelAlign.teachingDescribe" type="textarea" style="width: 320px;"/>
          </el-form-item>
        </el-card>
        <!-- 编辑才显示“所有者” -->
        <el-card v-if="editMode">
          <el-form-item label="所有者" prop="selectedTeacher">
            <span slot="label">
              <span>所有者</span>
              <el-tooltip transfer>
                <i class="el-icon-warning-outline" />
                <div slot="content">
                  {{ `变更课程的所有者时，其下课程内容的所有者将一同变更。` }}
                </div>
              </el-tooltip>
            </span>
            <el-tag
              v-if="formLabelAlign.selectedTeacher"
              :disable-transitions="true"
              style="vertical-align:middle;"
              closable
              @click="clickDrop('selectedTeacher')"
              @close="formLabelAlign.selectedTeacher = null"
            >
              {{ formLabelAlign.selectedTeacher.realname }}
            </el-tag>
            <el-button
              v-else type="ghost"
              style="vertical-align:middle;"
              @click="clickDrop('selectedTeacher')"
            >
              {{ titleMapping['selectedTeacher'] }}
            </el-button>
          </el-form-item>
        </el-card>
      </el-form>
      <!-- 侧拉弹窗 start -->
      <el-drawer
        :title="titleMapping[drawerName]"
        :visible.sync="drawerShow"
        :size="drawerWidth"
        append-to-body
        @close="drawerClose"
      >
        <transition name="el-fade-in-linear">
          <component
            :is="drawerName"
            :name="drawerName"
            @close="drawerClose"
            @call="drawerConfirmCall"
          />
        </transition>
      </el-drawer>
    </div>
    <!-- 侧拉弹窗 end -->
    <div slot="footer">
      <el-button type="text" @click="close()">取消</el-button>
      <el-button type="primary" @click="confirm()">确定</el-button>
    </div>
  </create-view>
</template>
<script>
import createView from '@/packages/create-view/index'
import selectedTeacher from '@/views/admin/hrm/TeachingAffairs/action/select-teacher.vue'
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
import validate from '@/packages/validate'
import { queryCourseCategory, saveCourse, upTeachingCoverApi, updataCourseCategory } from '@/api/teacher/index.js'

export default {
  components: {
    selectedTeacher,
    createView
  },
  mixins: [mixinsActionMenu],
  data() {
    return {
      loading: false,
      validate: validate,
      rules: {
        selectedTeacher: [validate.required(['blur', 'change'])],
        teachingName: [validate.required(), validate.name_64_char],
        categoryCode: [validate.required()],
        difficulty: [validate.required()],
        teachingDescribe: [{ min: 1, max: 65535, message: '1-65535个字符', trigger: 'blur' }]
      },
      formLabelAlign: {
        selectedTeacher: null,
        teachingName: '',
        url: '',
        teachingDescribe: '',
        categoryCode: '',
        categoryContent: [],
        difficulty: ''
      },
      drawerAction: ['selectedTeacher'],
      titleMapping: {
        'selectedTeacher': '选择所有者'
      },
      imgUrl: '',
      fileSize: 8, // 文件大小单位为 MB
      fileRatio: '430*236', // 图片分辨率
      fileTypes: ['jpg', 'jpeg', 'png'],
      categoryCodeList: [],
      editId: this.$route.query.id,
      apiType: saveCourse,
      contentLevelList: [{ label: '初级', value: 1 }, { label: '中级', value: 2 }, { label: '高级', value: 3 }],
      data: JSON.parse(this.$route.query.data)
    }
  },
  computed: {
    editMode() {
      return !!this.data.id
    },
    viewTitle() {
      return `${this.editMode ? '编辑课程' : '创建课程'}`
    },
    accept() {
      return this.fileTypes.map(type => `.${type}`).join(',')
    },
    imgStyle() {
      const [width, height] = this.fileRatio.split('*')
      return {
        width: width + 'px',
        height: height + 'px'
      }
    },
    fileTypeText() {
      return `支持图片格式：${this.fileTypes.join('、')}`
    },
    fileSizeText() {
      return `图片大小限制：${this.fileSize}MB`
    },
    fileRatioText() {
      return `建议封面图片尺寸：${this.fileRatio}`
    },
    fileUploadTip() {
      return `${this.fileTypeText}，${this.fileSizeText}，${this.fileRatioText}`
    }
  },
  async mounted() {
    await this.searchPoint()
    if (this.editMode) {
      this.formLabelAlign['selectedTeacher'] = {
        userId: this.data['createdBy'],
        realname: this.data['userName']
      }
      this.formLabelAlign['teachingName'] = this.data['name']
      this.formLabelAlign['url'] = this.data['courseCoverUrl']
      this.formLabelAlign['teachingDescribe'] = this.data['courseDescription']
      this.formLabelAlign['categoryCode'] = this.data['courseCategoryId']
      this.formLabelAlign['categoryContent'] = this.data['categoryContent']
      this.imgUrl = this.data['courseCoverUrl']
      this.formLabelAlign['difficulty'] = this.data['difficulty']
    }
  },
  methods: {
    searchPoint() {
      queryCourseCategory().then(res => {
        this.categoryCodeList = res.data
      })
    },
    handleUpload() {
      this.$refs.fileRef.value = ''
      this.$refs.fileRef.click()
    },
    // 限制分辨率
    isSize() {
      const file = this.$refs.fileRef.files[0]
      const reader = new FileReader()
      const [maxWidth, maxHeight] = this.fileRatio.split('*')
      reader.onload = () => {
        if (reader.readyState == 2) {
          const img = new Image()
          img.src = reader.result
          this.$nextTick(() => {
            if (
              (img.width && img.width <= Number(maxWidth)) &&
              (img.height && img.height <= Number(maxHeight))
            ) {
              this.isValidRatio = true
              this.doUpload()
            } else {
              this.isValidRatio = false
              this.doUpload()
            }
          })
        }
      }
      reader.readAsDataURL(file)
    },
    doUpload() {
      const file = this.$refs.fileRef.files[0]
      // #begin
      // 限制文档格式
      const fileArr = file.name.split('.')
      const fileType = fileArr[fileArr.length - 1]
      if (!this.fileTypes.includes(fileType.toLowerCase())) {
        this.$message.error(this.fileTypeText)
        return
      }
      // 限制文件大小
      const maxSize = this.fileSize * 1024 * 1024
      if (file.size > maxSize) {
        this.$message.warning(this.fileSizeText)
        return
      }
      // 限制分辨率
      if (!this.isValidRatio) {
        this.$message.warning(this.fileRatioText)
      }
      // #end
      const formData = new FormData()
      formData.append('file', this.$refs.fileRef.files[0])
      upTeachingCoverApi(formData).then((ret) => {
        if (ret.code === 200 || ret.code === 0) {
          this.$message({
            message: '上传成功',
            type: 'success'
          })
          this.imgUrl = ret.data.url
        }
      }).catch(() => {
        this.$message.error('上传失败')
      })
    },
    // 侧拉选择资源回调
    drawerConfirmCall: function(type, data) {
      if (type === 'close') {
        this.drawerClose()
      } else if (type === 'confirm_teacher') {
        this.formLabelAlign['selectedTeacher'] = data[0]
        this.drawerClose()
        this.$refs['formLabelAlign'].validateField('selectedTeacher')
      }
    },
    close() {
      this.$router.go(-1)
    },
    'confirm': function() {
      this.$refs['formLabelAlign'].validate((valid) => {
        if (valid) {
          this.loading = true
          const params = {
            name: this.formLabelAlign.teachingName,
            courseCoverUrl: this.imgUrl,
            courseCategoryId: this.formLabelAlign.categoryCode,
            difficulty: this.formLabelAlign.difficulty,
            courseDescription: this.formLabelAlign.teachingDescribe,
            contentIds: this.formLabelAlign['categoryContent']
          }
          if (this.editMode) {
            params.id = this.data.id
            params.createdBy = this.formLabelAlign['selectedTeacher'].userId
            this.apiType = updataCourseCategory
          }
          this.apiType(params).then(res => {
            if (res.code == 0) {
              this.$message.success(`${this.viewTitle}成功`)
              this.close()
            } else {
              this.$message.error(`${this.viewTitle}失败`)
            }
          }).finally(() => {
            this.loading = false
          })
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.form-wrap {
}
</style>
