<template>
  <div class="_paper_container card-bg">
    <div class="_paper_top">
      <div class="_paper_top_title">
        <div class="line"/>
        分类
      </div>
      <div style="margin-bottom: 10px;">
        <el-tag
          v-for="(item,index) in classifyList" :key="index"
          :disable-transitions="true"
          closable
          style="margin:3px 5px 5px 0"
          @close="closeClassify(index)">
          {{ item.categoryName }}
        </el-tag>
      </div>
      <el-button class="mb-10" type="ghost" @click="drawerName = 'selectClassify', drawerShow = true">选择分类</el-button>
    </div>
    <div class="_paper_search">
      <div class="line"/>
      <div class="_paper_search_1">
        总题数
        <span style="font-weight: bold; color: var(--color-600); font-size: 18px; margin-right: 10px;">{{
          total
        }}</span>
      </div>
      <div class="_paper_search_1">
        已选
        <span style="font-weight: bold; color: var(--color-600); font-size: 18px;">{{
          topicTotal
        }}</span>
      </div>
    </div>
    <div class="_paper_bottom mt-10">
      <el-form ref="form" :model="formData" :rules="rules" label-position="left" label-width="90px" class="dialog-form">
        <div>
          理论
        </div>
        <div class="p-10">
          <el-form-item label="单选题" prop="theoryVO.singleTypeNum">
            <el-input-number v-model="formData.theoryVO.singleTypeNum" :min="0" size="small" @change="formDataChange"/>
            （ {{ optionalDate.theoryVO ? optionalDate.theoryVO.singleTypeNum : 0 }} 道可用 ）
          </el-form-item>
          <el-form-item label="多选题" prop="theoryVO.manyTypeNum">
            <el-input-number v-model="formData.theoryVO.manyTypeNum" :min="0" size="small" @change="formDataChange"/>
            （ {{ optionalDate.theoryVO ? optionalDate.theoryVO.manyTypeNum : 0 }} 道可用 ）
          </el-form-item>
          <el-form-item label="判断题" prop="theoryVO.judgeTypeNum">
            <el-input-number v-model="formData.theoryVO.judgeTypeNum" :min="0" size="small" @change="formDataChange"/>
            （ {{ optionalDate.theoryVO ? optionalDate.theoryVO.judgeTypeNum : 0 }} 道可用 ）
          </el-form-item>
          <el-form-item label="CTF题" prop="theoryVO.ctfTypeNum">
            <el-input-number v-model="formData.theoryVO.ctfTypeNum" :min="0" size="small" @change="formDataChange"/>
            （ {{ optionalDate.theoryVO ? optionalDate.theoryVO.ctfTypeNum : 0 }} 道可用 ）
          </el-form-item>
          <el-form-item label="填空题" prop="theoryVO.completionNum">
            <el-input-number v-model="formData.theoryVO.completionNum" :min="0" size="small" @change="formDataChange"/>
            （ {{ optionalDate.theoryVO ? optionalDate.theoryVO.completionNum : 0 }} 道可用 ）
          </el-form-item>
          <el-form-item label="简答题" prop="theoryVO.saqTypeNum">
            <el-input-number v-model="formData.theoryVO.saqTypeNum" :min="0" size="small" @change="formDataChange"/>
            （ {{ optionalDate.theoryVO ? optionalDate.theoryVO.saqTypeNum : 0 }} 道可用 ）
          </el-form-item>
        </div>
        <div>
          靶机
        </div>
        <div class="p-10">
          <el-form-item label="CTF题" prop="targetVO.ctfTypeNum">
            <el-input-number v-model="formData.targetVO.ctfTypeNum" :min="0" size="small" @change="formDataChange"/>
            （ {{ optionalDate.targetVO ? optionalDate.targetVO.ctfTypeNum : 0 }} 道可用 ）
          </el-form-item>
          <el-form-item label="AWD题" prop="targetVO.awdTypeNum">
            <el-input-number v-model="formData.targetVO.awdTypeNum" :min="0" size="small" @change="formDataChange"/>
            （ {{ optionalDate.targetVO ? optionalDate.targetVO.awdTypeNum : 0 }} 道可用 ）
          </el-form-item>
          <el-form-item label="漏洞题" prop="targetVO.bugTypeNum">
            <el-input-number v-model="formData.targetVO.bugTypeNum" :min="0" size="small" @change="formDataChange"/>
            （ {{ optionalDate.targetVO ? optionalDate.targetVO.bugTypeNum : 0 }} 道可用 ）
          </el-form-item>
          <el-form-item label="其它" prop="targetVO.otherTypeNum">
            <el-input-number v-model="formData.targetVO.otherTypeNum" :min="0" size="small" @change="formDataChange"/>
            （ {{ optionalDate.targetVO ? optionalDate.targetVO.otherTypeNum : 0 }} 道可用 ）
          </el-form-item>
        </div>
        <div>
          仿真
        </div>
        <div class="p-10">
          <el-form-item label="单选题" prop="emulationVO.singleTypeNum">
            <el-input-number v-model="formData.emulationVO.singleTypeNum" :min="0" size="small" @change="formDataChange"/>
            （ {{ optionalDate.emulationVO ? optionalDate.emulationVO.singleTypeNum : 0 }} 道可用 ）
          </el-form-item>
          <el-form-item label="多选题" prop="emulationVO.manyTypeNum">
            <el-input-number v-model="formData.emulationVO.manyTypeNum" :min="0" size="small" @change="formDataChange"/>
            （ {{ optionalDate.emulationVO ? optionalDate.emulationVO.manyTypeNum : 0 }} 道可用 ）
          </el-form-item>
          <el-form-item label="判断题" prop="emulationVO.judgeTypeNum">
            <el-input-number v-model="formData.emulationVO.judgeTypeNum" :min="0" size="small" @change="formDataChange"/>
            （ {{ optionalDate.emulationVO ? optionalDate.emulationVO.judgeTypeNum : 0 }} 道可用 ）
          </el-form-item>
          <el-form-item label="CTF题" prop="emulationVO.ctfTypeNum">
            <el-input-number v-model="formData.emulationVO.ctfTypeNum" :min="0" size="small" @change="formDataChange"/>
            （ {{ optionalDate.emulationVO ? optionalDate.emulationVO.ctfTypeNum : 0 }} 道可用 ）
          </el-form-item>
          <el-form-item label="填空题" prop="emulationVO.completionNum">
            <el-input-number v-model="formData.emulationVO.completionNum" :min="0" size="small" @change="formDataChange"/>
            （ {{ optionalDate.emulationVO ? optionalDate.emulationVO.completionNum : 0 }} 道可用 ）
          </el-form-item>
          <el-form-item label="简答题" prop="emulationVO.saqTypeNum">
            <el-input-number v-model="formData.emulationVO.saqTypeNum" :min="0" size="small" @change="formDataChange"/>
            （ {{ optionalDate.emulationVO ? optionalDate.emulationVO.saqTypeNum : 0 }} 道可用 ）
          </el-form-item>
          <el-form-item label="组合题" prop="emulationVO.combinatorialTypeNum">
            <el-input-number v-model="formData.emulationVO.combinatorialTypeNum" :min="0" size="small" @change="formDataChange"/>
            （ {{ optionalDate.emulationVO ? optionalDate.emulationVO.combinatorialTypeNum : 0 }} 道可用 ）
          </el-form-item>
        </div>
      </el-form>
    </div>
    <el-drawer
      :title="titleMapping[drawerName]"
      :visible.sync="drawerShow"
      :size="drawerWidth"
      append-to-body
      @close="drawerClose"
    >
      <transition v-if="drawerShow" name="el-fade-in-linear">
        <component
          :is="drawerName"
          :name="drawerName"
          @close="drawerClose"
          @call="drawerConfirmCall"
        />
      </transition>
    </el-drawer>
  </div>
</template>

<script>
import validate from '@/packages/validate'
import selectClassify from './select-classify.vue'
import { categoryTypeNumber } from '@/api/accumulate/questionBank'

export default {
  components: {
    selectClassify
  },
  props: {
    classList: {
      type: Array
    },
    intelligenceData: {
      type: Object
    }
  },
  data() {
    var numCheck = (rule, value, callback) => {
      const arr = rule.field.split('.')
      if (value < 0 || value > (this.optionalDate && this.optionalDate[arr[0]] ? this.optionalDate[arr[0]][arr[1]] : 0)) {
        callback(new Error(`输入范围：0-${this.optionalDate && this.optionalDate[arr[0]] ? this.optionalDate[arr[0]][arr[1]] : 0}`))
      } else {
        callback()
      }
    }
    return {
      titleMapping: {
        'selectClassify': '分类列表'
      },
      validate: validate,
      optionalDate: {},
      formData: {
        'theoryVO': {
          singleTypeNum: 0,
          manyTypeNum: 0,
          judgeTypeNum: 0,
          ctfTypeNum: 0,
          completionNum: 0,
          saqTypeNum: 0
        },
        'targetVO': {
          ctfTypeNum: 0,
          awdTypeNum: 0,
          bugTypeNum: 0,
          otherTypeNum: 0
        },
        'emulationVO': {
          singleTypeNum: 0,
          manyTypeNum: 0,
          judgeTypeNum: 0,
          ctfTypeNum: 0,
          completionNum: 0,
          saqTypeNum: 0,
          combinatorialTypeNum: 0
        }
      },
      rules: {
        'theoryVO.singleTypeNum': [
          validate.all_number_integer,
          { validator: numCheck, trigger: 'change' }
        ],
        'theoryVO.manyTypeNum': [
          validate.all_number_integer,
          { validator: numCheck, trigger: 'change' }
        ],
        'theoryVO.judgeTypeNum': [
          validate.all_number_integer,
          { validator: numCheck, trigger: 'change' }
        ],
        'theoryVO.ctfTypeNum': [
          validate.all_number_integer,
          { validator: numCheck, trigger: 'change' }
        ],
        'theoryVO.completionNum': [
          validate.all_number_integer,
          { validator: numCheck, trigger: 'change' }
        ],
        'theoryVO.saqTypeNum': [
          validate.all_number_integer,
          { validator: numCheck, trigger: 'change' }
        ],
        'targetVO.ctfTypeNum': [
          validate.all_number_integer,
          { validator: numCheck, trigger: 'change' }
        ],
        'targetVO.awdTypeNum': [
          validate.all_number_integer,
          { validator: numCheck, trigger: 'change' }
        ],
        'targetVO.bugTypeNum': [
          validate.all_number_integer,
          { validator: numCheck, trigger: 'change' }
        ],
        'targetVO.otherTypeNum': [
          validate.all_number_integer,
          { validator: numCheck, trigger: 'change' }
        ],
        'emulationVO.singleTypeNum': [
          validate.all_number_integer,
          { validator: numCheck, trigger: 'change' }
        ],
        'emulationVO.manyTypeNum': [
          validate.all_number_integer,
          { validator: numCheck, trigger: 'change' }
        ],
        'emulationVO.judgeTypeNum': [
          validate.all_number_integer,
          { validator: numCheck, trigger: 'change' }
        ],
        'emulationVO.ctfTypeNum': [
          validate.all_number_integer,
          { validator: numCheck, trigger: 'change' }
        ],
        'emulationVO.completionNum': [
          validate.all_number_integer,
          { validator: numCheck, trigger: 'change' }
        ],
        'emulationVO.saqTypeNum': [
          validate.all_number_integer,
          { validator: numCheck, trigger: 'change' }
        ],
        'emulationVO.combinatorialTypeNum': [
          validate.all_number_integer,
          { validator: numCheck, trigger: 'change' }
        ]
      },
      drawerWidth: '720px',
      drawerShow: false,
      drawerName: null,
      questionList: [],
      classifyList: []
    }
  },
  computed: {
    topicTotal() {
      let num = 0
      for (const key in this.formData) {
        for (const key2 in this.formData[key]) {
          if ((this.optionalDate[key] && (this.formData[key][key2] <= this.optionalDate[key][key2])) && parseInt(this.formData[key][key2]) === this.formData[key][key2]) {
            num = num + this.formData[key][key2]
          }
        }
      }
      return num
    },
    total() {
      let num = 0
      for (const key in this.optionalDate) {
        for (const key2 in this.optionalDate[key]) {
          num = num + this.optionalDate[key][key2]
        }
      }
      return num
    }
  },
  watch: {
    classifyList() {
      // 选择类型发生变化去调用列表查询
      this.getList()
    }
  },
  created() {
    this.classifyList = this.classList
    this.formData = this.intelligenceData
  },
  methods: {
    formDataChange() {
      this.$emit('intelligence-data-change', this.formData)
    },
    closeClassify(index) {
      this.classifyList.splice(index, 1)
    },
    // 侧拉选择资源回调
    drawerConfirmCall: function(type, data) {
      if (type === 'confirm_classify') {
        data.forEach(item => {
          if (this.classifyList.filter(classify => { return classify.id == item.id }).length === 0) {
            this.classifyList.push(item)
          }
        })
        this.drawerClose()
      }
    },
    drawerClose() {
      this.drawerShow = false
    },
    getList: function() {
      const categoryIds = []
      this.classifyList.forEach(item => {
        categoryIds.push(item.id)
      })
      if (categoryIds.length === 0) {
        this.optionalDate = {
          theoryVO: {
            singleTypeNum: 0,
            manyTypeNum: 0,
            judgeTypeNum: 0,
            ctfTypeNum: 0,
            completionNum: 0,
            saqTypeNum: 0
          },
          targetVO: {
            ctfTypeNum: 0,
            awdTypeNum: 0,
            bugTypeNum: 0,
            otherTypeNum: 0
          },
          emulationVO: {
            singleTypeNum: 0,
            manyTypeNum: 0,
            judgeTypeNum: 0,
            ctfTypeNum: 0,
            completionNum: 0,
            saqTypeNum: 0,
            combinatorialTypeNum: 0
          }
        }
        return
      }
      categoryTypeNumber(categoryIds).then(res => {
        this.optionalDate = res.data
      }).catch(() => {})
    }
  }
}
</script>

<style lang="scss" scoped>
._paper_container {
  .line {
    width: 3px;
    height: 15px;
    margin-right: 5px;
    background: var(--color-600);
  }
  .mb-10 {
    margin-bottom: 10px;
  }
  .p-10 {
    padding: 10px 20px;
  }
  .flex {
    display: flex;
  }
  ._paper_search {
    display: flex;
    align-items: center;
    ._paper_search_1 {
      font-size: 16px;
      font-weight: 500;
      color: #333;
    }
  }
  .mt-10 {
    margin-top: 10px;
    margin-bottom: 0;
  }
  ._paper_top {
    padding-bottom: 10px;
    .ju-between {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    ._paper_top_title {
      font-size: 16px;
      font-weight: 500;
      color: #333;
      margin-bottom: 10px;
      display: flex;
      align-items: center;
    }
    ::v-deep {
      .transverse-list {
        padding: 0;
        border: none;
        .transverse-list-operate {
          display: none;
        }
      }
    }
  }
  ._paper_bottom {
    ::v-deep {
      .el-input-number--small{
        margin: 0 10px 0 0;
        .el-input-number__decrease{
          display: none;
        }
        .el-input-number__increase{
          display: none;
        }
      }
    }
    ._paper_header {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .select-box {
        display: flex;
        align-items: center;
        justify-content: space-between;
        color: #4e5969;
        font-size: 14px;
      }
    }

    ._paper_search_screen {
      padding: 11px;

      > div {
        display: flex;
        margin-bottom: 20px;

        > div {
          margin-right: 40px;
          padding: 10px;
          // background: #288FEF;
          font-size: 14px;
          color: #999999;
          text-align: center;
          border-radius: 4px;
          cursor: pointer;
        }
      }

      ._paper_type_check {
        background: #288fef;
        color: white;
      }
    }

    ._question_list {
      height: calc(100vh - 220px);
      overflow-y: auto;
      margin-top: 10px;
      ._question_item {
        padding: 20px 19px 0 50px;
        min-height: 90px;
        background: #ffffff;
        display: flex;
        flex-direction: column;
        font-size: 14px;
        color: #4e5969;
        position: relative;
        border: 1px solid #e5e6eb;
        border-radius: 4px;
        margin-bottom: 20px;
        ._question_option {
          margin-top: 20px;
          font-size: 14px;
          color: #4e5969;
          display: flex;
          flex-direction: column;
          line-height: 22px;
          word-break: break-all;
          ::v-deep .el-radio {
            margin-bottom: 17px;
            display: flex;
            align-items: flex-start;

            .el-radio__label {
              font-size: 14px;
              color: #4e5969;
              white-space: normal;
              word-break: break-all;
            }
          }
          ::v-deep .el-checkbox {
            margin-bottom: 17px;
            display: flex;
            align-items: flex-start;
            .el-checkbox__label {
              font-size: 14px;
              color: #4e5969;
              white-space: normal;
              word-break: break-all;
            }
          }
        }
        ._question_item_1 {
          display: flex;
          align-items: center !important;
          justify-content: end;

          ._question_item_type {
            width: 80px;
            text-align: right;
          }
        }
      }
      ._question_item_check {
        border: 1px solid var(--color-600);
      }
    }
  }
}
::v-deep {
  .el-icon-search {
    cursor: pointer;
  }
}
.card-bg {
  border-radius: 4px;
  padding: 15px;
  background-color: #FFFFFF;
}
</style>
