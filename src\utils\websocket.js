import { mapGetters, mapActions } from 'vuex'
export default {
  data() {
    return {
      path: '',
      socket: null,
      timeOutObj: null,
      serverTimeOutObj: null,
      timeout: 15000,
      lockReconnect: false,
      socketNum: 3,
      isAutoConnect: true, // 是否要自动重连  退出页面不重连  异常断后进行重连
      retimer: null
    }
  },
  computed: {
    ...mapGetters(['userInfo'])
  },
  watch: {
    'userInfo': function(userInfo) {
      if (window.ADMIN_CONFIG.WS_PREFIX) {
        if (this.socket) {
          this.socket.close()
        }
        // const token = JSON.parse(localStorage.getItem('Admin-Token')).data
        this.path = `/scene/websocket/${userInfo.userId}`
        if (typeof (WebSocket) === 'undefined') {
          this.$message.error('您的浏览器不支持')
        } else {
          this.isAutoConnect = true
          this.initWebsocket()
        }
      }
    }
  },
  destroyed() {
    // 销毁监听
    if (this.socket) {
      this.isAutoConnect = false
      this.socket.close()
    }
  },
  methods: {
    ...mapActions('websocketListener', [
      'SOCKET_CR'
    ]),
    'initWebsocket': function() {
      if (this.path) {
        // 实例化socket
        this.socket = this.createWebsocket(`${window.ADMIN_CONFIG.WS_PREFIX}${this.path}`)()
        // 监听socket连接
        this.socket.onopen = this.open
        // 监听socket错误信息
        this.socket.onerror = this.error
        // 监听socket消息
        this.socket.onmessage = this.onmessage
        // 销毁
        this.socket.onclose = this.close
      } else {
        return
      }
    },
    'createWebsocket': function(url) {
      let singWebSocket
      return function() {
        if (!singWebSocket) {
          singWebSocket = new WebSocket(url)
        }
        return singWebSocket
      }
    },
    'onmessage': function(msg) {
      // 后端推送心跳1时，前端发送2
      if (msg.data === '1') {
        this.send(2)
      }
      this.SOCKET_CR(msg)
    },
    'open': function() {
      console.log('打开了哥们')
      this.heartCheck()
    },
    'error': function() {
      if (this.socket.readyState === 3) {
        this.reConnect()
      }
    },
    'send': function(params) {
      if (this.socket.readyState === 1) { // 连接已建立
        this.socket.send(params)
      }
    },
    'close': function() {
      if (this.socket.readyState === 3) { // 连接已关闭
        if (this.isAutoConnect) {
          this.reConnect()
        }
      }
    },
    'reConnect': function() {
      if (this.lockReconnect) return
      this.lockReconnect = true
      this.retimer && clearTimeout(this.retimer)
      this.retimer = setTimeout(() => {
        this.socketNum = 3
        // this.socket = null
        this.lockReconnect = false
        this.initWebsocket()
      }, 1500)
    },
    // 心跳检测
    'heartCheck': function() {
      // 如果定时器存在先清除
      this.timeOutObj && clearTimeout(this.timeOutObj)
      this.serverTimeOutObj && clearTimeout(this.serverTimeOutObj)
      this.socketNum--
      this.timeOutObj = setTimeout(() => {
        // 向后端发送心跳信息
        this.send(1)
        if (this.socketNum === 0) {
          this.serverTimeOutObj = setTimeout(() => {
            this.socket.onclose()
          }, this.timeout)
        }
      }, this.timeout)
    }
  }
}
