<template>
  <div v-loading="loading" class="content-wrap-layout content-wrap">
    <el-tabs v-model="activeTab" :key="id" class="detail-tabs" type="card" @tab-click="handleTabClick">
      <el-tab-pane label="概况" name="overview">
        <overview :data="$route.query" />
      </el-tab-pane>
      <el-tab-pane label="视频讲解" name="video">
        <detail-video :id="id" :teaching-name="teachingName" />
      </el-tab-pane>
      <el-tab-pane label="课件资料" name="courseware">
        <detail-courseware :id="id" :teaching-name="teachingName" />
      </el-tab-pane>
      <el-tab-pane label="随堂练习" name="practice">
        <el-row :gutter="20" class="detail-tabs-content">
          <el-col :span="24">
            <!-- 动态试卷 -->
            <dynamic-exam-paper v-if="dynamicPaperData.examType === 1" :data.sync="dynamicPaperData" />
            <!-- 静态试卷/题目 -->
            <QuestionBank v-else :question-list="questionList" :show-user-answer="false" :answer-time="answerTime" />
          </el-col>
        </el-row>
      </el-tab-pane>
      <el-tab-pane v-if="curriculumType != '理论'" label="操作拓扑" name="topology">
        <div :class="{'task-full-screen': taskFullScreen}" class="detail-tabs-content">
          <HandleTopo ref="HandleTopo" style="padding: 0;"/>
        </div>
      </el-tab-pane>
    </el-tabs>
    <div v-if="activeTab == 'topology'" :class="{'task-full-screen': taskFullScreen}" class="screen-icon">
      <el-tooltip v-if="!taskFullScreen" effect="dark" content="全屏" placement="right">
        <i class="el-icon-full-screen" @click="taskFullScreen = !taskFullScreen" />
      </el-tooltip>
      <el-tooltip v-if="taskFullScreen" effect="dark" content="取消全屏" placement="right">
        <i class="el-icon-aim" @click="taskFullScreen = !taskFullScreen" />
      </el-tooltip>
    </div>
  </div>
</template>
<script>
import overview from './detail-overview'
import detailVideo from './detail-video'
import detailCourseware from './detail-courseware'
import HandleTopo from '@/views/admin/hrm/content/create/HandleTopo.vue'
import QuestionBank from '@/components/QuestionBank/QuestionDetail'
import dynamicExamPaper from '@/components/QuestionBank/dynamicPaper/index.vue'
import { queryPractice } from '@/api/teacher/index'
export default {
  name: 'CourseEditor',
  components: {
    overview,
    detailVideo,
    detailCourseware,
    HandleTopo,
    QuestionBank,
    dynamicExamPaper
  },
  data() {
    return {
      taskFullScreen: false, // 全屏
      loading: false,
      activeTab: 'overview',
      curriculumType: this.$route.query.curriculumType,
      type: this.$route.query.type,
      examType: this.$route.query.examType || null,
      id: this.$route.query.id,
      teachingName: this.$route.query.teachingName,
      answerTime: +this.$route.query.answerTime,
      questionList: [],
      dynamicPaperData: {}
    }
  },
  watch: {
    '$route': function(to, from) {
      const toId = to.query.hasOwnProperty('id') ? to.query.id : null
      const fromId = from.query.hasOwnProperty('id') ? from.query.id : null
      if (toId !== fromId) {
        this.curriculumType = this.$route.query.curriculumType
        this.type = this.$route.query.type
        this.examType = this.$route.query.examType || null
        this.answerTime = +this.$route.query.answerTime
        this.id = this.$route.query.id
        this.teachingName = this.$route.query.teachingName
        this.handleTabClick()
      }
    }
  },
  mounted() {
    this.activeTab = (this.$route.query.type == '编辑' && this.$route.query.btnType == '确定') ? 'video' : 'overview'
    if (this.$route.query.tabName) {
      this.activeTab = this.$route.query.tabName
    }
  },
  methods: {
    queryQuestionPractice() {
      const params = {
        contentId: this.$route.query.id,
        isShowAnswers: true
      }
      if (!['', null, undefined].includes(this.examType)) {
        params.examType = this.examType
      }
      queryPractice(params).then(res => {
        if (res.code == 0) {
          this.questionList = res.data || []
          // 动态试卷返回的对象
          if (Object.prototype.toString.call(res.data) === '[object Object]') {
            this.dynamicPaperData = res.data || {}
          }
        }
      })
    },
    handleTabClick() {
      if (this.activeTab === 'practice') {
        this.queryQuestionPractice()
      } else if (this.activeTab === 'topology') {
        setTimeout(() => {
          this.$refs['HandleTopo'].$refs['topo'].graph.centerContent()
        }, 200)
      }
    }
  }

}
</script>

<style lang="scss" scoped>
.screen-icon {
  position: absolute;
  top: 30px;
  left: 480px;
  z-index: 9999999;
  &.task-full-screen {
    position: fixed;
    top: 25px;
    left: 90px;
  }
  i {
    font-size: 20px;
    margin-left: 10px;
    cursor: pointer;
  }
}
.content-wrap {
  height: 100%;
  display: flex;
  flex-direction: column;
  > .detail-tabs {
    position: relative;
    margin-top: 26px;
    flex: 1;
    min-height: 0;
  }
  /deep/ .el-tabs__content {
    height: calc(100% - 34px);
  }
  /deep/ .el-tab-pane {
    height: 100%;
  }
  .detail-tabs-content {
    height: 100%;
    overflow-x: hidden;
    overflow-y: auto;
    padding: 15px;
    .el-col {
      height: 100%;
    }
    &.task-full-screen {
      background: #fff;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 3000;
    }
  }
  ::v-deep .el-tabs__nav-next, ::v-deep .el-tabs__nav-prev {
    line-height: 34px;
  }

  ::v-deep .report {
    .wrapper {
      padding: 0;
    }
  }
}
</style>
