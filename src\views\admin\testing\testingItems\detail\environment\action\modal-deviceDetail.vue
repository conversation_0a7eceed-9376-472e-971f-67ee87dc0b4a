<template>
  <div v-loading="loading" class="drawer-wrap">
    <div class="drawer-wrap-content">
      <el-form ref="form" :model="formData" style="padding: 0;" class="form-wrap" label-position="left" label-width="100px">
        <el-form-item label="设备名称">
          {{ formData.name || '-' }}
        </el-form-item>
        <el-form-item label="CPU">
          {{ formData.cpu || '-' }}
        </el-form-item>
        <el-form-item label="内存">
          {{ formData.ram ? $options.filters['transStoreShowInt'](formData.ram, 'MB') : '-' }}
        </el-form-item>
        <el-form-item label="系统盘大小">
          {{ formData.sys_disk_size ? $options.filters['transStoreShowInt'](formData.sys_disk_size, 'GB') :
          data[0].sysDiskSize ? $options.filters['transStoreShowInt'](data[0].sysDiskSize, 'GB') : '-' }}
        </el-form-item>
        <el-form-item label="数据盘大小">
          {{ formData.data_disk_size ? $options.filters['transStoreShowInt'](formData.data_disk_size, 'GB') :
          data[0].dataDiskSize ? $options.filters['transStoreShowInt'](data[0].dataDiskSize, 'GB') : '-' }}
        </el-form-item>
        <el-form-item label="镜像">
          {{ deviceData && deviceData.imageName ? deviceData.imageName : data[0].imageName ? data[0].imageName : '-' }}
        </el-form-item>
        <el-form-item label="端口">
          <el-table :data="formData.ports" border style="width: 100%">
            <el-table-column prop="name" label="名称" />
            <el-table-column prop="status" label="占用情况">
              <template slot-scope="scope">
                <div v-if="scope.row.imported">
                  已引入
                </div>
                <div v-else-if="scope.row.link_to">
                  已占用
                </div>
                <span v-else>{{ scope.row.reserved ? '已预留' : '-' }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="ip" label="IP地址" />
          </el-table>
        </el-form-item>
      </el-form>
    </div>
    <div class="drawer-footer">
      <el-button type="primary" @click="close">关闭</el-button>
    </div>
  </div>
</template>
<script>
import { getNodeItemDetailApi } from '@/api/testing/index.js'
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
import validate from '@/packages/validate'
import module from '../config.js'
import { getVirtualDetailApi } from '@/api/testing/index.js'

export default {
  components: {
  },
  mixins: [mixinsActionMenu],
  props: {
    // 传入数据
    data: {
      type: Array,
      default: () => {
        return []
      }
    },
    name: {
      type: String
    },
    envType: {
      type: String,
      default: 'network'
    }
  },
  data() {
    return {
      module,
      loading: false,
      validate: validate,
      formData: {
        name: '', // 设备名称
        cpu: '', // CPU
        ram: '', // 内存
        sys_disk_size: '', // 系统盘大小
        data_disk_size: '', // 数据盘大小
        imageName: '', // 镜像
        ports: [] // 端口信息（数组）
      },
      deviceData: null
    }
  },
  mounted() {
    this.getDetail()
  },
  methods: {
    getDetail() {
      if (this.envType == 'network') {
        this.loading = true
        getNodeItemDetailApi(this.data[0].id)
          .then(res => {
            this.formData = res['data']['data']
            this.deviceData = res['data']['data'].network_element_data || {}
            this.loading = false
          })
          .finally(() => {
            this.loading = false
          })
      }
      if (this.envType == 'virtual') {
        this.loading = true
        getVirtualDetailApi(this.data[0].id).then((res) => {
          this.formData = res['data']['data']
          this.deviceData = res['data']['data'].network_element_data || {}
          this.loading = false
        }).finally(() => {
          this.loading = false
        })
      }
    },
    close: function() {
      this.$emit('close')
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .el-button--ghost {
    border: 1px dashed #c8cacd !important;
}
/deep/ .el-table__cell {
  padding: 0;
}
/deep/ .el-tooltip__popper {
    max-width: 300px;
}
/deep/ .el-form .tableData .el-form-item:not(.is-error) {
  margin-bottom: 0;
}

.drawer-wrap {
  .drawer-wrap-content {
    overflow: auto;
    .tableData {
      /deep/ .el-input .el-input__inner {
        padding: 0 10px;
      }
    }
  }
  .form-wrap {
    /deep/ .el-select-dropdown__item.is-disabled {
      display: none;
    }
    /deep/ .time-select-item.disabled {
      display: none;
    }
  }
}
</style>
