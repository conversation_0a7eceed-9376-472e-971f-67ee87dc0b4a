<template>
  <div class="_c_report_container wrapper">
    <div v-if="type == '编辑'|| type==='创建'" class="btn-div">
      <el-button :disabled="practiceList.length < 1" class="return-btn" @click="courseArrangingVisible = true">
        <div class="fhpack">分数设置</div>
      </el-button>
      <el-button class="return-btn" @click="goAdd">
        <div class="fhpack">添加</div>
      </el-button >
    </div>
    <div v-if="practiceList" class="_paper_left">
      <!-- 题目类型 -->
      <div>
        <el-tabs v-model="currentType" tab-position="left" style="height: 100%;" @tab-click="handleClick">
          <el-tab-pane v-for="qt in questionTypeList" v-show="qt.length != 0" :key="qt.type" :label="qt.name" :name="qt.type"/>
        </el-tabs>
      </div>
      <div class="_question_details">
        <!-- 题目信息 -->
        <div>
          <div class="_question_list">
            <div
              v-for="(q, index) in questionList"
              :key="q.questionCode"
              :id="`qt_index_${index}`"
              class="_question_info"
            >
              <div class="_question_title_div">
                <div class="_question_title flex-left">
                  <div class="sort">{{ index + 1 }}. </div>
                  <div class="ml-15" v-html="q.content"/>
                </div>
                <div
                  class="questionStudentScore"
                >
                  {{ q.questionScore || 0 }}分
                </div>
              </div>
              <div v-if="q.questionType == '1'">
                <div
                  v-for="(op, i) in JSON.parse(q.questionOptions)"
                  :key="op"
                  class="_question_option"
                >
                  <el-radio
                    v-model="singleAnswerList['option' + index]"
                    :label="`${optionLabel[i]}`"
                    disabled
                  >{{ optionLabel[i] }}. {{ op }}</el-radio
                  >
                </div>
              </div>
              <div v-if="q.questionType == '2'">
                <el-checkbox-group
                  v-model="multiAnswerList['multiOption' + index]"
                >
                  <div
                    v-for="(op, i) in JSON.parse(q.questionOptions)"
                    :key="op"
                    class="_question_option"
                  >
                    <el-checkbox
                      :label="optionLabel[i]"
                      disabled
                    >{{ optionLabel[i] }}. {{ op }}</el-checkbox
                    >
                  </div>
                </el-checkbox-group>
              </div>
            </div>
            <el-empty
              v-if="questionList.length === 0 && noData"
              :image="img"
              :image-size="110"
              style="margin: 100px auto"
              description="暂无数据"
            />
          </div>
        </div>
      </div>
    </div>
    <el-empty
      v-else
      :image="img"
      :image-size="110"
      style="margin: 100px auto"
      description="暂无数据"
    />
    <el-dialog
      :visible.sync="courseArrangingVisible"
      title="分数设置"
      width="550px"
      center
    >
      <div>
        <div class="conten">
          <div>
            <span class="mr-10">单选题</span>
            <el-input-number v-model="singleTypeScore" :step="1" :min="0" step-strictly @change="(currentValue, oldValue) => numChange(currentValue, oldValue, '1')"/>
            <span class="ml-10">题目数量 <span class="color-main">{{ singleTypeQuantity }}</span> </span>
          </div>
          <div>
            <span class="mr-10">多选题</span>
            <el-input-number v-model="manyTypeScore" :step="1" :min="0" step-strictly @change="(currentValue, oldValue) => numChange(currentValue, oldValue, '2')"/>
            <span class="ml-10"> 题目数量 <span class="color-main">{{ manyTypeQuantity }}</span> </span>
          </div>
          <div class="input-div">
            <span class="mr-20">总分</span>
            <el-input v-model.trim="num" disabled/>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="courseArrangingVisible = false">取 消</el-button>
        <el-button
          type="primary" @click="editPractice()"
        >确 定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  updatePractice,
  queryPractice
} from '@/api/teacher/index.js'
import { debounce } from 'throttle-debounce'
import mixin from './mixin'
export default {
  mixins: [mixin],
  props: {
    curriculumCode: Number,
    sectionSeason: String,
    sectionTime: String,
    curriculumName: String
  },
  data() {
    return {
      questionTypeList: [
        { name: '单选题', type: '1', length: 0 },
        { name: '多选题', type: '2', length: 0 }
      ],
      courseArrangingVisible: false,
      total: 0, // 总题数,
      questionList: [],
      currentType: '1',
      optionLabel: Array.from({ length: 26 }, (_, index) => String.fromCharCode('A'.charCodeAt(0) + index)),
      singleArr: [],
      singleAnswerList: {}, // 单选题答案
      multiAnswerList: {}, // 多选题答案
      multiArr: [],
      practiceList: [],
      currentOrder: 1,
      studentSubmitQuestionBoList: [],
      evaluationCode: '',
      isExam: false,
      multipleChoiceList: [],
      singleChoiceList: [],
      totalScore: 0,
      multipleTotalScore: 0,
      singleTotalScore: 0,
      manyTypeScore: 1,
      singleTypeScore: 1,
      singleTypeQuantity: '',
      manyTypeQuantity: '',
      noData: false
    }
  },
  inject: ['parentVm'],
  computed: {
    num() {
      return (this.singleTypeScore * this.singleTypeQuantity) + (this.manyTypeScore * this.manyTypeQuantity)
    }
  },
  watch: {
    courseArrangingVisible(newVal, oldVal) {
      if (newVal) {
        this.searchCurriculum()
      }
    }
  },
  mounted() {
    this.searchCurriculum()
  },
  methods: {
    searchCurriculum: debounce(500, false, function() {
      // 这里放置需要防抖的操作
      queryPractice({ contentId: this.contentId }).then(res => {
        this.practiceList = res.data
        this.noData = true
        this.getPaperDetails()
      })
    }),
    editPractice() {
      if ((this.singleTypeScore > 5 || this.singleTypeScore < 1) || (this.manyTypeScore > 5 || this.manyTypeScore < 1)) {
        this.$message.warning('单选、多选每道题分数限制为1-5')
        return
      }
      const data = {
        contentId: this.contentId,
        singleScore: this.singleTypeScore,
        manyScore: this.manyTypeScore
      }
      this.courseArrangingVisible = false
      updatePractice(data).then(res => {
        this.$message.success('修改成功.')
        this.searchCurriculum()
      })
    },
    goAdd() {
      this.$router.replace({
        // path: '/teacher/testPaper/standAlone/build',
        name: 'trainingContentPractice',
        query: Object.assign({
          curriculumCode: this.$route.query.curriculumCode,
          evaluationCode: this.evaluationCode,
          id: this.contentId,
          active: this.parentVm.active,
          contentType: this.parentVm.contentType
        }, this.$route.query)
      })
    },
    // 交卷
    submitQuestion() {
      this.isExam = true
    },
    numChange(extraParam, val, valTwo) {
      if (!extraParam || extraParam > 5 || extraParam < 1) {
        this.$message.warning('单选、多选每道题分数限制为1-5')
        if (valTwo === '1') {
          this.$nextTick(() => {
            this.singleTypeScore = val
          })
        } else if (valTwo === '2') {
          this.$nextTick(() => {
            this.manyTypeScore = val
          })
        }
        return
      }
      if (valTwo === '1') {
        this.singleTypeScore = extraParam
      } else if (valTwo === '2') {
        this.manyTypeScore = extraParam
      }
    },
    handleClick(tab, event) {
      this.handleQuestionTypeClick(tab.name)
    },
    openFn() {
      for (const key in this.multiAnswerList) {
        if (this.multiAnswerList[key].length == 0) {
          this.open2()
          return
        }
      }
      for (const key in this.singleAnswerList) {
        if (!this.singleAnswerList[key] && this.singleAnswerList[key] != 0) {
          this.open2()
          return
        }
      }
      this.open()
    },
    open2() {
      this.$confirm('您还没答完试卷哦！确定现在提交答案吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.handPaper()
      }).catch(() => {
      })
    },
    open() {
      this.$confirm('恭喜您答完试卷！请确认提交答案！', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.handPaper()
      }).catch(() => {
      })
    },
    handPaper() {
      let index = 0
      for (const key in this.multiAnswerList) {
        const arr = []
        this.multiAnswerList[key].forEach(item => {
          arr.push(item)
        })
        const list = []
        arr.sort().forEach(item => {
          list.push(this.optionLabel[item])
        })
        this.multipleChoiceList.push({ questionCode: this.multiArr[index].questionCode, questionType: this.multiArr[index].questionType, questionUserAnswer: list.join(',') })
        index++
      }
      let index2 = 0
      for (const key in this.singleAnswerList) {
        this.singleChoiceList.push({ questionCode: this.singleArr[index2].questionCode, questionType: this.singleArr[index2].questionType, questionUserAnswer: this.optionLabel[this.singleAnswerList[key]] })
        index2++
      }
      this.submitQuestion()
    },
    handleUpload() {
      this.$refs.fileRef.click()
    },
    handleOrderClick(index) {
      this.currentOrder = index
      const el = document.getElementById(`qt_index_${index - 1}`)
      el.scrollIntoView({
        behavior: 'smooth'
      })
    },
    handleDownload() {},
    handleQuestionTypeClick(index) {
      this.currentType = index
      if (this.currentType == '1') {
        this.questionList = this.singleArr
      }
      if (this.currentType == '2') {
        this.questionList = this.multiArr
      }
      if (this.currentType == '3') {
        this.questionList = this.singleOneArr
      }
      if (this.currentType == '4') {
        this.questionList = this.simulatorArr
      }
      if (this.currentType == '5') {
        this.questionList = this.ctfArr
      }
    },
    // 试卷详情
    getPaperDetails() {
      if (!this.practiceList) {
        return
      }
      this.total = this.practiceList.length
      let singleTotal = 0
      let multipleToatal = 0
      this.practiceList.forEach(item => {
        this.totalScore += item.questionScore
        if (item.questionType == 2) {
          this.manyTypeScore = item.questionScore
          this.multipleTotalScore += item.questionScore
          multipleToatal++
        } else {
          this.singleTypeScore = item.questionScore
          singleTotal++
          this.singleTotalScore += item.questionScore
        }
      })
      this.manyTypeQuantity = multipleToatal
      this.singleTypeQuantity = singleTotal
      this.questionList = this.singleArr = this.practiceList.filter(q => q.questionType == '1')
      this.multiArr = this.practiceList.filter(q => q.questionType == '2')
      if (this.singleArr.length != 0) {
        this.currentType = '1'
      } else if (this.multiArr.length != 0) {
        this.currentType = '2'
      }
      this.questionTypeList[0].length = this.singleArr.length
      this.questionTypeList[1].length = this.multiArr.length
      this.handleQuestionTypeClick(this.currentType)
      this.singleArr.forEach((m, i) => {
        this.singleAnswerList['option' + i] = m.questionAnswer
      })
      this.multiArr.forEach((m, i) => {
        const anwserList = m.questionAnswer.split('')
        this.multiAnswerList['multiOption' + i] = anwserList
      })
    }
  }
}
</script>
<style lang="scss" scoped>
._c_report_container {
  // height: 100%;
}
._paper_left {
  margin-left: 10px;
  display: flex;
  height: 100%;
  ._question_header {
    background: #ffffff;
    padding: 20px 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    ._question_type_list {
      display: flex;
      align-items: center;
      color: #999999;

      ._question_type {
        margin-right: 30px;
        cursor: pointer;
      }

      ._question_type_check {
        color: #333333;

        &:after {
          content: "";
          top: 36px;
          left: 50%;
          transform: translateX(-50%);
          background: #288fef;
          width: 42.8px;
          height: 2.1px;
        }
      }
    }

    ._question_stat {
      display: flex;
      margin-right: 30px;

      ._question_stat_1 {
        font-size: 14px;
        margin-left: 30px;

        :nth-child(1) {
          color: #999999;
        }
        :nth-child(2) {
          color: #288fef;
        }
        :nth-child(3) {
          color: #333333;
        }
      }
    }
  }

  ._question_details {
    width: 100%;
    ._question_list {
      ::v-deep {
        .el-input__inner {
          border-radius: 10px;
        }
      }
      ._question_info {
        margin-bottom: 13px;
        padding: 20px;
        background: #ffffff;
        width: 100%;
        ._question_title_div {
          margin-bottom: 27px;
          display: flex;
          justify-content: space-between;
          ._question_title {
            font-size: 14px;
            font-family: Source Han Sans CN;
            font-weight: 400;
            color: #4e5969;
            line-height: 22px;
            position: relative;
            .sort {
              position: absolute;
              top: 0;
              left: 0;
            }
          }
          .a-div {
            margin-bottom: 20px;
            a {
              font-size: 12px;
              color: #288fef;
              text-decoration: underline !important;
            }
          }
          .questionStudentScore_red {
            min-width: 60px;
            display: flex;
            justify-content: end;
            color: red;
            line-height: 22px;
          }
          .questionStudentScore {
            min-width: 60px;
            display: flex;
            justify-content: end;
            color: #2bdb35;
            line-height: 22px;
          }
        }
        ._question_option {
          padding: 5px 0 10px;
          font-size: 14px;
        }
      }
    }
    ._question_list_button {
      padding-top: 13px;
      display: flex;
      justify-content: center;
    }
    ._question_sn {
      width: 270px;
      margin-left: 32px;
      margin-top: 13px;
      padding: 30px 25px;
      background: #ffffff;

      ._question_sn_order {
        margin-top: 29px;
        display: flex;
        flex-wrap: wrap;

        ._question_sn_order_item {
          width: 28px;
          height: 28px;
          color: #666666;
          border: 1px solid #d4d4d4;
          font-size: 14px;
          margin-right: 16px;
          margin-bottom: 16px;
          line-height: 28px;
          text-align: center;
          border-radius: 4px;
          cursor: pointer;
        }

        ._question_sn_order_item_check {
          background: #288fef;
          color: white;
          border: none;
        }
      }

      ._paper_info_stat {
        font-size: 14px;

        :nth-child(1) {
          color: #999999;
        }

        :nth-child(2) {
          color: #288fef;
        }

        :nth-child(3) {
          color: #999999;
        }
      }

      ._paper_info_title {
        color: #333333;
        padding-left: 9px;
        margin-bottom: 17px;

        &:before {
          content: "";
          left: 0;
          width: 4px;
          height: 20px;
          background: #288fef;
        }
      }
    }
  }
}
.conten{
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  div{
    margin: 10px 0 ;
  }
  .input-div{
    display: flex;
    align-items: center;
    .mr-20 {
      margin-right: 28px;
    }
    ::v-deep{
      .el-input{
        width: 50%;
      }
    }
  }
}
.btn-div{
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
  .return-btn {
    font-size: 14px;
    font-family: Source Han Sans CN;
    font-weight: 400;
    color: #ffffff;
    line-height: 32px;
    background: var(--color-600);
    display: flex;
    align-items: center;
    padding: 0 16px;
    border-radius: 4px;
    margin: 0 5px;
    cursor: pointer;
  }
  .return-btn:hover {
    background: #0e42d2;
  }
::v-deep .el-checkbox {
  margin-bottom: 17px;
  display: flex;
  align-items: flex-start;

  .el-checkbox__label {
    font-size: 16px;
    white-space: normal;
    word-break: break-all;
  }
}
/deep/ .el-dialog__footer {
  display: flex;
  justify-content: end;
}

/deep/ .el-dialog__body {
  padding-bottom: 10px;
}

/deep/ .el-dialog__header {
  border-bottom: 1px #e6e6e6 solid !important;
}
</style>
