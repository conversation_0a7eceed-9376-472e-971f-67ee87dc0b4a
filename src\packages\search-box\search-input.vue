<template>
  <div class="search-input-wrap">
    <span v-if="searchActive" class="search-key-text">{{ searchActive['label'] }}：</span>
    <!-- 类型选择 -->
    <template v-if="!searchActive">
      <el-dropdown
        v-if="searchKeyListView.length"
        :disabled="dropdownDisbaled"
        class="search-value-wrap"
        placement="bottom-start"
        trigger="click"
        size="medium"
        @command="selectKey"
      >
        <!--            默认输入-->
        <el-input
          ref="input_type"
          :maxlength="activeInputMaxLength"
          v-model.trim="searchValue"
          :placeholder="inputPlaceholder"
          size="mini"
          @keyup.enter.native="enterHandler"
          @keyup.backspace.native="backspaceHandler"
          @blur="blurHandler"
        />
        <el-dropdown-menu slot="dropdown" :visible-arrow="false" class="aaa">
          <el-dropdown-item v-for="keyItem in searchKeyListView" :key="keyItem.key" :command="keyItem" style="min-width: 120px;">{{ keyItem.label }}</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </template>
    <!--文字输入-->
    <template v-else-if="!searchActive.hasOwnProperty('type')">
      <el-input
        ref="input"
        :maxlength="activeInputMaxLength"
        v-model.trim="searchValue"
        :placeholder="inputPlaceholder"
        size="mini"
        class="search-input"
        @keyup.enter.native="enterHandler"
        @keyup.backspace.native="backspaceHandler"
        @blur="blurHandler"
      />
    </template>
    <!--单选输入-->
    <template v-else-if="searchActive['type'] === 'radio'">
      <el-popover
        v-model="selectView"
        placement="bottom-start"
        transition="el-zoom-in-top"
        trigger="click"
        class="search-value-wrap"
        @hide="hidePopover"
      >
        <radio-view
          :value="searchValue"
          :data="searchActive['valueList']"
          @confirm="enterRadioHandler"
          @cancel="cancelSelectView"
        />
        <el-input
          slot="reference"
          ref="input"
          :placeholder="inputPlaceholder"
          :maxlength="0"
          size="mini"
          class="search-input"
          @keyup.backspace.native="backspaceHandler"
        />
      </el-popover>
    </template>
    <!--多选输入-->
    <template v-else-if="searchActive['type'] === 'select'">
      <el-popover
        v-model="selectView"
        placement="bottom-start"
        transition="el-zoom-in-top"
        trigger="click"
        class="search-value-wrap"
        @hide="hidePopover"
      >
        <select-view
          :data="searchActive['valueList']"
          :value="searchValue"
          @confirm="enterSelectHandler"
          @cancel="cancelSelectView"
        />
        <el-input
          ref="input"
          slot="reference"
          :placeholder="inputPlaceholder"
          :maxlength="0"
          size="mini"
          class="search-input"
          @keyup.backspace.native="backspaceHandler"
        />
      </el-popover>
    </template>
    <!-- 多选颜色 -->
    <template v-else-if="searchActive['type'] === 'select-color'">
      <el-popover
        v-model="selectView"
        :width="150"
        placement="bottom-start"
        transition="el-zoom-in-top"
        trigger="click"
        class="search-value-wrap"
        @hide="hidePopover"
      >
        <color-select-view
          :data="searchActive['valueList']"
          :value="searchValue"
          @confirm="enterSelectHandler"
          @cancel="cancelSelectView"
        />
        <el-input
          ref="input"
          slot="reference"
          :placeholder="inputPlaceholder"
          :maxlength="0"
          size="mini"
          class="search-input"
          @keyup.backspace.native="backspaceHandler"
        />
      </el-popover>
    </template>
    <!-- 标签 -->
    <template v-else-if="searchActive['type'] === 'select-tag'">
      <el-popover
        v-model="selectView"
        :width="200"
        placement="bottom-start"
        transition="el-zoom-in-top"
        trigger="click"
        class="search-value-wrap"
        @hide="hidePopover"
      >
        <tag-select-view
          :data="searchActive['valueList']"
          :tab="searchActive['tab']"
          :value="searchValue"
          @confirm="enterSelectHandler"
          @cancel="cancelSelectView"
        />
        <el-input
          ref="input"
          slot="reference"
          :placeholder="inputPlaceholder"
          :maxlength="0"
          size="mini"
          class="search-input"
          @keyup.backspace.native="backspaceHandler"
        />
      </el-popover>
    </template>
    <!--时间输入:兼容目前诸多项目传递 time_range 作为时间选择的情况-->
    <template v-else-if="searchActive['type'] === 'time_range'">
      <el-date-picker
        ref="input"
        v-model="searchValue"
        :value-format="searchActive['format'] || 'yyyy-MM-dd HH:mm:ss'"
        :default-time="['00:00:00', '23:59:59']"
        :clearable="false"
        :start-placeholder="inputPlaceholder"
        class="search-value-wrap"
        end-placeholder=""
        type="datetimerange"
        align="left"
        @blur="blurTimeHandler"
        @keyup.backspace.native="backspaceHandler"
        @change="enterTimeHandler"
      />
    </template>
    <!--日期时间输入:其余情况-->
    <template v-else-if="supportTime.includes(searchActive['type'])">
      <el-date-picker
        ref="input"
        v-model="searchValue"
        :key="searchActive['key']"
        :value-format="searchActive['format'] || 'yyyy-MM-dd HH:mm:ss'"
        :format="searchActive['format'] || 'yyyy-MM-dd HH:mm:ss'"
        :default-time="searchActive['type'] === 'datetimerange' || searchActive['type'] === 'daterange' ? ['00:00:00', '23:59:59'] : '00:00:00'"
        :clearable="false"
        :type="searchActive['type']"
        :start-placeholder="inputPlaceholder"
        class="search-value-wrap"
        end-placeholder=""
        align="left"
        @blur="blurTimeHandler"
        @change="enterTimeHandler"
        @keyup.backspace.native="backspaceHandler"
      />
    </template>
  </div>
</template>
<script>
import lodash from 'lodash'
import selectView from './dropdown-select.vue'
import radioView from './dropdown-radio.vue'
import colorSelectView from './dropdown-color-select.vue'
import tagSelectView from './dropdown-tag-select.vue'
export default {
  components: {
    selectView,
    radioView,
    colorSelectView,
    tagSelectView
  },
  props: {
    // 传递此key代表编辑模式，具体值为对应的搜索key
    editKey: {
      type: String,
      default: null
    },
    // 搜索框placeholder
    defaultPlaceholder: {
      type: String,
      default: '默认搜索名称'
    },
    // 检索key:value集合
    searchObj: {
      type: Object,
      default: () => {
        return {}
      }
    },
    // 搜索key数据
    searchKeyList: {
      type: Array,
      default: () => {
        return []
      }
    },
    // 输入框最大允许输入字符长度
    inputMaxLength: {
      type: Number,
      default: 64
    }
  },
  data() {
    return {
      // 选择可见
      selectView: false,
      // 当前检索值
      searchValue: null,
      // 当前检索类型对象
      searchActive: null,
      shouldClearSearchActive: true,
      supportTime: ['year', 'month', 'date', 'dates', 'months', 'years', 'week', 'datetime', 'datetimerange', 'daterange', 'monthrange'], // 支持时间选择的所有类型
      supportRangeTime: ['datetimerange', 'daterange', 'monthrange'] // 支持范围选择的类型
    }
  },
  computed: {
    // 当前输入框可数组最大字符限制
    'activeInputMaxLength': function() {
      if (this.haveMasterItem && !this.searchActive) {
        // 默认
        return 0
      } else {
        if (this.searchActive) {
          return this.searchActive.hasOwnProperty('maxLength') ? this.searchActive['maxLength'] : this.inputMaxLength
        } else {
          let masterItem = null
          for (let i = 0; i < this.searchKeyList.length; i++) {
            if (this.searchKeyList[i]['master']) {
              masterItem = this.searchKeyList[i]
            }
          }
          if (masterItem) {
            return masterItem.hasOwnProperty('maxLength') ? masterItem['maxLength'] : this.inputMaxLength
          } else {
            console.log('搜索key列表缺少master')
          }
        }
      }
      // return (this.haveMasterItem && !this.searchActive) ? 0 : (this.searchActive.hasOwnProperty('maxLength') ? this.searchActive['maxLength'] : this.inputMaxLength)
    },
    // 是否处于编辑模式
    'editMode': function() {
      return this.editKey !== null
    },
    // 可选搜索key
    'searchKeyListView': function() {
      const data = []
      for (let i = 0; i < this.searchKeyList.length; i++) {
        if (!this.searchObj.hasOwnProperty(this.searchKeyList[i]['key'])) {
          data.push(this.searchKeyList[i])
        }
      }
      return data
    },
    // 输入框提示文字
    'inputPlaceholder': function() {
      if (this.searchActive) {
        let defaulText = '请输入'
        if (this.searchActive['type']) {
          defaulText = '请选择'
        }
        return this.searchActive.hasOwnProperty('placeholder') ? this.searchActive['placeholder'] : defaulText
      } else {
        return this.haveMasterItem ? null : this.defaultPlaceholder
      }
    },
    // 是否已存在默认key搜索项
    'haveMasterItem': function() {
      if (!this.masterKey) return true
      return this.searchObj.hasOwnProperty(this.masterKey)
    },
    // 可用搜索key下拉菜单是否禁止弹出判断
    'dropdownDisbaled': function() {
      if (this.searchValue) return true
      if (!this.searchKeyListView.length) return true
      if (this.searchKeyListView.length === 0 && this.searchKeyListView[0].hasOwnProperty('master') && this.searchKeyListView[0]['master']) {
        return true
      }
      return false
    },
    // 默认搜索key
    'masterKey': function() {
      for (let i = 0; i < this.searchKeyList.length; i++) {
        if (this.searchKeyList[i]['master']) {
          return this.searchKeyList[i]['key']
        }
      }
    }
  },
  mounted() {
    if (this.editMode) {
      for (let i = 0; i < this.searchKeyList.length; i++) {
        if (this.searchKeyList[i]['key'] === this.editKey) {
          this.searchActive = this.searchKeyList[i]
        }
      }
      this.selectView = true
      if (this.searchActive.hasOwnProperty('type') && (this.supportRangeTime.includes(this.searchActive['type']) || this.searchActive['type'] === 'time_range')) {
        this.searchValue = this.searchObj[this.editKey].split(',')
      } else {
        this.searchValue = this.searchObj[this.editKey]
      }
      setTimeout(() => {
        this.$refs['input'].focus()
      }, 50)
    }
  },
  methods: {
    // 清空
    'clearActive': function() {
      this.searchActive = null
      this.addSearchItem()
    },
    // 关闭单选/多选
    'cancelSelectView': function() {
      this.selectView = false
      this.addSearchItem()
    },
    // 单选确定
    'enterRadioHandler': function(data) {
      this.searchValue = data
      this.addSearchItem()
    },
    // 多选确定
    'enterSelectHandler': function(data) {
      if (this.searchActive['type'] === 'select-tag') {
        const val = data.map(item => item.id)
        this.searchValue = val.join()
        this.addSearchItem()
        return
      }
      this.searchValue = data.join()
      this.addSearchItem()
    },
    // input失去焦点
    'blurHandler': function() {
      this.addSearchItem()
    },
    // input回车确定
    'enterHandler': function() {
      this.addSearchItem()
    },
    // 选择时间确定
    'enterTimeHandler': function(data) {
      this.addSearchItem(data)
    },
    // 弹出框消失触发
    'hidePopover': function() {
      if (this.editMode) {
        this.addSearchItem()
      }
    },
    // 选择时间失去焦点
    'blurTimeHandler': function() {
      this.addSearchItem()
    },
    // 退格监听
    'backspaceHandler': function() {
      if (!this.searchActive) {
        return
      }
      if (this.searchValue) {
        this.shouldClearSearchActive = false
        return
      }
      if (this.shouldClearSearchActive) {
        if (!this.editMode) {
          this.searchActive = null
        } else {
          this.addSearchItem()
        }
      }
      this.shouldClearSearchActive = true
    },
    // 添加检索项目
    'addSearchItem': lodash.debounce(function(data) {
      if (this.editMode) {
        if (!this.searchActive) return
        let outData = data || this.searchValue
        // range选择情况
        if (this.searchActive.hasOwnProperty('type') && (this.supportRangeTime.includes(this.searchActive['type']) || this.searchActive['type'] === 'time_range')) {
          outData = outData ? outData.join() : outData
        }
        this.$emit('editItem', { 'key': this.editKey, 'value': outData })
        this.searchActive = null
      } else {
        if (!this.searchValue) return
        let outData = data || this.searchValue
        // range选择情况
        if (this.searchActive && this.searchActive.hasOwnProperty('type') && (this.supportRangeTime.includes(this.searchActive['type']) || this.searchActive['type'] === 'time_range')) {
          outData = outData ? outData.join() : outData
        }
        this.$emit('addSearchItem', { 'key': (this.searchActive ? this.searchActive['key'] : null) || this.masterKey, 'value': outData })
        this.searchValue = null
        this.searchActive = null
      }
    }, 50),
    // 下拉框选择检索key
    'selectKey': function(item) {
      this.searchActive = item
      window.setTimeout(() => {
        this.$refs['input'].focus()
      }, 50)
    }
  }
}
</script>
