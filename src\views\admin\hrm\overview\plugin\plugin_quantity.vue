<template>
  <!-- 资源数量统计 个人 队伍-->
  <div :class="['plugin-view',pluginTitle ==='教学专业及班级'?'mb-20':'']">
    <h3 class="plugin-title" style="margin-bottom: 20px;">{{ pluginTitle }}</h3>
    <div v-loading="true" v-if="loading" class="plugin-loading" />
    <div v-else-if="!apiData" class="flex-1 flex-center">暂无数据</div>
    <div v-else class="plugin-quantity-view">
      <div v-for="(value, key) in apiData" :key="key" class="quantity-item">
        <svg v-if="pluginTitle === '实训用户' && key === 'students'" t="1712045414668" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="61204" width="200" height="200"><path d="M763.77 722.223l223.564 1.598c0-116.364-56.902-282.826-204.402-282.826-78.702 0-122.484 40.624-155.771 97.402 67.661 34 108.295 110.073 136.61 183.826z" fill="#2c2c2c" p-id="61205"/><path d="M782.931 139.261c-41.377 0-78.769 17.046-105.555 44.491 26.99 34.754 43.069 78.406 43.069 125.817 0 34.923-8.73 67.803-24.11 96.594 24.318 17.668 54.237 28.096 86.596 28.096 81.462 0 147.5-66.037 147.5-147.498 0-81.462-66.037-147.5-147.5-147.5z" fill="#2c2c2c" p-id="61206"/><path d="M226.946 721.132l-223.564 1.598c0-116.364 56.902-282.826 204.402-282.826 78.702 0 122.484 40.624 155.771 97.402-67.661 34-108.295 110.073-136.61 183.826z" fill="#2c2c2c" p-id="61207"/><path d="M207.785 138.171c41.377 0 78.769 17.046 105.555 44.491-26.99 34.754-43.069 78.406-43.069 125.817 0 34.923 8.73 67.803 24.11 96.594-24.318 17.668-54.237 28.096-86.596 28.096-81.462 0-147.5-66.037-147.5-147.498 0-81.462 66.037-147.5 147.5-147.5z" fill="#2c2c2c" p-id="61208"/><path d="M216.535 913.737c0-161.974 72.716-389.666 278.030-389.666 205.316 0 284.522 231.71 284.522 393.685z" fill="#2c2c2c" p-id="61209"/><path d="M289.251 309.379c0 0 0 0 0 0 0 113.392 91.923 205.315 205.315 205.315 113.392 0 205.315-91.923 205.315-205.315 0 0 0 0 0 0 0-113.392-91.923-205.315-205.315-205.315-113.392 0-205.315 91.923-205.315 205.315z" fill="#2c2c2c" p-id="61210"/></svg>
        <svg v-if="pluginTitle === '实训用户' && key === 'teachers'" t="1712045351162" class="icon" viewBox="0 0 1206 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="55511" width="200" height="200"><path d="M1144.508487 204.734365h-234.360632a34.022034 34.022034 0 0 0 0 68.044068h234.360632a34.022034 34.022034 0 0 0 0-68.044068z m10.838878 193.293326a34.022034 34.022034 0 0 0-43.536161 16.860477l-163.486411 415.490328a480.644029 480.644029 0 0 0-310.654291-301.682107 271.574113 271.574113 0 0 0 130.78913-233.03588A279.221544 279.221544 0 0 0 490.56284 16.860477a288.374374 288.374374 0 0 0-283.376447 278.800032 271.574113 271.574113 0 0 0 130.789129 233.03588A489.555995 489.555995 0 0 0 0.103605 938.165118a75.691499 75.691499 0 0 0 21.798188 57.20519 86.470161 86.470161 0 0 0 60.21599 28.301515h817.432058a60.998797 60.998797 0 0 0 54.194391-22.882076 73.764587 73.764587 0 0 0 21.798188-56.60303v-6.021599a19.389549 19.389549 0 0 0 10.899094-11.441038l190.764255-482.932237a42.572705 42.572705 0 0 0-21.858404-45.764152zM485.083185 967.068793l-103.511286-113.808221 65.394564-278.800031h76.293659l65.394565 278.800031zM1171.545466 0h-305.174635a34.022034 34.022034 0 0 0 0 68.044068h305.174635A34.022034 34.022034 0 0 0 1171.726114 0z" fill="#6B6F71" p-id="55512"/></svg>
        <svg v-if="pluginTitle === '实训用户' && key === 'assistants'" t="1712045488706" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="62310" width="200" height="200"><path d="M469.312 597.376v341.312H128a341.312 341.312 0 0 1 341.312-341.312z m0-42.688a256 256 0 1 1 256-256 256 256 0 0 1-256 256z m384 170.688H896v213.312H554.688v-213.312h42.688v-42.688a128 128 0 0 1 256 0z m-85.312 0v-42.688a42.688 42.688 0 1 0-85.312 0v42.688z" fill="#2c2c2c" p-id="62311"/></svg>
        <svg v-if="pluginTitle === '课程内容' && key === 'theoryCourse'" t="1712045907807" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="69795" width="200" height="200"><path d="M324.512 161.312c57.232 0 117.776 22.384 181.92 66.064l6.208 4.288 6.032-4.064c63.232-42.32 117.952-64.72 165.456-66.208l5.056-0.08h235.248a36 36 0 0 1 36 36v559.424a36 36 0 0 1-36 36l-234.032-0.032c-56.592 1.92-108.048 22.144-155.424 61.44a36 36 0 0 1-46.336-0.32c-45.552-38.88-100.16-59.168-164.128-61.088H100.16a36 36 0 0 1-36-36V197.312a36 36 0 0 1 36-36zM136.16 233.28v487.424l189.424 0.032c55.264 1.648 105.552 14.304 150.416 37.936V293.92c-57.056-40.816-107.648-60.608-151.488-60.608L136.16 233.28z m752.272 0H689.184c-34.144 0-81.76 20.08-141.184 61.456v463.92a315.2 315.2 0 0 1 133.552-37.6l7.632-0.336 199.248-0.016V233.296z m-519.68 261.712a36 36 0 0 1 2.48 71.904l-2.464 0.08h-129.28a36 36 0 0 1-2.48-71.904l2.464-0.096h129.28z m416 0a36 36 0 0 1 2.48 71.904l-2.464 0.08h-129.28a36 36 0 0 1-2.48-71.904l2.464-0.096h129.28z m-416-139.008a36 36 0 0 1 2.48 71.92l-2.464 0.08h-129.28a36 36 0 0 1-2.48-71.92l2.464-0.08h129.28z m416 0a36 36 0 0 1 2.48 71.92l-2.464 0.08h-129.28a36 36 0 0 1-2.48-71.92l2.464-0.08h129.28z" fill="#2c2c2c" p-id="69796"/></svg>
        <svg v-if="pluginTitle === '课程内容' && key === 'simulationCourse'" t="1712044978160" class="icon" viewBox="0 0 1032 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="17016" width="200" height="200"><path d="M1006.933333 128c-59.733333-25.6-162.133333-59.733333-256-68.266667-85.333333-8.533333-170.666667 0-238.933333 51.2C452.266667 59.733333 358.4 51.2 273.066667 59.733333c-93.866667 8.533333-196.266667 42.666667-256 68.266667-8.533333 8.533333-17.066667 17.066667-17.066667 34.133333v699.733334c0 17.066667 17.066667 34.133333 34.133333 34.133333h17.066667c59.733333-25.6 145.066667-59.733333 238.933333-68.266667 93.866667-8.533333 162.133333 8.533333 204.8 59.733334 8.533333 17.066667 34.133333 17.066667 42.666667 8.533333l8.533333-8.533333c42.666667-51.2 119.466667-68.266667 204.8-59.733334 85.333333 8.533333 179.2 42.666667 238.933334 68.266667 17.066667 8.533333 34.133333 0 42.666666-17.066667V162.133333c-8.533333-17.066667-17.066667-25.6-25.6-34.133333zM477.866667 793.6C418.133333 759.466667 341.333333 759.466667 273.066667 768c-76.8 0-153.6 25.6-204.8 51.2V179.2c59.733333-25.6 136.533333-51.2 213.333333-59.733333 85.333333-8.533333 153.6 0 196.266667 51.2v622.933333z m477.866666 25.6c-51.2-25.6-128-51.2-204.8-51.2-68.266667-8.533333-145.066667-8.533333-204.8 25.6V170.666667c42.666667-42.666667 110.933333-59.733333 196.266667-51.2 76.8 8.533333 162.133333 34.133333 213.333333 59.733333v640z" p-id="17017" fill="#2c2c2c"/></svg>
        <svg v-if="pluginTitle === '教学专业及班级' && key === 'majorNumber'" t="1712043823997" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="9175" width="200" height="200"><path d="M513.719666 696.024224a92.227518 92.227518 0 0 1-46.01501-12.31856L221.720266 541.826306V818.310338a30.699186 30.699186 0 0 0 11.217482 23.725354c3.959172 3.252067 99.053993 79.628572 280.121886 79.628572 183.647647 0 275.135314-76.801177 278.94099-80.069617a30.696116 30.696116 0 0 0 10.690479-23.284309V544.739659L559.450197 683.868369a92.230588 92.230588 0 0 1-45.730531 12.155855z" fill="#2c2c2c" p-id="9176"/><path d="M959.491289 367.504048c0.010233-0.335644 0.033769-0.670266 0.033769-1.006933a30.699186 30.699186 0 0 0-15.45704-26.647917L528.960789 102.417601a30.692023 30.692023 0 0 0-30.209023-0.154519L80.466148 335.863421a30.696116 30.696116 0 0 0-15.72924 26.589588 30.696116 30.696116 0 0 0 15.359826 26.805506l418.284595 241.260809a30.676673 30.676673 0 0 0 15.337314 4.106528 30.66951 30.66951 0 0 0 15.242146-4.05127l349.723079-200.032825v327.904145c0 22.606881 18.325367 40.932248 40.932248 40.932248s40.932248-18.325367 40.932248-40.932248V376.729154c0-3.174296-0.373507-6.259564-1.057075-9.225106z" fill="#2c2c2c" p-id="9177"/></svg>
        <svg v-if="pluginTitle === '教学专业及班级' && key === 'classNumber'" t="1712044817543" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="11327" width="200" height="200"><path d="M853.461333 780.586667V426.666667h-85.333333v353.92l-26.368 9.429333q-111.36 39.765333-229.632 39.765333-118.229333 0-229.589333-39.765333l-26.410667-9.386667V426.666667h-85.333333v353.92q0 27.050667 15.616 49.152 15.573333 22.144 41.045333 31.232l26.368 9.386666q125.312 44.8 258.304 44.8 133.034667 0 258.346667-44.8l26.368-9.386666q25.472-9.088 41.045333-31.232 15.573333-22.101333 15.573333-49.152z" p-id="11328" fill="#2c2c2c"/><path d="M896.128 341.333333m42.666667 0l0 0q42.666667 0 42.666666 42.666667l0 213.333333q0 42.666667-42.666666 42.666667l0 0q-42.666667 0-42.666667-42.666667l0-213.333333q0-42.666667 42.666667-42.666667Z" p-id="11329" fill="#2c2c2c"/><path d="M547.456 97.194667q-35.328-16.042667-70.613333 0L25.173333 302.506667q-3.84 1.706667-7.253333 4.181333-3.413333 2.432-6.272 5.546667-2.901333 3.029333-5.12 6.613333-2.176 3.584-3.669333 7.509333-1.450667 3.925333-2.133334 8.106667-0.682667 4.096-0.554666 8.32 0.128 4.181333 1.109333 8.277333 0.938667 4.096 2.688 7.936 3.541333 7.808 9.856 13.653334 6.314667 5.845333 14.421333 8.789333l454.741334 165.376q29.141333 10.581333 58.325333 0L996.053333 381.44q3.968-1.450667 7.552-3.626667 3.584-2.176 6.698667-5.034666 3.072-2.816 5.546667-6.186667 2.517333-3.413333 4.266666-7.253333 1.792-3.754667 2.816-7.850667 0.981333-4.096 1.152-8.277333 0.213333-4.181333-0.426666-8.362667-0.64-4.138667-2.090667-8.106667-2.944-8.106667-8.746667-14.378666-5.845333-6.314667-13.653333-9.856L547.413333 97.194667z m-391.893333 239.786666l356.565333-162.133333 356.565333 162.133333-356.565333 129.621334-356.565333-129.664z" p-id="11330" fill="#2c2c2c"/></svg>
        <div v-if="arrName.indexOf(key) !== -1 && pluginTitle === compareName" class="value">
          <template>
            {{ value }}
          </template>
        </div>
        <div v-if="pluginTitle === '实训用户' && key === 'students'" class="label">学员数量</div>
        <div v-else-if="pluginTitle === '实训用户' && key === 'teachers'" class="label">教师数量</div>
        <div v-else-if="pluginTitle === '实训用户' && key === 'assistants'" class="label">助教数量</div>
        <div v-else-if="pluginTitle === '课程内容' && key === 'theoryCourse'" class="label">理论课数量</div>
        <div v-else-if="pluginTitle === '课程内容' && key === 'simulationCourse'" class="label">仿真课数量</div>
        <div v-else-if="pluginTitle === '教学专业及班级' && key === 'majorNumber'" class="label">专业数量</div>
        <div v-else-if="pluginTitle === '教学专业及班级' && key === 'classNumber'" class="label">班级数量</div>
      </div>
    </div>
  </div>
</template>
<style lang="scss">
.plugin-quantity-view {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding-top: 15px;
  .quantity-item {
    flex: 1;
    text-align: center;
    border-right: solid 1px #dbdde0;
    display: flex;
    flex-direction: column;
    align-items: center;
    &:last-child {
      border-right: none;
    }
    .icon {
      width: 36px;
      height: 36px;
      display: inline-block;
      margin-bottom: 10px;
      fill: black;
      path {
        fill: black;
      }
    }
    .value {
      margin-bottom: 10px;
      font-size: 20px;
      font-weight: 600;
      color: #333;
      a {
        color: #333;
        &:hover {
          color: var(--color-600);
        }
      }
    }
    .label {
      font-size: 14px;
      color: var(--neutral-600);
    }
  }
}
</style>
<script>
import pluginMixin from './mixin_plugin.js'
export default {
  mixins: [
    pluginMixin
  ],
  props: {
    data: {
      type: Object
    },
    personData: {
      type: Object
    }
  },
  watch: {
    personData: {
      handler(newVal) {
        // 处理对象参数的变化
        if (this.pluginTitle === '实训用户') {
          const { students, teachers, assistants } = newVal
          this.apiData = { students, teachers, assistants }
        } else if (this.pluginTitle === '课程内容') {
          const { theoryCourse, simulationCourse } = newVal
          this.apiData = { theoryCourse, simulationCourse }
        } else if (this.pluginTitle === '教学专业及班级') {
          const { majorNumber, classNumber } = newVal
          this.apiData = { majorNumber, classNumber }
        }
      },
      immediate: true,
      deep: true // 监听对象的深层变化
    }
  },
  methods: {
    'getData': function(hideloading) {
      if (this.pluginTitle === '实训用户') {
        this.arrName = ['students', 'teachers', 'assistants']
        this.compareName = '实训用户'
      } else if (this.pluginTitle === '课程内容') {
        this.arrName = ['theoryCourse', 'simulationCourse']
        this.compareName = '课程内容'
      } else if (this.pluginTitle === '教学专业及班级') {
        this.arrName = ['majorNumber', 'classNumber']
        this.compareName = '教学专业及班级'
      }
      if (!hideloading) {
        this.loading = true
      }
      this.loading = false
    }
  }
}
</script>
