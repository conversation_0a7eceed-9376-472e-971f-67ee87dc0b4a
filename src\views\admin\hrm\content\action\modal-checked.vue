<template>
  <div class="dialog-wrap">
    <el-alert :closable="false" type="warning">
      <div slot="title">
        <p v-if="checkType == 'inSelective'">该内容所在课程已被学员加入自学列表，编辑后在学员侧将同步编辑！</p >
        <p v-if="checkType == 'inCourse'">该课程内容已排课，请谨慎操作！</p >
      </div>
    </el-alert>
    <el-checkbox v-model="checked">我已知晓上述风险</el-checkbox>
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button :disabled="!checked" type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    data: {
      type: Array,
      default: () => []
    },
    checkType: {
      type: String,
      default: 'inSelective' // inSelective 被加入自学；inCourse 被排课
    }
  },
  data() {
    return {
      checked: false
    }
  },
  mounted() {

  },
  methods: {
    close() {
      this.$emit('close')
    },
    confirm: function() {
      this.$emit('call', 'detail')
    }
  }
}
</script>
