<template>
  <div v-loading="loading" class="dialog-wrap">
    <el-alert :closable="false" type="warning">
      <div slot="title">
        <p>此操作会删除虚拟设备的实例，以及释放物理设备的端口。请谨慎操作。</p >
      </div>
    </el-alert>
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>

<script>
import modalMixins from '../../mixins/modal_form'

export default {
  mixins: [modalMixins],
  data() {
    return {
      loading: false
    }
  },
  methods: {
    close() {
      this.$emit('close')
    },
    confirm() {
      this.$emit('call', 'cleanTopology')
      this.close()
    }
  }
}
</script>
