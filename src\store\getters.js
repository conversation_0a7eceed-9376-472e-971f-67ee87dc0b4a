const getters = {
  theme: state => state.app.theme,
  userInfo: state => state.user.userInfo,
  lang: state => state.app.lang,
  app: state => state.app,
  logo: state => {
    if (state.app.logo) {
      return state.app.logo
    }
    return require('@/assets/img/logo.png')
  },
  name: state => {
    if (state.app.name) {
      return state.app.name
    }
    return 'cepoCRM'
  },
  collapse: state => state.app.sidebar.collapse,
  userList: state => state.user.userList,
  deptList: state => state.user.deptList,
  hrmUserList: state => state.hrm.hrmUserList,
  hrmDeptList: state => state.hrm.hrmDeptList,

  // 权限
  allAuth: state => state.user.allAuth,
  manage: state => state.user.manage,

  // 路由
  addRouters: state => state.permission.addRouters,
  manageRouters: state => state.permission.manageRouters,

  // 配置信息
  prohibit: state => state.app.prohibit
}

export default getters
