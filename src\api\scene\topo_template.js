import request from '@/utils/request'
/**
 * 获取拓扑模板列表
 *
 */
export function getTemplatesList(data) {
  return request({
    url: '/nfvo/api/topology/templates',
    method: 'get',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}
/**
 * 修改拓扑模板
 *
 */
export function putTemplates(id, data) {
  return request({
    url: '/nfvo/api/topology/templates/' + id,
    method: 'put',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}
/**
 * 删除拓扑模板
 *
 */
export function deleteTemplates(id) {
  return request({
    url: '/nfvo/api/topology/templates/' + id,
    method: 'delete',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
