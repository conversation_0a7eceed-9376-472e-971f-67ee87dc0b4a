<template>
  <detail-view
    :loading="loading"
    :data="data"
    :id="id"
    :view-item="viewItem"
    mode="drawer"
    title-key="examName"
  />
</template>
<script>
import moduleConf from '../config'
import detailView from '@/packages/detail-view/index'
import detailOverview from './detail-overview'
import detailQuestionBank from './detail-questionBank'
import detailTrends from '@/components/QuestionBank/dynamicPaper/index.vue'
import detailQuestionBankCtf from './detail-questionBankCtf'

import { getExam, getQueryQuestionPageByExamId } from '@/api/accumulate/exam'
export default {
  components: {
    detailView,
    detailOverview,
    detailQuestionBank,
    detailQuestionBankCtf
  },
  data() {
    return {
      moduleName: moduleConf.name,
      id: null, // 资源ID
      data: null, // 资源数据对象
      questionList: [], // 试卷下题目列表
      loading: true,
      viewItem: [
        {
          transName: '概况',
          name: 'overview',
          component: detailOverview
        },
        {
          transName: '题目信息',
          name: 'questionBank',
          component: detailQuestionBank
        }
      ]
    }
  },
  watch: {
    '$route': function(to, from) {
      const toId = to.params.hasOwnProperty('id') ? to.params.id : null
      const fromId = from.params.hasOwnProperty('id') ? from.params.id : null
      if (toId !== fromId) {
        this.loadBase()
      }
    }
  },
  created() {
    this.loadBase()
  },
  methods: {
    'loadBase': function() {
      this.id = this.$route.params.id
      this.loading = true
      const result = [this.getData(this.id), this.getQuestions(this.id)]
      Promise.all(result).then(() => {
        this.data['questionList'] = this.questionList
        this.loading = false
      }).finally(() => {
        this.loading = false
      })
    },
    // 根据id获取详情数据
    'getData': function(id) {
      return new Promise((resolve, reject) => {
        getExam({ id: id }).then(res => {
          this.data = res['data']
          if (this.data.examType == 1) { // 动态试卷
            this.viewItem[1].component = detailTrends
          } else if (this.data.sceneTypeId == 5 && this.data.generatingTestPaper == 2) {
            this.viewItem[1].component = detailQuestionBankCtf
          }
          resolve()
        }).catch(() => {
          this.data = null
          reject()
        })
      })
    },
    // 根据id获取详情数据
    'getQuestions': function(id) {
      return new Promise((resolve, reject) => {
        getQueryQuestionPageByExamId({ pageType: 0, examId: id }).then(res => {
          this.questionList = res.data.records.sort((a, b) => {
            return a.questionType - b.questionType
          })
          res.data.records.forEach(item => {
            if (item.questionType == 10) {
              item.combinationPoints = item.combinationPoints === null ? '[]' : item.combinationPoints
              const combinationPoints = JSON.parse(item.combinationPoints)
              item.combinationQuestionBOS.forEach((sub, index) => {
                sub.content = JSON.parse(sub.content).map((val, subIndex) => {
                  return {
                    contentName: val,
                    questionScore: combinationPoints[index] && combinationPoints[index][subIndex]
                  }
                })
              })
            }
          })
          resolve()
        }).catch(() => {
          this.questionList = []
          reject()
        })
      })
    }
  }
}
</script>
