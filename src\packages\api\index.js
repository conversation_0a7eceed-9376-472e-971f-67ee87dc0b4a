import request from '@/utils/request'

// 根据URL地址获取文件流
export function downloadFiles(param) {
  return request({
    url: 'admin/adminSysFile/downloadFiles',
    method: 'get',
    params: param,
    responseType: 'blob',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 获取系统配置
export function queryFirstConfigByName(name, data) {
  return request({
    url: `adminConfig/queryFirstConfigByName?name=${name}`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
