<template>
  <div class="buttons-wrap">
    <!-- 弹窗 -->
    <el-dialog
      :title="titleMapping[modalName]"
      :visible.sync="modalShow"
      :width="modalWidth"
      @close="modalClose"
    >
      <transition name="el-fade-in-linear">
        <component
          :is="modalName"
          :name="modalName"
          :data="selectItem"
          :project-data="projectData"
          @close="modalClose"
          @call="confirmCall"
        />
      </transition>
    </el-dialog>
  </div>
</template>

<script>
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
export default {
  name: 'ActionMenu',
  components: {
  },
  mixins: [mixinsActionMenu],
  props: {
    data: {
      type: Object,
      default: () => ({})
    },
    moduleName: {
      type: String,
      default: ''
    },
    selectItem: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      drawerAction: [], // 需要侧拉打开的操作
      // 弹窗title映射
      titleMapping: {
      },
      projectData: {}
    }
  },
  created() {
    this.projectData = this.data
  },
  methods: {
    confirmCall: function(type, data) {
      if (type === 'close') {
        this.modalClose()
      } else if (type === 'refresh') {
        this.$emit('call', type)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.action-menu {
  display: inline-block;
}
</style>
