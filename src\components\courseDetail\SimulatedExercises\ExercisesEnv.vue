<template>
  <div v-loading="loading" class="content-wrap-layout">
    <div class="cr-header">
      <div class="cr-left">
        <img :src="config.configUrl" class="cr-image" alt="">
      </div>
      <div class="cr-right">
        <div style="margin-right: 10px;">
          <el-tag
            v-for="(node, index) in openedConsoleShow"
            :key="index"
            :type="currentNode && currentNode.id == node.id ? '' : 'info'"
            effect="dark"
            closable
            @click="clickDrop(null, node)"
            @close="closeConsole(node.id)"
          ><i v-if="currentNode && currentNode.id == node.id" class="cr-icon-diannao" />{{ node.name }}</el-tag>
        </div>
        <el-dropdown v-if="openedConsole.length" placement="bottom-start" trigger="click" class="console-fold" @command="clickDrop(null, $event)">
          <div>展开<i class="el-icon-d-arrow-left rotate-90deg" /></div>
          <el-dropdown-menu slot="dropdown">
            <div v-for="(node, index) in openedConsole" :key="index">
              <el-dropdown-item :style="{'background': currentNode && currentNode.id == node.id ? 'var(--color-50)' : '' }" :command="node">
                <i v-if="currentNode && currentNode.id == node.id" class="cr-icon-diannao" style="font-size: 14px; font-weight: bold;" />{{ node.name }}
              </el-dropdown-item>
            </div>
          </el-dropdown-menu>
        </el-dropdown>
        <el-divider v-if="openedConsole.length" direction="vertical" />
        <el-tag v-if="topologyNodesConsole.length" :type="topologyNodesConsole.length && showList ? '' : 'info'" effect="dark" class="right-item" @click="switchShowList">
          <i class="el-icon-menu" />控制台
        </el-tag>
        <el-divider v-if="topologyNodesConsole.length" direction="vertical" />
        <!-- 显示topo -->
        <el-tag v-if="topologyId && questionObj.topologyVisible == 1" :type="topologyId && showTopo ? '' : 'info'" effect="dark" class="right-item" @click="switchShowTopo">
          <i class="cr-icon-tuopumuban-copy" style="font-size: 14px;" />场景拓扑
        </el-tag>
      </div>
    </div>
    <div class="topo-content">
      <!-- 折叠 -->
      <div v-if="!fold" :style="{'left': `calc(${percent}% - 10px)`}" class="fold-wrap" @click="handleFold(true)">
        <i class="el-icon-caret-left" />
      </div>
      <!-- 展开 -->
      <div v-if="fold" :style="{'left': `calc(${percent}% - 10px)`}" class="fold-wrap" @click="handleFold(false)">
        <i class="el-icon-caret-right" />
      </div>
      <split-pane ref="split-pane" :min-percent="minPercent" :default-percent="percent" split="vertical" @resize="resize">
        <div slot="paneL" class="simulation-question-wrap">
          <div class="simulation-question">
            <div v-if="questionObj && questionObj.questionType != '10'" class="question_info">
              <div class="question_item_content">
                <div class="rich-text">
                  <div>
                    <span v-html="handleRichContent(questionObj.content).contentWithoutImgs"/>
                    {{ `【${questionObj.questionScore}分】` }}
                  </div>
                </div>
                <el-image
                  v-if="handleRichContent(questionObj.content).imgSrcArr.length > 0"
                  :src="handleRichContent(questionObj.content).imgSrcArr[0]"
                  :preview-src-list="handleRichContent(questionObj.content).imgSrcArr"
                  style="width: 485px; height: 300px"
                />
              </div>
            </div>
            <!-- 综合题 -->
            <div v-if="questionObj && questionObj.questionType == '10'" class="question_info">
              <div v-for="(com, idx) in questionObj.combinationQuestionBOS" :key="idx" class="comp-box">
                <div class="flex-space-between ai-start mb-10">
                  <div class="comp-question" style="margin-bottom: -10px;">
                    <span>综合题{{ idx + 1 }}.&nbsp;</span>
                    <span style="flex: 1;" v-html="com.questionName"/>
                    {{ `【${com.questionScore}分】` }}
                  </div>
                </div>
                <div v-for="(sub, subIndex) in com.content" :key="subIndex" class="comp-content-wrap">
                  <div>
                    <span>题目{{ subIndex + 1 }}.&nbsp;</span>
                    <span style="flex: 1;" v-html="sub.contentName"/>
                    {{ `【${sub.questionScore}分】` }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-loading="rightLoading" slot="paneR" class="paneR">
          <!-- vnc、ssh、rdp、serial -->
          <div v-if="openedConsole.length" :style="{ visibility: currentNode ? 'visible' : 'hidden'}" class="console-wrap">
            <div v-for="(node) in openedConsole" :key="node.id" :style="{'z-index': currentNode && currentNode.id === node.id ? '9' : '1'}" class="console-container">
              <!-- 在未启动的情况下点击控制台强制的打开这个空提示 -->
              <el-empty v-if="node.status != 'running'" :image="img" :image-size="180" description="工作站未启动" style="padding-top: 15%;" />
              <iframe
                v-else
                :id="node.id"
                :src="node.workstationUrl"
                :title="node.name"
                frameborder="0"
                allowfullscreen
                style="width: 100%;height:100%;"
                @mouseover="setFocus(node)"
              />
            </div>
          </div>
          <!-- 拓扑 -->
          <Topo
            v-if="topologyId && showTopo"
            :topo-id="topologyId"
            style="width: 100%; height: 100%;"
            topo-type="examinationPermissions"
            console-target="_self"
            @console-url="consoleUrl"
          >
            <template slot="rightIcon">
              <a href="javascript:;">
                <el-tooltip content="鼠标右键点击网元图标可查看网元信息、登录控制台（如开启）等" placement="bottom">
                  <i class="cr-icon-info" style="font-size: 19px;" />
                </el-tooltip>
              </a>
            </template>
          </Topo>
          <!-- 未启动拓扑 -->
          <div v-else-if="!topoRunning" class="topo-start-wrap">
            <div class="start-title">请等待管理员启动实操环境</div>
            <div class="start-button notext">
              <img :src="startBg" alt="">
            </div>
          </div>
          <!-- 如果有控制台节点，但是未选择进哪个工作站时 -->
          <div v-else-if="topologyNodesConsole.length && !currentNode" class="console-list">
            <div class="start-title">当前实操环境共 {{ topologyNodesConsole.length }} 个设备开启控制台登录</div>
            <div class="console-button">
              <div v-for="node in topologyNodesConsole" :key="node.id" class="console-item">
                <div v-overflow-tooltip class="ellipsis">{{ node.name }}</div>
                <img :src="startBg" alt="">
                <div class="console-view">
                  <el-popover placement="top" width="300" trigger="click" popper-class="view-wrap" @show="showView(node)">
                    <div v-loading="viewLoading" class="view-content">
                      <div>
                        <div>状态：</div>
                        <div>{{ nodeStatusInfo[node.status] }}</div>
                      </div>
                      <div>
                        <div>内网IP地址：</div>
                        <div><div v-for="(port, index) in node.ports" v-show="port.ip" :key="index">{{ port.ip }}</div></div>
                      </div>
                      <template v-if="node.console_info && node.console_info.length">
                        <div v-for="item in node.console_info" :key="item.console_type">
                          <div>{{ item.console_type === 'rdp' ? '远程桌面地址' : 'SSH地址' }}：</div>
                          <div>{{ item.ip }}:{{ item.port }}</div>
                        </div>
                      </template>
                    </div>
                    <el-button slot="reference" type="primary">查看</el-button>
                  </el-popover>
                  <el-dropdown placement="bottom" trigger="hover" @command="clickDrop($event, node)">
                    <div>远程访问<i class="el-icon-arrow-down el-icon--right" /></div>
                    <el-dropdown-menu slot="dropdown" class="console-list-dropdown">
                      <div v-for="(console, index) in node.console_type.split(',')" :key="index">
                        <el-dropdown-item :command="console">
                          <el-tooltip :content="consoleMap[console].tooltip" effect="dark" placement="top">
                            <i class="cr-icon-diannao" />
                          </el-tooltip>
                          {{ consoleMap[console].label }}
                        </el-dropdown-item>
                      </div>
                    </el-dropdown-menu>
                  </el-dropdown>
                </div>
              </div>
              <div class="perch" />
              <div class="perch" />
            </div>
          </div>
          <!-- 暂无数据 -->
          <el-empty v-else-if="description" :image="img" :image-size="180" :description="description" style="padding-top: 15%;" />
        </div>
      </split-pane>
    </div>
    <el-dialog
      :title="titleMapping[modalName]"
      :visible.sync="modalShow"
      :width="modalWidth"
      @close="modalClose"
    >
      <transition name="el-fade-in-linear">
        <component
          :is="modalName"
          :name="modalName"
          @close="modalClose"
          @call="confirmCall"
        />
      </transition>
    </el-dialog>
  </div>
</template>

<script>
import splitPane from 'vue-splitpane'
import Topo from '@/packages/topo/index'
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
import { queryBaseInfoConfig } from '@/packages/layout/api/config'
import { getItem, vnc, serial, getConsole } from '@/packages/topo/api/orchestration'
import { mapGetters } from 'vuex'
import { handleRichContent } from '@/utils/index.js'
import { getNodeItem } from '@/packages/topo/api/orchestration'
import { queryPracticeUser } from '@/api/teacher/index'
import { tabManager } from '@/packages/utils/tabManager.js'
export default {
  components: {
    splitPane,
    Topo
  },
  mixins: [mixinsActionMenu],
  data() {
    return {
      handleRichContent,
      loading: true,
      rightLoading: true,
      viewLoading: false,
      description: '', // 暂无数据文字
      img: require('@/packages/table-view/nodata.png'), // 暂无数据图片
      startBg: require('@/components/courseDetail/experimentTopo/startBg.jpg'),
      config: {}, // 头部logo

      fold: false, // 是否折叠
      minPercent: 0, // 题目区域的最小宽度
      percent: 30, // 题目区域的宽度

      showList: true,
      showTopo: false, // 是否展示拓扑
      titleMapping: {},

      examType: this.$route.query.examType || null,
      courseId: this.$route.query.courseId || '',
      contentId: this.$route.query.curriculumCode || '',
      schedulingCode: this.$route.query.schedulingId || '',
      questionId: this.$route.query.questionId, // 题目id
      questionObj: {}, // 题目数据

      topologyId: '', // 拓扑id
      topoRunning: true, // 拓扑是否启动
      topologyNodes: [], // 拓扑中的节点
      currentNode: null, // 当前选中的节点
      openedConsole: [], // 已经打开控制台的工作站
      nodeStatusInfo: {
        'pending': '添加成功',
        'starting': '启动中',
        'running': '运行中',
        'powering_on': '开机中',
        'shutoff': '关机',
        'powering_off': '关机中',
        'deleting': '删除中',
        'suspended': '挂起',
        'suspending': '挂起中',
        'paused': '暂停',
        'pausing': '暂停中',
        'rebooting': '重启中',
        'rebuilding': '重建中',
        'error': '错误',
        'resuming': '恢复中'
      },
      consoleMap: {
        'vnc': { label: 'VNC', tooltip: '远程访问图形化桌面，支持跨平台远程控制' },
        'rdp': { label: '远程桌面', tooltip: '远程访问图形化桌面，提供完整的桌面体验' },
        'ssh': { label: 'WebSSH', tooltip: '远程访问命令行界面' },
        'serial': { label: '串口控制台', tooltip: '通过串行接口连接设备' },
        'webshell': { label: '命令行', tooltip: '免登录远程访问命令行界面' }
      }
    }
  },
  computed: {
    ...mapGetters(['userInfo']),
    // 可以打开控制台的节点列表
    topologyNodesConsole() {
      return this.topologyNodes.filter(item => (item.virtual_type === 'qemu' || item.virtual_type === 'docker') && item.console_type)
    },
    openedConsoleShow() {
      return this.openedConsole.slice(0, 4)
    }
  },
  async mounted() {
    window.addEventListener('beforeunload', this.handleBeforeUnload)
    this.minPercent = (10 / this.$refs['split-pane'].$el.offsetWidth) * 100
    this.getDetail()
    this.getQuestionAllList()
  },
  beforeDestroy() {
    window.removeEventListener('beforeunload', this.handleBeforeUnload)
  },
  methods: {
    handleBeforeUnload(e) {
      e.preventDefault()
      e.returnValue = '关闭此页面后，控制台会话可能会丢失。'
      // 清除全局共享的浏览器tab页签
      tabManager.clearTabRef(`exercises-${this.questionId}-${this.topologyId}`)
      return e.returnValue
    },
    showView(node) {
      this.viewLoading = true
      getNodeItem(node.id)
        .then(res => {
          this.$set(node, 'console_info', res.data.data.console_info)
          this.viewLoading = false
        })
        .catch(() => {
          this.viewLoading = false
        })
    },
    switchShowList() {
      this.currentNode = null
      this.showList = true
      this.showTopo = false
    },
    // 展示/隐藏拓扑
    switchShowTopo() {
      this.currentNode = null
      this.showList = false
      this.showTopo = true
    },
    // 获取网站信息详情
    getDetail() {
      queryBaseInfoConfig().then((res) => {
        this.config = res.data.data[0]
      })
    },
    getQuestionAllList(flag) {
      this.loading = true
      const params = {
        examType: this.examType,
        contentId: this.contentId,
        courseCode: this.courseId,
        schedulingCode: this.schedulingCode
      }
      if (this.examType == '1') {
        params.schedulingBatchNum = this.schedulingBatchNum
      }
      queryPracticeUser(params).then(res => {
        if (res.code == 0) {
          this.questionObj = res.data.find(item => item.id == this.questionId) || {}
          this.topologyId = this.questionObj.topologyId
          // 综合题计算总分
          if (this.questionObj.questionType == '10') {
            const combinationPoints = JSON.parse(this.questionObj.combinationPoints)
            this.questionObj.combinationQuestionBOS && this.questionObj.combinationQuestionBOS.forEach((sub, index) => {
              sub.questionScore = combinationPoints[index].map(each => Number(each)).reduce((p, q) => p + q)
              sub.content = JSON.parse(sub.content).map((val, subIndex) => {
                return { contentName: val, questionScore: combinationPoints[index][subIndex] }
              })
            })
          }
          if (this.topologyId) {
            this.getTopoNodes()
          } else {
            this.currentNode = null
            this.loading = false
            this.rightLoading = false
            this.description = '暂无拓扑'
          }
        }
      }).finally(() => {
        this.loading = false
      })
    },
    // 获取拓扑中的节点
    getTopoNodes() {
      getItem(this.topologyId).then(res => {
        this.loading = false
        this.topologyNodes = res.data.data.nodes
        const topologyNodesConsole = this.topologyNodes.filter(item => (item.virtual_type === 'qemu' || item.virtual_type === 'docker') && item.console_type)
        this.topoRunning = topologyNodesConsole.some(item => item.status == 'running')
        if (!topologyNodesConsole.length) {
          this.rightLoading = false
          this.description = '暂未有控制台节点'
        } else {
          this.rightLoading = false
        }
      })
    },
    // 拓扑中节点右键控制台的回调
    consoleUrl(type, url, nodeId) {
      const node = this.topologyNodesConsole.find(item => item.id === nodeId)
      this.currentNode = node
      this.showList = false
      this.showTopo = false
      const hasNode = this.openedConsole.find(item => item.id === nodeId)
      // 如果node没有点击过，则插入数组
      if (!hasNode) {
        this.openedConsole.unshift(node)
      }
      // 如果点击的node的控制台类型跟之前不一样，或者没有打开过node，则去请求控制台地址
      if ((hasNode && hasNode.workstationType !== type) || !hasNode) {
        this.$set(node, 'workstationUrl', url)
        this.$set(node, 'workstationType', type)
        this.showTopo = false
      }
    },
    clickDrop(console_type, node) {
      this.currentNode = node
      this.showList = false
      this.showTopo = false
      // 如果header中点击已经存在node，则不重新获取控制台链接
      if (!console_type) {
        return
      }
      const hasNode = this.openedConsole.find(item => item.id === node.id)
      // 如果node没有点击过，则插入数组
      if (!hasNode) {
        this.openedConsole.unshift(node)
        if (node.status != 'running') {
          this.$message.warning(`操作机${node.name}未处于运行中状态`)
          return
        }
      }
      // 如果点击的node的控制台类型跟之前不一样，或者没有打开过node，则去请求控制台地址
      if ((hasNode && hasNode.workstationType !== console_type) || !hasNode) {
        if (console_type === 'vnc') {
          this.rightLoading = true
          this.getVnc(node.id)
        } else if (console_type === 'rdp' || console_type === 'ssh' || console_type === 'webshell') {
          this.rightLoading = true
          this.getConsole(node.id, console_type)
        } else if (console_type === 'serial') {
          this.rightLoading = true
          this.getSerial(node.id, node.name)
        }
      }
    },
    closeConsole(nodeId) {
      if (this.currentNode && this.currentNode.id === nodeId) {
        this.currentNode = null
        this.showList = true
        this.showTopo = false
      }
      const index = this.openedConsole.findIndex(item => item.id === nodeId)
      this.openedConsole.splice(index, 1)
    },
    // 获取vnc
    getVnc(id) {
      const current = this.openedConsole.find(item => item.id === id)
      this.$set(current, 'workstationType', 'vnc')
      vnc(id).then(res => {
        this.$set(current, 'workstationUrl', res.data.data)
        this.showTopo = false
        this.rightLoading = false
      }).catch(() => {
        this.$set(current, 'workstationUrl', '')
        this.description = '打开控制台失败'
        this.rightLoading = false
      })
    },
    // 获取rdp、ssh
    getConsole(id, console_type) {
      const current = this.openedConsole.find(item => item.id === id)
      this.$set(current, 'workstationType', console_type)
      getConsole(id, { console_type }).then(res => {
        this.$set(current, 'workstationUrl', (window.location.hostname == 'localhost' ? (window.ADMIN_CONFIG ? window.ADMIN_CONFIG.VIP_URL : window.WEB_CONFIG.VIP_URL) : '') + res.data.data)
        this.showTopo = false
        this.rightLoading = false
      }).catch(() => {
        this.$set(current, 'workstationUrl', '')
        this.description = '打开控制台失败'
        this.rightLoading = false
      })
    },
    // 获取serial
    getSerial(id, name) {
      const current = this.openedConsole.find(item => item.id === id)
      this.$set(current, 'workstationType', 'serial')
      serial(id).then(res => {
        this.$set(current, 'workstationUrl', res.data.data + '&title=' + name)
        this.showTopo = false
        this.rightLoading = false
      }).catch(() => {
        this.$set(current, 'workstationUrl', '')
        this.description = '打开控制台失败'
        this.rightLoading = false
      })
    },
    // iframe 获取焦点
    setFocus(node) {
      document.getElementById(node.id).contentWindow.focus()
    },
    // 折叠面板时
    handleFold(flag) {
      this.fold = flag
      if (flag) {
        this.percent = this.minPercent
      } else {
        this.percent = 30
      }
    },
    // 拖拽面板时
    resize(val) {
      this.percent = val
    },
    confirmCall: function(type, data) {
      if (type === 'close') {
        this.modalClose()
      }
    }
  }
}
</script>

<style lang="scss">
.el-dropdown-menu.el-popper {
  max-height: 400px;
  overflow-y: auto;
}
.el-dropdown-menu.el-popper[x-placement^="left"] {
  margin-right: 1px;
}
.el-dropdown-menu.console-list-dropdown {
  &.el-popper[x-placement^=bottom] {
    margin-top: 5px !important;
  }
  width: 150px;
  background: var(--color-400);
  border: 1px solid var(--color-400);
  .el-dropdown-menu__item {
    color: #fff !important;
    font-size: 14px !important;
    padding: 0 10px !important;
    font-weight: 500;
    i {
      margin-right: 2px;
      font-size: 14px;
      font-weight: 700;
    }
    &:focus, &:not(.is-disabled):hover {
      background-color: var(--color-600) !important;
      color: #fff !important;
    }
  }
}
.view-wrap {
  padding: 0;
  .view-content {
    padding: 5px 12px;
    >div {
      width: 100%;
      color: #333;
      font-weight: 500;
      margin: 10px 0;
      display: flex;
      >div:first-child {
        width: 105px;
      }
    }
  }
  .ip-wrap {
    display: flex;
  }
}
</style>
<style lang="scss" scoped>
.content-wrap-layout {
  width: 100%;
  height: 100%;
}
.cr-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 60px;
  border-bottom: 1px solid #e6e6e6;
  background-color: #fff;
  .cr-left {
    display: flex;
    align-items: center;
    color: var(--neutral-0);
    font-size: 14px;
    font-weight: 900;
    .cr-image {
      margin-left: 24px;
      margin-right: 8px;
      height: 42px;
      max-width: 300px;
    }
  }
  .cr-right {
    margin-right: 10px;
    flex: 1;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    color: var(--neutral-700);
    .console-fold {
      margin-right: 15px;
      color: var(--neutral-700);
      &:hover {
        color: var(--color-600);
      }
      cursor: pointer;
      i {
        font-weight: bold;
        margin-left: 3px;
      }
      .rotate90deg {
        transform: rotate(90deg);
      }
      .rotate-90deg {
        transform: rotate(-90deg);
      }
    }
    .right-item {
      font-size: 14px;
      padding: 0 10px !important;
      i {
        margin-right: 3px;
        font-size: 16px;
      }
    }
    ::v-deep .el-tag {
      cursor: pointer;
      font-weight: 700;
      display: inline-block;
      vertical-align: top;
      max-width: 180px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      position: relative;
      padding: 0 24px 0 30px;
      margin: 0 10px;
      &.el-tag--dark.el-tag--info {
        padding-left: 10px;
        background-color: transparent;
        border-color: var(--color-600);
        color: var(--color-600);
        .el-tag__close {
          color: var(--color-600);
          &:hover {
            background-color: var(--color-100);
          }
        }
      }
      .cr-icon-diannao {
        font-size: 16px;
        font-weight: bold;
        position: absolute;
        top: 6px;
        left: 8px;
      }
      .el-tag__close {
        position: absolute;
        top: 7px;
        right: 5px;
      }
    }
    .el-divider {
      height: 20px;
      width: 2px;
    }
  }
}
.topo-content {
  position: relative;
  flex: 1;
  min-height: 0;
  overflow: hidden;
  .simulation-question-wrap {
    height: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    position: relative;
    padding: 15px 0;
    border-right: 1px solid #dcdee2;
    .simulation-question {
      flex: 1;
      min-height: 0;
      display: flex;
      position: relative;
      width: 100%;
      padding: 0 10px 0 15px;
      .question_info {
        width: 100%;
        overflow: auto;
        border-radius: 4px;
        ::v-deep .question_item_content {
          overflow-x: auto;
          margin: 0 5px 5px 0;
          font-weight: 600;
          font-size: 14px;
          color: rgb(36, 41, 47);
          .rich-text {
            display: flex;
            justify-content: flex-start;
            span p {
              display: inline !important;
            }
          }
          .el-image {
            margin: 10px 0 0 16px;
          }
        }
        .comp-question {
          display: flex;
          max-height: 200px;
          overflow-y: auto;
          font-weight: 600;
          font-size: 14px;
          color: rgb(36, 41, 47);
          margin-bottom: 10px;
          >span {
            word-break: break-all;
          }
        }
        .comp-content-wrap {
          border: 1px solid #e5e6eb;
          margin: 15px 0 5px;
          padding: 10px 20px;
          border-radius: 4px;
          >div:first-child {
            display: flex;
            max-height: 200px;
            overflow-y: auto;
            margin: 5px 0;
            >span {
              word-break: break-all;
            }
          }
        }
      }
    }
  }
  .fold-wrap {
    position: absolute;
    top: calc(50% - 80px);
    width: 10px;
    height: 60px;
    line-height: 60px;;
    background-color: var(--color-600);
    overflow: hidden;
    cursor: pointer;
    z-index: 9;
    i {
      color: #fff;
      margin-left: -2px;
    }
  }
  .paneR {
    height: 100%;
    border-left: 1px solid #dcdee2;
    position: relative;
    .console-wrap {
      position: absolute;
      width: 100%;
      height: 100%;
      .console-container {
        position: absolute;
        width: 100%;
        height: 100%;
        background: #fff;
        overflow: hidden;
      }
    }
    .console-list {
      background: #f0f4f9;
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      position: absolute;
      overflow-y: auto;
      .start-title {
        color: #333;
        font-size: 24px;
        font-weight: bold;
        text-align: center;
        margin: 70px 0;
      }
      .console-button {
        border: 1px solid var(--color-600);
        border-radius: 8px;
        padding: 50px 20px 20px 20px;
        width: 90%;
        flex: 1;
        min-height: 0;
        overflow: auto;
        margin: 0 auto 50px auto;
        display: flex;
        justify-content: center;
        flex-wrap: wrap;
        column-gap: 30px;
        font-size: 15px;
        cursor: default;
        .console-item {
          width: 350px;
          height: fit-content;
          background: rgba(7, 128, 155, .1);
          display: flex;
          justify-content: space-between;
          align-items: center;
          flex-direction: column;
          border: 1px solid var(--color-600);
          border-radius: 8px;
          padding-bottom: 15px;
          margin-bottom: 30px;
          img {
            padding: 15px 15px 0 15px;
            width: 100%;
          }
          >div {
            width: 100%;
            color: #333;
            font-weight: 500;
            margin-top: 10px;
            padding: 0 15px;
            &:first-child {
              margin-top: 0px;
              font-size: 16px;
              border-bottom: 1px solid var(--color-600);
              padding: 10px 15px;
              font-weight: bold;
            }
          }
          .console-view {
            display: flex;
            justify-content: space-between;
            width: 100%;
            > span {
              width: 47%;
            }
            .el-button {
              width: 100%;
              height: 36px;
              font-weight: bold;
            }
            .el-dropdown {
              color: #fff;
              background: var(--color-600);
              width: 47%;
              height: 36px;
              line-height: 36px;
              border-radius: 4px;
              text-align: center;
              cursor: pointer;
            }
          }
        }
        .perch {
          width: 350px;
        }
      }
    }
    .topo-start-wrap {
      background: #383838;
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-evenly;
      position: absolute;
      .start-title {
        color: #fff;
        font-size: 24px;
        opacity: .8;
        text-align: center;
      }
      .start-button {
        width: 200px;
        border: 1px solid #2e7eee;
        border-radius: 8px;
        padding: 1px;
        display: flex;
        flex-direction: column;
        margin: 0 auto;
        cursor: pointer;
        &.console-button {
          width: 350px;
          cursor: default;
          padding: 12px 10px;
          background: rgba(52, 186, 255, .1);
          border-color: var(--color-700);
          &:hover {
            border-width: 1px;
          }
        }
        &:hover {
          border-width: 3px;
        }
        .el-button {
          margin: 2px 0;
          width: 100%;
          color: #2e7eee;
        }
        img {
          width: 100%;
          border-bottom-left-radius: 8px;
          border-bottom-right-radius: 8px;
        }
        &.notext img {
          border-top-left-radius: 8px;
          border-top-right-radius: 8px;
        }
        .console-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 4px 15px;
          background: rgba(7, 128, 155, .1);
          margin-bottom: 5px;
          >div {
            flex: 1;
            min-width: 0;
            color: #fff;
            font-weight: 500;
          }
          .el-button {
            width: auto;
            color: var(--color-600);
          }
        }
      }
    }
  }
  ::v-deep .vue-splitter-container {
    width: 100%;
    .splitter-pane.vertical.splitter-paneL {
      padding-right: 10px;
    }
    .splitter-pane.vertical.splitter-paneR {
      padding-left: 0px;
    }
    .splitter-pane.horizontal.splitter-paneL {
      padding-bottom: 10px;
    }
    .splitter-pane.horizontal.splitter-paneR {
      padding-top: 5px;
    }
    .splitter-pane-resizer.horizontal {
      height: 10px;
      margin: -10px 0 0 0;
      opacity: 1;
      z-index: 998;
      &::after {
        z-index: 999;
        transform: rotate(90deg);
        content: '|||';
        font-size: 18px;
        letter-spacing: -1.5px;
        color: var(--color-600);
        position: absolute;
        top: 0%;
        bottom: 0%;
        margin-top: -30px;
        left: 50%;
        right: 50%;
        height: 60px;
        line-height: 60px;
        width: 10px;
        background: rgba(2, 91, 212, 0.2);
        pointer-events: none;
      }
    }
    .splitter-pane-resizer.vertical {
      width: 10px;
      margin-left: -10px;
      opacity: 1;
      z-index: 998;
      &::after {
        z-index: 999;
        content: '|||';
        font-size: 18px;
        letter-spacing: -1.5px;
        color: var(--color-600);
        position: absolute;
        top: 50%;
        bottom: 50%;
        margin-top: -20px;
        left: -5px;
        right: 0;
        height: 60px;
        line-height: 60px;
        width: 10px;
        background: rgba(2, 91, 212, .2);
        pointer-events: none;
      }
    }
  }
}
.topo-masking {
  position: absolute;
  height: 100%;
  width: 100%;
  background: linear-gradient(rgba(0, 0, 0, 0.15), rgba(0, 0, 0, 0.15)), rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 999;
  .topo-masking-close {
    position: absolute;
    top: 15px;
    right: 15px;
    font-size: 20px;
    cursor: pointer;
  }
  ::v-deep .el-progress {
    width: 50%;
    margin-top: -90px;
  }
  &.percentage-0 {
    ::v-deep .el-progress-bar__innerText {
      color: rgba(0, 0, 0, 0.85);
    }
  }
}
::v-deep .orchestration-create-warp {
  position: absolute;
  right: 0;
  top: 0;
  z-index: 99;
}
::v-deep .serial-console {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  .terminal {
    flex: 1;
    min-height: 0;
  }
  .status {
    height: 30px;
    line-height: 20px;
    padding-left: 15px;
  }
}
::v-deep .el-empty__description {
  margin-top: 0;
  color: var(--neutral-600);
}
</style>
