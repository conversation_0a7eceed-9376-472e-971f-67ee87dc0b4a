<template>
  <div v-loading="loading" class="dialog-wrap" >
    <el-form ref="form" :model="formData" :rules="rules" label-width="80px" @submit.native.prevent>
      <el-form-item label="Pod 名称" prop="pod_name">
        {{ formData.pod_name }}
      </el-form-item>
      <el-form-item label="容器名称" prop="container_name">
        <el-select v-model="formData.container_name">
          <el-option
            v-for="(item) in podContainers"
            :key="item"
            :label="item"
            :value="item"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>

<script>
import module from '../config.js'
import { webshell, getNodeItem } from '../api/orchestration'
import modalMixins from '../../mixins/modal_form'
import validate from '../../validate'
export default {
  mixins: [modalMixins],
  props: {
    // 传入数据
    data: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      moduleName: module.name,
      loading: true,
      podContainers: [],
      formData: {
        'vnf_uuid': '',
        'pod_name': '',
        'container_name': ''
      },
      rules: {
        'container_name': [
          validate.required('change')
        ]
      }
    }
  },
  created() {
    this.getNodeItem()
  },
  methods: {
    'getNodeItem': function() {
      getNodeItem(this.data.node_id)
        .then(res => {
          this.formData['pod_name'] = res.data.data.name
          this.formData['vnf_uuid'] = res.data.data.vnf_uuid
          this.podContainers = res.data.data.pod_containers || []
          this.loading = false
        })
        .catch((error) => {
          this.loading = false
          console.log(error)
        })
    },
    // modal点击确定
    'confirm': function() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.loading = true
          const postData = {
            'pod_name': this.formData['vnf_uuid'],
            'container_name': this.formData['container_name']
          }
          webshell(this.data.node_id, postData)
            .then(res => {
              const url = res.data.data
              const a = document.createElement('a')
              a.setAttribute('href', encodeURI(url))
              a.setAttribute('target', '_blank')
              a.setAttribute('id', 'camnpr')
              document.body.appendChild(a)
              a.click()
            })
            .catch((error) => {
              this.$message.error(error)
            })
          this.close()
        }
      })
    },
    close() {
      this.$emit('close')
    }
  }
}
</script>
