<!-- 弹出层组件 -->
<template>
  <transition name="dialog">
    <div
      v-if="hy_show"
      :class="{ active: hy_show }"
      class="hy-dialog-box"
      @click="boxHide($event)"
    >
      <div
        :class="{ active: hy_show }"
        :style="{
          width: hy_width,
          height: hy_height ? hy_height : 'auto',
          ...extendStyle,
        }"
        class="hy-dialog"
      >
        <div
          :style="{ height: titleHeight || '48px' }"
          class="hy-dialog-header-box"
        >
          <slot name="title">
            <div class="hy-dialog-title hy-title-color font18">
              {{ hy_title }}
              <div class="hy-dialig-close-btn font18" @click="closeFn">
                <i class="el-icon-close" />
              </div>
            </div>
          </slot>
        </div>
        <div class="hy-dialog-content-box flex-1">
          <slot name="content">请设置内容区域</slot>
        </div>
        <div v-if="hy_footer_show" class="hy-dialog-footer-box">
          <slot name="footer">
            <div class="flex-right px-20">
              <el-button
                class="footer-btn cancel mr-10"
                type="primary"
                @click.native="closeFn"
              >取消</el-button
              >
              <el-button
                :loading="loading"
                class="footer-btn"
                type="primary"
                @click.native="confirmFn"
              >确定</el-button
              >
            </div>
          </slot>
        </div>
      </div>
    </div>
  </transition>
</template>
<script>
export default {
  name: 'Dialog',

  props: {
    visible: {
      type: Boolean
    },
    title: {
      type: String
    },
    width: {
      type: String
    },
    height: {
      type: String
    },
    titleHeight: {
      type: String
    },
    footerShow: {
      type: Boolean,
      default: true
    },
    modal: {
      type: Boolean
    },
    loading: {
      type: Boolean,
      default: false
    },
    extendStyle: {
      type: Object
    }
  },
  data() {
    return {
      hy_width: '500px',
      hy_height: '',
      hy_show: false,
      hy_title: '标题',
      hy_modal: true,
      hy_footer_show: true,
      hy_extendStyle: {}
    }
  },
  watch: {
    visible: {
      handler: function(v) {
        console.log(8888)
        this.hy_show = v
      },
      deep: true
    },
    title: {
      handler: function(v) {
        this.hy_title = v
      },
      deep: true
    }
  },
  created() {
    // 获取组件额外配置
    this.width && (this.hy_width = this.width)
    this.height && (this.hy_height = this.height)
    this.title && (this.hy_title = this.title)
    this.extendStyle && (this.hy_extendStyle = this.extendStyle)
    // 布尔值赋值
    this.modal !== undefined && (this.hy_modal = this.modal)
    this.footerShow !== undefined && (this.hy_footer_show = this.footerShow)
    this.visible !== undefined && (this.hy_show = this.visible)
  },
  mounted() {},
  updated() {
    // 处理图片过大溢出可视区问题
    const dom = document.getElementsByClassName('hy-dialog-content-box')
    if (dom && dom.length) {
      const DomList = dom[0].querySelectorAll('img')
      for (const i in DomList) {
        if (DomList[i].style) {
          DomList[i].style.width = '100%'
        }
      }
    }
  },
  methods: {
    closeFn() {
      this.$listeners.close && this.$listeners.close()
      this.$emit('update:visible', false)
    },
    confirmFn() {
      this.$emit('confirm')
    },
    boxHide(evt) {
      evt.target.className.includes('hy-dialog-box') &&
        this.hy_modal &&
        this.$emit('update:visible', false)
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-enter-active,
.dialog-leave-active {
  transition: all 0.3s;
}
.dialog-enter,
.dialog-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}
.footer-btn {
  min-width: 60px;
  height: 32px;
}
.footer-btn.cancel {
  background-color: #f2f3f5;
  border-color: #f2f3f5;
  color: #4e5969;
}
.footer-btn.cancel:hover {
  background-color: #e5e6eb;
  border-color: #e5e6eb;
}
.footer-btn.cancel:active {
  background-color: #c9cdd4;
  border-color: #c9cdd4;
}
.hy-dialog-box {
  position: fixed;
  display: none;
  top: 0;
  left: 0;
  height: 100% !important;
  width: 100% !important;
  background-color: rgba($color: #1a1b2b, $alpha: 0);
}

.hy-dialog-box.active {
  position: fixed;
  display: inherit;
  z-index: 999;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  background-color: rgba($color: #1d212999, $alpha: 0.6);
}

.hy-dialog {
  border-radius: 6px;
  box-shadow: 0px 0px 10px rgba(#000, 0.1);
  position: absolute;
  top: 50%;
  left: 50%;
  opacity: 1;
  z-index: 1;
  transform: translate(-50%, -50%);
  background-color: #fff;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.hy-dialog.active {
  opacity: 1;
}

.hy-dialog-header-box {
  // height: 48px;
  border-bottom: 1px solid #e6eaed;

  display: flex;
  justify-content: center;
  align-items: center;

  .hy-dialog-title {
    text-align: center;
    font-size: 16px;
    width: 100%;
    font-weight: bold;
    position: relative;

    .hy-dialig-close-btn {
      position: absolute;
      right: 20px;
      top: 0px;
      cursor: pointer;
      font-weight: lighter;
    }
  }
  .hy-dialig-close-btn:hover {
    opacity: 0.5;
  }
}

.hy-dialog-content-box {
  overflow: auto;
}

.hy-dialog-footer-box {
  padding: 0 16px;
  border-top: 1px solid #e6eaed;
  height: 64px !important;
  width: 100%;
  > div {
    height: inherit;
  }
}

.flex-right {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
</style>
