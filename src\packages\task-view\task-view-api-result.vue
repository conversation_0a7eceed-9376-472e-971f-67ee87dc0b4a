<template>
  <div class="api-result-view-wrap">
    <h3 class="view-title">操作：{{ taskName }}</h3>
    <template v-if="showBatList">
      <div v-for="(value, key) in data.response.result" :key="key" class="view-item">
        <div class="view-item-col status-icon">
          <i v-if="value.message === '成功'" class="el-icon-success" style="color: #67C23A;" />
          <i v-else class="el-icon-error" style="color: #F56C6C;" />
        </div>
        <div class="view-item-col name">{{ value.name }}</div>
        <div class="view-item-col message">{{ value.message }}</div>
      </div>
    </template>
    <p v-else>暂无数据</p>
  </div>
</template>
<style lang="less">
.api-result-view-wrap {
  padding: 20px 30px;
  .view-title {
    display: block;
    margin-bottom: 15px;
  }
  .view-item {
    display: flex;
    flex-direction: row;
    line-height: 20px;
    padding: 10px 0;
    border-bottom: 1px solid #ebeef5;
    .status-icon {
      width: 30px;
    }
    .name {
      width: 180px;
    }
    .message {
      flex: 1;
    }
  }
  .view-item-col {
    display: inline-block;
    padding: 0 20px 0 0;
    word-break: break-all;
  }
}
</style>
<script>
export default {
  props: {
    data: Object
  },
  computed: {
    // 任务名称
    'taskName': function() {
      return this.data.response.title
    },
    // 是否展示批量结果
    'showBatList': function() {
      return Object.keys(this.data.response.result).length > 0
    }
  }
}
</script>
