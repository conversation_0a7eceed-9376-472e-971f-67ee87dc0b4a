<template>
  <div :style="{ 'width': !collapse ? '240px' : '64px' }" class="cr-aside">
    <div class="cr-aside-content">
      <el-scrollbar>
        <div class="cr-aside-logo">
          <img :src="iconImg(items.meta.icon)" alt="">
          <span v-if="!collapse">{{ items.meta.title }}</span>
        </div>
        <el-menu
          :default-active="getCurrentActiveMenu"
          :default-openeds="defaultOpeneds"
          :collapse="collapse"
          class="cr-aside-menu"
          background-color="var(--neutral-0)"
          text-color="var(--neutral-700)"
          active-text-color="var(--color-600)"
          mode="vertical"
          @select="handleSelect"
          @open="handleOpen"
          @close="handleClose">
          <template v-for="item in items.children">
            <!-- 包含children的菜单项 -->
            <el-submenu v-if="!item.hidden && item.children && item.children.length > 0" :key="'sub_' + item.path" :index="item.path">
              <template slot="title">
                <i :class="item.meta.icon" />
                <span v-if="!collapse">{{ item.meta.title }}</span>
              </template>
              <el-menu-item
                v-for="child in item.children"
                v-if="!child.hidden"
                :key="child.path"
                :disabled="prohibit"
                :index="item.path + '/' + child.path"
                background-color="var(--neutral-0)"
                text-color="var(--neutral-700)"
                active-text-color="var(--color-600)">
                <i :class="child.meta.icon" />
                <span>{{ child.meta.title }}</span>
              </el-menu-item>
            </el-submenu>
            <!-- 普通菜单项 -->
            <el-menu-item
              v-else-if="!item.hidden"
              :key="'item_' + item.path"
              :disabled="prohibit"
              :index="item.path">
              <i :class="item.meta.icon" />
              <span v-if="!collapse">{{ item.meta.title }}</span>
            </el-menu-item>
          </template>
        </el-menu>
      </el-scrollbar>
    </div>
    <div class="cr-aside-footer">
      <i v-if="!collapse" class="el-icon-s-fold" @click="toggleSideBarClick" />
      <i v-if="collapse" class="el-icon-s-unfold" @click="toggleSideBarClick" />
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
export default {
  props: {
    activeMenu: {
      type: String,
      default: () => {
        return ''
      }
    },
    items: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {}
  },
  computed: {
    ...mapGetters(['collapse', 'prohibit']),
    getCurrentActiveMenu() {
      // 如果外部传入了activeMenu，优先使用
      if (this.activeMenu) {
        return this.activeMenu
      }

      // 获取当前路由路径
      const currentRoutePath = this.$route.path

      // 检查是否是子菜单路由
      if (this.items && this.items.children) {
        // 先检查是否是二级菜单的路由
        for (const item of this.items.children) {
          if (item.children && item.children.length > 0) {
            // 检查是否匹配子菜单路径
            for (const child of item.children) {
              const childFullPath = this.items.path + '/' + item.path + '/' + child.path
              if (currentRoutePath === childFullPath || currentRoutePath.startsWith(childFullPath + '/')) {
                return item.path + '/' + child.path
              }
            }

            // 检查是否是父菜单的路由
            const parentFullPath = this.items.path + '/' + item.path
            if (currentRoutePath === parentFullPath || currentRoutePath.startsWith(parentFullPath + '/')) {
              // 如果是redirect类型的路由，尝试找到默认子路由
              if (item.redirect) {
                const redirectPath = item.redirect.replace(this.items.path + '/', '')
                const parts = redirectPath.split('/')
                if (parts.length >= 2) {
                  return parts[0] + '/' + parts[1]
                }
                return item.path
              }

              // 如果没有重定向但有子菜单，默认选中第一个可见的子菜单
              if (item.children && item.children.length > 0) {
                const firstVisibleChild = item.children.find(child => !child.hidden)
                if (firstVisibleChild) {
                  return item.path + '/' + firstVisibleChild.path
                }
              }

              return item.path
            }
          }
        }

        // 如果不是子菜单，检查是否是一级菜单的路由
        for (const item of this.items.children) {
          if (!item.hidden) {
            const fullPath = this.items.path + '/' + item.path
            if (currentRoutePath === fullPath || currentRoutePath.startsWith(fullPath + '/')) {
              return item.path
            }
          }
        }
      }

      // 如果没有匹配到，返回空字符串
      return ''
    },
    defaultOpeneds() {
      // 找到当前路由所在的父菜单
      const currentRoutePath = this.$route.path
      const parentMenus = []

      // 遍历所有一级菜单项
      if (this.items && this.items.children) {
        this.items.children.forEach(item => {
          // 如果一级菜单有子菜单
          if (item.children && item.children.length) {
            // 检查当前路由是否是其子菜单
            const fullPath = this.items.path + (this.items.path[this.items.path.length - 1] === '/' ? '' : '/') + item.path
            if (currentRoutePath === fullPath || currentRoutePath.startsWith(fullPath + '/')) {
              parentMenus.push(item.path)
            }
          }
        })
      }

      return parentMenus
    }
  },
  methods: {
    iconImg(icon) {
      const themeKey = window.ADMIN_CONFIG ? (window.ADMIN_CONFIG.THEME || 'green') : (window.WEB_CONFIG.THEME || 'green')
      return require('../../assets/' + themeKey + '/' + icon + '.png')
    },
    toggleSideBarClick() {
      this.$store.commit('SET_COLLAPSE', !this.collapse)
    },
    handleSelect(key, keyPath) {
      if (this.prohibit) {
        return
      }

      // 检查是否是父菜单
      const isParentMenu = keyPath.length === 1

      if (isParentMenu) {
        // 查找当前选中的菜单项
        const currentItem = this.items.children.find(item => item.path === key)

        // 如果是有子菜单的父菜单，处理导航逻辑
        if (currentItem && currentItem.children && currentItem.children.length > 0) {
          // 如果有重定向，则导航到重定向路径
          if (currentItem.redirect) {
            this.$router.push({ path: currentItem.redirect }).catch(err => {
              if (err.name !== 'NavigationDuplicated') {
                throw err
              }
            })
            return
          }

          // 如果没有重定向但有子菜单，默认导航到第一个可见的子菜单
          const firstVisibleChild = currentItem.children.find(child => !child.hidden)
          if (firstVisibleChild) {
            const childPath = this.items.path + '/' + key + '/' + firstVisibleChild.path
            this.$router.push({ path: childPath }).catch(err => {
              if (err.name !== 'NavigationDuplicated') {
                throw err
              }
            })
          }
          return
        }

        // 如果是没有子菜单的菜单项，则导航到对应路由
        try {
          const fullPath = this.items.path + '/' + key
          this.$router.push({ path: fullPath }).catch(err => {
            if (err.name !== 'NavigationDuplicated') {
              throw err
            }
          })
        } catch (error) {
          console.log(error)
        }
      } else {
        // 子菜单项的处理
        try {
          // 获取父菜单和子菜单
          const parentKey = keyPath[0]
          const parentItem = this.items.children.find(item => item.path === parentKey)

          if (parentItem && parentItem.children) {
            // 找到对应的子菜单项
            const childPath = key.replace(parentKey + '/', '')
            const childItem = parentItem.children.find(item => item.path === childPath)

            if (childItem) {
              // 优先使用路由名称导航，如果有的话
              if (childItem.name) {
                this.$router.push({ name: childItem.name }).catch(err => {
                  if (err.name !== 'NavigationDuplicated') {
                    throw err
                  }
                })
                return
              }

              // 否则使用完整路径导航
              const fullPath = this.items.path + '/' + key
              this.$router.push({ path: fullPath }).catch(err => {
                if (err.name !== 'NavigationDuplicated') {
                  throw err
                }
              })
            }
          }
        } catch (error) {
          console.log(error)
        }
      }
    },
    handleOpen(key, keyPath) {
    },
    handleClose(key, keyPath) {
    }
  }
}
</script>

<style lang="scss" scoped>
.cr-aside {
  height: 100%;
  overflow: hidden;
  background-color: var(--neutral-0);
  border-right: 1px solid var(--neutral-300);
  padding: 0;
  display: flex;
  flex-direction: column;
  .cr-aside-content {
    flex: 1;
    min-height: 0;
    /deep/ .el-scrollbar {
      height: 100%;
      .el-scrollbar__wrap {
        overflow-x: hidden;
        .el-scrollbar__view {
          padding: 0 12px;
        }
      }
    }
    .cr-aside-logo {
      display: flex;
      align-items: center;
      margin: 24px 12px 0 12px;
      padding-bottom: 22px;
      font-size: 16px;
      color: var(--neutral-800);
      font-weight: 500;
      img {
        width: 32px;
        margin-right: 12px;
        vertical-align: middle;
      }
    }
    .cr-aside-menu {
      border: none;
      width: auto;
      /deep/ .el-menu-item {
        padding: 0 0 0 12px !important;
        margin-bottom: 4px;
        height: 32px;
        line-height: 32px;
        border-radius: 2px;
        font-weight: 500;
        font-size: 14px;
        i {
          margin-right: 10px;
          width: 16px;
          font-size: 16px;
        }
        &.is-active {
          background-color: var(--color-50) !important;
          color: var(--color-600) !important;
          font-weight: 900;
        }
        &:hover{
          background-color: var(--neutral-100) !important;
          opacity: 0.8;
        }
      }
      /deep/ .el-submenu {
        .el-submenu__title {
          padding: 0 0 0 12px !important;
          margin-bottom: 4px;
          height: 32px;
          line-height: 32px;
          border-radius: 2px;
          font-weight: 500;
          font-size: 14px;
          i {
            margin-right: 10px;
            width: 16px;
            font-size: 16px;
          }
          &:hover {
            background-color: var(--neutral-100) !important;
            opacity: 0.8;
          }
        }
        .el-menu-item {
          min-width: auto;
          padding-left: 32px !important;
          &.is-active {
            background-color: var(--color-50) !important;
            color: var(--color-600) !important;
            font-weight: 900;
          }
        }
      }
    }
  }
  .cr-aside-footer {
    width: 100%;
    border-top: 1px solid var(--neutral-300);
    padding: 12px 0;
    text-align: center;
    cursor: pointer;
    height: 40px;
  }
}
</style>
