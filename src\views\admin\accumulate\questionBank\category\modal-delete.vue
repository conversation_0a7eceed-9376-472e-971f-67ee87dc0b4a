<template>
  <div v-loading="loading" class="dialog-wrap">
    <el-alert :closable="false" type="warning">
      <div slot="title">此操作将永久删除该分类, 是否继续?</div>
    </el-alert>
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>
<script>
import module from './config.js'
import { deleteCategory } from '@/api/accumulate/category'

export default {
  props: {
    data: {
      type: Array,
      default: () => {
        return []
      }
    },
    categoryType: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      moduleName: module.name,
      loading: false
    }
  },
  methods: {
    close: function() {
      this.$emit('close')
    },
    confirm: function() {
      this.loading = true
      deleteCategory([this.data[0].id]).then(res => {
        this.$message.success('删除分类成功')
      }).finally(() => {
        // 都完成之后（无论成功失败），才去刷新列表
        this.$bus.$emit(this.moduleName + '_module', 'reload')
      })
      // 点击确定，立刻关闭弹窗
      this.close()
    }
  }
}
</script>
