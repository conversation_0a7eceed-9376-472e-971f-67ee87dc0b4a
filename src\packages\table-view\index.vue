<template>
  <div class="layout-table-wrap">
    <div v-loading="loading" v-show="loading" class="table-loading-wrap" />
    <div v-show="!data.length" class="table-empty-wrap">
      <img src="./nodata.png" alt="">
      <slot name="empty">暂无数据</slot>
    </div>
    <el-table
      ref="dataTable"
      :key="datekey"
      :data="data"
      :height="height"
      :overflow-tooltip-delay="600"
      :empty-text="tableEmptyText"
      :row-class-name="currentRowClass"
      :tree-props="treeProps"
      :default-sort="defaultSort"
      row-key="id"
      border
      size="small"
      @selection-change="onSelect"
      @sort-change="onSortChange"
    >
      <template v-if="type === 'selection'">
        <!--选择框-->
        <el-table-column v-if="single" :resizable="false" width="44" fixed="left" class-name="radio">
          <template slot-scope="scope">
            <a :class="{'not-allowed': scope.row['custom-selected']}" href="javascript:;" style="display: flex;" @click.stop="handleCurrentChange(scope.row)">
              <svg v-if="currentRow && (scope.row[currentKey] === currentRow[currentKey])" t="1705230997423" viewBox="0 0 1040 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4340" width="24" height="24"><path d="M509.92 176C325.504 176 176 325.504 176 509.92c0 184.416 149.504 333.92 333.92 333.92 184.416 0 333.92-149.504 333.92-333.92C843.84 325.504 694.32 176 509.92 176z m0 48c157.904 0 285.92 128 285.92 285.92 0 157.904-128.016 285.92-285.92 285.92C352 795.84 224 667.808 224 509.92 224 352 352 224 509.92 224z m0 96C405.024 320 320 405.024 320 509.92c0 104.88 85.024 189.92 189.92 189.92 104.88 0 189.92-85.04 189.92-189.92 0-104.896-85.04-189.92-189.92-189.92z" fill="var(--color-600)" p-id="4341" /></svg>
              <svg v-else t="1705231084064" viewBox="0 0 1040 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4484" width="24" height="24"><path d="M509.92 843.84C325.504 843.84 176 694.32 176 509.92 176 325.504 325.504 176 509.92 176c184.416 0 333.92 149.504 333.92 333.92 0 184.416-149.504 333.92-333.92 333.92z m0-48c157.904 0 285.92-128.016 285.92-285.92C795.84 352 667.808 224 509.92 224 352 224 224 352 224 509.92c0 157.904 128 285.92 285.92 285.92z" fill="#ccc" p-id="4485" /></svg>
            </a >
          </template>
        </el-table-column>
        <el-table-column v-else type="selection" width="44" fixed="left" />
      </template>
      <slot />
    </el-table>
    <!--页码 开始-->
    <div class="data-table-footer">
      <el-pagination
        v-if="multiplePage"
        :current-page.sync="internalCurrentPage"
        :page-sizes="[10, 20, 30, 40, 50, 100, 200]"
        :pager-count="5"
        :page-size="pageSize"
        :total="Number(total)"
        background
        class="page-wrap"
        layout="sizes, prev, pager, next"
        @size-change="onPageSizeChange"
        @current-change="changePage"
      />
      <div class="data-table-total">
        共 {{ total }} 条<template v-if="type === 'selection' && !single"> / 已选中 {{ selectItem.length }} 条</template>
      </div>
    </div>
    <!--页码 结束-->
  </div>
</template>
<style lang="less">
.layout-table-wrap {
  min-height: 0;
  position: relative;
  flex: 1 1;
  display: flex;
  flex-direction: column;
  .el-table__header-wrapper {
    height: 39px;
    overflow-y: hidden;
  }
  .el-table {
    background-color: unset;
  }
  .table-empty-wrap {
    position: absolute;
    z-index: 9;
    left: 50%;
    top: 90px;
    font-size: 14px;
    transform: translateX(-50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    color: var(--neutral-600);
    >img {
      width: 178px;
      height: 137px;
    }
  }
  .table-loading-wrap {
    bottom: 50px;
    height: 100%;
    top: 0;
  }
  .data-table-footer {
    position: relative;
    margin-top: 10px;
    background-color: #fff;
    height: 32px;
    .page-wrap {
      position: absolute;
      right: 5px;
    }
    .data-table-total {
      position: absolute;
      left: 15px;
      line-height: 32px;
    }
  }
  .el-table__body tr.highlight-row>td {
    background-color: #ecf5ff;
  }
  .el-table__empty-block {
    min-height: 250px;
  }
  .not-allowed {
    cursor: not-allowed;
  }
  .el-checkbox.is-disabled {
    pointer-events: none;
  }
}
</style>
<script>
export default {
  props: {
    // 高度配置，默认为自适应外层高度，传null则不控制高度，也可传数字控制
    height: {
      type: [String, Number],
      default: 'auto'
    },
    // 显示模式 可选【list】:仅显示列表 【selection】可选择
    type: {
      type: String,
      default: 'selection'
    },
    // 单选模式
    single: {
      type: Boolean,
      default: false
    },
    // 单选已选判定自动 (单选状态下判定已选依据)
    currentKey: {
      type: String,
      default: 'id'
    },
    // 载入状态开关
    loading: {
      type: Boolean,
      default: false
    },
    // 列表数据
    data: {
      type: Array,
      default: () => {
        return []
      }
    },
    // 子级名称
    treeProps: {
      type: Object,
      default: () => {
        return { children: 'children', hasChildren: 'hasChildren' }
      }
    },
    // 数据总数
    total: {
      type: [Number, String],
      default: 0
    },
    // 单页数据量
    pageSize: {
      type: Number
    },
    // 当前页码
    current: {
      type: Number,
      default: 1
    },
    // 是否存在分页
    multiplePage: {
      type: Boolean,
      default: true
    },
    // rowClassName prop
    rowClassName: {
      type: Function,
      default: null
    },
    // 默认排序
    defaultSort: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      selectItem: [],
      // 空列表提示文字
      tableEmptyText: ' ',
      // 已选择行
      currentRow: null,
      // 当前页码 本组件变量
      internalCurrentPage: 1,
      // 控制表格刷新
      datekey: Date.now()
    }
  },
  watch: {
    current: {
      immediate: true,
      handler(val) {
        this.internalCurrentPage = val
      }
    }
  },
  methods: {
    // 清除勾选
    'clearSelection': function() {
      this.$refs['dataTable'].clearSelection()
    },
    // 选中某项
    'toggleRowSelection': function(row, selected = true) {
      this.$refs['dataTable'].toggleRowSelection(row, selected)
    },
    // 更改pageSize
    'onPageSizeChange': function(pageSize) {
      this.$emit('on-page-size-change', pageSize)
    },
    // 翻页
    'changePage': function(num) {
      this.$emit('on-change', num)
    },
    // 排序
    'onSortChange': function(data) {
      this.$emit('on-sort-change', data)
    },
    // 选择
    'onSelect': function(data) {
      this.$emit('on-select', data)
      this.selectItem = data
    },
    // 点击单选行
    'handleCurrentChange': function(row) {
      if (row['custom-selected']) {
        return
      }
      this.$emit('on-select', [row])
      this.$emit('on-current', row)
      this.setHighlightRow(row)
    },
    // 高亮某行 {row：某行object}
    'setHighlightRow': function(row) {
      if (row && row['custom-selected']) {
        return
      }
      this.currentRow = row
    },
    // 通过选中行返回行高亮class
    'currentRowClass': function({ row, rowIndex }) {
      // 获取父组件定义的class
      const customClass = this.rowClassName
        ? this.rowClassName({ row, rowIndex })
        : {}
      // 转换字符串格式的class
      const normalizedClass = typeof customClass === 'string'
        ? { [customClass]: true }
        : customClass
      // 高亮逻辑
      const highlightClass = this.currentRow &&
        (row[this.currentKey] === this.currentRow[this.currentKey])
        ? { 'highlight-row': true }
        : {}
      // 合并class
      return {
        ...normalizedClass,
        ...highlightClass
      }
    }
  }
}
</script>
