<template>
  <div v-loading="loading" class="content-wrap-layout" style="width: 100%;height:100%">
    <!-- 针对限时题限时开始答题 -->
    <div v-if="showLimitPage" class="limit-time">
      <div class="title">随堂练习</div>
      <div v-if="isLimitModel" class="time">{{ `限时：${secondsToHms(limitTime)}` }}</div>
      <el-button type="primary" class="start-button" @click="handleStartAnswer">开始答题</el-button>
      <div class="exam-tip">
        <i class="el-icon-warning-outline mr-5"/>点击“开始答题”即开始考试，题目作答完成后需点击“提交试卷”完成考试。如遇到问题请联系管理员。
      </div>
    </div>
    <!-- 1 是随堂练习   2是考试 -->
    <el-row v-if="!showLimitPage" class="paper_container" @click="handlePicture">
      <div :xs="12" :sm="14" :md="16" :lg="18" :xl="20" class="content-wrap-layout paper_left">
        <!-- 题目信息 -->
        <!-- bankType: 1.理论题  2.靶机题  3.仿真题 -->
        <div class="question_details">
          <div v-if="questionAllList && questionAllList.length > 0" class="content-wrap-layout question_list">
            <el-tabs v-model="activeName" class="question_tabs">
              <el-tab-pane v-if="judgeArr.length" :label="`判断题（${ judgeArr.length }）`" name="judge">
                <!-- 题目列表 -->
                <div v-if="judgeArr.length" class="_question_list">
                  <!-- 判断题 -->
                  <div v-if="judgeArr.length > 0" class="mb-15">
                    <div v-for="(item, index) in judgeArr" :key="`judge${index}`" :id="`qt_judge${index}`" class="question_info">
                      <div class="flex-space-between ai-start">
                        <div class="question_item_content">
                          <div class="rich-text">
                            <div><span class="score">{{ index + 1 }}.</span></div>
                            <div>
                              {{ `【${item.questionScore}分】` }}
                              <span v-html="handleRichContent(item.content).contentWithoutImgs"/>
                            </div>
                          </div>
                          <el-image
                            v-if="handleRichContent(item.content).imgSrcArr.length > 0"
                            :src="handleRichContent(item.content).imgSrcArr[0]"
                            :preview-src-list="handleRichContent(item.content).imgSrcArr"
                            style="width: 485px; height: 300px"
                          />
                          <img src="/group1/M00/00/0F/wKg8R2dFG_yAKv6aAAW_rPFAucQ819.png" alt="">
                        </div>
                        <div v-if="item.bankType == 2">
                          <el-dropdown v-if="item.console_type" trigger="click" placement="bottom-start" @command="clickDrop($event, item, index, 'CTF')">
                            <el-button type="primary">
                              控制台<i class="el-icon-arrow-down el-icon--right" />
                            </el-button>
                            <el-dropdown-menu slot="dropdown">
                              <el-dropdown-item v-if="item.console_type && item.console_type.split(',').includes('vnc')" command="vnc">VNC</el-dropdown-item>
                              <el-dropdown-item v-if="item.console_type && item.console_type.split(',').includes('rdp')" command="rdp">远程桌面</el-dropdown-item>
                              <el-dropdown-item v-if="item.console_type && item.console_type.split(',').includes('ssh')" command="ssh">WebSSH</el-dropdown-item>
                              <el-dropdown-item v-if="item.console_type && item.console_type.split(',').includes('serial')" command="serial">串口控制台</el-dropdown-item>
                              <el-dropdown-item v-if="item.console_type && item.console_type.split(',').includes('webshell')" command="webshell">命令行</el-dropdown-item>
                            </el-dropdown-menu>
                          </el-dropdown>
                        </div>
                        <div v-if="item.bankType == 2">
                          <el-dropdown v-if="item.console_type" trigger="click" placement="bottom-start" @command="clickDrop($event, item, index, 'CTF')">
                            <el-button type="primary">
                              控制台<i class="el-icon-arrow-down el-icon--right" />
                            </el-button>
                            <el-dropdown-menu slot="dropdown">
                              <el-dropdown-item v-if="item.console_type && item.console_type.split(',').includes('vnc')" command="vnc">VNC</el-dropdown-item>
                              <el-dropdown-item v-if="item.console_type && item.console_type.split(',').includes('rdp')" command="rdp">远程桌面</el-dropdown-item>
                              <el-dropdown-item v-if="item.console_type && item.console_type.split(',').includes('ssh')" command="ssh">WebSSH</el-dropdown-item>
                              <el-dropdown-item v-if="item.console_type && item.console_type.split(',').includes('serial')" command="serial">串口控制台</el-dropdown-item>
                              <el-dropdown-item v-if="item.console_type && item.console_type.split(',').includes('webshell')" command="webshell">命令行</el-dropdown-item>
                            </el-dropdown-menu>
                          </el-dropdown>
                        </div>
                        <el-button v-if="item.bankType == 3" type="primary" class="env_btn" @click="openTestEnv(item, index, 'judge')">打开实验环境</el-button>
                      </div>
                      <div class="flex-left">
                        <el-radio-group v-for="(op, i) in JSON.parse(item.questionOptions)" :key="i" :value="item.questionAnswer" class="question_option mr-50">
                          <el-radio
                            v-model="judgeAnswerList['judgeOption' + index]"
                            :label="`${optionLabel[i]}`">
                            {{ judgeOption[i] }}
                          </el-radio>
                        </el-radio-group>
                      </div>
                    </div>
                  </div>
                </div>
              </el-tab-pane>
              <el-tab-pane v-if="singleArr.length" :label="`单选题（${ singleArr.length }）`" name="single">
                <div v-if="singleArr.length" class="_question_list">
                  <!-- 单选题 -->
                  <div v-if="singleArr.length > 0" class="mb-15">
                    <div v-for="(item, index) in singleArr" :key="`single${index}`" :id="`qt_single${index}`" class="question_info">
                      <div class="flex-space-between ai-start">
                        <div class="question_item_content">
                          <div class="rich-text">
                            <div><span class="score">{{ index + 1 }}.</span></div>
                            <div>
                              {{ `【${item.questionScore}分】` }}
                              <span v-html="handleRichContent(item.content).contentWithoutImgs"/>
                            </div>
                          </div>
                          <el-image
                            v-if="handleRichContent(item.content).imgSrcArr.length > 0"
                            :src="handleRichContent(item.content).imgSrcArr[0]"
                            :preview-src-list="handleRichContent(item.content).imgSrcArr"
                            style="width: 485px; height: 300px"
                          />
                        </div>
                        <div v-if="item.bankType == 2">
                          <el-dropdown v-if="item.console_type" trigger="click" placement="bottom-start" @command="clickDrop($event, item, index, 'CTF')">
                            <el-button type="primary">
                              控制台<i class="el-icon-arrow-down el-icon--right" />
                            </el-button>
                            <el-dropdown-menu slot="dropdown">
                              <el-dropdown-item v-if="item.console_type && item.console_type.split(',').includes('vnc')" command="vnc">VNC</el-dropdown-item>
                              <el-dropdown-item v-if="item.console_type && item.console_type.split(',').includes('rdp')" command="rdp">远程桌面</el-dropdown-item>
                              <el-dropdown-item v-if="item.console_type && item.console_type.split(',').includes('ssh')" command="ssh">WebSSH</el-dropdown-item>
                              <el-dropdown-item v-if="item.console_type && item.console_type.split(',').includes('serial')" command="serial">串口控制台</el-dropdown-item>
                              <el-dropdown-item v-if="item.console_type && item.console_type.split(',').includes('webshell')" command="webshell">命令行</el-dropdown-item>
                            </el-dropdown-menu>
                          </el-dropdown>
                        </div>
                        <el-button v-if="item.bankType == 3" type="primary" class="env_btn" @click="openTestEnv(item, index, 'judge')">打开实验环境</el-button>
                      </div>
                      <el-radio-group v-for="(op, i) in JSON.parse(item.questionOptions)" :key="op" :value="item.questionAnswer" class="question_option">
                        <el-radio
                          v-overflow-tooltip="{ content: op }"
                          v-model="singleAnswerList['singleOption' + index]"
                          :label="`${optionLabel[i]}`"
                        >
                          {{ optionLabel[i] }}. {{ op }}
                        </el-radio>
                      </el-radio-group>
                    </div>
                  </div>
                </div>
              </el-tab-pane>
              <el-tab-pane v-if="multiArr.length" :label="`多选题（${ multiArr.length }）`" name="multi">
                <div v-if="multiArr.length" class="_question_list">
                  <!-- 多选题 -->
                  <div v-if="multiArr.length > 0" class="mb-15">
                    <div v-for="(item, index) in multiArr" :key="`multi${index}`" :id="`qt_multi${index}`" class="question_info">
                      <div class="flex-space-between ai-start">
                        <div class="question_item_content">
                          <div class="rich-text">
                            <div><span class="score">{{ index + 1 }}.</span></div>
                            <div>
                              {{ `【${item.questionScore}分】` }}
                              <span v-html="handleRichContent(item.content).contentWithoutImgs"/>
                            </div>
                          </div>
                          <el-image
                            v-if="handleRichContent(item.content).imgSrcArr.length > 0"
                            :src="handleRichContent(item.content).imgSrcArr[0]"
                            :preview-src-list="handleRichContent(item.content).imgSrcArr"
                            style="width: 485px; height: 300px"
                          />
                        </div>
                        <div v-if="item.bankType == 2">
                          <el-dropdown v-if="item.console_type" trigger="click" placement="bottom-start" @command="clickDrop($event, item, index, 'CTF')">
                            <el-button type="primary">
                              控制台<i class="el-icon-arrow-down el-icon--right" />
                            </el-button>
                            <el-dropdown-menu slot="dropdown">
                              <el-dropdown-item v-if="item.console_type && item.console_type.split(',').includes('vnc')" command="vnc">VNC</el-dropdown-item>
                              <el-dropdown-item v-if="item.console_type && item.console_type.split(',').includes('rdp')" command="rdp">远程桌面</el-dropdown-item>
                              <el-dropdown-item v-if="item.console_type && item.console_type.split(',').includes('ssh')" command="ssh">WebSSH</el-dropdown-item>
                              <el-dropdown-item v-if="item.console_type && item.console_type.split(',').includes('serial')" command="serial">串口控制台</el-dropdown-item>
                              <el-dropdown-item v-if="item.console_type && item.console_type.split(',').includes('webshell')" command="webshell">命令行</el-dropdown-item>
                            </el-dropdown-menu>
                          </el-dropdown>
                        </div>
                        <el-button v-if="item.bankType == 3" type="primary" class="env_btn" @click="openTestEnv(item, index, 'judge')">打开实验环境</el-button>
                      </div>
                      <el-checkbox-group v-model="item.answerList" :value="item.questionAnswer && item.questionAnswer.split('')">
                        <div v-for="(op, i) in JSON.parse(item.questionOptions)" :key="i" class="question_option">
                          <el-checkbox
                            v-overflow-tooltip="{ content: op }"
                            :label="optionLabel[i]">
                            {{ optionLabel[i] }}. {{ op }}
                          </el-checkbox>
                        </div>
                      </el-checkbox-group>
                    </div>
                  </div>
                </div>
              </el-tab-pane>
              <el-tab-pane v-if="CTFArr.length" :label="`CTF题（${ CTFArr.length }）`" name="CTF">
                <div v-if="CTFArr.length" class="_question_list">
                  <!-- CTF题 -->
                  <div v-if="CTFArr.length > 0" class="mb-15">
                    <div v-for="(item, index) in CTFArr" :key="`qt_CTF${index}`" :id="`qt_CTF${index}`" class="question_info">
                      <div class="flex-space-between ai-start">
                        <div class="question_item_content">
                          <div class="rich-text">
                            <div><span class="score">{{ index + 1 }}.</span></div>
                            <div>
                              {{ `【${item.questionScore}分】` }}
                              <span v-html="handleRichContent(item.content).contentWithoutImgs"/>
                            </div>
                          </div>
                          <el-image
                            v-if="handleRichContent(item.content).imgSrcArr.length > 0"
                            :src="handleRichContent(item.content).imgSrcArr[0]"
                            :preview-src-list="handleRichContent(item.content).imgSrcArr"
                            style="width: 485px; height: 300px"
                          />
                        </div>
                        <div v-if="item.bankType == 2">
                          <el-dropdown v-if="item.console_type" trigger="click" placement="bottom-start" @command="clickDrop($event, item, index, 'CTF')">
                            <el-button type="primary">
                              控制台<i class="el-icon-arrow-down el-icon--right" />
                            </el-button>
                            <el-dropdown-menu slot="dropdown">
                              <el-dropdown-item v-if="item.console_type && item.console_type.split(',').includes('vnc')" command="vnc">VNC</el-dropdown-item>
                              <el-dropdown-item v-if="item.console_type && item.console_type.split(',').includes('rdp')" command="rdp">远程桌面</el-dropdown-item>
                              <el-dropdown-item v-if="item.console_type && item.console_type.split(',').includes('ssh')" command="ssh">WebSSH</el-dropdown-item>
                              <el-dropdown-item v-if="item.console_type && item.console_type.split(',').includes('serial')" command="serial">串口控制台</el-dropdown-item>
                              <el-dropdown-item v-if="item.console_type && item.console_type.split(',').includes('webshell')" command="webshell">命令行</el-dropdown-item>
                            </el-dropdown-menu>
                          </el-dropdown>
                        </div>
                        <el-button v-if="item.bankType == 3" type="primary" class="env_btn" @click="openTestEnv(item, index, 'judge')">打开实验环境</el-button>
                      </div>
                      <div v-if="item.fileUrl" style="text-align: left;margin-bottom: 10px;">
                        <span>附件：</span>
                        <el-link type="primary" @click="downloadReport(item)">{{ item.fileName }}<i class="el-icon-view el-icon-download"/></el-link>
                      </div>
                      <div class="flex-space-between">
                        <div style="display: flex; justify-content: flex-start; width: 85%;">
                          <div style="width: 70px; height: 32px; line-height: 32px;">flag：</div>
                          <el-input v-model.trim="CTFAnswerList['CTFOption' + index]" :value="item.questionAnswer" maxlength="65535" placeholder="请输入答案" style="flex: 1;" @blur="validateFlagInput(index, 'CTF')"/>
                        </div>
                      </div>
                      <div v-if="errorFlagAnswer.CTF[index]" class="error-info">flag格式不正确，请重新输入</div>
                    </div>
                  </div>
                </div>
              </el-tab-pane>
              <el-tab-pane v-if="AWDArr.length" :label="`AWD题（${ AWDArr.length }）`" name="AWD">
                <div v-if="AWDArr.length" class="_question_list">
                  <!-- AWD题 -->
                  <div v-if="AWDArr.length > 0" class="mb-15">
                    <div v-for="(item, index) in AWDArr" :key="`AWD${index}`" :id="`qt_AWD${index}`" class="question_info">
                      <div class="flex-space-between ai-start">
                        <div class="question_item_content">
                          <div class="rich-text">
                            <div><span class="score">{{ index + 1 }}.</span></div>
                            <div>
                              {{ `【${item.questionScore}分】` }}
                              <span v-html="handleRichContent(item.content).contentWithoutImgs"/>
                            </div>
                          </div>
                          <el-image
                            v-if="handleRichContent(item.content).imgSrcArr.length > 0"
                            :src="handleRichContent(item.content).imgSrcArr[0]"
                            :preview-src-list="handleRichContent(item.content).imgSrcArr"
                            style="width: 485px; height: 300px"
                          />
                        </div>
                        <div v-if="item.bankType == 2">
                          <el-dropdown v-if="item.console_type" trigger="click" placement="bottom-start" @command="clickDrop($event, item, index, 'CTF')">
                            <el-button type="primary">
                              控制台<i class="el-icon-arrow-down el-icon--right" />
                            </el-button>
                            <el-dropdown-menu slot="dropdown">
                              <el-dropdown-item v-if="item.console_type && item.console_type.split(',').includes('vnc')" command="vnc">VNC</el-dropdown-item>
                              <el-dropdown-item v-if="item.console_type && item.console_type.split(',').includes('rdp')" command="rdp">远程桌面</el-dropdown-item>
                              <el-dropdown-item v-if="item.console_type && item.console_type.split(',').includes('ssh')" command="ssh">WebSSH</el-dropdown-item>
                              <el-dropdown-item v-if="item.console_type && item.console_type.split(',').includes('serial')" command="serial">串口控制台</el-dropdown-item>
                              <el-dropdown-item v-if="item.console_type && item.console_type.split(',').includes('webshell')" command="webshell">命令行</el-dropdown-item>
                            </el-dropdown-menu>
                          </el-dropdown>
                        </div>
                        <el-button v-if="item.bankType == 3" type="primary" class="env_btn" @click="openTestEnv(item, index, 'judge')">打开实验环境</el-button>
                      </div>
                      <div v-if="item.fileUrl" style="text-align: left;margin-bottom: 10px;">
                        <span>附件：</span>
                        <el-link type="primary" @click="downloadReport(item)">{{ item.fileName }}<i class="el-icon-view el-icon-download"/></el-link>
                      </div>
                      <div class="flex-space-between">
                        <div style="display: flex; justify-content: flex-start; width: 85%;">
                          <div style="width: 70px; height: 32px; line-height: 32px;">flag：</div>
                          <el-input v-model.trim="AWDAnswerList['AWDOption' + index]" :value="item.questionAnswer" maxlength="65535" placeholder="请输入答案" style="flex: 1;" @blur="validateFlagInput(index, 'AWD')"/>
                        </div>
                      </div>
                      <div v-if="errorFlagAnswer.AWD[index]" class="error-info">flag格式不正确，请重新输入</div>
                    </div>
                  </div>
                </div>
              </el-tab-pane>
              <el-tab-pane v-if="loopholeArr.length" :label="`漏洞题（${ loopholeArr.length }）`" name="loophole">
                <div v-if="loopholeArr.length" class="_question_list">
                  <!-- 漏洞题 -->
                  <div v-if="loopholeArr.length > 0" class="mb-15">
                    <div v-for="(item, index) in loopholeArr" :key="`loophole${index}`" :id="`qt_loophole${index}`" class="question_info">
                      <div class="flex-space-between ai-start mb-10">
                        <div class="question_item_content">
                          <div class="rich-text">
                            <div><span class="score">{{ index + 1 }}.</span></div>
                            <div>
                              {{ `【${item.questionScore}分】` }}
                              <span v-html="handleRichContent(item.content).contentWithoutImgs"/>
                            </div>
                          </div>
                          <el-image
                            v-if="handleRichContent(item.content).imgSrcArr.length > 0"
                            :src="handleRichContent(item.content).imgSrcArr[0]"
                            :preview-src-list="handleRichContent(item.content).imgSrcArr"
                            style="width: 485px; height: 300px"
                          />
                        </div>
                        <div v-if="item.bankType == 2">
                          <el-dropdown v-if="item.console_type" trigger="click" placement="bottom-start" @command="clickDrop($event, item, index, 'CTF')">
                            <el-button type="primary">
                              控制台<i class="el-icon-arrow-down el-icon--right" />
                            </el-button>
                            <el-dropdown-menu slot="dropdown">
                              <el-dropdown-item v-if="item.console_type && item.console_type.split(',').includes('vnc')" command="vnc">VNC</el-dropdown-item>
                              <el-dropdown-item v-if="item.console_type && item.console_type.split(',').includes('rdp')" command="rdp">远程桌面</el-dropdown-item>
                              <el-dropdown-item v-if="item.console_type && item.console_type.split(',').includes('ssh')" command="ssh">WebSSH</el-dropdown-item>
                              <el-dropdown-item v-if="item.console_type && item.console_type.split(',').includes('serial')" command="serial">串口控制台</el-dropdown-item>
                              <el-dropdown-item v-if="item.console_type && item.console_type.split(',').includes('webshell')" command="webshell">命令行</el-dropdown-item>
                            </el-dropdown-menu>
                          </el-dropdown>
                        </div>
                        <el-button v-if="item.bankType == 3" type="primary" class="env_btn" @click="openTestEnv(item, index, 'judge')">打开实验环境</el-button>
                      </div>
                      <div v-if="item.fileUrl" style="text-align: left;margin-bottom: 10px;">
                        <span>附件：</span>
                        <el-link type="primary" @click="downloadReport(item)">{{ item.fileName }}<i class="el-icon-view el-icon-download"/></el-link>
                      </div>
                      <div class="flex-space-between">
                        <div style="display: flex; justify-content: flex-start; width: 85%;">
                          <div style="width: 70px; height: 32px; line-height: 32px;">flag：</div>
                          <el-input v-model.trim="loopholeAnswerList['loopholeOption' + index]" :value="item.questionAnswer" maxlength="65535" placeholder="请输入答案" style="flex: 1;" @blur="validateFlagInput(index, 'loophole')"/>
                        </div>
                      </div>
                      <div v-if="errorFlagAnswer.loophole[index]" class="error-info">flag格式不正确，请重新输入</div>
                    </div>
                  </div>
                </div>
              </el-tab-pane>
              <el-tab-pane v-if="fillBlankArr.length" :label="`填空题（${ fillBlankArr.length }）`" name="fillBlank">
                <div v-if="fillBlankArr.length" class="_question_list">
                  <!-- 填空题 -->
                  <div v-if="fillBlankArr.length > 0" class="mb-15">
                    <div v-for="(item, index) in fillBlankArr" :key="`fillBlank${index}`" :id="`qt_fillBlank${index}`" class="question_info">
                      <div class="flex-space-between ai-start mb-10">
                        <div class="question_item_content">
                          <div class="rich-text">
                            <div><span class="score">{{ index + 1 }}.</span></div>
                            <div>
                              {{ `【${item.questionScore}分】` }}
                              <span v-html="handleRichContent(item.content).contentWithoutImgs"/>
                            </div>
                          </div>
                          <el-image
                            v-if="handleRichContent(item.content).imgSrcArr.length > 0"
                            :src="handleRichContent(item.content).imgSrcArr[0]"
                            :preview-src-list="handleRichContent(item.content).imgSrcArr"
                            style="width: 485px; height: 300px"
                          />
                        </div>
                        <div v-if="item.bankType == 2">
                          <el-dropdown v-if="item.console_type" trigger="click" placement="bottom-start" @command="clickDrop($event, item, index, 'CTF')">
                            <el-button type="primary">
                              控制台<i class="el-icon-arrow-down el-icon--right" />
                            </el-button>
                            <el-dropdown-menu slot="dropdown">
                              <el-dropdown-item v-if="item.console_type && item.console_type.split(',').includes('vnc')" command="vnc">VNC</el-dropdown-item>
                              <el-dropdown-item v-if="item.console_type && item.console_type.split(',').includes('rdp')" command="rdp">远程桌面</el-dropdown-item>
                              <el-dropdown-item v-if="item.console_type && item.console_type.split(',').includes('ssh')" command="ssh">WebSSH</el-dropdown-item>
                              <el-dropdown-item v-if="item.console_type && item.console_type.split(',').includes('serial')" command="serial">串口控制台</el-dropdown-item>
                              <el-dropdown-item v-if="item.console_type && item.console_type.split(',').includes('webshell')" command="webshell">命令行</el-dropdown-item>
                            </el-dropdown-menu>
                          </el-dropdown>
                        </div>
                        <el-button v-if="item.bankType == 3" type="primary" class="env_btn" @click="openTestEnv(item, index, 'judge')">打开实验环境</el-button>
                      </div>
                      <div class="flex-space-between">
                        <div style="display: flex; justify-content: flex-start; width: 85%;">
                          <div style="width: 70px; height: 32px; line-height: 32px;">答案：</div>
                          <el-input v-model.trim="fillBlankAnswerList['fillBlankOption' + index]" :value="item.questionAnswer" maxlength="65535" style="flex: 1;" placeholder="请输入答案"/>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </el-tab-pane>
              <el-tab-pane v-if="otherArr.length" :label="`其他题（${ otherArr.length }）`" name="other">
                <div v-if="otherArr.length" class="_question_list">
                  <!-- 其他题 -->
                  <div v-if="otherArr.length > 0" class="mb-15">
                    <div v-for="(item, index) in otherArr" :key="`other${index}`" :id="`qt_other${index}`" class="question_info">
                      <div class="flex-space-between ai-start mb-10">
                        <div class="question_item_content">
                          <div class="rich-text">
                            <div><span class="score">{{ index + 1 }}.</span></div>
                            <div>
                              {{ `【${item.questionScore}分】` }}
                              <span v-html="handleRichContent(item.content).contentWithoutImgs"/>
                            </div>
                          </div>
                          <el-image
                            v-if="handleRichContent(item.content).imgSrcArr.length > 0"
                            :src="handleRichContent(item.content).imgSrcArr[0]"
                            :preview-src-list="handleRichContent(item.content).imgSrcArr"
                            style="width: 485px; height: 300px"
                          />
                        </div>
                        <div v-if="item.bankType == 2">
                          <el-dropdown v-if="item.console_type" trigger="click" placement="bottom-start" @command="clickDrop($event, item, index, 'CTF')">
                            <el-button type="primary">
                              控制台<i class="el-icon-arrow-down el-icon--right" />
                            </el-button>
                            <el-dropdown-menu slot="dropdown">
                              <el-dropdown-item v-if="item.console_type && item.console_type.split(',').includes('vnc')" command="vnc">VNC</el-dropdown-item>
                              <el-dropdown-item v-if="item.console_type && item.console_type.split(',').includes('rdp')" command="rdp">远程桌面</el-dropdown-item>
                              <el-dropdown-item v-if="item.console_type && item.console_type.split(',').includes('ssh')" command="ssh">WebSSH</el-dropdown-item>
                              <el-dropdown-item v-if="item.console_type && item.console_type.split(',').includes('serial')" command="serial">串口控制台</el-dropdown-item>
                              <el-dropdown-item v-if="item.console_type && item.console_type.split(',').includes('webshell')" command="webshell">命令行</el-dropdown-item>
                            </el-dropdown-menu>
                          </el-dropdown>
                        </div>
                        <el-button v-if="item.bankType == 3" type="primary" class="env_btn" @click="openTestEnv(item, index, 'judge')">打开实验环境</el-button>
                      </div>
                      <div v-if="item.fileUrl" style="text-align: left;margin-bottom: 10px;">
                        <span>附件：</span>
                        <el-link type="primary" @click="downloadReport(item)">{{ item.fileName }}<i class="el-icon-view el-icon-download"/></el-link>
                      </div>
                      <div class="flex-space-between">
                        <div style="display: flex; justify-content: flex-start; width: 85%;">
                          <div style="width: 70px; height: 32px; line-height: 32px;">flag：</div>
                          <el-input v-model.trim="otherAnswerList['otherOption' + index]" :value="item.questionAnswer" maxlength="65535" placeholder="请输入答案" style="flex: 1;" @blur="validateFlagInput(index, 'other')"/>
                        </div>
                      </div>
                      <div v-if="errorFlagAnswer.other[index]" class="error-info">flag格式不正确，请重新输入</div>
                    </div>
                  </div>
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>
          <el-empty
            v-else
            :image="img"
            :image-size="110"
            style="margin: 100px auto"
            description="暂无数据"
          />
        </div>
      </div>
      <!-- 答题卡 -->
      <div :xs="12" :sm="10" :md="8" :lg="6" :xl="4" class="content-wrap-layout paper_right">
        <div class="content-wrap-layout question_sn">
          <div class="paper_info_title">
            <div>答题卡</div>
            <div v-if="selfEnv == 'true' && bankTypeList.length">
              <span>
                <el-tooltip v-if="envStatus==0" transfer>
                  <img src="@/assets/img/server.svg" alt="" width="15">
                  <div slot="content">环境未开启</div>
                </el-tooltip>
                <el-tooltip v-if="envStatus == 1" transfer>
                  <img src="@/assets/img/server1.svg" alt="" width="15">
                  <div slot="content">环境已开启</div>
                </el-tooltip>
                <el-tooltip v-if="envStatus == 2" transfer>
                  <img src="@/assets/img/server2.svg" alt="" width="15">
                  <div slot="content">环境异常</div>
                </el-tooltip>
                <el-tooltip v-if="envStatus == 3" transfer>
                  <img class="loadding" src="@/assets/img/loadding.svg" alt="" width="15">
                  <div slot="content">环境开启中/关闭中</div>
                </el-tooltip>
              </span>
              <el-button v-if="envStatus==0" type="primary" @click="emitSelfENV">开启题目环境</el-button>
              <el-button v-else type="primary" @click="emitSelfENV">关闭题目环境</el-button>
            </div>
          </div>
          <div class="paper_content">
            <!-- 判断题 -->
            <div v-if="judgeArr.length > 0">
              <div class="question_type">{{ `判断题(共${judgeArr.length}题 共${judgeTotalScore}分)` }}</div>
              <div class="question_sn_order">
                <div
                  v-for="item in judgeArr.length" :key="`judge${item}`"
                  :class="`question_sn_order_item ${currentOrder.judge[item - 1] === item && 'question_sn_order_item_check'}`"
                  @click="handleOrderClick(item, 'judge')">
                  {{ item }}
                </div>
              </div>
            </div>
            <!-- 单选题 -->
            <div v-if="singleArr.length > 0">
              <div class="question_type">{{ `单选题(共${singleArr.length}题 共${singleTotalScore}分)` }}</div>
              <div class="question_sn_order">
                <div
                  v-for="item in singleArr.length" :key="`single${item}`"
                  :class="`question_sn_order_item ${currentOrder.single[item - 1] === item && 'question_sn_order_item_check'}`"
                  @click="handleOrderClick(item, 'single')">
                  {{ item }}
                </div>
              </div>
            </div>
            <!-- 多选题 -->
            <div v-if="multiArr.length > 0">
              <div class="question_type">{{ `多选题(共${multiArr.length}题 共${multiTotalScore}分)` }}</div>
              <div class="question_sn_order">
                <div
                  v-for="item in multiArr.length" :key="`multi${item}`"
                  :class="`question_sn_order_item ${currentOrder.multi[item - 1] === item && 'question_sn_order_item_check'}`"
                  @click="handleOrderClick(item, 'multi')">
                  {{ item }}
                </div>
              </div>
            </div>
            <!-- CTF题 -->
            <div v-if="CTFArr.length > 0">
              <div class="question_type">{{ `CTF题(共${CTFArr.length}题 共${CTFTotalScore}分)` }}</div>
              <div class="question_sn_order">
                <div
                  v-for="item in CTFArr.length" :key="`order_CTF${item}`"
                  :class="`question_sn_order_item ${currentOrder.CTF[item - 1] === item && 'question_sn_order_item_check'}`"
                  @click="handleOrderClick(item, 'CTF')">
                  {{ item }}
                </div>
              </div>
            </div>
            <!-- AWD题 -->
            <div v-if="AWDArr.length > 0">
              <div class="question_type">{{ `AWD题(共${AWDArr.length}题 共${AWDTotalScore}分)` }}</div>
              <div class="question_sn_order">
                <div
                  v-for="item in AWDArr.length" :key="`AWD${item}`"
                  :class="`question_sn_order_item ${currentOrder.AWD[item - 1] === item && 'question_sn_order_item_check'}`"
                  @click="handleOrderClick(item, 'AWD')">
                  {{ item }}
                </div>
              </div>
            </div>
            <!-- 漏洞题 -->
            <div v-if="loopholeArr.length > 0">
              <div class="question_type">{{ `漏洞题(共${loopholeArr.length}题 共${loopholeTotalScore}分)` }}</div>
              <div class="question_sn_order">
                <div
                  v-for="item in loopholeArr.length" :key="`loophole${item}`"
                  :class="`question_sn_order_item ${currentOrder.loophole[item - 1] === item && 'question_sn_order_item_check'}`"
                  @click="handleOrderClick(item, 'loophole')">
                  {{ item }}
                </div>
              </div>
            </div>
            <!-- 填空题 -->
            <div v-if="fillBlankArr.length > 0">
              <div class="question_type">{{ `填空题(共${fillBlankArr.length}题 共${fillBlankTotalScore}分)` }}</div>
              <div class="question_sn_order">
                <div
                  v-for="item in fillBlankArr.length" :key="`fillBlank${item}`"
                  :class="`question_sn_order_item ${currentOrder.fillBlank[item - 1] === item && 'question_sn_order_item_check'}`"
                  @click="handleOrderClick(item, 'fillBlank')">
                  {{ item }}
                </div>
              </div>
            </div>
            <!-- 简答题 -->
            <div v-if="shortAnswerArr.length > 0">
              <div class="question_type">{{ `简答题(共${shortAnswerArr.length}题 共${shortAnswerTotalScore}分)` }}</div>
              <div class="question_sn_order">
                <div
                  v-for="item in shortAnswerArr.length" :key="`shortAnswer${item}`"
                  :class="`question_sn_order_item ${currentOrder.shortAnswer[item - 1] === item && 'question_sn_order_item_check'}`"
                  @click="handleOrderClick(item, 'shortAnswer')">
                  {{ item }}
                </div>
              </div>
            </div>
            <!-- 综合题 -->
            <div v-if="synthesisArr.length > 0">
              <div class="question_type">{{ `组合题(共${synthesisArr.length}题 共${synthesisTotalScore}分)` }}</div>
              <div class="question_sn_order">
                <div
                  v-for="item in synthesisArr.length" :key="`synthesis${item}`"
                  :class="`question_sn_order_item ${currentOrder.synthesis[item - 1] === item && 'question_sn_order_item_check'}`"
                  @click="handleOrderClick(item, 'synthesis')">
                  {{ item }}
                </div>
              </div>
            </div>
            <!-- 其他题 -->
            <div v-if="otherArr.length > 0">
              <div class="question_type">{{ `其他题(共${otherArr.length}题 共${otherTotalScore}分)` }}</div>
              <div class="question_sn_order">
                <div
                  v-for="item in otherArr.length" :key="`other${item}`"
                  :class="`question_sn_order_item ${currentOrder.other[item - 1] === item && 'question_sn_order_item_check'}`"
                  @click="handleOrderClick(item, 'other')">
                  {{ item }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-row>
  </div>
</template>

<script>
import VueCountdown from '@x.ken/vue-countdown'
import { handleRichContent } from '@/utils/index.js'
import { queryPracticeUser } from '@/api/teacher/index'
import { queryCourseDetail } from '@/api/teacher/index.js'
import { openTrainingEnv, closeTrainingEnv, envStatusApi } from '@/api/teachingAffairs/index.js'
import { getConsole, getNodeItem, serial, vnc } from '@/packages/topo/api/orchestration'
import { tabManager } from '@/packages/utils/tabManager.js'

export default {
  // 考试试卷
  name: 'ExaminationPaper',
  components: {
    VueCountdown
  },
  props: {
    // 隐藏倒计时
    hiddenCountdown: Boolean,
    // 模拟练习传过来的
    isExam: Boolean,
    // 随堂练习和模拟练习的时间
    sectionSeason: String,
    sectionTime: String,
    // 1未开始，2进行中，3已结束
    status: Number,
    examName: String
  },
  data() {
    return {
      img: require('@/assets/empty_state.png'),
      envStatus: '',
      studentAnswerList: [],
      questionAllList: [],
      loading: false,
      sign: this.$route.query.sign,
      answerTime: this.$route.query.answerTime,
      examType: this.$route.query.examType || null,
      type: 1,
      currentOrder: {
        judge: [],
        single: [],
        multi: [],
        CTF: [],
        AWD: [],
        loophole: [],
        other: [],
        fillBlank: [],
        shortAnswer: [],
        synthesis: []
      },
      selfEnv: true,
      countdownTime: 0,
      countdownSecond: 1,
      judgeOption: ['正确', '错误'],
      judgeArr: [], // 判断题
      singleArr: [], // 单选题
      multiArr: [], // 多选题
      CTFArr: [], // CTF题
      AWDArr: [], // AWD题
      loopholeArr: [], // 漏洞题
      otherArr: [], // 其他题
      fillBlankArr: [],
      shortAnswerArr: [],
      synthesisArr: [], // 综合题
      judgeTotalScore: 0,
      singleTotalScore: 0,
      multiTotalScore: 0,
      CTFTotalScore: 0,
      AWDTotalScore: 0,
      loopholeTotalScore: 0,
      otherTotalScore: 0,
      fillBlankTotalScore: 0,
      shortAnswerTotalScore: 0,
      synthesisTotalScore: 0,
      optionLabel: ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J'],
      questionLabel: ['一', '二', '三', '四', '五', '六', '七', '八', '九', '十', '十一'],
      numberedQuestionTypes: [],
      judgeAnswerList: {}, // 判断题答案
      singleAnswerList: {}, // 单选题答案
      multiAnswerList: {}, // 多选题答案
      CTFAnswerList: {}, // CTF答案
      AWDAnswerList: {}, // AWD答案
      loopholeAnswerList: {}, // 漏洞答案
      otherAnswerList: {}, // 其他题答案
      fillBlankAnswerList: {}, // 填空题答案
      shortAnswerAnswerList: {}, // 简答题答案
      synthesisAnswerList: {}, // 综合题答案
      currentQuestionDom: '',
      judgeTitle: '判断题',
      singleTitle: '单选题',
      multiTitle: '多选题',
      CTFTitle: 'CTF题',
      AWDTitle: 'AWD题',
      loopholeTitle: '漏洞题',
      otherTitle: '其他题',
      fillBlankTitle: '填空题',
      shortAnswerTitle: '简答题',
      synthesisTitle: '组合题',
      bankTypeList: [],
      studentSubmitQuestionBoList: [],
      paperId: '',
      examId: '',
      errorFlagAnswer: {
        CTF: [],
        AWD: [],
        loophole: [],
        other: [],
        fillBlan: [],
        shortAnswer: []
      },
      uploadQuestionFile: {
        CTF: [],
        AWD: [],
        loophole: [],
        other: []
      },
      unFinishQuestionCount: 0,
      submitLoading: false,
      activeName: '',
      timeDifferenceInSeconds: '',
      limitTime: 0,
      isLimitModel: false, // 是否为限时模式
      showLimitPage: false,
      courseId: this.$route.query.courseId || '',
      contentId: this.$route.query.curriculumCode || '',
      schedulingCode: this.$route.query.schedulingId || '',
      handleRichContent
    }
  },
  computed: {
    questionTypes() {
      return [
        this.judgeArr.length > 0 ? 'judge' : null,
        this.singleArr.length > 0 ? 'single' : null,
        this.multiArr.length > 0 ? 'multi' : null,
        this.CTFArr.length > 0 ? 'CTF' : null,
        this.AWDArr.length > 0 ? 'AWD' : null,
        this.loopholeArr.length > 0 ? 'loophole' : null,
        this.fillBlankArr.length > 0 ? 'fillBlank' : null,
        this.otherArr.length > 0 ? 'other' : null
      ]
    },
    numberedQuestionTypesName() {
      return this.questionTypes.filter(type => type !== null).map((type, index) => ({ index, type }))
    }
  },
  watch: {
    questionAllList() {
      this.getPaperDetails()
    },
    numberedQuestionTypesName: {
      handler(newVal) {
        if (newVal && newVal[0]) {
          setTimeout(() => {
            this.activeName = newVal[0].type
          }, 800)
        }
      },
      deep: true
    },
    // 动态生成判断 v-model
    judgeArr() {
      this.judgeTotalScore = 0
      this.judgeArr.map((el, i) => {
        this.$set(this.judgeAnswerList, 'judgeOption' + i, '')
        this.judgeTotalScore += Number(el.questionScore)
      })
    },
    // 动态生成单选 v-model
    singleArr() {
      this.singleTotalScore = 0
      this.singleArr.map((el, i) => {
        this.$set(this.singleAnswerList, 'singleOption' + i, '')
        this.singleTotalScore += Number(el.questionScore)
      })
    },
    // 动态生成多选 v-model
    multiArr() {
      this.multiTotalScore = 0
      this.multiArr.map((el, i) => {
        this.$set(this.multiAnswerList, 'multiOption' + i, [])
        this.multiTotalScore += Number(el.questionScore)
      })
    },
    CTFArr() {
      this.CTFTotalScore = 0
      this.CTFArr.map((el, i) => {
        this.$set(this.CTFAnswerList, 'CTFOption' + i, '')
        this.CTFTotalScore += Number(el.questionScore)
      })
    },
    AWDArr() {
      this.AWDTotalScore = 0
      this.AWDArr.map((el, i) => {
        this.$set(this.AWDAnswerList, 'AWDOption' + i, '')
        this.AWDTotalScore += Number(el.questionScore)
      })
    },
    loopholeArr() {
      this.loopholeTotalScore = 0
      this.loopholeArr.map((el, i) => {
        this.$set(this.loopholeAnswerList, 'loopholeOption' + i, '')
        this.loopholeTotalScore += Number(el.questionScore)
      })
    },
    otherArr() {
      this.otherTotalScore = 0
      this.otherArr.map((el, i) => {
        this.$set(this.otherAnswerList, 'otherOption' + i, '')
        this.otherTotalScore += Number(el.questionScore)
      })
    },
    fillBlankArr() {
      this.fillBlankTotalScore = 0
      this.fillBlankArr.map((el, i) => {
        this.$set(this.fillBlankAnswerList, 'fillBlankOption' + i, '')
        this.fillBlankTotalScore += Number(el.questionScore)
      })
    },
    shortAnswerArr() {
      this.shortAnswerTotalScore = 0
      this.shortAnswerArr.map((el, i) => {
        this.$set(this.shortAnswerAnswerList, 'shortAnswerOption' + i, '')
        this.shortAnswerTotalScore += Number(el.questionScore)
      })
    },
    synthesisArr() {
      this.synthesisTotalScore = 0
      this.synthesisArr.map((el, i) => {
        this.$set(this.synthesisAnswerList, 'synthesisOption' + el.id, '')
        this.synthesisTotalScore += Number(el.questionScore)
      })
    },
    // 答完的题目与答题卡的联动
    judgeAnswerList: {
      handler(newVal) {
        if (newVal) {
          Object.keys(this.judgeAnswerList).map((el, i) => {
            if (this.judgeAnswerList['judgeOption' + i]) {
              this.currentOrder.judge[i] = i + 1
            }
          })
        }
      },
      deep: true
    },
    singleAnswerList: {
      handler(newVal) {
        if (newVal) {
          Object.keys(this.singleAnswerList).map((el, i) => {
            if (this.singleAnswerList['singleOption' + i]) {
              this.currentOrder.single[i] = i + 1
            }
          })
        }
      },
      deep: true
    },
    multiAnswerList: {
      handler(newVal) {
        if (newVal) {
          Object.keys(this.multiAnswerList).map((el, i) => {
            if (this.multiAnswerList['multiOption' + i].length > 0) {
              this.currentOrder.multi[i] = i + 1
            } else {
              this.currentOrder.multi[i] = -1
            }
          })
        }
      },
      deep: true
    },
    CTFAnswerList: {
      handler(newVal) {
        if (newVal) {
          Object.keys(this.CTFAnswerList).map((el, i) => {
            if (this.CTFAnswerList['CTFOption' + i]) {
              this.currentOrder.CTF[i] = i + 1
            } else {
              this.currentOrder.CTF[i] = -1
            }
          })
        }
      },
      deep: true
    },
    AWDAnswerList: {
      handler(newVal) {
        if (newVal) {
          Object.keys(this.AWDAnswerList).map((el, i) => {
            if (this.AWDAnswerList['AWDOption' + i]) {
              this.currentOrder.AWD[i] = i + 1
            } else {
              this.currentOrder.AWD[i] = -1
            }
          })
        }
      },
      deep: true
    },
    loopholeAnswerList: {
      handler(newVal) {
        if (newVal) {
          Object.keys(this.loopholeAnswerList).map((el, i) => {
            if (this.loopholeAnswerList['loopholeOption' + i]) {
              this.currentOrder.loophole[i] = i + 1
            } else {
              this.currentOrder.loophole[i] = -1
            }
          })
        }
      },
      deep: true
    },
    otherAnswerList: {
      handler(newVal) {
        if (newVal) {
          Object.keys(this.otherAnswerList).map((el, i) => {
            if (this.otherAnswerList['otherOption' + i]) {
              this.currentOrder.other[i] = i + 1
            } else {
              this.currentOrder.other[i] = -1
            }
          })
        }
      },
      deep: true
    },
    fillBlankAnswerList: {
      handler(newVal) {
        if (newVal) {
          Object.keys(this.fillBlankAnswerList).map((el, i) => {
            if (this.fillBlankAnswerList['fillBlankOption' + i]) {
              this.currentOrder.fillBlank[i] = i + 1
            } else {
              this.currentOrder.fillBlank[i] = -1
            }
          })
        }
      },
      deep: true
    },
    shortAnswerAnswerList: {
      handler(newVal) {
        if (newVal) {
          Object.keys(this.shortAnswerAnswerList).map((el, i) => {
            if (this.shortAnswerAnswerList['shortAnswerOption' + i]) {
              this.currentOrder.shortAnswer[i] = i + 1
            } else {
              this.currentOrder.shortAnswer[i] = -1
            }
          })
        }
      },
      deep: true
    },
    synthesisAnswerList: {
      handler(newVal) {
        if (newVal) {
          Object.keys(this.synthesisAnswerList).map((el, i) => {
            if (this.synthesisAnswerList[el]) {
              this.currentOrder.synthesis[i] = i + 1
            } else {
              this.currentOrder.synthesis[i] = -1
            }
          })
        }
      },
      deep: true
    },
    countdownSecond: {
      handler(newVal) {
        if (this.isLimitModel) { // 答题限时才自动提交
          if (newVal <= 0) {
            this.handPaper(1)
            this.$message.success('当前考试时间已结束，系统已自动提交试卷')
          }
        }
      }
    }
  },
  created() {
    this.isLimitModel = this.answerTime > 0
  },
  mounted() {
    this.getQuestionAllList()
    window.onbeforeunload = () => {
      this.cacheAnswers()
    }
    this.pollAsyncRequest()
  },
  beforeDestroy() {
    this.closePollAsyncRequest()
  },
  methods: {
    // getStudentAnswer() {
    //   if (this.schedulingCode) {
    //     studentAnswer({ schedulingCode: this.schedulingCode }).then((res) => {
    //       if (res.code == 0) {
    //         this.studentAnswerList = res.data || []
    //         this.questionAllList.forEach((k) => {
    //           const data = this.studentAnswerList.find(j => j.questionCode == k.questionCode)
    //           this.loading = false
    //           if (data) {
    //             this.$set(k, 'questionStudentScore', data.questionScore)
    //             this.$set(k, 'questionUserAnswer', data.questionUserAnswer)
    //           }
    //         })
    //       }
    //     })
    //   } else {
    //     studentSelfStudyAnswer({ courseId: this.courseId, contentId: this.contentId }).then((res) => {
    //       if (res.code == 0) {
    //         this.studentAnswerList = res.data || []
    //         this.questionAllList.forEach((k) => {
    //           const data = this.studentAnswerList.find(j => j.questionCode == k.questionCode)
    //           this.loading = false
    //           if (data) {
    //             this.$set(k, 'questionStudentScore', data.questionScore)
    //             this.$set(k, 'questionUserAnswer', data.questionUserAnswer)
    //           }
    //         })
    //       }
    //     })
    //   }
    // },
    getQuestionAllList() {
      this.loading = true
      const params = {
        examType: this.examType,
        contentId: this.contentId,
        courseCode: this.courseId
      }
      if (this.examType == '1') {
        params.schedulingBatchNum = this.schedulingBatchNum
      }
      queryPracticeUser(params).then(res => {
        if (res.code == 0) {
          // 靶机题去获取节点详情
          res.data.forEach(item => {
            if (item.bankType == 2 && item.vmId) {
              getNodeItem(item.vmId).then(response => {
                this.$set(item, 'console_type', response.data.data.network_element_data.console_type)
                this.$set(item, 'nodeName', response.data.data.name)
              })
            }
          })
          this.questionAllList = res.data || []
          if (!this.requiredCourse) {
            // this.getStudentAnswer()
          }
        }
      }).finally(() => {
        this.loading = false
      })
    },
    clickDrop(name, item, index, type) {
      switch (name) {
        case 'vnc': {
          vnc(item.vmId)
            .then(res => {
              if (res.data.code == 0) {
                const url = res.data.data
                const a = document.createElement('a')
                a.setAttribute('href', encodeURI(url))
                a.setAttribute('target', '_blank')
                a.setAttribute('id', 'camnpr')
                document.body.appendChild(a)
                a.click()
              }
            })
            .catch(() => {})
          break
        }
        case 'rdp':
        case 'webshell':
        case 'ssh': {
          getConsole(item.vmId, { console_type: name })
            .then(res => {
              const url = (window.location.hostname == 'localhost' ? (window.ADMIN_CONFIG ? window.ADMIN_CONFIG.VIP_URL : window.WEB_CONFIG.VIP_URL) : '') + res.data.data
              const a = document.createElement('a')
              a.setAttribute('href', encodeURI(url))
              a.setAttribute('target', '_blank')
              a.setAttribute('id', 'camnpr')
              document.body.appendChild(a)
              a.click()
            })
            .catch(() => {})
          break
        }
        case 'serial': {
          serial(item.vmId)
            .then(res => {
              const url = res.data.data + '&title=' + item.nodeName
              const a = document.createElement('a')
              a.setAttribute('href', encodeURI(url))
              a.setAttribute('target', '_blank')
              a.setAttribute('id', 'camnpr')
              document.body.appendChild(a)
              a.click()
            })
            .catch(() => {})
          break
        }
      }
    },
    emitSelfENV() {
      const params = {
        schedulingCode: null,
        courseCode: this.$route.query.courseId,
        curriculumCode: this.contentId || this.id
      }
      if (this.envStatus == 0) {
        openTrainingEnv(params).then((res) => {
          if (res.code === 0) {
            this.$message({
              message: '正在开启环境',
              type: 'success'
            })
            this.pollAsyncRequest()
          }
        })
      } else {
        closeTrainingEnv(params).then((res) => {
          if (res.code === 0) {
            this.$message({
              message: '正在关闭环境',
              type: 'success'
            })
            this.pollAsyncRequest()
          }
        }).catch(() => {
          this.loading = false
        })
      }
    },
    cacheAnswers() {
      const finishTempQuestion = {
        judgeAnswerList: {},
        singleAnswerList: {},
        multiAnswerList: {},
        CTFAnswerList: {},
        AWDAnswerList: {},
        loopholeAnswerList: {},
        otherAnswerList: {},
        fillBlankAnswerList: {},
        shortAnswerAnswerList: {}
      }
      for (const key in finishTempQuestion) {
        finishTempQuestion[key] = this[key]
      }
      finishTempQuestion.synthesis = this.currentOrder.synthesis
      finishTempQuestion.currentQuestionDom = this.currentQuestionDom
      // 打开实验环境将已答完题目存一遍
      // localStorage.setItem('examCenter_finishTempQuestion', JSON.stringify(finishTempQuestion))
    },
    // 打开实验环境
    openTestEnv(item, index, type) {
      this.currentQuestionDom = `qt_${type}${index}`
      // 综合题答题卡联动
      if (item.questionType == 10) {
        this.$set(this.currentOrder.synthesis, index, index + 1)
      }
      this.cacheAnswers()
      // 浏览器tab页签唯一值用题目id和topoid拼接
      const id = `exercises-${item.id}-${item.topologyId}`
      const route = this.$router.resolve({
        name: 'exercisesPaperEnv',
        query: {
          questionId: item.id,
          ...this.$route.query
        }
      })
      const existingRef = tabManager.getTabRef(id)
      if (existingRef && !existingRef.closed) {
        // 非当前打开者链的话，就新打开一个界面，不能打开空界面
        if (existingRef.location.href === 'about:blank') {
          existingRef.location.replace(route.href)
          setTimeout(() => existingRef.focus(), 300)
        } else {
          existingRef.focus()
        }
      } else {
        const newTab = window.open(route.href, `tab-${id}`)
        tabManager.setTabRef(id, newTab)
      }
    },
    envStatusFn() {
      const params = {
        schedulingCode: null,
        courseCode: this.courseId,
        curriculumCode: this.contentId
      }
      envStatusApi(params).then((result) => {
        if (result.code === 0) {
          const { total, running, error } = result.data
          if (total === 0) { // 未开启
            this.envStatus = 0
            this.closePollAsyncRequest()
          } else {
            if (total === running) { // 启动成功
              this.envStatus = 1
              this.closePollAsyncRequest()
            } else {
              if (error !== 0) { // 启动失败
                this.envStatus = 2
                this.closePollAsyncRequest()
              } else { // 启动中
                this.envStatus = 3
              }
            }
          }
        }
      })
    },
    // 轮询
    pollAsyncRequest(interval = 5000) {
      this.envStatusFn() // 每次轮询之前先立即调用一次，然后接着 5s 后进行轮询更新环境状态
      this.closePollAsyncRequest()
      this.timer = setInterval(() => {
        this.envStatusFn()
      }, interval)
    },
    // 关闭轮询定时器
    closePollAsyncRequest() {
      this.timer && clearInterval(this.timer)
    },
    // 根据课程id查询内容
    getCourseDetail() {
      const params = {
        courseId: this.courseId,
        page: 1,
        limit: 1000
      }
      queryCourseDetail(params).then(res => {
        if (res.code === 0 || res.code === 200) {
          if (res.data.records[0] && res.data.records[0].answerTime) {
            this.limitTime = res.data.records[0].answerTime
            this.isLimitModel = true
          }
        }
      })
    },
    secondsToHms(seconds) {
      if (seconds === 0) return '不限时'
      const hours = Math.floor(seconds / 3600)
      seconds %= 3600
      const minutes = Math.floor(seconds / 60)
      seconds %= 60
      let hms = ''
      hms += hours.toString().padStart(2, '0') + '时'
      hms += minutes.toString().padStart(2, '0') + '分'
      hms += seconds.toString().padStart(2, '0') + '秒'
      return hms
    },
    // 校验flag答案格式
    validateFlagInput(index, type) {
      const pattern = /^.{0,65535}$/
      let flagType = []
      if (type == 'CTF') {
        flagType = 'CTFAnswerList'
      } else if (type == 'AWD') {
        flagType = 'AWDAnswerList'
      } else if (type == 'loophole') {
        flagType = 'loopholeAnswerList'
      } else if (type == 'other') {
        flagType = 'otherAnswerList'
      }
      if (!pattern.test(this[flagType][`${type}Option${index}`]) && this[flagType][`${type}Option${index}`]) {
        this.$set(this.errorFlagAnswer[type], index, true)
      } else {
        this.$set(this.errorFlagAnswer[type], index, false)
      }
    },
    // 试卷详情
    getPaperDetails() {
      this.judgeArr = this.questionAllList.filter(q => q.questionType == '3')
      this.singleArr = this.questionAllList.filter(q => q.questionType == '1')
      this.multiArr = this.questionAllList.filter(q => q.questionType == '2')
      // 多选题答案单独处理
      if (this.multiArr && this.multiArr.length > 0) {
        this.multiArr.map((item, index) => {
          if (item.questionAnswer) {
            this.$set(this.multiArr[index], 'answerList', item.questionAnswer.split(''))
          } else {
            this.$set(this.multiArr[index], 'answerList', [])
          }
        })
      }
      console.log('this.multiArr', this.multiArr)
      this.CTFArr = this.questionAllList.filter(q => q.questionType == '4')
      this.AWDArr = this.questionAllList.filter(q => q.questionType == '5')
      this.loopholeArr = this.questionAllList.filter(q => q.questionType == '9') // 漏洞题
      this.otherArr = this.questionAllList.filter(q => q.questionType == '6') // 其他题
      this.fillBlankArr = this.questionAllList.filter(q => q.questionType == '7') // 填空题
      this.shortAnswerArr = this.questionAllList.filter(q => q.questionType == '8') // 简答题
      this.synthesisArr = this.questionAllList.filter(q => q.questionType == '10')
      this.bankTypeList = this.questionAllList.filter(item => { return item.bankType != 1 })
      // 题型序号
      const questionTypes = [
        this.judgeArr.length > 0 ? '判断题' : null,
        this.singleArr.length > 0 ? '单选题' : null,
        this.multiArr.length > 0 ? '多选题' : null,
        this.CTFArr.length > 0 ? 'CTF题' : null,
        this.AWDArr.length > 0 ? 'AWD题' : null,
        this.loopholeArr.length > 0 ? '漏洞题' : null,
        this.fillBlankArr.length > 0 ? '填空题' : null,
        this.shortAnswerArr.length > 0 ? '简答题' : null,
        this.synthesisArr.length > 0 ? '组合题' : null,
        this.otherArr.length > 0 ? '其他题' : null
      ]
      this.numberedQuestionTypes = questionTypes
        .filter(type => type !== null)
        .map((type, index) => ({ number: this.questionLabel[index], type }))

      const judgeItem = this.numberedQuestionTypes.find(item => item.type == this.judgeTitle)
      const singleItem = this.numberedQuestionTypes.find(item => item.type == this.singleTitle)
      const multiItem = this.numberedQuestionTypes.find(item => item.type == this.multiTitle)
      const CTFItem = this.numberedQuestionTypes.find(item => item.type == this.CTFTitle)
      const AWDItem = this.numberedQuestionTypes.find(item => item.type == this.AWDTitle)
      const loopholeItem = this.numberedQuestionTypes.find(item => item.type == this.loopholeTitle)
      const fillBlankItem = this.numberedQuestionTypes.find(item => item.type == this.fillBlankTitle)
      const shortAnswerItem = this.numberedQuestionTypes.find(item => item.type == this.shortAnswerTitle)
      const otherItem = this.numberedQuestionTypes.find(item => item.type == this.otherTitle)
      const synthesisItem = this.numberedQuestionTypes.find(item => item.type == this.synthesisTitle)
      if (judgeItem) this.judgeTitle = `${judgeItem.number}、${judgeItem.type}`
      if (singleItem) this.singleTitle = `${singleItem.number}、${singleItem.type}`
      if (multiItem) this.multiTitle = `${multiItem.number}、${multiItem.type}`
      if (CTFItem) this.CTFTitle = `${CTFItem.number}、${CTFItem.type}`
      if (AWDItem) this.AWDTitle = `${AWDItem.number}、${AWDItem.type}`
      if (loopholeItem) this.loopholeTitle = `${loopholeItem.number}、${loopholeItem.type}`
      if (fillBlankItem) this.fillBlankTitle = `${fillBlankItem.number}、${fillBlankItem.type}`
      if (shortAnswerItem) this.shortAnswerTitle = `${shortAnswerItem.number}、${shortAnswerItem.type}`
      if (otherItem) this.otherTitle = `${otherItem.number}、${otherItem.type}`
      if (synthesisItem) this.synthesisTitle = `${synthesisItem.number}、${synthesisItem.type}`

      // 处理综合题分数和布局
      this.synthesisArr.map((item, index) => {
        if (item.questionType == 10) {
          const combinationPoints = JSON.parse(item.combinationPoints)
          item.combinationQuestionBOS.forEach((sub, index) => {
            sub.questionScore = combinationPoints[index]
              .map(each => Number(each))
              .reduce((p, q) => p + q)
            this.synthesisTotalScore += sub.questionScore
            sub.content = JSON.parse(sub.content).map((val, subIndex) => {
              return {
                contentName: val,
                questionScore: combinationPoints[index][subIndex]
              }
            })
          })
          // 给每个题目标题家序号
          item.questionName = `组合题${index + 1}. ${item.questionName}`
        }
      })
      // 回显已答题目
      setTimeout(() => {
        if (localStorage.getItem('examCenter_finishTempQuestion')) {
          const tempObj = JSON.parse(localStorage.getItem('examCenter_finishTempQuestion'))
          this.currentOrder.synthesis = tempObj.synthesis
          // 定位到最近答题的dom
          const dom = document.getElementById(`${tempObj.currentQuestionDom}`)
          dom && dom.scrollIntoView({ behavior: 'smooth' })
          const tempQuestionArr = ['judgeAnswerList', 'singleAnswerList', 'multiAnswerList', 'CTFAnswerList', 'AWDAnswerList', 'loopholeAnswerList', 'otherAnswerList', 'fillBlankAnswerList', 'shortAnswerAnswerList', 'synthesisAnswerList']
          for (let i = 0; i < tempQuestionArr.length; i++) {
            for (const key in tempObj[tempQuestionArr[i]]) {
              if (tempObj[tempQuestionArr[i]].hasOwnProperty(key)) {
                this.$set(this[tempQuestionArr[i]], key, tempObj[tempQuestionArr[i]][key])
              }
            }
          }
        }
        const index = this.$route.params.index
        const type = this.$route.params.type
        if (type != '') {
          this.handleOrderClick(index + 1, type)
        }
      }, 500)
    },
    handleOrderClick(index, type) {
      this.activeName = type
      const el = document.getElementById(`qt_${type}${index - 1}`)
      this.$nextTick(() => {
        el && el.scrollIntoView({
          behavior: 'smooth'
        })
      })
    },
    // 下载附件
    downloadReport(item) {
      if (item.fileUrl) {
        fetch(item.fileUrl, {
          method: 'get',
          responseType: 'blob'
        })
          .then((response) => response.blob())
          .then((blob) => {
            const a = document.createElement('a')
            const URL = window.URL || window.webkitURL
            const href = URL.createObjectURL(blob)
            a.href = href
            a.download = item.fileName
            document.body.appendChild(a)
            a.click()
            document.body.removeChild(a)
            URL.revokeObjectURL(href)
          })
      }
    },
    // 计算尚未作答题目数
    countEmptyQuestion(...arrays) {
      let totalCount = 0
      arrays.forEach(item => {
        let count = 0
        for (let i = 0; i < item.length; i++) {
          // 多选题目情况
          if (Array.isArray(item[i]) && item[i].length === 0) {
            count++
          }
          if (!item[i]) {
            count++
          }
        }
        totalCount += count
      })
      return totalCount
    },
    handlePicture(event) {
      if (event.target.src) {
        const url = event.target.src
        const image = new Image()
        image.src = url
        image.onload = () => {
        // 创建弹出层
          const previewContatiner = document.createElement('div')
          previewContatiner.style.position = 'fixed'
          previewContatiner.style.top = 0
          previewContatiner.style.bottom = 0
          previewContatiner.style.left = 0
          previewContatiner.style.right = 0
          previewContatiner.style.zIndex = 9999
          previewContatiner.style.backgroundColor = 'rgba(0,0,0,0.8)'
          previewContatiner.style.display = 'flex'
          previewContatiner.style.justifyContent = 'center'
          previewContatiner.style.alignItems = 'center'
          document.body.appendChild(previewContatiner)
          // 在弹出层增加图片
          const previewImage = document.createElement('img')
          previewImage.src = url
          previewImage.style.maxWidth = '80%'
          previewImage.style.maxHeight = '80%'
          previewImage.style.height = '60%'
          previewImage.style.zIndex = 9999
          previewContatiner.appendChild(previewImage)
          // 点击弹出层，关闭预览
          previewContatiner.addEventListener('click', () => {
            document.body.removeChild(previewContatiner)
          })
        }
        image.onerror = function() {
          console.log('图片加载失败')
        }
      }
    },
    // 点击“上传文件”按钮
    handleUploadClick(type, index) {
      this.$refs[`inputFile${type}${index}`][0].click()
      this.$refs[`inputFile${type}${index}`][0].value = ''
    },
    // 删除文件
    handleFileDelete(type, index) {
      this[`${type}Arr`][index]['studentFileName'] = ''
      this[`${type}Arr`][index]['studentFileUrl'] = ''
      this.$forceUpdate()
    }
  }
}
</script>
<style lang="scss" scoped>
.paper_container {
  width: 100%;
  height: 100%;
  display: flex;
  .paper_left {
    flex: 1;
    overflow: hidden;
    height: 100%;
    position: relative;
    display: flex;
    flex-direction: column;
    .student_info {
      position: relative;
      z-index: 1000;
      color: #268CFC;
      background: #fff;
      font-size: 14px;
      font-weight: 600;
      font-family: Source Han Sans CN;
      display: flex;
      justify-content: flex-start;
      >div {
        margin-right: 50px;
      }
    }
    .question_details {
      flex: 1;
      overflow: hidden;
      display: flex;
      justify-content: flex-start;
      .question_list {
        .question_tabs {
          flex: 1;
          min-height: 0;
          /deep/ .el-tabs__header {
            padding: 0 24px;
            margin-bottom: 0px;
          }
          /deep/ .el-tabs__content {
            height: calc(100% - 40px);
          }
          /deep/ .el-tab-pane {
            height: 100%;
          }
          .detail-tabs-content {
            height: 100%;
            overflow-x: hidden;
            overflow-y: auto;
            padding: 15px;
          }

          ._question_list {
            height: 100%;
            overflow-x: hidden;
            overflow-y: auto;
            padding: 15px;
          }
        }
        /deep/.question_tabs .el-tabs__item {
          font-size: 14px;
          font-weight: 600;
        }
        ::v-deep {
          .el-input__inner {
            border-radius: 4px;
          }
        }
        width: 100%;
        height: 100%;
        // overflow-y: auto;
        // margin-right: 20px;
        // margin-top: 15px;
        .question_types {
          font-size: 16px;
          font-weight: 600;
          height: 35px;
          font-family: Source Han Sans CN;
        }
        .name_question {
            font-weight: 700;
            font-size: 15px;
            margin-bottom: 10px;
          }
        .flag_info {
          margin-left: 20px;
          font-size: 14px;
          color: #e45d5d;
          font-weight: 600;
          margin-top: 5px;
        }
        .question_info {
          margin: 0 0 15px 0px;
          padding: 15px;
          background: #ffffff;
          border-radius: 4px;
          border: 1px solid #e5e6eb;
          ::v-deep .question_item_content {
            overflow-x: auto;
            margin: 0 5px 5px 0;
            font-weight: 600;
            font-size: 14px;
            color: rgb(36, 41, 47);
            .rich-text {
              display: flex;
              justify-content: flex-start;
              span p {
                display: inline !important;
              }
            }
            .el-image {
              margin: 10px 0 0 16px;
            }
          }
          .question_option {
            padding-top: 8px;
            font-size: 14px;
            box-sizing: border-box;
            margin-left: 15px;
            display: flex;
            flex-direction: column;
            ::v-deep .el-radio {
              margin-bottom: 8px;
              display: flex;
              align-items: flex-start;
              .el-radio__label {
                font-size: 14px;
                color: #4e5969;
                flex: 1;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
              }
            }
            ::v-deep .el-checkbox {
              margin-bottom: 8px;
              display: flex;
              align-items: flex-start;
              .el-checkbox__label {
                font-size: 14px;
                color: #4e5969;
                flex: 1;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
              }
            }
          }
          .error-info {
            font-size: 12px;
            color: #e45d5d;
            font-weight: 600;
            margin: 10px 0 0 40px;
          }
          .comp-box {
            border: 1px solid #e5e6eb;
            padding: 10px 15px;
            border-radius: 4px;
          }
          .comp-question {
            display: flex;
            max-height: 200px;
            overflow-y: auto;
            font-weight: 600;
            font-size: 14px;
            color: rgb(36, 41, 47);
            margin-bottom: 10px;
            >span {
              word-break: break-all;
            }
          }
          .comp-content-wrap {
            border: 1px solid #e5e6eb;
            margin: 15px 0 5px;
            padding: 10px 20px;
            border-radius: 4px;
            >div:first-child {
              display: flex;
              max-height: 200px;
              overflow-y: auto;
              margin: 5px 0;
              >span {
                word-break: break-all;
              }
            }
          }
        }
      }
    }
  }

  .paper_right {
    width: 318px;
    height: 100%;
    color: #000;
    font-family: Source Han Sans CN;

    .question_sn {
      margin-left: 15px;
      // padding: 15px 0px;
      flex: 1;
      overflow: hidden;
      border: 1px solid #ebebeb;

      .paper_info_title {
        font-size: 16px;
        font-weight: 600;
        margin: 0 30px;
        padding: 10px 0 ;
        display: flex;
        align-items: center;
        justify-content: space-between;
      }

      .paper_content {
        border-top: 1px solid #ebebeb;
        border-bottom: 1px solid #ebebeb;
        margin: 0 0 25px 0;
        padding: 15px 25px;
        flex: 1;
        overflow-y: auto;
        .question_type {
          font-size: 14px;
          font-weight: 600;
        }
        .question_sn_order {
          margin-top: 10px;
          display: flex;
          flex-wrap: wrap;

          .question_sn_order_item {
            width: 32px;
            height: 32px;
            background: #ffffff;
            opacity: 1;
            border: 1px solid #D3D3D3;
            font-size: 14px;
            margin: 8px;
            line-height: 32px;
            text-align: center;
            border-radius: 4px;
            cursor: pointer;
          }

          .question_sn_order_item:hover {
            background: #D3D3D3;
            color: var(--color-600);
          }

          .question_sn_order_item_check {
            background: var(--color-600);
            color: white;
            border: none;
          }
        }
      }

      .footer {
        text-align: center;
        margin: 0 30px;
        padding: 0 0 10px 0;
        position: relative;
        .countdown {
          height: 30px;
          font-size: 16px;
          font-weight: 600;
          &--hidden {
            display: none;
          }
        }
        .finish {
          background: var(--color-600);
          width: 32px;
          height: 32px;
          border-radius: 4px;
        }

        .not_done {
          background: transparent;
          width: 32px;
          height: 32px;
          border-radius: 4px;
          border: 1px solid #D3D3D3;
        }

        .confirm {
          width: 80px;
          height: 32px;
          font-size: 14px;
          text-align: center;
          border: none;
          background-color: var(--color-600);
          color: #ffffff;
          padding: 0;
          margin-top: 20px;
        }
      }
    }

  }
}

.env_btn {
  width: 90px;
  min-width: 90px;
  height: 28px;
  line-height: 28px;
  font-size: 13px;
  text-align: center;
  border: none;
  background-color: var(--color-600);
  color: #ffffff;
  padding: 0;
}
.end-div {
  background: #ffffff;
  margin-left: 10px;
  border-radius: 2px;
  padding: 23px;
  .end-div-bk-div {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-around;
    height: 415px;
    padding-top: 42px;
    margin-top: 32px;
    ::v-deep {
      .el-button {
        width: 120px;
        height: 36px;
        background: var(--color-600);
        font-size: 14px;
        font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
        font-weight: 400;
        color: #ffffff;
      }
      .el-button:hover {
        background: #0e42d2;
      }
    }
  }
  .end-name {
    font-size: 24px;
    font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
    font-weight: 400;
    color: #1d2129;
  }
  .time {
    font-size: 14px;
    font-family: SourceHanSansCN-Regular, SourceHanSansCN;
    font-weight: 400;
    color: #666666;
  }
  .number-of_topics {
    height: 72px;
    background: #f7f8fa;
    border-radius: 8px;
    display: flex;
    align-items: center;
    padding: 0 24px;
    justify-content: space-between;
    div {
      display: flex;
      flex-direction: column;
      align-items: center;
      font-size: 12px;
      font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
      font-weight: 400;
      color: #86909c;
    }
    .number-of_topics_font {
      font-size: 16px;
      font-weight: 500;
      color: #1d2129;
      margin-bottom: 7px;
    }
  }
  .tips {
    font-size: 14px;
    color: #ddb757;
    font-family: SourceHanSansCN-Regular, SourceHanSansCN;
    font-weight: 400;
  }
  .time-stage-div {
    display: flex;
    align-items: center;
    .time-stage {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-right: 12px;
      .division {
        margin: 0 12px;
        color: #4e5969;
      }
      .time-num {
        font-size: 14px;
        font-family: HarmonyOS Sans SC-Medium, HarmonyOS Sans SC;
        font-weight: 500;
        color: #4e5969;
      }
    }
  }
}
::v-deep {
  .el-tabs__nav-wrap::after {
    height: 1px;
  }
  .el-tabs__content {
    height: 655px;
    overflow-y: auto;
  }
}
.limit-time {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  .title {
    font-size: 22px;
    color: #000;
  }
  .time {
    border: 1px solid #ccc;
    padding: 15px 50px;
    border-radius: 2px;
    font-size: 14px;
    color: #000;
    margin: 40px 0;
    font-weight: 600;
  }
  .start-button {
    width: 100px;
    height: 40px;
    border-radius: 4px;
    font-size: 14px;
    color: #ffffff;
    border: none;
  }
  .exam-tip {
    color: var(--alert-500);
    margin-top: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
.upload-file {
  display: flex;
  justify-content: flex-start;
  .title {
    line-height: 32px;
  }
  .tpl-upload {
    .upload-select-input {
      display: none;
    }
    .upload-click-wrap {
      display: inline-block;
    }
    .file-container {
      min-width: 200px;
      overflow-wrap: break-word;
      word-break: normal;
      line-height: 1.5;
      border: 1px solid;
      margin-top: 5px;
      padding: 6px 10px;
      overflow: hidden;
      text-overflow:ellipsis;
      white-space: nowrap;
      cursor: pointer;
      position: relative;
      .delete {
        position: absolute;
        top: 10px;
        right: 5px;
      }
    }
  }
}
</style>
