<template>
  <div v-loading="loading" class="dialog-wrap">
    <el-form ref="form" :model="formData" :rules="rules" label-position="left" label-width="120px">
      <el-form-item label="是否自动释放" prop="isAutoRelease">
        <el-switch v-model="formData.isAutoRelease" />
      </el-form-item>
      <el-form-item label="资源生命周期" prop="defaultReleaseLatency">
        <div class="flex">
          <el-input-number :min="0" :precision="0" :step="1" v-model="formData.defaultReleaseLatency" style="margin: 4px 10px 0 0;" />
          <el-select v-model="formData.releaseUnit" placeholder="请选择单位" style="width: 120px;">
            <el-option
              v-for="item in unitOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
      </el-form-item>
    </el-form>
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>
<script>
import { resourceConfigApi } from '@/api/admin/systemSettings'
import validate from '@/packages/validate'
export default {
  props: {
    // 传入数据
    data: {
      type: Object,
      default: () => null
    }
  },
  data() {
    var validateDefaultReleaseLatency = (rule, value, callback) => {
      if (value) {
        if (this.formData.releaseUnit == 'hour') {
          if (value > 24000) {
            callback(new Error('不能大于24000小时'))
          } else {
            callback()
          }
        } else if (this.formData.releaseUnit == 'day') {
          if (value > 1000) {
            callback(new Error('不能大于1000天'))
          } else {
            callback()
          }
        } else if (this.formData.releaseUnit == 'minute') {
          if (value > 1440000) {
            callback(new Error('不能大于1440000分钟'))
          } else {
            callback()
          }
        }
      } else {
        callback(new Error('必填项'))
      }
    }
    return {
      loading: false,
      validate: validate,
      formData: {
        isAutoRelease: true,
        defaultReleaseLatency: 0,
        releaseUnit: 'hour' // 默认单位为小时
      },
      unitOptions: [
        { label: '天', value: 'day', translateToMinutes: 1440 },
        { label: '小时', value: 'hour', translateToMinutes: 60 },
        { label: '分钟', value: 'minute', translateToMinutes: 1 }
      ],
      rules: {
        isAutoRelease: [
          validate.required('change')
        ],
        defaultReleaseLatency: [
          validate.required('change'), validate.number_integer, { validator: validateDefaultReleaseLatency, trigger: 'blur' }
        ]
      },
      releaseTranslate: null,
      releaseTransLabel: null
    }
  },
  created() {
    if (this.data) {
      this.formData = JSON.parse(JSON.stringify(this.data))
      // 处理释放间隔时间配置
      this.releaseTranslate = this.unitOptions.find(item => item.value === this.formData.releaseUnit).translateToMinutes
      this.releaseTransLabel = this.unitOptions.find(item => item.value === this.formData.releaseUnit).label
    }
  },
  methods: {
    close() {
      this.$emit('close')
    },
    confirm() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.loading = true
          this.releaseTranslate = this.unitOptions.find(item => item.value === this.formData.releaseUnit).translateToMinutes
          this.$set(this.formData, 'defaultReleaseLatency', Number(this.formData.defaultReleaseLatency) * this.releaseTranslate)
          const postData = new FormData()
          postData.append('value', JSON.stringify(this.formData))
          resourceConfigApi(postData).then(() => {
            this.$message.success('编辑成功')
            this.$emit('call', 'refresh')
            this.close()
          }).finally(() => {
            this.loading = false
          })
        }
      })
    }
  }
}
</script>
