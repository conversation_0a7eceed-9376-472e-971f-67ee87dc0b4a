<template>
  <div class="resource-table">
    <!-- 操作区 -->
    <div class="operation-wrap">
      <div class="operation-left">
        <slot name="action" />
        <el-button type="primary" icon="el-icon-refresh" @click="refresh" />
      </div>
      <div class="operation-right">
        <el-badge :value="searchBtnShowNum">
          <el-button icon="el-icon-search" @click="openSearch">搜索</el-button>
        </el-badge>
        <!-- 自定义表格列 -->
        <t-table-config
          :data="columnsObj"
          :active-key-arr="columnsViewArr"
          @on-change-col="onChangeCol"
        />
      </div>
    </div>
    <!-- 搜索区 -->
    <t-search-box
      v-show="searchView"
      :search-key-list="searchKeyListView"
      :default-placeholder="'默认搜索项目名称'"
      @search="searchMultiple"
    />
    <!-- 列表 -->
    <t-table-view
      ref="tableView"
      :height="height"
      :single="true"
      :loading="tableLoading"
      :data="tableData"
      :total="tableTotal"
      :page-size="pageSize"
      :current="pageCurrent"
      :select-item="selectItem"
      current-key="id"
      @on-select="onSelect"
      @on-current="onCurrent"
      @on-change="changePage"
      @on-sort-change="onSortChange"
      @on-page-size-change="onPageSizeChange"
    >
      <el-table-column
        v-for="item in columnsViewArr"
        :key="item"
        :min-width="columnsObj[item].colMinWidth || 100"
        :width="columnsObj[item].colWidth"
        :label="columnsObj[item].title"
        :fixed="columnsObj[item].master ? 'left' : false"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span v-if="item === 'projectStatus'">
            <el-badge :type="module.testStatusStrArr[scope.row[item]] && module.testStatusStrArr[scope.row[item]].type" is-dot />
            {{ module.testStatusStrArr[scope.row[item]] && module.testStatusStrArr[scope.row[item]].label }}
          </span>
          <span v-else-if="item == 'projectName'">
            <a
              v-if="scope.row[item]"
              :href="`/testing/testing/detail/${scope.row.id}/overview?routeSearch=true&searchVal=${scope.row.projectName}&searchKey=name&moduleName=testingTableItems`"
              @click.prevent="jumpToProject(scope.row)"
            >
              {{ scope.row[item] }}
            </a>
            <span v-else>-</span>
          </span>
          <span v-else>{{ scope.row[item] || '-' }}</span>
        </template>
      </el-table-column>
    </t-table-view>
  </div>
</template>
<script>
import tSearchBox from '@/packages/search-box/index.vue'
import mixinsPageTable from '@/packages/mixins/page_table'
import tTableView from '@/packages/table-view/index.vue'
import tTableConfig from '@/packages/table-config/table-col-config.vue'
import tableTdMultiCol from '@/packages/table-config/table-td-multi-col.vue'
import module from './config.js'
import { getProblemProjectPage } from '@/api/testing/index.js'

// 引入封装的方法
export default {
  components: {
    tSearchBox,
    tTableView,
    tTableConfig,
    tableTdMultiCol
  },
  mixins: [mixinsPageTable],
  data() {
    return {
      module,
      moduleName: module.name,
      searchKeyList: [
        { key: 'projectName', label: '项目名称', master: true, placeholder: '请输入' },
        { key: 'testProduct', label: '检测产品', placeholder: '请输入' },
        { key: 'versionNumber', label: '版本号', placeholder: '请输入' },
        { key: 'statusList', label: '状态', type: 'select', placeholder: '请选择', valueList: module.searchStatusArr },
        { key: 'vendorName', label: '厂商名称', placeholder: '请输入' }
      ],
      columnsObj: {
        'projectName': {
          title: '项目名称', master: true
        },
        'testProduct': {
          title: '检测产品'
        },
        'versionNumber': {
          title: '版本号'
        },
        'projectStatus': {
          title: '状态'
        },
        'vendorName': {
          title: '厂商名称'
        }
      },
      columnsViewArr: [
        'projectName',
        'testProduct',
        'versionNumber',
        'projectStatus',
        'vendorName'
      ]
    }
  },
  methods: {
    // 跳转到检测项目
    jumpToProject(row) {
      window.open(`/testing/testing/detail/${row.id}/overview?routeSearch=true&searchVal=${row.projectName}&searchKey=name&moduleName=testingTableItems`)
    },
    getList: function(showLoading = true) {
      if (showLoading) {
        this.tableLoading = true
      }
      const params = this.getPostData('page', 'limit')
      getProblemProjectPage(params).then((res) => {
        const data = { ...res.data.data }
        this.tableData = data ? data.records : []
        this.tableTotal = Number(data.total) || 0
        this.tableLoading = false
        this.handleSelection()
      }).catch(() => {
        this.tableLoading = false
      })
    }
  }
}
</script>
