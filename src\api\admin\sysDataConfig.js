import request from '@/utils/request'

export function sysDataConfigList(data) {
  return request({
    url: '/admin/sysDataConfig/queryPage',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export function sysDataConfigCreate(data) {
  return request({
    url: '/admin/sysDataConfig/create',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export function sysDataConfigUpdate(data) {
  return request({
    url: '/admin/sysDataConfig/update',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export function sysDataConfigRemove(data) {
  return request({
    url: '/admin/sysDataConfig/remove',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
