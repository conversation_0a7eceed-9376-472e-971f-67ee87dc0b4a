<template>
  <div class="content-wrap-layout">
    <div v-loading="loading" class="system-setting-wrap">
      <el-divider content-position="left">
        <span style="font-size: 16px;">资源管理配置</span>
        <a href="javascript:;" @click="openEditModal('resourceConfig')">
          <i class="el-icon-edit-outline" />
        </a>
      </el-divider>
      <el-form ref="form" :model="formData" label-position="left" label-width="120px">
        <el-form-item label="是否自动释放" prop="isAutoRelease">
          <el-badge :type="formData.isAutoRelease ? 'success' : 'danger'" is-dot />{{ formData.isAutoRelease ? '是' : '否' }}
        </el-form-item>
        <el-form-item label="资源生命周期" prop="defaultReleaseLatency">
          <span>{{ translateReleaseLatency }}</span>
        </el-form-item>
      </el-form>
    </div>
    <!-- 中部弹窗 start-->
    <el-dialog
      :title="titleMapping[modalName]"
      :visible.sync="modalShow"
      :width="modalWidth"
      @close="modalClose"
    >
      <transition name="el-fade-in-linear">
        <component
          :is="modalName"
          :name="modalName"
          :data="formData"
          @close="modalClose"
          @call="confirmCall"
        />
      </transition>
    </el-dialog>
    <!-- 中部弹窗 end-->
  </div>
</template>

<script>
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
import resourceConfig from './form/resource-config'
import { queryFirstConfigByName } from '@/api/admin/systemSettings'
export default {
  components: {
    resourceConfig
  },
  mixins: [mixinsActionMenu],
  data() {
    return {
      loading: false,
      formData: {
        isAutoRelease: true,
        defaultReleaseLatency: 0,
        releaseUnit: 'hour' // 默认单位为小时
      },
      unitOptions: [
        { label: '天', value: 'day', translateToMinutes: 1440 },
        { label: '小时', value: 'hour', translateToMinutes: 60 },
        { label: '分钟', value: 'minute', translateToMinutes: 1 }
      ],
      titleMapping: {
        'resourceConfig': '资源管理配置'
      },
      releaseTranslate: null,
      releaseTransLabel: null
    }
  },
  computed: {
    // 转化资源生命周期
    translateReleaseLatency() {
      let releaseTimeStr = ''
      releaseTimeStr = `${this.formData.defaultReleaseLatency} ${this.releaseTransLabel}`
      return releaseTimeStr
    }
  },
  created() {
    this.loadBase()
  },
  methods: {
    // 获取资源配置
    'loadBase': function(showLoading = true) {
      if (showLoading) {
        this.loading = true
      }
      queryFirstConfigByName('automaticSceneRelease').then(res => {
        this.formData = JSON.parse(res.data.value)
        // 处理释放间隔时间配置
        this.releaseTranslate = this.unitOptions.find(item => item.value === this.formData.releaseUnit).translateToMinutes
        this.releaseTransLabel = this.unitOptions.find(item => item.value === this.formData.releaseUnit).label
        const releaseValue = Number(this.formData.defaultReleaseLatency) / this.releaseTranslate
        this.$set(this.formData, 'defaultReleaseLatency', Number.isInteger(releaseValue) ? releaseValue : releaseValue.toFixed(2))
      }).finally(() => {
        this.loading = false
      })
    },
    'confirmCall': function(type, data) {
      if (type === 'close') {
        this.modalClose()
      } else if (type === 'refresh') {
        this.loadBase(false)
      }
    },
    // 打开弹窗
    'openEditModal': function(name) {
      this.modalName = name
    }
  }
}
</script>

<style lang="scss" scoped>
.system-setting-wrap {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
  /deep/ .el-form {
    margin-left: 32px;
    padding-bottom: 20px;
    .el-form-item {
      margin-bottom: 0;
      .el-form-item__label:after {
        display: none;
      }
    }
  }
  /deep/ .el-divider--horizontal {
    margin: 15px 0;
    .el-divider__text.is-left {
      left: 0;
      padding-left: 0px;
    }
  }
}
</style>
