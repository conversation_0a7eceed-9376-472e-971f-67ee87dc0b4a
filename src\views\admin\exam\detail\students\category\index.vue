<template>
  <div class="category-wrap">
    <transverse-list
      :data="examStatusArr"
      :allow-deletion="false"
      :allow-add="false"
      :all="true"
      v-bind="categoryProps"
      title="参考状态"
      @node-click="handleNodeClick($event, 'examStatus')"
    />
  </div>
</template>
<script>
import transverseList from '@/packages/transverse-list/index.vue'
import module from '../config.js'

export default {
  components: {
    transverseList
  },
  props: {
    examStatus: [String, Number]
  },
  data() {
    return {
      moduleName: module.name,
      examStatusArr: module.examStatus,
      categoryProps: {
        label: 'label',
        idName: 'value'
      },
      categoryQuery: {
        examStatus: this.examStatus
      }
    }
  },
  mounted() {
  },
  methods: {
    handleNodeClick(item, key) {
      this.categoryQuery[key] = item.value
      this.$emit('category-query', this.categoryQuery)
    }
  }
}
</script>
<style lang="scss" scoped>
.category-wrap {
  border-bottom: 1px solid var(--neutral-300) !important;
}
::v-deep {
  .transverse-list {
    border-bottom: 0px solid var(--neutral-300) !important;
    display: flex;
    justify-content: space-between;
    padding: 6px 16px;
    width: 100%;
    font-size: 16px;
    .transverse-list-operate {
      display: none !important;
    }
  }
}
.transverse-border {
  border-bottom: 1px solid var(--neutral-300) !important;
}

.transverse-list {
  .transverse-list-title {
    white-space: nowrap;
    font-size: 13px;
    font-family: Microsoft YaHei;
    font-weight: 400;
    color: #999999;
    margin-top: 10px;
  }
  .transverse-list-content {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    flex: 1;
    min-width: 0;
    position: relative;
    padding-top: 5px;
    .list-content {
      height: 30px;
      padding: 0 11px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      font-family: Microsoft YaHei;
      font-weight: 400;
      font-size: 13px;
      color: #5b5b5b;
      margin-bottom: 2px;
      margin-right: 10px;
    }

    .list-content:hover {
      background: #eaeeff;
      border-radius: 4px;
      color: #3152ef;
    }
    .content-selected {
      background: #eaeeff;
      color: #3152ef;
      font-size: 13px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      border-radius: 4px;
    }
    .type-item {
      position: relative;
      border-radius: 5px;
      z-index: 0;
    }
    .type-item:hover {
      background: rgba(35, 98, 251, 0.1);
    }
  }
}
</style>
