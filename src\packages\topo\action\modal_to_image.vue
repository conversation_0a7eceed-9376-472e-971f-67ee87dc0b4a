<template>
  <div v-loading="loading" class="dialog-wrap">
    <el-alert type="warning">
      <div slot="title">
        1.将云主机的系统盘通过克隆的方式另存为新的系统镜像，格式为RAW。<br>
        2.为了保证数据的完整性，建议在关机情况下执行生成镜像的操作。
      </div>
    </el-alert>
    <el-form ref="form" :model="formData" :rules="rules" label-width="80px" @submit.native.prevent>
      <el-form-item label="镜像名称" prop="image_name">
        <el-input v-model.trim="formData.image_name"/>
      </el-form-item>
    </el-form>
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>

<script>
import { toImage } from '../api/orchestration'
import modalMixins from '../../mixins/modal_form'
import validate from '../../validate'
export default {
  mixins: [modalMixins],
  props: {
    // 传入数据
    data: [Object, Array]
  },
  data() {
    return {
      loading: false,
      nodeId: '',
      formData: {
        'image_name': ''
      },
      rules: {
        'image_name': [
          validate.required(),
          validate.base_name
        ]
      }
    }
  },
  created() {
    if (Array.isArray(this.data)) {
      this.nodeId = this.data[0].id
    } else this.nodeId = this.data.node_id
  },
  methods: {
    // modal点击确定
    'confirm': function() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.loading = true
          toImage(this.nodeId, this.formData).then(res => {})
          this.close()
        }
      })
    },
    close() {
      this.$emit('close')
    }
  }
}
</script>
