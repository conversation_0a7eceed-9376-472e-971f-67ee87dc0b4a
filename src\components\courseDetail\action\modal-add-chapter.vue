<template>
  <div v-loading="loading" class="dialog-wrap">
    <div class="dialog-wrap-content">
      <el-form ref="form" :model="formData" :rules="rules" label-position="left" label-width="90px" @submit.native.prevent>
        <el-form-item label="章节名称:" prop="name">
          <el-input v-model.trim="formData.name" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
    </div>
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>
<script>
import validate from '@/packages/validate'
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
import { addChapterApi } from '@/api/teacher/index.js'
export default {
  components: {
  },
  mixins: [mixinsActionMenu],
  props: {
    courseId: {
      type: String,
      default: ''
    },
    // 新增的下标
    addIndex: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      loading: false,
      validate: validate,
      formData: {
        name: ''
      },
      rules: {
        name: [validate.required()]
      },
      id: 1
    }
  },
  methods: {
    close: function() {
      this.$emit('close')
    },
    confirm: function() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.loading = true
          const params = {
            name: this.formData.name,
            type: 0,
            parentId: 0,
            sort: this.addIndex,
            pjtCourseId: this.courseId
          }
          addChapterApi(params).then((res) => {
            if (res.code === 0) {
              this.$message.success('章节添加成功')
              this.$emit('call', 'refresh')
              this.close()
            }
          }).catch(() => {
            this.loading = false
          })
        } else {
          return false
        }
      })
    }
  }
}
</script>


