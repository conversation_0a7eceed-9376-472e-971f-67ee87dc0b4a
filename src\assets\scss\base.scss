html,body,#app{
	width: 100%;
	height: 100%;
	overflow: hidden;
}


.overflow{
	overflow-x: hidden;
	overflow-y: auto;
	scrollbar-color: transparent transparent;
	scrollbar-track-color: transparent;
	-mz-scrollbar-track-color: transparent;
	scrollbar-color:transparent transparent;
	scrollbar-track-color: transparent;
	-mz-scrollbar-track-color:transparent;
	-ms-scrollbar-track-color:transparent;
	-ms-overflow-style: none;
	-ms-content-zooming: zoom;
	-ms-scroll-rails: none;
	-ms-content-zoom-limit-min: 100%;
	-ms-content-zoom-limit-max: 500%;
	-ms-scroll-snap-type: proximity;
	-ms-scroll-snap-points-x: snapList(100%, 200%, 300%, 400%, 500%);
	-ms-overflow-style: none;
}
.ffox{
	scrollbar-color:  #D9D9D9 #000;  /* 第一个方块颜色，第二个轨道颜色(用于更改火狐浏览器样式) */
	scrollbar-width: thin;  /* 火狐滚动条无法自定义宽度，只能通过此属性使滚动条宽度变细 */
	-ms-overflow-style:none;  /* 隐藏滚动条（在IE和Edge两个浏览器中很难更改样式，固采取隐藏方式） */
}
@import '../../assets/scss/mixin.scss';
.contextM{
	height: auto;
	position: absolute;
	background-color: #FFFFFF;
	// border: 1px solid #b7c8f6;
	display: none;
	// @include paddingBoxSizing(10px 0);
	ul{
		width: 100%;
		height: 100%;
    margin: 0;
    padding: 0;
    box-shadow: 0px 0px 16px 0px rgba(49, 130, 223, 0.22);
    border-radius: 4px;
    position: relative;
    &:after{
        position: absolute;
        content: '';
        left: 59px;
        top: -9px;
        border-bottom: 9px solid #FFFFFF;
        //border-left和border-right换成透明色 才能形成三角形 不然是长方形
        border-left: 9px solid transparent;
        border-right: 9px solid transparent;
        //background-color: red;
    }
		li{
      list-style: none;
			cursor: pointer;
			@include paddingBoxSizing(0 0 0 5px);
			width: 100%;
			font-size: 16px;
			height: 30px;
      line-height: 30px;
			text-align: center;
			// border-bottom: 1px dotted #cccccc;
			color: #7d7d7d;
			&:hover{
				background-color: rgba(62, 157, 255, 0.2) !important;
			}
		}
	}
}
