/*  最大宽度 */
$maxWidth: 1200px;

/*  常规字体大小 */
$fontA: 12px;
$fontB: 14px;
$fontC: 16px;
$fontD: 18px;
$fontE: 20px;
$fontF: 22px;
$fontG: 24px;
$fontH: 26px;
$fontI: 28px;
$fontJ: 30px;
$fontK: 32px;
$fontL: 36px;
$fontM: 40px;

/*  常规配色 */
$colorMain: #165DFF; //主色调
$colorBtn: #165DFF; //按钮
$colorFont: #5FB41B;
$colorCourse: #EA4335;
$colorDanger: #EA4335;
$colorWhite: #fff;
$colorBlack: #1D2129;
$colorText: #4E5969;
$colorGrey: #86909C;
$color666: #666;
$color999: #999;
$colorBg: #f8f8f8;
$colorMenuHover: #165DFF; //菜单hover

#__nuxt,
#__layout {
  width: 100%;
  height: 100%;

  >div {
    width: 100%;
    height: 100%;
  }
}

div {
  box-sizing: border-box;
}

ul,
li {
  list-style: none;
}

p {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.h-100 {
  height: 100%;
}

.h-0 {
  height: 0;
}

.empty-page {
  height: calc(100vh - 150px);
}

/*  字体颜色 */
.color-main {
  color: $colorMain !important;
}

.color-white {
  color: $colorWhite !important;
}

.color-black {
  color: $colorBlack !important;
}

.color-danger {
  color: $colorDanger !important;
}

.color-text {
  color: $colorText !important;
}

.color-grey {
  color: $colorGrey !important;
}

.color-666 {
  color: $color666 !important;
}

.color-999 {
  color: $color999 !important;
}


/*  遮罩层 */
.v-modal {
  background: #1A1B2B !important;
  opacity: 0.15 !important;
}

/*  字体加粗 */
.font-bold {
  font-weight: bold;
}

/*  hover手势 */
.cursor {
  cursor: pointer
}

/*  hover下划线 */
.hover-line:hover {
  text-decoration: underline;
}


/*  hover字体颜色 */

.hover-color {
  transition: all .3s;

  &:hover {
    color: $colorMain;
  }
}

/*  hover字体颜色 */
// .hover-color:hover {

// }

/*  Flex布局相关公共样式 */
.flex {
  display: flex;
}

.flex-center {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}

.flex-space-between {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}

.flex-space-around {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content:space-around;
}

.flex-center-col {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.flex-left {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;

}

.flex-right {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;

}

.flex-top {
  display: flex;
  align-items: flex-start;

}

.flex-row {
  display: flex;
  flex-direction: row;
}

.flex-col {
  display: flex;
  flex-direction: column;
}

.flex-1 {
  flex: 1;
}

.flex-wrap {
  flex-wrap: wrap;
}

.jc-start {
  justify-content: flex-start;
}

.jc-end {
  justify-content: flex-end;
}

.jc-center {
  justify-content: center;
}

.jc-around {
  justify-content: space-around;
}

.jc-between {
  justify-content: space-between;
}

.ai-start {
  align-items: flex-start;
}

.ai-end {
  align-items: flex-end;
}

.ai-center {
  align-items: center;
}




/*  网格布局相关公共样式 */
.grid {
  display: grid;
}

$grid-gap: (
  5,
  8,
  10,
  15,
  16,
  20,
  25,
  30,
  35,
  40,
  45,
  50
);

@each $gg in $grid-gap {
  .gg-#{$gg} {
    grid-gap:#{$gg}px;
  }
}

$g-t-c: (
  1:1fr,
  2:1fr 1fr,
  3:1fr 1fr 1fr,
  4:1fr 1fr 1fr 1fr,
  5:1fr 1fr 1fr 1fr 1fr,
  6:1fr 1fr 1fr 1fr 1fr 1fr,
); // grid-template-columns

@each $key,
$gtc in $g-t-c {
  .gtc-#{$key} {
    grid-template-columns:#{$gtc} !important
  }
}

/* 文字溢出 */
.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.ellipsis2 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.ellipsis3 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
}

.ellipsis4 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 4;
  overflow: hidden;
}

/* 边距生成：padding/margin */
$spacing-types: (
  m: margin,
  p: padding
);
$spacing-directions: (
  t: top,
  b: bottom,
  l: left,
  r: right
);

$spacing-base-size : 10px; //基数
$spacing-sizes: (
  5: 0.5,
  10: 1,
  15: 1.5,
  20: 2,
  25: 2.5,
  30: 3,
  35: 3.5,
  40: 4,
  45: 4.5,
  50: 5,
  55: 5.5,
  60: 6,
  65: 6.5,
  70: 7,
  75: 7.5,
  80: 8
);

/* 

  循环出 margin 与 padding 的各类值
  取值参考$spacing-sizes中的key
  如：m-5，mx-10，ml-20，p-5，py-10, pr-20等

*/
@each $typeKey,
$type in $spacing-types {

  // m-10 {margin:10px} || p-50 {padding:50px}
  @each $sizeKey,
  $size in $spacing-sizes {
    .#{$typeKey}-#{$sizeKey} {
      #{$type}: $size * $spacing-base-size;
    }
  }

  // mx-10 {margin-left:10px; margin-right:10px} || px-50 {padding-left:50px; padding-right:50px;}
  @each $sizeKey,
  $size in $spacing-sizes {
    .#{$typeKey}x-#{$sizeKey} {
      #{$type}-left: $size * $spacing-base-size;
      #{$type}-right: $size * $spacing-base-size;
    }
  }

  // my-10{margin-top:10px; margin-bottom:10px} || py-50{padding-top:50px; padding-bottom:50px;}
  @each $sizeKey,
  $size in $spacing-sizes {
    .#{$typeKey}y-#{$sizeKey} {
      #{$type}-top: $size * $spacing-base-size;
      #{$type}-bottom: $size * $spacing-base-size;
    }
  }

  // mt-10 { margin-top: 10px } || pb-50{paddding-bottom:50px;}||ml-20{margin-left:20px}||pr-40{padding-right:40px}
  @each $directionsKey,
  $directions in $spacing-directions {

    @each $sizeKey,
    $size in $spacing-sizes {
      .#{$typeKey}#{$directionsKey}-#{$sizeKey} {
        #{$type}-#{$directions}: $size * $spacing-base-size;
      }
    }
  }
}

/* 字体大小 */
$fontsize: (
  12:$fontA,
  14:$fontB,
  16:$fontC,
  18:$fontD,
  20:$fontE,
  22:$fontF,
  24:$fontG,
  26:$fontH,
  28:$fontI,
  30:$fontJ,
  32:$fontK,
  36:$fontL,
  40:$fontM
);

@each $key,
$size in $fontsize {
  .font-#{$key} {
    font-size: #{$size};
  }
}

/* 12px缩小为10px */
.font-scale {
  display: inline-block;
  transform: scale(0.83)
}

/* 圆角 */
$radiusList: 3, 4, 6, 9, 12;

@each $i in $radiusList {
  .radius#{$i} {
    border-radius: #{$i}px;
    overflow: hidden;
  }
}

.circle {
  border-radius: 50%;
  overflow: hidden;
}

/* 字重 */
$fontWeight: (
  100,
  200,
  300,
  400,
  500,
  600,
  700,
  800,
  900,
  bold,
  bolder,
  lighter
);

@each $var in $fontWeight {
  .f-w-#{$var} {
    font-weight: $var;
  }
}
