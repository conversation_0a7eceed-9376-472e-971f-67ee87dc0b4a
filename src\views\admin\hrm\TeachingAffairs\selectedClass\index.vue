<template>
  <div class="content-wrap-layout">
    <!-- 分类区 -->
    <div class="category">专业
      <el-select v-model="categoryCode" :popper-append-to-body="false" filterable @change="categoryChange">
        <el-option
          v-for="item in categoryArr"
          :key="item.categoryCode"
          :label="item.categoryName"
          :value="item.categoryCode"
        >
          <el-tooltip
            placement="bottom"
            width="200">
            <div slot="content">{{ item.categoryName }}</div>
            <span>
              {{ item.categoryName }}
            </span>
          </el-tooltip>
        </el-option>
      </el-select>
    </div>
    <page-table
      ref="table"
      :category-code="categoryCode"
      :default-selected-arr="defaultSelectedArr"
      @refresh="refresh"
      @link-event="linkEvent"
      @on-select="tabelSelect"
      @on-current="tabelCurrent"
      @transferDataClick="transferDataClick"
    />
  </div>
</template>

<script>
import category from './category/index.vue'
import moduleConf from './config'
import pageTable from './table/index.vue'
import { queryMajor } from '@/api/teachingAffairs/index.js'

export default {
  name: moduleConf.name,
  components: {
    pageTable,
    category
  },
  data() {
    return {
      moduleName: moduleConf.name,
      selectItem: [],
      defaultSelectedArr: [],
      transferData: [],
      categoryArr: [],
      categoryCode: ''
    }
  },
  mounted() {
    this.pjtCategoryQuery()
  },
  methods: {
    // 列表点击
    linkEvent: function({ name, row, params }) {
      this.$router.push({ name: name, params: params })
    },
    // 返回已选
    tabelSelect: function(data) {
      this.selectItem = data
      this.$emit('on-select', this.selectItem)
    },
    // 返回单选
    tabelCurrent: function(row) {
      this.selectItem = [row]
    },
    'categoryChange': function(value) {
      this.categoryCode = value
      this.$nextTick(() => {
        this.$refs['table'].getList(true, value)
      })
    },
    transferDataClick(res) {
      this.transferData = res
    },
    // action menu 事件
    actionHandler: function(type, data) {
      switch (type) {
        case 'refresh':
          this.$refs['table'].getList()
      }
    },
    refresh: function() {},
    pjtCategoryQuery() {
      queryMajor().then(res => {
        const paramArr = []
        res.data.forEach((item) => {
          paramArr.push({ categoryCode: String(item.majorCode), categoryName: item.majorName })
        })
        this.categoryArr = [{ categoryName: '全部', categoryCode: '' }, ...paramArr]
      })
    }
  }
}
</script>

<style scoped lang="scss">
.content-wrap-layout {
  height: 92% !important;
  .category {
    width: 100%;
    padding: 15px;
    border-bottom: 1px solid #dbdde0;
  }
  .el-select {
    margin-left: 10px;
  }
}
/deep/ .el-select-dropdown {
  width: 150px !important;
}
</style>
