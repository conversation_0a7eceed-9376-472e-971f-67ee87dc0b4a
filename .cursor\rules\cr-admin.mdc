---
description: 
globs: 
alwaysApply: true
---
---
description: 
globs: 
alwaysApply: true
---
# CR Admin 开发规范

## 目录结构
- 模式: `src/views/admin/{module}`
- 必需文件:
  - `index.vue`
  - `config.js`
- 必需目录:
  - `table/`
  - `action/`
  - `detail/`

## 命名约定
### 主要文件
- 主文件: `index.vue`
- 配置文件: `config.js`

### 组件文件夹
- 表格组件: `table/index.vue`
- 操作组件: `action/index.vue`
- 详情组件: `detail/index.vue`

## 代码组织
### index.vue 结构
#### 模板部分
- 根元素: `div.content-wrap-layout`
- 必需组件:
  - `page-table`
  - `action-menu`
- page-table 属性:
  - `ref="table"`
  - `default-selected-arr`
  - `filter-data`
  - `cache-pattern: true`
- page-table 事件:
  - `refresh`
  - `link-event`
  - `on-select`
  - `on-current`
- action-menu 属性:
  - `slot="action"`
  - `module-name="moduleName"`
  - `select-item="selectItem"`
- action-menu 事件:
  - `call`

#### 脚本部分
- 导入:
  - `config.js as moduleConf`
  - `table/index as pageTable`
  - `action/index as actionMenu`
- 组件注册:
  - `pageTable`
  - `actionMenu`
- 数据属性:
  - `moduleName` (来自 moduleConf.name)
  - `selectItem` (数组)
  - `defaultSelectedArr` (数组)
- 必需方法:
  - `linkEvent`: 处理行点击和导航
  - `tabelSelect`: 处理多选
  - `tabelCurrent`: 处理单选
  - `actionHandler`: 处理操作菜单事件
  - `refresh`: 处理刷新事件,写成空函数

### config.js 结构
- 必需导出:
  - 状态映射
  - 其他映射
- 格式: `export default { name, statusArr, statusObj, ... }`

## 枚举值处理规范
### 配置文件定义
- 位置: `config.js`
- 命名规则:
  - 数组形式: `{type}Arr` (如: statusArr, sexArr)
  - 对象形式: `{type}Obj` (如: statusObj, sexObj)
- 数组格式:
  ```js
  const statusArr = [
    { label: '禁用', value: '0', type: 'danger' },
    { label: '激活', value: '1', type: 'success' },
    { label: '未激活', value: '2', type: 'info' }
  ]
  ```
- 对象转换:
  ```js
  const statusObj = statusArr.reduce((acc, prev) => {
    acc[prev.value] = prev
    return acc
  }, {})
  ```

### 表格中使用
- 状态显示:
  ```vue
  <el-badge 
    :type="(module.statusObj[scope.row.status] && module.statusObj[scope.row.status].type) || 'info'" 
    is-dot 
  />
  {{ (module.statusObj[scope.row.status] && module.statusObj[scope.row.status].label) || '-' }}
  ```
- 其他枚举值:
  ```vue
  {{ (module.sexObj[scope.row.sex] && module.sexObj[scope.row.sex].label) || '-' }}
  ```

### 搜索配置
- 枚举值搜索项格式:
  ```js
  { 
    key: 'status', 
    label: '状态', 
    type: 'radio', 
    valueList: [
      { label: '禁用', value: '0' }, 
      { label: '激活', value: '1' }, 
      { label: '未激活', value: '2' } 
    ] 
  }
  ```

## 组件通信
### 表格事件
- `selection-change`
- `row-click`
- `refresh`

### 操作事件
- `add`
- `edit`
- `delete`
- `refresh`

## 开发工作流
1. 创建模块目录结构
2. 创建带映射的 config.js
3. 创建表格组件
4. 创建操作组件
5. 创建主 index.vue
6. 根据需要实现详情视图

## 代码风格
- 语法: ES6+
- 方法: 箭头函数
- 命名: 方法和变量使用驼峰命名

## 注释要求
必需的注释：
- 组件用途
- 复杂逻辑说明
- API 集成说明

## 表格组件规范
### 结构
#### 模板
- 根元素: `div.resource-table`
- 分区:
  - 操作区 (operation-wrap)
  - 搜索区 (search-box)
  - 列表区 (table-view)
- 必需组件:
  - `t-search-box`
  - `t-table-view`
  - `t-table-config`
- 必需混入:
  - `mixinsPageTable`

### 配置
#### 搜索配置
- 必需字段:
  - name (主要)
  - status
  - time_range
- 格式: `{ key, label, type?, valueList? }`

#### 列配置
- 必需字段:
  - name (主要)
  - status
  - createTime
- 格式: `{ title, master? }`

### 数据处理
- 必需方法:
  - `getList`
  - `handleSelection`
  - `refresh`
- API 集成:
  - 模式: `get{Module}List`
  - 响应处理: `code === 0`

### 事件
- 表格事件:
  - `on-select`
  - `on-current`
  - `on-change`
  - `on-sort-change`
  - `on-page-size-change`
- 搜索事件:
  - `searchMultiple`
- 配置事件:
  - `onChangeCol`

## 表单组件规范
### 添加/编辑弹窗
#### 模板结构
- 根元素: `div.dialog-wrap` 并添加 `v-loading="loading"`
- 表单元素: `el-form` 添加 `@submit.native.prevent`
- 表单项: 所有输入框添加 `v-model.trim`
- 底部按钮: 使用 `type="text"` 的取消按钮

#### 组件结构
- 必需导入:
  - `config.js as module`
  - `validate` 工具
  - 相关 API 函数
  - `mixinsActionMenu` 混入
- 组件注册:
  - 相关子组件
- 混入:
  - `mixinsActionMenu`

#### 数据定义
- 表单数据: 使用 `formData` 对象
- 验证规则: 使用 `validate` 工具函数
- 计算属性:
  - `editMode`: 判断是否为编辑模式
  - `activeItem`: 获取当前编辑项

#### 方法定义
- 表单提交: 使用 `confirm` 方法
- 数据处理:
  - 编辑模式: 自动填充表单数据
  - 数组字段: 提交前转换为逗号分隔字符串
  - 敏感数据: 提交前加密处理

#### 选择器组件
- 下拉选择: 使用 `el-select` 组件
- 多选: 添加 `multiple` 属性
- 分组: 使用 `el-option-group` 组件
- 可搜索: 添加 `filterable` 属性
- 宽度设置: 所有下拉框必须设置 `style="width: 100%"` 以保持一致性

#### 弹窗选择
- 使用 `el-dialog`
- 标题映射: 使用 `titleMapping` 对象
- 组件通信: 使用 `call` 事件

#### 侧拉选择
- 使用 `el-drawer`
- 标题映射: 使用 `titleMapping` 对象
- 组件通信: 使用 `call` 事件

## 批量操作规范
### 操作菜单
- 位置: `action/index.vue`
- 必需组件:
  - `el-dropdown`
  - `el-dialog`
- 属性:
  - `singleDisabled`: 单选操作禁用条件
  - `multipleDisabled`: 多选操作禁用条件
- 事件:
  - `clickDrop`: 处理下拉菜单点击
  - `confirmCall`: 处理弹窗回调

### 批量操作弹窗
- 位置: `action/modal-{operation}.vue`
- 必需组件:
  - `batch-template`
- 属性:
  - `data`: 选中数据
  - `available-data`: 可操作数据
  - `view-key`: 显示字段
- 方法:
  - `close`: 关闭弹窗
  - `confirm`: 确认操作

### 状态变更操作
- 必需组件:
  - `batch-template`
  - `el-alert`
- 计算属性:
  - `availableArr`: 筛选可操作数据
- 事件处理:
  - 成功/失败都触发刷新
  - 使用 `$bus.$emit` 通知刷新

## ESLint 规则
- `no_trailing_spaces`: error
- `eol_last`: ["error", "always"]
- `no_multiple_empty_lines`: ["error", { "max": 1, "maxEOF": 1 }]
- `no_trailing_spaces`: ["error", { "skipBlankLines": true }]