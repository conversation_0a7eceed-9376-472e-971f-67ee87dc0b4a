<template>
  <div class="approval-detail">
    <slot name="content" />
  </div>
</template>

<script>
export default {
  props: {
    // 载入状态
    loading: <PERSON><PERSON><PERSON>,
    title: String,
    // 是否直接go(-1)返回，如果不是，去$emit触发goback方法
    goback: {
      type: <PERSON><PERSON><PERSON>,
      default: true
    },
    showHeader: {
      type: <PERSON><PERSON>an,
      default: true
    },
    showFooter: {
      type: <PERSON><PERSON>an,
      default: true
    }
  }
}
</script>
<style lang="less" scoped>
.create-wrap-layout {
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  min-width: 880px;
  background-color: var(--neutral-100);
  overflow-y: hidden;
  .create-header {
    background-color: var(--neutral-0);
    border-bottom: 1px solid var(--neutral-300);
    width: 100%;
    padding: 0 24px;
    .create-header-top {
      display: flex;
      align-items: center;
      font-size: 16px;
      height: 48px;
      line-height: 48px;
      font-weight: 900;
      color: var(--neutral-800);
      margin: 0 auto;
      width: 65%;
      min-width: 830px;
      i {
        font-size: 14px;
        margin-right: 5px;
        cursor: pointer;
      }
    }
  }
  .create-body {
    flex: 1 1;
    padding: 0 24px;
    display: inline-flex;
    overflow-y: hidden;
    justify-content: center;
    .create-body-content {
      width: 65%;
      min-width: 832px;
      max-width: 1200px;
      overflow-y: auto;
      margin: 10px 0;
    }
  }
  .create-body-topo {
    flex: 1 1;
    display: inline-flex;
    overflow-y: hidden;
    justify-content: center;
    .create-body-topo-content {
      padding: 0 20px;
      width: 100%;
      height: 100%;
      ::v-deep {
        .orchestration-create-warp {
          height: calc(100vh - 275px);
        }
      }
    }
    .create-body-topo-content > div {
      height: 100%;
    }
  }
  .create-footer {
    width: 100%;
    padding: 0 24px;
    border-top: 1px solid var(--neutral-300);
    background-color: var(--neutral-0);
    .create-footer-content {
      height: 60px;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      margin: 0 auto;
      width: 65%;
      min-width: 832px;
      max-width: 1200px;
    }
    .el-button--text {
      padding: 4px 15px;
    }
  }
}
.approval-detail {
  height: 100%;
}
</style>
