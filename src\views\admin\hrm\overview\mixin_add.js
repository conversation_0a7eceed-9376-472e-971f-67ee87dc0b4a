export default {
  data() {
    return {
      modalAdd: false,
      pluginListModal: [],
      addPlugin: null
    }
  },
  computed: {
    addDisabled: function() {
      for (let i = 0; i < this.pluginListModal.length; i++) {
        if (this.pluginListModal[i]['plugin'] === this.addPlugin) {
          if (this.pluginListModal[i]['pluginConfig']) {
            return this.pluginListModal[i]['pluginConfig']['type']['value'] === null
          } else {
            return this.addedKeys.includes(this.pluginListModal[i]['plugin'])
          }
        }
      }
    },
    addedKeys: function() {
      const out = []
      this.layout.forEach((item) => {
        out.push(item.pluginConfig.pluginApiKey || item.plugin)
      })
      return out
    }
  },
  methods: {
    changeConfigItem(pluginConfig, itemConfig) {
      if (pluginConfig.times && itemConfig.key !== 'times') {
        pluginConfig.times.value = null
      }
    },
    disabledOpt: function(pluginConfig, itemConfig) {
      const topNDisabled = ['personal_points', 'team_points', 'penetration_test_top', 'personal_attack', 'team_attack', 'target_system_attack']
      const resourcesPercentDisabled = ['personal_tasks', 'team_tasks', 'penetration_test_type', 'penetration_test_tools', 'screen_recording_service', 'application', 'network_authorization', 'penetration_tasks', 'alarm_strategy', 'alarm_log', 'untreated_alarm']
      return itemConfig.key === 'times' &&
        (topNDisabled.includes(pluginConfig.type.value) ||
        resourcesPercentDisabled.includes(pluginConfig.type.value))
    },
    'selectPlugin': function(item) {
      this.addPlugin = item['plugin']
    },
    'modalAddOpen': function() {
      const pluginModalData = JSON.parse(JSON.stringify(this.pluginList))
      let addPlugin = null
      for (let i = 0; i < pluginModalData.length; i++) {
        if (!pluginModalData[i]['pluginConfig']) {
          continue
        }
        const typeList = pluginModalData[i]['pluginConfig']['type']['list']
        for (const key in typeList) {
          if (!this.addedKeys.includes(key)) {
            pluginModalData[i]['pluginConfig']['type']['value'] = key
            break
          }
        }
        if (!addPlugin) {
          if (pluginModalData[i]['pluginConfig']['type']['value']) {
            addPlugin = pluginModalData[i]['plugin']
          }
        }
      }
      this.pluginListModal = pluginModalData
      this.addPlugin = addPlugin
      console.log(' this.pluginListModal-- this.pluginListModal', this.pluginListModal, this.addPlugin)
      this.modalAdd = true
    },
    'addOk': function() {
      for (let i = 0; i < this.pluginListModal.length; i++) {
        if (this.pluginListModal[i]['plugin'] === this.addPlugin) {
          const item = JSON.parse(JSON.stringify(this.pluginListModal[i]))
          console.log('item', item)
          const pluginConfig = item['pluginConfig']
          console.log('pluginConfig', pluginConfig)
          const addItem = {
            'h': item['h'],
            'w': item['w'],
            'plugin': item['plugin'],
            'pluginConfig': {
              'pluginName': pluginConfig ? (pluginConfig['type']['list'][pluginConfig['type']['value']] + '') : item.pluginCnName,
              'pluginApiKey': pluginConfig ? pluginConfig['type']['value'] : null,
              'timeName': pluginConfig['times'] ? (pluginConfig['times']['list'][pluginConfig['times']['value']] + '') : item.pluginCnName,
              'timeApiKey': pluginConfig['times'] ? pluginConfig['times']['value'] : null
            }
          }
          this.addPluginHandel(addItem)
          console.log('addOk', addItem)
          break
        }
      }
      this.addCancel()
    },
    'addCancel': function() {
      this.modalAdd = false
      this.addPlugin = null
      this.pluginListModal = []
    }
  }
}
