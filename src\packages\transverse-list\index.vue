<template>
  <div class="transverse-list">
    <div class="transverse-list-title">{{ title }}：</div>
    <div class="separate-line" />
    <el-scrollbar style="flex: 1; min-width: 0;">
      <div :class="basicTeachingTypeListBool ? 'transverse-list-content ':'transverse-list-content transverse-list-content-hide'">
        <div v-if="all" :class="categoryCode == 'all' ? 'content-selected list-content':'list-content'" title="全部" @click="selections('all')">全部</div>
        <div v-for="(item,index) in data" :key="index" :title="item[label]">
          <div
            :class="categoryCode == item[idName] ? 'content-selected list-content':'list-content'" class="type-item"
          >
            <span @click="selections(item)">
              {{ item[label].length > 15 ? item[label].substr(0,15) +'...' : item[label] }}</span>
            <i v-if="allowDeletion" class="el-icon-remove cursor reduce-type" @click="deleteList(item)"/>
          </div>
        </div>
      </div>
    </el-scrollbar>
    <div v-if="isShowExpand" class="transverse-list-operate" >
      <el-button v-if="allowAdd" plain size="small" icon="el-icon-plus" title="新增" @click="add()"/>
      <el-button v-if="allowDeletion" class="ml-5 mr-5" plain size="small" icon="el-icon-edit" title="编辑" @click="edit()"/>
      <div v-if="!basicTeachingTypeListBool" type="text" class="operation" @click="classificationListFn()">
        更多 <div><i class="el-icon-arrow-down"/></div>
      </div>
      <div v-if="basicTeachingTypeListBool" type="text" class="operation" @click="classificationListFn()">
        收起 <div><i class="el-icon-arrow-up"/></div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    // 用来做展示的名称
    label: {
      type: String,
      default: 'categoryName'
    },
    moduleName: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: '分类'
    },
    // 渲染的数据
    data: {
      type: Array,
      default: () => {
        return []
      }
    },
    // 唯一值
    idName: {
      type: String,
      default: 'categoryCode'
    },
    // 是否需要全部选项
    all: {
      type: Boolean,
      default: true
    },
    // 是否允许删除
    allowDeletion: {
      type: Boolean,
      default: true
    },
    // 是否允许新增
    allowAdd: {
      type: Boolean,
      default: true
    },
    // 是否允许修改
    allowEdit: {
      type: Boolean,
      default: true
    },
    // 默认选中id
    defaultCode: {
      type: String,
      default: ''
    },
    // 是否展示展开收起
    isShowExpand: {
      type: Boolean,
      default: true
    },
    cachePattern: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      basicTeachingTypeListBool: false,
      categoryCode: this.defaultCode || 'all'
    }
  },
  watch: {
    'data'(val) {
    }
  },
  created() {
    if (this.$store.state.cache[this.moduleName]) {
      this.selections(this.$store.state.cache[this.moduleName], 1)
    }
  },
  mounted() {
  },
  methods: {
    selections(item, type) {
      if (this.cachePattern) {
        const obj = {
          data: item,
          key: this.moduleName
        }
        this.$store.commit('SET_CACHE', obj)
      }
      if (item == 'all') {
        this.categoryCode = item
      } else {
        this.categoryCode = item[this.idName]
      }
      this.$emit('node-click', item, type)
    },
    add() {
      this.$emit('add')
    },
    edit() {
      this.$emit('edit')
    },
    deleteList(item) {
      this.$emit('delete', item)
    },
    classificationListFn() {
      this.basicTeachingTypeListBool = !this.basicTeachingTypeListBool
    }
  }
}
</script>

<style lang="scss" scoped>
.transverse-list {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 12px 15px 12px 24px;
  width: 100%;
  font-size: 16px;
  border-bottom: 1px solid var(--neutral-300);
  & + .transverse-list {
    padding-top: 0px;
  }
  .operation {
    border: 1px solid #DCDFE6;
    height: 32px;
    padding: 0 12px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 13px;
    min-width: 60px;
    font-weight: 400;
    color: #333333;
    display: flex;
    align-items: center;
    justify-content: center;
    div {
      margin-left: 3px;
      i {
        font-size: 12px;
      }
    }
  }
  .transverse-list-title{
    align-self: center;
    white-space: nowrap;
    font-size: 14px;
    line-height: 18px;
    font-weight: 500;
    color: #35445d;
  }
  .separate-line {
    align-self: center;
    height: 14px;
    width: 1px;
    margin: 0 12px;
    background-color: #e1e6ef;
  }
  .transverse-list-content {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    flex: 1;
    min-width: 0;
    position: relative;
    max-height: 200px;
    .list-content {
      height: 32px;
      padding: 7px 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      font-size: 14px;
      line-height: 18px;
      font-weight: 500;
      border-radius: 6px;
      color: #35445d;
      margin-right: 6px;
    }

    .list-content:hover{
      background-color: #ebeff5;
      border-radius: 6px;
    }
    .content-selected {
      background-color: #ebeff5;
    }
    .type-item{
      position: relative;
      border-radius: 6px;
      z-index: 0;
    }
    .type-item:hover{
      background: rgba(35, 98, 251, 0.1);
      .reduce-type{
        display: inline;
      }
    }
    .reduce-type{
      display: none;
      position: absolute;
      top: 0px;
      right: -1px;
      color: red;
      z-index: 1;
    }
  }
  .transverse-list-content:last-child {
    margin-right: 0;
  }
  .transverse-list-content-hide{
    height: 32px;
    overflow: hidden;
  }
  .transverse-list-operate{
    display: flex;
    align-items: center;
    justify-content: space-between;
    align-self: center;
    .el-button{
      border-radius: 6px;
    }
  }
  .list-operate-position{
    position: absolute;
    right: 0;
    bottom: 0px;
  }
}
.ml-5{
  margin-left: 5px !important;
}
.mr-5{
  margin-right: 5px;
}
</style>
