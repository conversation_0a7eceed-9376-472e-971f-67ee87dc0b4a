<template>
  <div class="buttons-wrap">
    <el-button type="primary" icon="el-icon-plus" @click="clickDrop('modalAdd')">创建</el-button>
    <el-dropdown trigger="click" placement="bottom-start" @command="clickDrop">
      <el-button type="primary">
        操作<i class="el-icon-arrow-down el-icon--right" />
      </el-button>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item :disabled="singleDisabled" command="modalEdit">编辑</el-dropdown-item>
        <el-dropdown-item :disabled="multipleDisabled" command="modalDelete">删除</el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
    <!-- 中部弹窗 start-->
    <el-dialog
      :title="titleMapping[modalName]"
      :visible.sync="modalShow"
      :width="modalWidth"
      :destroy-on-close="true"
      @close="modalClose"
    >
      <transition name="el-fade-in-linear">
        <component
          :is="modalName"
          :name="modalName"
          :data="selectItem"
          @close="modalClose"
          @call="confirmCall"
        />
      </transition>
    </el-dialog>
    <!-- 中部弹窗 end-->
  </div>
</template>
<script>
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
import modalDelete from './modal-delete'
import modalAdd from './modal-add.vue'
import modalEdit from './modal-add.vue'
export default {
  components: {
    modalDelete,
    modalAdd,
    modalEdit
  },
  mixins: [mixinsActionMenu],
  data() {
    return {
      // 弹窗title映射
      titleMapping: {
        'modalDelete': '删除',
        'modalAdd': '添加技能点',
        'modalEdit': '编辑技能点'
      }
    }
  },
  methods: {
    confirmCall: function(type, data) {
      if (type === 'close') {
        this.modalClose()
      } else if (type === 'refresh') {
        this.$emit('call', type)
      }
    },
    'clickDrop': function(name) {
      if (this.drawerAction && this.drawerAction.includes(name)) {
        this.drawerName = name
      } else {
        this.modalName = name
      }
    }
  }
}
</script>
