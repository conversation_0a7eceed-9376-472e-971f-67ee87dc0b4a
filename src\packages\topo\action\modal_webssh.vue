<template>
  <div v-loading="loading" class="dialog-wrap">
    <el-form ref="form" :model="formData" :rules="rules" label-width="80px" @submit.native.prevent>
      <el-form-item label="选择端口" prop="port_id">
        <el-select v-model="formData.port_id" @change="changePort">
          <el-option
            v-for="(item) in ports"
            :key="item.index"
            :label="item.name"
            :value="item.id"
            :disabled="!item.link_to"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="本地IP" prop="local_ip">
        <el-input v-model.trim="formData.local_ip"/>
      </el-form-item>
      <el-form-item label="节点IP" prop="remote_host">
        <el-input v-model.trim="formData.remote_host"/>
      </el-form-item>
      <el-form-item label="节点端口" prop="remote_host_port">
        <el-input-number v-model="formData.remote_host_port"/>
      </el-form-item>
    </el-form>
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>

<script>
import module from '../config.js'
import { getNodeItem, enable_webssh, webssh_console } from '../api/orchestration'
import modalMixins from '../../mixins/modal_form'
import validate from '../../validate'
export default {
  mixins: [modalMixins],
  props: {
    // 传入数据
    data: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      moduleName: module.name,
      loading: true,
      ports: [],
      formData: {
        'port_id': '', // 选择端口
        'local_ip': '', // 本地IP
        'remote_host': '', // 节点IP
        'remote_host_port': 22 // 节点端口
      },
      rules: {
        'port_id': [
          validate.required('change')
        ],
        'local_ip': [
          validate.required(),
          validate.filterIpCIDR
        ],
        'remote_host': [
          validate.required(),
          validate.filterIPAddress
        ],
        'remote_host_port': [
          validate.required('change'),
          validate.number_integer
        ]
      }
    }
  },
  created() {
    this.getNodeItem()
  },
  methods: {
    'changePort': function(value) {
      this.formData['remote_host'] = this.ports.find(port => port.id === value).ipaddr
    },
    // 重新获取node的端口
    'getNodeItem': function() {
      getNodeItem(this.data.node_id)
        .then(res => {
          this.ports = res.data.data.ports
          this.loading = false
        })
        .catch((error) => {
          this.loading = false
          console.log(error)
        })
    },
    'confirm': function() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.loading = true
          const enableData = {
            'port_id': this.formData.port_id,
            'local_ip': this.formData.local_ip
          }
          const websshData = {
            'remote_host': this.formData.remote_host,
            'remote_host_port': this.formData.remote_host_port
          }
          enable_webssh(this.data.node_id, enableData)
            .then(res => {
              webssh_console(this.data.node_id, websshData)
                .then(res => {
                  const url = res.data.data
                  const a = document.createElement('a')
                  a.setAttribute('href', encodeURI(url))
                  a.setAttribute('target', '_blank')
                  a.setAttribute('id', 'camnpr')
                  document.body.appendChild(a)
                  a.click()
                })
                .catch((error) => {
                  this.$message.error(error)
                })
            })
            .catch((error) => {
              this.$message.error(error)
            })
          this.close()
        }
      })
    },
    close() {
      this.$emit('close')
    }
  }
}
</script>
