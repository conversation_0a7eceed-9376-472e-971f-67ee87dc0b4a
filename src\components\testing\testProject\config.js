// 测试状态mapping
const testStatusArr = [
  { label: '待测试', value: '0', type: 'info' },
  { label: '测试中', value: '1', type: 'warning' },
  { label: '测试通过', value: '2', type: 'success' },
  { label: '测试不通过', value: '3', type: 'danger' }
]
export default {
  name: 'testProjectList',
  testStatusArr: testStatusArr,
  get searchStatusArr() {
    return this.testStatusArr.filter(item => {
      return item.value != '0'
    })
  },
  get testStatusStrArr() {
    return this.testStatusArr.map(item => {
      return { label: item.label, value: Number(item.value), type: item.type }
    })
  },
  // 将状态map数组转换为对象
  testStatusObj: testStatusArr.reduce((acc, prev) => {
    acc[prev.value] = prev
    return acc
  }, {})
}
