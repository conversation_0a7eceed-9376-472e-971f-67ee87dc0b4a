<template>
  <div class="info-content">
    <div class="body">
      <div class="title">
        <div class="title-left">课程信息</div>
        <div>课程目录</div>
      </div>
      <div class="content">
        <div class="content-left">
          <div class="course-name">
            {{ courseInfo.name }}
          </div>
          <div class="content-left-bottom">
            <div class="content-font">课程难度：{{ difficultyList[courseInfo.difficulty - 1] }}</div>
            <div class="content-font">课程数：{{ courseInfo.experimentNumber }}节</div>
            <div v-if="roleType == 2" class="content-font">完成数：{{ courseInfo.completeNumber }}</div>
            <div class="content-font">课时：{{ courseInfo.totalClassHour }}课时</div>
            <div class="content-title">
              课程介绍
            </div>
            <div class="content-font">
              {{ courseInfo.courseDescription }}
            </div>
            <div class="content-title">
              知识点
            </div>
            <div class="knowledge-points-container">
              <div v-for="(item, index) in courseInfo.knowledgePoints" :key="index" class="knowledge-points">
                {{ item }}
              </div>
            </div>
          </div>
        </div>
        <div class="content-right">
          <div v-if="editMode == 1 && notOperable == 0" style="padding: 20px 0px 0 0;">
            <el-button style="margin: 0 0 20px 20px;" type="primary" icon="el-icon-plus" @click="addChapterFn()">添加章节</el-button>
            <div class="segmentation" />
          </div>
          <div class="tree-content">
            <el-tree
              ref="treeRef"
              :data="treeData"
              :props="defaultProps"
              :expand-on-click-node="false"
              :default-expand-all="true"
              :allow-drop="allowDrop"
              class="training-course-tree"
              node-key="id"
              draggable
              @node-drop="handleDrop"
              @node-drag-start="handleDropBefore"
            >
              <div slot-scope="{ node, data }" :class="{'active-node': contentId == data.id, 'course-node': data.level == 3}" class="custom-tree-node" @click="clickNode(data)">
                <div v-if="data.level == 1" class="chapter-node">第{{ getNodeIndex(data) + 1 }}章 | {{ data.name }}</div>
                <div v-if="data.level == 2" class="unit-node">第{{ getNodeIndex(data) + 1 }}单元 | {{ data.name }}</div>
                <div v-if="data.level == 3" class="content-node">
                  <span>
                    {{ getNodeIndex($refs.treeRef.getNode(node).parent.parent.data) + 1 }}.{{ getNodeIndex($refs.treeRef.getNode(node).parent.data) + 1 }}.{{ getNodeIndex(data) + 1 }}
                  </span> {{ data.name }}
                </div>
                <div>
                  <el-button v-if="data.level == 1" type="text" size="mini" @click="addUnitFn(data)">
                    添加单元
                  </el-button>
                  <el-button v-if="data.level == 1" type="text" size="mini" @click="editChapterFn(data)">
                    编辑章节
                  </el-button>
                  <el-button v-if="data.level == 1" type="text" size="mini" @click="deleteChapterFn(data)">
                    删除章节
                  </el-button>
                  <el-button v-if="data.level == 2" type="text" size="mini" @click="addCourseFn(data)">
                    添加内容
                  </el-button>
                  <el-button v-if="data.level == 2" type="text" size="mini" @click="editUnitFn(data)">
                    编辑单元
                  </el-button>
                  <el-button v-if="data.level == 2" type="text" size="mini" @click="deleteUnitFn(data)">
                    删除单元
                  </el-button>
                  <el-button v-if="data.level == 3" type="text" size="mini" @click="deleteCourseFn(data)">
                    移除内容
                  </el-button>
                </div>
                <div v-if="data.level == 1 && (getNodeIndex(data) < treeData.length - 1)" class="segmentation-tree" />
              </div>
            </el-tree>
          </div>
        </div>
      </div>
    </div>
    <!-- 侧拉弹窗 -->
    <el-drawer
      :title="titleMapping[drawerName]"
      :visible.sync="drawerShow"
      :size="drawerWidth"
      append-to-body
      @close="drawerClose"
    >
      <transition name="el-fade-in-linear">
        <component
          :is="drawerName"
          :name="drawerName"
          :data="treeItem"
          :course-id="id"
          :add-index="addIndex"
          :exist-content-id-list="existContentIdList"
          @close="drawerClose"
          @call="drawerConfirmCall"
        />
      </transition>
    </el-drawer>
    <!-- 弹窗 -->
    <el-dialog
      :title="titleMapping[modalName]"
      :visible.sync="modalShow"
      :width="modalWidth"
      @close="modalClose"
    >
      <transition name="el-fade-in-linear">
        <component
          :is="modalName"
          :name="modalName"
          :data="treeItem"
          :add-index="addIndex"
          :course-id="id"
          @close="modalClose"
          @call="confirmCall"
        />
      </transition>
    </el-dialog>
  </div>
</template>

<script>
import addChapter from './action/modal-add-chapter.vue'
import editChapter from './action/modal-edit.vue'
import editUnit from './action/modal-edit.vue'
import addCourse from './action/modal-add-course.vue'
import addUnit from './action/modal-add-unit.vue'
import deleteChapter from './action/modal-delete.vue'
import deleteUnit from './action/modal-delete.vue'
import deleteCourse from './action/modal-delete.vue'
import tSearchBox from '@/packages/search-box/index.vue'
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
import { getCourseInfo } from '@/api/teacher/index'
import { pjtChapterUnitSortApi, crossChapterSortApi } from '@/api/teacher/index.js'
export default {
  components: {
    tSearchBox,
    addChapter,
    addUnit,
    addCourse,
    editChapter,
    editUnit,
    deleteChapter,
    deleteUnit,
    deleteCourse
  },
  mixins: [mixinsActionMenu],
  props: {
    id: [String, Number],
    roleType: [String, Number],
    chapterIdNew: [String, Number],
    unitIdNew: [String, Number],
    contentIdNew: [String, Number],
    contentNew: Object,
    editMode: {
      type: [String, Number],
      default: 0
    },
    notOperable: {
      type: [String, Number],
      default: 0
    }
  },
  data() {
    return {
      activeList: [],
      courseInfo: {},
      difficultyList: ['初级', '中级', '高级'],
      drawerAction: ['addCourse'], // 侧拉弹窗
      chapterId: '',
      unitId: '',
      contentId: '',
      treeItem: [],
      titleMapping: {
        'addChapter': '添加章节',
        'addUnit': '添加单元',
        'addCourse': '添加课程',
        'editChapter': '编辑章节',
        'editUnit': '编辑单元',
        'deleteChapter': '删除章节',
        'deleteUnit': '删除单元',
        'deleteCourse': '删除课程'
      },
      existContentIdList: [],
      defaultProps: {
        children: 'children',
        label: 'name'
      },
      treeData: [],
      loading: false,
      sortApiType: pjtChapterUnitSortApi,
      addIndex: 0, // 新增的索引
      sourceContentIndex: 0, // 当前拖拽的课程原索引
      sourceUnitId: 0, // 拖拽的单元id
      targetUnitId: 0 // 目标单元id
    }
  },
  watch: {
    contentIdNew: {
      handler() {
        this.handleChange(this.chapterIdNew, this.unitIdNew, this.contentIdNew, this.contentNew)
      }
    }
  },
  mounted() {
    this.getCourseInfo()
  },
  methods: {
    // 侧拉回调
    drawerConfirmCall: function(type, data) {
      if (type === 'close') {
        this.drawerClose()
      } else if (type === 'confirm_course') {
        this.formData.selectedCourse = data
        this.drawerClose()
      } else if (type === 'refresh') {
        this.getCourseInfo()
      }
    },
    // 弹窗回调
    confirmCall: function(type, data) {
      if (type === 'close') {
        this.modalClose()
      } else if (type === 'refresh') {
        this.getCourseInfo()
      }
    },
    // 新增章节
    addChapterFn(data) {
      this.treeItem = [data]
      this.addIndex = this.treeData.length
      this.clickDrop('addChapter', this.treeItem)
    },
    // 删除章节/单元
    deleteChapterFn(data) {
      this.treeItem = [data]
      this.clickDrop('deleteChapter', this.treeItem)
    },
    // 编辑章节
    editChapterFn(data) {
      this.treeItem = [data]
      this.clickDrop('editChapter', this.treeItem)
    },
    // 新增单元
    addUnitFn(data) {
      this.treeItem = [data]
      this.addIndex = data.children.length
      this.clickDrop('addUnit', this.treeItem)
    },
    // 添加课程内容
    addCourseFn(data) {
      this.treeItem = [data]
      this.addIndex = data.contentList.length
      this.existContentIdList = [] // 已有的课程内容
      this.courseInfo.chapterUnitList.forEach(item => {
        item.unitList.forEach(unit => {
          unit.contentList.forEach(content => {
            this.existContentIdList.push(content.id)
          })
        })
      })
      this.clickDrop('addCourse', this.treeItem)
    },
    // 编辑单元
    editUnitFn(data) {
      this.treeItem = [data]
      this.clickDrop('editUnit', this.treeItem)
    },
    // 删除单元
    deleteUnitFn(data) {
      this.treeItem = [data]
      this.clickDrop('deleteUnit', this.treeItem)
    },
    // 删除课程内容
    deleteCourseFn(data) {
      this.treeItem = [data]
      this.clickDrop('deleteCourse', this.treeItem)
    },
    getCourseInfo() {
      getCourseInfo({ courseId: this.id, roleType: this.roleType, editMode: this.editMode }).then(res => {
        this.courseInfo = res.data
        this.treeData = res.data.chapterUnitList
        this.$emit('set-course-info', this.courseInfo)
        this.contentId = ''
        if (this.$route.query.contentId) {
          this.courseInfo.chapterUnitList.forEach(item => {
            item.unitList.forEach(unit => {
              unit.contentList.forEach(content => {
                if (this.$route.query.contentId == content.id) {
                  this.chapterId = item.id
                  this.unitId = unit.id
                  this.contentId = content.id
                }
              })
            })
          })
        } else {
          this.courseInfo.chapterUnitList.forEach(item => {
            item.unitList.forEach(unit => {
              unit.contentList.forEach(content => {
                if (content && !this.contentId) {
                  this.chapterId = item.id
                  this.unitId = unit.id
                  this.contentId = content.id
                }
              })
            })
          })
        }
        // 处理树形数据
        this.treeData.forEach(item => {
          item.level = 1
          if (item.unitList) {
            item.children = item.unitList
            item.unitList.forEach(it => {
              it.level = 2
              if (it.contentList) {
                it.children = it.contentList
                it.contentList.forEach(i => {
                  i.level = 3
                })
              }
            })
          }
        })
      })
    },
    allowDrop(draggingNode, dropNode) {
      // 只允许在同级节点之间拖拽
      if (draggingNode.level === dropNode.level) {
        return true
      }
      return false
    },
    // 节点拖拽前
    handleDropBefore(node, event) {
      if (node.level === 3) {
        this.sourceContentIndex = this.getNodeIndex(node.data)
        this.sourceUnitId = this.findParentById(this.treeData, node.data.id).id
      }
    },
    // 拖拽节点
    handleDrop(draggingNode, dropNode, dropType) {
      const data = (dropType !== 'inner' && draggingNode.level != 1) ? dropNode.parent.data : dropNode.data
      let nodeData = null
      let nodeList = []
      let params = {}
      let crossSort = 0
      let sourceChapterId = null // 拖拽的章节id
      let targetChapterId = null // 目标章节id
      if (dropType !== 'inner') {
        nodeData = data.children
        if (draggingNode.level === 3 && dropNode.level === 3) {
          sourceChapterId = this.findParentById(this.treeData, draggingNode.data.unitId).id
          targetChapterId = this.findParentById(this.treeData, dropNode.data.unitId).id
          this.targetUnitId = this.findParentById(this.treeData, dropNode.data.id).id
        }
        // 章节排序
        if (draggingNode.level === 1 && dropNode.level === 1) {
          this.sortApiType = pjtChapterUnitSortApi
          // 章节排序
          this.treeData.forEach((ele, i) => {
            ele.pid = data.id
          })
          nodeList = this.treeData.map((item, index) => {
            return { id: item.id, sort: index + 1 }
          })
          params = {
            list: nodeList,
            courseId: this.id
          }
        }
        // 单元排序
        const crossUnitSortFlag = draggingNode.level === 2 && dropNode.level === 2
        if (crossUnitSortFlag) {
          this.sortApiType = crossChapterSortApi
          crossSort = nodeData.findIndex(item => item.id == draggingNode.data.id)
          params = {
            sourceChapterId: draggingNode.data.parentId,
            targetChapterId: dropNode.data.parentId,
            sort: crossSort,
            unitId: draggingNode.data.id
          }
        }
        // 课程排序
        if (draggingNode.level === 3 && dropNode.level === 3) {
          this.sortApiType = crossChapterSortApi
          crossSort = nodeData.findIndex(item => item.id == draggingNode.data.id)
          params = {
            sourceChapterId: sourceChapterId,
            targetChapterId: targetChapterId,
            unitId: draggingNode.data.unitId,
            targetUnitId: dropNode.data.unitId,
            contentSort: crossSort,
            contentId: draggingNode.data.id
          }
          // 同一单元内部才传值
          if (this.sourceUnitId === this.targetUnitId) {
            params.sourceContentSort = this.sourceContentIndex
          }
        }
      }
      this.sortApiType(params).then(res => {
        if (res.code === 0 || res.code === 200) {
          this.$message.success('操作成功')
          this.getCourseInfo()
        }
      })
    },
    // 通过遍历树形数据来找到节点的下标
    getNodeIndex(nodeData) {
      let index = 0
      const findIndex = (data, currentData) => {
        data.forEach((item, i) => {
          if (item === currentData) {
            index = i
            return
          }
          if (item.children && item.children.length > 0) {
            findIndex(item.children, currentData)
          }
        })
      }
      findIndex(this.treeData, nodeData)
      return index
    },
    clickNode(node) {
      // 处理课程数据
      if (node.level === 3) {
        const currentUnit = this.findParentById(this.treeData, node.id)
        if (node.id == this.contentId) {
          return
        }
        this.chapterId = currentUnit.parentId
        this.unitId = currentUnit.id
        this.contentId = node.id
        this.$emit('switch-courses', currentUnit.parentId, currentUnit.id, node.id, node)
      }
    },
    // 通过id找到父节点数据
    findParentById(data, targetId, parent = null) {
      for (let i = 0; i < data.length; i++) {
        const currentObj = data[i]
        if (currentObj.id === targetId) {
          return parent // 找到匹配的子对象的父级对象
        } else if (currentObj.children && currentObj.children.length > 0) {
          // 如果当前对象有子节点，递归搜索子节点
          const result = this.findParentById(currentObj.children, targetId, currentObj)
          if (result) {
            return result // 如果找到匹配的子对象的父级对象，返回它
          }
        }
      }
      return null
    },
    handleChange(chapterId, unitId, contentId, content) {
      if (contentId == this.contentId) {
        return
      }
      this.chapterId = chapterId
      this.unitId = unitId
      this.contentId = contentId
      this.$emit('switch-courses', chapterId, unitId, contentId, content)
    }
  }
}
</script>

<style lang="scss" scoped>
.info-content {
  margin-top: 15px;
  background: #fafafa;
  position: relative;
  .body {
    -moz-box-shadow: 0 1px 8px #bfbcbc;
    -webkit-box-shadow: 0 1px 8px #bfbcbc;
    box-shadow: 0 1px 8px #bfbcbc;
    margin: 0 20px 40px;
    .title {
      position: relative;
      padding-left: 15px;
      display: flex;
      align-items: center;
      height: 70px;
      background: #fff;
      font-size: 18px;
      margin-bottom: 10px;
      .title-left {
        width: 235px;
        margin-right: 30px;
      }
    }
    .content {
      display: flex;
      .content-left {
        min-width: 250px;
        max-width: 250px;
        margin-right: 10px;
        background: #fff;
        .content-left-bottom {
          margin-top: 10px;
          padding: 0 20px 20px 20px;
        }
        .course-name {
          border-bottom: 1px solid #ccc;
          padding: 10px 20px;
          font-size: 16px;
          color: #202020;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .content-title {
          border-top: 1px solid #ccc;
          padding-top: 10px;
          font-size: 16px;
          color: #202020;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .content-font {
          color: var(--neutral-700);
          font-size: 14px;
          margin: 4px 0;
          word-wrap: break-word;
        }
        .knowledge-points-container {
          margin-top: 10px;
          display: flex;
          flex-wrap: wrap;
          align-items: center;
          .knowledge-points {
            padding: 5px 10px;
            background: var(--color-600);
            border-radius: 16px;
            color: #fff;
            margin: 0 10px 10px 0;
          }
        }
      }
      .content-right {
        width: 100%;
        background: #fff;
        .segmentation {
          height: 10px;
          background: #fafafa;
        }
      }
    }
  }
}
.collapseded {
  animation: collapse 0.5s ease forwards;
  overflow: hidden;
  max-height: 0;
}
.expandded {
  animation: open 1s ease forwards;
  overflow: hidden;
}
@keyframes open {
  from {
    max-height: 0; /* 设置一个合适的最大高度 */
  }
  to {
    max-height: 1000px;
  }
}
@keyframes collapse {
  from {
    max-height: 1000px; /* 设置一个合适的最大高度 */
  }
  to {
    max-height: 0;
  }
}
::v-deep .tree-content {
  width: 100%;
  padding: 10px 20px;
  overflow-y: auto;
  .custom-tree-node {
    width: 100%;
    padding-right: 5px;
    display: flex;
    background: #fff;
    cursor: auto;
    align-items: center;
    justify-content: space-between;
    .chapter-node {
      font-size: 16px;
      color: #202020;
    }
    .unit-node, .content-node {
      font-size: 14px;
      color: #202020;
    }
    &.active-node {
      cursor: pointer;
      color: var(--color-700);
      background-color: var(--color-50);
    }
  }
  .course-node {
    padding: 0 5px;
    width: calc(100% + 10px);
    cursor: pointer;
  }
  .el-tree>div {
    position: relative;
    padding: 10px;
    .segmentation-tree {
      position: absolute;
      bottom: -15px;
      left: -20px;
      width: calc(100% + 40px);
      margin: 10px 0;
      height: 10px;
      background: #fafafa;
    }
  }
  .el-tree {
    .el-tree-node {
      .el-tree-node__content {
        padding: 0 !important;
      }
      .el-tree-node__children {
        .el-tree-node__content {
          padding-left: 0 !important;
          margin-left: 18px !important;
        }
        .el-tree-node__children {
          .el-tree-node {
            .el-tree-node__content {
              padding-left: 0 !important;
              margin-left: 30px !important;
            }
          }
        }
      }
    }
  }
  .el-tree .el-icon-caret-right:before {
    display: none;
  }
  .el-tree-node__children {
    padding-left: 2px;
  }
  .el-tree-node__content {
    height: 38px;
    line-height: 38px;
  }
  .el-tree-node__content>.el-tree-node__expand-icon {
    padding: 0;
  }
  .el-button {
    padding: 0;
    margin-left: 10px;
    font-size: 12px;
    color: #999;
  }
  .el-button:hover {
    color: var(--color-700);
  }
}
</style>
