import request from '@/utils/request'
const headers = { 'Content-Type': 'application/json;charset=UTF-8' }

// 查询队伍管理列表页
export function getTeamList(data) {
  return request({
    url: `admin/sysTeam/queryPage`,
    method: 'post',
    data,
    headers
  })
}

// 新增队伍管理
export function teamCreate(data) {
  return request({
    url: `admin/sysTeam/create`,
    method: 'post',
    data,
    headers
  })
}

// 修改队伍管理
export function teamUpdate(data) {
  return request({
    url: `admin/sysTeam/update`,
    method: 'post',
    data,
    headers
  })
}

// 删除队伍管理
export function teamRemove(data) {
  return request({
    url: `admin/sysTeam/remove`,
    method: 'post',
    data,
    headers
  })
}

// 修改队伍管理状态
export function teamUpdateTeamStatus(data) {
  return request({
    url: `admin/sysTeam/updateTeamStatus`,
    method: 'post',
    data,
    headers
  })
}

// 获取队伍管理信息
export function teamGet(params) {
  return request({
    url: `admin/sysTeam/get`,
    method: 'get',
    params,
    headers
  })
}

// 获取队伍队员列表
export function teamUserQueryPage(data) {
  return request({
    url: `admin/cepoSysTeamUser/queryPage`,
    method: 'post',
    data,
    headers
  })
}

// 查询可选人员列表
export function teamUserQueryUserWithoutPlayer(data) {
  return request({
    url: `admin/cepoSysTeamUser/queryUserWithoutPlayer`,
    method: 'post',
    data,
    headers
  })
}

// 添加队伍成员
export function teamUserCreate(data) {
  return request({
    url: `admin/cepoSysTeamUser/create`,
    method: 'post',
    data,
    headers
  })
}

// 移除队伍成员
export function teamUserRemove(data) {
  return request({
    url: `admin/cepoSysTeamUser/remove`,
    method: 'post',
    data,
    headers
  })
}

// 设置队伍队长
export function teamUserUpdate(data) {
  return request({
    url: `admin/cepoSysTeamUser/update`,
    method: 'post',
    data,
    headers
  })
}

// 获取队伍队长信息
export function teamUserGetTeamLeader(params) {
  return request({
    url: `admin/cepoSysTeamUser/getTeamLeaderByTeamId`,
    method: 'get',
    params,
    headers
  })
}

// 获取队伍成绩
export function teamGetTeamRecord(data) {
  return request({
    url: `admin/sysTeam/getTeamRecord`,
    method: 'post',
    data,
    headers
  })
}

// 获取队伍成绩记录人员列表
export function teamQueryPlayerInfo(data) {
  return request({
    url: `admin/sysTeam/queryPlayerInfo`,
    method: 'post',
    data,
    headers
  })
}

// 批量导入战队
export function excelImportTeam(data) {
  return request({
    url: '/admin/sysTeam/excelImportTeam',
    method: 'post',
    data,
    headers
  })
}

// 下载批量导入战队模板
export function downExcelImportTeamModel(data) {
  return request({
    url: '/admin/sysTeam/downExcelImportTeamModel',
    method: 'post',
    headers,
    data,
    responseType: 'blob'
  })
}

// 下载批量导入战队错误列表
export function downImportTeamErrorExcel(data) {
  return request({
    url: '/admin/sysTeam/downImportTeamErrorExcel',
    method: 'post',
    headers,
    data,
    responseType: 'blob'
  })
}
