<template>
  <div class="dropdown-radio">
    <div class="dropdown-radio-list">
      <el-input v-model="filterKey" size="mini" placeholder="请输入过滤条件" class="filter-input" />
      <el-radio-group v-model="radioValue">
        <ul>
          <li v-for="item in filterData" :key="item.value">
            <el-radio :label="item.value" :title="item.label">{{ item.label }}</el-radio>
          </li>
        </ul>
      </el-radio-group>
    </div>
    <div class="dropdown-radio-bottom">
      <el-button type="primary" size="mini" @click="confirm">确定</el-button>
      <el-button size="mini" type="text" @click="cancel">取消</el-button>
    </div>
  </div>
</template>
<style lang="less">
.dropdown-radio {
  width: 150px;
  .dropdown-radio-input {
    padding-bottom: 10px;
  }
  .dropdown-radio-list {
    padding-bottom: 10px;
    ul {
      width: 150px;
      max-height: 200px;
      overflow: auto;
      overflow-x: hidden;
    }
    li {
      padding: 2px;
      max-width: 140px;
      overflow: hidden;
      .el-radio__label {
        display: inline-block;
        max-width: 126px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        vertical-align: middle;
      }
    }
  }
  .dropdown-radio-bottom {
    margin: 0 -12px -12px -12px;
    border-top: solid 1px #ebeef5;
    padding: 8px 12px;
  }
}
</style>
<script>
export default {
  props: {
    value: {
      type: String,
      default: null
    },
    data: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      filterKey: '',
      searchKey: null,
      radioValue: null
    }
  },
  computed: {
    filterData: function() {
      if (this.filterKey) {
        return this.data.filter(item => item.label.indexOf(this.filterKey) > -1)
      }
      return this.data
    }
  },
  created() {
    if (this.value) {
      this.radioValue = this.value
    }
  },
  methods: {
    'confirm': function() {
      this.$emit('confirm', this.radioValue)
    },
    'cancel': function() {
      this.$emit('cancel')
    }
  }
}
</script>
