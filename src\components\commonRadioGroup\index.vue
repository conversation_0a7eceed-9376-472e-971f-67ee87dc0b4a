<template>
  <div class="radio-group-wrapper">
    <i
      v-if="showAllArrows"
      :class="{ 'disabled': !showLeftArrow }"
      class="el-icon-caret-left arrow-icon"
      @click="handlePrevious"
    />
    <div ref="radioGroupWrapper" class="radio-group-scroll">
      <el-radio-group
        ref="radioGroup"
        v-model="currentValue"
        :style="{ transform: `translateX(${radioGroupTranslateX}px)` }"
        size="small"
        class="common-radio-groups"
        @change="$emit('change', currentValue)"
      >
        <el-popover
          v-for="item in options"
          :key="item[valueKey]"
          :content="item[labelKey]"
          placement="top"
          trigger="hover"
          popper-class="testItem-file-popover"
        >
          <el-radio-button
            slot="reference"
            :label="item[valueKey]"
          >
            {{ item[labelKey] }}
          </el-radio-button>
        </el-popover>
      </el-radio-group>
    </div>
    <i
      v-if="showAllArrows"
      :class="{ 'disabled': !showRightArrow }"
      class="el-icon-caret-right arrow-icon"
      @click="handleNext"
    />
  </div>
</template>

<script>
export default {
  // 组件作用：长度超出容器时，显示左右箭头切换radio
  name: 'CommonRadioGroup',
  props: {
    value: [String, Number],
    options: {
      type: Array,
      required: true
    },
    labelKey: {
      type: String,
      default: 'name'
    },
    valueKey: {
      type: String,
      default: 'id'
    }
  },
  data() {
    return {
      radioGroupTranslateX: 0,
      showLeftArrow: false,
      showRightArrow: false,
      showAllArrows: false,
      currentValue: this.value
    }
  },
  watch: {
    value(val) {
      this.currentValue = val
    },
    options() {
      this.$nextTick(this.updateRadioGroupArrows)
    }
  },
  mounted() {
    this.$nextTick(this.updateRadioGroupArrows)
    window.addEventListener('resize', this.updateRadioGroupArrows)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.updateRadioGroupArrows)
  },
  methods: {
    handleNext() {
      const wrapper = this.$refs.radioGroupWrapper
      const group = this.$refs.radioGroup && this.$refs.radioGroup.$el
      if (!wrapper || !group) return
      const wrapperWidth = wrapper.offsetWidth
      const groupWidth = group.scrollWidth
      const maxTranslate = groupWidth - wrapperWidth
      let nextTranslate = this.radioGroupTranslateX - wrapperWidth / 3
      if (Math.abs(nextTranslate) > maxTranslate) {
        nextTranslate = -maxTranslate
      }
      this.radioGroupTranslateX = nextTranslate
      this.updateRadioGroupArrows()
    },
    handlePrevious() {
      const wrapper = this.$refs.radioGroupWrapper
      if (!wrapper) return
      let prevTranslate = this.radioGroupTranslateX + wrapper.offsetWidth / 3
      if (prevTranslate > 0) prevTranslate = 0
      this.radioGroupTranslateX = prevTranslate
      this.updateRadioGroupArrows()
    },
    updateRadioGroupArrows() {
      this.$nextTick(() => {
        const wrapper = this.$refs.radioGroupWrapper
        let group = this.$refs.radioGroup
        if (group && group.$el) group = group.$el
        if (!wrapper || !group) return
        const wrapperWidth = wrapper.offsetWidth
        const groupWidth = group.scrollWidth
        this.showAllArrows = groupWidth > wrapperWidth
        if (groupWidth > wrapperWidth) {
          this.showRightArrow = Math.abs(this.radioGroupTranslateX) + wrapperWidth < groupWidth
          this.showLeftArrow = this.radioGroupTranslateX < 0
        } else {
          this.showRightArrow = false
          this.showLeftArrow = false
          this.radioGroupTranslateX = 0
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.radio-group-wrapper {
  display: flex;
  align-items: center;
  .arrow-icon {
    cursor: pointer;
    font-size: 20px;
    z-index: 99;
    color: var(--color-600);
    transition: all 0.3s;
    &:hover {
      opacity: 0.8;
    }
    &.disabled {
      opacity: 0.5;
      pointer-events: none;
    }
  }
  .radio-group-scroll {
    overflow: hidden;
    min-width: 0;
    flex: 1 1 0%;
    height: 36px;
    display: flex;
    align-items: center;
  }
}
::v-deep .el-radio-group.common-radio-groups {
  border-radius: 10px;
  border: none;
  padding: 2px;
  background-color: #f5f6f9;
  height: 36px;
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  transition: transform 0.3s;
  will-change: transform;
  .el-radio-button {
    max-width: 156px;
    flex-shrink: 0;
    height: 36px !important;
    display: flex;
    align-items: center;
    .el-radio-button__inner {
      width: 100%;
      background-color: #f5f6f9;
      color: var(--neutral-700);
      border: none;
      border-radius: 8px;
      font-size: 14px;
      font-weight: bold;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }
  }
  .is-active {
    .el-radio-button__inner {
      background-color: #fff;
      color: var(--color-600);
      border-radius: 8px;
      font-size: 14px;
      border: none;
      font-weight: bold;
    }
  }
  .el-radio-button__orig-radio:checked + .el-radio-button__inner {
    box-shadow: none;
    -webkit-box-shadow: none;
  }
}
</style>
<style lang="scss">
.testItem-file-popover {
  height: fit-content;
  padding: 10px 15px;
  min-width: unset;
  width: auto;
  text-align: center;
}
</style>
