<template>
  <div class="content-wrap-layout">
    <!-- 分类区 -->
    <page-table
      ref="table"
      :default-selected-arr="defaultSelectedArr"
      :data="data"
      default-selected-key="pointId"
      @refresh="refresh"
      @link-event="linkEvent"
      @on-select="tabelSelect"
      @on-current="tabelCurrent"
      @call="confirm"
      @close="drawerClose"
    />
  </div>
</template>

<script>
import pageTable from './select_skill.vue'
export default {
  // 能力评估
  name: 'SelectSkill',
  components: {
    pageTable
  },
  props: {
    showData: {
      type: Array
    },
    data: {
      type: Array
    }
  },
  data() {
    return {
      selectItem: [],
      defaultSelectedArr: [],
      categoryId: ''
    }
  },
  created() {
    this.defaultSelectedArr = this.showData.map(item => item.pointId)
  },
  methods: {
    switchCategory: function(value) {
      this.categoryId = value
      this.$nextTick(() => {
        this.$refs['table'].getList()
      })
    },
    // 侧拉选择资源回调
    'confirm': function(name, data) {
      this.$emit('call', name, data)
    },
    // 列表点击
    linkEvent: function({ name, row, params }) {
      this.$router.push({ name: name, params: params })
    },
    // 返回已选
    tabelSelect: function(data) {
      this.selectItem = data
    },
    // 返回单选
    tabelCurrent: function(row) {
      this.selectItem = [row]
    },
    drawerClose() {
      this.$emit('close')
    },
    // action menu 事件
    actionHandler: function(type, data) {
      switch (type) {
        case 'refresh':
          this.$refs['table'].getList()
      }
    },
    refresh: function() {}
  }
}
</script>
