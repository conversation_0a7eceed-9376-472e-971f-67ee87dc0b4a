<template>
  <div v-loading="loading" class="drawer-wrap">
    <course-table
      ref="table"
      :height="null"
      :link="false"
      @on-select="onSelect"
    />
    <div class="drawer-footer">
      <div>
        <el-button :disabled="!selectedItem.length" type="primary" @click="confirm">确定</el-button>
        <el-button type="text" @click="close">取消</el-button>
      </div>
    </div>
  </div>
</template>
<script>
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
import { addUnitContentRelApi } from '@/api/teacher/index.js'
import courseTable from '@/views/admin/hrm/salary/course-management/detail/addContentData/index.vue'
export default {
  components: {
    courseTable
  },
  mixins: [mixinsActionMenu],
  props: {
    // 传入数据
    data: {
      type: Array
    },
    existContentIdList: {
      type: Array,
      default: () => []
    },
    courseId: {
      type: String,
      default: ''
    },
    // 新增的下标
    addIndex: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      loading: false,
      id: 1,
      treeData: [],
      selectedItem: [],
      addItemId: '',
      existContentNameStr: ''
    }
  },
  methods: {
    onSelect(data) {
      this.selectedItem = data
    },
    close: function() {
      this.$emit('close')
    },
    confirm: function() {
      // 重复课程名称字符串
      this.existContentNameStr = ''
      this.selectedItem.filter(item => this.existContentIdList.includes(item.id)).map(val => {
        this.existContentNameStr += `“${val.name}”、`
      })
      this.existContentNameStr = this.existContentNameStr.slice(0, -1)
      // 所选课程有重复就不调接口
      if (this.existContentNameStr.length) {
        this.$message.error(`课程内容${this.existContentNameStr}已添加`)
        return
      }
      this.loading = true
      // 去除重复课程
      const contentIdList = this.selectedItem.map((item, index) => {
        return { contentId: item.id, sort: index + this.addIndex }
      }).filter(val => !this.existContentIdList.includes(val))
      const params = {
        courseId: this.courseId,
        contentIdList: contentIdList,
        unitId: this.data[0].id
      }
      addUnitContentRelApi(params).then((res) => {
        if (res.code === 0 || res.code === 200) {
          this.$message.success('课程内容添加成功')
          this.$emit('call', 'refresh')
          this.close()
        }
      }).catch(() => {
        this.close()
        this.$emit('call', 'refresh')
        this.loading = false
      })
    }
  }
}
</script>
<style scoped>
.drawer-footer{
  display: flex;
  align-items: center;
  height: 8%;
}
</style>
