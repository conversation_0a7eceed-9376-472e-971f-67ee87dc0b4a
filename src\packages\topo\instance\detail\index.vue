<template>
  <detail-view
    ref="detail"
    :id="insId"
    :data="insData[0]"
    :view-item="viewItem"
    :type="type"
  />
</template>
<script>
import detailView from './detail_view'
import detailOverview from './detail_overview.vue'
import detailSnapshot from './detail_snapshot_chain'
import moduleConf from '../config.js'
export default {
  components: {
    detailView,
    detailOverview,
    detailSnapshot
  },
  props: {
    insId: String,
    insData: Array,
    type: String
  },
  data() {
    return {
      // 当前模块名称
      moduleName: moduleConf.name,
      viewItem: []
    }
  },
  watch: {
    'insId': function() {
      this.loadBase()
    }
  },
  created() {
    this.loadBase()
  },
  methods: {
    'loadBase': function() {
      this.viewItem = [
        {
          transName: '概况',
          name: 'overview',
          component: detailOverview
        }
      ]
      const data = this.insData[0]
      if (data && data.resource_type === 'vnf' && data.virtual_type === 'qemu') {
        this.viewItem.push({
          transName: '快照链',
          name: 'snapshot',
          component: detailSnapshot
        })
      } else {
        if (this.$refs.detail.tabsActive === 'snapshot') {
          this.$refs.detail.tabsActive = 'overview'
        }
      }
    }
  }
}
</script>
