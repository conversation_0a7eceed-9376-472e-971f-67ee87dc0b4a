import request from '@/utils/request'
const headers = { 'Content-Type': 'application/json;charset=UTF-8' }

// 获取用户token
export function getUserToken(params) {
  return request({
    url: '/authorization/getUserToken',
    method: 'get',
    params: params
  })
}

// 分页查询赛事列表
export function queryBigMatch(data) {
  return request({
    url: '/match/bigMatch/queryPage',
    method: 'post',
    data,
    headers
  })
}

// 创建赛事
export function createBigMatch(data) {
  return request({
    url: '/match/bigMatch/create',
    method: 'post',
    data,
    headers
  })
}

// 查询可选主裁判和研判人
export function queryMatchJudge(data) {
  return request({
    url: '/match/bigMatch/queryUserWithoutPlayer',
    method: 'post',
    data,
    headers
  })
}

// 更新赛事
export function updateBigMatch(data) {
  return request({
    url: '/match/bigMatch/update',
    method: 'post',
    data,
    headers
  })
}

// 删除赛事
export function deleteBigMatch(data) {
  return request({
    url: '/match/bigMatch/remove',
    method: 'post',
    data,
    headers
  })
}

// 发布赛事
export function publishBigMatch(data) {
  return request({
    url: '/match/bigMatch/release',
    method: 'post',
    data,
    headers
  })
}

// 取消发布赛事
export function unPublishedBigMatch(data) {
  return request({
    url: '/match/bigMatch/cancelRelease',
    method: 'post',
    data,
    headers
  })
}

// 赛事详情
export function queryBigMatchDetail(query) {
  return request({
    url: '/match/bigMatch/get',
    method: 'get',
    params: query
  })
}

// 赛事排行范围设置
export function setRankingRange(data) {
  return request({
    url: '/match/bigMatch/setRankingRange',
    method: 'post',
    data,
    headers
  })
}

// 分页查询比赛列表
export function queryMatchList(data) {
  return request({
    url: '/match/bigMatchBout/queryPage',
    method: 'post',
    data,
    headers
  })
}

// 查询比赛可晋级列表
export function queryPromotionMatchSeasonList(data) {
  return request({
    url: '/match/bigMatchBout/queryPromotionMatchSeasonList',
    method: 'post',
    data,
    headers
  })
}



// 总和成绩列表
export function bigMatchScoreQueryPage(data) {
  return request({
    url: '/match/bigMatchScore/queryPage',
    method: 'post',
    data,
    headers
  })
}

// 创建比赛
export function createBigMatchBout(data) {
  return request({
    url: '/match/bigMatchBout/create',
    method: 'post',
    data,
    headers
  })
}

// 查询比赛单条数据
export function queryBigMatchById(query) {
  return request({
    url: '/match/bigMatchBout/get',
    method: 'get',
    params: query
  })
}

// 修改比赛列表数据
export function editBigMatchBout(data) {
  return request({
    url: '/match/bigMatchBout/update',
    method: 'post',
    data,
    headers
  })
}

// 删除比赛列表数据
export function deleteBigMatchBout(data) {
  return request({
    url: '/match/bigMatchBout/remove',
    method: 'post',
    data,
    headers
  })
}

// 修改比赛-比赛规则
export function updateBigMatchSeasonRule(data) {
  return request({
    url: '/match/bigMatchBout/updateBigMatchSeasonRule',
    method: 'post',
    data,
    headers
  })
}

// 比赛延迟功能
export function extraTimeOfSeason(data) {
  return request({
    url: '/match/bigMatchBout/extraTimeOfSeason',
    method: 'post',
    data,
    headers
  })
}

// 查询比赛赛制规则
export function getBigMatchSeasonRuleByBigMatchSeasonId(query) {
  return request({
    url: '/match/bigMatchBout/getBigMatchSeasonRuleByBigMatchSeasonId',
    method: 'get',
    params: query
  })
}

// 设置比赛权重(战队)
export function setSeasonProportion(data) {
  return request({
    url: '/match/bigMatchBout/updateProportion',
    method: 'post',
    data,
    headers
  })
}

// 设置比赛权重(个人)
export function setSeasonProportionPerson(data) {
  return request({
    url: '/match/bigMatchBout/updateProportionPerson',
    method: 'post',
    data,
    headers
  })
}

// 设置比赛上限
export function setSeasonUpperLimitScore(data) {
  return request({
    url: '/match/bigMatchBout/updateUpperLimitScore',
    method: 'post',
    data,
    headers
  })
}
// 比赛成绩列表-团队
export function querySeasonTeamScorePage(data) {
  return request({
    url: '/match/bigMatchSeasonTeamPlayerRelation/querySeasonTeamPage',
    method: 'post',
    data,
    headers
  })
}

// 比赛成绩列表-个人
export function querySeasonPlayerScorePage(data) {
  return request({
    url: '/match/bigMatchSeasonTeamPlayerRelation/querySeasonPlayerPage',
    method: 'post',
    data,
    headers
  })
}

// 设为晋级-团队
export function setPromotionTeam(data) {
  return request({
    url: '/match/bigMatchSeasonTeamPlayerRelation/promotionTeam',
    method: 'post',
    data,
    headers
  })
}

// 设为晋级-个人
export function setPromotionPlayer(data) {
  return request({
    url: '/match/bigMatchSeasonTeamPlayerRelation/promotionPlayer',
    method: 'post',
    data,
    headers
  })
}

// 启动比赛
export function startMatch(data) {
  return request({
    url: '/match/bigMatchBout/begin',
    method: 'post',
    data,
    headers
  })
}

// 结束比赛
export function finishMatch(data) {
  return request({
    url: '/match/bigMatchBout/finish',
    method: 'post',
    data,
    headers
  })
}

// 关闭比赛
export function closeMatch(data) {
  return request({
    url: '/match/bigMatchBout/close',
    method: 'post',
    data,
    headers
  })
}

// 暂停比赛
export function pauseMatch(data) {
  return request({
    url: '/match/bigMatchBout/pause',
    method: 'post',
    data,
    headers
  })
}

// 恢复比赛
export function restoreMatch(data) {
  return request({
    url: '/match/bigMatchBout/restore',
    method: 'post',
    data,
    headers
  })
}

// 比赛准备资源
export function createMatchTopology(data) {
  return request({
    url: '/match/bigMatchBout/generateTopology',
    method: 'post',
    data,
    headers
  })
}

// 比赛下的阶段列表
export function queryMatchSceneInstanceVOPage(data) {
  return request({
    url: '/match/bigMatchBout/queryMatchSceneInstanceVOPage',
    method: 'post',
    data,
    headers
  })
}

// 比赛释放资源
export function releaseMatchTopology(data) {
  return request({
    url: '/match/bigMatchBout/releaseTopology',
    method: 'post',
    data,
    headers
  })
}

// 核对综合成绩
export function verifyScore(data) {
  return request({
    url: '/match/bigMatchScore/verifyScore',
    method: 'post',
    data,
    headers
  })
}

// 计算综合成绩
export function scoreComputation(data) {
  return request({
    url: '/match/bigMatchScore/scoreComputation',
    method: 'post',
    data,
    headers
  })
}

// 发布综合成绩
export function publishResults(data) {
  return request({
    url: '/match/bigMatchScore/publishResults',
    method: 'post',
    data,
    headers
  })
}

// 取消发布综合成绩
export function cancelPublishResults(data) {
  return request({
    url: '/match/bigMatchScore/cancelPublishResults',
    method: 'post',
    data,
    headers
  })
}

// 导出战队赛战队综合成绩
export function exportMatchTeamScore(data) {
  return request({
    url: '/match/bigMatchScore/exportMatchTeamScore',
    method: 'post',
    headers,
    data,
    responseType: 'blob'
  })
}

// 导出战队赛个人综合成绩
export function exportMatchTeamUserScore(data) {
  return request({
    url: '/match/bigMatchScore/exportMatchTeamUserScore',
    method: 'post',
    headers,
    data,
    responseType: 'blob'
  })
}

// 导出个人赛综合成绩
export function exportMatchUserScore(data) {
  return request({
    url: '/match/bigMatchScore/exportMatchUSerScore',
    method: 'post',
    headers,
    data,
    responseType: 'blob'
  })
}

// 导出排行榜
export function exportMatchRank(data) {
  return request({
    url: '/match/bigMatchScore/exportMatchRank',
    method: 'post',
    headers,
    data,
    responseType: 'blob'
  })
}

// 赛事颁奖
export function bigMatchAwards(data) {
  return request({
    url: '/match/bigMatchAwards/awards',
    method: 'post',
    data,
    headers
  })
}

// 比赛人员管理 战队详情列表
export function pageByTeamAndBigMatchSeasion(data) {
  return request({
    url: '/match/bigMatchSeasonTeamPlayerRelation/pageByTeamAndBigMatchSeason',
    method: 'post',
    data,
    headers
  })
}

// 比赛人员管理 删除战队成员
export function removeTeamPlayer(data) {
  return request({
    url: '/match/bigMatchSeasonTeamPlayerRelation/removeTeamPlayer',
    method: 'post',
    data,
    headers
  })
}

// 比赛人员管理 添加战队成员
export function addTeamPlayer(data) {
  return request({
    url: '/match/bigMatchSeasonTeamPlayerRelation/addTeamPlayer',
    method: 'post',
    data,
    headers
  })
}

// 比赛人员管理 查询战队队长
export function getCaptainByTeamAndBigMatchSeason(data) {
  return request({
    url: '/match/bigMatchSeasonTeamPlayerRelation/getCaptainByTeamAndBigMatchSeason',
    method: 'post',
    data,
    headers
  })
}

// 比赛人员管理 战队设置队长
export function changeCaptain(data) {
  return request({
    url: '/match/bigMatchSeasonTeamPlayerRelation/changeCaptain',
    method: 'post',
    data,
    headers
  })
}

// 比赛晋级名单团队列表
export function querySeasonPromotionTeamPage(data) {
  return request({
    url: '/match/bigMatchSeasonTeamPlayerRelation/querySeasonPromotionTeamPage',
    method: 'post',
    data,
    headers
  })
}

// 比赛晋级名单个人列表
export function querySeasonPromotionPlayerPage(data) {
  return request({
    url: '/match/bigMatchSeasonTeamPlayerRelation/querySeasonPromotionPlayerPage',
    method: 'post',
    data,
    headers
  })
}

// 设为晋级战队
export function confirmPromotionTeam(data) {
  return request({
    url: '/match/bigMatchSeasonTeamPlayerRelation/confirmPromotionTeam',
    method: 'post',
    data,
    headers
  })
}

// 设为晋级个人
export function confirmPromotionPlayer(data) {
  return request({
    url: '/match/bigMatchSeasonTeamPlayerRelation/confirmPromotionPlayer',
    method: 'post',
    data,
    headers
  })
}

// 取消晋级团队
export function cancelPromotionTeam(data) {
  return request({
    url: '/match/bigMatchSeasonTeamPlayerRelation/cancelPromotionTeam',
    method: 'post',
    data,
    headers
  })
}

// 取消晋级个人
export function cancelPromotionPlayer(data) {
  return request({
    url: '/match/bigMatchSeasonTeamPlayerRelation/cancelPromotionPlayer',
    method: 'post',
    data,
    headers
  })
}

// 计算比赛成绩
export function calculateScore(data) {
  return request({
    url: '/match/bigMatchBout/calculateScore',
    method: 'post',
    data,
    headers
  })
}

// 设置参与比赛计算阶段
export function setCalculateRule(data) {
  return request({
    url: '/match/bigMatchBout/setCalculateRule',
    method: 'post',
    data,
    headers
  })
}

// 自定义奖项按钮列表
export function bigMatchAwardsQueryPage(data) {
  return request({
    url: '/match/bigMatchAwards/queryPage',
    method: 'post',
    data,
    headers
  })
}

// 优胜单位排行榜接口
export function queryUnitPage(data) {
  return request({
    url: '/match/bigMatchScore/queryUnitPage',
    method: 'post',
    data,
    headers
  })
}

// 创建奖项接口
export function bigMatchAwardsCreate(data) {
  return request({
    url: '/match/bigMatchAwards/batchOperation',
    method: 'post',
    data,
    headers
  })
}

// 获奖总览
export function awardsOverview(data) {
  return request({
    url: '/match/bigMatchAwards/awardsOverview',
    method: 'post',
    data,
    headers
  })
}

// 查询比赛靶机总数量
export function getTargetTotal(data) {
  return request({
    url: '/scene/sceneBasicInfoMatchInstance/getTargetTotal',
    method: 'post',
    data,
    headers
  })
}

// 查询比赛靶机总数量 - 释放资源
export function getTargetTotalAtReleaseResources(data) {
  return request({
    url: '/scene/sceneBasicInfoMatchInstance/getTargetTotalAtReleaseResources',
    method: 'post',
    data,
    headers
  })
}

// 重置比赛状态
export function resetMatchStatusApi(data) {
  return request({
    url: '/match/bigMatchBout/resetStatus',
    method: 'post',
    data,
    headers
  })
}

// 取消核对成绩
export function cancelVerifyScore(data) {
  return request({
    url: '/match/bigMatchScore/cancelVerifyScore',
    method: 'post',
    data,
    headers
  })
}

export function querySceneBankType(query) {
  return request({
    url: '/scene/sceneBasicInfoInstance/querySceneBankType',
    method: 'get',
    params: query
  })
}

// 查询比赛下 战队以及战队下所有人员
export function querySeasonTeamAndPlayerList(data) {
  return request({
    url: '/match/bigMatchSeasonTeamPlayerRelation/querySeasonTeamAndPlayerList',
    method: 'post',
    data,
    headers
  })
}

// 查询是否有没有战队的人
export function checkAllPlayerHaveTeam(data) {
  return request({
    url: '/match/bigMatchSeasonTeamPlayerRelation/checkAllPlayerHaveTeam',
    method: 'post',
    data,
    headers
  })
}

// 取消个人成绩
export function cancelSeasonPlayerScore(data) {
  return request({
    url: '/match/bigMatchSeasonTeamPlayerRelation/cancelSeasonPlayerScore',
    method: 'post',
    data,
    headers
  })
}

// 取消战队成绩
export function cancelSeasonTeamScore(data) {
  return request({
    url: '/match/bigMatchSeasonTeamPlayerRelation/cancelSeasonTeamScore',
    method: 'post',
    data,
    headers
  })
}

// 赛事评价列表
export function queryEvaluationList(data) {
  return request({
    url: '/scene/sceneInstanceSuggest/queryPage',
    method: 'post',
    data,
    headers
  })
}
