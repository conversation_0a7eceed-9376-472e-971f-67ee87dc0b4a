<template>
  <div class="dialog-wrap">
    <div class="sections">
      <el-form ref="form" v-model="formData" :rules="rules" label-width="80px">
        <el-form-item label="附件" prop="file">
          <div class="file-up">
            <el-button type="ghost" @click="selectFile()">上传文件</el-button>
          </div>
          <div v-show="file.name" :style="{ 'border-color': '#abdcff', 'background-color': '#f0faff' }" class="file-container">
            <i :style="{ 'color': '#2d8cf0' }" class="el-icon-document" size="16" />
            <span :title="file.name" style="margin: 10px 10px 0 0;">{{ file.name }}</span>
            <i :style="{ 'color': '#2d8cf0' }" class="el-icon-delete delete-icon delete" @click.stop="clearFileName()" />
          </div>
        </el-form-item>
      </el-form>
    </div>
    <input
      id="importInputFile"
      ref="userFileInput"
      type="file"
      @change="handleChange">
    <div class="dialog-footer">
      <el-button @click="closeView">取消</el-button>
      <el-button type="primary" @click="sureClick">确定</el-button>
    </div>
  </div>
</template>

<script>
import validate from '@/packages/validate'
import { importQuestion, delErrorExcel } from '@/api/accumulate/questionBank'
export default {
  name: 'UploadReport',
  components: {},
  props: {
    show: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      validate: validate,
      maxSize: 1024 * 1024 * 10, // 文件最大大小
      loading: false,
      fileTypes: [],
      showDialog: false,
      file: { name: '' },
      formData: {
      },
      rules: {
        file: [validate.required(['blur', 'change'])]
      },
      resultData: null
    }
  },
  computed: {
  },
  watch: {
    show: function(val) {
      this.showDialog = val
      this.resetData()
    }
  },
  created() {
  },
  methods: {
    clearFileName() {
      this.file = { name: '' }
      this.$refs.userFileInput.value = ''
    },
    sureClick() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          console.log('formData', this.formData)
          console.log('file', this.file)
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    updateFile(result) {
      if (!this.file.name) {
        this.$message.error('请选择导入文件')
      } else {
        this.loading = true
        const formData = new FormData()
        formData.append('file', this.file)
        formData.append('questionDepotId', this.formData.questionDepotId)
        importQuestion(formData).then(res => {
          this.loading = false
          if (result) {
            result(res)
          }
          this.$emit('success')
        }).catch(() => {
          if (result) {
            result(false)
          }
          this.loading = false
        })
      }
    },

    /**
     * 选择文件
     */
    selectFile() {
      this.$refs.userFileInput.click()
    },

    handleChange(e) {
      const files = e.target.files
      if (!files || !files.length) {
        return
      }
      const fileArr = files[0].name.split('.')
      const fileType = fileArr[fileArr.length - 1]
      // if (!this.fileTypes.includes(fileType.toLowerCase())) {
      //   this.$message.error('支持上传.xlsx、.xls文件')
      //   return
      // }
      if (this.maxSize && files[0].size > this.maxSize) {
        this.$message.error(`文件大小不能超过${this.$options.filters['transStore'](this.maxSize, 'B', 0)}`)
        return
      } else {
        this.file = files[0]
      }
    },

    /**
     * 关闭
     */
    closeView() {
      if (this.resultData && this.resultData.token) {
        const formData = new FormData()
        formData.append('token', this.resultData.token)
        delErrorExcel(formData).then(res => {}).catch(() => {})
      }
      this.$emit('close')
    },

    /**
     * 重置数据
     */
    resetData() {
      this.file = { name: '' }
      this.resultData = null
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep .el-form-item__content {
  line-height: 40px !important;
}

.sections {
  font-size: 14px;
  min-height: 40px;
  padding: 0 20px;
  .file-up {
    display: flex;
    align-items: center;
  }
  .file-container {
    overflow-wrap: break-word;
    word-break: normal;
    line-height: 1.5;
    border: 1px solid;
    margin-top: 5px;
    padding: 6px 18px 6px 10px;
    overflow: hidden;
    text-overflow:ellipsis;
    white-space: nowrap;
    cursor: pointer;
    position: relative;
    .delete {
      position: absolute;
      top: 10px;
      right: 10px;
    }
  }
  .download {
    cursor: pointer;
    color: #2362fb;
    margin-left: 20px;
  }

  /deep/ .el-loading-spinner {
    top: 45%;
    .el-icon-loading {
      font-size: 40px;
      color: #999;
    }

    .el-loading-text {
      color: #333;
    }
  }
}

#importInputFile {
  display: none;
}
</style>
