export default {
  name: 'IssuesList',
  // 严重程度映射
  levelArray: [
    { label: '致命', value: '致命', type: 'danger' },
    { label: '严重', value: '严重', type: 'warning' },
    { label: '一般', value: '一般', type: 'primary' },
    { label: '轻微', value: '轻微', type: 'info' }
  ],
  levelArr: [
    { label: '致命', value: 4, type: 'danger' },
    { label: '严重', value: 3, type: 'warning' },
    { label: '一般', value: 2, type: 'primary' },
    { label: '轻微', value: 1, type: 'info' }
  ],
  get levelStrArr() {
    return this.levelArr.map(item => {
      return { label: item.label, value: String(item.value), type: item.type }
    })
  },
  // 将数组转换为对象形式，方便查找
  get levelObj() {
    return this.levelArr.reduce((acc, prev) => {
      acc[prev.value] = prev
      return acc
    }, {})
  },
  get levelTextObj() {
    return this.levelArray.reduce((acc, prev) => {
      acc[prev.value] = prev
      return acc
    }, {})
  },

  // 状态映射
  statusArray: [
    { label: '待审核', value: '待审核', type: 'danger' },
    { label: '激活', value: '激活', type: 'danger' },
    { label: '已修复', value: '已修复', type: 'success' },
    { label: '已拒绝', value: '已拒绝', type: 'danger' },
    { label: '已关闭', value: '已关闭', type: 'info' },
    { label: '待更新', value: '待更新', type: 'info' }
  ],
  // 状态映射
  statusArr: [
    { label: '待审核', value: 0, type: 'danger' },
    { label: '激活', value: 1, type: 'danger' },
    { label: '已修复', value: 4, type: 'success' },
    { label: '已拒绝', value: 2, type: 'danger' },
    { label: '已关闭', value: 6, type: 'info' },
    { label: '待更新', value: 3, type: 'info' }
  ],
  get statusStrArr() {
    return this.statusArr.map(item => {
      return { label: item.label, value: String(item.value), type: item.type }
    })
  },
  get statusSearchArr() {
    return this.statusStrArr.filter(item => ['1', '4', '6'].includes(item.value))
  },
  get statusObj() {
    return this.statusArr.reduce((acc, prev) => {
      acc[prev.value] = prev
      return acc
    }, {})
  },
  get statusTextObj() {
    return this.statusArray.reduce((acc, prev) => {
      acc[prev.value] = prev
      return acc
    }, {})
  },

  // 问题类型映射
  typeArray: [
    { label: '安全问题', value: '安全问题', type: 'danger' },
    { label: '功能问题', value: '功能问题', type: 'danger' },
    { label: '性能问题', value: '性能问题', type: 'success' },
    { label: '漏洞问题', value: '漏洞问题', type: 'danger' },
    { label: '已关闭', value: '已关闭', type: 'info' }
  ],
  // 状态映射
  typeArr: [
    { label: '安全漏洞', value: 1, type: 'info' },
    { label: '功能问题', value: 2, type: 'info' },
    { label: '性能问题', value: 3, type: 'info' }
  ],
  get typeStrArr() {
    return this.typeArr.map(item => {
      return { label: item.label, value: String(item.value), type: item.type }
    })
  },
  get typeObj() {
    return this.typeArr.reduce((acc, prev) => {
      acc[prev.value] = prev
      return acc
    }, {})
  },
  get typeTextObj() {
    return this.typeArray.reduce((acc, prev) => {
      acc[prev.value] = prev
      return acc
    }, {})
  }
}
