<template>
<!--  侧拉显示模式-->
  <div v-if="mode === 'drawer'" class="detail-tabs-wrap" v-loading="loading">
    <template v-if="data">
      <h3 class="detail-title">
        <template v-if="!labelName">{{$t(appName + '.' + moduleName) + '：' + data[titleKey]}}</template>
        <template v-else>{{ labelName  + '：' + data[titleKey] }}</template>
      </h3>
      <Tabs v-model="tabsActive" type="card" class="detail-tabs-top" @on-click="handleTabClick">
        <TabPane v-for="item in viewItem" :label="item.transName" :name="item.name" :key="item.name"></TabPane>
      </Tabs>
      <component v-for="item in viewItem" :key="item.name" :is="item.component" class="detail-tabs-content" :id="id" :data="data" :tabName="item.name" v-if="tabsActive === item.name"></component>
    </template>
    <div class="no-data-tooltip" v-else><span>暂无数据</span></div>
  </div>
<!--  独立路由显示模式-->
  <div v-else class="content-wrap-layout" v-loading="loading">
    <div class="detail-wrap" v-if="data">
      <!--      title start-->
      <el-page-header
        class="detail-header"
        @back="goBack"
      >
        <template slot="content">
          <slot></slot>
          <span v-if="!labelName">{{$t(appName + '.' + moduleName) + '：' + data[titleKey]}}</span>
          <span v-else>{{ labelName  + '：' + data[titleKey] }}</span>
        </template>
      </el-page-header>
      <!--      title end-->
      <Tabs v-model="tabsActive" @on-click="handleTabClick">
        <TabPane v-for="item in viewItem" :label="item.transName" :name="item.name" :key="item.name"></TabPane>
      </Tabs>
      <component v-for="item in viewItem" :key="item.name" :is="item.component" class="detail-tabs-content" :id="id" :data="data" :tabName="item.name" v-if="tabsActive === item.name"></component>
      <!-- <div>
        <el-tabs v-model="tabsActive" type="card" @tab-click="handleTabClick">
          <el-tab-pane v-for="item in viewItem" :label="item.transName" :name="item.name" :key="item.name">
            <component :is="item.component" class="detail-tabs-content" :id="id" :data="data" v-if="tabsActive === item.name"></component>
          </el-tab-pane>
        </el-tabs>
      </div> -->
    </div>
  </div>
</template>
<style lang="less">
.detail-wrap {
  height: 100%;
  display: flex;
  flex-direction: column;
  > .ivu-tabs {
    // margin-left: -15px;
    .ivu-tabs-bar {
      margin-bottom: 0px;
    }
    .ivu-tabs-tab {
      padding: 14px 12px;
    }
    .ivu-tabs-tab-active {
      font-weight: bold;
      font-size: 15px;
      color: #181a1d;
    }
  }
  .detail-tabs-content {
    margin: 0 -15px;
    padding: 16px 15px 0;
    overflow: hidden;
    flex: 1;
    .resource-table {
      height: 100%;
    }
  }
  .detail-header {
    padding: 20px 0;
    .el-page-header__content {
      font-size: 15px;
      display: flex;
      align-items: center;
    }
  }
}
.detail-tabs-wrap {
  .no-data-tooltip {
    height: 100%;
    display: table;
    span {
      display: table-cell;
      font-size: 16px;
      text-align: center;
      vertical-align:middle;
    }
  }
}
</style>
<script>
import { mapGetters } from 'vuex'
export default {
  props: {
    // 载入状态
    loading: Boolean,
    // 展示模式 Drawer侧拉 router独立路由
    mode: {
      type: String,
      default: 'drawer'
    },
    // 页头默认展示key
    titleKey: {
      type: String,
      default: 'name'
    },
    // 模块名称
    moduleName: String,
    labelName: {
      type: String,
      default: ''
    },
    // 资源ID
    id: String,
    // 资源数据对象
    data: {
      type: Object,
      default: () => {
        return null
      }
    },
    // 显示标签页
    viewItem: {
      type: Array,
      default: () => {
        return []
      }
    },
    // 是否需要路由
    router: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      tabsActive: 'overview'       // 标签页默认Key
    }
  },
  computed: {
    ...mapGetters('global', [
      'appName'
    ])
  },
  created() {
    try {
      this.tabsActive = this.$route['params']['view'] || this.viewItem[0]['name']
    } catch (err) {
      if (this.viewItem.length && this.viewItem[0].hasOwnProperty('name')) {
        this.tabsActive = this.viewItem[0]['name']
      }
    }
  },
  watch: {
    '$route': function (val, oldVal) {
      if (val.params.view !== oldVal.params.view && val.params.id === oldVal.params.id) {
        this.tabsActive = val.params.view
        this.handleTabClick(val.params.view)
      }
    }
  },
  methods: {
    'handleTabClick': function (data) {
      if (this.router) {
        this.$router.replace({ name: this.$route.name, params: { 'id': this.id, 'view': data }})
      }
    },
    'goBack': function() {
      this.$router.go(-1)
    }
  }
}
</script>
