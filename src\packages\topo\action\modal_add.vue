<template>
  <div v-loading="loading" class="dialog-wrap">
    <el-form ref="form" :model="formData" :rules="rules" label-position="left" label-width="80px" @submit.native.prevent>
      <el-form-item label="名称" prop="sceneRoleName">
        <el-input v-model.trim="formData.sceneRoleName" show-word-limit/>
      </el-form-item>
    </el-form>
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>
<script>
import module from '../config.js'
import validate from '../../validate'
import { createSceneRoleAPI, updateSceneRoleAPI } from '../api/role'
export default {
  components: {},
  mixins: [],
  props: {
    // 传入数据
    data: {
      type: Object,
      default: () => {
        return {}
      }
    },
    name: {
      type: String
    },
    classCode: {
      type: String
    }
  },
  data() {
    return {
      moduleName: module.name,
      loading: false,
      validate: validate,
      formData: {
        sceneRoleName: ''
      },
      rules: {
        sceneRoleName: [validate.required(), validate.name_64_char]
      },
      apiType: createSceneRoleAPI
    }
  },
  computed: {
    editMode: function() {
      return this.name === 'editSceneRole'
    }
  },
  created() {
    if (this.editMode && this.data) {
      this.formData['sceneRoleName'] = this.data['sceneRoleName']
    }
  },
  methods: {
    close: function() {
      this.$emit('close')
    },
    confirm: function() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.loading = true
          const postData = { sceneRoleName: this.formData.sceneRoleName, initRoleId: this.data.initRoleId, sceneId: this.data.sceneId }
          if (this.name === 'editSceneRole') {
            this.apiType = updateSceneRoleAPI
            postData.id = this.data.roleId
          }
          this.apiType(postData).then(res => {
            this.$message.success(`${this.name === 'addSceneRole' ? '添加角色' : '编辑角色'}成功`)
            this.$emit('call', 'sceneRole')
            this.close()
          }).catch(() => {
            this.loading = false
          })
        }
      })
    }
  }
}
</script>
