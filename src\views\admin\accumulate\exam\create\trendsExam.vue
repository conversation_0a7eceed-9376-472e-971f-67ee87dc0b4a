<template>
  <div class="_paper_container card-bg">
    <div class="_paper_header">
      <div class="_paper_search">
        <div class="_paper_search_1">
          总题数
          <span style="font-weight: bold; color: var(--color-600); font-size: 18px; margin-right: 10px;">
            {{ total }}
          </span>
        </div>
        <div class="_paper_search_1">
          总分数
          <span style="font-weight: bold; color: var(--color-600); font-size: 18px;">
            {{ totalScore }}
          </span>
        </div>
      </div>
    </div>
    <el-tabs v-model="activeName">
      <el-tab-pane label="理论" name="theory">
        <el-form ref="theoryForm" :model="theoryRuleVO" :rules="rules" label-position="left" label-width="0" class="dialog-form">
          <div v-for="(item, index) in theoryRuleVO.categoryVOList" :key="index" class="category-wrap mb-10">
            <div class="select-wrap">
              <div class="left">
                <div class="select-title">筛选范围：</div>
                <el-form-item label="题库" label-width="40px" style="margin: 0 25px 0 20px;">
                  <el-select v-model="item.questionDepotId" filterable clearable style="width: 220px;" @change="categorySelect($event, 'questionDepotId', item)">
                    <el-option
                      v-for="item in theoryQuestionCategoryList"
                      :key="item.id"
                      :label="item.questionDepotName"
                      :value="+item.id"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item label="分类" label-width="40px" style="margin: 0 25px 0 20px;">
                  <el-select v-model="item.categoryId" filterable clearable style="width: 220px;" @change="categorySelect($event, 'categoryId', item)">
                    <el-option
                      v-for="item in theoryCategoryList"
                      :key="item.id"
                      :label="item.categoryName"
                      :value="item.id"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item label="技能点" label-width="55px">
                  <el-select v-model="item.skillPointId" filterable clearable style="width: 220px;" @change="categorySelect($event, 'skillPointId', item)">
                    <el-option
                      v-for="item in skillPointList"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    />
                  </el-select>
                </el-form-item>
              </div>
              <el-button v-if="theoryRuleVO.categoryVOList.length > 1" type="primary" icon="el-icon-delete" @click="deleteClass(index)"/>
            </div>
            <div>
              <div class="trends-content border-b" style="padding-bottom: 6px;">
                <div>难易程度</div>
                <div>单选题</div>
                <div>多选题</div>
                <div>判断题</div>
                <div>CTF题</div>
                <div>填空题</div>
                <div>简答题</div>
              </div>
              <div class="trends-content border-b">
                <div>初级</div>
                <el-form-item :rules="rules.primarySingle" :prop="`categoryVOList[${index}][primarySingle]`" class="category">
                  <el-input-number v-model="item.primarySingle" :min="0" size="mini" @input="changeMessage"/> /
                  {{ item.categoryVO.primarySingle || 0 }}
                </el-form-item>
                <el-form-item :rules="rules.primaryMultiple" :prop="`categoryVOList[${index}][primaryMultiple]`" class="category">
                  <el-input-number v-model="item.primaryMultiple" :min="0" size="mini" @input="changeMessage"/> /
                  {{ item.categoryVO.primaryMultiple || 0 }}
                </el-form-item>
                <el-form-item :rules="rules.primaryJudge" :prop="`categoryVOList[${index}][primaryJudge]`" class="category">
                  <el-input-number v-model="item.primaryJudge" :min="0" size="mini" @input="changeMessage"/> /
                  {{ item.categoryVO.primaryJudge || 0 }}
                </el-form-item>
                <el-form-item :rules="rules.primaryCTF" :prop="`categoryVOList[${index}][primaryCTF]`" class="category">
                  <el-input-number v-model="item.primaryCTF" :min="0" size="mini" @input="changeMessage"/> /
                  {{ item.categoryVO.primaryCTF || 0 }}
                </el-form-item>
                <el-form-item :rules="rules.primaryCompletion" :prop="`categoryVOList[${index}][primaryCompletion]`" class="category">
                  <el-input-number v-model="item.primaryCompletion" :min="0" size="mini" @input="changeMessage"/> /
                  {{ item.categoryVO.primaryCompletion || 0 }}
                </el-form-item>
                <el-form-item :rules="rules.primaryShort" :prop="`categoryVOList[${index}][primaryShort]`" class="category">
                  <el-input-number v-model="item.primaryShort" :min="0" size="mini" @input="changeMessage"/> /
                  {{ item.categoryVO.primaryShort || 0 }}
                </el-form-item>
              </div>
              <div class="trends-content border-b">
                <div>中级</div>
                <el-form-item :rules="rules.middleSingle" :prop="`categoryVOList[${index}][middleSingle]`" class="category">
                  <el-input-number v-model="item.middleSingle" :min="0" size="mini" @input="changeMessage"/> /
                  {{ item.categoryVO.middleSingle || 0 }}
                </el-form-item>
                <el-form-item :rules="rules.middleMultiple" :prop="`categoryVOList[${index}][middleMultiple]`" class="category">
                  <el-input-number v-model="item.middleMultiple" :min="0" size="mini" @input="changeMessage"/> /
                  {{ item.categoryVO.middleMultiple || 0 }}
                </el-form-item>
                <el-form-item :rules="rules.middleJudge" :prop="`categoryVOList[${index}][middleJudge]`" class="category">
                  <el-input-number v-model="item.middleJudge" :min="0" size="mini" @input="changeMessage"/> /
                  {{ item.categoryVO.middleJudge || 0 }}
                </el-form-item>
                <el-form-item :rules="rules.middleCTF" :prop="`categoryVOList[${index}][middleCTF]`" class="category">
                  <el-input-number v-model="item.middleCTF" :min="0" size="mini" @input="changeMessage"/> /
                  {{ item.categoryVO.middleCTF || 0 }}
                </el-form-item>
                <el-form-item :rules="rules.middleCompletion" :prop="`categoryVOList[${index}][middleCompletion]`" class="category">
                  <el-input-number v-model="item.middleCompletion" :min="0" size="mini" @input="changeMessage"/> /
                  {{ item.categoryVO.middleCompletion || 0 }}
                </el-form-item>
                <el-form-item :rules="rules.middleShort" :prop="`categoryVOList[${index}][middleShort]`" class="category">
                  <el-input-number v-model="item.middleShort" :min="0" size="mini" @input="changeMessage"/> /
                  {{ item.categoryVO.middleShort || 0 }}
                </el-form-item>
              </div>
              <div class="trends-content">
                <div>高级</div>
                <el-form-item :rules="rules.seniorSingle" :prop="`categoryVOList[${index}][seniorSingle]`" class="category">
                  <el-input-number v-model="item.seniorSingle" :min="0" size="mini" @input="changeMessage"/> /
                  {{ item.categoryVO.seniorSingle || 0 }}
                </el-form-item>
                <el-form-item :rules="rules.seniorMultiple" :prop="`categoryVOList[${index}][seniorMultiple]`" class="category">
                  <el-input-number v-model="item.seniorMultiple" :min="0" size="mini" @input="changeMessage"/> /
                  {{ item.categoryVO.seniorMultiple || 0 }}
                </el-form-item>
                <el-form-item :rules="rules.seniorJudge" :prop="`categoryVOList[${index}][seniorJudge]`" class="category">
                  <el-input-number v-model="item.seniorJudge" :min="0" size="mini" @input="changeMessage"/> /
                  {{ item.categoryVO.seniorJudge || 0 }}
                </el-form-item>
                <el-form-item :rules="rules.seniorCTF" :prop="`categoryVOList[${index}][seniorCTF]`" class="category">
                  <el-input-number v-model="item.seniorCTF" :min="0" size="mini" @input="changeMessage"/> /
                  {{ item.categoryVO.seniorCTF || 0 }}
                </el-form-item>
                <el-form-item :rules="rules.seniorCompletion" :prop="`categoryVOList[${index}][seniorCompletion]`" class="category">
                  <el-input-number v-model="item.seniorCompletion" :min="0" size="mini" @input="changeMessage"/> /
                  {{ item.categoryVO.seniorCompletion || 0 }}
                </el-form-item>
                <el-form-item :rules="rules.seniorShort" :prop="`categoryVOList[${index}][seniorShort]`" class="category">
                  <el-input-number v-model="item.seniorShort" :min="0" size="mini" @input="changeMessage"/> /
                  {{ item.categoryVO.seniorShort || 0 }}
                </el-form-item>
              </div>
            </div>
          </div>
          <div class="add-classification" @click="addClass()">
            + 添加筛选范围
          </div>
        </el-form>
        <div class="mt-15 category-wrap">
          <div style="font-size: 15px; font-weight: bold;">题目总计：</div>
          <div class="mt-10">
            <div class="trends-content border-b" style="padding-bottom: 6px;">
              <div>难易程度</div>
              <div>单选题</div>
              <div>多选题</div>
              <div>判断题</div>
              <div>CTF题</div>
              <div>填空题</div>
              <div>简答题</div>
            </div>
            <div class="trends-content border-b">
              <div>初级</div>
              <div>{{ theoryScore.countPrimarySingle }}</div>
              <div>{{ theoryScore.countPrimaryMultiple }}</div>
              <div>{{ theoryScore.countPrimaryJudge }}</div>
              <div>{{ theoryScore.countPrimaryCTF }}</div>
              <div>{{ theoryScore.countPrimaryCompletion }}</div>
              <div>{{ theoryScore.countPrimaryShort }}</div>
            </div>
            <div class="trends-content border-b">
              <div>中级</div>
              <div>{{ theoryScore.countMiddleSingle }}</div>
              <div>{{ theoryScore.countMiddleMultiple }}</div>
              <div>{{ theoryScore.countMiddleJudge }}</div>
              <div>{{ theoryScore.countMiddleCTF }}</div>
              <div>{{ theoryScore.countMiddleCompletion }}</div>
              <div>{{ theoryScore.countMiddleShort }}</div>
            </div>
            <div class="trends-content border-b">
              <div>高级</div>
              <div>{{ theoryScore.countSeniorSingle }}</div>
              <div>{{ theoryScore.countSeniorMultiple }}</div>
              <div>{{ theoryScore.countSeniorJudge }}</div>
              <div>{{ theoryScore.countSeniorCTF }}</div>
              <div>{{ theoryScore.countSeniorCompletion }}</div>
              <div>{{ theoryScore.countSeniorShort }}</div>
            </div>
            <div class="trends-content  border-b">
              <div>合计</div>
              <div>{{ theoryScore.countPrimarySingle + theoryScore.countMiddleSingle + theoryScore.countSeniorSingle }}</div>
              <div>{{ theoryScore.countPrimaryMultiple + theoryScore.countMiddleMultiple + theoryScore.countSeniorMultiple }}</div>
              <div>{{ theoryScore.countPrimaryJudge + theoryScore.countMiddleJudge + theoryScore.countSeniorJudge }}</div>
              <div>{{ theoryScore.countPrimaryCTF + theoryScore.countMiddleCTF + theoryScore.countSeniorCTF }}</div>
              <div>{{ theoryScore.countPrimaryCompletion + theoryScore.countMiddleCompletion + theoryScore.countSeniorCompletion }}</div>
              <div>{{ theoryScore.countPrimaryShort + theoryScore.countMiddleShort + theoryScore.countSeniorShort }}</div>
            </div>
            <el-form ref="formTheory" :model="theoryRuleVO" :rules="rules" label-position="left" label-width="0" class="dialog-form">
              <div class="trends-content">
                <div>单题分数</div>
                <el-form-item :rules="rules.scoreSingle" prop="scoreSingle" class="category">
                  <el-input-number v-model="theoryRuleVO.scoreSingle" :min="0" size="mini" @input="changeMessage"/> 分
                </el-form-item>
                <el-form-item :rules="rules.scoreMultiple" prop="scoreMultiple" class="category">
                  <el-input-number v-model="theoryRuleVO.scoreMultiple" :min="0" size="mini" @input="changeMessage"/> 分
                </el-form-item>
                <el-form-item :rules="rules.scoreJudge" prop="scoreJudge" class="category">
                  <el-input-number v-model="theoryRuleVO.scoreJudge" :min="0" size="mini" @input="changeMessage"/> 分
                </el-form-item>
                <el-form-item :rules="rules.scoreCTF" prop="scoreCTF" class="category">
                  <el-input-number v-model="theoryRuleVO.scoreCTF" :min="0" size="mini" @input="changeMessage"/> 分
                </el-form-item>
                <el-form-item :rules="rules.scoreCompletion" prop="scoreCompletion" class="category">
                  <el-input-number v-model="theoryRuleVO.scoreCompletion" :min="0" size="mini" @input="changeMessage"/> 分
                </el-form-item>
                <el-form-item :rules="rules.scoreShort" prop="scoreShort" class="category">
                  <el-input-number v-model="theoryRuleVO.scoreShort" :min="0" size="mini" @input="changeMessage"/> 分
                </el-form-item>
              </div>
            </el-form>
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane label="靶机" name="target">
        <el-form ref="targetForm" :model="targetRuleVO" :rules="rules" label-position="left" label-width="0" class="dialog-form">
          <div v-for="(item, index) in targetRuleVO.categoryVOList" :key="index" class="mb-10 category-wrap">
            <div class="select-wrap">
              <div class="left">
                <div class="select-title">筛选范围：</div>
                <el-form-item label="题库" label-width="40px" style="margin: 0 25px 0 20px;">
                  <el-select v-model="item.questionDepotId" filterable clearable style="width: 220px;" @change="categorySelect($event, 'questionDepotId', item)">
                    <el-option
                      v-for="item in targetQuestionCategoryList"
                      :key="item.id"
                      :label="item.questionDepotName"
                      :value="+item.id"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item label="分类" label-width="40px" style="margin: 0 25px 0 20px;">
                  <el-select v-model="item.categoryId" filterable clearable style="width: 220px;" @change="categorySelect($event, 'categoryId', item)">
                    <el-option
                      v-for="item in targetCategoryList"
                      :key="item.id"
                      :label="item.categoryName"
                      :value="item.id"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item label="技能点" label-width="55px">
                  <el-select v-model="item.skillPointId" filterable clearable style="width: 220px;" @change="categorySelect($event, 'skillPointId', item)">
                    <el-option
                      v-for="item in skillPointList"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    />
                  </el-select>
                </el-form-item>
              </div>
              <el-button v-if="targetRuleVO.categoryVOList.length > 1" type="primary" icon="el-icon-delete" @click="deleteClass(index)"/>
            </div>
            <div>
              <div class="trends-content border-b" style="padding-bottom: 6px;">
                <div>难易程度</div>
                <div>CTF题</div>
                <div>AWD题</div>
                <div>漏洞题</div>
                <div>其它题</div>
              </div>
              <div class="trends-content border-b">
                <div>初级</div>
                <el-form-item :rules="rules.primaryCTF" :prop="`categoryVOList[${index}][primaryCTF]`" class="category">
                  <el-input-number v-model="item.primaryCTF" :min="0" size="mini" @input="changeMessage"/> /
                  {{ item.categoryVO.primaryCTF || 0 }}
                </el-form-item>
                <el-form-item :rules="rules.primaryAWD" :prop="`categoryVOList[${index}][primaryAWD]`" class="category">
                  <el-input-number v-model="item.primaryAWD" :min="0" size="mini" @input="changeMessage"/> /
                  {{ item.categoryVO.primaryAWD || 0 }}
                </el-form-item>
                <el-form-item :rules="rules.primaryBug" :prop="`categoryVOList[${index}][primaryBug]`" class="category">
                  <el-input-number v-model="item.primaryBug" :min="0" size="mini" @input="changeMessage"/> /
                  {{ item.categoryVO.primaryBug || 0 }}
                </el-form-item>
                <el-form-item :rules="rules.primaryOther" :prop="`categoryVOList[${index}][primaryOther]`" class="category">
                  <el-input-number v-model="item.primaryOther" :min="0" size="mini" @input="changeMessage"/> /
                  {{ item.categoryVO.primaryOther || 0 }}
                </el-form-item>
              </div>
              <div class="trends-content border-b">
                <div>中级</div>
                <el-form-item :rules="rules.middleCTF" :prop="`categoryVOList[${index}][middleCTF]`" class="category">
                  <el-input-number v-model="item.middleCTF" :min="0" size="mini" @input="changeMessage"/> /
                  {{ item.categoryVO.middleCTF || 0 }}
                </el-form-item>
                <el-form-item :rules="rules.middleAWD" :prop="`categoryVOList[${index}][middleAWD]`" class="category">
                  <el-input-number v-model="item.middleAWD" :min="0" size="mini" @input="changeMessage"/> /
                  {{ item.categoryVO.middleAWD || 0 }}
                </el-form-item>
                <el-form-item :rules="rules.middleBug" :prop="`categoryVOList[${index}][middleBug]`" class="category">
                  <el-input-number v-model="item.middleBug" :min="0" size="mini" @input="changeMessage"/> /
                  {{ item.categoryVO.middleBug || 0 }}
                </el-form-item>
                <el-form-item :rules="rules.middleOther" :prop="`categoryVOList[${index}][middleOther]`" class="category">
                  <el-input-number v-model="item.middleOther" :min="0" size="mini" @input="changeMessage"/> /
                  {{ item.categoryVO.middleOther || 0 }}
                </el-form-item>
              </div>
              <div class="trends-content">
                <div>高级</div>
                <el-form-item :rules="rules.seniorCTF" :prop="`categoryVOList[${index}][seniorCTF]`" class="category">
                  <el-input-number v-model="item.seniorCTF" :min="0" size="mini" @input="changeMessage"/> /
                  {{ item.categoryVO.seniorCTF || 0 }}
                </el-form-item>
                <el-form-item :rules="rules.seniorAWD" :prop="`categoryVOList[${index}][seniorAWD]`" class="category">
                  <el-input-number v-model="item.seniorAWD" :min="0" size="mini" @input="changeMessage"/> /
                  {{ item.categoryVO.seniorAWD || 0 }}
                </el-form-item>
                <el-form-item :rules="rules.seniorBug" :prop="`categoryVOList[${index}][seniorBug]`" class="category">
                  <el-input-number v-model="item.seniorBug" :min="0" size="mini" @input="changeMessage"/> /
                  {{ item.categoryVO.seniorBug || 0 }}
                </el-form-item>
                <el-form-item :rules="rules.seniorOther" :prop="`categoryVOList[${index}][seniorOther]`" class="category">
                  <el-input-number v-model="item.seniorOther" :min="0" size="mini" @input="changeMessage"/> /
                  {{ item.categoryVO.seniorOther || 0 }}
                </el-form-item>
              </div>
            </div>
          </div>
          <div class="add-classification" @click="addClass()">
            + 添加筛选范围
          </div>
        </el-form>
        <div class="mt-15 category-wrap">
          <div style="font-size: 15px; font-weight: bold;">题目总计：</div>
          <div class="mt-10">
            <div class="trends-content border-b" style="padding-bottom: 6px;">
              <div>难易程度</div>
              <div>CTF题</div>
              <div>AWD题</div>
              <div>漏洞题</div>
              <div>其它题</div>
            </div>
            <div class="trends-content border-b">
              <div>初级</div>
              <div>{{ targetScore.countPrimaryCTF }}</div>
              <div>{{ targetScore.countPrimaryAWD }}</div>
              <div>{{ targetScore.countPrimaryBug }}</div>
              <div>{{ targetScore.countPrimaryOther }}</div>
            </div>
            <div class="trends-content border-b">
              <div>中级</div>
              <div>{{ targetScore.countMiddleCTF }}</div>
              <div>{{ targetScore.countMiddleAWD }}</div>
              <div>{{ targetScore.countMiddleBug }}</div>
              <div>{{ targetScore.countMiddleOther }}</div>
            </div>
            <div class="trends-content border-b">
              <div>高级</div>
              <div>{{ targetScore.countSeniorCTF }}</div>
              <div>{{ targetScore.countSeniorAWD }}</div>
              <div>{{ targetScore.countSeniorBug }}</div>
              <div>{{ targetScore.countSeniorOther }}</div>
            </div>
            <div class="trends-content  border-b">
              <div>合计</div>
              <div>{{ targetScore.countPrimaryCTF + targetScore.countMiddleCTF + targetScore.countSeniorCTF }}</div>
              <div>{{ targetScore.countPrimaryAWD + targetScore.countMiddleAWD + targetScore.countSeniorAWD }}</div>
              <div>{{ targetScore.countPrimaryBug + targetScore.countMiddleBug + targetScore.countSeniorBug }}</div>
              <div>{{ targetScore.countPrimaryOther + targetScore.countMiddleOther + targetScore.countSeniorOther }}</div>
            </div>
            <el-form ref="formTarget" :model="targetRuleVO" :rules="rules" label-position="left" label-width="0" class="dialog-form">
              <div class="trends-content">
                <div>单题分数</div>
                <el-form-item :rules="rules.scoreCTF" prop="scoreCTF" class="category">
                  <el-input-number v-model="targetRuleVO.scoreCTF" :min="0" size="mini" @input="changeMessage"/> 分
                </el-form-item>
                <el-form-item :rules="rules.scoreAWD" prop="scoreAWD" class="category">
                  <el-input-number v-model="targetRuleVO.scoreAWD" :min="0" size="mini" @input="changeMessage"/> 分
                </el-form-item>
                <el-form-item :rules="rules.scoreBug" prop="scoreBug" class="category">
                  <el-input-number v-model="targetRuleVO.scoreBug" :min="0" size="mini" @input="changeMessage"/> 分
                </el-form-item>
                <el-form-item :rules="rules.scoreOther" prop="scoreOther" class="category">
                  <el-input-number v-model="targetRuleVO.scoreOther" :min="0" size="mini" @input="changeMessage"/> 分
                </el-form-item>
              </div>
            </el-form>
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane label="仿真" name="simulation">
        <el-form ref="simulationForm" :model="simulationRuleVO" :rules="rules" label-position="left" label-width="0" class="dialog-form">
          <div v-for="(item, index) in simulationRuleVO.categoryVOList" :key="index" class="category-wrap mb-10">
            <div class="select-wrap">
              <div class="left">
                <div class="select-title">筛选范围：</div>
                <el-form-item label="题库" label-width="40px" style="margin: 0 25px 0 20px;">
                  <el-select v-model="item.questionDepotId" filterable clearable style="width: 220px;" @change="categorySelect($event, 'questionDepotId', item)">
                    <el-option
                      v-for="item in simulationQuestionCategoryList"
                      :key="item.id"
                      :label="item.questionDepotName"
                      :value="+item.id"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item label="分类" label-width="40px" style="margin: 0 25px 0 20px;">
                  <el-select v-model="item.categoryId" filterable clearable style="width: 220px;" @change="categorySelect($event, 'categoryId', item)">
                    <el-option
                      v-for="item in simulationCategoryList"
                      :key="item.id"
                      :label="item.categoryName"
                      :value="item.id"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item label="技能点" label-width="55px">
                  <el-select v-model="item.skillPointId" filterable clearable style="width: 220px;" @change="categorySelect($event, 'skillPointId', item)">
                    <el-option
                      v-for="item in skillPointList"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    />
                  </el-select>
                </el-form-item>
              </div>
              <el-button v-if="simulationRuleVO.categoryVOList.length > 1" type="primary" icon="el-icon-delete" @click="deleteClass(index)"/>
            </div>
            <div>
              <div class="trends-content border-b" style="padding-bottom: 6px;">
                <div>难易程度</div>
                <div>单选题</div>
                <div>多选题</div>
                <div>判断题</div>
                <div>CTF题</div>
                <div>填空题</div>
                <div>简答题</div>
                <div>组合题</div>
              </div>
              <div class="trends-content border-b">
                <div>初级</div>
                <el-form-item :rules="rules.primarySingle" :prop="`categoryVOList[${index}][primarySingle]`" class="category">
                  <el-input-number v-model="item.primarySingle" :min="0" size="mini" @input="changeMessage"/> /
                  {{ item.categoryVO.primarySingle || 0 }}
                </el-form-item>
                <el-form-item :rules="rules.primaryMultiple" :prop="`categoryVOList[${index}][primaryMultiple]`" class="category">
                  <el-input-number v-model="item.primaryMultiple" :min="0" size="mini" @input="changeMessage"/> /
                  {{ item.categoryVO.primaryMultiple || 0 }}
                </el-form-item>
                <el-form-item :rules="rules.primaryJudge" :prop="`categoryVOList[${index}][primaryJudge]`" class="category">
                  <el-input-number v-model="item.primaryJudge" :min="0" size="mini" @input="changeMessage"/> /
                  {{ item.categoryVO.primaryJudge || 0 }}
                </el-form-item>
                <el-form-item :rules="rules.primaryCTF" :prop="`categoryVOList[${index}][primaryCTF]`" class="category">
                  <el-input-number v-model="item.primaryCTF" :min="0" size="mini" @input="changeMessage"/> /
                  {{ item.categoryVO.primaryCTF || 0 }}
                </el-form-item>
                <el-form-item :rules="rules.primaryCompletion" :prop="`categoryVOList[${index}][primaryCompletion]`" class="category">
                  <el-input-number v-model="item.primaryCompletion" :min="0" size="mini" @input="changeMessage"/> /
                  {{ item.categoryVO.primaryCompletion || 0 }}
                </el-form-item>
                <el-form-item :rules="rules.primaryShort" :prop="`categoryVOList[${index}][primaryShort]`" class="category">
                  <el-input-number v-model="item.primaryShort" :min="0" size="mini" @input="changeMessage"/> /
                  {{ item.categoryVO.primaryShort || 0 }}
                </el-form-item>
                <el-form-item :rules="rules.primaryCombinatorial" :prop="`categoryVOList[${index}][primaryCombinatorial]`" class="category">
                  <el-input-number v-model="item.primaryCombinatorial" :min="0" size="mini" @input="changeMessage"/> /
                  {{ item.categoryVO.primaryCombinatorial || 0 }}
                </el-form-item>
              </div>
              <div class="trends-content border-b">
                <div>中级</div>
                <el-form-item :rules="rules.middleSingle" :prop="`categoryVOList[${index}][middleSingle]`" class="category">
                  <el-input-number v-model="item.middleSingle" :min="0" size="mini" @input="changeMessage"/> /
                  {{ item.categoryVO.middleSingle || 0 }}
                </el-form-item>
                <el-form-item :rules="rules.middleMultiple" :prop="`categoryVOList[${index}][middleMultiple]`" class="category">
                  <el-input-number v-model="item.middleMultiple" :min="0" size="mini" @input="changeMessage"/> /
                  {{ item.categoryVO.middleMultiple || 0 }}
                </el-form-item>
                <el-form-item :rules="rules.middleJudge" :prop="`categoryVOList[${index}][middleJudge]`" class="category">
                  <el-input-number v-model="item.middleJudge" :min="0" size="mini" @input="changeMessage"/> /
                  {{ item.categoryVO.middleJudge || 0 }}
                </el-form-item>
                <el-form-item :rules="rules.middleCTF" :prop="`categoryVOList[${index}][middleCTF]`" class="category">
                  <el-input-number v-model="item.middleCTF" :min="0" size="mini" @input="changeMessage"/> /
                  {{ item.categoryVO.middleCTF || 0 }}
                </el-form-item>
                <el-form-item :rules="rules.middleCompletion" :prop="`categoryVOList[${index}][middleCompletion]`" class="category">
                  <el-input-number v-model="item.middleCompletion" :min="0" size="mini" @input="changeMessage"/> /
                  {{ item.categoryVO.middleCompletion || 0 }}
                </el-form-item>
                <el-form-item :rules="rules.middleShort" :prop="`categoryVOList[${index}][middleShort]`" class="category">
                  <el-input-number v-model="item.middleShort" :min="0" size="mini" @input="changeMessage"/> /
                  {{ item.categoryVO.middleShort || 0 }}
                </el-form-item>
                <el-form-item :rules="rules.middleCombinatorial" :prop="`categoryVOList[${index}][middleCombinatorial]`" class="category">
                  <el-input-number v-model="item.middleCombinatorial" :min="0" size="mini" @input="changeMessage"/> /
                  {{ item.categoryVO.middleCombinatorial || 0 }}
                </el-form-item>
              </div>
              <div class="trends-content">
                <div>高级</div>
                <el-form-item :rules="rules.seniorSingle" :prop="`categoryVOList[${index}][seniorSingle]`" class="category">
                  <el-input-number v-model="item.seniorSingle" :min="0" size="mini" @input="changeMessage"/> /
                  {{ item.categoryVO.seniorSingle || 0 }}
                </el-form-item>
                <el-form-item :rules="rules.seniorMultiple" :prop="`categoryVOList[${index}][seniorMultiple]`" class="category">
                  <el-input-number v-model="item.seniorMultiple" :min="0" size="mini" @input="changeMessage"/> /
                  {{ item.categoryVO.seniorMultiple || 0 }}
                </el-form-item>
                <el-form-item :rules="rules.seniorJudge" :prop="`categoryVOList[${index}][seniorJudge]`" class="category">
                  <el-input-number v-model="item.seniorJudge" :min="0" size="mini" @input="changeMessage"/> /
                  {{ item.categoryVO.seniorJudge || 0 }}
                </el-form-item>
                <el-form-item :rules="rules.seniorCTF" :prop="`categoryVOList[${index}][seniorCTF]`" class="category">
                  <el-input-number v-model="item.seniorCTF" :min="0" size="mini" @input="changeMessage"/> /
                  {{ item.categoryVO.seniorCTF || 0 }}
                </el-form-item>
                <el-form-item :rules="rules.seniorCompletion" :prop="`categoryVOList[${index}][seniorCompletion]`" class="category">
                  <el-input-number v-model="item.seniorCompletion" :min="0" size="mini" @input="changeMessage"/> /
                  {{ item.categoryVO.seniorCompletion || 0 }}
                </el-form-item>
                <el-form-item :rules="rules.seniorShort" :prop="`categoryVOList[${index}][seniorShort]`" class="category">
                  <el-input-number v-model="item.seniorShort" :min="0" size="mini" @input="changeMessage"/> /
                  {{ item.categoryVO.seniorShort || 0 }}
                </el-form-item>
                <el-form-item :rules="rules.seniorCombinatorial" :prop="`categoryVOList[${index}][seniorCombinatorial]`" class="category">
                  <el-input-number v-model="item.seniorCombinatorial" :min="0" size="mini" @input="changeMessage"/> /
                  {{ item.categoryVO.seniorCombinatorial || 0 }}
                </el-form-item>
              </div>
            </div>
          </div>
          <div class="add-classification" @click="addClass()">
            + 添加筛选范围
          </div>
        </el-form>
        <div class="mt-15 category-wrap">
          <div style="font-size: 15px; font-weight: bold;">题目总计：</div>
          <div class="mt-10">
            <div class="trends-content border-b" style="padding-bottom: 6px;">
              <div>难易程度</div>
              <div>单选题</div>
              <div>多选题</div>
              <div>判断题</div>
              <div>CTF题</div>
              <div>填空题</div>
              <div>简答题</div>
              <div>组合题</div>
            </div>
            <div class="trends-content border-b">
              <div>初级</div>
              <div>{{ simulationScore.countPrimarySingle }}</div>
              <div>{{ simulationScore.countPrimaryMultiple }}</div>
              <div>{{ simulationScore.countPrimaryJudge }}</div>
              <div>{{ simulationScore.countPrimaryCTF }}</div>
              <div>{{ simulationScore.countPrimaryCompletion }}</div>
              <div>{{ simulationScore.countPrimaryShort }}</div>
              <div>{{ simulationScore.countPrimaryCombinatorial }}</div>
            </div>
            <div class="trends-content border-b">
              <div>中级</div>
              <div>{{ simulationScore.countMiddleSingle }}</div>
              <div>{{ simulationScore.countMiddleMultiple }}</div>
              <div>{{ simulationScore.countMiddleJudge }}</div>
              <div>{{ simulationScore.countMiddleCTF }}</div>
              <div>{{ simulationScore.countMiddleCompletion }}</div>
              <div>{{ simulationScore.countMiddleShort }}</div>
              <div>{{ simulationScore.countMiddleCombinatorial }}</div>
            </div>
            <div class="trends-content border-b">
              <div>高级</div>
              <div>{{ simulationScore.countSeniorSingle }}</div>
              <div>{{ simulationScore.countSeniorMultiple }}</div>
              <div>{{ simulationScore.countSeniorJudge }}</div>
              <div>{{ simulationScore.countSeniorCTF }}</div>
              <div>{{ simulationScore.countSeniorCompletion }}</div>
              <div>{{ simulationScore.countSeniorShort }}</div>
              <div>{{ simulationScore.countSeniorCombinatorial }}</div>
            </div>
            <div class="trends-content  border-b">
              <div>合计</div>
              <div>{{ simulationScore.countPrimarySingle + simulationScore.countMiddleSingle + simulationScore.countSeniorSingle }}</div>
              <div>{{ simulationScore.countPrimaryMultiple + simulationScore.countMiddleMultiple + simulationScore.countSeniorMultiple }}</div>
              <div>{{ simulationScore.countPrimaryJudge + simulationScore.countMiddleJudge + simulationScore.countSeniorJudge }}</div>
              <div>{{ simulationScore.countPrimaryCTF + simulationScore.countMiddleCTF + simulationScore.countSeniorCTF }}</div>
              <div>{{ simulationScore.countPrimaryCompletion + simulationScore.countMiddleCompletion + simulationScore.countSeniorCompletion }}</div>
              <div>{{ simulationScore.countPrimaryShort + simulationScore.countMiddleShort + simulationScore.countSeniorShort }}</div>
              <div>{{ simulationScore.countPrimaryCombinatorial + simulationScore.countMiddleCombinatorial + simulationScore.countSeniorCombinatorial }}</div>
            </div>
            <el-form ref="formSimulation" :model="simulationRuleVO" :rules="rules" label-position="left" label-width="0" class="dialog-form">
              <div class="trends-content">
                <div>单题分数</div>
                <el-form-item :rules="rules.scoreSingle" prop="scoreSingle" class="category">
                  <el-input-number v-model="simulationRuleVO.scoreSingle" :min="0" size="mini" @input="changeMessage"/> 分
                </el-form-item>
                <el-form-item :rules="rules.scoreMultiple" prop="scoreMultiple" class="category">
                  <el-input-number v-model="simulationRuleVO.scoreMultiple" :min="0" size="mini" @input="changeMessage"/> 分
                </el-form-item>
                <el-form-item :rules="rules.scoreJudge" prop="scoreJudge" class="category">
                  <el-input-number v-model="simulationRuleVO.scoreJudge" :min="0" size="mini" @input="changeMessage"/> 分
                </el-form-item>
                <el-form-item :rules="rules.scoreCTF" prop="scoreCTF" class="category">
                  <el-input-number v-model="simulationRuleVO.scoreCTF" :min="0" size="mini" @input="changeMessage"/> 分
                </el-form-item>
                <el-form-item :rules="rules.scoreCompletion" prop="scoreCompletion" class="category">
                  <el-input-number v-model="simulationRuleVO.scoreCompletion" :min="0" size="mini" @input="changeMessage"/> 分
                </el-form-item>
                <el-form-item :rules="rules.scoreShort" prop="scoreShort" class="category">
                  <el-input-number v-model="simulationRuleVO.scoreShort" :min="0" size="mini" @input="changeMessage"/> 分
                </el-form-item>
                <el-form-item :rules="rules.scoreCombinatorial" prop="scoreCombinatorial" class="category">
                  <el-input-number v-model="simulationRuleVO.scoreCombinatorial" :min="0" size="mini" @input="changeMessage"/> 分
                </el-form-item>
              </div>
            </el-form>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import questionConf from '../../questionBank/config.js'
import validate from '@/packages/validate'
import { getCategory, categoryComplexityNumber } from '@/api/accumulate/category'
import { getSkillPoint } from '@/api/accumulate/skillPoint.js'
import { getQuestionCategory } from '@/api/accumulate/questionLibrary'

export default {
  props: {
    theoryVO: {
      type: Object
    },
    targetVO: {
      type: Object
    },
    simulationVO: {
      type: Object
    }
  },
  data() {
    var numCheck = (rule, value, callback) => {
      if (this.activeName === 'theory') {
        const arr = rule.field.split('categoryVOList[').join('').split('][')
        const index = arr[0]
        const key = arr[1].split(']').join('')
        // 没有选择分类或技能点、题库
        if (!this.theoryRuleVO.categoryVOList[index].categoryVO['questionDepotId'] && !this.theoryRuleVO.categoryVOList[index].categoryVO['categoryId'] && !this.theoryRuleVO.categoryVOList[index].categoryVO['skillPointId'] && this.theoryRuleVO.categoryVOList[index][key] != 0) {
          callback(new Error('请选择筛选范围'))
        } else if (this.theoryRuleVO.categoryVOList[index][key] > this.theoryRuleVO.categoryVOList[index].categoryVO[key]) {
          callback(new Error(`输入范围：0-${this.theoryRuleVO.categoryVOList[index].categoryVO[key]}`))
        } else {
          callback()
        }
      } else if (this.activeName === 'target') {
        const arr = rule.field.split('categoryVOList[').join('').split('][')
        const index = arr[0]
        const key = arr[1].split(']').join('')
        if (!this.targetRuleVO.categoryVOList[index].categoryVO['questionDepotId'] && !this.targetRuleVO.categoryVOList[index].categoryVO['categoryId'] && !this.targetRuleVO.categoryVOList[index].categoryVO['skillPointId'] && this.targetRuleVO.categoryVOList[index][key] != 0) {
          callback(new Error('请选择筛选范围'))
        } else if (this.targetRuleVO.categoryVOList[index][key] > this.targetRuleVO.categoryVOList[index].categoryVO[key]) {
          callback(new Error(`输入范围：0-${this.targetRuleVO.categoryVOList[index].categoryVO[key]}`))
        } else {
          callback()
        }
      } else if (this.activeName === 'simulation') {
        const arr = rule.field.split('categoryVOList[').join('').split('][')
        const index = arr[0]
        const key = arr[1].split(']').join('')
        if (!this.simulationRuleVO.categoryVOList[index].categoryVO['questionDepotId'] && !this.simulationRuleVO.categoryVOList[index].categoryVO['categoryId'] && !this.simulationRuleVO.categoryVOList[index].categoryVO['skillPointId'] && this.simulationRuleVO.categoryVOList[index][key] != 0) {
          callback(new Error('请选择筛选范围'))
        } else if (this.simulationRuleVO.categoryVOList[index][key] > this.simulationRuleVO.categoryVOList[index].categoryVO[key]) {
          callback(new Error(`输入范围：0-${this.simulationRuleVO.categoryVOList[index].categoryVO[key]}`))
        } else {
          callback()
        }
      }
    }
    var scoreCheck = (rule, value, callback) => {
      if (this.activeName === 'theory') {
        if (rule.field === 'scoreCTF') {
          if (this.theoryScore.countPrimaryCTF + this.theoryScore.countMiddleCTF + this.theoryScore.countSeniorCTF > 0) {
            if (value >= 0 && value <= 9999) {
              callback()
            } else {
              callback(new Error(`输入范围：0-9999`))
            }
            callback()
          } else {
            if (value > 0) {
              callback(new Error(`请设置题型数量`))
            } else {
              callback()
            }
          }
        } else if (rule.field === 'scoreSingle') {
          if (this.theoryScore.countPrimarySingle + this.theoryScore.countMiddleSingle + this.theoryScore.countSeniorSingle > 0) {
            if (value >= 0 && value <= 9999) {
              callback()
            } else {
              callback(new Error(`输入范围：0-9999`))
            }
            callback()
          } else {
            if (value > 0) {
              callback(new Error(`请设置题型数量`))
            } else {
              callback()
            }
          }
        } else if (rule.field === 'scoreShort') {
          if (this.theoryScore.countPrimaryShort + this.theoryScore.countMiddleShort + this.theoryScore.countSeniorShort > 0) {
            if (value >= 0 && value <= 9999) {
              callback()
            } else {
              callback(new Error(`输入范围：0-9999`))
            }
            callback()
          } else {
            if (value > 0) {
              callback(new Error(`请设置题型数量`))
            } else {
              callback()
            }
          }
        } else if (rule.field === 'scoreCompletion') {
          if (this.theoryScore.countPrimaryCompletion + this.theoryScore.countMiddleCompletion + this.theoryScore.countSeniorCompletion > 0) {
            if (value >= 0 && value <= 9999) {
              callback()
            } else {
              callback(new Error(`输入范围：0-9999`))
            }
            callback()
          } else {
            if (value > 0) {
              callback(new Error(`请设置题型数量`))
            } else {
              callback()
            }
          }
        } else if (rule.field === 'scoreJudge') {
          if (this.theoryScore.countPrimaryJudge + this.theoryScore.countMiddleJudge + this.theoryScore.countSeniorJudge > 0) {
            if (value >= 0 && value <= 9999) {
              callback()
            } else {
              callback(new Error(`输入范围：0-9999`))
            }
            callback()
          } else {
            if (value > 0) {
              callback(new Error(`请设置题型数量`))
            } else {
              callback()
            }
          }
        } else if (rule.field === 'scoreMultiple') {
          if (this.theoryScore.countPrimaryMultiple + this.theoryScore.countMiddleMultiple + this.theoryScore.countSeniorMultiple > 0) {
            if (value >= 0 && value <= 9999) {
              callback()
            } else {
              callback(new Error(`输入范围：0-9999`))
            }
            callback()
          } else {
            if (value > 0) {
              callback(new Error(`请设置题型数量`))
            } else {
              callback()
            }
          }
        }
      } else if (this.activeName === 'target') {
        if (rule.field === 'scoreCTF') {
          if (this.targetScore.countPrimaryCTF + this.targetScore.countMiddleCTF + this.targetScore.countSeniorCTF > 0) {
            if (value >= 0 && value <= 9999) {
              callback()
            } else {
              callback(new Error(`输入范围：0-9999`))
            }
            callback()
          } else {
            if (value > 0) {
              callback(new Error(`请设置题型数量`))
            } else {
              callback()
            }
          }
        } else if (rule.field === 'scoreAWD') {
          if (this.targetScore.countPrimaryAWD + this.targetScore.countMiddleAWD + this.targetScore.countSeniorAWD > 0) {
            if (value >= 0 && value <= 9999) {
              callback()
            } else {
              callback(new Error(`输入范围：0-9999`))
            }
            callback()
          } else {
            if (value > 0) {
              callback(new Error(`请设置题型数量`))
            } else {
              callback()
            }
          }
        } else if (rule.field === 'scoreBug') {
          if (this.targetScore.countPrimaryBug + this.targetScore.countMiddleBug + this.targetScore.countSeniorBug > 0) {
            if (value >= 0 && value <= 9999) {
              callback()
            } else {
              callback(new Error(`输入范围：0-9999`))
            }
            callback()
          } else {
            if (value > 0) {
              callback(new Error(`请设置题型数量`))
            } else {
              callback()
            }
          }
        } else if (rule.field === 'scoreOther') {
          if (this.targetScore.countPrimaryOther + this.targetScore.countMiddleOther + this.targetScore.countSeniorOther > 0) {
            if (value >= 0 && value <= 9999) {
              callback()
            } else {
              callback(new Error(`输入范围：0-9999`))
            }
            callback()
          } else {
            if (value > 0) {
              callback(new Error(`请设置题型数量`))
            } else {
              callback()
            }
          }
        }
      } else if (this.activeName === 'simulation') {
        if (rule.field === 'scoreCTF') {
          if (this.simulationScore.countPrimaryCTF + this.simulationScore.countMiddleCTF + this.simulationScore.countSeniorCTF > 0) {
            if (value >= 0 && value <= 9999) {
              callback()
            } else {
              callback(new Error(`输入范围：0-9999`))
            }
            callback()
          } else {
            if (value > 0) {
              callback(new Error(`请设置题型数量`))
            } else {
              callback()
            }
          }
        } else if (rule.field === 'scoreSingle') {
          if (this.simulationScore.countPrimarySingle + this.simulationScore.countMiddleSingle + this.simulationScore.countSeniorSingle > 0) {
            if (value >= 0 && value <= 9999) {
              callback()
            } else {
              callback(new Error(`输入范围：0-9999`))
            }
            callback()
          } else {
            if (value > 0) {
              callback(new Error(`请设置题型数量`))
            } else {
              callback()
            }
          }
        } else if (rule.field === 'scoreShort') {
          if (this.simulationScore.countPrimaryShort + this.simulationScore.countMiddleShort + this.simulationScore.countSeniorShort > 0) {
            if (value >= 0 && value <= 9999) {
              callback()
            } else {
              callback(new Error(`输入范围：0-9999`))
            }
            callback()
          } else {
            if (value > 0) {
              callback(new Error(`请设置题型数量`))
            } else {
              callback()
            }
          }
        } else if (rule.field === 'scoreCompletion') {
          if (this.simulationScore.countPrimaryCompletion + this.simulationScore.countMiddleCompletion + this.simulationScore.countSeniorCompletion > 0) {
            if (value >= 0 && value <= 9999) {
              callback()
            } else {
              callback(new Error(`输入范围：0-9999`))
            }
            callback()
          } else {
            if (value > 0) {
              callback(new Error(`请设置题型数量`))
            } else {
              callback()
            }
          }
        } else if (rule.field === 'scoreJudge') {
          if (this.simulationScore.countPrimaryJudge + this.simulationScore.countMiddleJudge + this.simulationScore.countSeniorJudge > 0) {
            if (value >= 0 && value <= 9999) {
              callback()
            } else {
              callback(new Error(`输入范围：0-9999`))
            }
            callback()
          } else {
            if (value > 0) {
              callback(new Error(`请设置题型数量`))
            } else {
              callback()
            }
          }
        } else if (rule.field === 'scoreMultiple') {
          if (this.simulationScore.countPrimaryMultiple + this.simulationScore.countMiddleMultiple + this.simulationScore.countSeniorMultiple > 0) {
            if (value >= 0 && value <= 9999) {
              callback()
            } else {
              callback(new Error(`输入范围：0-9999`))
            }
            callback()
          } else {
            if (value > 0) {
              callback(new Error(`请设置题型数量`))
            } else {
              callback()
            }
          }
        } else if (rule.field === 'scoreCombinatorial') {
          if (this.simulationScore.countPrimaryCombinatorial + this.simulationScore.countMiddleCombinatorial + this.simulationScore.countSeniorCombinatorial > 0) {
            if (value >= 0 && value <= 9999) {
              callback()
            } else {
              callback(new Error(`输入范围：0-9999`))
            }
            callback()
          } else {
            if (value > 0) {
              callback(new Error(`请设置题型数量`))
            } else {
              callback()
            }
          }
        }
      }
    }
    return {
      questionConf: questionConf,
      validate: validate,
      activeName: 'theory',
      img: require('@/assets/empty_state.png'),
      theoryQuestionCategoryList: [],
      targetQuestionCategoryList: [],
      simulationQuestionCategoryList: [],
      theoryCategoryList: [],
      targetCategoryList: [],
      simulationCategoryList: [],
      skillPointList: [],
      // 理论题
      theoryRuleVO: {
        categoryVOList: [
          {
            categoryId: '',
            skillPointId: null,
            questionDepotId: '',
            categoryVO: {},
            primarySingle: 0, // 初级单选题
            primaryMultiple: 0, // 初级多选题
            primaryJudge: 0, // 初级判断题
            primaryCTF: 0, // 初级CTF题
            primaryCompletion: 0, // 初级填空题
            primaryShort: 0, // 初级简答题
            primaryAWD: 0, // 初级awd题
            primaryBug: 0, // 初级bug题
            primaryCombinatorial: 0, // 初级组合题
            primaryOther: 0, // 初级其它题
            middleSingle: 0, // 中级单选题
            middleMultiple: 0, // 中级多选题
            middleJudge: 0, // 中级判断题
            middleCTF: 0, // 中级CTF题
            middleCompletion: 0, // 中级填空题
            middleShort: 0, // 中级简答题
            middleAWD: 0, // 中级awd题
            middleBug: 0, // 中级bug题
            middleCombinatorial: 0, // 中级组合题
            middleOther: 0, // 中级其它题
            seniorSingle: 0, // 高级单选题
            seniorMultiple: 0, // 高级多选题
            seniorJudge: 0, // 高级判断题
            seniorCTF: 0, // 高级CTF题
            seniorCompletion: 0, // 高级填空题
            seniorShort: 0, // 高级简答题
            seniorAWD: 0, // 高级awd题
            seniorBug: 0, // 高级bug题
            seniorCombinatorial: 0, // 高级组合题
            seniorOther: 0 // 高级其它题
          }
        ],
        scoreSingle: 0, // 单选题分数
        scoreMultiple: 0, // 多选题分数
        scoreJudge: 0, // 判断题分数
        scoreCTF: 0, // CTF题分数
        scoreCompletion: 0, // 填空题分数
        scoreShort: 0 // 简答题分数
      },
      // 靶机题
      targetRuleVO: {
        categoryVOList: [
          {
            categoryId: '',
            skillPointId: null,
            questionDepotId: '',
            categoryVO: {},
            primarySingle: 0, // 初级单选题
            primaryMultiple: 0, // 初级多选题
            primaryJudge: 0, // 初级判断题
            primaryCTF: 0, // 初级CTF题
            primaryCompletion: 0, // 初级填空题
            primaryShort: 0, // 初级简答题
            primaryAWD: 0, // 初级awd题
            primaryBug: 0, // 初级bug题
            primaryCombinatorial: 0, // 初级组合题
            primaryOther: 0, // 初级其它题
            middleSingle: 0, // 中级单选题
            middleMultiple: 0, // 中级多选题
            middleJudge: 0, // 中级判断题
            middleCTF: 0, // 中级CTF题
            middleCompletion: 0, // 中级填空题
            middleShort: 0, // 中级简答题
            middleAWD: 0, // 中级awd题
            middleBug: 0, // 中级bug题
            middleCombinatorial: 0, // 中级组合题
            middleOther: 0, // 中级其它题
            seniorSingle: 0, // 高级单选题
            seniorMultiple: 0, // 高级多选题
            seniorJudge: 0, // 高级判断题
            seniorCTF: 0, // 高级CTF题
            seniorCompletion: 0, // 高级填空题
            seniorShort: 0, // 高级简答题
            seniorAWD: 0, // 高级awd题
            seniorBug: 0, // 高级bug题
            seniorCombinatorial: 0, // 高级组合题
            seniorOther: 0 // 高级其它题
          }
        ],
        scoreCTF: 0, // CTF题分数
        scoreAWD: 0, // AWD题分数
        scoreOther: 0, // 其它题分数
        scoreBug: 0 // 漏洞题分数
      },
      // 仿真题
      simulationRuleVO: {
        categoryVOList: [
          {
            categoryId: '',
            skillPointId: null,
            questionDepotId: '',
            categoryVO: {},
            primarySingle: 0, // 初级单选题
            primaryMultiple: 0, // 初级多选题
            primaryJudge: 0, // 初级判断题
            primaryCTF: 0, // 初级CTF题
            primaryCompletion: 0, // 初级填空题
            primaryShort: 0, // 初级简答题
            primaryAWD: 0, // 初级awd题
            primaryBug: 0, // 初级bug题
            primaryCombinatorial: 0, // 初级组合题
            primaryOther: 0, // 初级其它题
            middleSingle: 0, // 中级单选题
            middleMultiple: 0, // 中级多选题
            middleJudge: 0, // 中级判断题
            middleCTF: 0, // 中级CTF题
            middleCompletion: 0, // 中级填空题
            middleShort: 0, // 中级简答题
            middleAWD: 0, // 中级awd题
            middleBug: 0, // 中级bug题
            middleCombinatorial: 0, // 中级组合题
            middleOther: 0, // 中级其它题
            seniorSingle: 0, // 高级单选题
            seniorMultiple: 0, // 高级多选题
            seniorJudge: 0, // 高级判断题
            seniorCTF: 0, // 高级CTF题
            seniorCompletion: 0, // 高级填空题
            seniorShort: 0, // 高级简答题
            seniorAWD: 0, // 高级awd题
            seniorBug: 0, // 高级bug题
            seniorCombinatorial: 0, // 高级组合题
            seniorOther: 0 // 高级其它题
          }
        ],
        scoreSingle: 0, // 单选题分数
        scoreMultiple: 0, // 多选题分数
        scoreJudge: 0, // 判断题分数
        scoreCTF: 0, // CTF题分数
        scoreCompletion: 0, // 填空题分数
        scoreShort: 0, // 简答题分数
        scoreCombinatorial: 0 // 组合题分数
      },
      rules: {
        'scoreSingle': [
          validate.all_number_integer,
          { validator: scoreCheck, trigger: 'change' }
        ],
        'scoreMultiple': [
          validate.all_number_integer,
          { validator: scoreCheck, trigger: 'change' }
        ],
        'scoreJudge': [
          validate.all_number_integer,
          { validator: scoreCheck, trigger: 'change' }
        ],
        'scoreCTF': [
          validate.all_number_integer,
          { validator: scoreCheck, trigger: 'change' }
        ],
        'scoreAWD': [
          validate.all_number_integer,
          { validator: scoreCheck, trigger: 'change' }
        ],
        'scoreOther': [
          validate.all_number_integer,
          { validator: scoreCheck, trigger: 'change' }
        ],
        'scoreCompletion': [
          validate.all_number_integer,
          { validator: scoreCheck, trigger: 'change' }
        ],
        'scoreShort': [
          validate.all_number_integer,
          { validator: scoreCheck, trigger: 'change' }
        ],
        'scoreBug': [
          validate.all_number_integer,
          { validator: scoreCheck, trigger: 'change' }
        ],
        'scoreCombinatorial': [
          validate.all_number_integer,
          { validator: scoreCheck, trigger: 'change' }
        ],
        'primarySingle': [
          validate.all_number_integer,
          { validator: numCheck, trigger: 'change' }
        ],
        'primaryMultiple': [
          validate.all_number_integer,
          { validator: numCheck, trigger: 'change' }
        ],
        'primaryJudge': [
          validate.all_number_integer,
          { validator: numCheck, trigger: 'change' }
        ],
        'primaryCTF': [
          validate.all_number_integer,
          { validator: numCheck, trigger: 'change' }
        ],
        'primaryCompletion': [
          validate.all_number_integer,
          { validator: numCheck, trigger: 'change' }
        ],
        'primaryShort': [
          validate.all_number_integer,
          { validator: numCheck, trigger: 'change' }
        ],
        'primaryAWD': [
          validate.all_number_integer,
          { validator: numCheck, trigger: 'change' }
        ],
        'primaryBug': [
          validate.all_number_integer,
          { validator: numCheck, trigger: 'change' }
        ],
        'primaryCombinatorial': [
          validate.all_number_integer,
          { validator: numCheck, trigger: 'change' }
        ],
        'primaryOther': [
          validate.all_number_integer,
          { validator: numCheck, trigger: 'change' }
        ],
        'middleSingle': [
          validate.all_number_integer,
          { validator: numCheck, trigger: 'change' }
        ],
        'middleMultiple': [
          validate.all_number_integer,
          { validator: numCheck, trigger: 'change' }
        ],
        'middleJudge': [
          validate.all_number_integer,
          { validator: numCheck, trigger: 'change' }
        ],
        'middleCTF': [
          validate.all_number_integer,
          { validator: numCheck, trigger: 'change' }
        ],
        'middleCompletion': [
          validate.all_number_integer,
          { validator: numCheck, trigger: 'change' }
        ],
        'middleShort': [
          validate.all_number_integer,
          { validator: numCheck, trigger: 'change' }
        ],
        'middleAWD': [
          validate.all_number_integer,
          { validator: numCheck, trigger: 'change' }
        ],
        'middleBug': [
          validate.all_number_integer,
          { validator: numCheck, trigger: 'change' }
        ],
        'middleCombinatorial': [
          validate.all_number_integer,
          { validator: numCheck, trigger: 'change' }
        ],
        'middleOther': [
          validate.all_number_integer,
          { validator: numCheck, trigger: 'change' }
        ],
        'seniorSingle': [
          validate.all_number_integer,
          { validator: numCheck, trigger: 'change' }
        ],
        'seniorMultiple': [
          validate.all_number_integer,
          { validator: numCheck, trigger: 'change' }
        ],
        'seniorJudge': [
          validate.all_number_integer,
          { validator: numCheck, trigger: 'change' }
        ],
        'seniorCTF': [
          validate.all_number_integer,
          { validator: numCheck, trigger: 'change' }
        ],
        'seniorCompletion': [
          validate.all_number_integer,
          { validator: numCheck, trigger: 'change' }
        ],
        'seniorShort': [
          validate.all_number_integer,
          { validator: numCheck, trigger: 'change' }
        ],
        'seniorAWD': [
          validate.all_number_integer,
          { validator: numCheck, trigger: 'change' }
        ],
        'seniorBug': [
          validate.all_number_integer,
          { validator: numCheck, trigger: 'change' }
        ],
        'seniorCombinatorial': [
          validate.all_number_integer,
          { validator: numCheck, trigger: 'change' }
        ],
        'seniorOther': [
          validate.all_number_integer,
          { validator: numCheck, trigger: 'change' }
        ]
      }
    }
  },
  computed: {
    // 理论题数量统计
    theoryScore() {
      const arr = {
        countPrimarySingle: this.theoryRuleVO.categoryVOList.map(item => { return item.primarySingle }).reduce((a, b) => this.isNaNs(a, b), 0),
        countMiddleSingle: this.theoryRuleVO.categoryVOList.map(item => { return item.middleSingle }).reduce((a, b) => this.isNaNs(a, b), 0),
        countSeniorSingle: this.theoryRuleVO.categoryVOList.map(item => { return item.seniorSingle }).reduce((a, b) => this.isNaNs(a, b), 0),
        countPrimaryMultiple: this.theoryRuleVO.categoryVOList.map(item => { return item.primaryMultiple }).reduce((a, b) => this.isNaNs(a, b), 0),
        countMiddleMultiple: this.theoryRuleVO.categoryVOList.map(item => { return item.middleMultiple }).reduce((a, b) => this.isNaNs(a, b), 0),
        countSeniorMultiple: this.theoryRuleVO.categoryVOList.map(item => { return item.seniorMultiple }).reduce((a, b) => this.isNaNs(a, b), 0),
        countPrimaryJudge: this.theoryRuleVO.categoryVOList.map(item => { return item.primaryJudge }).reduce((a, b) => this.isNaNs(a, b), 0),
        countMiddleJudge: this.theoryRuleVO.categoryVOList.map(item => { return item.middleJudge }).reduce((a, b) => this.isNaNs(a, b), 0),
        countSeniorJudge: this.theoryRuleVO.categoryVOList.map(item => { return item.seniorJudge }).reduce((a, b) => this.isNaNs(a, b), 0),
        countPrimaryCTF: this.theoryRuleVO.categoryVOList.map(item => { return item.primaryCTF }).reduce((a, b) => this.isNaNs(a, b), 0),
        countMiddleCTF: this.theoryRuleVO.categoryVOList.map(item => { return item.middleCTF }).reduce((a, b) => this.isNaNs(a, b), 0),
        countSeniorCTF: this.theoryRuleVO.categoryVOList.map(item => { return item.seniorCTF }).reduce((a, b) => this.isNaNs(a, b), 0),
        countPrimaryCompletion: this.theoryRuleVO.categoryVOList.map(item => { return item.primaryCompletion }).reduce((a, b) => this.isNaNs(a, b), 0),
        countMiddleCompletion: this.theoryRuleVO.categoryVOList.map(item => { return item.middleCompletion }).reduce((a, b) => this.isNaNs(a, b), 0),
        countSeniorCompletion: this.theoryRuleVO.categoryVOList.map(item => { return item.seniorCompletion }).reduce((a, b) => this.isNaNs(a, b), 0),
        countPrimaryShort: this.theoryRuleVO.categoryVOList.map(item => { return item.primaryShort }).reduce((a, b) => this.isNaNs(a, b), 0),
        countMiddleShort: this.theoryRuleVO.categoryVOList.map(item => { return item.middleShort }).reduce((a, b) => this.isNaNs(a, b), 0),
        countSeniorShort: this.theoryRuleVO.categoryVOList.map(item => { return item.seniorShort }).reduce((a, b) => this.isNaNs(a, b), 0)
      }
      return arr
    },
    // 靶机题数量统计
    targetScore() {
      const arr = {
        countPrimaryCTF: this.targetRuleVO.categoryVOList.map(item => { return item.primaryCTF }).reduce((a, b) => this.isNaNs(a, b), 0),
        countPrimaryAWD: this.targetRuleVO.categoryVOList.map(item => { return item.primaryAWD }).reduce((a, b) => this.isNaNs(a, b), 0),
        countPrimaryOther: this.targetRuleVO.categoryVOList.map(item => { return item.primaryOther }).reduce((a, b) => this.isNaNs(a, b), 0),
        countPrimaryBug: this.targetRuleVO.categoryVOList.map(item => { return item.primaryBug }).reduce((a, b) => this.isNaNs(a, b), 0),
        countMiddleCTF: this.targetRuleVO.categoryVOList.map(item => { return item.middleCTF }).reduce((a, b) => this.isNaNs(a, b), 0),
        countMiddleAWD: this.targetRuleVO.categoryVOList.map(item => { return item.middleAWD }).reduce((a, b) => this.isNaNs(a, b), 0),
        countMiddleOther: this.targetRuleVO.categoryVOList.map(item => { return item.middleOther }).reduce((a, b) => this.isNaNs(a, b), 0),
        countMiddleBug: this.targetRuleVO.categoryVOList.map(item => { return item.middleBug }).reduce((a, b) => this.isNaNs(a, b), 0),
        countSeniorCTF: this.targetRuleVO.categoryVOList.map(item => { return item.seniorCTF }).reduce((a, b) => this.isNaNs(a, b), 0),
        countSeniorAWD: this.targetRuleVO.categoryVOList.map(item => { return item.seniorAWD }).reduce((a, b) => this.isNaNs(a, b), 0),
        countSeniorOther: this.targetRuleVO.categoryVOList.map(item => { return item.seniorOther }).reduce((a, b) => this.isNaNs(a, b), 0),
        countSeniorBug: this.targetRuleVO.categoryVOList.map(item => { return item.seniorBug }).reduce((a, b) => this.isNaNs(a, b), 0)
      }
      return arr
    },
    // 仿真题数量统计
    simulationScore() {
      const arr = {
        countPrimarySingle: this.simulationRuleVO.categoryVOList.map(item => { return item.primarySingle }).reduce((a, b) => this.isNaNs(a, b), 0),
        countMiddleSingle: this.simulationRuleVO.categoryVOList.map(item => { return item.middleSingle }).reduce((a, b) => this.isNaNs(a, b), 0),
        countSeniorSingle: this.simulationRuleVO.categoryVOList.map(item => { return item.seniorSingle }).reduce((a, b) => this.isNaNs(a, b), 0),
        countPrimaryMultiple: this.simulationRuleVO.categoryVOList.map(item => { return item.primaryMultiple }).reduce((a, b) => this.isNaNs(a, b), 0),
        countMiddleMultiple: this.simulationRuleVO.categoryVOList.map(item => { return item.middleMultiple }).reduce((a, b) => this.isNaNs(a, b), 0),
        countSeniorMultiple: this.simulationRuleVO.categoryVOList.map(item => { return item.seniorMultiple }).reduce((a, b) => this.isNaNs(a, b), 0),
        countPrimaryJudge: this.simulationRuleVO.categoryVOList.map(item => { return item.primaryJudge }).reduce((a, b) => this.isNaNs(a, b), 0),
        countMiddleJudge: this.simulationRuleVO.categoryVOList.map(item => { return item.middleJudge }).reduce((a, b) => this.isNaNs(a, b), 0),
        countSeniorJudge: this.simulationRuleVO.categoryVOList.map(item => { return item.seniorJudge }).reduce((a, b) => this.isNaNs(a, b), 0),
        countPrimaryCTF: this.simulationRuleVO.categoryVOList.map(item => { return item.primaryCTF }).reduce((a, b) => this.isNaNs(a, b), 0),
        countMiddleCTF: this.simulationRuleVO.categoryVOList.map(item => { return item.middleCTF }).reduce((a, b) => this.isNaNs(a, b), 0),
        countSeniorCTF: this.simulationRuleVO.categoryVOList.map(item => { return item.seniorCTF }).reduce((a, b) => this.isNaNs(a, b), 0),
        countPrimaryCompletion: this.simulationRuleVO.categoryVOList.map(item => { return item.primaryCompletion }).reduce((a, b) => this.isNaNs(a, b), 0),
        countMiddleCompletion: this.simulationRuleVO.categoryVOList.map(item => { return item.middleCompletion }).reduce((a, b) => this.isNaNs(a, b), 0),
        countSeniorCompletion: this.simulationRuleVO.categoryVOList.map(item => { return item.seniorCompletion }).reduce((a, b) => this.isNaNs(a, b), 0),
        countPrimaryShort: this.simulationRuleVO.categoryVOList.map(item => { return item.primaryShort }).reduce((a, b) => this.isNaNs(a, b), 0),
        countMiddleShort: this.simulationRuleVO.categoryVOList.map(item => { return item.middleShort }).reduce((a, b) => this.isNaNs(a, b), 0),
        countSeniorShort: this.simulationRuleVO.categoryVOList.map(item => { return item.seniorShort }).reduce((a, b) => this.isNaNs(a, b), 0),
        countPrimaryCombinatorial: this.simulationRuleVO.categoryVOList.map(item => { return item.primaryCombinatorial }).reduce((a, b) => this.isNaNs(a, b), 0),
        countMiddleCombinatorial: this.simulationRuleVO.categoryVOList.map(item => { return item.middleCombinatorial }).reduce((a, b) => this.isNaNs(a, b), 0),
        countSeniorCombinatorial: this.simulationRuleVO.categoryVOList.map(item => { return item.seniorCombinatorial }).reduce((a, b) => this.isNaNs(a, b), 0)
      }
      return arr
    },
    total() {
      return this.theoryScore.countPrimarySingle + this.theoryScore.countMiddleSingle + this.theoryScore.countSeniorSingle +
      this.theoryScore.countPrimaryMultiple + this.theoryScore.countMiddleMultiple + this.theoryScore.countSeniorMultiple +
      this.theoryScore.countPrimaryJudge + this.theoryScore.countMiddleJudge + this.theoryScore.countSeniorJudge +
      this.theoryScore.countPrimaryCTF + this.theoryScore.countMiddleCTF + this.theoryScore.countSeniorCTF +
      this.theoryScore.countPrimaryCompletion + this.theoryScore.countMiddleCompletion + this.theoryScore.countSeniorCompletion +
      this.theoryScore.countPrimaryShort + this.theoryScore.countMiddleShort + this.theoryScore.countSeniorShort +
      this.targetScore.countPrimaryCTF + this.targetScore.countMiddleCTF + this.targetScore.countSeniorCTF +
      this.targetScore.countPrimaryAWD + this.targetScore.countMiddleAWD + this.targetScore.countSeniorAWD +
      this.targetScore.countPrimaryBug + this.targetScore.countMiddleBug + this.targetScore.countSeniorBug +
      this.targetScore.countPrimaryOther + this.targetScore.countMiddleOther + this.targetScore.countSeniorOther +
      this.simulationScore.countPrimarySingle + this.simulationScore.countMiddleSingle + this.simulationScore.countSeniorSingle +
      this.simulationScore.countPrimaryMultiple + this.simulationScore.countMiddleMultiple + this.simulationScore.countSeniorMultiple +
      this.simulationScore.countPrimaryJudge + this.simulationScore.countMiddleJudge + this.simulationScore.countSeniorJudge +
      this.simulationScore.countPrimaryCTF + this.simulationScore.countMiddleCTF + this.simulationScore.countSeniorCTF +
      this.simulationScore.countPrimaryCompletion + this.simulationScore.countMiddleCompletion + this.simulationScore.countSeniorCompletion +
      this.simulationScore.countPrimaryShort + this.theoryScore.countMiddleShort + this.simulationScore.countSeniorShort +
      this.simulationScore.countPrimaryCombinatorial + this.simulationScore.countMiddleCombinatorial + this.simulationScore.countSeniorCombinatorial
    },
    totalScore() {
      return ((this.theoryScore.countPrimarySingle + this.theoryScore.countMiddleSingle + this.theoryScore.countSeniorSingle) * (this.theoryRuleVO.scoreSingle ? this.theoryRuleVO.scoreSingle : 0)) +
      ((this.theoryScore.countPrimaryMultiple + this.theoryScore.countMiddleMultiple + this.theoryScore.countSeniorMultiple) * (this.theoryRuleVO.scoreMultiple ? this.theoryRuleVO.scoreMultiple : 0)) +
      ((this.theoryScore.countPrimaryJudge + this.theoryScore.countMiddleJudge + this.theoryScore.countSeniorJudge) * (this.theoryRuleVO.scoreJudge ? this.theoryRuleVO.scoreJudge : 0)) +
      ((this.theoryScore.countPrimaryCTF + this.theoryScore.countMiddleCTF + this.theoryScore.countSeniorCTF) * (this.theoryRuleVO.scoreCTF ? this.theoryRuleVO.scoreCTF : 0)) +
      ((this.theoryScore.countPrimaryCompletion + this.theoryScore.countMiddleCompletion + this.theoryScore.countSeniorCompletion) * (this.theoryRuleVO.scoreCompletion ? this.theoryRuleVO.scoreCompletion : 0)) +
      ((this.theoryScore.countPrimaryShort + this.theoryScore.countMiddleShort + this.theoryScore.countSeniorShort) * (this.theoryRuleVO.scoreShort ? this.theoryRuleVO.scoreShort : 0)) +
      ((this.targetScore.countPrimaryCTF + this.targetScore.countMiddleCTF + this.targetScore.countSeniorCTF) * (this.targetRuleVO.scoreCTF ? this.targetRuleVO.scoreCTF : 0)) +
      ((this.targetScore.countPrimaryAWD + this.targetScore.countMiddleAWD + this.targetScore.countSeniorAWD) * (this.targetRuleVO.scoreAWD ? this.targetRuleVO.scoreAWD : 0)) +
      ((this.targetScore.countPrimaryBug + this.targetScore.countMiddleBug + this.targetScore.countSeniorBug) * (this.targetRuleVO.scoreBug ? this.targetRuleVO.scoreBug : 0)) +
      ((this.targetScore.countPrimaryOther + this.targetScore.countMiddleOther + this.targetScore.countSeniorOther) * (this.targetRuleVO.scoreOther ? this.targetRuleVO.scoreOther : 0)) +
      ((this.simulationScore.countPrimarySingle + this.simulationScore.countMiddleSingle + this.simulationScore.countSeniorSingle) * (this.simulationRuleVO.scoreSingle ? this.simulationRuleVO.scoreSingle : 0)) +
      ((this.simulationScore.countPrimaryMultiple + this.simulationScore.countMiddleMultiple + this.simulationScore.countSeniorMultiple) * (this.simulationRuleVO.scoreMultiple ? this.simulationRuleVO.scoreMultiple : 0)) +
      ((this.simulationScore.countPrimaryJudge + this.simulationScore.countMiddleJudge + this.simulationScore.countSeniorJudge) * (this.simulationRuleVO.scoreJudge ? this.simulationRuleVO.scoreJudge : 0)) +
      ((this.simulationScore.countPrimaryCTF + this.simulationScore.countMiddleCTF + this.simulationScore.countSeniorCTF) * (this.simulationRuleVO.scoreCTF ? this.simulationRuleVO.scoreCTF : 0)) +
      ((this.simulationScore.countPrimaryCompletion + this.simulationScore.countMiddleCompletion + this.simulationScore.countSeniorCompletion) * (this.simulationRuleVO.scoreCompletion ? this.simulationRuleVO.scoreCompletion : 0)) +
      ((this.simulationScore.countPrimaryShort + this.simulationScore.countMiddleShort + this.simulationScore.countSeniorShort) * (this.simulationRuleVO.scoreShort ? this.simulationRuleVO.scoreShort : 0)) +
      ((this.simulationScore.countPrimaryCombinatorial + this.simulationScore.countMiddleCombinatorial + this.simulationScore.countSeniorCombinatorial) * (this.simulationRuleVO.scoreCombinatorial ? this.simulationRuleVO.scoreCombinatorial : 0))
    }
  },
  created() {
    this.theoryRuleVO = this.theoryVO
    this.targetRuleVO = this.targetVO
    this.simulationRuleVO = this.simulationVO
    this.getCategoryFn()
  },
  methods: {
    isNaNs(a, b) {
      if (isNaN(b)) {
        return a
      }
      return a + b
    },
    addClass() {
      if (this.activeName === 'theory') {
        if (this.theoryRuleVO.categoryVOList.length > 30) {
          this.$message.error('分类数量不能大于30')
          return
        }
        this.theoryRuleVO.categoryVOList.push({
          categoryId: '',
          skillPointId: null,
          questionDepotId: '',
          categoryVO: {},
          primarySingle: 0, // 初级单选题
          primaryMultiple: 0, // 初级多选题
          primaryJudge: 0, // 初级判断题
          primaryCTF: 0, // 初级CTF题
          primaryCompletion: 0, // 初级填空题
          primaryShort: 0, // 初级简答题
          primaryAWD: 0, // 初级awd题
          primaryBug: 0, // 初级bug题
          primaryCombinatorial: 0, // 初级组合题
          primaryOther: 0, // 初级其它题
          middleSingle: 0, // 中级单选题
          middleMultiple: 0, // 中级多选题
          middleJudge: 0, // 中级判断题
          middleCTF: 0, // 中级CTF题
          middleCompletion: 0, // 中级填空题
          middleShort: 0, // 中级简答题
          middleAWD: 0, // 中级awd题
          middleBug: 0, // 中级bug题
          middleCombinatorial: 0, // 中级组合题
          middleOther: 0, // 中级其它题
          seniorSingle: 0, // 高级单选题
          seniorMultiple: 0, // 高级多选题
          seniorJudge: 0, // 高级判断题
          seniorCTF: 0, // 高级CTF题
          seniorCompletion: 0, // 高级填空题
          seniorShort: 0, // 高级简答题
          seniorAWD: 0, // 高级awd题
          seniorBug: 0, // 高级bug题
          seniorCombinatorial: 0, // 高级组合题
          seniorOther: 0 // 高级其它题
        })
      } else if (this.activeName === 'target') {
        if (this.targetRuleVO.categoryVOList.length > 30) {
          this.$message.error('分类数量不能大于30')
          return
        }
        this.targetRuleVO.categoryVOList.push({
          categoryId: '',
          skillPointId: null,
          questionDepotId: '',
          categoryVO: {},
          primarySingle: 0, // 初级单选题
          primaryMultiple: 0, // 初级多选题
          primaryJudge: 0, // 初级判断题
          primaryCTF: 0, // 初级CTF题
          primaryCompletion: 0, // 初级填空题
          primaryShort: 0, // 初级简答题
          primaryAWD: 0, // 初级awd题
          primaryBug: 0, // 初级bug题
          primaryCombinatorial: 0, // 初级组合题
          primaryOther: 0, // 初级其它题
          middleSingle: 0, // 中级单选题
          middleMultiple: 0, // 中级多选题
          middleJudge: 0, // 中级判断题
          middleCTF: 0, // 中级CTF题
          middleCompletion: 0, // 中级填空题
          middleShort: 0, // 中级简答题
          middleAWD: 0, // 中级awd题
          middleBug: 0, // 中级bug题
          middleCombinatorial: 0, // 中级组合题
          middleOther: 0, // 中级其它题
          seniorSingle: 0, // 高级单选题
          seniorMultiple: 0, // 高级多选题
          seniorJudge: 0, // 高级判断题
          seniorCTF: 0, // 高级CTF题
          seniorCompletion: 0, // 高级填空题
          seniorShort: 0, // 高级简答题
          seniorAWD: 0, // 高级awd题
          seniorBug: 0, // 高级bug题
          seniorCombinatorial: 0, // 高级组合题
          seniorOther: 0 // 高级其它题
        })
      } else if (this.activeName === 'simulation') {
        if (this.simulationRuleVO.categoryVOList.length > 30) {
          this.$message.error('分类数量不能大于30')
          return
        }
        this.simulationRuleVO.categoryVOList.push({
          categoryId: '',
          skillPointId: null,
          questionDepotId: '',
          categoryVO: {},
          primarySingle: 0, // 初级单选题
          primaryMultiple: 0, // 初级多选题
          primaryJudge: 0, // 初级判断题
          primaryCTF: 0, // 初级CTF题
          primaryCompletion: 0, // 初级填空题
          primaryShort: 0, // 初级简答题
          primaryAWD: 0, // 初级awd题
          primaryBug: 0, // 初级bug题
          primaryCombinatorial: 0, // 初级组合题
          primaryOther: 0, // 初级其它题
          middleSingle: 0, // 中级单选题
          middleMultiple: 0, // 中级多选题
          middleJudge: 0, // 中级判断题
          middleCTF: 0, // 中级CTF题
          middleCompletion: 0, // 中级填空题
          middleShort: 0, // 中级简答题
          middleAWD: 0, // 中级awd题
          middleBug: 0, // 中级bug题
          middleCombinatorial: 0, // 中级组合题
          middleOther: 0, // 中级其它题
          seniorSingle: 0, // 高级单选题
          seniorMultiple: 0, // 高级多选题
          seniorJudge: 0, // 高级判断题
          seniorCTF: 0, // 高级CTF题
          seniorCompletion: 0, // 高级填空题
          seniorShort: 0, // 高级简答题
          seniorAWD: 0, // 高级awd题
          seniorBug: 0, // 高级bug题
          seniorCombinatorial: 0, // 高级组合题
          seniorOther: 0 // 高级其它题
        })
      }
    },
    deleteClass(index) {
      if (this.activeName === 'theory') {
        this.theoryRuleVO.categoryVOList.splice(index, 1)
      } else if (this.activeName === 'target') {
        this.targetRuleVO.categoryVOList.splice(index, 1)
      } else if (this.activeName === 'simulation') {
        this.simulationRuleVO.categoryVOList.splice(index, 1)
      }
    },
    changeMessage() {
      this.$forceUpdate()
    },
    getCategoryFn() {
      // 获取题库列表
      getQuestionCategory({ categoryTypes: [1], pageType: 0 }).then(res => {
        this.theoryQuestionCategoryList = res.data || []
      })
      getQuestionCategory({ categoryTypes: [2], pageType: 0 }).then(res => {
        this.targetQuestionCategoryList = res.data || []
      })
      getQuestionCategory({ categoryTypes: [3], pageType: 0 }).then(res => {
        this.simulationQuestionCategoryList = res.data || []
      })
      getCategory({ categoryTypes: [1], pageType: 0 }).then(res => {
        this.theoryCategoryList = res.data.records
      })
      getCategory({ categoryTypes: [2], pageType: 0 }).then(res => {
        this.targetCategoryList = res.data.records
      })
      getCategory({ categoryTypes: [3], pageType: 0 }).then(res => {
        this.simulationCategoryList = res.data.records
      })
      // 获取技能点
      getSkillPoint({ page: 1, limit: 9999 }).then((res) => {
        if (res.code === 0 || res.code === 200) {
          this.skillPointList = res.data.records
        }
      })
    },
    categorySelect(value, key, item) {
      const { categoryId, skillPointId, questionDepotId } = item
      if (categoryId || skillPointId || questionDepotId) {
        const formData = new FormData()
        formData.append('categoryId', categoryId || '')
        formData.append('skillPointId', skillPointId || '')
        formData.append('questionDepotId', questionDepotId || '')
        categoryComplexityNumber(formData).then(res => {
          item.primarySingle = 0 // 初级单选题
          item.primaryMultiple = 0 // 初级多选题
          item.primaryJudge = 0 // 初级判断题
          item.primaryCTF = 0 // 初级CTF题
          item.primaryCompletion = 0 // 初级填空题
          item.primaryShort = 0 // 初级简答题
          item.primaryAWD = 0 // 初级awd题
          item.primaryBug = 0 // 初级bug题
          item.primaryCombinatorial = 0 // 初级组合题
          item.primaryOther = 0 // 初级其它题
          item.middleSingle = 0 // 中级单选题
          item.middleMultiple = 0 // 中级多选题
          item.middleJudge = 0 // 中级判断题
          item.middleCTF = 0 // 中级CTF题
          item.middleCompletion = 0 // 中级填空题
          item.middleShort = 0 // 中级简答题
          item.middleAWD = 0 // 中级awd题
          item.middleBug = 0 // 中级bug题
          item.middleCombinatorial = 0 // 中级组合题
          item.middleOther = 0 // 中级其它题
          item.seniorSingle = 0 // 高级单选题
          item.seniorMultiple = 0 // 高级多选题
          item.seniorJudge = 0 // 高级判断题
          item.seniorCTF = 0 // 高级CTF题
          item.seniorCompletion = 0 // 高级填空题
          item.seniorShort = 0 // 高级简答题
          item.seniorAWD = 0 // 高级awd题
          item.seniorBug = 0 // 高级bug题
          item.seniorCombinatorial = 0 // 高级组合题
          item.seniorOther = 0 // 高级其它题
          item.categoryVO = res.data
          item.categoryVO.skillPointId = skillPointId || null
          item.categoryVO.questionDepotId = questionDepotId
        })
      } else {
        item.categoryId = ''
        item.skillPointId = null
        item.questionDepotId = ''
        item.primarySingle = 0 // 初级单选题
        item.primaryMultiple = 0 // 初级多选题
        item.primaryJudge = 0 // 初级判断题
        item.primaryCTF = 0 // 初级CTF题
        item.primaryCompletion = 0 // 初级填空题
        item.primaryShort = 0 // 初级简答题
        item.primaryAWD = 0 // 初级awd题
        item.primaryBug = 0 // 初级bug题
        item.primaryCombinatorial = 0 // 初级组合题
        item.primaryOther = 0 // 初级其它题
        item.middleSingle = 0 // 中级单选题
        item.middleMultiple = 0 // 中级多选题
        item.middleJudge = 0 // 中级判断题
        item.middleCTF = 0 // 中级CTF题
        item.middleCompletion = 0 // 中级填空题
        item.middleShort = 0 // 中级简答题
        item.middleAWD = 0 // 中级awd题
        item.middleBug = 0 // 中级bug题
        item.middleCombinatorial = 0 // 中级组合题
        item.middleOther = 0 // 中级其它题
        item.seniorSingle = 0 // 高级单选题
        item.seniorMultiple = 0 // 高级多选题
        item.seniorJudge = 0 // 高级判断题
        item.seniorCTF = 0 // 高级CTF题
        item.seniorCompletion = 0 // 高级填空题
        item.seniorShort = 0 // 高级简答题
        item.seniorAWD = 0 // 高级awd题
        item.seniorBug = 0 // 高级bug题
        item.seniorCombinatorial = 0 // 高级组合题
        item.seniorOther = 0 // 高级其它题
        item.categoryVO = {}
      }
    }
  }
}
</script>

<style lang="scss" scoped>
._paper_container {
  display: flex;
  flex-direction: column;
  min-height: 100%;
  ._paper_header {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    position: relative;
    ._paper_search {
      position: absolute;
      display: flex;
      top: 6px;
      align-items: center;
      ._paper_search_1 {
        font-size: 16px;
        font-weight: 500;
        color: #333;
      }
    }
  }
  .border-b {
    border-bottom: 1px solid #d7d7d7;
  }
  .trends-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 5px 0;
    .el-form-item {
      margin: 0;
    }
    .category {
      display: flex;
      align-items: center;
      ::v-deep {
        .el-input-number {
          width: 60%;
          .el-input__inner {
            padding: 0;
          }
        }
        .el-input-number__decrease {
          display: none;
        }
        .el-input-number__increase {
          display: none;
        }
      }
      .el-form-item__content {
        display: flex;
      }
    }
    .score-form {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    > div {
      width: 10%;
      display: flex;
      justify-content: center;
    }
  }
}
.add-classification {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px 0;
  border: 1px dashed #2d8cf0;
  color: #2d8cf0;
  cursor: pointer;
}
.category-wrap {
  border: 1px solid #C0C4CC;
  border-radius: 5px;
  padding: 10px 15px;
}
.select-wrap {
  height: 36px;
  display: flex;
  justify-content: space-between;
  .left {
    display: flex;
    justify-content: flex-start;
    .select-title {
      height: 36px;
      line-height: 40px;
      font-size: 15px;
      font-weight: bold;
    }
  }
}
.card-bg {
  border-radius: 4px;
  padding: 15px;
  background-color: #FFFFFF;
}
</style>
