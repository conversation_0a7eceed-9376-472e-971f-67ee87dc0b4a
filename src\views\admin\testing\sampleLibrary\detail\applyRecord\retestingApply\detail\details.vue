<template>
  <div :loading="loading">
    <el-tabs
      v-model="tabsActive"
      class="detail-tabs-drawer detail-tabs"
      style="margin-top: 26px;"
      type="card"
    >
      <el-tab-pane label="查看详情" name="retesting-detail" />
    </el-tabs>
    <div slot="content" class="content-container">
      <el-form ref="form" label-position="left" label-width="120px" style="width: 50%;">
        <el-card>
          <el-divider content-position="left">基本信息</el-divider>
          <el-form-item label="复测任务：">
            <div v-if="data.taskList && data.taskList.length > 0">
              <span v-for="(item, index) in data.taskList" :key="item.taskName + index">
                {{ item.taskName }}<span v-if="index !== data.taskList.length - 1">、</span>
              </span>
            </div>
            <div v-else>-</div>
          </el-form-item>
          <el-form-item label="审核状态：">
            <el-badge :type="statusMapping[data.auditStatus]" is-dot />
            {{ statusObj[data.auditStatus].label || '-' }}
          </el-form-item>
          <el-form-item label="申请人：">
            {{ data.applyName || '-' }}
          </el-form-item>
          <el-form-item label="申请时间：">
            {{ data.applyTime || '-' }}
          </el-form-item>
          <el-form-item label="申请原因：">
            <div v-if="data.applyReason" class="common-html" v-html="data.applyReason" />
            <div v-else>-</div>
          </el-form-item>
          <el-form-item label="审核人：">
            {{ data.auditUserName || '-' }}
          </el-form-item>
          <el-form-item label="审核时间：">
            {{ data.auditTime || '-' }}
          </el-form-item>
          <el-form-item label="审核意见：">
            <div v-if="data.auditComment" class="common-html" v-html="data.auditComment" />
            <div v-else>-</div>
          </el-form-item>
        </el-card>
      </el-form>
    </div>
  </div>
</template>

<script>
import statusConf from '../../config.js'
export default {
  name: 'RetestingApplyDetail',
  props: {
    data: {
      type: Object,
      default: () => {}
    },
    projectId: {
      type: Number,
      default: null
    }
  },
  data() {
    return {
      loading: false,
      tabsActive: 'retesting-detail',
      statusObj: statusConf.statusObj,
      statusMapping: statusConf.statusMapping
    }
  },
  methods: {
  }
}
</script>

<style scoped lang="scss">
.content-container {
  max-height: 93vh;
  padding-top: 20px;
  overflow-y: auto;
  .common-html {
    white-space: pre-line;
    line-height: 28px;
    padding-top: 6px;
  }
}

.el-tabs--card > .el-tabs__header .el-tabs__item.is-active {
  border-bottom-color: var(--color-600);
}

::v-deep.el-card.is-always-shadow,
.el-card.is-hover-shadow:focus,
.el-card.is-hover-shadow:hover {
  box-shadow: none;
}

::v-deep .el-card__body {
  padding: 6px 20px;
}

::v-deep .el-form .el-form-item {
  margin-bottom: 5px;
}
</style>
