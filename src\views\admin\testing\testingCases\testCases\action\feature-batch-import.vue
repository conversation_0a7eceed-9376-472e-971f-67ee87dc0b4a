<template>
  <div class="dialog-wrap">
    <el-steps
      :active="stepsActive"
      simple>
      <el-step
        v-for="(item, index) in stepList"
        :key="index"
        :title="item.title"
        :icon="item.icon"
        :status="item.status" />
    </el-steps>
    <div
      v-if="stepsActive == 1"
      class="sections">
      <el-form ref="form" v-model="formData" label-width="80px">
        <el-form-item label="文件">
          <div class="file-up">
            <el-button type="ghost" @click="selectFile()">上传文件</el-button>
            <div
              class="download"
              @click="download">
              <i class="el-icon-download"/>
              下载导入模板</div>
          </div>
          <div style="font-size: 12px; color: #999;margin-top:-10px">支持上传.xlsx、.xls文件，且大小不超过100MB。</div>
          <div v-show="file.name" style="border-color: var(--color-601-border); background-color: var(--color-601-background)" class="file-container">
            <i :style="{ 'color': '#909399' }" class="el-icon-document" size="16" />
            <span :title="file.name" style="color: #000000d9;margin: 10px 10px 0 0;">{{ file.name }}</span>
            <i style="color: var(--color-600);cursor: pointer;" class="el-icon-delete delete-icon delete" @click.stop="clearFileName()" />
          </div>
        </el-form-item>
      </el-form>
    </div>
    <div
      v-loading="loading"
      v-else-if="stepsActive == 2"
      element-loading-text="数据导入中"
      element-loading-spinner="el-icon-loading"
      class="sections" />

    <div
      v-loading="loading"
      v-else-if="stepsActive == 3"
      class="sections">
      <div class="result-info">
        <i class="wk wk-success result-info__icon" />
        <p class="result-info__des">数据导入完成</p>
        <p v-if="resultData" class="result-info__detail" >导入总数据<span class="result-info__detail--all">{{ resultData.totalSize }}</span>条，导入成功<span class="result-info__detail--suc"><template v-if="resultData">{{ resultData.totalSize - (resultData.errSize || 0) }}</template></span>条，导入失败<span class="result-info__detail--err">{{ resultData.errSize || 0 }}</span>条</p>
        <el-button
          v-if="resultData && resultData.errSize > 0"
          class="result-info__btn--err"
          style="cursor: pointer; color: #2362fb;"
          type="text"
          @click="downloadErrData">下载错误数据</el-button>
      </div>
    </div>
    <input
      id="importInputFile"
      ref="userFileInput"
      type="file"
      @change="handleChange">
    <div class="dialog-footer">
      <el-button
        :class="{ 'is-hidden': !showCancel }"
        type="text"
        @click="closeView">取消</el-button>
      <el-button
        v-if="sureTitle"
        type="primary"
        @click="sureClick">{{ sureTitle }}</el-button>
    </div>
  </div>
</template>

<script>
import { downloadExcelWithResData } from '@/utils'
import { downloadTemplate, testCaseImport, downErrorExcel } from '@/api/testing/testCase'

const delErrorExcel = (formData) => {
  return Promise.resolve({
    code: 0,
    data: true
  })
}

export default {
  name: 'BatchImport',
  components: {},
  props: {
    show: {
      type: Boolean,
      default: false
    },
    info: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      maxSize: 1024 * 1024 * 100, // 文件最大大小 100MB
      loading: false,
      fileTypes: ['xlsx', 'xls'],
      showDialog: false,
      file: { name: '' },
      stepsActive: 1,
      formData: {},
      stepList: [
        {
          icon: 'wk wk-upload',
          title: '上传文件',
          status: 'wait'
        },
        {
          icon: 'wk wk-data-import',
          title: '导入数据',
          status: 'wait'
        },
        {
          icon: 'wk wk-success',
          title: '导入完成',
          status: 'wait'
        }
      ],
      resultData: null
    }
  },
  computed: {
    sureTitle() {
      return {
        1: '导入',
        2: '',
        3: '确定'
      }[this.stepsActive]
    },
    showCancel() {
      return this.stepsActive != 2
    }
  },
  watch: {
    show: function(val) {
      this.showDialog = val
      this.resetData()
    }
  },
  methods: {
    clearFileName() {
      this.file = { name: '' }
      this.stepList[0].status = 'wait'
      this.$refs.userFileInput.value = ''
    },
    sureClick() {
      if (this.stepsActive == 1) {
        if (this.stepList[0].status == 'finish') {
          this.stepList[1].status = 'process'
          this.stepsActive = 2
          this.updateFile(res => {
            this.stepList[1].status = 'finish'
            this.stepsActive = 3
            if (res.data) {
              this.resultData = res.data
              if (res.data.errSize > 0) {
                this.stepList[2].status = 'error'
              } else {
                this.stepList[2].status = 'finish'
              }
            }
          })
        } else {
          this.$message.error('请选择导入文件')
        }
      } else if (this.stepsActive == 3) {
        this.$emit('call', 'refresh')
        this.resetData()
        this.closeView()
      }
    },

    updateFile(result) {
      if (!this.file.name) {
        this.$message.error('请选择导入文件')
      } else {
        this.loading = true
        const formData = new FormData()
        formData.append('file', this.file)
        testCaseImport(formData, 1).then(res => {
          this.loading = false
          if (result) {
            result(res)
          }
          this.$emit('success')
        }).catch(() => {
          if (result) {
            result(false)
          }
          this.stepsActive = 1
          this.loading = false
        })
      }
    },

    /**
     * 下载错误
     */
    downloadErrData() {
      this.getImportError(this.resultData.token)
    },

    /**
     * 导入错误下载
     */
    getImportError(token) {
      this.loading = true
      const formData = new FormData()
      formData.append('token', token)
      downErrorExcel(formData)
        .then(res => {
          downloadExcelWithResData(res)
          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    },

    /**
     * 下载模板操作
     */
    download() {
      const params = {
        type: 1
      }
      downloadTemplate(params).then(res => {
        downloadExcelWithResData(res)
      })
    },

    /**
     * 选择文件选择文件
     */
    selectFile() {
      this.$refs.userFileInput.click()
    },

    /**
     * 选择触发
     */
    handleChange(e) {
      const files = e.target.files
      if (!files || !files.length) {
        return
      }
      const fileArr = files[0].name.split('.')
      const fileType = fileArr[fileArr.length - 1]
      if (!this.fileTypes.includes(fileType.toLowerCase())) {
        this.$message.error('支持上传.xlsx、.xls文件')
        return
      }
      if (this.maxSize && files[0].size > this.maxSize) {
        this.$message.error(`文件大小不能超过${this.$options.filters['transStore'](this.maxSize, 'B', 0)}`)
        return
      } else {
        this.file = files[0]
        this.stepList[0].status = 'finish'
      }
    },

    /**
     * 关闭
     */
    closeView() {
      if (this.resultData && this.resultData.token) {
        const formData = new FormData()
        formData.append('token', this.resultData.token)
        delErrorExcel(formData).then(res => {}).catch(() => {})
      }
      this.resetData()
      this.$emit('call', 'refresh')
      this.$emit('close')
    },

    /**
     * 重置数据
     */
    resetData() {
      this.file = { name: '' }
      this.stepList = [
        {
          icon: 'wk wk-upload',
          title: '上传文件',
          status: 'wait'
        },
        {
          icon: 'wk wk-data-import',
          title: '导入数据',
          status: 'wait'
        },
        {
          icon: 'wk wk-success',
          title: '导入完成',
          status: 'wait'
        }
      ]
      this.stepsActive = 1
      this.resultData = null
    }
  }
}
</script>

<style scoped lang="scss">
.el-steps {
  margin-bottom: 15px;

  /deep/ .el-step__title {
    font-size: 14px;
  }

  /deep/ .el-step.is-simple .el-step__arrow::before,
  /deep/ .el-step.is-simple .el-step__arrow::after {
    height: 10px;
    width: 2px;
  }

  /deep/ .el-step.is-simple .el-step__arrow::after {
    transform: rotate(45deg) translateY(3px);
  }
  /deep/ .el-step.is-simple .el-step__arrow::before {
    transform: rotate(-45deg) translateY(-2px);
  }
}

.sections {
  font-size: 14px;
  min-height: 120px;
  padding: 0 20px;
  .file-up {
    display: flex;
    align-items: center;
  }
  .file-container {
    overflow-wrap: break-word;
    word-break: normal;
    line-height: 1.5;
    border: 1px solid;
    margin-top: 5px;
    padding: 6px 18px 6px 10px;
    overflow: hidden;
    text-overflow:ellipsis;
    white-space: nowrap;
    cursor: pointer;
    position: relative;
    .delete {
      position: absolute;
      top: 10px;
      right: 10px;
    }
  }
  .download {
    cursor: pointer;
    color: var(--color-600);
    margin-left: 20px;
  }

  /deep/ .el-loading-spinner {
    top: 45%;
    .el-icon-loading {
      font-size: 40px;
      color: #999;
    }

    .el-loading-text {
      color: #333;
    }
  }
}

.content {
  padding: 10px 0;
}

.content-tips {
  font-size: 12px;
  color: #999;
  line-height: 15px;
}

#importInputFile {
  display: none;
}

.file-select {
  .el-input {
    width: 400px;
  }
  button {
    margin-left: 20px;
  }
}

.is-hidden {
  visibility: hidden;
}

// 结果信息
.result-info {
  text-align: center;
  padding-top: 30px;

  &__icon {
    font-size: 40px;
    color: $xr-color-primary;
  }

  &__des {
    margin-top: 15px;
    color: #333;
    font-size: 14px;
  }

  &__detail {
    margin-top: 15px;
    font-size: 12px;
    color: #666;
    &--all {
      color: #333;
      font-weight: 600;
    }

    &--suc {
      color: $xr-color-primary;
      font-weight: 600;
    }

    &--err {
      color: #f94e4e;
      font-weight: 600;
    }
  }

  &__btn--err {
    margin-top: 10px;
  }
}
</style>
