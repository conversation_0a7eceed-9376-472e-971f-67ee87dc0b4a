<template>
  <div class="content-wrap-layout">
    <top-nav />
    <!-- 分类区 -->
    <category :style="{'height': fold ? '0' : 'unset', 'overflow': fold ? 'hidden' : 'unset'}" :category-name="moduleName" :category-type="2" @switchCategory="switchCategory"/>
    <page-table
      ref="table"
      :filter-data="{ 'bankType': 2, 'questionDepotIds': !questionDepotId ? [] : [questionDepotId], 'categoryIds': !categoryId ? [] : [categoryId] }"
      :default-selected-arr="defaultSelectedArr"
      :cache-pattern="true"
      :module-name="moduleName"
      :bank-type="2"
      @refresh="refresh"
      @link-event="linkEvent"
      @on-select="tabelSelect"
      @on-current="tabelCurrent"
    >
      <action-menu
        slot="action"
        :bank-type="2"
        :module-name="moduleName"
        :select-item="selectItem"
        @call="actionHandler"
      />
    </page-table>
    <el-drawer
      :visible.sync="detailShow"
      :modal="false"
      size="75%"
      append-to-body
      class="detail-view"
      @close="closeDetail"
    >
      <t-detail v-if="detailShow" />
    </el-drawer>
  </div>
</template>

<script>
import moduleConf from './config'
import topNav from '../index_top_nav'
import category from '../category/index'
import pageTable from '../table/index'
import actionMenu from '../action/index'
import tDetail from '../detail/index.vue'
import moduleMixin from '@/packages/mixins/module_list'
import lodash from 'lodash'
export default {
  components: {
    topNav,
    category,
    pageTable,
    actionMenu,
    tDetail
  },
  mixins: [moduleMixin],
  data() {
    return {
      fold: false,
      listRouterName: 'targetDevice',
      moduleName: moduleConf.name,
      selectItem: [],
      defaultSelectedArr: [],
      questionDepotId: '',
      categoryId: '',
      num: 0
    }
  },
  methods: {
    switchCategory: lodash.debounce(function(value) {
      this.questionDepotId = value.questionDepotId
      this.categoryId = value.categoryId
      if (!this.$store.state.cache[this.moduleName]) {
        const obj = {
          data: { searchShow: true },
          key: this.moduleName
        }
        this.$store.commit('SET_CACHE', obj)
      }
      this.$nextTick(() => {
        if (this.num != 0) {
          this.$refs['table']['pageCurrent'] = 1
        } else {
          this.num = this.num + 1
        }
        this.$refs['table'].getList()
      })
    }, 500),
    // 列表点击
    linkEvent: function({ name, row, params }) {
      this.$router.push({ name: name, params: params })
    },
    // 返回已选
    tabelSelect: function(data) {
      this.selectItem = data
    },
    // 返回单选
    tabelCurrent: function(row) {
      this.selectItem = [row]
    },
    // action menu 事件
    actionHandler: function(type, data) {
      switch (type) {
        case 'refresh':
          this.$refs['table'].getList()
          break
        case 'fold':
          this.fold = data
          break
      }
    },
    refresh: function() {}
  }
}
</script>
