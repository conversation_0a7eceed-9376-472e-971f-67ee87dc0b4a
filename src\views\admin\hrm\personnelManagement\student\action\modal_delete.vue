<template>
  <div v-loading="loading" class="dialog-wrap">
    <el-alert :closable="false" type="warning">
      <div slot="title">
        <p>{{ `学员${unFinishStudentStr}尚有未完成的教学事务，请谨慎操作。` }}</p >
      </div>
    </el-alert>
    <batch-template
      :data="data"
      :available-data="data"
      view-key="realname"
    />
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button :disabled="!data.length" type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>

<script>
import batchTemplate from '@/packages/batch-delete/modal-bat-template.vue'
import modalMixins from '@/packages/mixins/modal_form'
import { deleteStudentRole } from '@/api/admin/training/student'

export default {
  components: {
    batchTemplate
  },
  mixins: [modalMixins],
  props: {
    data: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      loading: false
    }
  },
  computed: {
    // 有未完成任务的学员
    unFinishStudentStr() {
      let realNameStr = ''
      this.data.filter(item => item.inSchedule).map(item => {
        realNameStr += `“${item.realname}”、`
      })
      return realNameStr.slice(0, -1)
    }
  },
  methods: {
    close() {
      this.$emit('close')
    },
    saveJoinCourse(postData) {
      return new Promise((resolve, reject) => {
        deleteStudentRole(postData).then(res => {
          resolve(res)
        })
      })
    },
    confirm: function() {
      this.loading = true
      const idArr = this.data.map(item => {
        return { userId: item.userId, realname: item.realname }
      })
      idArr.map((item, index) => {
        this.saveJoinCourse({ userId: item.userId, realname: item.realname })
          .then((res) => {
            this.$message.success(res.data)
          })
      })
      setTimeout(() => {
        this.$emit('call', 'refresh')
        this.close()
        this.loading = false
      }, 500)
    }
  }
}
</script>
