<template>
  <div v-loading="loading" class="dialog-wrap">
    <el-form ref="form" label-position="left" label-width="120px">
      <el-form-item label="内网IP地址">
        <div v-for="(item, index) in nodeData.ports.filter(val => val.ipaddr)" :key="index">{{ item.ipaddr }}</div>
      </el-form-item>
      <el-form-item label="控制台连接地址">
        <div v-for="(item, index) in linkAddress" :key="index">{{ item.url }}</div>
      </el-form-item>
    </el-form>
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button type="primary" @click="close">确定</el-button>
    </div>
  </div>
</template>
<script>
import { vnc, serial, getConsole, getNodeItem } from '../api/orchestration'
export default {
  components: {},
  mixins: [],
  props: {
    // 传入数据
    nodeId: {
      type: String,
      default: () => {
        return ''
      }
    }
  },
  data() {
    return {
      loading: true,
      nodeData: {},
      linkAddress: []
    }
  },
  created() {
    getNodeItem(this.nodeId)
      .then(res => {
        this.nodeData = res.data.data
        const consoleArr = this.nodeData.console_type.split(',')
        const fetchArr = []
        consoleArr.forEach(item => {
          switch (item) {
            case 'vnc': {
              fetchArr.push(this.vnc())
              break
            }
            case 'rdp': {
              fetchArr.push(this.getConsole('rdp'))
              break
            }
            case 'ssh': {
              fetchArr.push(this.getConsole('ssh'))
              break
            }
            case 'webshell': {
              fetchArr.push(this.getConsole('webshell'))
              break
            }
            case 'serial': {
              fetchArr.push(this.serial())
              break
            }
          }
        })
        Promise.all(fetchArr).then(() => {
          this.loading = false
        }).finally(() => {
          this.loading = false
        })
      })
      .catch(() => {
        this.loading = false
      })
  },
  methods: {
    vnc() {
      return new Promise((resolve, reject) => {
        vnc(this.nodeId).then(res => {
          const url = res.data.data
          this.linkAddress.push({ label: 'VNC', url: url })
          resolve(url)
        }).catch(() => {
          reject()
        })
      })
    },
    getConsole(type) {
      return new Promise((resolve, reject) => {
        getConsole(this.nodeId, { console_type: type }).then(res => {
          const url = (window.location.hostname == 'localhost' ? (window.ADMIN_CONFIG ? window.ADMIN_CONFIG.VIP_URL : window.WEB_CONFIG.VIP_URL) : '') + res.data.data
          this.linkAddress.push({ label: type.toUpperCase(), url: url })
          resolve(url)
        }).catch(() => {
          reject()
        })
      })
    },
    serial() {
      return new Promise((resolve, reject) => {
        serial(this.nodeId).then(res => {
          const url = res.data.data + '&title=' + this.nodeData.name
          this.linkAddress.push({ label: '串口控制台', url: url })
          resolve(url)
        }).catch(() => {
          reject()
        })
      })
    },
    close: function() {
      this.$emit('close')
    }
  }
}
</script>
