<template>
  <div class="buttons-wrap">
    <el-button v-permission="'manage.testing.project.projectDetail.deviceLog.fileTransferRecords.deviceLogFileTransferRecordsExport'" type="primary" @click="clickDrop('exportRecord')">导出</el-button>
    <!-- 中部弹窗 start-->
    <el-dialog
      :title="titleMapping[modalName]"
      :visible.sync="modalShow"
      :width="modalWidth"
      :destroy-on-close="true"
      append-to-body
      @close="modalClose"
    >
      <transition name="el-fade-in-linear">
        <component
          :is="modalName"
          :name="modalName"
          :data="selectItem"
          @close="modalClose"
          @call="confirmCall"
        />
      </transition>
    </el-dialog>
    <!-- 中部弹窗 end-->
  </div>
</template>
<script>
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
import exportRecord from './modal-export'
export default {
  components: {
    exportRecord
  },
  mixins: [mixinsActionMenu],
  props: {
    selectItem: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      // 弹窗title映射
      titleMapping: {
        'exportRecord': '导出'
      }
    }
  },
  methods: {
    confirmCall: function(type, data) {
      if (type === 'close') {
        this.modalClose()
      } else if (type === 'refresh') {
        this.$emit('call', type)
      }
    },
    'clickDrop': function(name) {
      this.modalName = name
    }
  }
}
</script>
