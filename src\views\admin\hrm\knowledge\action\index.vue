<template>
  <div class="buttons-wrap">
    <el-button type="primary" icon="el-icon-plus" @click="clickDrop('addKnowledge')">创建</el-button>
    <el-button :disabled="singleDisabled" type="primary" @click="clickDrop('editKnowledge')">编辑</el-button>
    <el-button :disabled="selectItem.length < 1" type="primary" @click="clickDrop('deleteKnowledge')">删除</el-button>
    <!-- 中部弹窗 start-->
    <el-dialog
      :title="titleMapping[modalName]"
      :visible.sync="modalShow"
      :width="modalWidth"
      destroy-on-close
      @close="modalClose"
    >
      <transition name="el-fade-in-linear">
        <component
          :is="modalName"
          :name="modalName"
          :data="selectItem"
          @close="modalClose"
          @call="confirmCall"
        />
      </transition>
    </el-dialog>
    <!-- 中部弹窗 end-->
  </div>
</template>
<script>
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
import addKnowledge from './modal-add.vue'
import editKnowledge from './modal-add.vue'
import deleteKnowledge from './modal-delete.vue'
export default {
  components: {
    addKnowledge,
    editKnowledge,
    deleteKnowledge
  },
  mixins: [mixinsActionMenu],
  data() {
    return {
      // 弹窗title映射
      titleMapping: {
        'editKnowledge': '编辑知识点',
        'addKnowledge': '创建知识点',
        'deleteKnowledge': '删除知识点'
      },
      dialogLoading: false,
      confirmDisabled: false,
      // 多选
      selectList: []
    }
  },
  mounted() {
    // 从创建题目中创建知识点跳转过来
    if (this.$route.query && this.$route.query.openCreateDialog) {
      this.modalName = 'addKnowledge'
    }
    this.token = JSON.parse(localStorage.getItem('Admin-Token')).data
  },
  methods: {
    confirmCall: function(type, data) {
      if (type === 'close') {
        this.modalClose()
      } else if (type === 'refresh') {
        this.$emit('call', type)
      } else if (type === 'upload') {
        this.$emit('call', type)
      } else if (type === 'confirmDisabled') {
        this.confirmDisabled = data
      }
    }
  }
}
</script>
