
import request from '@/utils/request'
const headers = {
  'Content-Type': 'application/json;charset=UTF-8'
}

// 部门树形列表
export function getDeptListAPI(data) {
  return request({
    url: 'adminDept/queryDeptTree',
    method: 'post',
    data: data,
    headers
  })
}

// 添加部门
export const addDeptAPI = (data) => {
  return request({
    url: '/adminDept/addDept',
    method: 'post',
    data,
    headers
  })
}

// 编辑部门
export const editDeptAPI = (data) => {
  return request({
    url: '/adminDept/setDept',
    method: 'post',
    data,
    headers
  })
}

// 删除部门
export const deleteDeptAPI = (id) => {
  return request({
    url: `/adminDept/deleteDept/${id}`,
    method: 'post',
    headers
  })
}
