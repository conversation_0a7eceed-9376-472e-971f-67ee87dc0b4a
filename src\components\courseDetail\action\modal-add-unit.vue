<template>
  <div v-loading="loading" class="dialog-wrap">
    <div class="dialog-wrap-content">
      <el-form ref="form" :model="formData" :rules="rules" label-position="left" label-width="90px">
        <el-form-item label="单元名称:" prop="name">
          <el-input v-model.trim="formData.name" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
    </div>
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>
<script>
import validate from '@/packages/validate'
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
import { addChapterApi } from '@/api/teacher/index.js'
export default {
  components: {
  },
  mixins: [mixinsActionMenu],
  props: {
    // 传入数据
    data: {
      type: Array,
      default: () => {
        return []
      }
    },
    courseId: {
      type: String,
      default: ''
    },
    // 新增的下标
    addIndex: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      loading: false,
      validate: validate,
      formData: {
        name: ''
      },
      rules: {
        name: [validate.required()]
      },
      id: 1
    }
  },
  methods: {
    // 新增单元节点
    close: function() {
      this.$emit('close')
    },
    confirm: function() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.loading = true
          const params = {
            name: this.formData.name,
            type: 1,
            sort: this.addIndex,
            parentId: this.data[0].id,
            pjtCourseId: this.courseId
          }
          addChapterApi(params).then((res) => {
            if (res.code === 0) {
              this.$message.success('单元添加成功')
              this.$emit('call', 'refresh')
              this.close()
            }
          }).catch(() => {
            this.loading = false
          })
        } else {
          return false
        }
      })
    }
  }
}
</script>


