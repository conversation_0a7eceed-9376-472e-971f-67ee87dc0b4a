/* 改变主题色变量 */
$--color-primary: #069072;

/** input */
$--input-height: 32px;
$--input-border-radius: 2px;

// /** button */
$--button-padding-vertical: 4px;
$--button-padding-horizontal: 15px;
$--button-border-radius: 2px;
$--button-font-size: 14px;

/* Radio */
$--radio-font-size: 13px;
$--select-font-size: 12px;

/* Checkbox */
$--checkbox-font-size: 13px;

/* 改变 icon 字体路径变量，必需 */
$--font-path: '~element-ui/lib/theme-chalk/fonts';

@import "~element-ui/packages/theme-chalk/src/index";

// 主要的一步：
:export {
  theme: $--color-primary;
}
