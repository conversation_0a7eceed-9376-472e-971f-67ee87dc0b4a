<template>
  <div class="content-wrap-layout">
    <top-nav />
    <div class="vertical-wrap">
      <page-table
        ref="table"
        :default-selected-arr="defaultSelectedArr"
        :cache-pattern="true"
        :filter-data="{}"
        @transmitTime="transmitTime"
        @refresh="refresh"
        @link-event="linkEvent"
        @on-select="tabelSelect"
        @on-current="tabelCurrent"
      >
        <action-menu
          slot="action"
          :module-name="moduleName"
          :select-item="selectItem"
          :time-params="timeParams"
          @call="actionHandler"
        />
      </page-table>
    </div>
    <el-drawer
      :visible.sync="detailShow"
      :modal="false"
      size="75%"
      append-to-body
      class="detail-view"
      @close="closeDetail"
    >
      <t-detail v-if="detailShow" />
    </el-drawer>
  </div>
</template>

<script>
import moduleConf from './config'
import topNav from '../index_top_nav'
import pageTable from './table/index.vue'
import actionMenu from './action/index.vue'
import tDetail from './detail/index.vue'
import moduleMixin from '@/packages/mixins/module_list'
export default {
  // 学生管理
  name: 'StudentManage',
  components: {
    topNav,
    pageTable,
    actionMenu,
    tDetail
  },
  mixins: [moduleMixin],
  data() {
    return {
      detailShowOfName: ['learningProcess'],
      listRouterName: 'studentManage',
      moduleName: moduleConf.name,
      selectItem: [],
      defaultSelectedArr: [],
      timeParams: {}
    }
  },
  methods: {
    // 列表点击
    linkEvent: function({ name, row, params }) {
      this.$router.push({ name: name, params: params })
    },
    // 返回已选
    tabelSelect: function(data) {
      console.log(data)
      this.selectItem = data
    },
    // 返回单选
    tabelCurrent: function(row) {
      this.selectItem = [row]
    },
    transmitTime(val) {
      this.timeParams = val
    },
    // action menu 事件
    actionHandler: function(type, data) {
      switch (type) {
        case 'refresh':
          this.$refs['table'].getList()
      }
    },
    refresh: function() {
    }
  }
}
</script>
