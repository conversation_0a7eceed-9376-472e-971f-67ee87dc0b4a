<template>
  <div class="buttons-wrap">
    <el-button icon="el-icon-plus" type="primary" @click="clickDrop('resource')">关联资源</el-button>
    <el-dropdown style="margin-right: 4px;" trigger="click" placement="bottom-start" @command="clickDrop">
      <el-button type="primary">
        操作<i class="el-icon-arrow-down el-icon--right" />
      </el-button>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item :disabled="multipleDisabled" command="deleteItem">删除</el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
    <!-- 弹窗 -->
    <el-dialog
      :title="titleMapping[modalName]"
      :visible.sync="modalShow"
      :width="modalWidth"
      @close="modalClose"
    >
      <transition name="el-fade-in-linear">
        <component
          :is="modalName"
          :name="modalName"
          :data="selectItem"
          @close="modalClose"
          @call="confirmCall"
        />
      </transition>
    </el-dialog>
    <!-- 侧拉弹窗 start -->
    <el-drawer
      :title="titleMapping[drawerName]"
      :visible.sync="drawerShow"
      :size="drawerWidth"
      @close="drawerClose"
    >
      <transition name="el-fade-in-linear">
        <component
          :is="drawerName"
          :name="drawerName"
          :data="selectItem"
          @close="drawerClose"
          @call="drawerConfirmCall"
        />
      </transition>
    </el-drawer>
    <!-- 侧拉弹窗 end -->
  </div>
</template>

<script>
import resource from './modal-resource.vue'
import deleteItem from './modal-deleteItem.vue'
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
export default {
  name: 'ActionMenu',
  components: {
    resource,
    deleteItem
  },
  mixins: [mixinsActionMenu],
  props: {
    moduleName: {
      type: String,
      default: ''
    },
    selectItem: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      drawerAction: [], // 需要侧拉打开的操作
      // 弹窗title映射
      titleMapping: {
        'resource': '关联资源',
        'deleteItem': '移出资源'
      }
    }
  },
  methods: {
    confirmCall: function(type, data) {
      if (type === 'close') {
        this.modalClose()
      } else if (type === 'refresh') {
        this.$emit('call', type)
      }
    },
    drawerConfirmCall: function(type, data) {
      if (type === 'close') {
        this.drawerClose()
      } else if (type === 'refresh') {
        this.$emit('call', type)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.action-menu {
  display: inline-block;
}
</style>
