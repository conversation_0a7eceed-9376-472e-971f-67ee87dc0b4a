<template>
  <detail-view
    :loading="loading"
    :data="data"
    :id="id"
    :view-item="viewItem"
    :show-header="false"
    title-key="name"
  />
</template>

<script>
import moduleConf from '../config'
import detailView from '@/packages/detail-view/index'
import detailOverview from './detail_overview'
import detailTask from './testingTask/index'
import detailEnvironment from './environment/index.vue'
import detailIssuesList from './issuesList/index.vue'
import detailTestingReport from './testingReport/index.vue'
import detailApplyRecord from './applyRecord/index.vue'
import detailDeviceLogs from './deviceLogs/index.vue'
import detailAttachments from './attachments/index.vue'
import { getSampleProjectById } from '@/api/testing/index'
export default {
  name: 'SampleLibraryDetail',
  components: {
    detailView,
    detailOverview,
    detailTask,
    detailEnvironment,
    detailIssuesList,
    detailTestingReport,
    detailApplyRecord,
    detailDeviceLogs,
    detailAttachments
  },
  data() {
    return {
      moduleName: moduleConf.name,
      id: null, // 资源ID
      data: null, // 资源数据对象
      loading: true,
      viewItem: [
        {
          transName: '概况',
          name: 'overview',
          component: detailOverview
        },
        {
          transName: '测试任务',
          name: 'tasks',
          component: detailTask
        },
        {
          transName: '测试环境',
          name: 'environment',
          component: detailEnvironment
        },
        {
          transName: '测试报告',
          name: 'testingReport',
          component: detailTestingReport
        },
        {
          transName: '问题清单',
          name: 'issuesList',
          component: detailIssuesList
        },
        {
          transName: '申请记录',
          name: 'applyRecord',
          component: detailApplyRecord
        },
        {
          transName: '设备日志',
          name: 'deviceLogs',
          component: detailDeviceLogs
        },
        {
          transName: '附件',
          name: 'attachments',
          component: detailAttachments
        }
      ]
    }
  },
  watch: {
    '$route': function(to, from) {
      const toId = to.params.hasOwnProperty('id') ? to.params.id : null
      const fromId = from.params.hasOwnProperty('id') ? from.params.id : null
      if (toId !== fromId) {
        this.loadBase()
      }
    }
  },
  created() {
    this.loadBase()
  },
  methods: {
    actionHandler: function(type, data) {
      switch (type) {
        case 'refresh':
          this.loadBase()
      }
    },
    'loadBase': function() {
      if (this.$route.name !== 'sampleLibraryTaskDetail') {
        this.id = this.$route.params.id
        this.getData(this.id)
      }
    },
    // 根据id获取详情数据
    'getData': function(id) {
      if (id) {
        this.loading = true
        getSampleProjectById({ id }).then(res => {
          if (res.data && res.data.code === 0) {
            this.data = res.data.data
          } else {
            this.$message.error(res.data.msg || '获取项目详情失败')
          }
          this.loading = false
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
