<template>
  <el-form ref="form" :model="formData" :rules="rules" label-position="left" label-width="120px">
    <el-form-item label="多会话" prop="value">
      <el-switch v-if="editMode" :active-value="1" :inactive-value="0" v-model="formData.value" />
      <span v-else>
        <el-badge :type="formData.value ? 'success' : 'danger'" is-dot />{{ formData.value ? '开' : '关' }}
      </span>
    </el-form-item>
  </el-form>
</template>
<script>
import validate from '@/packages/validate'
export default {
  props: {
    // 传入数据
    data: {
      type: Object,
      default: () => null
    },
    editMode: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      loading: false,
      validate: validate,
      formData: {
        value: 1
      },
      rules: {
      }
    }
  },
  watch: {
    data(val) {
      if (val) {
        this.formData['value'] = Number(val['value'])
      }
    }
  },
  created() {
    if (this.data) {
      this.formData['value'] = Number(this.data['value'])
    }
  }
}
</script>
<style lang="scss" scoped>
.license-notice-days-form {
  /deep/ .el-form-item__error {
    text-indent: 46px;
  }
}
</style>
