<template>
  <page-table
    ref="table"
    :default-selected-arr="defaultSelectedArr"
    :cache-pattern="true"
    default-selected-key="id"
    @refresh="refresh"
    @link-event="linkEvent"
    @on-select="tabelSelect"
    @on-current="tabelCurrent"
    @show-drawer-detail="showDrawerDetail"
  />
</template>

<script>
import moduleConf from './config'
import pageTable from './table/index.vue'
export default {
  // 延期申请
  name: 'DelayApply',
  components: {
    pageTable
  },
  data() {
    return {
      moduleName: moduleConf.name,
      selectItem: [],
      defaultSelectedArr: []
    }
  },
  methods: {
    // 列表点击
    linkEvent: function({ name, row, params }) {
      this.$router.push({ name: name, params: params })
    },
    // 返回已选
    tabelSelect: function(data) {
      this.selectItem = data
    },
    // 返回单选
    tabelCurrent: function(row) {
      this.selectItem = [row]
    },
    // action menu 事件
    actionHandler: function(type, data) {
      switch (type) {
        case 'refresh':
          this.selectItem = []
          this.$refs['table'].getList(false)
      }
    },
    showDrawerDetail(val) {
      this.$emit('show-detail', val)
    },
    refresh: function() {}
  }
}
</script>
