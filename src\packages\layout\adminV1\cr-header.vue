<template>
  <div class="cr-header">
    <div class="cr-header-left">
      <!-- 全部菜单 -->
      <el-popover
        v-if="showAllMenu"
        v-model="allMenuVisible"
        :visible-arrow="false"
        :popper-class="'sitemap-popper ' + (notifyShow ? 'notify-show' : '')"
        placement="bottom-start"
        width="750"
        trigger="click">
        <div class="sitemap">
          <div v-for="(item,index) in allMenuConfig" :key="index" class="menu-level1-container">
            <div class="menu-level1-title">
              <span>{{ item.title }}</span>
              <span class="menu-level1-title-line"/>
            </div>
            <div v-if="item.subs && !item.special" class="menu-level1-content">
              <div v-for="(subs, subsIndex) in item.subs" :key="subsIndex" class="menu-level2-item">
                <div class="menu-level2-item-label">
                  <span v-if="!getManageRouters(subs.path)" class="can-click" @click="go(subs)">{{ subs.title }}</span>
                  <span v-else>{{ subs.title }}</span>
                </div>
                <div v-if="getManageRouters(subs.path) && getManageRouters(subs.path).children" class="menu-level2-item-list">
                  <div v-for="(meta, metaIndex) in getManageRouters(subs.path).children" v-show="!meta.hidden" :key="metaIndex" class="menu-level2-item-list-item">
                    <span @click="go(subs, meta)">{{ meta.meta.title }}</span>
                  </div>
                </div>
              </div>
            </div>
            <div v-else-if="item.special" class="menu-level1-content">
              <div v-for="(subs, subsIndex) in item.subs" :key="subsIndex" class="menu-level2-item">
                <div class="menu-level2-item-label">
                  <span v-if="subs">{{ subs.title }}</span>
                </div>
                <div v-if="subs.children" class="menu-level2-item-list">
                  <div v-for="(meta, metaIndex) in subs.children" :key="metaIndex" class="menu-level2-item-list-item">
                    <span @click="go(meta)">{{ meta.title }}</span>
                  </div>
                </div>
              </div>
            </div>
            <div v-else class="menu-level1-content">
              <div class="menu-level2-item">
                <div class="menu-level2-item-label">
                  <span v-if="!getManageRouters(item.path)" class="can-click" @click="go(item)">{{ item.title }}</span>
                  <span v-else>{{ item.title }}</span>
                </div>
                <div v-if="getManageRouters(item.path) && getManageRouters(item.path).children" class="menu-level2-item-list">
                  <div v-for="(meta, metaIndex) in getManageRouters(item.path).children" v-show="!meta.hidden" :key="metaIndex" class="menu-level2-item-list-item">
                    <span @click="go(item, meta)">{{ meta.meta.title }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <img slot="reference" src="../../assets/images/menu.svg" alt="menu">
      </el-popover>
      <!-- 分割线 -->
      <el-divider v-if="showAllMenu" direction="vertical" />
      <!-- logo -->
      <img v-if="!loading" :src="configUrl || logoNull" class="cr-image" alt="">
    </div>
    <div class="cr-header-right">
      <!-- 一级菜单 -->
      <el-menu
        :default-active="activeMenu"
        class="cr-first-menu"
        mode="horizontal"
        background-color="var(--neutral-0)"
        text-color="var(--neutral-700)"
        active-text-color="var(--color-600)"
        @select="handleSelect">
        <component
          v-for="menu in config"
          :key="menu.key"
          :index="menu.key"
          :is="menu.subs && menu.subs.length && !menu.special ? 'el-submenu' : 'el-menu-item'"
          :popper-class="menu.subs && menu.subs.length && !menu.special ? 'cr-first-sub-menu' : ''"
        >
          <template slot="title">{{ menu.title }}</template>
          <template v-if="menu.subs && menu.subs.length && !menu.special">
            <el-menu-item v-for="sub in menu.subs" :key="sub.key" :index="sub.key" :disabled="prohibit">
              <img :src="iconImg(sub.icon)" alt="">
              <div class="menu-item-title-wrap">
                <span>{{ sub.title }}</span>
                <span />
              </div>
            </el-menu-item>
          </template>
        </component>
      </el-menu>
      <el-divider direction="vertical" />
      <slot v-if="show" name="page3D" />
      <!-- 培训入口 -->
      <slot name="training" />
      <!-- 靶场介绍 -->
      <div v-if="introduceData && introduceData.enable" class="home-icon" @click="goHomePage">
        <img src="../../assets/images/home.png" alt="" @click="goHomePage">
        靶场介绍
      </div>
      <slot name="user" />
    </div>
  </div>
</template>
<script>
import { queryBaseInfoConfig } from '../api/config'
import { queryFirstConfigByName } from '../../api/index'
import { mapGetters } from 'vuex'
export default {
  props: {
    // 展示位置：后台admin、前台web，方便做特殊处理
    viewLocation: {
      type: String,
      default: () => {
        return 'admin'
      }
    },
    showAllMenu: {
      type: Boolean,
      default: () => {
        return true
      }
    },
    activeMenu: {
      type: String,
      default: () => {
        return ''
      }
    },
    config: {
      type: Array,
      default: () => {
        return []
      }
    },
    webRouters: {
      type: Array,
      default: () => {
        return []
      }
    },
    notifyShow: {
      type: Boolean,
      default: () => {
        return false
      }
    }
  },
  data() {
    return {
      logoNull: require('./logo-null.png'),
      configUrl: '',
      introduceData: null,
      loading: true,
      show: window.ADMIN_CONFIG ? window.ADMIN_CONFIG.LARGE_SCREEN_SHOW : false,
      allMenuVisible: false
    }
  },
  computed: {
    ...mapGetters(['manage', 'manageRouters', 'prohibit']),
    allMenuConfig() {
      const arr = []
      const obj = {
        key: 'other',
        path: null,
        title: null,
        sort: 2,
        subs: []
      }
      this.config.forEach(item => {
        if (!item.subs && !item.special) {
          obj.subs.push(item)
        } else {
          arr.push(item)
        }
      })
      arr.push(obj)
      // 找到"渗透测试系统"的菜单项并修改其sort值
      const penetrantMenu = arr.find(menu => menu.key === 'penetrant')
      if (penetrantMenu) penetrantMenu.sort = 3
      arr.sort((a, b) => a.sort - b.sort)
      return this.viewLocation === 'web' ? arr : this.config
    }
  },
  mounted() {
    this.getDetail()
    this.getIntroduceData()
  },
  methods: {
    // 获取靶场介绍配置数据
    getIntroduceData() {
      queryFirstConfigByName('introduce').then(res => {
        this.introduceData = res.data.value ? JSON.parse(res.data.value) : null
      }).catch(() => {})
    },
    getDetail() {
      queryBaseInfoConfig().then((res) => {
        this.configUrl = res.data.data[0] ? res.data.data[0].configUrl : ''
        this.loading = false
      })
    },
    getManageRouters(path) {
      const router = this.webRouters && this.webRouters.length ? this.webRouters : this.manageRouters
      return router.find(item => item.path === path) || null
    },
    iconImg(icon) {
      const themeKey = window.ADMIN_CONFIG ? (window.ADMIN_CONFIG.THEME || 'green') : (window.WEB_CONFIG.THEME || 'green')
      return require('../../assets/' + themeKey + '/' + icon + '.png')
    },
    handleSelect(key, keyPath) {
      if (this.prohibit) {
        return
      }
      this.$emit('changeMenu', key, keyPath)
    },
    go(subs, meta) {
      if (this.prohibit) {
        return
      }
      this.$emit('pageJump', subs, meta)
      this.allMenuVisible = false
    },
    goHomePage() {
      window.open(`/home`, '_blank')
    }
  }
}
</script>
<style lang="scss" scoped>
.cr-header {
  width: 100%;
  height: 60px;
  box-sizing: border-box;
  min-height: 60px;
  max-height: 60px;
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid #e6e6e6;
  background-color: #fff;
  .cr-header-left {
    display: flex;
    align-items: center;
    color: var(--neutral-0);
    font-size: 14px;
    font-weight: 900;
    /deep/ .el-popover__reference-wrapper {
      margin-left: 16px;
      width: 32px;
      height: 48px;
      display: flex;
      justify-content: center;
      align-items: center;
      img {
        width: 22px;
        cursor: pointer;
      }
    }
    /deep/ .el-divider {
      background-color: var(--neutral-300);
      height: 22px;
    }
    .cr-image {
      margin-left: 8px;
      margin-right: 8px;
      height: 42px;
    }
  }
  .cr-header-right {
    display: flex;
    align-items: center;
    .cr-first-menu {
      height: 59px;
      border: none;
      /deep/ .el-submenu .el-submenu__title, /deep/ .el-menu-item {
        height: 30px;
        line-height: 30px;
        margin: 15px 12px 0 12px;
        padding: 0;
        font-weight: 900;
        font-size: 15px;
        border: none !important;
        i {
          font-weight: bolder;
          color: var(--neutral-700);
        }
        &:hover{
          background-color: transparent !important;
          color: var(--color-600) !important;
        }
      }
    }
    .el-divider {
      height: 40%;
      width: 2px;
    }
    .home-icon {
      display: flex;
      align-items: center;
      margin-left: 10px;
      cursor: pointer;
      height: 30px;
      line-height: 30px;
      font-weight: 900;
      font-size: 15px;
      color: var(--neutral-700);
      background-color: var(--neutral-0);
      img {
        width: 20px;
        margin-top: -3px;
        margin-right: 3px;
      }
      svg {
        margin-top: -2px;
        margin-right: 3px;
      }
      &:hover {
        color: var(--color-600);
      }
    }
  }
}
</style>
<style lang="scss">
.sitemap-popper {
  left: 0 !important;
  top: 59px !important;
  margin: 0 !important;
  padding: 0 !important;
  color: rgba(0, 0, 0, 0.85);
  font-size: 14px;
  z-index: 1050;
  display: block;
  &.notify-show {
    top: 103px !important;
  }
  .sitemap {
    background: var(--neutral-0);
    box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.12);
    overflow: auto;
    height: auto;
    max-height: 750px;
    padding: 24px 0;
    .menu-level1-container {
      padding: 0 24px;
      .menu-level1-title {
        margin-bottom: 20px;
        font-size: 12px;
        color: var(--neutral-500);
        position: relative;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .menu-level1-title-line {
          flex-grow: 1;
          border-bottom: 1px solid var(--neutral-300);
        }
      }
      .menu-level1-content {
        // display: flex;
        // flex-flow: row wrap;
        column-count: 4;
        padding: 0 20px;
        .menu-level2-item {
          // display: block;
          // flex: 0 0 25%;
          padding-bottom: 35px;
          break-inside: avoid;
          .menu-level2-item-label {
            color: var(--neutral-800);
            font-weight: bold;
            .can-click {
              cursor: pointer;
              &:hover {
                text-decoration: underline;
              }
            }
          }
          .menu-level2-item-list-item {
            color: var(--neutral-600);
            margin-top: 8px;
            span {
              cursor: pointer;
            }
          }
          .menu-level2-item-list-item:hover {
            span {
              text-decoration: underline;
            }
          }
        }
      }
    }
  }
}
.cr-first-sub-menu.el-menu--horizontal {
  width: 800px;
  right: 0px !important;
  left: auto !important;
  top: 58px !important;
  box-shadow: none !important;
  .el-menu {
    padding: 0;
    max-height: 50%;
    margin-top: 0;
    box-shadow: 0px 10px 12px 0 rgba(0, 0, 0, .1);
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-evenly;
    padding: 20px 20px 30px 20px;
    &::before {
      display: none;
    }
    &::after {
      content: '';
      flex: 0 0 calc(50% - 60px);
      height: 0;
    }
    &:has(> :first-child:last-child)::after {
      display: none;
    }
    .el-menu-item {
      height: auto;
      line-height: 40px;
      padding: 12px 20px;
      margin: 12px;
      font-size: 16px;
      font-weight: 800;
      width: 300px;
      border-radius: 10px;
      display: flex;
      align-items: center;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);
      .menu-item-title-wrap {
        display: flex;
        flex-direction: column;
        >span:first-child {
          line-height: 28px;
        }
        >span:last-child {
          font-size: 13px;
          line-height: 22px;
          color: rgba(0, 0, 0, .4);
        }
      }
      &.is-active{
        color: var(--neutral-700) !important;
        background-color: var(--color-50) !important;
        font-weight: 600;
      }
      &:hover{
        background-color: var(--color-50) !important;
      }
      img {
        width: 40px;
        height: 40px;
        margin-right: 15px;
      }
    }
  }
}
</style>
