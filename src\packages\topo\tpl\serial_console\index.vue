<template>
  <div class="serial-console">
    <div ref="terminal" class="terminal" />
    <div class="status">Status: {{ status }}</div>
  </div>
</template>
<script>
import Terminal from './xterm'
export default {
  name: 'SerialConsole',
  props: {
    // 连接地址
    url: {
      type: String
    }
  },
  data() {
    return {
      status: '',
      term: null,
      socket: null,
      cols: 80,
      rows: 24
    }
  },
  mounted: function() {
    this.init()
  },
  beforeDestroy: function() {
    this.disconnect()
  },
  methods: {
    'getRow': function() {
      const rows = Math.round((window.innerHeight - 50) / 17)
      return rows
    },
    'onResize': function() {
      this.term.resize(80, this.getRow())
      this.term.fit()
    },
    'init': function() {
      window.onresize = (evt) => {
        this.onResize()
      }
      if (this.url) {
        this.connect(this.url)
      }
    },
    'str2ab': function(str) {
      var buf = new ArrayBuffer(str.length) // 2 bytes for each char
      var bufView = new Uint8Array(buf)
      for (var i = 0, strLen = str.length; i < strLen; i++) {
        bufView[i] = str.charCodeAt(i)
      }
      return buf
    },
    'connect': function(url) {
      const socket = new WebSocket(url, ['binary', 'base64'])
      this.socket = socket

      socket.onclose = (event) => {
        console.log('已关闭连接：', event)
        this.status = 'Closed'
      }

      socket.onerror = (event) => {
        console.log('连接错误：', event)
        this.status = 'Error'
      }

      socket.onopen = (event) => {
        console.log('已建立连接：', event)
        const term = new Terminal({
          rows: this.getRow()
        })
        this.term = term
        this.status = 'Open'

        term.open(this.$refs.terminal, true)
        term.fit()

        socket.send(this.str2ab(String.fromCharCode(13)))

        term.on('data', (data) => {
          socket.send(this.str2ab(data))
        })

        socket.onmessage = (e) => {
          if (e.data instanceof Blob) {
            var f = new FileReader()
            f.onload = function() {
              term.write(f.result)
            }
            f.readAsText(e.data)
          } else {
            term.write(e.data)
          }
        }
      }
    },
    'disconnect': function() {
      window.onresize = null
      if (this.term) {
        this.term.destroy()
        this.term = null
      }
      if (this.socket) {
        this.socket.close()
        this.socket = null
      }
    }
  }
}
</script>
<style lang="less">
  .serial-console {
    .status {
      font-size: 16px;
      margin-top: 5px;
      font-weight: 500;
    }
  }
</style>
