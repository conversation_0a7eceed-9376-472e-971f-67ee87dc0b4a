<template>
  <div class="history-record">
    <div class="history-title">
      <div class="left">
        <el-button v-if="showAddButton" type="text" icon="el-icon-plus" class="add-btn" @click="addRemark" >
          添加备注
        </el-button>
      </div>
      <div class="right">
        <div
          :class="{ active: sortDesc }"
          class="sort-btn"
          @click="toggleSort"
        >
          <span>按时间排序</span>
          <i :class="sortDesc ? 'el-icon-sort-up' : 'el-icon-sort-down'" />
        </div>
      </div>
    </div>
    <div class="history-content">
      <ul class="history-list">
        <li v-for="(item, index) in recordList" :key="index" class="history-item">
          <div class="item-header">
            <span class="item-index">{{ index + 1 }}. </span>
            <span class="item-time">{{ item.time }}, 由 </span>
            <span class="item-user">{{ item.user }} </span>
            <span class="item-action">{{ item.operationTypeName }}。</span>
            <el-button v-if="item.showDetail && item.operationTypeCode != 'CREATE'" type="text" style="height: 25px;" @click="toggleDetail(index)">
              <i
                :class="item.showDetail ? (item.detailVisible ? 'el-icon-minus' : 'el-icon-plus') : ''"
                class="detail-btn"
              />
            </el-button>
          </div>
          <!-- 只渲染 UPDATE 类型 -->
          <div v-if="item.operationTypeCode == 'UPDATE'">
            <div v-show="item.detailVisible">
              <div
                v-for="(detailItem, dIdx) in item.detail"
                :key="dIdx"
                class="detail-item"
              >
                <!-- 这里是children的UPDATE类型变更渲染 -->
                <div v-if="detailItem.rowNum" class="changes-info">
                  <div class="change-item">
                    <span>修改了 </span>
                    <span class="change-label">{{ detailItem.fieldLabel }}</span>
                    <span class="change-old">，区别为：</span>
                  </div>
                  <div v-if="detailItem.children && detailItem.children.length > 0" class="item-detail">
                    <div v-for="(child, childIdx) in sortedChildren(detailItem.children)" :key="childIdx">
                      <div v-if="child.operationTypeCode === 'DELETED'" style="text-decoration: line-through;">
                        {{ child.rowNum.toString().padStart(3, '0') + '- ' + child.oldValue }}
                      </div>
                      <div v-else-if="child.operationTypeCode === 'CREATE'">
                        {{ child.rowNum.toString().padStart(3, '0') + '+ ' + child.newValue }}
                      </div>
                      <!-- 其他类型可按需扩展 -->
                    </div>
                  </div>
                  <div v-else class="item-detail">
                    <div style="text-decoration: line-through;">
                      {{ detailItem.rowNum.toString().padStart(3, '0') + '- ' + detailItem.oldValue }}
                    </div>
                    <div>
                      {{ detailItem.rowNum.toString().padStart(3, '0') + '+ ' + detailItem.newValue }}
                    </div>
                  </div>
                </div>
                <div v-else class="changes-info">
                  <div class="change-item">
                    <span>修改了 </span>
                    <span class="change-label">{{ detailItem.fieldLabel }}</span>
                    <span class="change-old">，旧值为“ {{ detailItem.oldValue }} ”，</span>
                    <span class="change-new">新值为“ {{ detailItem.newValue }} ”。</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- 其他类型渲染 -->
          <div v-else>
            <div v-show="item.detailVisible || item.operationTypeCode == 'REMARK' || item.operationTypeCode == 'CREATE'" class="changes-info">
              <div v-if="item.changes && item.changes.length > 0 && item.operationTypeCode != 'REMARK' && item.operationTypeCode!='CREATE'">
                <div v-for="(change, cIdx) in item.changes" :key="cIdx" class="change-item">
                  <span>修改了 </span>
                  <span class="change-label">{{ change.fieldLabel }}</span>
                  <span class="change-old">，旧值为“ {{ change.oldValue }} ”，</span>
                  <span class="change-new">新值为“ {{ change.newValue }} ”。</span>
                </div>
              </div>
              <div
                v-if="Array.isArray(item.detail) && item.detail.length > 0 && (
                  item.operationTypeCode !== 'FIX' &&
                  item.operationTypeCode !== 'AUDIT' &&
                  item.operationTypeCode !== 'ACCEPT'&&
                  item.operationTypeCode !== 'ASSIGN'&&
                  item.operationTypeCode !== 'START'&&
                  item.operationTypeCode !== 'OVER' &&
                  item.operationTypeCode !== 'DELETED' &&
                  item.operationTypeCode !== 'CREATE'
                  || item.detail.some(d => d.remark)
                ) && !isAttachmentDelete(item)"
                class="item-detail"
              >
                <div
                  v-for="(detailItem, dIdx) in item.detail"
                  :key="dIdx"
                >
                  <!-- 合并后的备注、审核、修复、验收渲染和编辑按钮 -->
                  <div
                    v-if="['CREATE', 'REMARK', 'FIX', 'AUDIT', 'ACCEPT'].includes(item.operationTypeCode)"
                    style="position: relative;"
                  >
                    <div v-if="detailItem.remark" v-html="detailItem.remark || ''"/>
                    <el-button
                      v-if="showEditBtn(item, index) && canEdit"
                      type="text"
                      icon="el-icon-edit"
                      class="edit-btn"
                      @click="editRemark(item, index)"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
export default {
  name: 'Remark',
  components: {
  },
  props: {
    // 记录列表
    records: {
      type: Array,
      default: () => []
    },
    // 是否显示添加备注按钮
    showAddButton: {
      type: Boolean,
      default: false
    },
    // 是否可以编辑备注
    canEdit: {
      type: Boolean,
      default: false
    },
    // 关联ID,用于提交备注时关联到具体对象
    relatedId: {
      type: [String, Number],
      default: ''
    },
    // 模块名称,用于提交备注时标识模块
    moduleName: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      recordList: [],
      sortDesc: false
    }
  },
  computed: {
    ...mapGetters(['userInfo'])
  },
  watch: {
    records: {
      handler(val) {
        if (val && val.length) {
          this.recordList = val.map(item => ({
            ...item,
            detailVisible: false,
            showDetail: !!item.detail && item.operationTypeCode !== 'REMARK'
          }))
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    // 切换排序方式
    toggleSort() {
      this.sortDesc = !this.sortDesc
      this.$emit('sortOpera', {
        tbOrder: this.sortDesc ? 'desc' : ''
      })
    },
    // 切换详情显示状态
    toggleDetail(index) {
      this.$set(this.recordList[index], 'detailVisible', !this.recordList[index].detailVisible)
    },
    // 打开添加备注弹窗
    addRemark() {
      this.$emit('addRemark')
    },
    // 打开编辑备注弹窗
    editRemark(item, index) {
      this.$emit('editRemark', {
        index,
        content: item.detail,
        id: item.id
      })
    },
    firstRemarkIndex(types = ['REMARK', 'FIX', 'AUDIT', 'ACCEPT']) {
      for (let i = 0; i < this.recordList.length; i++) {
        if (types.includes(this.recordList[i].operationTypeCode)) {
          return i
        }
      }
      return -1
    },
    lastRemarkIndex(types = ['REMARK', 'FIX', 'AUDIT', 'ACCEPT']) {
      for (let i = this.recordList.length - 1; i >= 0; i--) {
        if (types.includes(this.recordList[i].operationTypeCode)) {
          return i
        }
      }
      return -1
    },
    showEditBtn(item, index) {
      const types = ['CREATE', 'REMARK', 'FIX', 'AUDIT', 'ACCEPT']
      // 1. 基础判断：是否是可编辑的操作类型，且包含备注内容
      const hasRemark = types.includes(item.operationTypeCode) && Array.isArray(item.detail) && item.detail.some(d => d.remark)
      if (!hasRemark) return false

      // 2. 核心条件合并：是否自己创建（用item.user对比当前用户） AND 是否处于可编辑的排序位置
      // 2.1 判断是否自己创建：用item.user与当前登录用户的username对比
      const isSelfCreated = item.user === this.userInfo.username
      // 2.2 判断是否处于可编辑的排序位置（原逻辑）
      const isEditablePosition = this.sortDesc
        ? index === 0 // 降序时，第一条可编辑
        : index === this.recordList.length - 1 // 升序时，最后一条可编辑

      // 3. 两个条件必须同时满足才返回true（显示编辑按钮）
      return isSelfCreated && isEditablePosition
    },
    isAttachmentDelete(item) {
      // 只处理删除类型
      if (item.operationTypeCode !== 'DELETED') return false
      // 检查 detail 里的 children 或 oldValue 是否为附件
      if (Array.isArray(item.detail)) {
        return item.detail.some(detailItem => {
          // 1. children 情况
          if (Array.isArray(detailItem.children)) {
            return detailItem.children.some(child =>
              (child.oldValue && typeof child.oldValue === 'string' && child.oldValue.includes('附件'))
            )
          }
          // 2. 直接 oldValue 情况
          return detailItem.fieldLabel === '附件名称' ||
            (detailItem.oldValue && typeof detailItem.oldValue === 'string' && detailItem.oldValue.includes('附件'))
        })
      }
      return false
    },
    sortedChildren(children) {
      if (!Array.isArray(children)) return []
      return [...children].sort((a, b) => {
        if (a.rowNum !== b.rowNum) {
          return a.rowNum - b.rowNum
        }
        // rowNum相同，DELETED在前
        if (a.operationTypeCode === b.operationTypeCode) return 0
        if (a.operationTypeCode === 'DELETED') return -1
        if (b.operationTypeCode === 'DELETED') return 1
        return 0
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.history-record {
  padding: 0;

  .history-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0;
    font-weight: bold;

    .left {
      min-width: 80px;
    }
    .right {
      flex: 1;
      display: flex;
      justify-content: flex-end;
    }

    .add-btn {
      padding: 0;
      color: var(--color-600);
    }

    .sort-btn {
      color: #606266;
      font-size: 14px;
      cursor: pointer;

      &.active {
        color: var(--color-600);
      }

      i {
        margin-left: 5px;
      }
    }
  }

  .history-content {
    padding: 0;

    .history-list {
      list-style: none;
      padding: 0;
      margin: 0;

      .history-item {
        padding: 2px 0;
        line-height: 1.5;

        .item-header {
          display: flex;
          align-items: baseline;
          flex-wrap: wrap;

          .item-index, .item-time, .item-action {
            margin-right: 5px;
          }

          .item-user {
            margin-right: 5px;
            font-weight: 700;
          }

          .detail-btn {
            border: 1px solid #CCC;
          }
        }

        .changes-info {
          padding: 2px 0 2px 20px;
          color: #666;

          .change-item {
            padding: 2px 0;

            .change-label {
              color: #000;
              font-weight: 700;
            }
          }
        }

        .item-detail {
          padding: 10px 15px;
          background: #f5f7fa;
          border-radius: 4px;
          word-break: break-all;

          .detail-item {
            padding: 0 10px;
          }
        }
      }
    }
  }
}

.edit-btn {
  position: absolute;
  top: -7px;
  right: -12px;
  z-index: 999;
  font-size: 16px;
}

::v-deep .item-detail img {
  max-width: 100%;
  cursor: pointer;
}
</style>
