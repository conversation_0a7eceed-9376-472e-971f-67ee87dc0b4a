<template>
  <create-view :loading="loading" title="查看详情">
    <el-form slot="content" ref="form" :model="formData" label-position="left" label-width="150px">
      <el-card>
        <el-form-item label="考试名称:">
          <div>{{ formData.examName }}</div>
        </el-form-item>
        <el-form-item label="证书名称:">
          <div>{{ formData.credentialName }}</div>
        </el-form-item>
        <el-form-item label="证书等级:">
          <div>{{ level }}</div>
        </el-form-item>
        <el-form-item label="考试试卷:">
          <el-tag
            v-if="formData.selectedExamPaper"
            :disable-transitions="true"
          >
            {{ formData.selectedExamPaper }}
          </el-tag>
        </el-form-item>
        <el-form-item label="报名时间:">
          <div>{{ formData.startEndTime }}</div>
        </el-form-item>
        <el-form-item label="考试开始时间:">
          <div>{{ formData.date1 }}</div>
        </el-form-item>
        <el-form-item label="考试时长:" >
          <div>{{ formData.time }}</div>
        </el-form-item>
        <el-form-item label="考试须知:">
          <div class="notice" v-html="formData.notice"/>
        </el-form-item>
        <el-form-item label="备注:">
          <div>{{ formData.description }}</div>
        </el-form-item>
      </el-card>
    </el-form>
  </create-view>
</template>
<script>
import module from '../config.js'
import createView from '@/packages/create-view/index'
import { queryById } from '@/api/exam/index.js'

export default {
  components: {
    createView
  },
  mixins: [],
  data() {
    return {
      loading: false,
      formData: {
        examName: '',
        credentialName: '',
        level: '',
        selectedExamPaper: '',
        date1: '',
        time: '',
        startEndTime: '',
        notice: '',
        description: ''
      },
      examLevel: module.examLevel,
      level: ''
    }
  },
  created() {
    this.getEaxmById()
  },
  methods: {
    // 过滤html代码、空格、回车 空白字符
    filterHtml(str) {
      str = str.replace(/<("[^"]*"|'[^']*'|[^'">])*>/gi, '')
      str = str.replace(/[\r\n]/g, '')
      str = str.replace(/\s/g, '')
      str = str.replace(/&nbsp;/ig, '')
      return str
    },
    getEaxmById() {
      const id = Number(this.$route.query.examId)
      queryById({ id: id }).then((res) => {
        if (res.code === 0) {
          this.formData.examName = res.data.name
          this.formData.credentialName = res.data.certificateName
          this.formData.level = res.data.certificateLevel
          this.formData.selectedExamPaper = res.data.paperName
          this.formData.date1 = res.data.beginTime.split(' ')[0] + ' ' + res.data.beginTime.split(' ')[1]
          this.formData.time = res.data.distanceTime.split('天')[1].split('分')[0] + '分钟'
          this.formData.startEndTime = res.data.regBeginTime + ' 至 ' + res.data.regEndTime
          this.formData.notice = res.data.description
          this.formData.description = res.data.mark
          this.level = this.examLevel[this.formData.level - 1].label
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.notice {
  display: inline-block;
  width: 880px;
  overflow: auto;
}
</style>
