<template>
  <div class="buttons-wrap">
    <div :class="{ 'folded': fold }" class="category-fold-wrap" @click="handleFold">
      <div v-if="fold">展开<i class="el-icon-d-arrow-left rotate-90deg" /></div>
      <div v-else>折叠<i class="el-icon-d-arrow-left rotate90deg" /></div>
    </div>
    <el-button type="primary" icon="el-icon-plus" @click="$router.push({ name: 'questionBankCreate', params: { bankType: bankType }})">创建题目</el-button>
    <el-dropdown placement="bottom-start" trigger="click" @command="clickDrop">
      <el-button type="primary">
        操作<i class="el-icon-arrow-down el-icon--right"/>
      </el-button>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item v-if="bankType === 1" command="batchExport">批量导入</el-dropdown-item>
        <el-dropdown-item :disabled="singleDisabled" command="edit">编辑</el-dropdown-item>
        <el-dropdown-item :disabled="multipleDisabled" command="modalDelete">删除</el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
    <!-- 中部弹窗 start-->
    <el-dialog
      :title="titleMapping[modalName]"
      :visible.sync="modalShow"
      :width="modalWidth"
      @close="modalClose"
    >
      <transition name="el-fade-in-linear">
        <component
          :is="modalName"
          :name="modalName"
          :bank-type="bankType"
          :data="selectItem"
          :module-name="moduleName"
          @close="modalClose"
          @call="confirmCall"
        />
      </transition>
    </el-dialog>
    <!-- 中部弹窗 end-->
  </div>
</template>
<script>
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
import batchExport from './batch-export'
import modalDelete from './modal-delete'

export default {
  components: {
    batchExport,
    modalDelete
  },
  mixins: [mixinsActionMenu],
  props: {
    // 1-理论 2-靶机 3-仿真
    bankType: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      fold: false,
      // 弹窗title映射
      titleMapping: {
        batchExport: '批量导入',
        modalDelete: '删除'
      }
    }
  },
  methods: {
    handleFold() {
      this.fold = !this.fold
      this.$emit('call', 'fold', this.fold)
    },
    'clickDrop': function(name) {
      if (this.drawerAction && this.drawerAction.includes(name)) {
        this.drawerName = name
      } else {
        if (name === 'edit') {
          this.$router.push({ name: 'questionBankEdit', params: { id: this.selectItem[0].id, bankType: this.bankType }})
        } else {
          this.modalName = name
        }
      }
    },
    confirmCall: function(type, data) {
      if (type === 'close') {
        this.modalClose()
      } else if (type === 'refresh') {
        this.$emit('call', type)
      }
    }
  }
}
</script>
