export default {
  name: 'TaskReport',
  // 审批状态映射
  levelArr: [
    { label: '已驳回', value: 2, type: 'danger' },
    { label: '待审核', value: 0, type: 'warning' },
    { label: '已通过', value: 1, type: 'success' }
  ],
  get levelStrArr() {
    return this.levelArr.map(item => {
      return { label: item.label, value: String(item.value), type: item.type }
    })
  },
  // 将数组转换为对象形式，方便查找
  get levelObj() {
    return this.levelArr.reduce((acc, prev) => {
      acc[prev.value] = prev
      return acc
    }, {})
  }
}
