import request from '@/utils/request'
const headers = { 'Content-Type': 'application/json;charset=UTF-8' }

// 获取漏洞靶标列表
export function getTargetList(data) {
  return request({
    url: `accumulate/holeTarget/queryPage`,
    method: 'post',
    headers,
    data
  })
}

// 删除漏洞靶标
export function deleteTarget(data) {
  return request({
    url: `accumulate/holeTarget/delete`,
    method: 'post',
    headers,
    data
  })
}

// 禁用漏洞靶标
export function disableTarget(data) {
  return request({
    url: `accumulate/holeTarget/disable`,
    method: 'post',
    headers,
    data
  })
}

// 启用漏洞靶标
export function enableTarget(data) {
  return request({
    url: `accumulate/holeTarget/enable`,
    method: 'post',
    headers,
    data
  })
}

// 修改漏洞靶标信息
export function updateTarget(data) {
  return request({
    url: `accumulate/holeTarget/update`,
    method: 'post',
    headers,
    data
  })
}

// 创建漏洞靶标信息
export function createTarget(data) {
  return request({
    url: `accumulate/holeTarget/create`,
    method: 'post',
    headers,
    data
  })
}

// 获取漏洞靶标详情
export function getTarget(data) {
  return request({
    url: `/accumulate/holeTarget/get`,
    method: 'post',
    headers,
    data
  })
}

// 获取漏洞靶标实例列表
export function getHoleTargetInsList(data) {
  return request({
    url: `/accumulate/holeTargetIns/queryPage`,
    method: 'post',
    headers,
    data
  })
}

// 删除漏洞靶标实例
export function deleteHoleTargetIns(data) {
  return request({
    url: `/accumulate/holeTargetIns/delete`,
    method: 'post',
    headers,
    data
  })
}

// 后台漏洞靶标挑战记录
export function holeTargetUserRefPage(data) {
  return request({
    url: `/accumulate/holeTarget/holeTargetUserRefPage`,
    method: 'post',
    headers,
    data
  })
}

// 通关成绩导出
export function exportHoleTargetUserRefPage(data) {
  return request({
    url: '/accumulate/holeTarget/holeTargetUserRefPage/export',
    method: 'post',
    headers,
    data,
    responseType: 'blob'
  })
}
