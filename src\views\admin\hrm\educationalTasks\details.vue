<template>
  <div class="content-wrap-layout">
    <el-breadcrumb style="margin: 15px;" class="detail-breadcrumb" separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ name: 'teacherAffairs' }">{{ '教学事务' }}</el-breadcrumb-item>
      <el-breadcrumb-item :to="{ name: 'listGroups',query:$route.query }">{{ content }}</el-breadcrumb-item>
      <el-breadcrumb-item>{{ groupName }}</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="detail-content-wrap">
      <el-row :gutter="20">
        <el-col :span="6">
          <detail-card title="成绩信息">
            <el-form slot="content" label-position="left" label-width="135px">
              <el-form-item label="小组项目分数：" prop="taskGroupScore" class="has-form">
                <span slot="label">小组项目分数
                  <el-tooltip
                    :content="'允许输入0以上的正整数，最低0分，最高999999分'"
                    effect="dark"
                    placement="top">
                    <i class="wukong wukong-help_tips" />
                  </el-tooltip>：
                </span>
                <el-input-number
                  v-model="taskGroupScore"
                  :disabled="teachingTaskGroupUserVoList.length == 0"
                  :min="0"
                  :max="999999"
                  :step="1"
                  :precision="0"
                  size="small"
                  controls-position="right"
                  placeholder="请输入"
                  style="width: 100px;"
                />&nbsp;分
              </el-form-item>
              <div v-if="taskGroupScoreNum != 0" class="task-group-font">
                <span v-if="taskGroupScoreNum == 1">
                  请输入正整数
                </span>
                <span v-if="taskGroupScoreNum == 2">
                  项目分数最高不能超过999999分
                </span>
                <span v-if="taskGroupScoreNum == 3"> 请输入项目分数 </span>
              </div>
              <el-form-item label="小组排名：">
                <div>
                  <span style="color: var(--color-600);">{{ taskRanking.split("/")[0] }}</span>
                  / {{ taskRanking.split("/")[1] }}
                </div>
              </el-form-item>
              <el-form-item label="小组平均分：">{{ taskAverage }} 分</el-form-item>
            </el-form>
          </detail-card>
          <detail-card :title="groupName" class="mt-10">
            <el-form slot="content" label-position="left" label-width="110px">
              <template v-if="teachingTaskGroupUserVoList.length != 0">
                <div v-for="(item, index) in teachingTaskGroupUserVoList" :key="index">
                  <el-form ref="ruleForm" :model="item" :rules="rules" label-width="">
                    <el-form-item :label="item.realname + ':'" prop="groupUserScore" class="has-form">
                      <el-input-number
                        v-model="item.groupUserScore"
                        :min="0"
                        :step="1"
                        :precision="0"
                        size="small"
                        controls-position="right"
                        maxlength="6"
                        placeholder="0"
                        style="width: 100px;"
                      />&nbsp;分
                    </el-form-item>
                  </el-form>
                </div>
              </template>
              <el-empty v-else description="该小组没有成员"/>
              <div class="preservation">
                <el-button v-if="teachingTaskGroupUserVoList.length != 0" :disabled="teachingTaskGroupUserVoList.length == 0" type="primary" @click="submitForm('ruleForm')">保存</el-button>
                <div class="replacement">
                  <el-button type="primary" @click="lowerGroup(1)">上一组</el-button>
                  <el-button type="primary" @click="lowerGroup(2)">下一组</el-button>
                </div>
              </div>
            </el-form>
          </detail-card>
        </el-col>
        <el-col :span="18" class="detail-content-task">
          <detail-card title="任务">
            <div slot="content" class="body_subject">
              <el-tabs v-model="processCode" type="card" @tab-click="handleClick">
                <el-tab-pane
                  v-for="(item, index) in taskProcessVoList"
                  :key="index"
                  :label="item.processName"
                  :name="String(item.processCode)"
                />
                <el-tab-pane label="任务拓扑" name="1" />
              </el-tabs>
              <div v-if="taskProcessLinkVoList.length != 0 && processCode != 1" class="body">
                <div v-for="(item, index) in taskProcessLinkVoList" :key="index">
                  <div v-if="item.taskProcessLinkVoList.length != 0 && item.processAsk">
                    <div>
                      <div class="task-title">流程要求</div>
                      <div v-if="item.processAsk" class="task-content">{{ item.processAsk || '无' }}</div>
                    </div>
                    <div v-for="(dom, i) in item.taskProcessLinkVoList" :key="i" class="mb-15">
                      <div>任务{{ numberToChinese(i + 1) }}</div>
                      <div class="task-wrap">
                        <div v-if="dom.linkName" class="task-item">
                          <div class="title">任务名称：</div>
                          <div>{{ dom.linkName }}</div>
                        </div>
                        <div v-if="dom.linkAsk" class="task-item">
                          <div class="title">任务要求：</div>
                          <div>{{ dom.linkAsk }}</div>
                        </div>
                        <div v-if="dom.url" class="task-item">
                          <div class="title">任务附件：</div>
                          <div><el-link type="primary" @click="downloadReport(dom, 'task')"><i class="el-icon-download mr-5"/>{{ dom.urlName }}</el-link></div>
                        </div>
                        <div v-if="dom.linkGain == 2 || dom.linkGain == 3 && dom.studentLinkFlag" class="task-item task-flag" style="margin: 15px 0;">
                          <div class="title">提交答案：</div>
                          <div>
                            <el-input
                              v-model.trim="dom.studentLinkFlag"
                              disabled
                              placeholder="请输入答案，答案格式flag{123}"
                            />
                          </div>
                        </div>
                        <div v-if="dom.studentLinkFlagCreateBy" class="task-item">
                          <div class="title">答题学员：</div>
                          <div>{{ dom.studentLinkFlagCreateBy }}</div>
                        </div>
                        <div v-if="(dom.linkGain == 1 || dom.linkGain == 3) && dom.taskProcessLinkFileVoList.length > 0">
                          <div class="task-item">
                            <div class="title">上传附件：</div>
                            <div class="file-wrap">
                              <div v-for="(file, index) in dom.taskProcessLinkFileVoList" :key="index" style="height: 24px;">
                                <el-link type="primary" @click="downloadReport(file, 'group')"><i class="el-icon-download mr-5"/>{{ file.groupUrlName }}</el-link>
                              </div>
                            </div>
                          </div>
                          <div class="task-item">
                            <div class="title">上传学员：</div>
                            <div>{{ dom.taskProcessLinkFileVoList[0].urlCreateByName }}</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <el-empty
                    v-if="!item.processAsk"
                    :image="img"
                    :image-size="110"
                    style="margin-top: 23vh;"
                    description="暂无数据"
                  />
                </div>
              </div>
              <template>
                <Topo v-if="topologyId && processCode == 1" :topo-id="topologyId" topo-type="firingPermissions"/>
              </template>
            </div>
          </detail-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import {
  searchAffairTeachingTaskDetailAPI,
  searchTaskProcessLinkStudentAPI,
  updateTeachingTaskScoreAPI,
  topologyQueryGroup
} from '@/api/teacher/index.js'
import { numberToChinese } from '@/utils/index'
import Topo from '@/packages/topo/index'
import detailCard from '@/packages/detail-view/detail-card.vue'
export default {
  components: {
    Topo,
    detailCard
  },
  data() {
    var validateName = (rule, value, callback) => {
      console.log(value)
      if (Number(value) > Number(this.taskGroupScore)) {
        callback(new Error('学生分数不能大于项目分数'))
      } else if (!Number.isInteger(Number(value)) || Number(value) < 0) {
        callback(new Error('请输入正整数'))
      } else {
        callback()
      }
    }
    return {
      content: this.$route.query.content,
      taskCode: this.$route.query.taskCode,
      groupCode: this.$route.query.groupCode,
      upAndDown: this.$route.query.upAndDown,
      schedulingCode: this.$route.query.schedulingCode,
      processCode: '',
      taskProcessVoList: [],
      businessId: '',
      img: require('@/packages/table-view/nodata.png'),
      taskProcessLinkVoList: [],
      groupName: '',
      taskRanking: '',
      taskGroupScore: 0,
      taskGroupScoreNum: '',
      teachingTaskGroupUserVoList: [],
      taskAverage: '',
      groupUserScoreList: [],
      rules: {
        groupUserScore: [{ validator: validateName, trigger: 'blur' }]
      },
      topologyId: '',
      numberToChinese
    }
  },
  mounted() {
    this.searchAffairTeachingTaskDetail()
  },
  methods: {
    previewUrl: function(url, blank) {
      const resultUrl = this.viewFileUrl + encodeURIComponent(btoa(window.ADMIN_CONFIG.VIP_URL + url)) // kkfile 预览
      if (blank) {
        window.open(resultUrl, '_blank')
      } else {
        return resultUrl
      }
    },
    submitForm(formName) {
      if (this.teachingTaskGroupUserVoList.length == 0) {
        this.$message.error('该小组没有成员')
        return
      }
      let num = 0
      this.$refs[formName].forEach((item) => {
        item.validate((valid) => {
          if (!valid) {
            num = num + 1
          }
        })
      })
      if (num == 0) {
        this.updateTeachingTaskScore()
      }
    },
    taskGroupScoreFn() {
      if (!Number.isInteger(Number(this.taskGroupScore)) || Number(this.taskGroupScore) < 0) {
        this.taskGroupScoreNum = 1
      } else if (this.taskGroupScore > 999999) {
        this.taskGroupScoreNum = 2
      } else if (!this.taskGroupScore) {
        this.taskGroupScoreNum = 3
      } else {
        this.taskGroupScoreNum = 0
      }
    },
    upperGroup(num) {
      this.upAndDown = num
      this.searchAffairTeachingTaskDetail()
    },
    lowerGroup(num) {
      this.upAndDown = num
      this.searchAffairTeachingTaskDetail()
    },
    updateTeachingTaskScore() {
      this.taskGroupScoreFn(this.taskGroupScore)
      if (this.taskGroupScoreNum != 0) {
        return
      }
      const teachingTaskGroupUserBoList = []
      this.teachingTaskGroupUserVoList.forEach((item) => {
        teachingTaskGroupUserBoList.push({
          userId: item.userId,
          taskGroupScore: item.groupUserScore || 0
        })
      })
      const obj = {
        groupCode: this.groupCode,
        taskGroupScore: this.taskGroupScore,
        teachingTaskGroupUserBoList: teachingTaskGroupUserBoList
      }
      updateTeachingTaskScoreAPI(obj).then((res) => {
        this.$message.success('保存成功')
        this.upAndDown = 0
        this.searchAffairTeachingTaskDetail()
      })
    },
    searchAffairTeachingTaskDetail() {
      const obj = {
        taskCode: this.taskCode,
        groupCode: this.groupCode,
        schedulingCode: this.schedulingCode,
        upAndDown: this.upAndDown
      }
      searchAffairTeachingTaskDetailAPI(obj).then((res) => {
        this.taskProcessVoList =
          res.data.teachingTasksVo.taskProcessVoList
        this.groupCode = res.data.groupCode
        this.taskRanking = res.data.taskRanking
        this.groupName = res.data.groupName
        this.teachingTaskGroupUserVoList =
          res.data.teachingTaskGroupUserVoList
        this.teachingTaskGroupUserVoList.forEach((item) => {
          item.groupUserScoreNum = 0
        })
        this.taskGroupScore = res.data.taskGroupScore
        this.taskAverage = res.data.taskAverage
        this.searchTaskProcessLinkStudent(
          this.taskProcessVoList[0].processCode
        )
        this.getTopologyQueryTask()
      })
    },
    getTopologyQueryTask() {
      topologyQueryGroup({ teachingTaskId: this.taskCode, schedulingCode: this.schedulingCode, groupCode: this.groupCode }).then(res => {
        this.topologyId = res.data[0].topologyInsId || res.data[0].topologyId
      })
    },
    handleClick(tab, event) {
      this.searchTaskProcessLinkStudent(tab.name)
    },
    goAffairs() {
      this.$router.push({
        path: '/teacher/affairs'
      })
    },
    goPage() {
      this.$router.push({
        path: '/teacher'
      })
    },
    searchTaskProcessLinkStudent(processCode) {
      this.processCode = String(processCode)
      const obj = {
        processCode: processCode,
        groupCode: this.groupCode
      }
      searchTaskProcessLinkStudentAPI(obj).then((res) => {
        this.taskProcessLinkVoList = res.data
      })
    },
    // 下载附件
    downloadReport(item, type) {
      if (type === 'group') {
        item.url = item.groupUrl
        item.urlName = item.groupUrlName
      }
      if (item.url) {
        fetch(item.url, {
          method: 'get',
          responseType: 'blob'
        })
          .then((response) => response.blob())
          .then((blob) => {
            const a = document.createElement('a')
            const URL = window.URL || window.webkitURL
            const href = URL.createObjectURL(blob)
            a.href = href
            a.download = item.urlName
            document.body.appendChild(a)
            a.click()
            document.body.removeChild(a)
            URL.revokeObjectURL(href)
          })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.detail-content-wrap {
  flex: 1;
  min-height: 0;
  padding: 15px 0 15px 15px;
  .el-row {
    width: 100%;
    height: 100%;
  }
  .detail-content-task {
    height: 100%;
    flex: 1;
    ::v-deep .detail-card {
      height: 100%;
      display: flex;
      flex-direction: column;
      .detail-card-body {
        flex: 1;
        min-height: 0;
        padding: 8px 0 0 0 ;
      }
      .task-wrap {
        border: 1px #f2f3f5 solid;
        border-radius: 6px;
        padding: 15px;
        margin-top: 10px;
        .task-item {
          line-height: 20px;
          font-size: 14px;
          color: #4e5969;
          margin-bottom: 10px;
          display: flex;
          justify-content: flex-start;
          .title {
            min-width: 70px;
            margin-right: 10px;
          }
        }
      }
    }
  }
  .body_subject {
    height: 100%;
    .body {
      width: 100%;
      height: calc(100% - 34px);
      padding: 12px 20px;
      background: #ffffff;
      overflow: hidden;
      overflow-y: auto;
      .division {
        height: 1px;
        background: #f2f3f5;
        border-radius: 1px;
        margin-bottom: 20px;
      }
    }
    ::v-deep {
      .orchestration-create-warp {
        height: calc(100% - 34px);
      }
    }
  }
}
.preservation {
  margin-top: 20px;
  ::v-deep {
    .el-button {
      width: 100%;
      height: 32px;
      background: var(--color-600);
      border-radius: 4px;
    }
  }
  .replacement {
    margin-top: 20px;
    display: flex;
    justify-content: space-between;
    ::v-deep {
      .el-button {
        width: 121px;
        height: 32px;
        background: #f2f3f5;
        border-radius: 4px;
        border: none;
        font-size: 14px;
        font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
        font-weight: 400;
        color: #4e5969;
      }
      .el-button:hover {
        background: #c9cdd4;
      }
    }
  }
}
.task-title {
  font-size: 14px;
  font-family: Microsoft YaHei;
  color: #1d2129;
  margin-bottom: 10px;
}
.task-content {
  font-size: 14px;
  color: #4e5969;
  margin-bottom: 15px;
  border: 1px #f2f3f5 solid;
  border-radius: 3px;
  padding: 15px;
  min-height: 98px;
}
.task-flag_div {
  font-size: 16px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #333333;
  margin-bottom: 20px;
  .task-flag_title {
    line-height: 30px;
  }
  .upload-div {
    display: flex;
    align-items: center;
    .el-upload__tip {
      font-size: 12px;
      margin: 0 0 0 10px;
      font-family: SourceHanSansCN-Regular, SourceHanSansCN;
      font-weight: 400;
      color: #999999;
    }
  }
  ::v-deep {
    .el-upload-list__item-name {
      margin-right: 18px;
    }
  }
}
.task-flag {
  display: flex;
  font-size: 16px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #333333;
  margin-bottom: 20px;
  align-items: center;
  ::v-deep {
    .el-upload-list__item-name {
      margin-right: 18px;
    }
    .el-input {
      width: 451px;
      margin-right: 17px;
    }
    .el-input__inner {
      color: #333333 !important;
    }
    .el-button--primary {
      margin-left: 10px;
    }
  }
}
.task-flag_body {
  display: flex;
}
.task-group-font {
  color: #F56C6C;
  margin-left: 110px;
  margin-top: -10px;
}
</style>
