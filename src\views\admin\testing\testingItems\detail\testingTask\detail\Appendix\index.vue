<template>
  <!-- 上传附件 -->
  <div :class="{ 'height-100': height100 }" class="resource-table">
    <!-- 操作区 -->
    <div class="operation-wrap">
      <div class="operation-left">
        <slot name="action" />
        <el-button
          style="margin-right: 5px;"
          type="primary"
          icon="el-icon-refresh"
          @click="refresh"
        />
      </div>
      <div class="operation-right">
        <el-badge :value="searchBtnShowNum">
          <el-button icon="el-icon-search" @click="openSearch">搜索</el-button>
        </el-badge>
        <!-- 自定义表格列 -->
        <t-table-config
          v-if="!customColData.length"
          :data="columnsObj"
          :active-key-arr="columnsViewArr"
          @on-change-col="onChangeCol"
        />
      </div>
    </div>
    <!-- 搜索区 -->
    <t-search-box
      v-show="searchView"
      :search-key-list="searchKeyListView"
      default-placeholder="默认搜索文件名称"
      @search="searchMultiple"
    />
    <t-table-view
      ref="tableView"
      :height="height"
      :single="single"
      :data="tableData"
      :total="tableTotal"
      :page-size="pageSize"
      :current="pageCurrent"
      :select-item="selectItem"
      current-key="id"
      type="list"
      @on-select="onSelect"
      @on-current="onCurrent"
      @on-change="changePage"
      @on-sort-change="onSortChange"
      @on-page-size-change="onPageSizeChange"
    >
      <el-table-column
        v-for="item in columnsViewArr"
        :key="item"
        :min-width="colMinWidth"
        :width="columnsObj[item].colWidth"
        :label="columnsObj[item].title"
        :fixed="columnsObj[item].master ? 'left' : false"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span v-if="item == 'sort'">
            {{ scope.$index + 1 }}
          </span>
          <span v-else-if="item === 'fileSize'">
            {{ (scope.row.fileSize / 1024).toFixed(2) }} KB
          </span>
          <div v-else-if="item === 'operate'">
            <el-link :disabled="false" :underline="false" type="primary" @click.stop="handleFileView(scope.row)">查看</el-link>
            <el-link :disabled="false" :underline="false" type="primary" @click.stop="downloadFile(scope.row)">下载</el-link>
          </div>
          <span v-else>{{ scope.row[item] || "-" }}</span>
        </template>
      </el-table-column>
    </t-table-view>
  </div>
</template>
<script>
import tSearchBox from '@/packages/search-box/index.vue'
import tTableView from '@/packages/table-view/index'
import tTableConfig from '@/packages/table-config/table-col-config.vue'
import mixinsPageTable from '@/packages/mixins/page_table'
import { attachmentPageAPI, downloadAttachmentAPI } from '@/api/testing/index'
import filePreview from '@/components/testing/utils/filePreview'
export default {
  name: 'Appendix',
  components: {
    tTableView,
    tTableConfig,
    tSearchBox
  },
  mixins: [mixinsPageTable, filePreview],
  props: {
    // 在详情页单独展示的时候传 ture 高度会填充满
    height100: {
      type: Boolean,
      default: false
    },
    // 自定义列宽度
    customizeColumnsObj: {
      type: Object,
      default: () => {}
    },
    data: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      searchKeyList: [
        { key: 'fileName', label: '文件名称', master: true },
        { key: 'createBy', label: '提交人' },
        { key: 'createAt', label: '提交时间', type: 'time_range' },
        { key: 'updateBy', label: '最后更新人' },
        { key: 'updateAt', label: '更新时间', type: 'time_range' }
      ],
      columnsObj: {
        'sort': {
          title: '序号', master: true, colWidth: 50
        },
        fileName: {
          title: '文件名称',
          master: true
        },
        fileSize: {
          title: '文件大小'
        },
        createByName: {
          title: '提交人'
        },
        createAt: {
          title: '提交时间'
        },
        updateByName: {
          title: '最后更新人'
        },
        updateAt: {
          title: '最后更新时间'
        },
        operate: {
          title: '操作'
        }
      },
      columnsViewArr: [
        'sort',
        'fileName',
        'fileSize',
        'createByName',
        'createAt',
        'updateByName',
        'updateAt',
        'operate'
      ],
      tableData: [],
      tableTotal: 0,
      selectItem: [],
      previewUrl: '',
      loading: false,
      pageNum: 1,
      pageSize: 10
    }
  },
  computed: {},
  watch: {},
  mounted() {
    // 如果有自定义列，则使用自定义列
    if (
      Object.keys(this.customizeColumnsObj ? this.customizeColumnsObj : {})
        .length > 0
    ) {
      this.columnsObj = JSON.parse(JSON.stringify(this.customizeColumnsObj))
    }
  },
  methods: {
    getList(showLoading = true) {
      if (showLoading) {
        this.tableLoading = true
      }
      const params = this.getPostData('page', 'limit')
      params.projectId = this.$route.params.projectId
      params.taskId = this.$route.params.id
      if (params.createAt) {
        params.createAtStart = params.createAt.split(',')[0]
        params.createAtEnd = params.createAt.split(',')[1]
        delete params.createAt
      }
      if (params.updateAt) {
        params.updateAtStart = params.updateAt.split(',')[0]
        params.updateAtEnd = params.updateAt.split(',')[1]
        delete params.updateAt
      }
      attachmentPageAPI(params).then(res => {
        this.tableData = res.data.data.records || []
        this.tableTotal = Number(res.data.data.total) || 0
        this.tableLoading = false
      }).catch(() => {
        this.tableLoading = false
      })
    },
    // 下载附件
    downloadFile(item) {
      downloadAttachmentAPI({ id: item.id }).then((res) => {
        if (res.data.data) {
          fetch(res.data.data, {
            method: 'get',
            responseType: 'blob'
          })
            .then((response) => response.blob())
            .then((blob) => {
              const a = document.createElement('a')
              const URL = window.URL || window.webkitURL
              const href = URL.createObjectURL(blob)
              a.href = href
              a.download = item.fileName
              document.body.appendChild(a)
              a.click()
              document.body.removeChild(a)
              URL.revokeObjectURL(href)
            })
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.height-100 {
  .layout-table-wrap {
    height: 100%;
  }
}
</style>
