export default {
  methods: {
    'isPnf': function(data) {
      return data.resource_type === 'pnf'
    },
    'isVnf': function(data) {
      return data.resource_type === 'vnf'
    },
    'isInf': function(data) {
      return data.resource_type === 'inf'
    },
    'isBase': function(data) {
      return data.resource_type === 'base'
    },
    'isQemu': function(data) {
      return data.resource_type === 'vnf' && data.virtual_type === 'qemu'
    },
    'isDocker': function(data) {
      return data.resource_type === 'vnf' && data.virtual_type === 'docker'
    },
    'isRouter': function(data) {
      return data.resource_type === 'vnf' && data.virtual_type === 'cloud_router'
    },
    'isGlobalNetwork': function(data) {
      return data.resource_type === 'vnf' && data.virtual_type === 'global_network'
    },
    'isGlobalQemu': function(data) {
      return data.resource_type === 'vnf' && data.virtual_type === 'global_qemu'
    },
    'isSwitch': function(data) {
      return data.resource_type === 'base' && data.virtual_type === 'logic_switch'
    },
    'isExternal': function(data) {
      return data.resource_type === 'base' && data.virtual_type === 'external_switch'
    },
    'getType': function(data) {
      let type = ''
      if (this.isPnf(data) || this.isInf(data)) {
        type = data.resource_type
      } else if (this.isVnf(data) || this.isBase(data)) {
        type = data.virtual_type
      }
      return type
    }
  }
}
