<template>
  <div v-loading="loading" class="dialog-wrap">
    <el-form ref="form" :model="formData" :rules="rules" label-width="90px">
      <el-form-item label="名称" prop="questionDepotName">
        <el-input v-model.trim="formData.questionDepotName" placeholder=""/>
      </el-form-item>
      <el-form-item label="版本" prop="questionDepotVersion">
        <el-input v-model.trim="formData.questionDepotVersion" placeholder=""/>
      </el-form-item>
      <el-form-item label="描述" prop="questionDepotDescription">
        <el-input v-model.trim="formData.questionDepotDescription" placeholder="" type="textarea"/>
      </el-form-item>
    </el-form>
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>

<script>
import moduleConf from '../config.js'
import validate from '@/packages/validate'
import { createQuestionDepot, updateQuestionDepot } from '@/api/accumulate/questionLibrary.js'

export default {
  props: {
    name: String,
    data: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      moduleName: moduleConf.name,
      loading: false,
      validate: validate,
      formData: {
        questionDepotName: '',
        questionDepotVersion: '',
        questionDepotDescription: ''
      },
      rules: {
        questionDepotName: [validate.required(), validate.name_64_char],
        questionDepotVersion: [{ type: 'string', max: 30, message: '不可超过30个字符', trigger: 'blur' }],
        questionDepotDescription: [validate.description]
      }
    }
  },
  inject: ['questionLibraryAction'],
  created() {
    if (this.name === 'modalEdit') {
      // 回显表单
      for (const key in this.formData) {
        this.formData[key] = this.data[0] && this.data[0][key]
      }
    } else {
      this.formData['id'] = null
    }
  },
  methods: {
    close() {
      this.$emit('close')
    },
    confirm: function() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.loading = true
          const postData = JSON.parse(JSON.stringify(this.formData))
          postData['id'] = this.data[0] && this.data[0]['id']
          const apiName = postData['id'] ? updateQuestionDepot : createQuestionDepot
          apiName(postData).then(res => {
            if (res.code == 0) {
              this.$message.success(`${postData['id'] ? '编辑题库' : '创建题库'}成功`)
              this.$bus.$emit(this.moduleName + '_module', 'reload')
              this.close()
            }
          }).finally(() => {
            this.loading = false
          })
        }
      })
    }
  }
}
</script>
