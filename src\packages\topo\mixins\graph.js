import { Graph } from '@antv/x6'
import { Dnd } from '@antv/x6-plugin-dnd'
import { Keyboard } from '@antv/x6-plugin-keyboard'
import { Clipboard } from '@antv/x6-plugin-clipboard'
import { Selection } from '@antv/x6-plugin-selection'
import { Snapline } from '@antv/x6-plugin-snapline'
import { History } from '@antv/x6-plugin-history'
import { <PERSON>roll<PERSON> } from '@antv/x6-plugin-scroller'
import { Export } from '@antv/x6-plugin-export'
import { Transform } from '@antv/x6-plugin-transform'
import { register } from '@antv/x6-vue-shape'
import { DagreLayout, CircularLayout, GridLayout } from '@antv/layout'
import CustomNode from '../tpl/CustomNode.vue'
import _ from 'lodash'
import moduleConf from '../config'

register({ shape: 'custom-vue-node', width: 100, height: 100, component: CustomNode })

export default {
  data() {
    return {
      graph: null, // 画布数据对象
      dnd: null,
      selection: null, // 框选实例
      canRedo: false, // 能否重做
      canUndo: false, // 能否撤销
      viewComponent: ['panel', 'text'], // 只用于展示的组件（面板、文字）
      selectedCells: [] // 选中的元素
    }
  },
  methods: {
    // 获取字符串宽度
    'getTextWidth': function(text, fontSize, nodeData) {
      const _span = document.createElement('span')
      _span.innerText = text
      _span.style.fontSize = fontSize + 'px'
      _span.style.position = 'absolute'
      document.body.appendChild(_span)
      let width = _span.offsetWidth + 4
      document.body.removeChild(_span)
      if (nodeData.loading || (nodeData.resource_type === 'vnf' && nodeData.status)) {
        width += 10
      }
      width = width % 2 !== 0 ? (width + 1) : width
      console.log(width)
      return width
    },
    // 撤销
    'undo': function() {
      this.graph.undo()
    },
    // 重做
    'redo': function() {
      this.graph.redo()
    },
    // 缩放
    'setZoom': function(Number) {
      this.graph.zoom(Number)
    },
    // 居中
    'setZoomTo': function(Number) {
      this.graph.zoomTo(Number)
      if (Number === 1) this.graph.centerContent()
    },
    // 控制连接桩显示/隐藏
    'showPorts': function(ports, show) {
      for (let i = 0, len = ports.length; i < len; i = i + 1) {
        ports[i].style.visibility = show ? 'visible' : 'hidden'
        ports[i].style.transition = show ? '.2s' : 'none'
        ports[i].style.transform = show ? 'scale(1.2)' : 'none'
      }
    },
    // 设置连线样式（选中和非选中）
    'setEdgeAttr': function(cells, active) {
      cells.forEach(cell => {
        if (cell.shape === 'edge') {
          const error = cell.data && cell.data.status === 'error'
          const labels = []
          cell.labels.forEach(label => {
            labels.push({
              attrs: {
                text: {
                  text: label.attrs.text.text,
                  fill: error ? '#ed4014' : (active ? '#337cdd' : '#37668c'),
                  fontWeight: active ? 'bold' : 'normal',
                  visibility: active ? 'available' : 'hidden'
                },
                bg: {
                  stroke: error ? '#ed4014' : (active ? '#337cdd' : '#37668c'),
                  strokeWidth: active ? 2 : 1,
                  refWidth: label.attrs.text.text === '' ? 0 : 4,
                  visibility: active ? 'available' : 'hidden'
                }
              },
              position: label.position
            })
          })
          cell.setLabels(labels)
          cell.attr('line/stroke', error ? '#ed4014' : (active ? '#337cdd' : '#37668c'))
          cell.attr('line/strokeWidth', active ? 2 : 1)
          cell.setZIndex(active ? 900 : 100)
        }
      })
    },
    // 重排连线：改变偏移量，避免连线重合
    'resetEdgePosition': function(edge, node) {
      if (edge) { // 增加或删除连线时
        const sourceNodeEdges = this.graph.getConnectedEdges(edge.source.cell) // 源节点的所有连线
        const targetNodeEdges = this.graph.getConnectedEdges(edge.target.cell) // 目标节点的所有连线
        const commonEdges = [] // 共同连线
        sourceNodeEdges.forEach(item => {
          if (targetNodeEdges.includes(item)) {
            commonEdges.push(item)
          }
        })
        const sourcePosition = this.graph.getCellById(edge.source.cell).position() // 源节点的坐标
        const targetPosition = this.graph.getCellById(edge.target.cell).position() // 目标节点的坐标
        const diffX = Math.abs(sourcePosition.x - targetPosition.x)
        const diffY = Math.abs(sourcePosition.y - targetPosition.y)
        const offset = parseInt(50 / commonEdges.length)
        if (diffX > diffY) {
          let sourceDy = commonEdges.length > 1 ? -20 : -10
          let targetDy = commonEdges.length > 1 ? -20 : -10
          commonEdges.forEach(item => {
            item.setSource({
              cell: item.source.cell,
              anchor: {
                name: 'nodeCenter',
                args: { dy: sourceDy }
              }
            })
            item.setTarget({
              cell: item.target.cell,
              anchor: {
                name: 'nodeCenter',
                args: { dy: targetDy }
              }
            })
            targetDy += offset
            sourceDy += offset
          })
        } else {
          let sourceDx = commonEdges.length > 1 ? -13 : 0
          let targetDx = commonEdges.length > 1 ? -13 : 0
          commonEdges.forEach(item => {
            item.setSource({
              cell: item.source.cell,
              anchor: {
                name: 'nodeCenter',
                args: { dy: -10, dx: sourceDx }
              }
            })
            item.setTarget({
              cell: item.target.cell,
              anchor: {
                name: 'nodeCenter',
                args: { dy: -10, dx: targetDx }
              }
            })
            targetDx += offset
            sourceDx += offset
          })
        }
      } else if (node) { // 拖动节点时
      }
    },
    // 传入右击的坐标，返回 Contextmenu 的left，top
    'handleContextmenuXY': function(x, y, name = 'menuBar') {
      const wrap = this.$refs['orchestration-create-warp'].getBoundingClientRect()
      let left, top
      const menuBar = this.$refs[name].$el
      if (x + menuBar.offsetWidth > wrap.width) {
        left = x - menuBar.offsetWidth
      } else {
        left = x
      }
      if (y + menuBar.offsetHeight > wrap.height) {
        top = y - menuBar.offsetHeight
      } else {
        top = y
      }
      if (wrap.width - x - menuBar.offsetWidth < 130) {
        this.$refs[name].consoleMenu = 'left'
      }
      return { left, top }
    },
    // 初始化画布
    'initGraph': function() {
      // 初始化画布
      this.graph = new Graph({
        container: document.getElementById(this.idPrefix + 'container'),
        height: 1000,
        width: 2000,
        autoResize: moduleConf.graphConfig.autoResize,
        mousewheel: moduleConf.graphConfig.mousewheel,
        background: moduleConf.graphConfig.background,
        grid: moduleConf.graphConfig.grid,
        highlighting: moduleConf.graphConfig.highlighting,
        connecting: Object.assign({}, moduleConf.graphConfig.connecting, {
          createEdge: function() {
            return this.createEdge(moduleConf.edgeConfig)
          },
          validateEdge: ({ edge }) => {
            const sourceData = this.graph.getCellById(edge.source.cell).data // 源节点的数据
            const targetData = this.graph.getCellById(edge.target.cell).data // 目标节点的数据
            if (sourceData.loading || targetData.loading || sourceData.status === 'error' || targetData.status === 'error') { // 处于loading、error状态的节点不可连线
              return false
            } else {
              // // 图形设备与其他设备的连线默认为虚线
              // if (sourceData.type === 'inf' || targetData.type === 'inf') {
              //   edge.attr('line/strokeDasharray', '5 5')
              // }
              this.open('configPort', edge)
              return true
            }
          }
        }),
        // 不是以模板类型进入进行 拖拽方法禁用
        interacting: () => {
          if (this.type !== 'templatePermissions' && this.type !== 'allPermissions') {
            return { nodeMovable: false }
          }
          return true
        }
      })
      this.loading = false
      this.initDnd()
      this.loadPlugIn()
      this.bindKey()
      this.bindEvent()
      this.echoGraph()
    },
    // 初始化拖拽插件
    'initDnd': function() {
      const dndContainer = document.getElementById('dnd')
      this.dnd = new Dnd({
        target: this.graph,
        dndContainer: dndContainer,
        validateNode: (node) => {
          if (node.data.type === 'base') {
            if (node.data.virtual_type === 'panel') {
              node.setZIndex(0)
              node.size(100, 100)
              node.label = ''
              this.createNode(node)
              return true
            } else if (node.data.virtual_type === 'text') {
              node.setZIndex(300)
              node.size(120, 50)
              node.label = '在此处输入文字'
              node.attrs.body.strokeWidth = 0
              this.createNode(node)
              return true
            } else if (node.data.virtual_type === 'logic_switch' || node.data.virtual_type === 'external_switch') {
              this.createNode(node)
              return true
            } else {
              return false
            }
          } else if (node.data.type === 'pnf') {
            if (node.data.disabled) {
              return false
            } else {
              this.createNode(node)
              this.setPDStatus(node.data.name, true)
              return true
            }
          } else {
            this.createNode(node)
            return true
          }
        }
      })
    },
    // 拖拽生成节点
    'startDrag': function(e, data) {
      let node = null
      if (data.type === 'base') {
        if (data.virtual_type === 'panel') {
          node = this.graph.createNode({
            shape: 'rect',
            label: '面板',
            width: 50,
            height: 50,
            data: data,
            attrs: {
              body: {
                fill: 'rgba(255, 255, 255, 1)',
                stroke: '#8f8f8f',
                strokeWidth: 1
              }
            }
          })
        } else if (data.virtual_type === 'text') {
          node = this.graph.createNode({
            shape: 'rect',
            width: 50,
            height: 50,
            label: '文字',
            data: data,
            attrs: {
              body: {
                'stroke-dasharray': '5',
                'fill-opacity': 0,
                fill: '#fff',
                stroke: '#8f8f8f',
                strokeWidth: 1
              },
              text: {
                fill: 'rgba(0, 0, 0, 1)'
              }
            },
            tools: ['node-editor']
          })
        } else if (data.virtual_type === 'logic_switch' || data.virtual_type === 'external_switch') {
          node = this.graph.createNode({
            shape: 'custom-vue-node',
            width: 50,
            height: 70,
            ports: moduleConf.portsConfig,
            data: data
          })
        }
      } else {
        node = this.graph.createNode({
          shape: 'custom-vue-node',
          width: 50,
          height: 70,
          ports: moduleConf.portsConfig,
          data: data
        })
      }
      this.dnd.start(node, e)
    },
    // 加载各种插件
    'loadPlugIn': function() {
      this.graph.use(new Snapline({ enabled: true, clean: false }))
      this.graph.use(new History({ enabled: true, ignoreChange: false, beforeAddCommand(event, args) {
        // hover连线样式改变不记录history
        if (event === 'cell:change:*' && (args.key === 'attrs' || args.key === 'labels')) {
          return false
        }
        // if ((event === 'cell:added' || event === 'cell:removed') && args.cell.shape === 'custom-vue-node') {
        //   return true
        // }
        // if (event === 'cell:change:*' && args.key === 'position') {
        //   return true
        // }
        // return false
        return true
      } }))
      this.graph.use(new Clipboard({ enabled: true }))
      this.graph.use(new Keyboard({ enabled: true, global: true }))
      this.graph.use(new Scroller({ enabled: true, pannable: true, modifiers: ['ctrl', 'meta'] }))
      this.graph.use(new Export())
      this.selection = new Selection({
        enabled: true,
        rubberband: this.type === 'templatePermissions' || this.type === 'allPermissions',
        rubberNode: true,
        rubberEdge: true,
        className: 'selection-node',
        pointerEvents: 'none',
        showNodeSelectionBox: true,
        filter: (cell) => {
          return cell.data && cell.visible
        }
      })
      this.graph.use(this.selection)
      this.graph.use(new Transform({ resizing: {
        orthogonal: false,
        enabled: (node) => {
          return this.viewComponent.includes(node.data.virtual_type)
        }
      }}))
    },
    // 绑定快捷键
    'bindKey': function() {
      // this.graph.bindKey(['meta+z', 'ctrl+z'], () => {
      //   if (this.graph.canUndo()) {
      //     this.graph.undo()
      //   }
      //   return false
      // })
      this.graph.bindKey(['meta+a', 'ctrl+a'], () => {
        const nodes = this.graph.getNodes()
        if (nodes) {
          this.graph.select(nodes)
        }
      })
    },
    // 绑定节点和连线的事件
    'bindEvent': function() {
      // 节点移动时，计算重排连线位置
      this.graph.on('node:mousemove', ({ e, x, y, node, view }) => { //
        const sourceNodeEdges = this.graph.getConnectedEdges(node) // 源节点的所有连线
        const neighbors = this.graph.getNeighbors(node) // 源节点的所有连线节点
        const arr = {} // 组成（节点：连线）的json
        neighbors.forEach(nodeItem => {
          const edges = []
          sourceNodeEdges.forEach(edgeItem => {
            if ((edgeItem.source.cell === node.id && edgeItem.target.cell === nodeItem.id) || (edgeItem.source.cell === nodeItem.id && edgeItem.target.cell === node.id)) {
              edges.push(edgeItem)
            }
          })
          arr[nodeItem.id] = edges
        })
        for (const nodeId in arr) {
          if (arr[nodeId].length > 1) {
            const sourcePosition = node.position() // 源节点的坐标
            const targetPosition = this.graph.getCellById(nodeId).position() // 目标节点的坐标
            const diffX = Math.abs(sourcePosition.x - targetPosition.x)
            const diffY = Math.abs(sourcePosition.y - targetPosition.y)
            const offset = parseInt(50 / arr[nodeId].length) // 间隔
            if (diffX > diffY) {
              let sourceDy = -20
              let targetDy = -20
              arr[nodeId].forEach(item => {
                item.setSource({
                  cell: item.source.cell,
                  anchor: {
                    name: 'nodeCenter',
                    args: { dy: sourceDy }
                  }
                })
                item.setTarget({
                  cell: item.target.cell,
                  anchor: {
                    name: 'nodeCenter',
                    args: { dy: targetDy }
                  }
                })
                targetDy += offset
                sourceDy += offset
              })
            } else {
              let sourceDx = -13
              let targetDx = -13
              arr[nodeId].forEach(item => {
                item.setSource({
                  cell: item.source.cell,
                  anchor: {
                    name: 'nodeCenter',
                    args: { dy: -10, dx: sourceDx }
                  }
                })
                item.setTarget({
                  cell: item.target.cell,
                  anchor: {
                    name: 'nodeCenter',
                    args: { dy: -10, dx: targetDx }
                  }
                })
                targetDx += offset
                sourceDx += offset
              })
            }
          }
        }
      })
      // 鼠标移入节点，显示连接桩
      if (this.type === 'templatePermissions' || this.type === 'allPermissions' || (this.type === 'teamPermissions' && this.allowLinks === 1)) {
        this.graph.on('node:mouseenter', ({ node }) => {
          if (node.shape === 'custom-vue-node') {
            const container = document.getElementById(this.idPrefix + 'container')
            const ports = container.querySelectorAll('.x6-port-body, .custom-port-icon')
            this.showPorts(ports, true)
          }
        })
      }
      // 移入鼠标节点，改变节点zIndex
      this.graph.on('node:mouseenter', ({ node }) => {
        if (node.shape === 'custom-vue-node') {
          node.setZIndex(900)
        }
      })
      // 移出鼠标节点，隐藏链接桩
      this.graph.on('node:mouseleave', ({ node }) => {
        if (node.shape === 'custom-vue-node') {
          node.setZIndex(200)
          const container = document.getElementById(this.idPrefix + 'container')
          const ports = container.querySelectorAll('.x6-port-body, .custom-port-icon')
          this.showPorts(ports, false)
        }
      })
      // 连线右键菜单
      this.graph.on('edge:contextmenu', ({ e, x, y, edge, view }) => {
        this.showContextMenu = true
        this.$nextTick(() => {
          const p = this.graph.localToPage(x, y)
          const wrap = this.$refs['orchestration-create-warp'].getBoundingClientRect()
          const X = p.x - wrap.left
          const Y = p.y - wrap.top
          this.$refs.menuBar.init(X, Y, 'edge', edge)
          this.$nextTick(() => {
            const { left, top } = this.handleContextmenuXY(X, Y)
            this.$refs.menuBar.init(left, top < 0 ? 0 : top, 'edge', edge)
          })
        })
      })
      // 节点右键菜单
      this.graph.on('node:contextmenu', ({ e, x, y, node, view }) => {
        // 右的节点是否在框选的范围内，如果在则显示框选的右键菜单、否则显示当前的右键菜单
        const isInSelected = this.selectedCells.some(item => item.id === node.id)
        // 框选的节点
        const selectedNode = this.selectedCells.filter(item => item.shape !== 'edge')
        if (isInSelected && selectedNode.length > 1) {
          this.showBatchContextmenu(x, y)
        } else {
          if (!node.data.loading) {
            this.showContextMenu = true
            this.$nextTick(() => {
              const p = this.graph.localToPage(x, y)
              const wrap = this.$refs['orchestration-create-warp'].getBoundingClientRect()
              const X = p.x - wrap.left
              const Y = p.y - wrap.top
              this.$refs.menuBar.init(X, Y, 'node', node)
              this.$nextTick(() => {
                const { left, top } = this.handleContextmenuXY(X, Y)
                this.$refs.menuBar.init(left, top < 0 ? 0 : top, 'node', node)
              })
            })
          }
        }
      })
      // 批量选中元素时（框选）的右键菜单
      this.graph.on('blank:contextmenu', ({ e, x, y, view, cell }) => {
        this.showBatchContextmenu(x, y)
      })
      // history改变时
      this.graph.on('history:change', () => {
        this.canRedo = this.graph.canRedo()
        this.canUndo = this.graph.canUndo()
      })
      // 框选改变
      this.graph.on('selection:changed', ({ selected }) => {
        this.selectedCells = selected
        // 当选中的元素改变时：先清空所有连线的样式，再设置选中连线的样式
        const cells = this.graph.getEdges()
        this.graph.setRubberbandModifiers(selected.modifiers)
        this.setEdgeAttr(cells, false)
        if (this.selectedCells.length) {
          this.setEdgeAttr(this.selectedCells, true)
        }
        // 当选中的元素改变时：隐藏连接桩
        const container = document.getElementById(this.idPrefix + 'container')
        const ports = container.querySelectorAll('.x6-port-body, .custom-port-icon')
        this.showPorts(ports, false)
      })
      // 鼠标移入连线：改变连线样式（加粗）
      this.graph.on('edge:mouseenter', ({ edge }) => {
        const flag = this.selectedCells.some(item => item.id === edge.id)
        if (!flag) this.setEdgeAttr([edge], true)
        //  有权限的显示线工具
        if (this.type === 'templatePermissions' || this.type === 'allPermissions' || (this.type === 'teamPermissions' && this.allowLinks === 1)) {
          edge.addTools({
            name: 'vertices',
            args: {
              attrs: { fill: '#37668c', r: 4 }
            }
          })
        }
      })
      // 鼠标移出连线：改变连线样式（不加粗）
      this.graph.on('edge:mouseleave', ({ edge }) => {
        const flag = this.selectedCells.some(item => item.id === edge.id)
        if (!flag) this.setEdgeAttr([edge], false)
        if (edge.hasTool('vertices')) {
          edge.removeTool('vertices')
        }
      })
      // 点击空白：隐藏右键菜单
      this.graph.on('blank:click', () => {
        this.showContextMenu = false
        this.showConsoleInfo = false
      })
      // 点击节点：选中节点
      this.graph.on('node:click', ({ e, x, y, node, view }) => {
        console.log(node.toJSON())
        if (this.viewComponent.includes(node.data.virtual_type)) {
          this.selectedCells = [node]
        }
        if (this.type !== 'templatePermissions' && this.type !== 'allPermissions') {
          this.graph.cleanSelection()
        }
        // if (node.toJSON().data.console_type) {
        //   if (!node.data.loading) {
        //     this.showConsoleInfo = true
        //     this.$nextTick(() => {
        //       const p = this.graph.localToPage(x, y)
        //       const wrap = this.$refs['orchestration-create-warp'].getBoundingClientRect()
        //       const X = p.x - wrap.left
        //       const Y = p.y - wrap.top
        //       this.$refs.consolInfo.init(X, Y, 'node', node)
        //       this.$nextTick(() => {
        //         const { left, top } = this.handleContextmenuXY(X, Y, 'consolInfo')
        //         this.$refs.consolInfo.init(left, top < 0 ? 0 : top, 'node', node)
        //       })
        //     })
        //   }
        // }
      })
      // 双击节点：打开节点配置
      this.graph.on('node:dblclick', ({ node }) => {
        if (!this.viewComponent.includes(node.data.virtual_type) && !node.data.loading && node.data.status !== 'error') {
          this.open('viewConfigNode', node)
        }
      })
      // 点击连线
      this.graph.on('edge:click', ({ edge }) => {
        console.log(edge.toJSON())
      })
      // 拖动节点：更新缓存
      this.graph.on('node:moved', ({ x, y, node }) => {
        // 节点移动时更新位置信息的缓存
        const obj = {
          id: node.toJSON().id,
          position: node.toJSON().position,
          size: node.toJSON().size,
          text: node.data.virtual_type === 'text' ? node.attrs.text.text : ''
        }
        if (this.config.positional) {
          if (this.config.positional.find(item => item.id === obj.id)) {
            this.config.positional.forEach(item => {
              if (item.id === obj.id) {
                item.position = obj.position
                item.size = obj.size
                item.text = obj.text
              }
            })
          } else {
            this.config.positional.push(obj)
          }
        } else this.config.positional = [obj]
        localStorage.setItem(this.topologyData.id + '_config', JSON.stringify(this.config))
      })
    },
    // 显示框选的右键菜单
    showBatchContextmenu(x, y) {
      const target = this.graph.localToGraph(x, y)
      // 框选的节点
      const selectedNode = this.selectedCells.filter(item => item.shape !== 'edge')
      if (selectedNode.length > 1) {
        const el = this.selection.selectionImpl.selectionContainer
        var elX1 = el.offsetLeft // 左上角x坐标
        var elY1 = el.offsetTop // 左上角y坐标
        var elX2 = el.offsetLeft + el.offsetWidth // 右下角x坐标
        var elY2 = el.offsetTop + el.offsetHeight // 右下角y坐标
        if (target.x > elX1 && target.x < elX2 && target.y > elY1 && target.y < elY2) {
          this.showContextMenu = true
          this.$nextTick(() => {
            const p = this.graph.localToPage(x, y)
            const wrap = this.$refs['orchestration-create-warp'].getBoundingClientRect()
            const X = p.x - wrap.left
            const Y = p.y - wrap.top
            this.$refs.menuBar.init(X, Y, 'batch', this.selectedCells)
            this.$nextTick(() => {
              const { left, top } = this.handleContextmenuXY(X, Y)
              this.$refs.menuBar.init(left, top < 0 ? 0 : top, 'batch', this.selectedCells)
            })
          })
        }
      }
    },
    // 回显画布
    'echoGraph': function() {
      if (this.graph) {
        const cells = []
        const positional = this.topologyData.positional ? JSON.parse(this.topologyData.positional) : []
        // 如果有位置信息的缓存，则将缓存的位置信息放入positional（暂时先去掉缓存）
        // if (this.config.positional.length) {
        //   this.canUndo = true
        //   this.config.positional.forEach(item => {
        //     positional.forEach(sub => {
        //       if (sub.id === item.id) {
        //         sub.position = item.position
        //         sub.size = item.size
        //         sub.text = item.text
        //       }
        //     })
        //   })
        // }
        this.topologyNodes.forEach(node => {
          const position = JSON.parse(node.position)
          const cell = {
            id: position.id,
            position: position.position,
            size: position.size
          }
          if (node.virtual_type === 'panel') {
            cell['shape'] = 'rect'
            cell['zIndex'] = 0
            cell['label'] = ''
            cell['attrs'] = { body: { fill: 'rgba(255, 255, 255, 1)', stroke: '#8f8f8f', strokeWidth: 1 }}
          } else if (node.virtual_type === 'text') {
            cell['shape'] = 'rect'
            cell['zIndex'] = 300
            cell['label'] = position.text
            cell['attrs'] = { body: { 'stroke-dasharray': '5', 'fill-opacity': 0, fill: '#fff', stroke: '#8f8f8f', strokeWidth: 0 }, text: { fill: 'rgba(0, 0, 0, 1)' }}
            cell['tools'] = ['node-editor']
          } else {
            cell['view'] = 'vue-shape-view'
            cell['shape'] = 'custom-vue-node'
            cell['zIndex'] = 200
            cell['ports'] = moduleConf.portsConfig
            const textWidth = this.getTextWidth(node.name, 13, node)
            cell['size'] = {
              width: textWidth < 50 ? 50 : textWidth,
              height: 70
            }
          }
          const topologyData = positional.find(val => val.id === cell.id)
          if (topologyData) {
            cell.position = topologyData.position // 按照拓扑中的信息更新节点的x，y
            if (node.virtual_type === 'panel' || node.virtual_type === 'text') {
              cell.size = topologyData.size // 按照拓扑中的信息更新节点的x，y
              if (node.virtual_type === 'text') {
                cell['label'] = topologyData.text
                cell['attrs']['text']['fill'] = topologyData.fill || 'rgba(0, 0, 0, 1)'
              } else if (node.virtual_type === 'panel') {
                cell['zIndex'] = topologyData.zIndex || 0
                cell['attrs']['body']['fill'] = topologyData.fill || 'rgba(255, 255, 255, 1)'
              }
            }
          }
          // 节点对应的设备数据
          const deviceData = this.device_list.find(val => val.id === node.network_element_id)
          // 重新组装节点的data
          const newData = _.cloneDeep(deviceData)
          newData['name'] = node['name']
          newData['deviceName'] = deviceData['name']
          newData['topology_id'] = node['topology_id']
          newData['node_id'] = node['id']
          newData['status'] = node['status']
          newData['is_workstation'] = node['is_workstation']
          newData['console_type'] = node['console_type']
          newData['cpu'] = node['cpu']
          newData['ram'] = node['ram']
          newData['sys_disk_size'] = node['sys_disk_size']
          newData['admin_user'] = node['admin_user']
          newData['admin_pass'] = node['admin_pass']
          newData['description'] = node['description']
          newData['cidr'] = node['cidr']
          newData['gateway'] = node['gateway']
          newData['resource_type'] = node['resource_type']
          newData['vnf_uuid'] = node['vnf_uuid']
          newData['devicePorts'] = _.cloneDeep(deviceData.ports)
          node['ports'].forEach(port => {
          // 如果port存在link_to,则找出对端的信息
            if (port.link_to) {
              this.topologyNodes.forEach(item => {
                if (item.name === port.link_to.node_name) {
                  item.ports.forEach(sub => {
                    if (sub.link_to) {
                      if (sub.name === port.link_to.port_name) {
                        port['topology_link_to'] = {
                          'node_name': item.name,
                          'port_name': sub.name
                        }
                        if (!port.flag && !sub.flag) {
                          const labelConfigCopy = JSON.parse(JSON.stringify(moduleConf.labelConfig))
                          labelConfigCopy[0]['attrs']['text']['text'] = port.name
                          labelConfigCopy[1]['attrs']['text']['text'] = sub.name
                          // （图形设备、云路由器、逻辑交换机都不展示端口）
                          if (this.isInf(node) || this.isSwitch(node) || this.isExternal(node) || this.isRouter(node)) {
                            labelConfigCopy[0]['attrs']['text']['text'] = ''
                            labelConfigCopy[0]['attrs']['bg']['refWidth'] = 0
                          }
                          if (this.isInf(item) || this.isSwitch(item) || this.isExternal(item) || this.isRouter(item)) {
                            labelConfigCopy[1]['attrs']['text']['text'] = ''
                            labelConfigCopy[1]['attrs']['bg']['refWidth'] = 0
                          }
                          const edgeConfig = JSON.parse(JSON.stringify(moduleConf.edgeConfig))
                          // 图形设备与其他设备的连线默认为虚线
                          // if (this.isInf(node) || this.isInf(item)) {
                          //   edgeConfig['attrs']['line']['strokeDasharray'] = '5 5'
                          // }
                          const edge = Object.assign({}, edgeConfig, {
                            labels: labelConfigCopy,
                            source: { cell: cell.id },
                            target: { cell: JSON.parse(item.position).id },
                            data: {
                              source: { 'node_name': node.name, 'port_name': port.name, ip: port.ip || '', 'device_port_type': port.device_port_type || '' },
                              dest: { 'node_name': item.name, 'port_name': sub.name, ip: sub.ip || '', 'device_port_type': sub.device_port_type || '' },
                              network_type: port.network_type
                            },
                            zIndex: 100
                          })
                          if (this.isSwitch(node)) {
                            edge.data.network_type = node.network_type
                          }
                          if (this.isSwitch(item)) {
                            edge.data.network_type = item.network_type
                          }
                          cells.push(edge)
                          sub.flag = true
                        }
                      }
                    }
                  })
                }
              })
              port.flag = true
            }
          })
          newData['ports'] = node['ports']
          if (node.resource_type === 'pnf') {
          // 置灰节点对应的物理设备
            this.setPDStatus(deviceData['name'], true)
          }
          cell.data = newData
          cells.push(cell)
        })
        // 渲染线的样式
        cells.forEach(item => {
          if (item.shape === 'edge') {
            const edgeStyle = positional.find(pos =>
              pos.edgeKey &&
              pos.edgeKey.includes(`${item.data.source.node_name}_${item.data.source.port_name}`) &&
              pos.edgeKey.includes(`${item.data.dest.node_name}_${item.data.dest.port_name}`)
            )
            if (edgeStyle) {
              item.connector = edgeStyle.connector
              item.vertices = edgeStyle.vertices
            }
          }
        })
        if (this.layout === 'dagre') {
          const dagreLayout = new DagreLayout({
            type: 'dagre',
            rankdir: 'TB',
            ranksep: 50,
            nodesep: 10
          })
          const model = dagreLayout.layout({
            nodes: cells.filter(item => item.shape !== 'edge'),
            edges: cells.filter(item => item.shape === 'edge')
          })
          this.graph.fromJSON(model)
        } else if (this.layout === 'circular') {
          const dagreLayout = new CircularLayout({
            type: 'circular'
          })
          const model = dagreLayout.layout({
            nodes: cells.filter(item => item.shape !== 'edge'),
            edges: cells.filter(item => item.shape === 'edge')
          })
          this.graph.fromJSON(model)
        } else if (this.layout === 'grid') {
          const dagreLayout = new GridLayout({
            type: 'grid'
          })
          const model = dagreLayout.layout({
            nodes: cells.filter(item => item.shape !== 'edge'),
            edges: cells.filter(item => item.shape === 'edge')
          })
          this.graph.fromJSON(model)
        } else {
          this.graph.fromJSON({ cells: cells })
        }
        // 渲染完中后重排连线的位置
        const edges = this.graph.getEdges()
        edges.forEach(item => {
          this.resetEdgePosition(item)
          // 重排连线后会触发history，所以没有位置信息缓存时需要清空history
          if (!this.config.positional.length) {
            this.graph.cleanHistory()
          }
        })
        setTimeout(() => {
          this.graph.centerContent()
          if (this.zoomToFit) {
            // 只缩小不放大
            this.graph.zoomToFit({ maxScale: 1 })
          }
        }, 200)
      }
    }
  }
}
