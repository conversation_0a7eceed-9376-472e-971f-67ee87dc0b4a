<template>
  <div class="content-wrap-layout">
    <div v-loading="loading" class="system-setting-wrap">
      <el-divider content-position="left">
        <span style="font-size: 16px;">授权到期提醒</span>
        <a href="javascript:;" @click="openEditModal('授权到期提醒', 'licenseNotice', licenseNoticeData)">
          <i class="el-icon-edit-outline" />
        </a>
      </el-divider>
      <license-notice :data="licenseNoticeData" />
      <el-divider content-position="left">
        <span style="font-size: 16px;">平台会话设置</span>
        <a href="javascript:;" @click="openEditModal('平台会话设置', 'multiSession', multiSessionData)">
          <i class="el-icon-edit-outline" />
        </a>
      </el-divider>
      <multi-session :data="multiSessionData" />
      <el-divider content-position="left">
        <span style="font-size: 16px;">靶场介绍设置</span>
        <a href="javascript:;" @click="openEditModal('靶场介绍设置', 'introduce', introduceData)">
          <i class="el-icon-edit-outline" />
        </a>
      </el-divider>
      <introduce :data="introduceData" />
    </div>
    <!-- 中部弹窗 start-->
    <el-dialog
      :title="modalTitle"
      :visible.sync="modalShow"
      :width="modalWidth"
      @close="modalClose"
    >
      <transition name="el-fade-in-linear">
        <component
          :is="modalName"
          :name="editName"
          :data="editData"
          @close="modalClose"
          @call="confirmCall"
        />
      </transition>
    </el-dialog>
    <!-- 中部弹窗 end-->
  </div>
</template>

<script>
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
import edit from './modal-edit.vue'
import licenseNotice from './form/license-notice'
import multiSession from './form/platform-session'
import introduce from './form/introduce'
import { queryFirstConfigByName } from '@/api/admin/systemSettings'
export default {
  components: {
    edit,
    licenseNotice,
    multiSession,
    introduce
  },
  mixins: [mixinsActionMenu],
  data() {
    return {
      loading: true,
      modalTitle: '',
      editName: '',
      editData: null,
      licenseNoticeData: null,
      multiSessionData: null,
      introduceData: null
    }
  },
  created() {
    this.loadBase()
  },
  methods: {
    'loadBase': function(showLoading = true) {
      if (showLoading) {
        this.loading = true
      }
      Promise.all([this.getLicenseNoticeData(), this.getMultiSessionData(), this.getIntroduceData()]).then(() => {
        this.loading = false
      })
    },
    // 获取license提醒配置数据
    'getLicenseNoticeData': function() {
      return new Promise((resolve, reject) => {
        queryFirstConfigByName('licenseAlert').then(res => {
          this.licenseNoticeData = JSON.parse(res.data.value)
          resolve()
        }).catch(() => {
          reject()
        })
      })
    },
    // 获取平台会话配置数据
    'getMultiSessionData': function() {
      return new Promise((resolve, reject) => {
        queryFirstConfigByName('multiSession').then(res => {
          this.multiSessionData = res.data
          resolve()
        }).catch(() => {
          reject()
        })
      })
    },
    // 获取靶场介绍配置数据
    'getIntroduceData': function() {
      return new Promise((resolve, reject) => {
        queryFirstConfigByName('introduce').then(res => {
          this.introduceData = JSON.parse(res.data.value)
          resolve()
        }).catch(() => {
          reject()
        })
      })
    },
    // 打开弹窗
    'openEditModal': function(title, name, data) {
      this.modalTitle = title
      this.modalName = 'edit'
      this.editName = name
      this.editData = data
    },
    'confirmCall': function(type, data) {
      if (type === 'close') {
        this.modalClose()
      } else if (type === 'refresh') {
        this.loadBase(false)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.system-setting-wrap {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
  /deep/ .el-form {
    margin-left: 32px;
    padding-bottom: 20px;
    .el-form-item {
      margin-bottom: 0;
      .el-form-item__label:after {
        display: none;
      }
    }
  }
  /deep/ .el-divider--horizontal {
    margin: 15px 0;
    .el-divider__text.is-left {
      left: 0;
      padding-left: 0px;
    }
  }
}
</style>
