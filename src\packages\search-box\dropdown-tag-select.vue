<template>
  <div class="dropdown-select tags-dropdown-select">
    <div class="dropdown-select-list">
      <el-input v-model.trim="filterVal" placeholder="搜索" class="tag-search-input" style="margin-bottom: 10px;" />
      <div v-if="!filterData.length" style="line-height: 24px; text-align: center;">暂无数据</div>
      <el-checkbox-group v-else v-model="selectedList">
        <ul>
          <li v-for="item in filterData" :key="item.id">
            <el-checkbox :label="item" class="search-box-tags-checkout">
              <span style="float: right; margin-left: 10px;">
                <template v-if="item.resource_count === 0">0/0</template>
                <template v-else>{{ item['resource'][tab + '_tag_count'] }}/{{ item.resource_count }}</template>
              </span>
              <span :style="{ backgroundColor: item.color, color: handleFilterColor(item.color)}" style="display: inline-block; padding: 1px 6px; border-radius: 3px; max-width: 80px; overflow: hidden; white-space: nowrap; text-overflow: ellipsis;" @mouseenter="event => handlemouseOver(event)" @mouseleave="handleMouseLeave">{{ item.name }}</span>
            </el-checkbox>
          </li>
          <el-tooltip ref="tooltip" :content="tooltipContent" placement="top" />
        </ul>
      </el-checkbox-group>
    </div>
    <div class="dropdown-select-bottom">
      <el-button type="primary" size="mini" @click="confirm">确定</el-button>
      <el-button size="mini" type="text" @click="cancel">取消</el-button>
    </div>
  </div>
</template>
<style lang="less">
.dropdown-select.tags-dropdown-select {
  width: 180px;
  .dropdown-select-input {
    padding-bottom: 10px;
  }
  .dropdown-select-list {
    padding-bottom: 10px;
    .tag-search-input {
      .el-input__inner{
        border-radius: 2px;
        border: 1px solid #c8cacd;
        box-sizing: border-box;
        color: #252525;
        display: inline-block;
        font-size: inherit;
        height: 32px;
        line-height: 32px;
        outline: 0;
        padding: 0 10px;
      }
    }
    ul {
      max-height: 200px;
      overflow: auto;
      overflow-x: hidden;
    }
    li {
      padding: 2px;
    }
    .search-box-tags-checkout {
      .el-checkbox__label {
        width: 135px;
        vertical-align: -5px;
      }
    }
  }
  .dropdown-select-bottom {
    margin: 0 -12px -12px -12px;
    border-top: solid 1px #ebeef5;
    padding: 8px 12px;
  }
}
</style>
<script>
export default {
  props: {
    value: {
      type: String,
      default: null
    },
    data: {
      type: Array,
      default: () => {
        return []
      }
    },
    transTitle: {
      type: String
    },
    tab: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      searchKey: null,
      selectedList: [],
      filterVal: '',
      filterData: [],
      backUpBaseData: []
    }
  },
  watch: {
    filterVal(val) {
      this.handleSetBaseData()
      const vals = []
      this.filterData.forEach(item => {
        if (item.name.toLowerCase().includes(val.toLowerCase())) {
          vals.push(item)
        }
      })
      this.filterData = vals.concat()
    }
  },
  mounted() {
    this.filterData = JSON.parse(JSON.stringify(this.data))
    this.handleData()
    this.$bus.$on('update-tags-value', (data) => {
      this.filterData = JSON.parse(JSON.stringify(data))
      this.handleData()
    })
  },
  beforeDestroy() {
    this.$bus.$off('update-tags-value')
  },
  methods: {
    handleSetBaseData() {
      if (!this.filterData.length) {
        this.filterData = this.backUpBaseData.concat()
      } else {
        this.backUpBaseData.forEach((item, index) => {
          this.filterData.forEach((val, idx, self) => {
            if (val.id !== item.id) {
              self.splice(index, 1, item)
            }
          })
        })
      }
    },
    handleData() {
      this.backUpBaseData = this.filterData.concat()
      if (this.value) {
        const result = this.value.split(',')
        this.filterData.forEach(item => {
          if (result.includes(item.id)) {
            this.selectedList.push(item)
          }
        })
      }
    },
    'confirm': function() {
      this.$emit('confirm', this.selectedList)
    },
    'cancel': function() {
      this.selectedList = []
      this.$emit('cancel')
    }
  }
}
</script>
