import request from '../../request'
const _thisApi = window.NFVO_CONFIG.nfvo_api

export function getCategory(data, header) {
  return request({
    url: _thisApi + '/networkelement/category',
    method: 'get',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data,
    header: header
  })
}

export function addCategory(data) {
  return request({
    url: _thisApi + '/networkelement/category',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

export function editCategory(id, data) {
  return request({
    url: _thisApi + '/networkelement/category/' + id,
    method: 'put',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}
export function deleteCategory(id) {
  return request({
    url: _thisApi + '/networkelement/category/' + id,
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
