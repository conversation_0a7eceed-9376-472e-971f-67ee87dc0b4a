<template>
  <!--资源趋势 成绩部分 折现图 -->
  <div class="plugin-view" style="padding-bottom: 0px;">
    <h3 class="plugin-title" style="margin-bottom: 20px;">{{ pluginTitle }}</h3>
    <div v-if="pluginTitle == '近一年内班级模拟练习成绩趋势'" class="plugin-quota-line">
      <div id="lineChart" class="line-chart" @mouseleave="handleMouseLeave('lineChart')"/>
    </div>
    <div v-else class="plugin-quota-line">
      <div id="lineChart2" class="line-chart" @mouseleave="handleMouseLeave('lineChart2')"/>
    </div>
  </div>
</template>
<style lang="scss">
.plugin-quota-line {
  width: 100%;
  .line-chart {
    width: 100%;
    height: 400px;
  }
}
</style>
<script>
import * as echarts from 'echarts'
import pluginMixin from './mixin_plugin.js'
import {
  getSearchTaskScore,
  getSearchTestScore
} from '@/api/teacher/index.js'
export default {
  mixins: [
    pluginMixin
  ],
  props: {
    data: {
      type: Object
    }
  },
  methods: {
    handleMouseLeave(str) {
      document.getElementById(str).children[1].style.display = 'none'
    },
    // 获取折线图数据
    getData() {
      const ress =
    {
      type: 'line',
      showSymbol: false, // 默认不显示，鼠标移入才显示
      symbol: 'circle',
      symbolSize: 12,
      smooth: true,
      yAxisIndex: 0,
      lineStyle: {
        width: 4,
        color: '#37E2E2'
      },
      areaStyle: {
        color: new echarts.graphic.LinearGradient(
          0,
          0.8,
          0,
          1,
          [
            {
              offset: 0,
              color: 'rgba(166, 231, 231,0.2)'
            },
            {
              offset: 1,
              color: 'rgba(166, 231, 231,0)'
            }
          ],
          false
        )
      },
      itemStyle: {
        color: '#37E2E2',
        borderColor: '#37E2E2'
      },
      emphasis: {
        itemStyle: {
          color: '#fff',
          borderWidth: 3,
          borderColor: '#37E2E2',
          shadowColor: 'rgba(55, 226, 226, 0.5)',
          shadowBlur: 5
        }
      }
    }
      if (this.pluginTitle == '近一年内班级模拟练习成绩趋势') {
        getSearchTestScore().then((res) => {
          const lineData = res.data
          lineData.map((item) => {
            item.name = item.majorName
            item.type = 'line'
            item.data = item.score
            item = { ...ress, ...item.name, ...item.data }
          })
          this.createLineEchart(lineData)
        })
      } else {
        getSearchTaskScore().then((res) => {
          const lineData1 = res.data.data
          lineData1.map((item) => {
            item.name = item.majorName
            item.type = 'line'
            item.data = item.score
            item = { ...ress, ...item.name, ...item.data }
          })
          this.createLineEchart(lineData1)
        })
      }
    },
    async createLineEchart(data) {
      await this.$nextTick()
      const chart = this.pluginTitle == '近一年内班级模拟练习成绩趋势' ? echarts.init(document.getElementById('lineChart')) : echarts.init(document.getElementById('lineChart2'))
      const xdata = [
        '1月',
        '2月',
        '3月',
        '4月',
        '5月',
        '6月',
        '7月',
        '8月',
        '9月',
        '10月',
        '11月',
        '12月'
      ]
      const option = {
        backgroundColor: '#fff',
        legend: {
          icon: 'circle',
          x: '87%',
          y: 'center',
          orient: 'vertical',
          type: 'scroll',
          top: 10,
          itemWidth: 6,
          itemGap: 18,
          textStyle: {
            color: '#4E5969',
            fontSize: 14,
            fontWeight: 400
          },
          formatter: function(name) {
            if (name.length > 5) {
              return name.substring(0, 5) + '...'
            }
            return name
          },
          tooltip: {
            show: true
          }
        },
        tooltip: {
          trigger: 'axis',
          show: true,
          confine: true,
          renderMode: 'html',
          triggerOn: 'mousemove',
          enterable: true, // 允许鼠标进入tooltip
          z: 999,
          position: function(point) {
            // 固定tooltip位置，防止鼠标移入时tooltip闪烁
            return [point[0], point[1] - 150]
          },
          axisPointer: {
            type: 'shadow',
            shadowStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [{
                  offset: 0, color: 'rgba(22, 93, 255, 0.02)' // 0% 处的颜色
                }, {
                  offset: 1, color: 'rgba(22, 93, 255, 0.08)' // 100% 处的颜色
                }],
                global: false // 缺省为 false
              }
            },
            lineStyle: {
              width: 2,
              type: 'dashed',
              color: '#4080FF'
            }
          },
          textStyle: {
            color: '#4E5969',
            backgroundColor: '#fff'
          },
          padding: [10, 10],
          formatter: (data) => {
            let dom = `<div style="max-height: 250px;overflow-y: auto;padding-right: 10px;"><p style="color:#1D2129;">${data[0].axisValue}</p>`
            for (let i = 0; i < data.length; i++) {
              dom += `<p style="padding: 6px 9px;background-color: #fff;border-radius: 4px;box-shadow: 6px 0px 20px 0px rgba(33,87,188,0.1);margin-top: 10px;">
                <p title="${data[i].seriesName}" style="max-width: 360px;overflow: hidden;text-overflow:ellipsis;white-space: nowrap; display: inline-block;">${data[i].seriesName}</p>
                <span style="color: #1D2129;font-weight:bold;display: inline-block;float: right; margin-left: 30px;">${data[i].value}</span></p>`
            }
            dom += '</div>'
            return dom
          },
          extraCssText: 'background: rgba(255,255,255,.6); backdrop-filter: blur(4px);'
        },
        grid: {
          top: '5%',
          left: '10%',
          right: '15%',
          bottom: '8%'
        },
        xAxis: [
          {
            type: 'category',
            data: xdata,
            axisLine: {
              lineStyle: {
                color: '#DCE2E8'
              }
            },
            axisTick: {
              show: false
            },
            axisLabel: {
              interval: 0,
              color: '#86909C',
              // 默认x轴字体大小
              fontSize: 12,
              // margin:文字到x轴的距离
              margin: 15
            },
            axisPointer: {
              label: {
                padding: [0, 0, 10, 0],
                // 这里的margin和axisLabel的margin要一致!
                margin: 15,
                // 移入时的字体大小
                fontSize: 12,
                backgroundColor: {
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: '#fff' // 0% 处的颜色
                    },
                    {
                      offset: 0.86, // 0.86 = （文字 + 文字距下边线的距离）/（文字 + 文字距下边线的距离 + 下边线的宽度）
                      color: '#fff' // 0% 处的颜色
                    },
                    {
                      offset: 0.86,
                      color: '#33c0cd' // 0% 处的颜色
                    },
                    {
                      offset: 1,
                      color: '#33c0cd' // 100% 处的颜色
                    }
                  ],
                  global: false // 缺省为 false
                }
              }
            },
            boundaryGap: false,
            splitLine: { // 坐标轴在grid区域中的分隔线（网格中的横线）
              show: true,
              lineStyle: {
                color: '#EEEEEE',
                width: 1,
                type: 'solid'
              }
            }
          }
        ],
        yAxis: [
          {
            type: 'value',
            nameTextStyle: {
              color: '#86909C',
              padding: [0, 18, 0, 0]
            },
            axisTick: {
              show: false
            },
            axisLine: {
              show: false,
              lineStyle: {
                color: '#DCE2E8'
              }
            },
            axisLabel: {
              formatter: '{value}',
              color: '#86909C'
            },
            splitLine: { // 坐标轴在grid区域中的分隔线（网格中的横线）
              show: true,
              lineStyle: {
                color: '#EEEEEE',
                width: 1,
                type: 'dashed'
              }
            }
          }
        ],
        series: data
      }
      chart.setOption(option)
      // 建议加上以下这一行代码，不加的效果图如下（当浏览器窗口缩小的时候）。超过了div的界限（红色边框）
      window.addEventListener('resize', function() { chart.resize() })
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep #lineChart div {
  pointer-events: auto !important;
}
</style>
