import request from '@/packages/request'
const _thisApi = window.NFVO_CONFIG.nfvo_api
const headers = { 'Content-Type': 'application/json', 'x-access-module': 'ADMIN' }

export function getListApi(params) {
  return request({
    url: _thisApi + '/networkelement/controllers',
    method: 'get',
    params,
    headers
  })
}

export function getItem(id) {
  return request({
    url: _thisApi + '/networkelement/controllers/' + id,
    method: 'get',
    headers
  })
}

export function addControllers(data) {
  return request({
    url: _thisApi + '/networkelement/controllers',
    method: 'post',
    data,
    headers
  })
}

export function editControllers(id, data) {
  return request({
    url: _thisApi + '/networkelement/controllers/' + id,
    method: 'put',
    data,
    headers
  })
}

export function deleteControllers(id) {
  return request({
    url: _thisApi + '/networkelement/controllers/' + id,
    method: 'DELETE',
    headers
  })
}

export function getPort(params) {
  return request({
    url: _thisApi + '/networkelement/ports',
    method: 'get',
    params,
    headers
  })
}

export function addPort(data) {
  return request({
    url: _thisApi + '/networkelement/ports',
    method: 'post',
    data,
    headers
  })
}

export function editPort(id, data) {
  return request({
    url: _thisApi + '/networkelement/ports/' + id,
    method: 'put',
    data,
    headers
  })
}

export function deletePort(id) {
  return request({
    url: _thisApi + '/networkelement/ports/' + id,
    method: 'DELETE',
    headers
  })
}

export function getConnections(data) {
  const id = data.controller_id
  const postData = JSON.parse(JSON.stringify(data))
  delete postData['controller_id']
  return request({
    url: _thisApi + '/networkelement/controllers/' + id + '/connections',
    method: 'get',
    headers,
    params: postData
  })
}
