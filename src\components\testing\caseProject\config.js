// 测试状态mapping
const testStatusArr = [
  { label: '待测试', value: 0, type: 'info' },
  { label: '测试中', value: 1, type: 'warning' },
  { label: '测试通过', value: 2, type: 'success' },
  { label: '测试不通过', value: 3, type: 'danger' },
  { label: '待送审资料', value: 4, type: 'info' },
  { label: '待审核资料', value: 5, type: 'info' },
  { label: '待部署环境', value: 6, type: 'info' },
  // 挂起状态 pendingStatus
  { label: '取消挂起', value: 8, type: 'info' },
  { label: '已挂起', value: 9, type: 'info' }
]
export default {
  name: 'caseProjectList',
  testStatusArr: testStatusArr,
  get testStatusStrArr() {
    return this.testStatusArr.map(item => {
      return { label: item.label, value: Number(item.value), type: item.type }
    })
  },
  // 将状态map数组转换为对象
  testStatusObj: testStatusArr.reduce((acc, prev) => {
    acc[prev.value] = prev
    return acc
  }, {})
}
