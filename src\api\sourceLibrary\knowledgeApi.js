import request from '@/utils/request'

// 查询知识库数据列表
export function listKbs(data) {
  return request({
    url: '/accumulate/knowledge/list',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data
  })
}

// 查询知识库数据详细
export function getKbs(kbsId) {
  return request({
    url: '/accumulate/knowledge/query/' + kbsId,
    method: 'get'
  })
}

// 新增知识库数据
export function addKbs(data) {
  return request({
    url: '/accumulate/knowledge/add',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

// 修改知识库数据
export function updateKbs(data) {
  return request({
    url: '/accumulate/knowledge/update',
    method: 'put',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

// 删除知识库数据
export function delKbs(kbsId) {
  return request({
    url: '/accumulate/knowledge/delete',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: kbsId
  })
}


// 上传文件
export function uploadKbs() {
  return '/accumulate/knowledge/accFile'
}
// 下载文件
export function downloadFiles() {
  return '/file/downloadFiles'
}
