<template>
  <el-row :gutter="20">
    <el-col :span="24">
      <detail-card title="项目测试报告">
        <project slot="content" :data="data"/>
      </detail-card>
    </el-col>
    <el-col :span="24" style="margin-top: 15px;">
      <detail-card title="任务测试报告">
        <task slot="content" :data="data"/>
      </detail-card>
    </el-col>
  </el-row>
</template>

<script>
import detailCard from '@/packages/detail-view/detail-card.vue'
import project from './project/index.vue'
import task from './task/index.vue'

export default {
  components: {
    detailCard,
    project,
    task
  },
  props: {
    data: {
      type: Object,
      default: () => ({})
    },
    id: {
      type: Number,
      default: null
    }
  },
  data() {
    return {
    }
  },
  methods: {

  }
}
</script>

<style scoped lang="scss">

</style>
