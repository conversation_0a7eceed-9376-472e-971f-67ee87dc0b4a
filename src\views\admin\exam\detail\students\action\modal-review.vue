<template>
  <div v-loading="loading" class="wrap-layout">
    <detail-view
      :data="data"
      :id="id"
      :view-item="viewItem"
      :params="{
        'id': $route.query.examinationId,
        'view': 'students',
        'name': $route.query.name
      }"
      :show-header="false"
      style="height: auto;"
      title-key="realname"
    >
      <div slot="action" class="stuMsg">
        <h3>手工阅卷分数: {{ allScore || '-' }} 分</h3>
      </div>
    </detail-view>
    <div class="content">
      <div v-for="(value, type, index) in questionObj" :key="index + ''">
        <!-- 简答题和其他题型 -->
        <div v-if="['1', '2', '3', '4', '5', '6', '7', '8', '9'].includes(type) && shortAnswerArr.length > 0" class="mb-15">
          <div class="question_types">{{ arabicToChinese(index + 1) }}、{{ questionObj[type].questionTitle }}</div>
          <div v-for="(item, index) in questionObj[type].list" :key="`synthesis${index}`" :id="`qt_synthesis${index}`" class="question_info">
            <div class="flex-space-between ai-start mb-5">
              <div class="comp-question">
                <div>{{ index + 1 }}、&nbsp;</div><div style="max-width: 90%;"><span v-html="item.content"/></div><span>{{ `(${item.questionScore}分)` }}</span>
              </div>
              <div style="display: flex; margin-left: 10px;">
                <div class="name_question" style="margin-right: 5px;">
                  <div v-if="item.bankType == 2">
                    <el-dropdown v-if="item.console_type" trigger="click" placement="bottom-start" @command="clickDrop($event, item)">
                      <el-button type="primary">
                        控制台<i class="el-icon-arrow-down el-icon--right" />
                      </el-button>
                      <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item v-if="item.console_type && item.console_type.split(',').includes('vnc')" command="vnc">VNC</el-dropdown-item>
                        <el-dropdown-item v-if="item.console_type && item.console_type.split(',').includes('rdp')" command="rdp">远程桌面</el-dropdown-item>
                        <el-dropdown-item v-if="item.console_type && item.console_type.split(',').includes('ssh')" command="ssh">WebSSH</el-dropdown-item>
                        <el-dropdown-item v-if="item.console_type && item.console_type.split(',').includes('serial')" command="serial">串口控制台</el-dropdown-item>
                        <el-dropdown-item v-if="item.console_type && item.console_type.split(',').includes('webshell')" command="webshell">命令行</el-dropdown-item>
                      </el-dropdown-menu>
                    </el-dropdown>
                  </div>
                  <el-button v-if="item.bankType == 3" type="primary" @click="openEnv(item)">打开实验环境</el-button>
                </div>
                <el-button v-if="!item.Marking" class="env_disabled">未阅卷</el-button>
                <el-button v-else class="env_abled">已阅卷</el-button>
              </div>
            </div>
            <div class="flex-space-between ai-start">
              <div class="short_answer">
                <div class="stu_answer"><span>学员答案：</span><span> {{ item.questionUserAnswer }}</span></div>
                <div class="cor_answer"><span>正确答案：</span><span> {{ item.questionAnswer }}</span></div>
                <div v-if="item.solutionApproach == 1" class="mb-5">
                  解题方法：
                  <template v-if="item.fileUrl">
                    <el-link type="primary" @click="downloadReport(item)">
                      {{ item.fileName }}<i class="el-icon-view el-icon-download" style="margin-left: 5px;"/>
                    </el-link>
                  </template>
                  <template v-else>-</template>
                </div>
                <div class="cor_analysis"><span>题目解析：</span><div style="max-width: 90%; margin-left: 5px;" v-html="item.questionAnalysis"/></div>
              </div>
              <div class="flex-right">
                <div>得分:<el-input-number v-model="shortScore[item.questionCode]" :controls="false" :min="0" :max="item.questionScore" :precision="0" style="width: 80px;margin: 0 5px;" size="mini" label="描述文字" @blur="shortChange(item, shortScore[item.questionCode])"/>分</div>
              </div>
            </div>
          </div>
        </div>
        <!-- 组合题 -->
        <div v-if="type == 10 && synthesisArr.length > 0" class="mb-15">
          <div class="question_types">{{ arabicToChinese(index + 1) }}、{{ questionObj[type].questionTitle }}</div>
          <div v-for="(item, index) in questionObj[type].list" :key="`synthesis${index}`" :id="`qt_synthesis${index}`" class="question_info">
            <div class="name_question"><div style="margin-right: 20px;">{{ item.questionName }}</div><el-button type="primary" class="env_btn" @click="openEnv(item)">打开实验环境</el-button></div>
            <div v-for="(q, idx) in item.combinationQuestionBOS" :key="idx" class="border_comp">
              <div class="flex-space-between ai-start mb-5">
                <div class="comp-question">
                  <div>综合题{{ idx + 1 }}.&nbsp;</div><div style="max-width: 90%;"><span v-html="q.questionName"/></div><span>{{ `(${q.questionScore}分)` }}</span>
                </div>
                <div style="display: flex; margin-left: 10px;">
                  <el-button :disabled="!q.fileUrl" type="primary" class="env_btn" style="margin-right: 5px;" @click="downloadReport(q)">下载WriteUp</el-button>
                  <el-button v-if="!item.Marking" class="env_disabled">未阅卷</el-button>
                  <el-button v-else class="env_abled">已阅卷</el-button>
                </div>
              </div>
              <div v-for="(sub, subIndex) in JSON.parse(q.content)" :key="subIndex" class="comp-content-wrap">
                <div>题目{{ subIndex + 1 }}.&nbsp;<span style="display: block; width: 90%;" v-html="sub"/>&nbsp;<span>{{ `(${q.scoreArr[subIndex]}分)` }}</span></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="footer">
      <el-button :disabled="noPreviousOne" type="primary" @click="up">上一个</el-button>
      <el-button :disabled="downMarking || downMarkingShort" type="primary" @click="down">确定并进入下一个</el-button>
    </div>
    <!-- 靶机题查看 -->
    <el-dialog
      :visible.sync="modalViewNode"
      :width="'520px'"
      title="查看"
      append-to-body
      @close="modalViewNode = false"
    >
      <transition name="el-fade-in-linear">
        <view-node :node-id="nodeId" @close="modalViewNode = false" />
      </transition>
    </el-dialog>
  </div>
</template>
<script>
import { queryAnswer, submitScoreApi, studentList, queryById } from '@/api/exam/index.js'
import { vnc, serial, getConsole, getNodeItem, getSnapshot, restoreSnapshot } from '@/packages/topo/api/orchestration'
import detailView from '@/packages/detail-view/index'
import viewNode from '@/packages/topo/action/modal_view-node.vue'
export default {
  // 跳转到其他页面清除阅卷题目缓存(实验环境页面除外)
  beforeRouteLeave(to, from, next) {
    if (to && to.name) {
      if (to.name !== from.name && to.name !== 'openEnv') {
        this.allScore = '-'
        localStorage.removeItem('shortMarking')
        localStorage.removeItem('shortScore')
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i)
          if (key && key.includes('soreList')) {
            localStorage.removeItem(`${key}`)
          }
        }
      }
    }
    next()
  },
  components: {
    detailView,
    viewNode
  },
  data() {
    return {
      modalViewNode: false,
      nodeId: '',
      examInfo: {},
      id: null, // 资源ID
      data: {
        name: '阅卷',
        realname: ''
      }, // 资源数据对象
      loading: false,
      viewItem: [],
      name: '',
      score: '',
      synthesisArr: [],
      shortAnswerArr: [],
      studentListData: [],
      studentListParam: {},
      switchStudent: 0,
      maxStudent: 0,
      evaluationCode: this.$route.query.evaluationCode,
      currentEvaluationCode: '',
      realname: '',
      markingPapersStatus: 0,
      downMarking: false,
      downMarkingShort: false,
      allScore: '-',
      noPreviousOne: false,
      shortScore: {},
      shortMarking: {},
      isShowEditScore: {},
      shortAllScore: 0,
      shortAllScores: 0,
      questionObj: {}
    }
  },
  watch: {
    'shortAnswerArr': {
      handler(data) {
        this.downMarkingShort = data.some(item => !!item.Marking == false)
      },
      deep: true,
      immediate: true
    },
    'synthesisArr': {
      handler(data) {
        this.downMarking = data.some(item => !!item.Marking == false)
      },
      deep: true,
      immediate: true
    }
  },
  mounted() {
    this.getStudentList()
    this.queryById()
  },
  methods: {
    // 靶机题相关操作
    clickDrop(name, item, index, type) {
      switch (name) {
        case 'vnc': {
          vnc(item.vmId)
            .then(res => {
              if (res.data.code == 0) {
                const url = res.data.data
                const a = document.createElement('a')
                a.setAttribute('href', encodeURI(url))
                a.setAttribute('target', '_blank')
                a.setAttribute('id', 'camnpr')
                document.body.appendChild(a)
                a.click()
              }
            })
            .catch(() => {})
          break
        }
        case 'rdp':
        case 'webshell':
        case 'ssh': {
          getConsole(item.vmId, { console_type: name })
            .then(res => {
              const url = (window.location.hostname == 'localhost' ? (window.ADMIN_CONFIG ? window.ADMIN_CONFIG.VIP_URL : window.WEB_CONFIG.VIP_URL) : '') + res.data.data
              const a = document.createElement('a')
              a.setAttribute('href', encodeURI(url))
              a.setAttribute('target', '_blank')
              a.setAttribute('id', 'camnpr')
              document.body.appendChild(a)
              a.click()
            })
            .catch(() => {})
          break
        }
        case 'serial': {
          serial(item.vmId)
            .then(res => {
              const url = res.data.data + '&title=' + item.nodeName
              const a = document.createElement('a')
              a.setAttribute('href', encodeURI(url))
              a.setAttribute('target', '_blank')
              a.setAttribute('id', 'camnpr')
              document.body.appendChild(a)
              a.click()
            })
            .catch(() => {})
          break
        }
        case 'viewNode':
          this.modalViewNode = true
          this.nodeId = item.vmId
          break
        case 'resetNode': {
          getSnapshot({ 'node_id': item.vmId, limit: 999, offset: 0 }).then((res) => {
            const list = res.data.data
            if (list && list.length) {
              this.$bus.$emit(
                'SINGLE_TASK_API',
                {
                  taskName: '重置',
                  resource: list[list.length - 1],
                  apiObj: restoreSnapshot,
                  data: { id: item.vmId, data: { 'snapshot_id': list[0].id }},
                  sucsessCallback: (res) => {},
                  errorCallback: () => {}
                }
              )
            } else {
              this.$message.error('该题目没有快照')
            }
          })
          break
        }
      }
    },
    // 获取参数学生列表
    getStudentList: function() {
      const params = {
        page: 1,
        limit: 10000,
        examStatus: 1,
        examId: this.$route.query.examinationId
      }
      studentList(params).then((res) => {
        if (res.code === 0) {
          this.studentListData = res.data.records
          // 阅卷的学生在前面，未阅卷在后面
          this.studentListData.sort((a, b) => b.markingPapersStatus - a.markingPapersStatus)
          this.studentListParam = this.studentListData.find(item => item.evaluationCode == (this.evaluationCode || this.$route.query.evaluationCode))
          this.evaluationCode = this.studentListParam.evaluationCode
          this.data.realname = this.realname = this.studentListParam.realname
          // 该学生是否阅卷 1阅卷  0未阅卷
          this.markingPapersStatus = this.studentListParam.markingPapersStatus
          this.maxStudent = this.studentListData.length
          this.switchStudent = this.studentListData.findIndex(item => item.evaluationCode == this.$route.query.evaluationCode)
          // 如果是第一个学生，上一个不可点
          if (this.switchStudent === 0) {
            this.noPreviousOne = true
          }
          this.getQuestion()
        }
      })
    },
    queryById() {
      queryById({ id: this.$route.query.examinationId }).then((res) => {
        if (res.code === 0) {
          this.examInfo = res.data
        }
      })
    },
    shortChange(data, value) {
      value = Number(value)
      if (!Number.isInteger(value) || value < 0) {
        this.$message.warning('请输入自然数')
        this.$set(this.shortScore, `${data.questionCode}`, null)
        this.$set(this.isShowEditScore, `${data.questionCode}`, true)
        return
      }
      // 计算手工打分总分
      this.shortAllScores = 0
      Object.keys(this.shortScore).forEach((key) => {
        this.shortAllScores += this.shortScore[key]
      })
      // 如果有综合题，计入分数
      if (this.synthesisArr && this.synthesisArr.length > 0) {
        const allScore = []
        this.synthesisArr.map(item => {
          const scoreListData = JSON.parse(localStorage.getItem('soreList' + item.questionCode))
          // 如果有老师新打的分数就拿新打的分数，否则就拿老师之前打的分数
          if (scoreListData) {
            allScore.push(scoreListData)
          } else {
            allScore.push(JSON.parse(item.combinationUserPoints))
          }
          // 当打分0分时，手工阅卷分数显示0分
          if (this.allScore === 0) {
            for (let i = 0; i < localStorage.length; i++) {
              if (localStorage.key(i).includes('soreList')) {
                this.allScore = '0'
              }
            }
          }
        })
        // 老师打的全部题目的总分数
        this.allScore = allScore.flat().flat().map(each => Number(each)).reduce((p, q) => p + q)
        this.allScore = this.allScore + Number(this.shortAllScores)
      } else {
        this.allScore = this.shortAllScores
      }
      // 如果有综合题，计入分数
      if (this.synthesisArr && this.synthesisArr.length > 0) {
        const allScore = []
        this.synthesisArr.map(item => {
          const scoreListData = JSON.parse(localStorage.getItem('soreList' + item.questionCode))
          // 如果有老师新打的分数就拿新打的分数，否则就拿老师之前打的分数
          if (scoreListData) {
            allScore.push(scoreListData)
          } else {
            allScore.push(JSON.parse(item.combinationUserPoints))
          }
          // 当打分0分时，手工阅卷分数显示0分
          if (this.allScore === 0) {
            for (let i = 0; i < localStorage.length; i++) {
              if (localStorage.key(i).includes('soreList')) {
                this.allScore = '0'
              }
            }
          }
        })
        // 老师打的全部题目的总分数
        this.allScore = allScore.flat().flat().map(each => Number(each)).reduce((p, q) => p + q)
        this.allScore = this.allScore + Number(this.shortAllScores)
      } else {
        this.allScore = this.shortAllScores
      }
      this.shortAnswerArr.map((item) => {
        if (data.questionCode == item.questionCode) {
          item.Marking = true
        }
        this.shortMarking[item.questionCode] = item.Marking
      })
      for (const key in this.shortScore) {
        if (this.shortScore.hasOwnProperty(key)) { // 确保属性是对象自身的而不是从原型链继承的
          if (data.questionCode == key) {
            if (this.shortScore[key] > data.questionScore) {
              this.$set(this.shortScore, `${data.questionCode}`, null)
              this.isShowEditScore[data.questionCode] = true
              return this.$message.error('所填的分数不能大于该题的分数')
            } else {
              this.isShowEditScore[key] = false
            }
          }
        }
      }
      if (this.shortAnswerArr.every(item => item.Marking === true)) {
        this.downMarkingShort = false
      }
      this.$forceUpdate()
      this.allScore = String(this.allScore)
      localStorage.setItem('shortScore', JSON.stringify(this.shortScore))
      localStorage.setItem('shortMarking', JSON.stringify(this.shortMarking))
    },
    editScore(data) {
      this.shortAnswerArr.map((item) => {
        for (const key in this.shortScore) {
          if (this.shortScore.hasOwnProperty(key)) { // 确保属性是对象自身的而不是从原型链继承的
            if (data.questionCode == key) {
              this.isShowEditScore[key] = true
            }
          }
        }
        // this.downMarkingShort = false
        // // 如果题目未阅完，不能点击下个学生
        // if (!item.Marking) {
        //   this.downMarkingShort = true
        // }
      })
      this.$forceUpdate()
    },
    submitScore(lastOne = false, type) {
      // 简答题参数
      const shortSoreListData = []
      this.shortScore = JSON.parse(localStorage.getItem('shortScore')) || this.shortScore
      for (const key in this.shortScore) {
        if (this.shortScore.hasOwnProperty(key)) { // 确保属性是对象自身的而不是从原型链继承的
          shortSoreListData.push({
            evaluationCode: this.currentEvaluationCode,
            questionCode: key,
            combinationPoints: JSON.stringify([this.shortScore[key]]),
            questionScore: this.shortScore[key]
          })
        }
      }
      const params = this.synthesisArr.map((item) => {
        // 从缓存里面拿老师给学生打的题目分数
        const scoreListData = JSON.parse(localStorage.getItem('soreList' + item.questionCode))
        // 如果有缓存的分数 就拿缓存的分数，没有就拿老师已经阅过卷的分数的
        if (scoreListData) {
          const soreData = this.synthesisArr.find((soreItem) => soreItem.questionCode === item.questionCode)
          const allSore = scoreListData.flat().reduce((a, b) => a + b, 0)
          return {
            evaluationCode: this.currentEvaluationCode,
            questionCode: soreData.questionCode,
            combinationPoints: JSON.stringify(scoreListData),
            questionScore: allSore
          }
        } else {
          const allSore = JSON.parse(item.combinationUserPoints) ? JSON.parse(item.combinationUserPoints).flat().reduce((a, b) => a + b, 0) : ''
          return {
            evaluationCode: this.currentEvaluationCode,
            questionCode: item.questionCode,
            combinationPoints: item.combinationUserPoints,
            questionScore: allSore
          }
        }
      })
      if (type === 'up') {
        this.getQuestion()
        return
      }
      submitScoreApi([...params, ...shortSoreListData]).then((res) => {
        if (res.code === 0) {
          // 清除题目缓存，为了切换下一个学生是，重置已阅卷和未阅卷状态
          if (localStorage.getItem('shortMarking') !== null) {
            localStorage.removeItem('shortMarking')
          }
          if (localStorage.getItem('shortScore') !== null) {
            localStorage.removeItem('shortScore')
          }
          // 在阅卷完最后一个考生并提交后，等接口调用完成才返回考生列表页面
          if (lastOne) {
            this.$message.success('已完成本次考试阅卷')
            this.$router.push({ name: 'detailNonCertificateExam', params: { name: this.$route.query.twoLevelTitle, view: 'students', id: this.$route.query.examinationId }})
          }
          for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i)
            if (key && key.includes('soreList')) {
              localStorage.removeItem(`${key}`)
            }
          }
          // 切换学生时，该学生是否已经是阅卷状态了
          this.studentListParam = this.studentListData.find(item => item.evaluationCode == (this.evaluationCode || this.$route.query.evaluationCode))
          this.markingPapersStatus = this.studentListParam.markingPapersStatus
          this.getQuestion()
        }
      })
    },
    getQuestion() {
      this.loading = true
      const { userId, examCode, examinationId, submitType } = this.$route.query
      const params = {
        userId: this.studentListParam.userId || userId,
        examCode: examCode,
        evaluationCode: this.evaluationCode,
        examinationId: examinationId,
        submitType: submitType
      }
      queryAnswer(params).then((res) => {
        if (res.code === 0) {
          res.data.forEach(item => {
            if (item.bankType == 2 && item.vmId) {
              getNodeItem(item.vmId).then(response => {
                this.$set(item, 'console_type', response.data.data.console_type)
                this.$set(item, 'nodeName', response.data.data.name)
              })
            }
          })
          this.loading = false
          this.markingPapersStatus = res.data[0].markingPapersStatus

          // 处理所有非组合题题型
          const questionTypes = {
            '1': '单选题',
            '2': '多选题',
            '3': '判断题',
            '4': 'CTF题',
            '5': 'AWD题',
            '6': '其他题',
            '7': '填空题',
            '8': '简答题',
            '9': '漏洞题'
          }

          // 按题型分组题目
          Object.keys(questionTypes).forEach(type => {
            const questions = res.data.filter(q => q.questionType === type)
            if (questions.length) {
              this.questionObj[type] = { questionTitle: questionTypes[type], list: questions }
            }
          })

          // 处理简答题和其他题型的阅卷状态
          this.shortAnswerArr = res.data.filter(q => ['1', '2', '3', '4', '5', '6', '7', '8', '9'].includes(q.questionType))
          this.shortMarking = JSON.parse(localStorage.getItem('shortMarking')) || {}
          this.shortAnswerArr.map((item) => {
            this.downMarkingShort = false
            // 如果题目未阅完，不能点击下个学生
            if (!item.Marking) {
              this.downMarkingShort = true
            }
            item.combinationUserPoints = item.combinationUserPoints ? item.combinationUserPoints : '[]'
            this.shortScore[item.questionCode] = JSON.parse(item.combinationUserPoints)[0] || ''
            this.isShowEditScore[item.questionCode] = false
            if (this.markingPapersStatus) {
              item.Marking = true
            }
            if (Object.keys(this.shortMarking).length > 0) {
              for (const key in this.shortMarking) {
                if (key == item.questionCode) {
                  item.Marking = this.shortMarking[key]
                }
              }
            }
          })
          this.synthesisArr = res.data.filter(q => q.questionType == '10')
          if (this.synthesisArr.length) {
            this.questionObj['10'] = { questionTitle: '组合题', list: this.synthesisArr }
          }
          this.synthesisArr.map((item, index) => {
            const scoreData = JSON.parse(item.combinationPoints)
            item.combinationQuestionBOS.map((questionItem, questionIndex) => {
              questionItem.questionScore = scoreData[questionIndex].map(each => Number(each)).reduce((p, q) => p + q)
              questionItem.scoreArr = scoreData[questionIndex] // 子题干的分数
            })
            item.questionScore = JSON.parse(item.combinationPoints)[0].map(each => Number(each)).reduce((p, q) => p + q) // 主题干的分数
            // 判断已阅卷和未阅卷的按钮状态  如果已阅过卷或者老师刚阅过卷
            if (this.markingPapersStatus || JSON.parse(localStorage.getItem('soreList' + item.questionCode))) {
              item.Marking = true
            }
            // 给每个题目标题家序号
            item.questionName = `组合题${index + 1}.` + item.questionName
          })
          this.shortScore = JSON.parse(localStorage.getItem('shortScore')) || this.shortScore
          this.shortAllScores = 0
          Object.keys(this.shortScore).forEach((key) => {
            this.shortAllScores += this.shortScore[key]
          })
          if (this.synthesisArr && this.synthesisArr.length > 0) {
            const allScore = []
            this.synthesisArr.map(item => {
              this.downMarking = false
              // 如果题目未阅完，不能点击下个学生
              if (!item.Marking) {
                this.downMarking = true
              }
              const scoreListData = JSON.parse(localStorage.getItem('soreList' + item.questionCode))
              // 如果有老师新打的分数就拿新打的分数，否则就拿老师之前打的分数
              if (scoreListData) {
                allScore.push(scoreListData)
              } else {
                allScore.push(JSON.parse(item.combinationUserPoints))
              }
            })
            // 老师打的全部题目的总分数
            this.allScore = allScore.flat().flat().map(each => Number(each)).reduce((p, q) => p + q)
            this.allScore = this.allScore + Number(this.shortAllScores)
            // 当打分0分时，手工阅卷分数显示0分
            if (this.allScore === 0) {
              for (let i = 0; i < localStorage.length; i++) {
                if (localStorage.key(i).includes('soreList')) {
                  this.allScore = '0'
                }
              }
            }
          } else {
            this.allScore = this.shortAllScores
          }
        }
      })
    },
    up() {
      // 如果是第一个学生不可点击上一个
      this.noPreviousOne = false
      if (this.switchStudent === 1) {
        this.noPreviousOne = true
      }
      this.currentEvaluationCode = this.studentListData[this.switchStudent].evaluationCode
      this.switchStudent = this.switchStudent - 1
      if (this.switchStudent < 0) {
        this.switchStudent = 0
        return
      }
      // 切换是，修改学生姓名和请求参数
      this.evaluationCode = this.studentListData[this.switchStudent].evaluationCode
      this.data.realname = this.realname = this.studentListData[this.switchStudent].realname
      this.submitScore(false, 'up')
    },
    down() {
      this.currentEvaluationCode = this.studentListData[this.switchStudent].evaluationCode
      this.switchStudent = this.switchStudent + 1
      // 最后一个时，不可点击了
      if (this.switchStudent >= this.maxStudent) {
        this.switchStudent = this.maxStudent - 1
        this.submitScore(true)
        return
      }
      this.noPreviousOne = false
      this.evaluationCode = this.studentListData[this.switchStudent].evaluationCode
      this.data.realname = this.realname = this.studentListData[this.switchStudent].realname
      this.submitScore(false, 'down')
    },
    openEnv(data) {
      this.$router.push({
        name: 'openEnv',
        query: {
          ...data,
          ...this.$route.query,
          evaluationCode: this.evaluationCode
        }
      })
    },
    // 下载文件
    downloadReport(item) {
      if (item.fileUrl) {
        fetch(item.fileUrl, {
          method: 'get',
          responseType: 'blob'
        })
          .then((response) => response.blob())
          .then((blob) => {
            const a = document.createElement('a')
            const URL = window.URL || window.webkitURL
            const href = URL.createObjectURL(blob)
            a.href = href
            a.download = `${this.examInfo.name}-${this.studentListParam.realname}-${item.fileName}`
            document.body.appendChild(a)
            a.click()
            document.body.removeChild(a)
            URL.revokeObjectURL(href)
          })
      }
    },
    arabicToChinese(number) {
      // 处理负数
      if (number < 0) {
        return '负' + this.arabicToChinese(Math.abs(number))
      }
      // 处理小数
      if (number !== Math.floor(number)) {
        return '非整数'
      }
      const chineseDigits = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九']
      const chineseUnits = ['', '十', '百', '千', '万', '亿']
      if (number === 0) {
        return chineseDigits[0]
      }
      let result = ''
      let unitIndex = 0
      let lastDigit = 0
      let hasZero = false
      while (number > 0) {
        const digit = number % 10
        // 处理零
        if (digit === 0) {
          if (!hasZero && lastDigit !== 0) {
            result = chineseDigits[0] + result
            hasZero = true
          }
        } else {
          // 处理特殊情况：一十 -> 十
          if (digit === 1 && unitIndex === 1 && result === '') {
            result = chineseUnits[unitIndex]
          } else {
            result = chineseDigits[digit] + chineseUnits[unitIndex] + result
          }
          hasZero = false
        }
        lastDigit = digit
        number = Math.floor(number / 10)
        unitIndex++
      }
      // 处理末尾的零
      if (result.endsWith(chineseDigits[0])) {
        result = result.slice(0, -1)
      }
      return result
    }
  }
}
</script>

<style scoped lang="scss">
.question_info {
  margin: 20px 10px;
    padding: 10px;
    font-size: 14px;
    border: 1px solid #d1d1d1;
    border-radius: 10px;
    box-shadow: 1px 1px 1px rgba(0, 0, 0, 0.1);
  .short_answer {
    width: 90%;
    padding-left: 25px;
    .stu_answer {
    }
    .cor_answer {
      margin: 5px 0;
    }
    .cor_analysis {
      display: flex;
    }
  }
}
.footer {
  width: 100%;
  text-align: right;
  padding: 10px 24px;
  border-top: 1px solid var(--neutral-300);
  background-color: var(--neutral-0);
}
.name_question {
  display: flex;
  justify-content: space-between;
  font-weight: 700;
  font-size: 15px;
}
.border_comp {
  border: 1px solid #d1d1d1;
  border-radius: 10px;
  box-shadow: 1px 1px 1px rgba(0, 0, 0, 0.1);
  margin: 10px;
  padding: 20px;
}
.wrap-layout {
  overflow: hidden;
  width: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  min-width: 880px;
  height: 100%;
  // overflow-y: auto;
  /deep/ .detail-tabs {
    display: none;
  }
}
  .question_types {
    font-size: 16px;
    font-weight: 600;
    height: 35px;
    line-height: 35px;
    font-family: Source Han Sans CN;
  }
  .comp-question {
    display: flex;
    max-height: 200px;
    overflow-y: auto;
    font-weight: 600;
    font-size: 14px;
    color: rgb(36, 41, 47);
    >span {
      word-break: break-all;
    }
  }
  .comp-content-wrap {
    border-radius: 10px;
    border: 1px solid #e5e6eb;
    margin: 5px 0 10px;
    padding: 10px 20px;
    >div:first-child {
      display: flex;
      max-height: 200px;
      overflow-y: auto;
      margin-bottom: 10px;
      >span {
        word-break: break-all;
      }
    }
    &:last-child {
      margin-bottom: 0;
    }
  }
  .env_btn {
  width: 90px;
  min-width: 90px;
  // height: 28px;
  // line-height: 28px;
  font-size: 13px;
  text-align: center;
  border: none;
  background-color: var(--color-600);
  color: #ffffff;
  padding: 0;
}
.env_disabled {
    background-color: #f7f7f7;
    border: none;
    color: #999999;
}
.env_abled {
  background-color: #88cf65;
    border: none;
    color: #ffffff;
}
  .stuMsg {
    display: flex;
    justify-content: space-between;
    padding: 0 20px;
  }
  .content {
    flex: 1;
    overflow-y: auto;
    padding: 0px 20px;
    .title {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      padding: 10px 0;
    }
  }
  ._question_info{
    position: relative;
    .download {
      position: absolute;
      top: 0;
      right: 0;
    }
  }
  /deep/.detail-wrap-layout {
    .detail-header {
      // margin-top: 45px;
    }
    .detail-breadcrumb {
      width: 100%;
      background: #ffffff;
    }
    .detail-breadcrumb::after {
      display: none;
      content: ""; /* 必须要有内容，才能显示伪元素 */
      position: absolute; /* 绝对定位，以便位于 div 元素下面 */
      bottom: 3px; /* 控制伪元素的位置，使其在 div 元素下面 */
      left: 0;
      width: 100%;
      height: 1px; /* 阴影的高度 */
      box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.8);
    }
  }
</style>
