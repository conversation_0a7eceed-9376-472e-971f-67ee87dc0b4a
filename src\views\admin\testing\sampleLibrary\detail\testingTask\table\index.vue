<template>
  <div class="resource-table">
    <!-- 操作区 -->
    <div class="operation-wrap">
      <div class="operation-left">
        <slot name="action" />
        <el-button type="primary" icon="el-icon-refresh" @click="refresh" />
      </div>
      <div class="operation-right">
        <el-badge :value="searchBtnShowNum">
          <el-button icon="el-icon-search" @click="openSearch">搜索</el-button>
        </el-badge>
        <t-table-config
          v-if="!customColData.length"
          :data="columnsObj"
          :active-key-arr="columnsViewArr"
          @on-change-col="onChangeCol"
        />
      </div>
    </div>
    <!-- 搜索区 -->
    <t-search-box
      v-show="searchView"
      :search-key-list="searchKeyListView"
      default-placeholder="默认搜索测试任务"
      @search="searchMultiple"
    />
    <!-- 列表 -->
    <t-table-view
      ref="tableView"
      :height="height"
      :single="true"
      :loading="tableLoading"
      :data="tableData"
      :total="tableTotal"
      :page-size="pageSize"
      :tree-props="treeProps"
      :current="pageCurrent"
      :select-item="selectItem"
      :default-expand-all="true"
      :is-selectable-row="row => !row.children || row.children.length === 0"
      current-key="id"
      @on-select="onSelect"
      @on-current="onCurrent"
      @on-change="changePage"
      @on-sort-change="onSortChange"
      @on-page-size-change="onPageSizeChange"
    >
      <el-table-column
        v-for="item in columnsViewArr"
        :key="item"
        :min-width="columnsObj[item].colMinWidth || colMinWidth"
        :width="columnsObj[item].colWidth"
        :label="columnsObj[item].title"
        :fixed="columnsObj[item].master ? 'left' : false"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <!-- 主任务行 -->
          <template v-if="scope.row.children && typeof scope.row.children.length === 'number' && scope.row.children.length > 0">
            <span v-if="item === 'name'">{{ scope.row.name }}</span>
            <span v-else/>
          </template>
          <!-- 子任务行 -->
          <template v-else>
            <span v-if="item === 'name'">
              <a
                :style="scope.row.parentId ? 'padding-left: 2em;' : ''"
                href="javascript:;"
                @click="
                  linkEvent('sampleLibraryTaskDetail', scope.row, {
                    id: scope.row.id,
                    projectId: projectId,
                    taskId: scope.row.id,
                    applyId: scope.row.applyId,
                    view: 'overview'
                  })
                "
              >
                {{ scope.row[item] || "-" }}
              </a>
            </span>
            <span v-else-if="item === 'status'">
              <el-badge :type="module.statusObj[scope.row[item]] ? module.statusObj[scope.row[item]].type : 'info'" is-dot />
              {{ module.statusObj[scope.row[item]] ? module.statusObj[scope.row[item]].label : '-' }}
            </span>
            <span v-else-if="item === 'tester'">
              <table-td-multi-col v-if="scope.row.testerList && scope.row.testerList.length > 0" :data="scope.row.testerList" :number="scope.row.testerList.length">
                <div slot="reference">{{ scope.row.testerList[0].testerName }}</div>
                <div v-for="val in scope.row.testerList" :key="val.id">{{ val.testerName }}</div>
              </table-td-multi-col>
              <span v-else>-</span>
            </span>
            <span v-else-if="item === 'caseList'">
              <a
                :href="`/testing/sampleLibrary/sampleLibraryDetail/${scope.row.id}/${$route.params.projectId || projectId}/testingCase`"
                @click.prevent="linkEvent('sampleLibraryTaskDetail', scope.row, { id: scope.row.id, projectId: $route.params.projectId || projectId, view: 'testingCase' })"
              >
                {{
                  Array.isArray(scope.row[item])
                    ? scope.row[item].length
                    : (
                      typeof scope.row[item] === 'string' && scope.row[item].startsWith('[')
                        ? JSON.parse(scope.row[item]).length
                        : '-'
                    )
                }}
              </a>
            </span>
            <span v-else>
              {{ scope.row[item] || '-' }}
            </span>
          </template>
        </template>
      </el-table-column>
    </t-table-view>
  </div>
</template>

<script>
import module from '../config.js'
import tSearchBox from '@/packages/search-box/index.vue'
import tTableView from '@/components/testing/tableView/index.vue'
import tTableConfig from '@/packages/table-config/table-col-config.vue'
import tableTdMultiCol from '@/packages/table-config/table-td-multi-col.vue'
import mixinsPageTable from '@/packages/mixins/page_table'
import { getSampleTaskPage } from '@/api/testing/index'

export default {
  components: {
    tSearchBox,
    tTableView,
    tTableConfig,
    tableTdMultiCol
  },
  mixins: [mixinsPageTable],
  data() {
    return {
      module,
      moduleName: module.name,
      // 搜索配置项
      searchKeyList: [
        { key: 'name', label: '测试任务', master: true },
        { key: 'statusList', label: '状态', type: 'radio', valueList: module.statuStrArr },
        { key: 'round', label: '测试轮次' },
        { key: 'tester', label: '测试人员' },
        { key: 'time', label: '开始时间', type: 'time_range' },
        { key: 'createAt', label: '创建时间', type: 'time_range' }
      ],
      // 所有可配置显示列
      columnsObj: {
        'name': {
          title: '测试任务', master: true
        },
        'status': {
          title: '状态'
        },
        'round': {
          title: '测试轮次'
        },
        'tester': {
          title: '测试人员'
        },
        'caseList': {
          title: '用例数量'
        },
        'beginTime': {
          title: '开始时间'
        },
        'endTime': {
          title: '结束时间'
        },
        'createAt': {
          title: '创建时间'
        }
      },
      // 当前显示列
      columnsViewArr: [
        'name',
        'status',
        'round',
        'tester',
        'caseList',
        'beginTime',
        'endTime',
        'createAt'
      ],
      treeProps: { children: 'children', hasChildren: 'hasChildren' },
      projectId: this.$route.name === 'testingTask_detail' ? this.$route.params.projectId : this.$route.params.id
    }
  },
  methods: {
    getList(showLoading = true) {
      if (showLoading) {
        this.tableLoading = true
      }
      const params = this.getPostData('pageNum', 'pageSize')
      params.projectId = this.projectId
      if (params.statusList) {
        params.statusList = Array.isArray(params.statusList) ? params.statusList : [params.statusList]
      }
      if (params.time) {
        params.beginTime = params.time.split(',')[0]
        params.endTime = params.time.split(',')[1]
      }
      if (params.createAt) {
        params.createAtBegin = params.createAt.split(',')[0]
        params.createAtEnd = params.createAt.split(',')[1]
      }
      getSampleTaskPage(params).then(res => {
        this.tableData = res.data.data ? res.data.data.records : []
        this.tableTotal = Number(res.data.data.total) || 0
        this.tableLoading = false
        this.handleSelection()
      }).catch(() => {
        this.tableLoading = false
      })
    }
  }
}
</script>
