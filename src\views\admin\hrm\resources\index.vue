<template>
  <div class="main">
    <div class="title">
      <div class="title-content blue">
        <div class="name">基础教学</div>
        <div class="quantity">1630节</div>
      </div>
      <div class="title-content sky-blue">
        <div class="name">题库数量</div>
        <div class="quantity">2603节</div>
      </div>
      <div class="title-content green">
        <div class="name">安全产品</div>
        <div class="quantity">17台</div>
      </div>
      <div class="title-content yellow">
        <div class="name">虚拟化资源</div>
        <div class="quantity">347台</div>
      </div>
      <div class="title-content orange">
        <div class="name">安全工具</div>
        <div class="quantity">94个</div>
      </div>
    </div>
    <div class="body">
      <div class="icon">
        <div class="icon-title">
          <div class="spot"/>
          <div>知识分布点</div>
        </div>
        <!-- <div class="wp-1 h-26">
          <div id="char2" ref="chart2" class="wp-1 hp-1"/>
        </div> -->
      </div>
    </div>
  </div>
</template>

<script>
// import * as echarts from 'echarts'
// import 'echarts-wordcloud/dist/echarts-wordcloud'
export default {
  data() {
    // 定义需要展示的词语和数值（数值越大，字体会越大）
    // [
    //   {
    //     name: 'Java',
    //     value: 2300
    //   },
    //   {
    //     name: 'python',
    //     value: 2000
    //   }
    // ]
  },
  mounted() {
    // this.$nextTick(() => {
    //   this.initCharts() // 调用定义词云图方法
    // })
  },
  methods: {
    // 定义词云图并插入容器内
    // initCharts() {
    //   const myChart2 = echarts.init(this.$refs.chart2)
    //   const dou_live_word_result = this.worddata
    //   myChart2.setOption({
    //     title: {
    //       x: 'center'
    //     },
    //     backgroundColor: '#fff',
    //     series: [{
    //       type: 'wordCloud',
    //       // 用来调整词之间的距离
    //       gridSize: 10,
    //       // 用来调整字的大小范围
    //       sizeRange: [14, 60],
    //       // 用来调整词的旋转方向，，[0,0]--代表着没有角度，也就是词为水平方向，需要设置角度参考注释内容
    //       // rotationRange: [-45, 0, 45, 90],
    //       // rotationRange: [ 0,90],
    //       rotationRange: [0, 0],
    //       // 随机生成字体颜色
    //       textStyle: {
    //         normal: {
    //           color: function() {
    //             return (
    //               'rgb(' +
    //              Math.round(Math.random() * 255) +
    //               ', ' +
    //               Math.round(Math.random() * 255) +
    //               ', ' +
    //               Math.round(Math.random() * 255) +
    //               ')'
    //             )
    //           }
    //         }
    //       },
    //       // 位置相关设置
    //       left: 'center',
    //       top: 'center',
    //       right: null,
    //       bottom: null,
    //       width: '200%',
    //       height: '200%',
    //       // 数据
    //       data: dou_live_word_result
    //     }]
    //   })
    // }
  }

}
</script>

<style lang="scss" scoped>
.main{
  padding: 30px;
  .title{
    display: flex;
    align-items: center;
    justify-content: space-between;
    .blue{
      background: #2865EF;
    }
    .sky-blue{
      background: #28A6EF;
    }
    .green{
      background: #05AA53;
    }
    .yellow{
      background: #EFA628;
    }
    .orange{
      background: #EF8628;
    }
    .title-content{
      width: 280px;
      height: 70px;
      border-radius: 6px;
      display: flex;
      flex-direction: column;
      color: #fff;
      font-family: Microsoft YaHei;
      font-weight: 400;
      padding: 9px 0 18px 0;
      align-items: center;
      .name{
        font-size: 14px;
        margin-bottom: 8px;
      }
      .quantity{
        font-size: 20px;
      }
    }
  }
  .body{
    display: flex;
    margin-top: 30px;
    align-items: center;
    justify-content: space-between;
    .icon{
      padding: 30px;
      background: #FFFFFF;
      .icon-title{
        display: flex;
        align-items: center;
        font-size: 16px;
        font-family: Microsoft YaHei;
        font-weight: 400;
        color: #333333;
        .spot{
          width: 4px;
          height: 20px;
          background: #288FEF;
          margin-right: 9px;
        }
      }
    }
  }
}
</style>
