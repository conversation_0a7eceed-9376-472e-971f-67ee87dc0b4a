// https://github.com/ElemeFE/element/issues/10308
.el-table th.is-leaf {
  padding: 8px 0;
  background-color: $xr--background-color-base;
}

.el-table th.is-sortable {
  padding: 3px;
}

.el-table th:hover {
  background-color: #E7EDF4;
}

.el-table th>.cell {
  color: #333;
  font-size: 12px;
  font-weight: 600;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.el-table .caret-wrapper {
  left: 2px;
}

.el-table .sort-caret {
  transform: scale(0.8);
}

.el-table .sort-caret.ascending {
  top: 7px;
}

.el-table th.is-sortable.ascending,
.el-table th.is-sortable.descending {
  background-color: #E7EDF4;
}

.el-table__body td {
  padding: 0;
  height: 40px;
  border-top-color: transparent;
  border-left-color: transparent;
  border-right-color: transparent;
  border-bottom-color: $xr-border-line-color;
  font-size: 13px;
  color: #333;
}

.el-table .el-table__row.current-row {
  td:first-child::before {
    content: ' ';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    width: 2px;
    background-color: #5383ED;
  }
}

.el-table__fixed-right {
  td::before {
    display: none;
  }
}

.el-table__fixed::before,
.el-table__fixed-right::before {
  display: none;
}

.n-table--border {
  border-width: 0;
}

// 右侧边线
.n-table--border::after {
  display: none;
}

// 底部边线
.n-table--border::before {
  display: none;
}

// 分页
.p-contianer {
  position: relative;
  background-color: white;
  height: 44px;
  border-top: 1px solid $xr-border-line-color  ;

  .p-bar {
    float: right;
    margin: 5px 100px 0 0;
  }
}

// 头部为白色 不加粗的表风格
.el-table-header--white {
  th {
    border-right-width: 0;
    background-color: white !important;
    .cell {
      font-size: 13px;
      font-weight: normal;
      font-weight: bold;
    }
    &:hover {
      border-right-width: 1px;
    }
  }

  .el-table-column--selection .cell {
    padding-right: 14px !important;
  }
}

.el-table-column--selection .cell {
  padding-left: 12px !important;
}
