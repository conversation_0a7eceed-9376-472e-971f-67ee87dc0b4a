<template>
  <detail-view
    :loading="loading"
    :data="data"
    :id="id"
    :view-item="viewItem"
    mode="drawer"
    title-key="name"
  />
</template>
<script>
import detailView from '@/packages/detail-view/index'
import question from './question'
export default {
  components: {
    detailView,
    question
  },
  data() {
    return {
      id: null, // 资源ID
      data: {}, // 资源数据对象
      loading: false,
      viewItem: [
        {
          transName: '题目',
          name: 'question',
          component: question
        }
      ]
    }
  }
}
</script>
