import request from '@/utils/request'
// 分页查询任务模板列表
export function taskTemplateQueryPage(data) {
  return request({
    url: '/scene/taskTemplate/queryPage',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

// 新增任务模板
export function createTaskTemplate(data) {
  return request({
    url: '/scene/taskTemplate/create',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

// 修改任务模板
export function updateTaskTemplate(data) {
  return request({
    url: '/scene/taskTemplate/update',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

// 获取单条任务模板信息接口
export function getTaskTemplate(params) {
  return request({
    url: '/scene/taskTemplate/get',
    method: 'get',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    params
  })
}


// 获取单条任务模板信息接口
export function queryStageVOByTaskTemplateId(params) {
  return request({
    url: '/scene/sceneStage/queryStageVOByTaskTemplateId',
    method: 'get',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    params
  })
}

// 使用任务模板
export function chooseStageVOByTaskTemplateId(data) {
  return request({
    url: '/scene/sceneStage/chooseStageVOByTaskTemplateId',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

// 删除任务模板
export function removeTaskTemplate(data) {
  return request({
    url: '/scene/taskTemplate/remove',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

// 创建任务模板
export function createTemplateAll(data) {
  return request({
    url: '/scene/taskTemplate/createTemplateAll',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}
