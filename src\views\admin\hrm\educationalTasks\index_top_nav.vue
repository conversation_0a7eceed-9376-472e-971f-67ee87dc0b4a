<template>
  <div class="content-header">
    <el-tabs v-model="tabsActive" class="content-subs" type="card" @tab-click="handleTabClick">
      <el-tab-pane label="小组列表" name="listGroups">
        <router-link :to="{ name: 'listGroups' }" />
      </el-tab-pane>
      <el-tab-pane label="学生列表" name="studentList" @tab-click="handleTabClick">
        <router-link :to="{ name: 'studentList' }" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
export default {
  data() {
    return {
      tabsActive: ''
    }
  },
  created() {
    this.tabsActive = this.$route.name
  },
  methods: {
    'handleTabClick': function(data) {
      this.$router.push({ name: data.name,
        query: { ...this.$route.query }
      })
    }
  }
}
</script>
