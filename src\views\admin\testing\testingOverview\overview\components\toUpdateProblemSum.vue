<template>
  <div class="resource-table" style="height: 100%;">
    <!-- 操作区 -->
    <div class="operation-wrap">
      <div class="operation-left">
        <slot name="action" />
        <el-button type="primary" icon="el-icon-refresh" @click="refresh" />
      </div>
      <div class="operation-right">
        <el-badge :value="searchBtnShowNum">
          <el-button icon="el-icon-search" @click="openSearch">搜索</el-button>
        </el-badge>
        <!-- 自定义表格列 -->
        <t-table-config
          :data="columnsObj"
          :active-key-arr="columnsViewArr"
          @on-change-col="onChangeCol"
        />
      </div>
    </div>
    <!-- 搜索区 -->
    <t-search-box
      v-show="searchView"
      :search-key-list="searchKeyListView"
      :default-placeholder="'默认搜索问题标题'"
      @search="searchMultiple"
    />
    <!-- 列表 -->
    <t-table-view
      ref="tableView"
      :height="height"
      :single="true"
      :loading="tableLoading"
      :data="tableData"
      :total="tableTotal"
      :page-size="pageSize"
      :current="pageCurrent"
      :select-item="selectItem"
      type="list"
      current-key="id"
      @on-select="onSelect"
      @on-current="onCurrent"
      @on-change="changePage"
      @on-sort-change="onSortChange"
      @on-page-size-change="onPageSizeChange"
    >
      <el-table-column
        v-for="item in columnsViewArr"
        :key="item"
        :width="columnsObj[item].colWidth"
        :label="columnsObj[item].title"
        :fixed="columnsObj[item].master ? 'left' : false"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span v-if="item === 'problemNo'">
            <a :href="`/testing/unFixedQuestion/detail/${scope.row.id}/overview`" target="_blank">{{ scope.row.problemNo||'-' }}</a>
          </span>
          <span v-else-if="item === 'title'">
            <a :href="`/testing/unFixedQuestion/detail/${scope.row.id}/overview`" target="_blank">{{ scope.row.title||'-' }}</a>
          </span>
          <span v-else-if="item === 'type'">
            <span v-if="scope.row.type === '1'">安全漏洞</span>
            <span v-else-if="scope.row.type === '2'">功能问题</span>
            <span v-else-if="scope.row.type === '3'">性能问题</span>
            <span v-else>-</span>
          </span>
          <span v-else-if="item === 'impactLevel'">
            <span :class="getImpactLevelType(scope.row[item])">
              {{ getImpactLevelLabel(scope.row[item]) || '-' }}
            </span>
          </span>
          <span v-else-if="item === 'projectName'">
            <a :href="`/testing/testing/detail/${scope.row.projectId}/overview`" target="_blank">{{ scope.row.projectName||'-' }}</a>
          </span>
          <span v-else>{{ scope.row[item] || '-' }}</span>
        </template>
      </el-table-column>
    </t-table-view>
  </div>
</template>
<script>
import { testerTodoIssuePage } from '@/api/testing/testingOverview.js'
import mixinsPageTable from '@/packages/mixins/page_table'
import tSearchBox from '@/packages/search-box/index.vue'
import tTableConfig from '@/packages/table-config/table-col-config.vue'
import tableTdMultiCol from '@/packages/table-config/table-td-multi-col.vue'
import tTableView from '@/packages/table-view/index.vue'

// 引入封装的方法
export default {
  components: {
    tSearchBox,
    tTableView,
    tTableConfig,
    tableTdMultiCol
  },
  mixins: [mixinsPageTable],
  props: {
    processId: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    return {
      searchKeyList: [
        { key: 'title', label: '问题标题', master: true, placeholder: '请输入' },
        { key: 'problemNo', label: 'ID', master: true, placeholder: '请输入' },
        { key: 'type', label: '问题类型', placeholder: '请输入', type: 'select', valueList: [
          { label: '安全问题', value: '1' },
          { label: '功能问题', value: '2' },
          { label: '性能问题', value: '3' },
          { label: '漏洞问题', value: '4' }
        ] },
        { key: 'impactLevel', label: '影响程度', placeholder: '请输入', type: 'select', valueList: [
          { label: '轻微', value: 1, type: 'info' },
          { label: '一般', value: 2, type: 'primary' },
          { label: '严重', value: 3, type: 'warning' },
          { label: '致命', value: 4, type: 'danger' }
        ] },
        { key: 'projectName', label: '所属检查项目', placeholder: '请输入' }
      ],
      columnsObj: {
        'problemNo': {
          title: 'ID', master: true, colWidth: 80
        },
        'title': {
          title: '问题标题'
        },
        'type': {
          title: '问题类型'
        },
        'impactLevel': {
          title: '影响程度'
        },
        'projectName': {
          title: '所属检查项目'
        }
      },
      columnsViewArr: [
        'problemNo',
        'title',
        'type',
        'impactLevel',
        'projectName'
      ]
    }
  },
  methods: {
    getImpactLevelType(value) {
      const impactLevelItem = this.searchKeyList.find(item => item.key === 'impactLevel') && this.searchKeyList.find(item => item.key === 'impactLevel').valueList.find(item => item.value === value)
      return impactLevelItem && impactLevelItem.type || ''
    },
    getImpactLevelLabel(value) {
      const impactLevelItem = this.searchKeyList.find(item => item.key === 'impactLevel') && this.searchKeyList.find(item => item.key === 'impactLevel').valueList.find(item => item.value === value)
      return impactLevelItem && impactLevelItem.label || '-'
    },
    getList: function(showLoading = true) {
      if (showLoading) {
        this.tableLoading = true
      }
      const params = this.getPostData('page', 'limit')
      params.processId = this.processId
      if (params.type) {
        params.type = params.type.join(',')
      }
      if (params.impactLevel) {
        params.impactLevel = params.impactLevel.join(',')
      }
      testerTodoIssuePage(params).then((res) => {
        const data = { ...res.data }
        this.tableData = data ? data.records : []
        this.tableTotal = Number(data.total) || 0
        this.tableLoading = false
        this.handleSelection()
      }).catch(() => {
        this.tableLoading = false
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.success {
  color: var(--color-600);
}
.warning {
  color: #E6A23C;
}
.primary {
  color: #409EFF;
}
.danger {
  color: #F56C6C;
}
</style>
