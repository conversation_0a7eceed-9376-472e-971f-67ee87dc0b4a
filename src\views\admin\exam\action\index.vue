<template>
  <div class="buttons-wrap">
    <div :class="{ 'folded': fold }" class="category-fold-wrap" @click="handleFold">
      <div v-if="fold">展开<i class="el-icon-d-arrow-left rotate-90deg" /></div>
      <div v-else>折叠<i class="el-icon-d-arrow-left rotate90deg" /></div>
    </div>
    <el-button type="primary" icon="el-icon-plus" @click="$router.push({ name: 'addExam' })">创建考试</el-button>
    <el-dropdown trigger="click" placement="bottom-start" @command="clickDrop">
      <el-button type="primary">
        操作<i class="el-icon-arrow-down el-icon--right" />
      </el-button>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item :disabled="singleDisabled" command="editExam">编辑</el-dropdown-item>
        <el-dropdown-item :disabled="multipleDisabled" command="deleteExam">删除</el-dropdown-item>
        <el-dropdown-item :disabled="singleDisabled || selectItem[0].published === '1'" command="publicExam">发布</el-dropdown-item>
        <el-dropdown-item :disabled="singleDisabled || selectItem[0].published === '0'" command="privateExam">取消发布</el-dropdown-item>
        <el-dropdown-item :disabled="singleDisabled" command="cloneExam">克隆</el-dropdown-item>
        <el-dropdown-item :disabled="singleDisabled || selectItem[0].envStatus !== 0 || selectItem[0].published !== '1' || selectItem[0].questionType === 1" command="openEnv">开启环境</el-dropdown-item>
        <el-dropdown-item :disabled="singleDisabled || !selectItem[0].envStatus || selectItem[0].envStatus === 0" command="closeEnv">关闭环境</el-dropdown-item>
        <el-dropdown-item :disabled="singleDisabled || selectItem[0].envStatus !== 1" command="snapshot">创建快照</el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
    <!-- 中部弹窗 start-->
    <el-dialog
      :title="titleMapping[modalName]"
      :visible.sync="modalShow"
      :width="modalWidth"
      :destroy-on-close="true"
      @close="modalClose"
    >
      <transition name="el-fade-in-linear">
        <component
          :is="modalName"
          :name="modalName"
          :data="selectItem"
          @close="modalClose"
          @call="confirmCall"
        />
      </transition>
    </el-dialog>
    <!-- 中部弹窗 end-->
  </div>
</template>
<script>
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
import deleteExam from './modal-delete'
import publicExam from './modal-authority.vue'
import privateExam from './modal-authority.vue'
import openEnv from './modal-envHandle.vue'
import closeEnv from './modal-envHandle.vue'
import snapshot from './modal-snapshot.vue'

export default {
  components: {
    deleteExam,
    publicExam,
    privateExam,
    openEnv,
    closeEnv,
    snapshot
  },
  mixins: [mixinsActionMenu],
  data() {
    return {
      fold: false,
      // 弹窗title映射
      titleMapping: {
        'deleteExam': '删除考试',
        'publicExam': '发布',
        'privateExam': '取消发布',
        'openEnv': '开启环境',
        'closeEnv': '关闭环境',
        'snapshot': '创建快照'
      },
      userId: '',
      dialogLoading: false,
      confirmDisabled: false
    }
  },
  computed: {
    isAdmin() {
      return this.$store.state.user.userInfo.roleId == '180162'
    }
  },
  mounted() {
    this.userId = this.$store.state.user.userInfo.userId
  },
  methods: {
    handleFold() {
      this.fold = !this.fold
      this.$emit('call', 'fold', this.fold)
    },
    confirmCall: function(type, data) {
      if (type === 'close') {
        this.modalClose()
      } else if (type === 'refresh') {
        this.$emit('call', type)
      } else if (type === 'confirmDisabled') {
        this.confirmDisabled = data
      } else if (type === 'setLoading') {
        this.dialogLoading = data
      }
    },
    handleExam(name) {
      if (this.selectItem[0].examStatus == 'END' && name == 'editExam') {
        this.$message.warning(`${this.selectItem[0].name}考试已结束，不可修改`)
        return
      }
      this.$router.push({
        name: name,
        query: {
          examId: this.selectItem[0].id,
          publicExam: this.selectItem[0].published
        }
      })
    },
    'clickDrop': function(name) {
      if (name === 'editExam' || name === 'cloneExam') {
        this.handleExam(name)
        return
      }
      this.modalName = name
    }
  }
}
</script>
