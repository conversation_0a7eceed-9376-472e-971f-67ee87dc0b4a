// key 字段跟权限挂钩，auth接口返回的mange权限里面如果包含key对应的权限才展示对应菜单
// alwaysShow为true时，不走权限，一直显示对应菜单
// path字段对应路由表中的path，通过此字段去查找对应子路由
export const supervisorConfig = [ // 检测主管
  {
    key: 'app',
    title: '业务系统',
    subs: [
      { key: 'match', path: '/manage/match', title: '竞赛系统', icon: 'competition' },
      { key: 'training', path: '/manage/training', title: '实训系统', icon: 'practical-training' },
      { key: 'penetration', path: '/penetrant', title: '渗透测试管控系统', icon: 'permeate' },
      { key: 'simulation', path: '/simulation/training', title: '攻防演练系统', icon: 'drill' },
      { key: 'recurrent', path: '/recurrent', title: '漏洞复现系统', icon: 'recurrent' },
      { key: 'testing', path: '/testing', title: '检测管理系统', icon: 'detection' },
      { key: 'exam', path: '/manage/exam', title: '考试中心', icon: 'exam' },
      { key: 'analysis', path: '/analysis', title: '数据分析', icon: 'analysis' },
      { key: 'scene', path: '/scene', title: '场景管理', icon: 'scene', alwaysShow: true },
      { key: 'nfvo', path: '/os_network', title: '靶场底座', icon: 'simulation', alwaysShow: true }
    ]
  }
]
export const manufacturerConfig = [ // 检测厂商
  {
    key: 'app',
    title: '业务系统',
    subs: [
      { key: 'match', path: '/manage/match', title: '竞赛系统', icon: 'competition' },
      { key: 'training', path: '/manage/training', title: '实训系统', icon: 'practical-training' },
      { key: 'penetration', path: '/penetrant', title: '渗透测试管控系统', icon: 'permeate' },
      { key: 'simulation', path: '/simulation/training', title: '攻防演练系统', icon: 'drill' },
      { key: 'recurrent', path: '/recurrent', title: '漏洞复现系统', icon: 'recurrent' },
      { key: 'testing', path: '/testing', title: '检测管理系统', icon: 'detection' },
      { key: 'exam', path: '/manage/exam', title: '考试中心', icon: 'exam' },
      { key: 'analysis', path: '/analysis', title: '数据分析', icon: 'analysis' },
      { key: 'scene', path: '/scene', title: '场景管理', icon: 'scene' },
      { key: 'nfvo', path: '/os_network', title: '靶场底座', icon: 'simulation' }
    ]
  }
]
export const personConfig = [ // 检测人员
  {
    key: 'app',
    title: '业务系统',
    subs: [
      { key: 'match', path: '/manage/match', title: '竞赛系统', icon: 'competition' },
      { key: 'training', path: '/manage/training', title: '实训系统', icon: 'practical-training' },
      { key: 'penetration', path: '/penetrant', title: '渗透测试管控系统', icon: 'permeate' },
      { key: 'simulation', path: '/simulation/training', title: '攻防演练系统', icon: 'drill' },
      { key: 'recurrent', path: '/recurrent', title: '漏洞复现系统', icon: 'recurrent' },
      { key: 'testing', path: '/testing', title: '检测管理系统', icon: 'detection' },
      { key: 'exam', path: '/manage/exam', title: '考试中心', icon: 'exam' },
      { key: 'analysis', path: '/analysis', title: '数据分析', icon: 'analysis' },
      { key: 'scene', path: '/scene', title: '场景管理', icon: 'scene' },
      { key: 'nfvo', path: '/os_network', title: '靶场底座', icon: 'simulation' }
    ]
  }
]
export const projectHeadConfig = [ // 检测项目负责人
  {
    key: 'app',
    title: '业务系统',
    subs: [
      { key: 'match', path: '/manage/match', title: '竞赛系统', icon: 'competition' },
      { key: 'training', path: '/manage/training', title: '实训系统', icon: 'practical-training' },
      { key: 'penetration', path: '/penetrant', title: '渗透测试管控系统', icon: 'permeate' },
      { key: 'simulation', path: '/simulation/training', title: '攻防演练系统', icon: 'drill' },
      { key: 'recurrent', path: '/recurrent', title: '漏洞复现系统', icon: 'recurrent' },
      { key: 'testing', path: '/testing', title: '检测管理系统', icon: 'detection' },
      { key: 'exam', path: '/manage/exam', title: '考试中心', icon: 'exam' },
      { key: 'analysis', path: '/analysis', title: '数据分析', icon: 'analysis' },
      { key: 'scene', path: '/scene', title: '场景管理', icon: 'scene' },
      { key: 'nfvo', path: '/os_network', title: '靶场底座', icon: 'simulation' }
    ]
  }
]
