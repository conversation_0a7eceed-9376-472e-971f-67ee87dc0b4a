<template>
  <el-form
    ref="form"
    :model="formData"
    :rules="rules"
    label-position="left"
    label-width="100px"
  >
    <el-card class="mb-10">
      <el-form-item label="直播地址" prop="attachmentUrl">
        <el-input v-model.trim="formData.attachmentUrl" clearable style="width: 280px" @change="handleFormChange"/>
      </el-form-item>
    </el-card>
  </el-form>
</template>

<script>
import mixin from './mixin'
export default {
  mixins: [mixin],
  props: {
    liveData: {
      type: Object,
      default: () => ({
        attachmentUrl: ''
      })
    }
  },
  data() {
    return {
      formData: {
        attachmentUrl: ''
      },
      rules: {
        attachmentUrl: [
          {
            message: '请输入正确url地址',
            type: 'url',
            trigger: 'change'
          }
        ]
      }
    }
  },
  created() {
    // 回显表单
    for (const key in this.formData) {
      this.formData[key] = this.liveData[key]
    }
  },
  methods: {
    handleFormChange() {
      this.$emit('call', 'live-data-change', this.formData)
    },
    confirm(callback) {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          callback(this.formData)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
