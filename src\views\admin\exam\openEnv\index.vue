<template>
  <div class="experimental-content">
    <!-- 折叠 -->
    <div v-if="!fold" :style="{'left': `calc(${percent}% - 10px)`}" class="fold-wrap" @click="handleFold(true)">
      <i class="el-icon-caret-left" />
    </div>
    <!-- 展开 -->
    <div v-if="fold" :style="{'left': `calc(${percent}% - 10px)`}" class="fold-wrap" @click="handleFold(false)">
      <i class="el-icon-caret-right" />
    </div>
    <split-pane ref="split-pane" :min-percent="minPercent" :default-percent="percent" split="vertical" @resize="resize">
      <div slot="paneL" class="content">
        <div class="button">
          <el-button style="margin-bottom: 10px;" type="primary" icon="el-icon-arrow-left" @click="paramsBack">返回</el-button>
        </div>
        <div v-if="synthesisArr.length > 0" style="display: flex;justify-content: flex-end;" class="button">
          <el-button size="mini" type="primary" style="margin-right: 8px;" @click="scoringScoring">正确</el-button>
          <el-button size="mini" type="primary" @click="scoringDanger">错误</el-button>
        </div>
        <!-- 非组合题题型 -->
        <div v-if="shortAnswerArr.length > 0" class="mb-15">
          <div class="question_types">{{ getQuestionTypeTitle(questionType) }}</div>
          <div v-for="(item, index) in shortAnswerArr" :key="`synthesis${index}`" :id="`qt_synthesis${index}`" class="question_short">
            <div class="flex-space-between">
              <div class="comp-question">
                <div>{{ index + 1 }}、&nbsp;</div><div style="max-width: 90%;"><span v-html="item.content"/>&nbsp;</div><span>{{ `(${item.questionScore}分)` }}</span>
              </div>
            </div>
          </div>
        </div>
        <!-- 组合题 -->
        <div v-if="synthesisArr.length > 0" class="mb-15">
          <div class="question_types">综合题</div>
          <div class="question_synthesis">
            <div class="name_question"><span>{{ $route.query.questionName }}</span></div>
            <div v-for="(item, index) in synthesisArr" :key="`synthesis${index}`" :id="`qt_synthesis${index}`" class="question_synthesisArr">
              <div v-for="(q, idx) in item.combinationQuestionBOS" :key="idx">
                <!-- <div class="name_question"><span>{{ item.questionName }}</span></div> -->
                <div style="display: flex;">
                  <div class="comp-question">
                    <div>综合题{{ idx + 1 }}.</div>&nbsp;<div style="max-width: 80%;"><div style="overflow: auto;" v-html="q.questionName"/></div>&nbsp;<span>{{ `(${q.questionScore}分)` }}</span>
                  </div>
                  <div style="min-width: 9%; text-align: right;">
                    <div>得分:</div>
                    <div>
                      <span v-if="q.questionStemScore" style="color: #46ad32;">{{ q.questionStemScore }}分</span>
                      <span v-else style="color: #ff2424;">{{ q.questionStemScore }}分</span>
                    </div>
                  </div>
                </div>
                <div v-for="(sub, subIndex) in JSON.parse(q.content)" :key="subIndex" class="comp-content-wrap">
                  <div style="max-width: 93.5%"><el-checkbox @change="boxChange(q,subIndex,q.scoreArr[subIndex],$event)"/>
                    &nbsp;{{ subIndex + 1 }}.&nbsp;<div style="max-width: 78%;"><div style="overflow: auto;" v-html="sub"/></div>&nbsp;
                    <span>{{ `(${q.scoreArr[subIndex]}分)` }}</span>
                    <div style="position: absolute;right: 5px;">
                      <div>得分:</div>
                      <div>
                        <div v-if="q.soreListBOS[subIndex]" style="color: #46ad32;text-align: right;">{{ q.soreListBOS[subIndex] }}分</div>
                        <div v-else style="color: #ff2424;text-align: right">{{ q.soreListBOS[subIndex] }}分</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div slot="paneR" class="paneR">
        <Topo v-if="topologyId" :topo-id="topologyId" topo-type="examinationPermissions"/>
      </div>
    </split-pane>
  </div>
</template>

<script>
import splitPane from 'vue-splitpane'
import Topo from '@/packages/topo/index'

export default {
  name: 'OpenEnv',
  components: {
    splitPane,
    Topo
  },
  mixins: [],
  data() {
    return {
      fold: false, // 是否折叠
      minPercent: 0, // 题目区域的最小宽度
      percent: 40, // 题目区域的宽度
      topologyId: '',
      synthesisArr: [],
      checkData: {},
      watchValue: false,
      chooseQuestionData: [],
      shortAnswerArr: [],
      questionType: '',
      questionTypes: {
        '1': '单选题',
        '2': '多选题',
        '3': '判断题',
        '4': 'CTF题',
        '5': 'AWD题',
        '6': '其他题',
        '7': '填空题',
        '8': '简答题',
        '9': '漏洞题',
        '10': '组合题'
      }
    }
  },
  watch: {
    // 解决页面渲染不出现分数的问题
    watchValue() {
      this.$forceUpdate()
    }
  },
  mounted() {
    this.minPercent = (10 / this.$refs['split-pane'].$el.offsetWidth) * 100
    this.topologyId = this.$route.query.topologyId
    this.questionType = this.$route.query.questionType
    if (this.questionType == '10') {
      this.init()
    } else {
      this.initShort()
    }
  },
  methods: {
    // 折叠面板时
    handleFold(flag) {
      this.fold = flag
      if (flag) {
        this.percent = this.minPercent
      } else {
        this.percent = 40
      }
    },
    // 拖拽面板时
    resize(val) {
      this.percent = val
    },
    getQuestionTypeTitle(type) {
      return this.questionTypes[type] || '未知题型'
    },
    initShort() {
      this.shortAnswerArr = [this.$route.query]
    },
    init() {
      this.synthesisArr = [this.$route.query] // 试卷详情
      const scoreData = JSON.parse(this.$route.query.combinationPoints) // 题目分数
      let soreListBOS = []
      // 老师可以重新打分，优先回显老师打的分数，combinationUserPoints是之前打的分数
      if (JSON.parse(this.$route.query.combinationUserPoints) || JSON.parse(localStorage.getItem('soreList' + this.$route.query.questionCode))) {
        soreListBOS = JSON.parse(localStorage.getItem('soreList' + this.$route.query.questionCode)) || JSON.parse(this.$route.query.combinationUserPoints)
      } else {
        // 把二位数组置为零，和试卷详情,题目分数同结构,方便处理提交参数
        const scoreUrlData = JSON.parse(this.$route.query.combinationPoints)
        soreListBOS = this.replaceTwoDimensionalArrayWithZero(scoreUrlData)
      }
      this.synthesisArr[0].combinationQuestionBOS.map((questionItem, questionIndex) => {
        questionItem.questionScore = scoreData[questionIndex].map(each => Number(each)).reduce((p, q) => p + q) // 主题干总分
        questionItem.scoreArr = scoreData[questionIndex] // 每个子题干的分数
        questionItem.soreListBOS = soreListBOS[questionIndex] // // 老师给每个子题干打的分数
        questionItem.questionStemScore = questionItem.soreListBOS.map(each => Number(each)).reduce((p, q) => p + q) // 老师打每个子题干的总分
      })
    },
    // 把二位数组置为零，和题目分数同结构
    replaceTwoDimensionalArrayWithZero(array) {
      for (let i = 0; i < array.length; i++) {
        for (let j = 0; j < array[i].length; j++) {
          array[i][j] = 0
        }
      }
      return array
    },
    paramsBack() {
      if (this.questionType == '10') {
        const soreList = []
        this.synthesisArr[0].combinationQuestionBOS.map((questionItem, questionIndex) => {
          soreList.push(questionItem.soreListBOS)
        })
        // 把老师打的分数放缓存，方便上一个路由页面取出来当参数
        localStorage.setItem('soreList' + this.synthesisArr[0].questionCode, JSON.stringify(soreList))
      }

      this.$router.push({
        name: 'examReview',
        query: {
          examId: this.$route.query.examId,
          examStatus: this.$route.query.examStatus,
          examCode: this.$route.query.examCode,
          evaluationCode: this.$route.query.evaluationCode,
          examinationId: this.$route.query.examinationId,
          name: this.$route.query.name,
          submitType: '1',
          oneLevelTitle: '考试管理',
          oneLevelName: 'ca-exam',
          twoLevelTitle: this.$route.query.twoLevelTitle
        }
      })
    },
    // 点击复选框
    boxChange(q, i, s, e) {
      if (e) {
        this.checkData = {
          id: q.id,
          index: i,
          sore: s,
          isCheck: e
        }
        this.chooseQuestionData.push(this.checkData)
      } else {
        this.chooseQuestionData = this.chooseQuestionData.filter((item) => !(item.id === q.id && item.index === i))
      }
    },
    scoringScoring() {
      // 如果是打勾的话
      this.watchValue = !this.watchValue
      this.synthesisArr[0].combinationQuestionBOS.map((questionItem, questionIndex) => {
        this.chooseQuestionData.forEach(chooseItem => {
          if (chooseItem.id == questionItem.id) {
            questionItem.soreListBOS.splice(chooseItem.index, 1, chooseItem.sore)
            questionItem.questionStemScore = questionItem.soreListBOS.map(each => Number(each)).reduce((p, q) => p + q)
          }
        })
      })
    },
    scoringDanger() {
      this.watchValue = !this.watchValue
      this.synthesisArr[0].combinationQuestionBOS.map((questionItem, questionIndex) => {
        // 拿到对应题目数据的索引，回显到页面
        this.chooseQuestionData.forEach(chooseItem => {
          if (chooseItem.id == questionItem.id) {
            questionItem.soreListBOS.splice(chooseItem.index, 1, 0)
            questionItem.questionStemScore = questionItem.soreListBOS.map(each => Number(each)).reduce((p, q) => p + q)
          }
        })
      })
    }
  }
}
</script>

<style scoped lang="scss">
.fold-wrap {
  position: absolute;
  top: calc(50% - 80px);
  width: 10px;
  height: 60px;
  line-height: 60px;;
  background-color: var(--color-600);
  overflow: hidden;
  cursor: pointer;
  z-index: 999;
  i {
    color: #fff;
    margin-left: -2px;
  }
}
.question_short {
  padding: 10px;
  font-size: 14px;
  border: 1px solid #d1d1d1;
  border-radius: 10px;
  box-shadow: 1px 1px 1px rgba(0, 0, 0, 0.1);
}
.question_synthesis {
  padding: 10px;
  font-size: 14px;
  border: 1px solid #d1d1d1;
  border-radius: 10px;
  box-shadow: 1px 1px 1px rgba(0, 0, 0, 0.1);
}
.question_synthesisArr {
  padding-left: 10px;
  padding-right: 10px;
  padding-top: 10px;
  font-size: 14px;
  border: 1px solid #d1d1d1;
  border-radius: 10px;
  box-shadow: 1px 1px 1px rgba(0, 0, 0, 0.1);
}
::v-deep {
  .orchestration-create-warp {
    height: 100%;
  }
}
.name_question {
  font-weight: 700;
  font-size: 15px;
}
.question_types {
  font-size: 16px;
  font-weight: 600;
  font-family: Source Han Sans CN;
}
.comp-question {
  display: flex;
  max-height: 200px;
  overflow-y: auto;
  font-weight: 600;
  font-size: 14px;
  color: rgb(36, 41, 47);
  margin-bottom: 10px;
  width: 226%;
  >span {
    word-break: break-all;
  }
}
.comp-content-wrap {
  border-radius: 10px;
  border: 1px solid #e5e6eb;
  margin: 5px 0 10px;
  padding: 10px 20px;
  position: relative;
  >div:first-child {
    display: flex;
    max-height: 200px;
    overflow-y: auto;
    margin-bottom: 10px;
    >span {
      word-break: break-all;
    }
  }
}
.experimental-content {
  position: relative;
  height: 100%;
  display: flex;
  justify-content: flex-start;
  .content {
    width: 100%;
    height: 100%;
    overflow: auto;
    padding: 10px 0 10px 10px;
  }
}
</style>
