<template>
  <div v-loading="loading" class="dialog-wrap">
    <el-form ref="form" :model="ruleForm">
      <div ref="tableDom" class="tableData">
        <el-table :data="ruleForm.tableData">
          <el-table-column label="上课日期" min-width="140">
            <template slot-scope="scope">
              <el-form-item :prop="'tableData.' + scope.$index + '.sectionTime'" :rules="ruleForm.rules.sectionTime" label-width="0px">
                <el-date-picker
                  v-model="scope.row.sectionTime"
                  :picker-options="pickerOptions"
                  type="date"
                  align="center"
                  size="mini"
                  prefix-icon="-"
                  value-format="yyyy-MM-dd"
                  placeholder="上课日期"
                  style="width: 100%"
                  @change="sectionTimeChange($event, scope.row)"
                />
                <div class="el-form-item__error placeholder__error">&nbsp;</div>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column label="上课开始时间" width="100">
            <template slot-scope="scope">
              <el-form-item :prop="'tableData.' + scope.$index + '.startTime'" :rules="ruleForm.rules.startTime" label-width="0px">
                <el-time-picker
                  v-model="scope.row.startTime"
                  :disabled="!scope.row.sectionTime"
                  :append-to-body="false"
                  :picker-options="{
                    selectableRange: `${disabledStartMinTime(scope.row)}:00 - 23:59:00`
                  }"
                  format="HH:mm"
                  value-format="HH:mm"
                  editable
                  size="mini"
                  prefix-icon="-"
                  style="width: 100%"
                  placeholder="开始时间"
                  @change="startTimeChange($event, scope.row, scope.$index)"/>
                <div class="el-form-item__error placeholder__error">&nbsp;</div>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column label="上课结束时间" width="100">
            <template slot-scope="scope">
              <el-form-item :prop="'tableData.' + scope.$index + '.endTime'" :rules="ruleForm.rules.endTime" label-width="0px">
                <el-time-picker
                  v-model="scope.row.endTime"
                  :disabled="!scope.row.startTime"
                  :append-to-body="false"
                  :picker-options="{
                    selectableRange: `${scope.row.startTime || '00:00'}:00 - 23:59:00`
                  }"
                  format="HH:mm"
                  value-format="HH:mm"
                  editable
                  size="mini"
                  prefix-icon="-"
                  style="width: 100%"
                  placeholder="结束时间"/>
                <div class="el-form-item__error placeholder__error">&nbsp;</div>
              </el-form-item>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-form>
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>

<script>
import modules from '../../config'
import moment from 'moment'
import modalMixins from '@/packages/mixins/modal_form'
import validate from '@/packages/validate'
import { updateTeacherSchedulingTimeAPI } from '@/api/teachingAffairs/index.js'

export default {
  components: {
  },
  mixins: [modalMixins],
  props: {
    data: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      conf: modules,
      loading: false,
      validate: validate,
      ruleForm: {
        sectionTime: '',
        sectionSeason: '',
        schedulingCode: '',
        courseCode: '',
        classCode: '',
        curriculumCode: '',
        tableData: [],
        rules: {
          sectionTime: [validate.required(['blur'])],
          startTime: [validate.required(['blur'])],
          endTime: [validate.required(['blur']), { validator: this.endTimeValid, trigger: 'change' }]
        }
      },
      sectionSeason: this.data[0].sectionSeason,
      sectionTime: this.data[0].sectionTime,
      startTime: this.data[0].sectionSeason.split('-')[0],
      endTime: this.data[0].sectionSeason.split('-')[1],
      curriculumCode: this.data[0].resultList[0].curriculumCode,
      schedulingCode: this.data[0].resultList[0].schedulingCode,
      classCode: this.data[0].resultList[0].classCode,
      timeStep: '00:01', // 时刻间隔
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() <= (Date.now() - 24 * 60 * 60 * 1000)
        }
      }
    }
  },
  mounted() {
    this.ruleForm.sectionTime = this.sectionTime
    this.ruleForm.sectionSeason = this.sectionSeason
    this.ruleForm.tableData.push({
      sectionTime: this.sectionTime,
      startTime: this.startTime,
      endTime: this.endTime
    })
  },
  methods: {
    endTimeValid(rule, value, callback) {
      const endTimeStamp = new Date(this.ruleForm.tableData[0].sectionTime + ' ' + this.ruleForm.tableData[0].endTime).getTime()
      const startTimeStamp = new Date(this.ruleForm.tableData[0].sectionTime + ' ' + this.ruleForm.tableData[0].startTime).getTime()
      if (value === '') {
        callback(new Error('必填项'))
      } else if (startTimeStamp > endTimeStamp) {
        callback(new Error('请合理安排时间'))
      } else {
        callback()
      }
    },
    // 上课日期改变
    sectionTimeChange(date, row) {
      // 清空日期时，上课的起始时间应也被清空
      if (!date) {
        this.$set(row, 'startTime', null)
        this.$set(row, 'endTime', null)
      } else {
        const time = row.startTime || row.endTime
        if (date && time) {
          const selectTime = this.getFullDateTime(date, time)
          const isBefore = moment(selectTime).isBefore(new Date())
          // 选中的上课时间在当前时间之前时，清空开始时间、结束时间，重新选择
          if (isBefore) {
            this.$set(row, 'startTime', null)
            this.$set(row, 'endTime', null)
          }
        }
      }
    },
    // 禁用开始时间选项
    disabledStartMinTime(row) {
      if (!row.sectionTime) {
        return '00:00' // 未选择日期时，返回00:00，避免时间选择组件报错
      }
      const isAfterStart = moment(row.sectionTime).isAfter(new Date(), 'days')
      if (isAfterStart) {
        return '00:00'
      } else {
        const { hours, minutes } = moment(new Date()).toObject()
        const timeStr = String(hours).padStart(0, '2') + ':' + String(minutes).padStart(0, '2')
        return timeStr
      }
    },
    // 禁用开始时间-最大时间选项
    disabledStartMaxTime(row) {
      if (row.endTime) {
        return row.endTime
      } else {
        return null
      }
    },
    // 开始时间改变回调
    startTimeChange(startTime, row, index) {
      // 开始时间清空，同时也清空结束时间
      if (!startTime && row.endTime) {
        this.$set(row, 'endTime', null)
      } else if (row.sectionTime && startTime && row.endTime) {
        const start = this.getFullDateTime(row.sectionTime, startTime)
        const end = this.getFullDateTime(row.sectionTime, row.endTime)
        if (start >= end) {
          if (row.endTime) {
            this.$set(row, 'endTime', null)
          }
        }
      }
    },
    // 获取上课完整时间 YYYYY-MM-DD HH:mm:ss
    getFullDateTime(date, time) {
      return `${date} ${time}:00`
    },
    updateTeacherSchedulingTime() {
      this.ruleForm.courseCode = this.courseCode
      this.ruleForm.curriculumCode = this.curriculumCode
      this.ruleForm.schedulingCodeAndClassCodeBos = this.data[0].resultList
      this.ruleForm.sectionSeason = this.ruleForm.tableData[0]['startTime'] + '-' + this.ruleForm.tableData[0]['endTime']
      this.ruleForm.sectionTime = this.ruleForm.tableData[0]['sectionTime']
      updateTeacherSchedulingTimeAPI(this.ruleForm).then(res => {
        this.$message({
          type: 'success',
          message: '修改成功!'
        })
        this.close()
        this.$emit('call', 'refresh')
      })
    },
    close() {
      this.$emit('close')
    },
    confirm: function() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.updateTeacherSchedulingTime()
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-wrap {
  .el-form {
    padding: 0;
    /deep/ .el-input .el-input__inner {
      padding: 0 10px;
    }
    /deep/ .el-form-item {
      margin-bottom: 0;
      &.is-error {
        .el-form-item__error {
          &.placeholder__error {
            display: none;
          }
        }
      }
      .time-select-item.disabled {
        display: none;
      }
    }
  }
}
</style>
