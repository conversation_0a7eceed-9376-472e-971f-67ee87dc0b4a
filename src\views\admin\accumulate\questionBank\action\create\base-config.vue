<template>
  <el-form v-loading="loading" ref="form" :model="formData" :rules="rules" :disabled="!canOpt" class="base-config-wrap" label-position="left" label-width="130px">
    <el-scrollbar>
      <el-card class="mb-10">
        <el-form-item label="名称" prop="questionName">
          <el-input v-model.trim="formData.questionName" style="width: 320px;"/>
        </el-form-item>
        <el-form-item label="题库" prop="questionDepotId">
          <el-select v-model="formData.questionDepotId" filterable style="width: 320px;">
            <el-option
              v-for="item in questionCategoryList"
              :key="item.id"
              :label="item.questionDepotName"
              :value="+item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="分类" prop="categoryId">
          <el-select v-model="formData.categoryId" filterable style="width: 320px;">
            <el-option
              v-for="item in categoryList"
              :key="item.id"
              :label="item.categoryName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="难度" prop="complexity">
          <el-radio
            v-for="item in questionConf.complexityArr"
            :key="item.value"
            v-model="formData.complexity"
            :label="item.value"
          >{{ item.label }}</el-radio>
        </el-form-item>
        <el-form-item label="用途" prop="uses">
          <el-select v-model="formData.uses" multiple clearable style="width: 320px;">
            <el-option
              v-for="item in questionConf.usesArr"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <!-- 实训知识点code -->
        <el-form-item prop="knowledgeCode">
          <span slot="label">
            <span>知识点</span>
            <el-tooltip transfer>
              <i class="el-icon-warning-outline" />
              <div slot="content">
                如果没有符合的知识点，可以前往
                <span style="color: var(--color-600); cursor: pointer;" @click="goToKnowledgePage">创建知识点</span>
              </div>
            </el-tooltip>
          </span>
          <el-select v-model="formData.knowledgeCode" multiple filterable clearable style="width: 320px;">
            <el-option
              v-for="item in trainingPointList"
              :key="item.knowledgeCode"
              :label="item.knowledgeName"
              :value="item.knowledgeCode"
            />
          </el-select>
        </el-form-item>
        <el-form-item prop="skillPointsBOS" class="skill-points-BOS">
          <span slot="label">
            <span>技能点</span>
            <el-tooltip transfer>
              <i class="el-icon-warning-outline" />
              <div slot="content">本题答对时，答题者将获得对应的能力值。</div>
            </el-tooltip>
          </span>
          <div class="skill-points-BOS-title">
            <div style="width: 220px;">名称</div>
            <div style="margin-left: 20px">能力值</div>
          </div>
          <div v-for="(skill, skillIndex) in formData.skillPointsBOS" :key="skillIndex" class="skill-points-BOS-content">
            <el-form-item :prop="`skillPointsBOS[${skillIndex}][id]`" :rules="[validate.required()]">
              <el-select v-model="skill.id" filterable clearable style="width: 220px;">
                <el-option
                  v-for="item in skillPointList"
                  :key="item.id"
                  :disabled="formData.skillPointsBOS.filter(i => i.id == item.id).length ? true : false"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item :prop="`skillPointsBOS[${skillIndex}][abilityValue]`" :rules="[validate.required()]" style="margin-left: 20px">
              <el-input-number v-model="skill.abilityValue" :max="5" :min="1" style="width: 150px;"/>
            </el-form-item>
            <i v-if="skillIndex !== 0" class="el-icon-remove-outline" @click="deleteSkillPoints(skillIndex)"/>
          </div>
          <el-button icon="el-icon-plus" @click="addSkillPoints()">添加</el-button>
        </el-form-item>
      </el-card>
      <el-card class="mb-10">
        <el-form-item label="题型" prop="questionType">
          <el-radio-group v-model="formData.questionType" :disabled="editMode || !!baseData" size="small" @input="changeQuestionType">
            <el-radio-button
              v-for="item in questionTypeArr"
              :key="item.value"
              :label="item.value"
            >{{ item.label }}</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <div v-if="formData.questionType != 10">
          <el-form-item label="题干" prop="content">
            <myEditor
              :key="contentTimer"
              :content="formData.content"
              :only-editor="!canOpt"
              :is-read-only="!canOpt"
              id-prefix="content"
              width="100%"
              height="200px"
              @contentChange="contentChange('content', $event)"
            />
          </el-form-item>
          <!-- 单选、多选 -->
          <el-form-item v-if="formData.questionType == 1 || formData.questionType == 2" label="选项" prop="questionOptions" class="question-options-form">
            <div v-for="(option, index) in formData.questionOptions" :key="index">
              <el-form-item
                :prop="'questionOptions.' + index + '.name'"
                :rules="[
                  validate.required(),
                  { min: 1, max: 255, message: '1-255个字符', trigger: 'blur' },
                  { validator: repeatOptValidate, trigger: 'blur' },
                ]"
              >
                <el-input v-model.trim="option.name" :placeholder="`选项${radioMap[index]}`" style="width: 320px;" />
                <!-- 单选 -->
                <el-radio-group
                  v-if="formData.questionType == 1"
                  v-model="formData.questionAnswer"
                  class="question-answer"
                  @input="showErrMsg = false"
                >
                  <el-radio :label="radioMap[index]">正确答案</el-radio>
                </el-radio-group>
                <!-- 多选 -->
                <el-checkbox
                  v-else-if="formData.questionType == 2"
                  v-model="option.questionAnswer"
                  class="question-answer"
                  @change="showErrMsg = formData.questionOptions.filter(item => item.questionAnswer).length < 2"
                >正确答案</el-checkbox>
                <a v-if="index > 1" href="javascript:;" class="option-delete" @click="optionDelete(index)">
                  <i class="el-icon-delete" />
                </a>
              </el-form-item>
            </div>
            <div v-if="showErrMsg" class="error-message">{{ errMsgText }}</div>
            <el-button class="option-add" icon="el-icon-plus" @click="optionAdd()">添加选项</el-button>
          </el-form-item>
          <!-- 判断题 -->
          <el-form-item v-else-if="formData.questionType == 3" label="答案" prop="trueOrFalse">
            <el-radio v-model="formData.trueOrFalse" :label="true">正确</el-radio>
            <el-radio v-model="formData.trueOrFalse" :label="false">错误</el-radio>
          </el-form-item>
          <!-- 简答题 -->
          <el-form-item v-else-if="formData.questionType == 8" label="答案" prop="textAreaAnswer">
            <el-input v-model.trim="formData.textAreaAnswer" type="textarea" />
          </el-form-item>
          <!-- CTF题、AWD、其他、填空题、漏洞题 -->
          <!-- 理论的展示“答案”，靶机题的展示“flag” -->
          <el-form-item v-else :label="formData.bankType == 2 ? 'flag' : '答案'" prop="inputAnswer">
            <el-input v-model.trim="formData.inputAnswer" style="width: 320px;"/>
          </el-form-item>
          <!-- CTF题、AWD、其他、漏洞题 -->
          <el-form-item v-if="formData.questionType == 4 || formData.questionType == 5 || formData.questionType == 6 || formData.questionType == 9" label="附件" prop="fileUrl">
            <div class="tpl-upload">
              <div class="upload-select">
                <input ref="inputFile" type="file" class="upload-select-input" @change="handleFileChange($event, 'fileUrl')">
                <el-button class="upload-click-wrap" @click="handleUploadClick('fileUrl')">上传附件</el-button>
                <div v-show="formData.fileName" :style="{ 'border-color': formData.fileError ? '#F56C6C' : '#abdcff', 'background-color': formData.fileError ? '#fff6f4' : '#f0faff' }" class="file-container">
                  <i :style="{ 'color': formData.fileError ? '#F56C6C' : '#2d8cf0' }" class="el-icon-document" size="16" />
                  <span :title="formData.fileName" style="margin: 10px 10px 0 0;">{{ formData.fileName }}</span>
                  <i v-if="canOpt" :style="{ 'color': formData.fileError ? '#F56C6C' : '#2d8cf0' }" class="el-icon-delete delete-icon delete" @click.stop="handleFileDelete('fileUrl')" />
                </div>
              </div>
            </div>
          </el-form-item>
          <el-form-item label="人工判分" prop="manualScoring">
            <el-radio
              v-for="item in questionConf.manualScoringArr"
              :key="item.value"
              v-model="formData.manualScoring"
              :label="item.value"
            >{{ item.label }}</el-radio>
          </el-form-item>
          <el-form-item label="上传解题方法" prop="solutionApproach">
            <el-radio
              v-for="item in questionConf.solutionApproachArr"
              :key="item.value"
              v-model="formData.solutionApproach"
              :label="item.value"
            >{{ item.label }}</el-radio>
          </el-form-item>
          <el-form-item label="题目解析" prop="questionAnalysis">
            <myEditor
              :key="questionAnalysisTimer"
              :content="formData.questionAnalysis"
              :only-editor="!canOpt"
              :is-read-only="!canOpt"
              id-prefix="questionAnalysis"
              width="100%"
              height="200px"
              @contentChange="contentChange('questionAnalysis', $event)"
            />
          </el-form-item>
        </div>
      </el-card>
      <div v-if="formData.questionType == 10">
        <el-card v-for="(comp, index) in formData.combinationQuestionBOS" :key="index" class="mb-10">
          <!-- 综合题N -->
          <el-form-item
            :prop="`combinationQuestionBOS.${index}.questionName`"
            :rules="[ validate.required('change') ]"
            :label="`综合题${index + 1}`"
          >
            <span slot="label">
              <i
                :class="index > 0 ? 'el-icon-remove' : 'el-icon-circle-plus'"
                style="color: var(--color-600); margin-right: 5px; cursor: pointer;"
                @click="index > 0 ? deleteComp(index) : addComp()"
              />{{ `综合题${index + 1}` }}
            </span>
            <myEditor
              :key="comp.questionTimer"
              :content="comp.questionName"
              :only-editor="!canOpt"
              :is-read-only="!canOpt"
              :id-prefix="`question${index}`"
              width="97%"
              height="200px"
              @contentChange="compChange('questionName', index, $event)"
            />
          </el-form-item>
          <!-- 题目N -->
          <el-form-item
            v-for="(con, subIndex) in comp.content"
            :key="subIndex"
            :prop="`combinationQuestionBOS.${index}.content.${subIndex}.contentName`"
            :rules="[ validate.required('change') ]"
            :label="`题目${subIndex + 1}`"
            style="margin-bottom: 0px;"
          >
            <myEditor
              :key="con.contentTimer"
              :content="con.contentName"
              :only-editor="!canOpt"
              :is-read-only="!canOpt"
              :id-prefix="`content${index}${subIndex}`"
              width="97%"
              height="200px"
              style="display: inline-block;"
              @contentChange="compContentChange(index, subIndex, $event)"
            />
            <i v-if="subIndex > 0" class="el-icon-delete" style="font-size: 18px; cursor: pointer;" @click.stop="deleteCompContent(index, subIndex)" />
          </el-form-item>
          <el-button icon="el-icon-plus" style="margin: 0px 0px 20px 130px;" @click="addCompContent(index)">添加题目</el-button>
          <el-form-item label="人工判分" prop="manualScoring">
            <el-radio
              v-for="item in questionConf.manualScoringArr"
              :key="item.value"
              v-model="formData.manualScoring"
              :label="item.value"
            >{{ item.label }}</el-radio>
          </el-form-item>
          <el-form-item label="上传解题方法" prop="solutionApproach">
            <el-radio
              v-for="item in questionConf.solutionApproachArr"
              :key="item.value"
              v-model="formData.solutionApproach"
              :label="item.value"
            >{{ item.label }}</el-radio>
          </el-form-item>
          <el-form-item label="参考答案">
            <div class="tpl-upload">
              <div class="upload-select">
                <input :ref="`input${index}`" type="file" class="upload-select-input" @change="handleFileChange($event, 'combinationQuestionBOS', index)">
                <el-button class="upload-click-wrap" @click="handleUploadClick('combinationQuestionBOS', index)">上传附件</el-button>
                <div v-show="comp.fileName" :style="{ 'border-color': comp.error ? '#F56C6C' : '#abdcff', 'background-color': comp.error ? '#fff6f4' : '#f0faff' }" class="file-container">
                  <i :style="{ 'color': comp.error ? '#F56C6C' : '#2d8cf0' }" class="el-icon-document" size="16" />
                  <span :title="comp.fileName" style="margin: 10px 10px 0 0;">{{ comp.fileName }}</span>
                  <i :style="{ 'color': comp.error ? '#F56C6C' : '#2d8cf0' }" class="el-icon-delete delete-icon delete" @click.stop="handleFileDelete('combinationQuestionBOS', index)" />
                </div>
              </div>
            </div>
          </el-form-item>
          <el-form-item label="题目解析">
            <myEditor
              :key="comp.analysisTimer"
              :content="comp.questionAnalysis"
              :only-editor="!canOpt"
              :is-read-only="!canOpt"
              :id-prefix="`analysis${index}`"
              width="97%"
              height="200px"
              @contentChange="compChange('questionAnalysis', index, $event)"
            />
          </el-form-item>
        </el-card>
      </div>
      <!-- 仿真：拓扑是否可见 -->
      <el-card v-if="formData.bankType == 3" class="mb-10">
        <el-form-item v-if="!editMode" label="场景" prop="topologyMode">
          <el-radio-group v-model="formData.topologyMode">
            <el-radio :label="0">新建</el-radio>
            <el-radio :label="1" style="margin-left: 16px;">选择已有</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="!editMode && formData.topologyMode == 1" prop="topologyTemplateId">
          <el-tag
            v-if="formData.selectedScene"
            :disable-transitions="true"
            closable
            @click="drawerName = 'selectScene'"
            @close="formData.selectedScene = null">
            {{ formData.selectedScene.sceneName }}
          </el-tag>
          <el-button v-else type="ghost" @click="drawerName = 'selectScene'">选择场景</el-button>
        </el-form-item>
        <el-form-item label="拓扑是否可见" prop="topologyVisible">
          <!-- 0-不可见 1-可见 -->
          <el-switch
            v-model="formData.topologyVisible"
            :active-value="1"
            :inactive-value="0"
          />
          <span class="ml-5">关闭后，答题者将不可见拓扑</span>
        </el-form-item>
      </el-card>
      <!-- 靶机：相关表单 -->
      <el-card v-if="formData.bankType == 2" class="mb-10">
        <el-form-item label="靶机" prop="networkElementId">
          <el-tag
            v-if="formData.selectedNE"
            :disable-transitions="true"
            closable
            @click="drawerName = 'selectNE'"
            @close="formData.selectedNE = null">
            {{ formData.selectedNE.name }}
          </el-tag>
          <el-button v-else type="ghost" @click="drawerName = 'selectNE'">选择靶机设备</el-button>
          <div>
            <el-checkbox v-model="formData.targetType">作为跳板机</el-checkbox>
          </div>
        </el-form-item>
        <!-- flag路径 -->
        <el-form-item>
          <span slot="label">
            <span>flag路径</span>
            <el-tooltip transfer>
              <i class="el-icon-warning-outline" />
              <div slot="content">靶机中flag的放置位置，如/tmp/flag</div>
            </el-tooltip>
          </span>
          <el-input v-model.trim="formData.flagPath" style="width: 320px;"/>
        </el-form-item>
        <!-- 动态flag -->
        <el-form-item label="动态flag" prop="flagRefresh">
          <!-- 开启flag自动刷新 0-未开启 1-已开启 -->
          <el-switch
            v-model="formData.flagRefresh"
            :disabled="formData.selectedNE && formData.selectedNE.type === 'pnf'"
            :active-value="1"
            :inactive-value="0"
          />
        </el-form-item>
        <!-- 选手加固账号 -->
        <el-form-item>
          <span slot="label">
            <span>选手加固账号</span>
            <el-tooltip transfer>
              <i class="el-icon-warning-outline" />
              <div slot="content">选手登录靶机时使用的账号（用户名）</div>
            </el-tooltip>
          </span>
          <el-input v-model.trim="formData.playerAccount" style="width: 320px;"/>
        </el-form-item>
        <!-- 题目检测脚本 -->
        <el-form-item label="题目检测脚本" prop="checkFileUrl">
          <div class="tpl-upload">
            <div class="upload-select">
              <input ref="inputCheckFile" type="file" class="upload-select-input" @change="handleFileChange($event, 'checkFileUrl')">
              <el-button class="upload-click-wrap" @click="handleUploadClick('checkFileUrl')">上传附件</el-button>
              <div v-show="formData.checkFileName" :style="{ 'border-color': formData.checkFileError ? '#F56C6C' : '#abdcff', 'background-color': formData.checkFileError ? '#fff6f4' : '#f0faff' }" class="file-container">
                <i :style="{ 'color': formData.checkFileError ? '#F56C6C' : '#2d8cf0' }" class="el-icon-document" size="16" />
                <span :title="formData.checkFileName" style="margin: 10px 10px 0 0;">{{ formData.checkFileName }}</span>
                <i :style="{ 'color': formData.checkFileError ? '#F56C6C' : '#2d8cf0' }" class="el-icon-delete delete-icon delete" @click.stop="handleFileDelete('checkFileUrl')" />
              </div>
            </div>
          </div>
        </el-form-item>
        <!-- 题目更新脚本 -->
        <el-form-item label="题目更新脚本" prop="updateFileUrl">
          <div class="tpl-upload">
            <div class="upload-select">
              <input ref="inputUpdateFile" type="file" class="upload-select-input" @change="handleFileChange($event, 'updateFileUrl')">
              <el-button class="upload-click-wrap" @click="handleUploadClick('updateFileUrl')">上传附件</el-button>
              <div v-show="formData.updateFileName" :style="{ 'border-color': formData.updateFileError ? '#F56C6C' : '#abdcff', 'background-color': formData.updateFileError ? '#fff6f4' : '#f0faff' }" class="file-container">
                <i :style="{ 'color': formData.updateFileError ? '#F56C6C' : '#2d8cf0' }" class="el-icon-document" size="16" />
                <span :title="formData.updateFileName" style="margin: 10px 10px 0 0;">{{ formData.updateFileName }}</span>
                <i :style="{ 'color': formData.updateFileError ? '#F56C6C' : '#2d8cf0' }" class="el-icon-delete delete-icon delete" @click.stop="handleFileDelete('updateFileUrl')" />
              </div>
            </div>
          </div>
        </el-form-item>
        <!-- 靶机题-漏洞题：选择漏洞（单选） -->
        <el-form-item v-if="formData.questionType == '9'" label="题目漏洞内容" prop="selectedHole">
          <el-tag
            v-if="formData.selectedHole"
            :disable-transitions="true"
            closable
            @click="drawerName = 'selectHole'"
            @close="formData.selectedHole = null">
            {{ formData.selectedHole.bugName }}
          </el-tag>
          <el-button v-else type="ghost" @click="drawerName = 'selectHole'">选择漏洞</el-button>
        </el-form-item>
        <!-- 靶机题-非漏洞题：选择漏洞（多选） -->
        <el-form-item v-else label="题目漏洞内容" prop="selectedHoleMuti">
          <div v-if="formData.selectedHoleMuti.length">
            <el-tag
              v-for="(item, index) in formData.selectedHoleMuti"
              :key="index"
              :disable-transitions="true"
              closable
              class="mr-5"
              @close="formData.selectedHoleMuti.splice(index, 1)">
              {{ item.bugName }}
            </el-tag>
          </div>
          <el-button type="ghost" @click="drawerName = 'selectHoleMuti'">选择漏洞</el-button>
        </el-form-item>
        <el-form-item v-if="formData.questionType == 4 || formData.questionType == 9" label="漏洞端口" prop="bugPort">
          <el-input-number v-model="formData.bugPort" />
        </el-form-item>
      </el-card>
      <!-- 侧拉弹窗 start -->
      <el-drawer
        :title="titleMapping[drawerName]"
        :visible.sync="drawerShow"
        :size="drawerWidth"
        append-to-body
        @close="drawerClose"
      >
        <transition name="el-fade-in-linear">
          <component
            :is="drawerName"
            :name="drawerName"
            :question-type="formData.questionType"
            @close="drawerClose"
            @call="drawerConfirmCall"
          />
        </transition>
      </el-drawer>
      <!-- 侧拉弹窗 end -->
    </el-scrollbar>
  </el-form>
</template>
<script>
import questionConf from '../../config.js'
import createView from '@/packages/create-view/index'
import myEditor from '@/packages/editor/index.vue'
import validate from '@/packages/validate'
import { getCategory } from '@/api/accumulate/category'
import { getQuestionCategory } from '@/api/accumulate/questionLibrary'
import { getSkillPoint } from '@/api/accumulate/skillPoint'
import { getQuestionBankItem, getTrainingPoint, getNetworkElementItem, uploadFile } from '@/api/accumulate/questionBank'
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
import selectScene from './select-scene'
import selectNE from './select-ne'
import selectHole from './select-hole'
import selectHoleMuti from './select-hole'
export default {
  name: 'BaseConfig',
  components: {
    createView,
    myEditor,
    selectScene,
    selectNE,
    selectHole,
    selectHoleMuti
  },
  mixins: [mixinsActionMenu],
  props: {
    baseData: {
      type: Object,
      default: () => null
    }
  },
  data() {
    // 选项重复校验
    const repeatOptValidate = (rule, value, callback) => {
      const currentIndex = rule.field.match(/\d+/)[0]
      if (this.formData.questionOptions.find((item, index) => index != currentIndex && item.name === value)) {
        callback(new Error('选项内容重复'))
      } else {
        callback()
      }
    }
    return {
      canOpt: true, // 是否能编辑
      contentTimer: 'content' + new Date().getTime(),
      questionAnalysisTimer: 'questionAnalysis' + new Date().getTime(),
      editMode: false,
      // ['A', 'B', 'C', 'D', 'E', ...]
      radioMap: Array.from({ length: 26 }, (_, index) => String.fromCharCode('A'.charCodeAt(0) + index)),
      titleMapping: {
        'selectScene': '选择场景',
        'selectNE': '选择靶机设备',
        'selectHole': '选择漏洞',
        'selectHoleMuti': '选择漏洞'
      },
      questionConf: questionConf,
      validate: validate,
      repeatOptValidate, // 选项重复校验
      showErrMsg: false, // 正确答案校验报错信息
      token: '', // 上传文件需要token
      loading: true,
      formData: {
        questionName: '', // 名称
        questionDepotId: '', // 题库id
        categoryId: '', // 分类id
        complexity: '1', // 难度
        manualScoring: '0', // 人工判分
        solutionApproach: '0', // 是否上传解题办法
        uses: [], // 用途
        skillPointsBOS: [{ // 技能点
          id: '', // 技能点 id
          abilityValue: 1 // 技能点能力值 1-5 正整数
        }],
        knowledgeCode: [], // 实训知识点code
        bankType: 1, // 题库类型: 1-理论 2-靶机 3-仿真
        questionType: '1', // 题型
        content: '', // 题干
        questionOptions: [
          { name: '' },
          { name: '' }
        ], // 选项
        questionAnswer: '', // 单选的正确答案
        trueOrFalse: true, // 判断题的答案
        inputAnswer: '', // CTF、AWD、其他、填空题的答案
        textAreaAnswer: '', // 简答题的答案
        fileUrl: '', // 附件的地址
        fileName: '', // 附件的name
        fileError: false,
        questionAnalysis: '', // 题目解析
        topologyMode: 0, // 场景
        selectedScene: null, // 已选的场景
        topologyTemplateId: '', // 选择的场景中的topo id
        topologyVisible: 1, // 拓扑是否可见
        flagRefresh: 0, // 动态flag
        networkElementId: '', // 选择的靶机设备id
        selectedNE: null, // 已选的靶机设备
        targetType: false, // 靶机类型 1-目标机 2-跳板机',
        selectedHole: null, // 漏洞题已选的漏洞（单选）
        selectedHoleMuti: [], // 非漏洞题已选的漏洞（多选）
        flagPath: '', // flag路径
        playerAccount: '', // 选手加固账号
        checkFileUrl: '', // 检测脚本地址
        checkFileName: '', // 检测脚本名称
        updateFileName: '', // 题目更新脚本名称
        updateFileUrl: '', // 题目更新脚本地址
        checkFileError: false,
        updateFileError: false,
        questionHoleRelationBOS: [], // 题目漏洞内容
        bugPort: undefined,
        combinationQuestionBOS: [ // 组合题里的综合题
          {
            questionName: '', // 组合题文本
            questionTimer: 'question0' + new Date().getTime(),
            content: [
              {
                contentName: '', // 题干文本
                contentTimer: 'content00' + new Date().getTime()
              }
            ],
            fileName: '', // 答案文件名
            fileUrl: '', // 答案地址
            error: '',
            questionAnalysis: '', // 题目解析
            analysisTimer: 'analysis0' + new Date().getTime()
          }
        ]
      },
      rules: {
        questionName: [validate.required(), validate.name_64_char],
        categoryId: [validate.required('change')],
        complexity: [validate.required('change')],
        manualScoring: [validate.required('change')],
        solutionApproach: [validate.required('change')],
        uses: [validate.required('change')],
        questionType: [validate.required('change')],
        content: [validate.required()],
        questionOptions: [validate.required()],
        questionAnswer: [validate.required()],
        trueOrFalse: [validate.required()],
        inputAnswer: [
          validate.required(),
          { min: 1, max: 255, message: '1-255个字符', trigger: 'blur' }
        ],
        textAreaAnswer: [
          validate.required(),
          { min: 1, max: 500, message: '1-500个字符', trigger: 'blur' }
        ],
        networkElementId: [validate.required()],
        topologyTemplateId: [validate.required()],
        selectedHole: [validate.required('change')],
        skillPointsBOS: [validate.required()],
        bugPort: [
          validate.required('change'),
          validate.number_integer,
          { type: 'number', min: 1, max: 65535, message: '输入范围：1-65535', trigger: 'change' }
        ]
      },
      questionCategoryList: [], // 题库列表
      categoryList: [], // 分类列表
      skillPointList: [], // 技能点列表
      trainingPointList: [], // 实训知识点列表
      questionTypeArr: [], // 题型列表
      allHoleList: [], // 所有的漏洞列表
      maxFileSize: localStorage.getItem('maxFileSize') // 最大文件大小
    }
  },
  computed: {
    errMsgText() {
      return this.formData.questionType == 1 ? '至少选择一个正确答案' : '至少选择两个正确答案'
    }
  },
  watch: {
    'formData.selectedHole': function(val) {
      this.$refs['form'].validateField('selectedHole')
    },
    'formData.selectedNE': function(val) {
      if (val) {
        this.formData.networkElementId = val['id']
        if (val.type === 'pnf') {
          this.formData.flagRefresh = 0
        }
      } else {
        this.formData.networkElementId = ''
      }
      this.$refs['form'].validateField('networkElementId')
    },
    'formData.selectedScene': function(val) {
      if (val) {
        this.formData.topologyTemplateId = val['topologyTemplateId']
      } else {
        this.formData.topologyTemplateId = ''
      }
      this.$refs['form'].validateField('topologyTemplateId')
    }
  },
  created() {
    this.token = JSON.parse(localStorage.getItem('Admin-Token')).data
    this.formData.bankType = this.$route.params.bankType
    this.editMode = !!this.$route.params.id
    // 在题库页面创建题目跳转过来时回显当前题库
    if (this.$route.params.questionDepotId) {
      this.formData.questionDepotId = +this.$route.params.questionDepotId
    }

    // 设置题型 start
    let typeMap = []
    if (this.formData.bankType == 1) { // 理论
      typeMap = ['1', '2', '3', '4', '7', '8']
    } else if (this.formData.bankType == 2) { // 靶机
      typeMap = ['4', '5', '9', '6']
      this.formData.questionType = '4'
    } else if (this.formData.bankType == 3) { // 仿真
      typeMap = ['1', '2', '3', '4', '7', '8', '10']
    }
    const questionTypeArr = []
    typeMap.forEach(item => {
      const val = questionConf.questionTypeArr.find(val => val.value == item)
      if (val) {
        questionTypeArr.push(val)
      }
    })
    this.questionTypeArr = questionTypeArr
    // 设置题型 end

    const result = [this.getCategory(), this.getQuestionCategory(), this.getTrainingPoint(), this.getSkillPoint()]
    // 编辑
    if (this.editMode) {
      result.push(this.getQuestionBankItem(this.$route.params.id))
    } else if (this.baseData) {
      this.handleEditData(this.baseData)
    }

    Promise.all(result).then(() => {
      this.loading = false
      this.contentTimer = 'content' + new Date().getTime()
      this.questionAnalysisTimer = 'questionAnalysis' + new Date().getTime()
      this.formData['combinationQuestionBOS'].map((item, index) => {
        item.questionTimer = 'question' + index + new Date().getTime()
        item.analysisTimer = 'analysis' + index + new Date().getTime()
        item.content.map((sub, subIndex) => {
          sub.contentTimer = 'content' + index + subIndex + new Date().getTime()
        })
      })
    }).finally(() => {
      this.loading = false
    })
  },
  methods: {
    addSkillPoints() {
      if (this.formData.skillPointsBOS.length === 30) {
        this.$message({
          message: `最多添加30个技能点`,
          type: 'error'
        })
        return
      }
      this.formData.skillPointsBOS.push({
        id: '',
        abilityValue: 1
      })
    },
    deleteSkillPoints(index) {
      this.formData.skillPointsBOS.splice(index, 1)
    },
    // 上传文件
    'handleFileChange'(e, key, index) {
      const files = e.target.files
      if (!files || !files.length) {
        return
      }
      const file = files[0]
      const maxSize = this.maxFileSize * 1024 * 1024
      if (key === 'fileUrl') { // 附件
        if (file.size > maxSize) {
          this.formData.fileError = true
          this.$message.error(`上传文件不能超过${this.maxFileSize}MB!`)
          return
        }
        this.formData.fileError = false
      } else if (key === 'checkFileUrl') { // check脚本
        this.formData.checkFileError = false
      } else if (key === 'updateFileUrl') {
        this.formData.updateFileError = false
      } else if (key === 'combinationQuestionBOS') { // 综合题参考答案
        if (file.size > maxSize) {
          this.formData.combinationQuestionBOS[index].error = true
          this.$message.error(`上传文件不能超过${this.maxFileSize}MB!`)
          return
        }
        this.formData.combinationQuestionBOS[index].error = false
      }
      uploadFile({ file: file }).then((res) => {
        if (key === 'fileUrl') { // 附件
          this.formData.fileName = file.name
          this.formData.fileUrl = res.data
        } else if (key === 'checkFileUrl') { // check脚本
          this.formData.checkFileName = file.name
          this.formData.checkFileUrl = res.data
        } else if (key === 'combinationQuestionBOS') { // 综合题参考答案
          this.formData.combinationQuestionBOS[index].fileName = file.name
          this.formData.combinationQuestionBOS[index].fileUrl = res.data
        } else if (key === 'updateFileUrl') {
          this.formData.updateFileName = file.name
          this.formData.updateFileUrl = res.data
        }
      }).catch(() => {
        if (key === 'fileUrl') { // 附件
          this.formData.fileError = true
        } else if (key === 'checkFileUrl') { // check脚本
          this.formData.checkFileError = true
        } else if (key === 'combinationQuestionBOS') { // 综合题参考答案
          this.formData.combinationQuestionBOS[index].error = true
        } else if (key === 'updateFileUrl') { // check脚本
          this.formData.updateFileError = true
        }
      })
    },
    // 点击“上传文件”按钮
    'handleUploadClick'(key, index) {
      if (key === 'fileUrl') { // 附件
        this.$refs['inputFile'].click()
        this.$refs['inputFile'].value = ''
      } else if (key === 'checkFileUrl') { // check脚本
        this.$refs['inputCheckFile'].click()
        this.$refs['inputCheckFile'].value = ''
      } else if (key === 'updateFileUrl') {
        this.$refs['inputUpdateFile'].click()
        this.$refs['inputUpdateFile'].value = ''
      } else if (key === 'combinationQuestionBOS') { // 综合题参考答案
        this.$refs[`input${index}`][0].click()
        this.$refs[`input${index}`][0].value = ''
      }
    },
    // 删除文件
    'handleFileDelete'(key, index) {
      if (key === 'fileUrl') { // 附件
        this.formData.fileUrl = ''
        this.formData.fileName = ''
      } else if (key === 'checkFileUrl') { // check脚本
        this.formData.checkFileUrl = ''
        this.formData.checkFileName = ''
      } else if (key === 'updateFileUrl') {
        this.formData.updateFileUrl = ''
        this.formData.updateFileName = ''
      } else if (key === 'combinationQuestionBOS') { // 综合题参考答案
        this.formData.combinationQuestionBOS[index].fileUrl = ''
        this.formData.combinationQuestionBOS[index].fileName = ''
      }
    },
    // 增加综合题
    'addComp': function() {
      const length = this.formData.combinationQuestionBOS.length
      this.formData.combinationQuestionBOS.push({
        questionName: '', // 组合题文本
        questionTimer: `question${length}` + new Date().getTime(),
        content: [
          {
            contentName: '', // 题干文本
            contentTimer: `content${length}0` + new Date().getTime()
          }
        ],
        fileName: '', // 答案文件名
        fileUrl: '', // 答案地址
        questionAnalysis: '', // 题目解析
        analysisTimer: `analysis${length}` + new Date().getTime()
      })
    },
    // 删除综合题
    'deleteComp': function(index) {
      this.formData.combinationQuestionBOS.splice(index, 1)
    },
    // 添加综合题-题干
    'addCompContent': function(index) {
      const length = this.formData.combinationQuestionBOS[index].content.length
      this.formData.combinationQuestionBOS[index].content.push({
        contentName: '', // 题干文本
        contentTimer: `content${index}${length}` + new Date().getTime()
      })
    },
    // 删除综合题-题干
    'deleteCompContent': function(index, subIndex) {
      this.formData.combinationQuestionBOS[index].content.splice(subIndex, 1)
    },
    'handleEditData': function(baseData) {
      this.formData['topologyTemplateId'] = baseData['topologyTemplateId']
      this.formData['questionName'] = baseData['questionName']
      this.formData['questionDepotId'] = baseData['questionDepotId']
      this.formData['categoryId'] = baseData['categoryId']
      this.formData['complexity'] = String(baseData['complexity'])
      this.formData['manualScoring'] = String(baseData['manualScoring'])
      this.formData['solutionApproach'] = String(baseData['solutionApproach'])
      this.formData['uses'] = baseData['uses'].split(',')
      this.formData['knowledgeCode'] = baseData['knowledgeVos'].map(item => {
        return item.knowledgeCode
      })
      this.formData['questionType'] = String(baseData['questionType'])
      this.formData['content'] = baseData['content']
      this.formData['questionOptions'] = JSON.parse(baseData['questionOptions']).map(item => { return { name: item } })
      this.formData['skillPointsBOS'] = baseData['skillPointsBOS']
      // 回显 questionAnswer start
      if (baseData.questionType == 1) { // 单选
        this.formData['questionAnswer'] = baseData['questionAnswer']
      } else if (baseData.questionType == 2) { // 多选
        const result = baseData['questionAnswer'].split('')
        result.forEach(item => {
          const index = this.radioMap.findIndex(val => val == item)
          this.formData['questionOptions'][index].questionAnswer = true
        })
      } else if (baseData.questionType == 3) { // 判断
        this.formData['trueOrFalse'] = baseData['questionAnswer'] === 'A'
      } else if (baseData.questionType == 8) { // 简答题
        this.formData['textAreaAnswer'] = baseData['questionAnswer']
      } else if (baseData.questionType == 4) { // 简答题
        this.formData['bugPort'] = baseData['targetPort']
        this.formData['inputAnswer'] = baseData['questionAnswer']
      } else { // CTF、AWD、其他、填空题、漏洞题
        this.formData['inputAnswer'] = baseData['questionAnswer']
      }
      // 回显 questionAnswer end

      this.formData['fileUrl'] = baseData['fileUrl']
      this.formData['fileName'] = baseData['fileName']
      this.formData['questionAnalysis'] = baseData['questionAnalysis']
      this.formData['topologyVisible'] = baseData['topologyVisible']
      this.formData['flagRefresh'] = baseData['flagRefresh']
      this.formData['networkElementId'] = baseData['networkElementId']
      if (this.formData['networkElementId']) {
        this.getNetworkElementItem(this.formData['networkElementId'])
      }
      this.formData['flagPath'] = baseData['flagPath']
      this.formData['targetType'] = baseData['targetType'] == 2
      this.formData['playerAccount'] = baseData['playerAccount']
      this.formData['checkFileUrl'] = baseData['checkFileUrl']
      this.formData['checkFileName'] = baseData['checkFileName']
      this.formData['updateFileUrl'] = baseData['updateFileUrl']
      this.formData['updateFileName'] = baseData['updateFileName']
      if (baseData['questionType'] == 9) {
        this.formData['selectedHole'] = baseData['questionHoleRelationVOList'][0]
        this.formData['bugPort'] = baseData['questionHoleRelationVOList'][0]['bugPort']
      } else {
        this.formData['selectedHoleMuti'] = baseData['questionHoleRelationVOList']
      }
      if (baseData['questionType'] == 10) {
        this.formData['combinationQuestionBOS'] = baseData['combinationQuestionBOS'].map((item, index) => {
          return {
            questionName: item.questionName,
            questionTimer: 'question' + index + new Date().getTime(),
            fileUrl: item.fileUrl,
            fileName: item.fileName,
            questionAnalysis: item.questionAnalysis,
            analysisTimer: 'analysis' + index + new Date().getTime(),
            content: JSON.parse(item.content).map((sub, subIndex) => {
              return {
                contentName: sub,
                contentTimer: 'content' + index + subIndex + new Date().getTime()
              }
            })
          }
        })
      }
    },
    'changeQuestionType': function(value) {
      // 如果是漏洞题，并且之前选择的靶机是虚拟设备，则清空
      if (value == 9 && this.formData.selectedNE && this.formData.selectedNE.type === 'pnf') {
        this.formData.selectedNE = null
      }
      this.$refs['form'].clearValidate('selectedHole')
    },
    // 添加选项
    'optionAdd': function() {
      if (this.formData.questionOptions.length == 26) {
        this.$message({
          message: `选项最多支持26个`,
          type: 'error'
        })
        return
      }
      this.formData.questionOptions.push({ 'name': '' })
    },
    // 删除选项
    'optionDelete': function(index) {
      this.formData.questionOptions.splice(index, 1)
      if (this.formData.questionType == 1) { // 单选
        // 如果删除的是正确答案，则同时删除正确答案
        if (this.formData.questionAnswer === this.radioMap[index]) {
          this.formData.questionAnswer = ''
          this.showErrMsg = true
        }
      } else if (this.formData.questionType == 2) { // 多选
        this.showErrMsg = this.formData.questionOptions.filter(item => item.questionAnswer).length < 2
      } else {
        this.showErrMsg = false
      }
    },
    // 综合题：富文本编辑器改变内容时
    'compChange': function(key, index, value) {
      if (this.delHtml(value)) {
        this.formData['combinationQuestionBOS'][index][key] = value
      } else {
        const imgStrs = value.match(/<img.*?>/g)
        if (imgStrs && imgStrs.length) { // 内容只有图片时
          this.formData['combinationQuestionBOS'][index][key] = value
        } else {
          this.formData['combinationQuestionBOS'][index][key] = ''
        }
      }
      if (String(this.delHtml(value)).length != 0) {
        this.$refs['form'].validateField(`combinationQuestionBOS.${index}.${key}`)
      }
    },
    // 综合题-题干：富文本编辑器改变内容时
    'compContentChange': function(index, subIndex, value) {
      if (this.delHtml(value)) {
        this.formData['combinationQuestionBOS'][index]['content'][subIndex]['contentName'] = value
      } else {
        const imgStrs = value.match(/<img.*?>/g)
        if (imgStrs && imgStrs.length) { // 内容只有图片时
          this.formData['combinationQuestionBOS'][index]['content'][subIndex]['contentName'] = value
        } else {
          this.formData['combinationQuestionBOS'][index]['content'][subIndex]['contentName'] = ''
        }
      }
      if (String(this.delHtml(value)).length != 0) {
        this.$refs['form'].validateField(`combinationQuestionBOS.${index}.content.${subIndex}.contentName`)
      }
    },
    // 富文本编辑器改变内容时
    'contentChange': function(key, value) {
      if (this.delHtml(value)) {
        this.formData[key] = value
      } else {
        const imgStrs = value.match(/<img.*?>/g)
        if (imgStrs && imgStrs.length) { // 内容只有图片时
          this.formData[key] = value
        } else {
          this.formData[key] = ''
        }
      }
      if (String(this.delHtml(value)).length != 0) {
        this.$refs['form'].validateField(key)
      }
    },
    // 过滤html代码、空格、回车 空白字符
    'delHtml': function(str) {
      str = str.replace(/<("[^"]*"|'[^']*'|[^'">])*>/gi, '')
      str = str.replace(/[\r\n]/g, '')
      str = str.replace(/\s/g, '')
      str = str.replace(/&nbsp;/ig, '')
      return str
    },
    // 侧拉选择资源回调
    'drawerConfirmCall': function(type, data) {
      if (type === 'close') {
        this.drawerClose()
      } else if (type === 'confirm_scene') {
        this.formData.selectedScene = data[0]
        this.drawerClose()
      } else if (type === 'confirm_NE') {
        this.formData.selectedNE = data[0]
        this.drawerClose()
      } else if (type === 'confirm_hole') {
        this.formData.selectedHole = data[0]
        this.drawerClose()
      } else if (type === 'confirm_hole_muti') {
        data.forEach(item => {
          if (!this.formData.selectedHoleMuti.find(val => val.bugId == item.bugId)) {
            this.formData.selectedHoleMuti.push(item)
          }
        })
        this.drawerClose()
      }
    },
    // 编辑时候获取数据
    'getQuestionBankItem': function(id) {
      return new Promise((resolve, reject) => {
        getQuestionBankItem({ id }).then(res => {
          this.handleEditData(res.data)
          resolve()
        }).catch(() => {
          reject()
        })
      })
    },
    // 获取分类列表
    'getCategory': function() {
      return new Promise((resolve, reject) => {
        getCategory({ categoryType: this.formData.bankType, pageType: 0 }).then(res => {
          this.categoryList = res.data.records
          resolve()
        }).catch(() => {
          this.categoryList = []
          reject()
        })
      })
    },
    'getSkillPoint': function() {
      return new Promise((resolve, reject) => {
        getSkillPoint({ pageType: 0 }).then(res => {
          this.skillPointList = res.data.records
          resolve()
        }).catch(() => {
          this.skillPointList = []
          reject()
        })
      })
    },
    // 获取实训知识点列表
    'getTrainingPoint': function() {
      return new Promise((resolve, reject) => {
        getTrainingPoint().then(res => {
          this.trainingPointList = res.data
          resolve()
        }).catch(() => {
          this.trainingPointList = []
          reject()
        })
      })
    },
    // 获取选择的靶机设备
    'getNetworkElementItem': function(id) {
      return new Promise((resolve, reject) => {
        getNetworkElementItem(id).then(res => {
          this.formData.selectedNE = res.data
          resolve()
        }).catch(() => {
          reject()
        })
      })
    },
    // 获取题库列表
    'getQuestionCategory': function() {
      return new Promise((resolve, reject) => {
        getQuestionCategory({ categoryType: this.formData.bankType, pageType: 0 }).then(res => {
          this.questionCategoryList = res.data || []
          resolve()
        }).catch(() => {
          this.questionCategoryList = []
          reject()
        })
      })
    },
    confirm: function(callback) {
      this.$refs['form'].validate((valid) => {
        if (this.formData.questionType == 1) { // 单选
          this.showErrMsg = !this.formData.questionAnswer
        } else if (this.formData.questionType == 2) { // 多选
          this.showErrMsg = this.formData.questionOptions.filter(item => item.questionAnswer).length < 2
        } else {
          this.showErrMsg = false
        }
        if (valid && !this.showErrMsg) {
          const postData = JSON.parse(JSON.stringify(this.formData))

          // 设置questionAnswer start
          if (postData.questionType == 1) { // 单选
            postData['questionAnswer'] = postData['questionAnswer']
          } else if (postData.questionType == 2) { // 多选
            const result = []
            postData['questionOptions'].forEach((item, index) => {
              if (item.questionAnswer) {
                result.push(this.radioMap[index])
              }
            })
            postData['questionAnswer'] = result.join('')
          } else if (postData.questionType == 3) { // 判断
            postData['questionAnswer'] = postData['trueOrFalse'] ? 'A' : 'B'
          } else if (postData.questionType == 4) { // ctf
            postData['questionAnswer'] = postData['inputAnswer']
            postData['targetPort'] = postData['bugPort']
          } else if (postData.questionType == 8) { // 简答题
            postData['questionAnswer'] = postData['textAreaAnswer']
          } else { // CTF、AWD、其他、填空题、漏洞题
            postData['questionAnswer'] = postData['inputAnswer']
          }
          delete postData['trueOrFalse']
          delete postData['inputAnswer']
          delete postData['textAreaAnswer']
          // 设置questionAnswer end

          postData['uses'] = postData['uses'].join(',')
          postData['knowledgeBOs'] = postData['knowledgeCode'].map(item => {
            return { knowledgeId: item }
          })
          delete postData['knowledgeCode']
          postData['questionOptions'] = JSON.stringify(postData['questionOptions'].map(item => item.name))
          if (postData.questionType == 9) { // 漏洞题的单选漏洞
            postData['questionHoleRelationBOS'] = [{
              bugType: postData.selectedHole.bugType,
              bugId: postData.selectedHole.bugId,
              bugName: postData.selectedHole.bugName,
              bugNum: 1,
              bugPort: postData.bugPort
            }]
          } else { // 非漏洞题的多选漏洞
            postData['questionHoleRelationBOS'] = postData.selectedHoleMuti && postData.selectedHoleMuti.map(item => {
              return {
                bugType: item.bugType,
                bugId: item.bugId,
                bugName: item.bugName,
                bugNum: 1
              }
            })
          }
          delete postData['selectedHole']
          delete postData['selectedHoleMuti']
          delete postData['selectedNE']

          if (postData.bankType == 2) {
            postData.targetType = postData.targetType ? 2 : 1
          } else {
            delete postData.selectedScene
            delete postData.targetType
          }

          // 设置组合题
          if (postData.questionType == 10) {
            postData['combinationQuestionBOS'] = postData['combinationQuestionBOS'].map(item => {
              return {
                questionName: item.questionName,
                fileUrl: item.fileUrl,
                fileName: item.fileName,
                questionAnalysis: item.questionAnalysis,
                content: JSON.stringify(item.content.map(sub => {
                  return sub.contentName
                }))
              }
            })
          } else {
            postData['combinationQuestionBOS'] = []
          }
          callback(postData)
        }
      })
    },
    // 跳转到创建知识点页面
    goToKnowledgePage() {
      this.$router.push({
        path: '/manage/training/knowledgeManage',
        query: { openCreateDialog: true }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.base-config-wrap {
  height: 100%;
  .el-scrollbar {
    height: 100%;
    /deep/ .el-scrollbar__wrap {
      overflow-x: hidden;
    }
    /deep/ .el-scrollbar__bar.is-horizontal {
      display: none;
    }
  }
  .error-message {
    color: rgb(245, 108, 108);
    font-size: 12px;
    line-height: 1;
    padding: 5px 0;
  }
  .question-answer {
    margin: 0 8px;
    line-height: 15px;
  }
  .question-options-form {
    /deep/ .el-form-item {
      margin-bottom: 5px;
    }
  }
  .bug-content-title {
    display: flex;
    color: var(--neutral-600);
    font-weight: 500;
  }
  .tpl-upload {
    .upload-select-input {
      display: none;
    }
    .upload-click-wrap {
      display: inline-block;
    }
    .file-container {
      overflow-wrap: break-word;
      word-break: normal;
      line-height: 1.5;
      border: 1px solid;
      margin-top: 5px;
      padding: 6px 10px;
      overflow: hidden;
      text-overflow:ellipsis;
      white-space: nowrap;
      cursor: pointer;
      position: relative;
      .delete {
        position: absolute;
        top: 10px;
        right: 10px;
      }
    }
  }
}
.skill-points-BOS {
  .skill-points-BOS-title {
    display: flex;
  }
  .skill-points-BOS-content {
    display: flex;
    align-items: center;
    .el-form-item {
      margin-bottom: 5px;
    }
    i {
      font-size: 20px;
      cursor: pointer;
      margin-left: 10px;
      margin-bottom: 5px;
    }
    ::v-deep {
      .el-input-number__decrease, .el-input-number__increase {
        display: none;
      }
    }
  }
}
</style>
