<template>
  <section v-if="data.length" class="question-no-wrap">
    <div class="question_type">{{ questionTypeTitle }}</div>
    <div class="question_sn_order">
      <div
        v-for="(item, index) in data"
        :key="item.questionCode"
        @click="handleOrderClick(item, index)"
      >
        <!-- 已作答 -->
        <div v-if="isAnswer(item)">
          <div
            v-if="item.questionAnswer === item.questionUserAnswer || item.questionStudentScore > 0"
            :class="`question_sn_order_item is-success`"
          >
            {{ index + 1 }}
          </div>
          <div v-else class="question_sn_order_item is-error">{{ index + 1 }}</div>
        </div>
        <!-- 未作答 -->
        <div v-else class="question_sn_order_item is-default">
          <div>{{ index + 1 }}</div>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
import conf from '@/views/admin/hrm/content/create/config.js'

export default {
  props: {
    data: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {}
  },
  inject: ['questionDetail'],
  computed: {
    // 题目类型
    questionType: function() {
      return this.data[0] && this.data[0]['questionType']
    },
    // 题目类型标题
    questionTypeTitle: function() {
      const title = conf.questionTypObj[this.questionType].label
      return `${title}(共${this.data.length}题)`
    }
  },
  methods: {
    // 是否作答
    isAnswer: function(item) {
      const { courseRole } = this.questionDetail
      return (
        courseRole == 1 && item.questionUserAnswer !== '未作答'
      )
    },
    handleOrderClick(item, index) {
      this.$emit('click', item, index)
    }
  }
}
</script>

<style lang="scss" scoped>
.question-no-wrap {
  .question_type {
    font-size: 14px;
    font-weight: 600;
  }
  .question_sn_order {
    margin-top: 10px;
    display: flex;
    flex-wrap: wrap;
    .question_sn_order_item {
      width: 32px;
      height: 32px;
      font-size: 14px;
      margin: 8px;
      line-height: 32px;
      text-align: center;
      border-radius: 2px;
      cursor: pointer;
      &.is-default {
        border: 1px solid var(--neutral-300);
        // color: #1267d7;
      }
      &.is-success {
        background: #e8ffea;
        color: #00b42a;
      }
      &.is-error {
        color: #f53f3f;
        background: #ffece8;
      }
    }
  }
}
</style>
