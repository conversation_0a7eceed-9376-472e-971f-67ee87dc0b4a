<template>
  <div v-loading="loading" class="drawer-wrap orchestration-drawer-wrap">
    <div class="drawer-wrap-content">
      <el-form ref="form" :model="formData" :rules="rules" label-width="140px">
        <el-form-item label="基础组件名称" prop="deviceName">
          {{ formData.deviceName }}
        </el-form-item>
        <el-form-item label="组件名称" prop="name">
          <el-input :disabled="disabledType" v-model.trim="formData.name"/>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input :disabled="disabledType" v-model.trim="formData.description" type="textarea" />
        </el-form-item>
        <el-form-item v-if="isSwitch" label="CIDR" prop="cidr">
          <el-dropdown :disabled="disabledCIDR || disabledType" trigger="click" placement="bottom-start" @command="setCidr($event, 'cidr')">
            <el-input :disabled="disabledCIDR || disabledType" v-model.trim="formData.cidr"/>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="***********/24">***********/24</el-dropdown-item>
              <el-dropdown-item command="10.0.0.0/24">10.0.0.0/24</el-dropdown-item>
              <el-dropdown-item command="**********/24">**********/24</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </el-form-item>
        <el-form-item v-if="isSwitch" label="网关" prop="gateway">
          <el-input :disabled="disabledCIDR || disabledType" v-model.trim="formData.gateway"/>
        </el-form-item>
        <el-form-item v-if="isSwitch" label="网络类型" prop="network_type">
          <el-select :disabled="disabledType" v-model="formData.network_type">
            <el-option label="VLAN" value="vlan" />
            <el-option label="VXLAN" value="vxlan" />
          </el-select>
        </el-form-item>
        <el-form-item v-if="isExternal" label="外部网络" prop="vnf_uuid">
          <el-select :disabled="disabledCIDR || disabledType" v-model="formData.vnf_uuid">
            <el-option v-for="item in externalNetworkList" :key="item.id" :label="item.network_name + '（' + item.cidr + '）'" :value="item.vnf_uuid" :disabled="selectedExternalNetworkIds.includes(item.id)"/>
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <div class="drawer-footer">
      <el-button :disabled="disabledType" type="primary" @click="confirm">确定</el-button>
      <el-button type="text" @click="close">取消</el-button>
    </div>
  </div>
</template>
<script>
import validate from '../../validate'
import { isV4, inRange } from 'range_check'
import { getNodeItem, getExternalNetwork } from '../api/orchestration'
import { Graph } from '@antv/x6'
export default {
  components: {},
  props: {
    data: {
      type: Object,
      default: () => {
        return {}
      }
    },
    type: {
      type: String,
      default: ''
    },
    graph: {
      type: [Object, Graph]
    }
  },
  data() {
    return {
      loading: true,
      validate: validate,
      externalNetworkList: [],
      selectedExternalNetworkIds: [], // 拓扑中外部网络节点已经选择的网络id
      formData: {
        'name': '',
        'deviceName': '',
        'description': '',
        'cidr': '',
        'gateway': '',
        'network_type': '',
        'vnf_uuid': ''
      },
      rules: {
        'name': [
          validate.required(),
          validate.base_name
        ],
        'description': [
          validate.description
        ],
        'cidr': [
          validate.required(),
          validate.filterIpCIDR
        ],
        'gateway': [
          validate.required(),
          { validator: this.validateIp, trigger: 'blur' }
        ],
        'network_type': [
          validate.required('change')
        ],
        'vnf_uuid': [
          validate.required('change')
        ]
      }
    }
  },
  computed: {
    disabledCIDR() {
      return this.data.node.data.status !== 'pending'
    },
    disabledType() {
      return this.type !== 'allPermissions' && this.type !== 'templatePermissions'
    },
    'isSwitch': function(data) {
      return this.data.node.data.virtual_type === 'logic_switch'
    },
    'isExternal': function(data) {
      return this.data.node.data.virtual_type === 'external_switch'
    }
  },
  created() {
    const nodes = this.graph.getNodes()
    this.selectedExternalNetworkIds = nodes.filter(item => item.data.vnf_uuid).map(item => item.data.vnf_uuid)
    const data = this.data.node.data
    this.formData['name'] = data['name']
    this.formData['deviceName'] = data['deviceName']
    this.formData['description'] = data['description'] || ''
    this.formData['cidr'] = data['cidr'] || ''
    this.formData['gateway'] = data['gateway'] || ''
    Promise.all([this.getNodeItem(), this.getExternalNetwork()]).then(() => {
      this.loading = false
    })
  },
  methods: {
    // 重新获取node的端口
    'getNodeItem': function() {
      return new Promise((resolve, reject) => {
        getNodeItem(this.data.node.data.node_id)
          .then(res => {
            this.formData['network_type'] = res.data.data.network_type || ''
            const data = res.data.data.external_network
            if (data) {
              this.formData['vnf_uuid'] = data.id || ''
              this.selectedExternalNetworkIds = this.selectedExternalNetworkIds.filter(item => item != data.id)
            }
            resolve()
          })
          .catch((error) => {
            console.log(error)
            reject()
          })
      })
    },
    // 获取云平台外部网络
    'getExternalNetwork': function() {
      return new Promise((resolve, reject) => {
        getExternalNetwork()
          .then(res => {
            this.externalNetworkList = res.data.data.result || []
            resolve()
          })
          .catch((error) => {
            console.log(error)
            reject()
          })
      })
    },
    // 校验IP地址
    'validateIp': function(rule, value, callback) {
      const block = this.formData['cidr']
      if (value) {
        if (!isV4(value)) {
          callback(new Error('请输入正确的IPv4地址'))
        } else if (!block) {
          callback(new Error('请先输入CIDR'))
        } else if (!inRange(value, block)) {
          callback(new Error('IP地址不在允许范围内'))
        }
      }
      callback()
    },
    // 选择网络地址时
    'setCidr': function(value, name) {
      this.formData[name] = value
      this.$refs['form'].validateField(name)
    },
    'close': function() {
      this.$emit('close')
    },
    'confirm': function() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          const postData = {
            'name': this.formData['name'],
            'description': this.formData['description'],
            'cidr': this.formData['cidr'],
            'gateway': this.formData['gateway'],
            'network_type': this.formData['network_type'],
            'vnf_uuid': this.formData['vnf_uuid']
          }
          this.$emit('call', 'configNode', postData)
          this.close()
        }
      })
    }
  }
}
</script>
<style lang="less">
.orchestration-drawer-wrap {
  .el-form {
    & >.el-form-item > .el-form-item__content > .el-dropdown,
    & >.el-form-item > .el-form-item__content > .el-input,
    & >.el-form-item > .el-form-item__content > .el-select,
    & >.el-form-item > .el-form-item__content > .el-textarea {
      width: 90%;
    }
  }
  .port-form {
    th {
      padding: 0;
    }
    >.el-form-item__label {
      width: 70px !important;
    }
    >.el-form-item__content {
      margin-left: 70px !important;
      // margin-top: 40px;
      .data-table-footer {
        display: none;
      }
    }
  }
}
</style>
