import request from '@/utils/request'

/**
 * 基础教学-分类
 */
export function searchCategoryTypeAPI(data) {
  return request({
    url: '/training/pjtCategory/searchCategoryType',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 新增课程内容
 */
export function addCategoryTypeAPI(data) {
  return request({
    url: '/training/pjtCategoryContent/insertCategoryContent',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 新增课程菜单
 */
export function addCourseCategoryTypeAPI(data) {
  return request({
    url: '/training/pjtCategoryContent/searchCategoryContentType',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 删除课程
 */
export function deleteCourseCategoryTypeAPI(data) {
  return request({
    url: '/training/pjtCategoryContent/deleteCategoryContent',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 编辑课程内容
 */
export function editCourseCategoryTypeAPI(data) {
  return request({
    url: '/training/pjtCategoryContent/updateCategoryContent',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
/**
 * 基础教学-新增分类
 */
export function insertCategoryAPI(data) {
  return request({
    url: '/training/pjtCategory/insertCategory',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 基础教学查询
 */
export function searchBasicsTeachingAPI(data) {
  return request({
    url: '/training/pjtBasicsTeaching/backSearchBasicsTeaching',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 基础教学的课程通过ID删除课程
 */
export function deleteTeachingCurriculumAPI(data) {
  return request({
    url: '/training/pjtTeachingCurriculum/deleteTeachingCurriculum',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
/**
 * 基础教学的单元新增
 */
export function insertPjtBasicsTeachingUnitAPI(data) {
  return request({
    url: '/training/pjtBasicsTeachingUnit/insertPjtBasicsTeachingUnit',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}


/**
 * 基础教学-删除
 */
export function deleteBasicsTeachingAPI(data) {
  return request({
    url: '/training/pjtBasicsTeaching/deleteBasicsTeaching',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}


/**
 * 基础教学-课程列表类型下拉
 *
 */
export function searchPackageTypeApi(data) {
  return request({
    url: '/training/pjtTeachingPlanUnitAttribute/searchPackageType',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 基础教学-新增批量排课
 *
 */
export function insertBasicsTeacherSchedulingApi(data) {
  return request({
    url: '/training/pjtTeacherScheduling/insertBasicsTeacherScheduling',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 基础教学的课程列表查询
 *
 */
export function searchBasicsPackageApi(data) {
  return request({
    url: '/training/pjtTeachingCurriculum/searchBasicsCurriculum',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 增加排课的班级下拉
 *
 */
export function searchMajorApi(data) {
  return request({
    url: '/training/pjtTeacherScheduling/searchMajor',
    method: 'post',
    data: data
  })
}

/**
 * 基础教学的单元查询
 *
 */
export function searchPjtBasicsTeachingUnitApi(data) {
  return request({
    url: '/training/pjtBasicsTeachingUnit/searchPjtBasicsTeachingUnit',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 基础教学的删除单元
 *
 */
export function deleteBasicUnitByid(data) {
  return request({
    url: 'training/pjtBasicsTeachingUnit/removeByIds',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 基础教学-增加排课中的内容下拉
 *
 */
export function searchBasicsMainCourseApi(data) {
  return request({
    url: '/training/pjtTeacherScheduling/backSearchBasicsMainCourse',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 基础教学新增的知识点下拉
 *
 */
export function searchPointApi(data) {
  return request({
    url: '/training/pjtKnowledgePoint/searchPoint',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 基础教学上传url
 *
 */
export function upTeachingCoverApi(data) {
  return request({
    url: '/training/pjtTeachingPlan/upTeachingCover',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 基础教学-修改封面
 *
 */
export function updateBasicsTeachingApi(data) {
  return request({
    url: '/training/pjtBasicsTeaching/updateBasicsTeaching',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 基础教学-新增
 *
 */
export function insertBasicsTeachingApi(data) {
  return request({
    url: '/training/pjtBasicsTeaching/insertBasicsTeaching',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 基础教学的课程编辑
 *
 */
export function updateTeachingCurriculumApi(data) {
  return request({
    url: '/training/pjtTeachingCurriculum/updateTeachingCurriculum',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 基础教学的课程通过ID查询课程
 *
 */
export function searchTeachingCurriculumOneApi(data) {
  return request({
    url: '/training/pjtTeachingCurriculum/searchTeachingCurriculumOne',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}


/**
 * 基础教学上传资料
 *
 */
export function upCurriculumCoverApi(data) {
  return request({
    url: '/training/pjtTeachingCurriculum/upCurriculumCover',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 课程查询-操作手册\课件和视频、拓补Id
 *
 */
export function searchCurriculumApi(data) {
  return request({
    url: '/training/pjtTeachingCurriculum/searchCurriculum',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 查看课程详情
 *
 */
export function uploadReportApi(data) {
  return request({
    url: '/training/pjtCurriculumReport/upReport',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}


/**
 * 基础教学的课程新增
 *
 */
export function insertTeachingPackageApi(data) {
  return request({
    url: '/training/pjtTeachingCurriculum/insertTeachingCurriculum',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 查询编辑随堂练习题目与数量
 *
 */
export function chapelQueryApi(data) {
  return request({
    url: `/training/pjtPracticePlanCurriculumRelation/query?curriculumCode=${data.curriculumCode}`,
    method: 'get',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 编辑随堂练习
 *
 */
export function editPracticeApi(data) {
  return request({
    url: `/training/pjtPracticePlanCurriculumRelation/editPractice`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 手动组卷题目查询
 *
 */
export function questionByManualPaperAPI(data) {
  return request({
    url: '/training/pjtExamQuestion/queryBankChoice',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
/**
 * 随堂练习 选题 新建
 *
 */
export function queryPracticeAPI(data) {
  return request({
    url: '/training/pjtPracticePlanCurriculumRelation/queryPractice',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
/**
 * 随堂code-回显题库
 *
 */
export function queryPracticeCodeAPI(data) {
  return request({
    url: '/training/pjtPracticePlanCurriculumRelation/queryPracticeCode?evaluationCode=' + data.evaluationCode,
    method: 'get',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 随堂练习重新添加
 *
 */
export function updatePracticeAPI(data) {
  return request({
    url: '/training/pjtPracticePlanCurriculumRelation/updatePractice',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/** training/pjtCategory/updateCategory
 * 删除分类
 * @param {*} data
 * @returns
 */
export function delBaseType(data) {
  return request({
    url: '/training/pjtCategory/deleteCategory',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/** training/pjtCategory/updateCategory
 * 删除分类
 * @param {*} data
 * @returns
 */
export function updateCategory(data) {
  return request({
    url: '/training/pjtCategory/updateCategory',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
