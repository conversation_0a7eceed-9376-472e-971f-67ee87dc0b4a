<template>
  <div>
    <el-scrollbar class="tree-scrollbar-wrapper">
      <div class="layout-tree-list">
        <div class="spec-title" @click="handleAddMajor"><i class="el-icon-plus" style="margin-right:5px"/>创建专业</div>
        <div class="spec-body">
          <div v-for="item in majorTreeData" :class="addMajorId == item.id ? 'content-selected':''" :key="item.id" class="spec-li" @click="handleNodeClick(item, 1)">
            <el-tooltip :content="item.majorName" class="item" effect="dark" placement="top-start">
              <div class="spec-body-left">{{ item.majorName }}</div>
            </el-tooltip>
            <div class="spec-body-right">
              <i style="margin-right: 10px;cursor: pointer;" class="el-icon-edit" @click.stop="editItem(item)" />
              <i style="cursor: pointer;" class="el-icon-delete" @click.stop="handleDropClass(item)" />
            </div>
          </div>
        </div>
      </div>
    </el-scrollbar>
    <el-dialog
      :visible.sync="dialogVisible"
      :title="title"
      width="30%">
      <el-form ref="addMajorFormRef" :model="addMajorForm" :rules="classFormRules">
        <el-form-item label="名称" prop="majorName">
          <el-input v-model.trim="addMajorForm.majorName" style="width: 80%;"/>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button class="cancel-btn" @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSubmitForm">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { saveMajor, updateMajor, deleteMajor, queryMajor } from '@/api/admin/training/student'
import validate from '@/packages/validate'
export default {
  components: {
  },
  props: {
    categoryName: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      title: '创建专业',
      ApiType: saveMajor,
      majorTreeData: [],
      dialogVisible: false,
      addMajorForm: {
        majorCode: '',
        majorName: ''
      },
      classFormRules: {
        majorName: [validate.required(), validate.base_name]
      },
      addMajorId: ''
    }
  },
  mounted() {
    this.initialTree()
  },
  methods: {
    // 创建专业
    handleAddMajor() {
      this.dialogVisible = true
      this.title = '创建专业'
      this.addMajorForm.majorName = ''
      this.addMajorForm.majorCode = ''
      this.ApiType = saveMajor
      this.$nextTick(() => {
        this.$refs['addMajorFormRef'].clearValidate()
      })
    },
    editItem(dataObj) {
      this.title = '编辑专业'
      this.addMajorForm.majorName = dataObj.majorName
      this.addMajorForm.majorCode = dataObj.majorCode
      this.dialogVisible = true
    },
    // 新增专业表单
    handleSubmitForm() {
      // 校验表单
      this.$refs.addMajorFormRef.validate((valid) => {
        if (valid) {
          if (this.addMajorForm.majorCode) {
            this.ApiType = updateMajor
          }
          this.ApiType({ className: this.addMajorForm.majorName, id: this.addMajorForm.majorCode }).then((ret) => {
            if (ret.code === 0) {
              this.initialTree()
              if (this.addMajorForm.majorCode) {
                this.$message.success('编辑成功')
              } else {
                this.$message.success('添加成功')
              }
              return
            }
            if (this.addMajorForm.majorCode) {
              this.$message.warning('编辑失败')
            } else {
              this.$message.warning('添加失败')
            }
          }).finally(() => {
            this.dialogVisible = false
          })
        }
      })
    },
    handleDropClass(dataObj) {
      this.$confirm(`<p style='word-wrap: break-word;'>此操作将永久删除 “${dataObj.majorName}” 专业, 是否继续?</p>`, '提示', {
        dangerouslyUseHTMLString: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteMajor({
          className: dataObj.majorName,
          majorCode: dataObj.majorCode,
          id: dataObj.id
        }).then((ret) => {
          if (ret.code === 0) {
            this.initialTree()
            this.$message.success('删除成功')
            return
          }
          this.$message.warning('删除失败')
        })
      })
    },
    initialTree() {
      queryMajor().then((res) => {
        if (res.code === 0) {
          this.majorTreeData = res.data
          this.addMajorId = res.data[0].id
          if (this.$store.state.cache[this.categoryName + 'major']) {
            this.handleNodeClick(this.$store.state.cache[this.categoryName + 'major'])
          } else {
            this.$emit('setClassCode', res.data[0].majorCode, res.data[0].majorName)
          }
        }
      })
    },
    handleNodeClick(node, type) {
      this.addMajorId = node.id
      this.majorCode = node.majorCode
      const obj = {
        data: node,
        key: this.categoryName + 'major'
      }
      this.$store.commit('SET_CACHE', obj)
      this.$emit('setClassCode', node.majorCode, node.majorName, type)
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep {
  .el-scrollbar__wrap{
    overflow: hidden;
    overflow-y: scroll;
  }
}
.tree-scrollbar-wrapper{
  height: 100%;
  width: 230px;
  overflow: hidden;
  padding-bottom: 15px;
  border-right: 1px solid var(--neutral-300);
  .layout-tree-list {
    width: 100%;
    padding: 15px;
    background: #ffffff;
    .spec-title {
      cursor: pointer;
      width: 100%;
      height: 32px;
      color: #ffffff;
      text-align: center;
      line-height: 32px;
      background-color: var(--color-600);
      border-radius: 2px;
    }
    .spec-body {
      .content-selected {
        padding: 0 8px 0 10px;
        background-color: #f2f3f5;
      }
      line-height: 40px;
      .spec-li:hover {
        background-color: #f2f3f5; /* 鼠标放上后的背景色 */
      }
      .spec-li{
        padding: 0 8px 0 10px;
        display: flex;
        justify-content: space-between;
        margin: 5px 0;
        .spec-body-left {
          width: 138px;
          height: 40px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
    }
  }
}
.cancel-btn {
  border: none !important;
}
</style>
