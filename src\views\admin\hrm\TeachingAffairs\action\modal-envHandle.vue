<template>
  <div class="dialog-wrap">
    <div v-if="name == 'openEnv'">请确认是否开启"{{ data[0].content }}"的环境?</div>
    <div v-else>请确认是否关闭"{{ data[0].content }}"的环境?</div>
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>

<script>
import modalMixins from '@/packages/mixins/modal_form'
import { openTrainingEnv, closeTrainingEnv } from '@/api/teachingAffairs/index.js'

export default {
  components: {
  },
  mixins: [modalMixins],
  props: {
    name: {
      type: String,
      default: ''
    },
    data: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {}
  },
  computed: {
  },
  methods: {
    close() {
      this.$emit('close')
    },
    confirm: function() {
      if (this.name == 'openEnv') {
        const params = {
          classCode: this.data[0].resultList[0].classCode,
          schedulingCode: this.data[0].resultList[0].schedulingCode,
          curriculumCode: this.data[0].resultList[0].curriculumCode,
          sceneInstanceId: this.data[0].sceneInstanceId,
          schedulingType: this.data[0].schedulingType
        }
        openTrainingEnv(params).then((res) => {
          if (res.code === 0) {
            this.$message({
              message: '正在开启题目环境',
              type: 'success'
            })
            this.$emit('call', 'refresh')
            this.close()
          }
        }).catch(() => {
          this.loading = false
          this.close()
        })
      } else {
        const params = {
          classCode: this.data[0].resultList[0].classCode,
          schedulingCode: this.data[0].resultList[0].schedulingCode,
          curriculumCode: this.data[0].resultList[0].curriculumCode,
          sceneInstanceId: this.data[0].sceneInstanceId,
          schedulingType: this.data[0].schedulingType
        }
        closeTrainingEnv(params).then((res) => {
          if (res.code === 0) {
            this.$message({
              message: '正在关闭环境',
              type: 'success'
            })
            this.$emit('call', 'refresh')
            this.close()
          }
        }).catch(() => {
          this.loading = false
        })
      }
    }
  }
}
</script>
