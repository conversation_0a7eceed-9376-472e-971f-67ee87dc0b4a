<template>
  <div v-loading="loading" class="dialog-wrap">
    <Attachment :query-id="id" style="padding: 0;"/>
    <div class="dialog-footer">
      <el-button type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>
<script>
import Attachment from '@/views/admin/hrm/content/create/Attachment.vue'

export default {
  components: {
    Attachment
  },
  props: {
    id: [String, Number]
  },
  data() {
    return {
      loading: false
    }
  },
  mounted() {
  },
  methods: {
    confirm() {
      this.$emit('call', 'close')
    }
  }
}
</script>
<style lang="scss" scoped>
</style>
