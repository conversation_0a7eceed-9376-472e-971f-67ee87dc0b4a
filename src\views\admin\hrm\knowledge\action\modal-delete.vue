<template>
  <div v-loading="loading" class="dialog-wrap">
    <batch-template
      :data="data"
      :available-data="availableData"
      :show-delete-warning="true"
      view-key="knowledgeName"
      post-key="knowledgeCode"
    />
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button :disabled="!availableData.length" type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>

<script>
import batchTemplate from '@/packages/batch-delete/modal-bat-template.vue'
import modalMixins from '@/packages/mixins/modal_form'
import { dropAPI } from '@/api/admin/training/knowledge'

export default {
  components: {
    batchTemplate
  },
  mixins: [modalMixins],
  props: {
    data: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      loading: false
    }
  },
  computed: {
    availableData: function() {
      const tempArr = []
      this.data.forEach((item) => {
        const associationType = ['taskCount', 'questionCount', 'curriculumCount']
        if (associationType.every(val => item[val] === 0)) {
          tempArr.push(item)
        }
      })
      return tempArr
    }
  },
  methods: {
    close() {
      this.$emit('close')
    },
    confirm: function() {
      this.loading = true
      const postData = this.availableData.map((item) => {
        return item.knowledgeCode
      })
      dropAPI(postData).then(res => {
        this.$message.success('删除成功')
        this.$emit('call', 'refresh')
        this.close()
      }).catch(() => {
        this.loading = false
      })
    }
  }
}
</script>
