<template>
  <div v-loading="loading" class="dialog-wrap">
    <el-alert :closable="false" type="warning">
      <div slot="title">
        <p>内容所在课程被排课后无法删除，若内容所在课程被学员加入自学列表，删除后学员侧将同步删除！</p >
      </div>
    </el-alert>
    <batch-template
      :data="data"
      :available-data="availableArr"
      view-key="name"
      post-key="id"
    />
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button :disabled="!availableArr.length" type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>

<script>
import batchTemplate from '@/packages/batch-delete/modal-bat-template.vue'
import modalMixins from '@/packages/mixins/modal_form'
import { deleteContent } from '@/api/teacher/index.js'

export default {
  components: {
    batchTemplate
  },
  mixins: [modalMixins],
  props: {
    data: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      loading: false
    }
  },
  computed: {
    // 筛选返回可操作数据
    'availableArr': function() {
      const _data = []
      this.data.forEach((item) => {
        if (!item.inCourse) {
          _data.push(item)
        }
      })
      return _data
    }
  },
  methods: {
    close() {
      this.$emit('close')
    },
    saveJoinCourse(postData) {
      return new Promise((resolve, reject) => {
        deleteContent(postData).then(res => {
          resolve(res)
        })
      })
    },
    confirm: function() {
      const idArr = this.data.map(item => {
        return { id: item.id }
      })
      idArr.map((item, index) => {
        this.saveJoinCourse({ id: item.id })
          .then((res) => {
            this.$message.success(res.data)
          })
      })
      setTimeout(() => {
        this.$emit('call', 'refresh')
        this.close()
        this.loading = false
      }, 500)
    }
  }
}
</script>
