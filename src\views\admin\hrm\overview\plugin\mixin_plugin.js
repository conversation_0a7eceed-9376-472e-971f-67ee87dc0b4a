export default {
  props: {
    data: Object
  },
  data() {
    return {
      loading: false,
      apiData: null,
      timeParam: null,
      role: undefined,
      roleMapping: {
        'Tester': 1,
        'managerIndex': 2
      },
      arrName: [],
      compareName: ''
    }
  },
  watch: {

  },
  computed: {
    'pluginTitle': function() {
      if (this.data.pluginConfig.timeApiKey) {
        if (this.data.pluginConfig.timeApiKey !== 'no_limit') {
          if (!this.data.pluginConfig.pluginName.includes(this.data.pluginConfig.timeName)) {
            this.data.pluginConfig.pluginName = this.data.pluginConfig.timeName + this.data.pluginConfig.pluginName
          }
        }
      }
      return this.data.pluginConfig.pluginName || '暂无'
    },
    'pluginApiType': function() {
      return this.data.pluginConfig.pluginApiKey || null
    }
  },
  created() {
    if (this.$route.name) {
      this.role = this.roleMapping[this.$route.name]
    }
    this.getData()
  }
}
