<template>
  <div>
    <el-tabs v-model="tabsActive" class="content-subs" type="card" style="margin-top: 20px;" @tab-click="handleTabClick">
      <el-tab-pane label="学员管理" name="studentManage">
        <router-link :to="{ name: 'studentManage' }" />
      </el-tab-pane>
      <el-tab-pane label="班级管理" name="classManage">
        <router-link :to="{ name: 'classManage' }" />
      </el-tab-pane>
      <el-tab-pane label="教师管理" name="teacherManage">
        <router-link :to="{ name: 'teacherManage' }" />
      </el-tab-pane>
      <el-tab-pane label="助教管理" name="assistantManage">
        <router-link :to="{ name: 'assistantManage' }" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
export default {
  data() {
    return {
      tabsActive: ''
    }
  },
  created() {
    this.tabsActive = this.$route.name
  },
  methods: {
    'handleTabClick': function(data) {
      this.$router.push({ name: data.name })
    }
  }
}
</script>
