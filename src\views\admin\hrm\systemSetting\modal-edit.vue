<template>
  <div v-loading="loading" class="dialog-wrap">
    <component ref="formWrap" :is="name" :data="data" edit-mode />
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>
<script>
import licenseNotice from './form/license-notice'
import multiSession from './form/platform-session'
import introduce from './form/introduce'
import { updateAdminConfigValue } from '@/api/admin/systemSettings'
export default {
  components: {
    licenseNotice,
    multiSession,
    introduce
  },
  props: {
    // 传入数据
    data: {
      type: Object,
      default: () => null
    },
    name: {
      type: String
    }
  },
  data() {
    return {
      loading: false
    }
  },
  methods: {
    close: function() {
      this.$emit('close')
    },
    confirm: function() {
      this.$refs['formWrap'].$refs['form'].validate((valid) => {
        if (valid) {
          this.loading = true
          const postData = JSON.stringify(this.$refs['formWrap'].formData)
          const configValue = {
            'licenseNotice': 'licenseAlert',
            'multiSession': 'multiSession',
            'introduce': 'introduce'
          }
          if (this.name == 'multiSession') {
            updateAdminConfigValue(configValue[this.name], { value: JSON.parse(postData).value }).then(res => {
              this.$message.success(`编辑成功`)
              this.$emit('call', 'refresh')
              this.close()
            }).catch(() => {
              this.loading = false
            })
          } else {
            updateAdminConfigValue(configValue[this.name], { value: postData }).then(res => {
              this.$message.success(`编辑成功`)
              this.$emit('call', 'refresh')
              this.close()
            }).catch(() => {
              this.loading = false
            })
          }
        }
      })
    }
  }
}
</script>
