/**
 * 数学运算
 */

/**
 * 两个浮点数求和
 * <AUTHOR>
 * @param num1
 * @param num2
 * @return {number}
 */
export function accAdd(num1, num2) {
  // eslint-disable-next-line one-var
  let r1 = 0, r2 = 0
  try {
    r1 = (num1.toString().split('.')[1] || []).length
  } catch (e) {
    return num1 + num2
  }
  try {
    r2 = (num2.toString().split('.')[1] || []).length
  } catch (e) {
    return num1 + num2
  }
  const m = Math.pow(10, Math.max(r1, r2))
  return Math.round(accMul(num1, m) + accMul(num2, m)) / m
}

/**
 * 两个浮点数相减
 * <AUTHOR>
 * @param num1
 * @param num2
 * @return {number}
 */
export function accSub(num1, num2) {
  let r1, r2
  try {
    r1 = (num1.toString().split('.')[1] || []).length
  } catch (e) {
    return num1 - num2
  }
  try {
    r2 = (num2.toString().split('.')[1] || []).length
  } catch (e) {
    return num1 - num2
  }
  const m = Math.pow(10, Math.max(r1, r2))
  return Math.round(accMul(num1, m) - accMul(num2, m)) / m
}

/**
 * 两个浮点数相乘
 * <AUTHOR>
 * @param num1
 * @param num2
 * @return {number}
 */
export function accMul(num1, num2) {
  let m = 0
  const s1 = num1.toString()
  const s2 = num2.toString()
  try {
    m += (s1.split('.')[1] || []).length
  } catch (e) {
    return num1 * num2
  }
  try {
    m += (s2.split('.')[1] || []).length
  } catch (e) {
    return num1 * num2
  }
  return Number(s1.replace('.', '')) * Number(s2.replace('.', '')) / Math.pow(10, m)
}

/**
 * 两个浮点数相除
 * <AUTHOR>
 * @param num1
 * @param num2
 * @return {number}
 */
export function accDiv(num1, num2) {
  let m = 0
  const s1 = num1.toString()
  const s2 = num2.toString()
  try {
    m += (s1.split('.')[1] || []).length
  } catch (e) {
    return num1 / num2
  }
  try {
    m += (s2.split('.')[1] || []).length
  } catch (e) {
    return num1 / num2
  }
  const n1 = accMul(num1, Math.pow(10, m))
  const n2 = accMul(num2, Math.pow(10, m))
  return Number(n1) / Number(n2)
}

/**
 * 按分数从高到低竞争排名
 */
export function getAccRaceRanking(arr) {
  arr.sort((a, b) => b.totalScore - a.totalScore)
  // 初始化排名
  let rank = 1
  let rankIncrement = 1
  for (let i = 0; i < arr.length; i++) {
    // 如果当前分数与前一个分数不同，则更新排名和排名增量
    if (i > 0 && arr[i].totalScore !== arr[i - 1].totalScore) {
      rank += rankIncrement
      rankIncrement = 1
    }
    // 为当前学生设置排名
    arr[i].ranking = rank
    // 如果当前分数与前一个分数相同，增加排名增量
    if (i > 0 && arr[i].totalScore === arr[i - 1].totalScore) {
      rankIncrement++
    }
  }
  return arr
}
