<template>
  <div v-loading="loading" class="drawer-wrap orchestration-drawer-wrap">
    <div class="drawer-wrap-content">
      <el-form ref="form" :model="formData" :rules="rules" label-width="140px">
        <el-form-item label="图形设备名称" prop="deviceName">
          {{ formData.deviceName }}
        </el-form-item>
        <el-form-item label="图形名称" prop="name">
          <el-input :disabled="disabledType" v-model.trim="formData.name"/>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input :disabled="disabledType" v-model.trim="formData.description" type="textarea"/>
        </el-form-item>
      </el-form>
    </div>
    <div class="drawer-footer">
      <el-button :disabled="disabledType" type="primary" @click="confirm">确定</el-button>
      <el-button type="text" @click="close">取消</el-button>
    </div>
  </div>
</template>
<script>
import validate from '../../validate'
export default {
  components: {},
  props: {
    data: {
      type: Object,
      default: () => {
        return {}
      }
    },
    type: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      loading: false,
      validate: validate,
      formData: {
        'name': '',
        'deviceName': '',
        'description': ''
      },
      rules: {
        'name': [
          validate.required(),
          validate.base_name
        ],
        'description': [
          validate.description
        ]
      }
    }
  },
  computed: {
    disabledType() {
      return this.type !== 'allPermissions' && this.type !== 'templatePermissions'
    }
  },
  created() {
    const data = this.data.node.data
    this.formData['name'] = data['name']
    this.formData['deviceName'] = data['deviceName']
    this.formData['description'] = data['description'] || ''
  },
  methods: {
    'close': function() {
      this.$emit('close')
    },
    'confirm': function() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          const postData = {
            'name': this.formData['name'],
            'description': this.formData['description']
          }
          this.$emit('call', 'configNode', postData)
          this.close()
        }
      })
    }
  }
}
</script>
<style lang="less">
.orchestration-drawer-wrap {
  .el-form {
    & >.el-form-item > .el-form-item__content > .el-input,
    & >.el-form-item > .el-form-item__content > .el-select,
    & >.el-form-item > .el-form-item__content > .el-textarea {
      width: 90%;
    }
  }
  .port-form {
    th {
      padding: 0;
    }
    >.el-form-item__label {
      width: 70px !important;
    }
    >.el-form-item__content {
      margin-left: 70px !important;
      // margin-top: 40px;
      .data-table-footer {
        display: none;
      }
    }
  }
}
</style>
