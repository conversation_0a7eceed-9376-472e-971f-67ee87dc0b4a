<template>
  <div class="content-wrap-layout">
    <top-nav />
    <div class="vertical-wrap">
      <page-table
        ref="table"
        :default-selected-arr="defaultSelectedArr"
        :cache-pattern="true"
        :filter-data="{}"
        @refresh="refresh"
        @link-event="linkEvent"
        @on-select="tabelSelect"
        @on-current="tabelCurrent"
        @transmitTime="transmitTime"
      >
        <action-menu
          slot="action"
          :module-name="moduleName"
          :select-item="selectItem"
          :time-params="timeParams"
          @call="actionHandler"
        />
      </page-table>
    </div>
  </div>
</template>

<script>
import moduleConf from './config'
import topNav from '../index_top_nav'
import pageTable from './table/index.vue'
import actionMenu from './action/index.vue'
export default {
  name: 'AssistantManage',
  components: {
    topNav,
    pageTable,
    actionMenu
  },
  data() {
    return {
      moduleName: moduleConf.name,
      selectItem: [],
      defaultSelectedArr: [],
      timeParams: {}
    }
  },
  methods: {
    // 列表点击
    linkEvent: function({ name, row, params }) {
      this.$router.push({ name: name, params: params })
    },
    // 返回已选
    tabelSelect: function(data) {
      console.log(data)
      this.selectItem = data
    },
    // 返回单选
    tabelCurrent: function(row) {
      this.selectItem = [row]
    },
    transmitTime(val) {
      this.timeParams = val
    },

    // action menu 事件
    actionHandler: function(type, data) {
      switch (type) {
        case 'refresh':
          this.$refs['table'].getList()
      }
    },
    refresh: function() {
    }
  }
}
</script>
