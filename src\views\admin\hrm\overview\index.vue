<template>
  <div :class="{'edit-mode': editModel}" class="overview-wrap dark scroll">
    <div class="overview-title">
      <div v-if="editModel" class="edit-toolbar">
        <div class="edit-header">
          <h3>自定义概览</h3>
          <div style="float: right;">
            <el-button type="text" @click="cancelCustomView">取消</el-button>
            <el-button type="default" @click="reloadView">恢复默认</el-button>
            <el-button type="primary" @click="saveView">保存</el-button>
          </div>
        </div>
        <div class="edit-bar">
          <i class="el-icon-warning-outline" size="16" style="color:#2d8cf0;position: relative;top: 2px;" /><span class="font-14 ml-5" style="vertical-align: middle;">您可按需添加、移除模块，以及拖动卡片进行自定义布局。</span>
          <el-button type="primary" icon="el-icon-plus" class="add-btn" @click="modalAddOpen">添加模块</el-button>
        </div>
      </div>
      <div v-else class="overview-toolbar">
        <span>当前身份：{{ userInfo && userInfo.roleName }}</span>
        <el-button class="mr-15" type="text" icon="el-icon-edit" style="float: right;color: #000;" @click="customView">自定义</el-button>
      </div>
    </div>
    <div :class="{'edit-mode': editModel}" class="overview-content dark scroll">
      <div class="overview-view">
        <div v-if="!editModel && layout.length === 0" class="no-data-view">
          <div class="con">
            <div class="help-text">暂未添加内容模块</div>
            <el-button type="primary" icon="md-create" @click="customView">自定义概览</el-button>
          </div>
        </div>
        <grid-layout
          ref="gridlayout"
          :layout.sync="layoutReverse"
          :col-num="24"
          :row-height="10"
          :is-draggable="editModel"
          :is-resizable="editModel"
          :is-mirrored="false"
          :vertical-compact="true"
          :margin="[20, 20]"
          :use-css-transforms="false"
          @layout-updated="layoutUpdatedEvent"
        >
          <grid-item
            v-for="(item) in layoutReverse"
            :x="item.x"
            :y="item.y"
            :w="item.w"
            :h="item.h"
            :i="item.i"
            :is-resizable = "false"
            :key="item.pluginConfig.pluginApiKey"
          >
            <div class="plugin-wrap">
              <a href="javascript:;" class="handler-btn delete" @click="deletePlugin(item.i)"><i class="el-icon-close" size="16"/></a>
              <component :is="item.plugin" :data="item" :person-data="Object.assign({}, apiData)" @getPerson="getPerson()"/>
            </div>
          </grid-item>
        </grid-layout>
      </div>
      <el-dialog
        :visible.sync="modalAdd"
        v-model="modalAdd"
        width="30%"
        title="添加模块"
        @close="addCancel">
        <div class="modal-wrap">
          <div
            v-for="item in pluginListModal"
            :class="{'active': addPlugin === item.plugin }"
            :key="item.plugin"
            class="overview-plugin-item"
            @click="selectPlugin(item)">
            <i :class="{ 'el-icon-star-on': addPlugin === item.plugin, 'el-icon-star-off': addPlugin !== item.plugin }" class="item-radio" size="18" />
            <h3>{{ item.pluginCnName }}</h3>
            <div class="info">{{ item.pluginExplain }}</div>
            <div v-if="item.pluginConfig && addPlugin === item.plugin" class="data-config">
              <div
                v-for="configItem in item.pluginConfig"
                :key="configItem.key"
                class="data-config-item">
                <span class="data-config-item-title">{{ configItem.label }}</span>
                <el-select v-model="configItem.value" class="data-config-item-select">
                  <el-option
                    v-for="(selectItemVal, selectItemKey) in configItem.list"
                    :disabled="addedKeys.includes(selectItemKey)"
                    :value="selectItemKey"
                    :label="selectItemVal"
                    :key="selectItemKey">{{ selectItemVal }}</el-option>
                </el-select>
              </div>
            </div>
          </div>
          <div class="dialog-footer" style="margin: 24px -14px -24px;">
            <el-button type="text" size="large" @click="addCancel">取消</el-button>
            <el-button :disabled="addDisabled" type="primary" size="large" @click="addOk">确定</el-button>
          </div>
        </div>
      </el-dialog>
    </div>
  </div>
</template>
<style lang="scss">
  .overview-wrap {
    margin: 0;
    background-color: #f0f4f9;
    height: 100%;
    display: flex;
    flex-direction: column;
    .overview-title {
      overflow: hidden;
    }
    .overview-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: auto;
    }
    .edit-toolbar {
      .edit-bar {
        height: 44px;
        line-height: 30px;
        padding: 5px 20px 5px 25px;
        background-color: #fff;
        border-bottom: solid 1px #DBDDE0;
        .add-btn {
          font-size: 14px;
          float: right;
          span {
            vertical-align: middle;
          }
        }
      }
      .edit-header {
        background-color: #fff;
        border-bottom: solid 1px #f0f4f9;
        border-top: solid 1px #f0f4f9;
        padding: 10px 20px 10px 25px;
        h3 {
          line-height: 32px;
          display: inline-block;
        }
      }
    }
    .overview-toolbar {
      font-size: 14px;
      color: #000;
      padding: 8px 5px 8px 25px;
      background-color: #fff;
      border-bottom: solid 1px #DBDDE0;
      >span {
        display: inline-block;
        height: 32px;
        line-height: 32px;
      }
    }
    .overview-view {
      flex: 1;
      margin-top: -5px;
      position: relative;
      min-width: 1200px;
    }
    .no-data-view {
      position: absolute;
      top: 0;
      left: 0;
      z-index: 8;
      width: 100%;
      height: 100%;
      .con {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%,-50%);
        text-align: center;
        .help-text {
          margin-bottom: 10px;
          font-size: 14px;
        }
      }
    }
    .plugin-wrap {
      height: 100%;
      width: 100%;
      position: relative;
      background-color: #fff;
      border-radius: 4px;
      box-shadow: 0px 2px 4px 0px rgba(0,0,0,.08);
      .delete {
        position: absolute;
        right: 10px;
        top: 10px;
        z-index: 10;
        visibility: hidden;
      }
    }
    .plugin-view {
      height: 100%;
      padding: 20px;
      position: relative;
      display: flex;
      flex-direction: column;
      .plugin-title {
        font-size: 16px;
        color: #333;
        font-weight: normal;
        margin-bottom: 24px;
        white-space:nowrap;
        // overflow: hidden;
        text-overflow:ellipsis;
      }
      .plugin-loading {
        flex: 1;
      }
      .nodata-spin {
        // display: flex;
        // justify-content: center;
        background-color: rgba(255,255,255,0);
        .ivu-spin-dot {
          display: none;
        }
        .nodata-text {
          color: #999;
          font-size: 14px;
        }
      }
    }
    &.edit-mode {
      .plugin-wrap {
        &:hover {
          .delete {
            visibility: visible;
          }
        }
      }
    }
  }
  .overview-plugin-item {
    position: relative;
    width: 100%;
    padding: 12px 20px;
    border: 1px solid #DBDDE0;
    border-radius: 2px;
    margin-bottom: 10px;
    cursor: pointer;
    .item-radio {
      position: absolute;
      right: 10px;
      top: 10px;
    }
    h3 {
      color: #333;
    }
    &.active {
      border-color: var(--color-600);
      box-shadow: 0 0 0 2px #E6F6FF;
    }
    &.disabled {
      pointer-events: none;
      background-color: #f7f7f7;
      .data-config {
        display: none;
      }
      h3,.info {
        color: #999;
      }
    }
    .data-config {
      margin-top: 10px;
      border-top: solid 1px #DBDDE0;
      .data-config-item {
        display: flex;
        margin-top: 10px;
        flex-flow: row wrap;
        .data-config-item-title {
          flex-grow: 0;
          font-size: 13px;
          color: #333;
          line-height: 32px;
          width: 120px;
        }
        .data-config-item-select {
          flex: 1;
        }
      }
    }
  }
  .modal-footer {
    display: flex;
    justify-content: end;
  }
</style>
<script>
import { mapGetters } from 'vuex'
import VueGridLayout from 'vue-grid-layout'
import mixinAdd from './mixin_add'
import pluginResourcesPercent from './plugin/plugin_resources_percent.vue'
import pluginQuantity from './plugin/plugin_quantity.vue'
import pluginTopN from './plugin/plugin_top_n.vue'
import pluginLoadTrend from './plugin/plugin_load_trend.vue'
import {
  getPersonnelManagement,
  layoutSave,
  layoutInfo
} from '@/api/teacher/index.js'
export default {
  components: {
    GridLayout: VueGridLayout.GridLayout,
    GridItem: VueGridLayout.GridItem,
    pluginResourcesPercent,
    pluginQuantity,
    pluginTopN,
    pluginLoadTrend
  },
  mixins: [
    mixinAdd
  ],
  data() {
    return {
      index: null,
      editModel: false,
      // 组件注册列表
      pluginList: [
        { 'w': 8, 'h': 8,
          'plugin': 'pluginQuantity',
          'pluginCnName': '资源数量统计',
          'pluginExplain': '从不同维度展示各类资源的数量',
          'pluginConfig': {
            'type': {
              'label': '资源类型',
              'value': null,
              'list': {
                'completed_tasks': '实训用户',
                'total_tasks': '课程内容',
                'total_score': '教学专业及班级'
              }
            },
            'times': {
              'label': '时间范围',
              'value': null,
              'list': {
                'no_limit': '不限'
              }
            }
          }
        },
        { 'w': 8, 'h': 8,
          'plugin': 'pluginResourcesPercent',
          'pluginCnName': '资源状态统计',
          'pluginExplain': '各类资源总数量及不同状态资源数量的统计信息',
          'pluginConfig': {
            'type': {
              'label': '资源类型',
              'value': null,
              'list': {
                'personal_tasks': '课程资源'
              }
            },
            'times': {
              'label': '时间范围',
              'value': null,
              'list': {
                'no_limit': '不限'
              }
            }
          }
        },
        { 'w': 12, 'h': 17,
          'plugin': 'pluginLoadTrend',
          'pluginCnName': '资源趋势',
          'pluginExplain': '从不同维度展示各类资源的趋势',
          'pluginConfig': {
            'type': {
              'label': '资源类型',
              'value': null,
              'list': {
                'grade_distribution': '近一年内班级模拟练习成绩趋势',
                'grade_distribution2': '近一年内班级项目成绩趋势'
              }
            }
          }
        },
        { 'w': 8, 'h': 8,
          'plugin': 'pluginTopN',
          'pluginCnName': '资源排行',
          'pluginExplain': '从不同维度展示各类资源的Top排行',
          'pluginConfig': {
            'type': {
              'key': 'type',
              'label': '资源类型',
              'value': null,
              'list': {
                'team_points': '热门自学课程top5',
                'penetration_test_top': '热门排课课程top5'
              }
            }
          }
        }
      ],
      layout: [],
      oldLayout: [],
      defaultLayout: [
        { 'h': 8, 'w': 8, 'plugin': 'pluginQuantity', 'pluginConfig': { 'pluginName': '课程内容', 'pluginApiKey': 'total_tasks', 'timeName': '', 'timeApiKey': 'Last_month' }, 'x': 16, 'y': 0, 'i': 2, 'moved': false },
        { 'h': 8, 'w': 8, 'plugin': 'pluginQuantity', 'pluginConfig': { 'pluginName': '实训用户', 'pluginApiKey': 'completed_tasks', 'timeName': '', 'timeApiKey': 'Last_month' }, 'x': 0, 'y': 0, 'i': 3, 'moved': false },
        { 'h': 8, 'w': 8, 'plugin': 'pluginResourcesPercent', 'pluginConfig': { 'pluginName': '课程资源', 'pluginApiKey': 'personal_tasks', 'timeName': '资源状态统计', 'timeApiKey': null }, 'x': 8, 'y': 0, 'i': 4, 'moved': false },
        { 'h': 17, 'w': 12, 'plugin': 'pluginLoadTrend', 'pluginConfig': { 'pluginName': '近一年内班级模拟练习成绩趋势', 'pluginApiKey': 'grade_distribution', 'timeName': '资源趋势', 'timeApiKey': null }, 'x': 0, 'y': 8, 'i': 5, 'moved': false },
        { 'h': 17, 'w': 12, 'plugin': 'pluginLoadTrend', 'pluginConfig': { 'pluginName': '近一年内班级项目成绩趋势', 'pluginApiKey': 'grade_distribution2', 'timeName': '', 'timeApiKey': 'projectKey' }, 'x': 12, 'y': 8, 'i': 6, 'moved': false },
        { 'h': 8, 'w': 8, 'plugin': 'pluginQuantity', 'pluginConfig': { 'pluginName': '教学专业及班级', 'pluginApiKey': 'total_score', 'timeName': '', 'timeApiKey': 'Last_month' }, 'x': 0, 'y': 32, 'i': 9, 'moved': false },
        { 'h': 8, 'w': 8, 'plugin': 'pluginTopN', 'pluginConfig': { 'pluginName': '热门自学课程top5', 'pluginApiKey': 'team_points', 'timeName': '资源排行', 'timeApiKey': null }, 'x': 8, 'y': 32, 'i': 12, 'moved': false },
        { 'h': 8, 'w': 8, 'plugin': 'pluginTopN', 'pluginConfig': { 'pluginName': '热门排课课程top5', 'pluginApiKey': 'penetration_test_top', 'timeName': '资源排行', 'timeApiKey': null }, 'x': 16, 'y': 32, 'i': 13, 'moved': false }],
      apiData: {
        assistants: 0,
        classNumber: 0,
        majorNumber: 0,
        noOpenCourse: 0,
        openCourse: 0,
        simulationCourse: 0,
        students: 0,
        teachers: 0,
        theoryCourse: 0
      }
    }
  },
  computed: {
    ...mapGetters(['userInfo']),
    'layoutReverse': function() {
      return this.layout
    },
    'layoutIndexMax': function() {
      const out = []
      this.layout.forEach((item) => {
        out.push(item['i'])
      })
      return out.length ? Math.max(...out) : 0
    }
  },
  created() {
    this.getView()
    this.getPerson()
  },
  methods: {
    'getView': function() {
      const roleId = this.userInfo.roleId
      layoutInfo(roleId).then(res => {
        console.log(res.data, 'resdatadata')
        if (res.data.layout) {
          this.layout = JSON.parse(res.data.layout)
          this.index = this.layout.length
        } else {
          this.layout = JSON.parse(JSON.stringify(this.defaultLayout))
          this.index = this.layout.length
        }
      })
    },
    getPerson() {
      getPersonnelManagement().then((res) => {
        if (res.code == 0) {
          this.apiData = { ...res.data }
        }
      })
    },
    'saveView': function() {
      this.editModel = !this.editModel
      const postData = {
        roleId: this.userInfo.roleId,
        layout: JSON.stringify(this.layout)
      }
      layoutSave(postData)
        .then(res => {
          this.$message.success('自定义概览保存成功')
        })
    },
    'reloadView': function() {
      this.layout = JSON.parse(JSON.stringify(this.defaultLayout))
    },
    'cancelCustomView': function() {
      this.editModel = !this.editModel
      this.layout = JSON.parse(JSON.stringify(this.oldLayout))
    },
    'customView': function() {
      this.editModel = !this.editModel
      this.oldLayout = JSON.parse(JSON.stringify(this.layout))
    },
    'layoutUpdatedEvent': function() {
    },
    'deletePlugin': function(id) {
      for (let i = 0; i <= this.layout.length; i++) {
        if (this.layout[i].i === id) {
          this.layout.splice(i, 1)
          break
        }
      }
    },
    'addPluginHandel': function(item) {
      this.layout.unshift(Object.assign(item, {
        x: 0,
        y: 0,
        i: this.layoutIndexMax + 1
      }))
      this.index++
    }
  }
}
</script>
