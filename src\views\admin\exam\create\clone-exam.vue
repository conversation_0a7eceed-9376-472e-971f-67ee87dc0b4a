<template>
  <create-view :loading="loading" title="克隆考试">
    <el-form slot="content" ref="form" :model="formData" :rules="rules" label-position="left" label-width="150px">
      <el-card>
        <el-form-item label="考试名称" prop="examName">
          <el-input v-model.trim="formData.examName" show-word-limit/>
        </el-form-item>
        <el-form-item label="证书名称">
          <el-select v-model="formData.credentialName" prop="credentialName">
            <el-option
              v-for="item in credentials"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="证书等级" prop="level">
          <el-select v-model="formData.level" class="w-33">
            <el-option
              v-for="item in examLevel"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="考试试卷" prop="selectedExamPaper">
          <el-tag
            v-if="formData.selectedExamPaper"
            :disable-transitions="true"
            closable
            @click="drawerName = 'selectedExamPaper'"
            @close="formData.selectedExamPaper = null">
            {{ formData.selectedExamPaper[0].examName || formData.selectedExamPaper }}
          </el-tag>
          <el-button v-else type="ghost" @click="drawerName = 'selectedExamPaper'">选择试卷</el-button>
        </el-form-item>
        <el-form-item label="报名时间" prop="startEndTime">
          <div class="block w-66">
            <el-date-picker
              v-model="formData.startEndTime"
              :picker-options="pickerOptions"
              :default-time="['00:00:00', '23:59:59']"
              type="datetimerange"
              size="small"
              align="center"
              format="yyyy-MM-dd HH:mm:ss"
              value-format="yyyy-MM-dd HH:mm:ss"
              range-separator="-"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              @change="startEndTimeChange($event, formData.startEndTime)"
            />
          </div>
        </el-form-item>
        <el-form-item label="考试开始时间" prop="date1">
          <div class="block w-33">
            <el-date-picker
              v-model="formData.date1"
              :picker-options="pickerOptions"
              align="center"
              format="yyyy-MM-dd HH:mm:ss"
              value-format="yyyy-MM-dd HH:mm:ss"
              type="datetime"
              placeholder="开始日期"
              prefix-icon="-"
              @change="enterDateChange($event, formData.date1)"
            />
          </div>
        </el-form-item>
        <el-form-item label="考试时长" prop="time">
          <el-input-number v-model="formData.time" :precision="2" :step="0.5" :min="0.5" :max="100" class="w-33"/> 小时
        </el-form-item>
        <el-form-item label="考试须知" prop="notice">
          <myEditor :content="formData.notice" :key="timer" @contentChange="contentChange" />
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            :autosize="{ minRows: 4, maxRows: 4 }"
            v-model.trim="formData.description"

            resize="none"
            type="textarea"
            maxlength="255"
            show-word-limit
            placeholder="请输入255字以内的备注"
          />
        </el-form-item>
      </el-card>
      <!-- 侧拉弹窗 start -->
      <el-drawer
        :title="titleMapping[drawerName]"
        :visible.sync="drawerShow"
        :size="drawerWidth"
        @close="drawerClose"
      >
        <transition name="el-fade-in-linear">
          <component
            :is="drawerName"
            :name="drawerName"
            @close="drawerClose"
            @call="drawerConfirmCall"
          />
        </transition>
      </el-drawer>
      <!-- 侧拉弹窗 end -->
    </el-form>
    <div slot="footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </div>
  </create-view>
</template>
<script>
import module from '../config.js'
import createView from '@/packages/create-view/index'
import validate from '@/packages/validate'
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
import myEditor from '@/components/HyEditor/index.vue'
import selectedExamPaper from '../action/select-exam-paper.vue'
import { queryById, examClone, queryCaCertificateApi } from '@/api/exam/index.js'
import moment from 'moment'

export default {
  components: {
    createView,
    myEditor,
    selectedExamPaper
  },
  mixins: [mixinsActionMenu],
  data() {
    return {
      drawerAction: ['selectedExamPaper'], // 需要侧拉打开的操作
      titleMapping: {
        'selectedExamPaper': '选择试卷'
      },
      loading: false,
      validate: validate,
      formData: {
        examName: 'CIE-DSE',
        credentialName: '',
        level: '',
        selectedExamPaper: '',
        date1: '',
        date2: '',
        time: '',
        startEndTime: [],
        notice: '',
        description: '',
        endTime: ''
      },
      rules: {
        examName: [validate.required(), validate.base_name],
        credentialName: [validate.required()],
        level: [validate.required()],
        selectedExamPaper: [validate.required()],
        date1: [{ required: true, message: '请选择时间', trigger: 'change' }, { validator: this.validatedDate1, trigger: 'change' }],
        time: [validate.required(), { validator: this.timeValidator, trigger: 'blur' }],
        startEndTime: [{ required: true, message: '请选择时间', trigger: 'change' }, { validator: this.validatedStartEndTime, trigger: 'change' }],
        notice: [validate.required()]
      },
      paperId: '',
      paperName: '',
      credentials: [],
      examLevel: module.examLevel,
      timeList: module.timeList,
      timer: '1000',
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() <= (Date.now() - 24 * 60 * 60 * 1000)
        }
      }
    }
  },
  created() {
    this.getEaxmById()
    this.queryCaCertificate()
  },
  methods: {
    validatedStartEndTime(rule, value, callback) {
      const regEndTimeStamp = new Date(this.formData.startEndTime[1]).getTime()
      const startTimeStamp = new Date(this.formData.date1 + ' ' + this.formData.date2).getTime()
      if (value === '') {
        callback(new Error('请安排时间'))
      } else if (startTimeStamp < regEndTimeStamp) {
        callback(new Error('请合理安排时间'))
      } else {
        callback()
      }
    },
    validatedDate1(rule, value, callback) {
      const regEndDate = this.dateFilter(this.formData.startEndTime[1])
      const regEndDateStamp = new Date(regEndDate).getTime()
      const startDateStamp = new Date(this.formData.date1).getTime()
      if (value === '') {
        callback(new Error('请安排时间'))
      } else if (startDateStamp < regEndDateStamp) {
        callback(new Error('请合理安排时间'))
      } else {
        callback()
      }
    },
    timeValidator(rule, value, callback) {
      const reg = /^([0-9]|[1-9][0-9])(\.5)?$/
      if (value === '') {
        callback(new Error('必填项'))
      } else if (!reg.test(value)) {
        callback(new Error('请正确输入时长'))
      } else {
        callback()
      }
    },
    getEaxmById() {
      const id = Number(this.$route.query.examId)
      queryById({ id: id }).then((res) => {
        if (res.code === 0) {
          const distanceTime = moment(res.data.endTime).diff(moment(res.data.beginTime), 'hours')
          this.formData.examName = res.data.name
          this.formData.credentialName = res.data.certificateName
          this.formData.level = res.data.certificateLevel
          this.formData.selectedExamPaper = res.data.paperName
          this.paperId = res.data.paperId
          this.paperName = res.data.paperName
          this.formData.date1 = res.data.beginTime
          this.formData.time = distanceTime
          this.formData.startEndTime.push(res.data.regBeginTime)
          this.formData.startEndTime.push(res.data.regEndTime)
          this.formData.notice = res.data.description
          this.formData.description = res.data.mark
          this.level = this.examLevel[this.formData.level - 1].label
          this.timer = new Date().getTime()
        }
      })
    },
    queryCaCertificate() {
      queryCaCertificateApi().then((res) => {
        if (res.code === 0) {
          this.credentials = res.data
        }
      })
    },
    cloneDateFilter() {
      const now = new Date().getTime()
      const startTimeStamp = new Date(this.formData.date1 + ' ' + this.formData.date2).getTime()
      const regStartDate = this.dateFilter(this.formData.startEndTime[0])
      const regStartDateStamp = new Date(regStartDate).getTime()
      if (now > startTimeStamp) {
        this.formData.date1 = ''
        this.formData.date2 = ''
        this.formData.startEndTime = []
      }
      if (now > regStartDateStamp) {
        this.formData.startEndTime = []
      }
    },
    drawerConfirmCall: function(type, data) {
      if (type === 'close') {
        this.drawerClose()
      } else if (type === 'confirm_examPaper') {
        this.formData.selectedExamPaper = data
        this.drawerClose()
        this.$nextTick(() => {
          this.$refs['form'].clearValidate('selectedExamPaper')
        })
      }
    },
    dateFilter(time) {
      const y = new Date(time).getFullYear()
      let m = new Date(time).getMonth() + 1
      m = m < 10 ? ('0' + m) : m
      let d = new Date(time).getDate()
      d = d < 10 ? ('0' + d) : d
      let h = new Date(time).getHours()
      h = h < 10 ? ('0' + h) : h
      let M = new Date(time).getMinutes()
      M = M < 10 ? ('0' + M) : M
      let s = new Date(time).getSeconds()
      s = s < 10 ? ('0' + s) : s
      const dateTime = y + '-' + m + '-' + d + ' ' + h + ':' + M + ':' + s
      return dateTime
    },
    secondsToHHMMSS(seconds) {
      var hours = Math.floor(seconds / 3600)
      var minutes = Math.floor((seconds % 3600) / 60)
      var secs = seconds % 60

      return [
        hours,
        minutes < 10 ? '0' + minutes : minutes,
        secs < 10 ? '0' + secs : secs
      ].join(':')
    },
    endTimeFilter(date, time) {
      // 时间转秒
      const date1 = date.split(' ')[0]
      const date2 = date.split(' ')[1]
      const h = date2.split(':')[0]
      const m = date2.split(':')[1]
      const s = Number(h * 60 * 60) + Number(m * 60) + Number(date2.split(':')[2])

      const m1 = Number(time * 60 * 60) + s // 结束时间总秒数
      let time1 = this.secondsToHHMMSS(m1) // 秒转时间
      // 计算结束时间
      const tList = time1.split(':')
      if (tList[0] < 10) {
        const h2 = Number(tList[0]) % 24 < 10 ? '0' + Number(tList[0]) % 24 : Number(tList[0]) % 24
        tList.splice(0, 1, h2)
        time1 = tList.join(':')
      }

      const dList = date1.split('-')
      this.formData.endTime = date1 + ' ' + time1
      if (tList[0] >= 24) {
        const h2 = Number(tList[0]) % 24 < 10 ? '0' + Number(tList[0]) % 24 : Number(tList[0]) % 24
        tList.splice(0, 1, h2)
        time1 = tList.join(':')
        dList.splice(2, 1, Number(dList[2]) + 1)
        this.formData.endTime = dList.join('-') + ' ' + time1
      }
    },
    startEndTimeChange(event, row) {
      if (row && this.formData.date1 && this.formData.date2) {
        this.$refs['form'].clearValidate(['date1'])
      }
    },
    enterDateChange(event, row) {
      if (row && this.formData.startEndTime) {
        this.$refs['form'].clearValidate(['startEndTime'])
      }
    },
    contentChange(value) {
      if (this.filterHtml(value)) {
        this.formData.notice = value
      } else {
        const imgStrs = value.match(/<img.*?>/g)
        if (imgStrs && imgStrs.length) { // 内容只有图片时
          this.formData.notice = value
        } else {
          this.formData.notice = ''
        }
      }
      this.$nextTick(() => {
        this.$refs['form'].clearValidate('notice')
      })
    },
    // 过滤html代码、空格、回车 空白字符
    filterHtml(str) {
      str = str.replace(/<("[^"]*"|'[^']*'|[^'">])*>/gi, '')
      str = str.replace(/[\r\n]/g, '')
      str = str.replace(/\s/g, '')
      str = str.replace(/&nbsp;/ig, '')
      return str
    },
    close: function() {
      this.$router.go(-1)
    },
    confirm: function() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.loading = true
          const credentialsItem = this.credentials.find(item => item.id == this.formData.credentialName || item.name == this.formData.credentialName)
          const credentialsLevel = this.examLevel.find(item => item.value == this.formData.level)
          const endTime = moment(this.formData.date1).add(this.formData.time, 'hours').format('YYYY-MM-DD HH:mm:ss')
          this.formData.startEndTime[0] = this.dateFilter(this.formData.startEndTime[0])
          this.formData.startEndTime[1] = this.dateFilter(this.formData.startEndTime[1])
          const params = {
            name: this.formData.examName,
            id: Number(this.$route.query.examId),
            certificateId: credentialsItem.id,
            certificateName: credentialsItem.name,
            certificateLevel: credentialsLevel.value,
            paperId: this.formData.selectedExamPaper[0].id || this.paperId,
            paperName: this.formData.selectedExamPaper[0].examName || this.paperName,
            examStatus: 0,
            regBeginTime: this.formData.startEndTime[0],
            regEndTime: this.formData.startEndTime[1],
            beginTime: this.formData.date1,
            endTime: endTime,
            published: 0,
            description: this.formData.notice,
            mark: this.formData.description
          }
          examClone(params).then((res) => {
            if (res.code === 0) {
              this.$message({
                message: '克隆成功',
                type: 'success'
              })
              this.loading = false
              this.$emit('call', 'refresh')
              this.close()
            }
          }).catch(() => {
            this.loading = false
          })
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.el-input {
  width: 66.6%;
}
.el-input__inner {
  width: 66.6%;
}
.el-select {
  width: 66.6%;
}
.el-date-editor {
  width: 100%;
}
.w-33 {
  width: 33.3%;
}
.w-66 {
  width: 66.6%;
}
</style>
