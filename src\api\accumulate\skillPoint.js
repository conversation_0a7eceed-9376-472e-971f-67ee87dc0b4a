import request from '@/utils/request'
const headers = { 'Content-Type': 'application/json;charset=UTF-8' }

// 获取技能点
export function getSkillPoint(data) {
  return request({
    url: `admin/sysSkillPoints/queryPage`,
    method: 'post',
    headers,
    data
  })
}

// 新增技能点
export function addSkillPoint(data) {
  return request({
    url: 'admin/sysSkillPoints/create',
    method: 'post',
    headers,
    data
  })
}

// 编辑技能点
export function editSkillPoint(data) {
  return request({
    url: 'admin/sysSkillPoints/update',
    method: 'post',
    headers,
    data
  })
}

// 删除技能点
export function deleteSkillPoint(data) {
  return request({
    url: 'admin/sysSkillPoints/remove',
    method: 'post',
    headers,
    data
  })
}

// 获取拥有技能点的用户
export function getUserSkillPointList(data) {
  return request({
    url: 'admin/sysSkillPoints/queryPointsByUsers',
    method: 'post',
    headers,
    data
  })
}

// 获取用户的技能点
export function getUserSkillPoint(data) {
  return request({
    url: 'admin/sysSkillPoints/queryPointsByActivities',
    method: 'post',
    headers,
    data
  })
}

// 获取用户的技能点详情数值
export function getUserSkillPointInfo(data) {
  return request({
    url: 'admin/sysSkillPoints/queryPointDetailsByUserId',
    method: 'post',
    headers,
    data
  })
}

// 获取用户某项活动的技能点详情数值
export function getUserSkillPointDetailsInfo(data) {
  return request({
    url: 'admin/sysSkillPoints/queryPointDetailsByActivityId',
    method: 'post',
    headers,
    data
  })
}

// 导出用户所有能力评估信息
export function exportPointsByUsers(data) {
  return request({
    url: '/admin/sysSkillPoints/exportPointsByUsers',
    method: 'post',
    headers,
    data,
    responseType: 'blob'
  })
}

// 导出某个用户所有能力评估信息
export function exportPointsByActivities(data) {
  return request({
    url: '/admin/sysSkillPoints/exportPointsByActivities',
    method: 'post',
    headers,
    data,
    responseType: 'blob'
  })
}

// 技能点详情接口
export function getSkillPointDetailApi(data) {
  return request({
    url: 'admin/sysSkillPoints/queryQuestionPageBySkillPointId',
    method: 'post',
    headers,
    data
  })
}
