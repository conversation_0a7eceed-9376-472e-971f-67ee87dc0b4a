<template>
  <div class="drawer-wrap">
    <scene-table
      :custom-col-data="['sceneName', 'sceneDifficulty', 'nodeScale']"
      :filter-data="{}"
      :link="false"
      :single="true"
      :not-allowed-arr="[]"
      height="auto"
      @on-select="onSelect"
    />
    <div class="drawer-footer">
      <el-button :disabled="!selectedItem.length" type="primary" @click="confirm">确定</el-button>
      <el-button type="text" @click="close">取消</el-button>
    </div>
  </div>
</template>

<script>
import sceneTable from '@/views/admin/scenemanage/scene/table/index.vue'
export default {
  components: {
    sceneTable
  },
  data() {
    return {
      selectedItem: []
    }
  },
  methods: {
    'onSelect': function(data) {
      this.selectedItem = data
    },
    'close': function() {
      this.$emit('close')
    },
    'confirm': function() {
      this.$emit('call', 'confirm_scene', this.selectedItem)
    }
  }
}
</script>
