<template>
  <detail-view
    :loading="loading"
    :data="data"
    :id="id"
    :view-item="viewItem"
    mode="drawer"
    title-key="questionName"
  />
</template>
<script>
import moduleConf from '../config'
import detailView from '@/packages/detail-view/index'
import detailOverview from './detail-overview'
import detailTopo from './detail-topo'
import { getQuestionBankItem, getTrainingPoint } from '@/api/accumulate/questionBank'
export default {
  components: {
    detailView,
    detailOverview,
    detailTopo
  },
  data() {
    return {
      moduleName: moduleConf.name,
      id: null, // 资源ID
      data: null, // 资源数据对象
      trainingPointList: [],
      loading: true,
      viewItem: [
        {
          transName: '概况',
          name: 'overview',
          component: detailOverview
        }
      ]
    }
  },
  watch: {
    '$route': function(to, from) {
      const toId = to.params.hasOwnProperty('id') ? to.params.id : null
      const fromId = from.params.hasOwnProperty('id') ? from.params.id : null
      if (toId !== fromId) {
        this.loadBase()
      }
    }
  },
  created() {
    this.loadBase()
  },
  methods: {
    'loadBase': function() {
      this.id = this.$route.params.id
      this.loading = true
      const result = [this.getData(this.id), this.getTrainingPoint()]
      Promise.all(result).then(() => {
        if (this.data.uses.includes('1') && this.data.knowledgeCode) {
          const point = this.trainingPointList.find(val => val.knowledgeCode === this.data.knowledgeCode)
          this.data.knowledgeName = point ? point.knowledgeName : this.data.knowledgeName
        }
        if (this.data.bankType == 3 && !this.viewItem.find(item => item.name === 'topo')) {
          this.viewItem.push({
            transName: '拓扑',
            name: 'topo',
            component: detailTopo
          })
        }
        this.loading = false
      }).finally(() => {
        this.loading = false
      })
    },
    // 根据id获取详情数据
    'getData': function(id) {
      return new Promise((resolve, reject) => {
        getQuestionBankItem({ id: id }).then(res => {
          this.data = res['data']
          resolve()
        }).catch(() => {
          this.data = null
          reject()
        })
      })
    },
    // 获取实训知识点列表
    'getTrainingPoint': function() {
      return new Promise((resolve, reject) => {
        getTrainingPoint().then(res => {
          this.trainingPointList = res.data
          resolve()
        }).catch(() => {
          this.trainingPointList = []
          reject()
        })
      })
    }
  }
}
</script>
