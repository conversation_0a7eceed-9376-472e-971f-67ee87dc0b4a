<template>
  <div class="drawer-wrap">
    <hole-table
      ref="table"
      :custom-col-data="['bugId', 'bugName', 'cnnvdNumber', 'cveNumber', 'bugType', 'bugLevel']"
      :filter-data="{}"
      :link="false"
      :single="questionType == 9"
      :not-allowed-arr="[]"
      default-selected-key="bugId"
      height="auto"
      @on-select="onSelect"
    />
    <div class="drawer-footer">
      <el-button :disabled="!selectedItem.length" type="primary" @click="confirm">确定</el-button>
      <el-button type="text" @click="close">取消</el-button>
    </div>
  </div>
</template>

<script>
import holeTable from '@/views/admin/loophole/hole/table/index'
export default {
  components: {
    holeTable
  },
  props: {
    questionType: {
      type: String,
      default: '9'
    }
  },
  data() {
    return {
      selectedItem: []
    }
  },
  methods: {
    'onSelect': function(data) {
      this.selectedItem = data
    },
    'close': function() {
      this.$emit('close')
    },
    'confirm': function() {
      this.$emit('call', this.questionType == 9 ? 'confirm_hole' : 'confirm_hole_muti', this.selectedItem)
    }
  }
}
</script>
