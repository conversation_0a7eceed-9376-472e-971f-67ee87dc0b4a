<template>
  <detail-view
    :loading="loading"
    :data="data"
    :id="id"
    :view-item="viewItem"
    mode="drawer"
    title-key="user"
    desc-key="desc"
  />
</template>
<script>
import moduleConf from '../config'
import actionMenu from '../action/index'
import detailOverview from './overview/index.vue'
import detailView from '@/packages/detail-view/index'
export default {
  components: {
    actionMenu,
    detailView,
    detailOverview
  },
  data() {
    return {
      moduleName: moduleConf.name,
      id: null, // 资源ID
      data: null, // 资源数据对象
      loading: false,
      viewItem: [
        {
          transName: '概况',
          name: 'overview',
          component: detailOverview
        }
      ]
    }
  },
  watch: {
    '$route': function(to, from) {
      const toId = to.params.hasOwnProperty('id') ? to.params.id : null
      const fromId = from.params.hasOwnProperty('id') ? from.params.id : null
      if (toId !== fromId) {
        this.loadBase()
      }
    }
  },
  created() {
    this.loadBase()
  },
  methods: {
    actionHandler: function(type, data) {
      switch (type) {
        case 'refresh':
          this.loadBase()
      }
    },
    'loadBase': function() {
      this.id = this.$route.params.id
      this.getData(this.id)
    },
    // 根据id获取详情数据
    'getData': function(id) {
      return new Promise((resolve, reject) => {
        // getTestProcessesByIdApi(id).then(res => {
        //   this.data = res.data
        //   resolve()
        // }).catch(() => {
        //   this.data = null
        //   reject()
        // })
        this.data = {
          user: '测试2',
          asset: '99-服务器',
          account: 'ceshi2',
          protocol: 'ssh',
          loginFrom: 'Web Terminal',
          dateStart: '2025-01-01 00:00:00',
          dateEnd: '2025-01-02 00:00:00'
        }
      })
    }
  }
}
</script>
