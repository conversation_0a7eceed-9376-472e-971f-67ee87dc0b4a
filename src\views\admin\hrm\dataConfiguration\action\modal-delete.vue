<template>
  <div v-loading="loading" class="dialog-wrap">
    <el-alert :closable="false" type="warning">
      <div slot="title">
        <p>此功能为系统管理员运维使用，请谨慎操作。</p >
      </div>
    </el-alert>
    <batch-template
      :data="data"
      :available-data="data"
      view-key="keyName"
    />
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button :disabled="!data.length" type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>

<script>
import moduleConf from '../config.js'
import batchTemplate from '@/packages/batch-delete/modal-bat-template.vue'
import { sysDataConfigRemove } from '@/api/admin/sysDataConfig.js'
export default {
  components: {
    batchTemplate
  },
  props: {
    data: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      moduleName: moduleConf.name,
      loading: false
    }
  },
  computed: {
  },
  methods: {
    close() {
      this.$emit('close')
    },
    confirm: function() {
      this.loading = true
      const postData = this.data.map(item => item.id)
      sysDataConfigRemove(postData).then(res => {
        this.$message.success('自定义数据删除成功')
        this.$emit('call', 'refresh')
        this.close()
      }).catch(() => {
        this.loading = false
      })
    }
  }
}
</script>
