const statusArr = [
  { label: '待审核', value: '0', type: 'info' },
  { label: '通过', value: '1', type: 'success' },
  { label: '不通过', value: '2', type: 'error' }
]
export default {
  name: 'applicationRecords',
  statusArr: statusArr,
  // 将状态map数组转换为对象
  statusObj: statusArr.reduce((acc, prev) => {
    acc[prev.value] = prev
    return acc
  }, {}),
  statusMapping: statusArr.reduce((acc, prev) => {
    acc[prev.value] = prev.type
    return acc
  }, {})
}
