<template>
  <div v-loading="loading" class="dialog-wrap">
    <batch-template
      :data="list"
      :available-data="list"
      :show-delete-warning="false"
      view-key="name"
      post-key="nodeId"
    />
    <el-form ref="form" :model="formData" :rules="rules" label-position="left" label-width="55px">
      <div class="binding-role-property">
        <el-form-item label="角色" prop="roleId">
          <el-select v-model="formData.roleId" placeholder="请选择角色阵营" @change="getSceneRoleVOList()">
            <el-option
              v-for="item in roleList"
              :key="String(item.id)"
              :label="item.initRoleName"
              :value="String(item.id)"/>
          </el-select>
        </el-form-item>
        <el-form-item prop="sceneRoleId" label-width="10px">
          <el-select v-model="formData.sceneRoleId" placeholder="请选择角色" @change="getNetworkElementList()">
            <el-option
              v-for="item in sceneRoleVOList"
              :key="String(item.id)"
              :label="item.sceneRoleName"
              :value="String(item.id)"/>
          </el-select>
        </el-form-item>
      </div>
    </el-form>
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button :disabled="!data.data" type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>

<script>
import batchTemplate from '../../batch-delete/modal-bat-template.vue'
import modalMixins from '../../mixins/modal_form'
import validate from '../../validate'
import { sceneTypeInitRole, createSceneRoleResourceRelAPI } from '../api/role'

export default {
  components: {
    batchTemplate
  },
  mixins: [modalMixins],
  props: {
    data: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      loading: false,
      list: [],
      roleList: [],
      sceneRoleVOList: [],
      networkElementList: [],
      formData: {
        roleId: '',
        sceneRoleId: ''
      },
      validate: validate,
      rules: {
        sceneRoleId: [validate.required()],
        roleId: [validate.required()]
      },
      typeObj: {
        'pnf': '物理设备',
        'vnf': '虚拟设备',
        'inf': '图形设备'
      }
    }
  },
  mounted() {
    this.list = this.data.data
    this.getSceneTypeInitRole()
  },
  methods: {
    close() {
      this.$emit('close')
    },
    getSceneTypeInitRole() {
      sceneTypeInitRole({ pageType: 0, sceneId: this.data.sceneId, isInitRole: 0 }).then(res => {
        this.roleList = res.data.data.records.filter(item => {
          return (item.initRoleName === '红方' || item.initRoleName === '蓝方') && item.status === 0
        })
      })
    },
    getSceneRoleVOList() {
      let arr = []
      arr = this.roleList.filter(item => {
        return item.id === this.formData.roleId
      })
      if (arr.length) {
        this.sceneRoleVOList = arr[0].sceneRoleVOList
        this.formData.sceneRoleId = ''
      }
    },
    getNetworkElementList() {
      if (this.formData.sceneRoleId) {
        let arr = []
        arr = this.sceneRoleVOList.filter(item => {
          return item.id === this.formData.sceneRoleId
        })
        if (arr.length) {
          this.networkElementList = arr[0].networkElementList
        }
      }
    },
    confirm() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.loading = true
          const arr = []
          this.data.data.forEach(item => {
            arr.push({ nodeId: item.nodeId, networkElementTypeName: this.typeObj[item.type] })
          })
          const newNetworkElementList = this.uniqueByProperty([...this.networkElementList, ...arr], 'nodeId')
          const postData = { topologyTemplateId: this.data.topologyTemplateId, sceneId: this.data.sceneId, sceneRoleId: this.formData.sceneRoleId, networkElementList: newNetworkElementList }
          createSceneRoleResourceRelAPI(postData).then(res => {
            this.$message.success(`资产设定成功`)
            this.$emit('call', 'sceneRole')
            this.close()
          }).catch(() => {
            this.loading = false
          })
        }
      })
    },
    uniqueByProperty(arr, prop) {
      const seen = new Set()
      return arr.filter((item) => {
        const key = JSON.stringify(item[prop])
        return seen.has(key) ? false : seen.add(key)
      })
    }
  }
}
</script>
<style scoped>
.binding-role-property {
  display: flex;
  align-items: center;
}
</style>
