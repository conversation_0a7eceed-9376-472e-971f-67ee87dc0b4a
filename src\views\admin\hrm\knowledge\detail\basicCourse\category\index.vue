<template>
  <div class="category-wrap">
    <transverse-list
      :data="contentTypeArr"
      :allow-deletion="false"
      :allow-add="false"
      :all="true"
      :is-show-expand="false"
      v-bind="categoryProps"
      title="类型"
      @node-click="handleNodeClick($event, 'contentType')"
    />
  </div>
</template>
<script>
import transverseList from '@/packages/transverse-list/index.vue'
import module from '../config.js'

export default {
  components: {
    transverseList
  },
  props: {
    contentType: [String, Number]
  },
  data() {
    return {
      moduleName: module.name,
      contentTypeArr: [
        { value: '1', label: '理论' },
        { value: '2', label: '仿真' }
      ],
      categoryProps: {
        label: 'label',
        idName: 'value'
      },
      categoryQuery: {
        contentType: this.contentType
      }
    }
  },
  mounted() {
  },
  methods: {
    handleNodeClick(item, key) {
      this.categoryQuery[key] = item.value
      this.$emit('category-query', this.categoryQuery)
    }
  }
}
</script>
