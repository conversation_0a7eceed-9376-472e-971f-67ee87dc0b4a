<template>
  <div v-if="list.length > 1" class="count-popover">
    <el-popover
      v-bind="$attrs"
      :placement="showplace?'bottom':'top-start'"
      popper-class="popover-content"
      trigger="hover"
    >
      <template #content>
        <p v-for="(item, index) in list" :key="index+''" class="mb-5">
          {{ userName?item[userName]:item }}
        </p>
      </template>
      <div slot="reference" class="count">{{ list.length }}</div>
    </el-popover>
  </div>
</template>

<script>
export default {
  name: 'CountPopover',
  props: {
    list: {
      type: Array,
      default: () => []
    },
    showplace: {
      type: Boolean,
      default: false
    },
    userName: {
      type: String,
      default: ''
    }
  },
  data() {
    return {

    }
  },
  created() {

  },
  methods: {

  }
}
</script>

<style lang="scss" scoped>
.count-popover {
  $countSize: 22px;
  .count {
    width: $countSize;
    height: $countSize;
    line-height: $countSize;
    font-size: 10px;
    text-align: center;
    border-radius: 50%;
    background-color: #e4e7ed;
  }
}
</style>
<style>
.popover-content {
  max-height: 50vh;
  overflow-y: auto;
  .popper__arrow {
    display: none !important;
  }
}
</style>
