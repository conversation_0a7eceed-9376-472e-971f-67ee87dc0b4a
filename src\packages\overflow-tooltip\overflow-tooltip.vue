<!--**范例**-->
<!--**slot内容中class="overflow-tooltip"元素超出隐藏后自动显示tooltip**-->
<!--<overflow-tooltip>-->
<!--<ul>-->
<!--  <li class="overflow-tooltip">超长字符超长字符超长字符超长字符</li>-->
<!--</ul>-->
<!--<overflow-tooltip>-->
<template>
  <div class="overflow-tooltip-view">
    <slot />
    <el-tooltip ref="tooltip" :content="tooltipContent" placement="top" />
  </div>
</template>
<script>
import _ from 'lodash'
// import { getStyle } from 'element-ui/src/utils/dom'
export default {
  data() {
    return {
      tooltipContent: null,
      activateTooltip: null
    }
  },
  created() {
    this.activateTooltip = _.debounce(tooltip => tooltip.handleShowPopper(), 50)
  },
  mounted() {
    this.$el.querySelectorAll('.overflow-tooltip').forEach((item) => {
      item.addEventListener('mouseover', (e) => {
        this.onMouseOver(e)
      }, false)
      item.addEventListener('mouseleave', (e) => {
        this.onMouseLeave(e)
      }, false)
    })
  },
  methods: {
    // 鼠标移入
    onMouseOver(event) {
      const target = event.target
      // 判断是否text-overflow, 如果是就显示tooltip
      let child = target
      // let child = target.querySelector('.modal-batch-item')
      // 在此处我一共有三个class控制三块class，这一块主要是对<el-menu>以及文本区域控制，所以class要有多个。
      // 如果区域宽度被限定，则通过高度判断
      let heightFlag = false
      // if (!child) {
      //   child = target.querySelector('.setItemClass')
      // }
      if (!child) {
        // 文本区域
        child = target.querySelector('.modal-batch-item-view')
      }
      if (child.scrollHeight > child.offsetHeight) {
        heightFlag = true
      }
      // use range width instead of scrollWidth to determine whether the text is overflowing
      // to address a potential FireFox bug: https://bugzilla.mozilla.org/show_bug.cgi?id=1074543#c3
      const range = document.createRange()
      range.setStart(child, 0)
      range.setEnd(child, child.childNodes.length)
      // const rangeWidth = range.getBoundingClientRect().width // 文本区域宽度
      // const padding = (parseInt(getStyle(target, 'paddingLeft'), 10) || 0) +
      //   (parseInt(getStyle(target, 'paddingRight'), 10) || 0)
      // if ((rangeWidth + padding > target.offsetWidth || child.scrollWidth > child.offsetWidth) || heightFlag && this.$refs.tooltip) {
      if ((child.scrollWidth > child.offsetWidth) || heightFlag && this.$refs.tooltip) {
        const tooltip = this.$refs.tooltip
        tooltip.debounceClose()
        tooltip.setExpectedState(false)
        tooltip.handleClosePopper()
        this.tooltipContent = target.innerText || target.textContent
        tooltip.referenceElm = target
        tooltip.$refs.popper && (tooltip.$refs.popper.style.display = 'none')
        tooltip.doDestroy()
        tooltip.show()
        this.activateTooltip(tooltip)
      }
    },
    // 鼠标移出
    onMouseLeave(event) {
      const tooltip = this.$refs.tooltip
      if (tooltip) {
        tooltip.hide()
        // tooltip.setExpectedState(false)
        // tooltip.handleClosePopper()
      }
    }
  }
}
</script>
