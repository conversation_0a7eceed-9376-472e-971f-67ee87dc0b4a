<template>
  <div :loading="loading">
    <el-tabs
      v-model="tabsActive"
      class="detail-tabs-drawer detail-tabs"
      style="margin-top: 26px;"
      type="card"
    >
      <el-tab-pane label="查看详情" name="view-detail" />
    </el-tabs>
    <div slot="content" class="content-container">
      <el-form ref="form" :model="formData" label-position="left" label-width="150px">
        <div class="content-wrap">
          <div class="left-info">
            <el-card class="info-card">
              <el-divider content-position="left">项目信息</el-divider>
              <div class="info-row">
                <div class="info-item">
                  <span class="label">项目名称：</span>
                  <span class="value">{{ applyDetail.projectName || '-' }}</span>
                </div>
                <div class="info-item">
                  <span class="label">厂商：</span>
                  <span class="value">{{ applyDetail.vendorName || '-' }}</span>
                </div>
              </div>
              <div class="info-row">
                <div class="info-item">
                  <span class="label">检测产品：</span>
                  <span class="value">{{ applyDetail.productName || '-' }}</span>
                </div>
                <div class="info-item">
                  <span class="label">版本号：</span>
                  <span class="value">{{ applyDetail.productVersion }}</span>
                </div>
              </div>
            </el-card>
            <el-card v-if="applyDetail.vendorStatus === 1" class="form-card">
              <el-divider content-position="left">检测范围</el-divider>
              <el-form-item label="测试任务" prop="testingTasks">
                <el-tree
                  ref="testingTaskTree"
                  :data="testingTasksData"
                  :props="testingTaskProps"
                  show-checkbox
                  node-key="id"
                  default-expand-all
                />
              </el-form-item>
            </el-card>
            <el-card class="form-card">
              <el-divider content-position="left">环境信息</el-divider>
              <div v-if="!disableDetail" class="tip-info"><i class="el-icon-warning-outline"/><span style="margin-left: 4px;">请审核测试环境信息，并标记“审核结果”。</span></div>
              <div class="env-tabs">
                <div class="label-block">检测环境：</div>
                <div class="tab-buttons">
                  {{ deployType === 'local' ? '本平台部署' : '外部部署' }}
                </div>
              </div>
              <!-- 本平台部署 -->
              <div v-if="deployType === 'local'" class="deploy-content">
                <div class="env-tabs">
                  <div class="label-block">资源申请：</div>
                  <div class="tab-buttons">
                    {{ resourceType === 'virtual' ? '虚拟机资源' : '网络编排' }}
                  </div>
                </div>
                <!-- 虚拟资源 -->
                <div v-if="resourceType === 'virtual'" class="resource-content">
                  <div class="section-header">
                    <span>虚拟机配置</span>
                  </div>
                  <!-- 虚拟机配置列表 -->
                  <div class="resource-table" style="padding: 0; min-height: 332px;">
                    <t-table-view
                      ref="tableView"
                      :height="height"
                      :single="single"
                      :loading="tableLoading"
                      :data="formData.virtualMachines"
                      :total="tableTotal"
                      :multiple-page="false"
                      :select-item="selectItem"
                      type="list"
                      current-key="id"
                      @on-select="onSelect"
                      @on-current="onCurrent"
                      @on-change="changePage"
                      @on-sort-change="onSortChange"
                      @on-page-size-change="onPageSizeChange"
                    >
                      <el-table-column
                        v-for="item in virtualColumnsViewArr"
                        :key="item"
                        :min-width="colMinWidth"
                        :width="virtualColumnsObj[item].colWidth"
                        :label="virtualColumnsObj[item].title"
                        :fixed="virtualColumnsObj[item].master ? 'left' : false"
                        show-overflow-tooltip
                      >
                        <template slot-scope="scope">
                          <div v-if="item == 'index'">
                            {{ scope.$index + 1 }}
                          </div>
                          <div v-else-if="item == 'memory'">
                            {{ scope.row.memory ? `${scope.row.memory} ${scope.row.memUnit}` : '-' }}
                          </div>
                          <span v-else>{{ scope.row[item] || "-" }}</span>
                        </template>
                      </el-table-column>
                    </t-table-view>
                  </div>
                </div>
                <!-- 网络编排 -->
                <div v-if="resourceType === 'network'" class="resource-content">
                  <el-form-item label="仿真场景：" label-width="100px" prop="scenarioId" class="required-item">
                    <el-tag
                      v-if="formData.scenarioId"
                      :disable-transitions="true"
                      style="margin-top: 5px;"
                      @click="drawerName = 'selectScenario'"
                      @close="formData.scenarioId = null">
                      {{ formData.scenarioName }}
                    </el-tag>
                    <!-- <el-button v-else type="ghost" @click="drawerName = 'selectScenario'">选择仿真场景</el-button> -->
                  </el-form-item>
                  <el-form-item label="场景描述：" label-width="100px" prop="scenarioDescription" class="required-item">
                    <div v-if="formData.scenarioDescription" style="white-space: pre-line; line-height: 28px; padding-top: 6px;" v-html="formData.scenarioDescription" />
                    <div v-else>-</div>
                  </el-form-item>
                </div>
              </div>
              <!-- 外部部署 -->
              <div v-if="deployType === 'external'" class="deploy-content">
                <div class="section-header">
                  <span>设备信息</span>
                  <!-- <el-button style="color: #1C6FFF" type="text" icon="el-icon-plus" @click="showAddDeviceDrawer">添加设备信息</el-button> -->
                </div>
                <!-- 设备配置列表 -->
                <div class="resource-table" style="padding: 0; min-height: 332px;">
                  <t-table-view
                    ref="device-tableView"
                    :height="height"
                    :single="single"
                    :loading="tableLoading"
                    :data="formData.devices"
                    :total="deviceTableTotal"
                    :multiple-page="false"
                    :select-item="selectItem"
                    type="list"
                    current-key="id"
                    @on-select="onSelect"
                    @on-current="onCurrent"
                    @on-change="changePage"
                    @on-sort-change="onSortChange"
                    @on-page-size-change="onPageSizeChange"
                  >
                    <el-table-column
                      v-for="item in deviceColumnsViewArr"
                      :key="item"
                      :min-width="colMinWidth"
                      :width="deviceColumnsObj[item].colWidth"
                      :label="deviceColumnsObj[item].title"
                      :fixed="deviceColumnsObj[item].master ? 'left' : false"
                      show-overflow-tooltip
                    >
                      <template slot-scope="scope">
                        <div v-if="item == 'index'">
                          {{ scope.$index + 1 }}
                        </div>
                        <div v-else-if="item == 'credentials'">
                          <div class="flex jc-between ai-center">
                            <div class="ellipsis overflow-tooltip">
                              <div class="ellipsis">{{ scope.row.credentials[0] || "-" }}</div>
                            </div>
                            <CountPopover :list="scope.row.credentials" />
                          </div>
                        </div>
                        <span v-else>{{ scope.row[item] || "-" }}</span>
                      </template>
                    </el-table-column>
                  </t-table-view>
                </div>
              </div>
              <!-- 外部部署附件 -->
              <el-form-item v-if="deployType === 'external'" label="" label-width="100px">
                <span slot="label">附件：
                  <el-tooltip transfer placement="top">
                    <i class="el-icon-warning-outline" style="color: var(--neutral-700);" />
                    <div slot="content">详细的系统访问信息</div>
                  </el-tooltip>
                </span>
                <div
                  v-if="envAttachments && envAttachments.length"
                  class="attachment-list"
                >
                  <div
                    v-for="(item, fileIndex) in envAttachments"
                    :key="fileIndex"
                    class="attachment-item mb-10"
                  >
                    <i class="el-icon-document" size="16" />
                    <div class="attachment-name">{{ item.name }}（{{ formatFileSize(item.size) }}）</div>
                    <div class="attachment-actions">
                      <el-button
                        class="primary"
                        type="text"
                        icon="el-icon-view"
                        @click="handleFileView(item)"
                      />
                      <el-button
                        class="primary"
                        type="text"
                        icon="el-icon-download"
                        @click="handleDownload(item)"
                      />
                    </div>
                  </div>
                </div>
                <span v-else>-</span>
              </el-form-item>
              <div class="env-tabs">
                <div class="label-block">审核结果：</div>
                <div class="tab-buttons">
                  <el-badge :type="statusMapping[applyDetail.auditEnvStatus]" is-dot />
                  {{ statusObj[applyDetail.auditEnvStatus] ? statusObj[applyDetail.auditEnvStatus].label : '-' }}
                </div>
              </div>
              <!-- 环境信息不通过原因 -->
              <div v-if="deployType2 === 'external'" class="env-tabs">
                <div class="label-block">原因：</div>
                <div v-if="applyDetail.rejectReason" style="white-space: pre-line" v-html="applyDetail.rejectReason" />
                <div v-else>-</div>
              </div>
              <!-- 网络选择 - 当选择本平台部署+虚拟机资源+审核通过时显示 -->
              <div v-if="globalNetworkEnable && deployType === 'local' && resourceType === 'virtual' && deployType2 === 'local'">
                <el-form-item label-width="100px" label="选择网络：" prop="networkId">
                  <span>{{ networkList.find(item => item.networkId == networkId) ? networkList.find(item => item.networkId == networkId).networkName : '-' }}</span>
                </el-form-item>
              </div>
            </el-card>
          </div>
          <div class="right-file">
            <el-card class="form-card">
              <el-divider content-position="left">附件</el-divider>
              <div v-if="!disableDetail" class="tip-info"><i class="el-icon-warning-outline"/><span style="margin-left: 4px;">请依次审核各个测试任务下的附件，并依次标记“审核结果”。</span></div>
              <div class="attachment-section">
                <div v-if="processTasks.length > 0" class="switch-group">
                  <radio-group
                    v-model="activeAttachmentType"
                    :options="processTasks"
                    label-key="processName"
                    value-key="id"
                    @change="val => activeAttachmentType = val"
                  />
                </div>
                <div class="resource-table" style="padding: 0; min-height: 332px;">
                  <!-- 附件列表 -->
                  <t-table-view
                    ref="file-tableView"
                    :height="height"
                    :single="single"
                    :loading="tableLoading"
                    :data="activeAttachmentTypeList"
                    :total="fileTableTotal"
                    :multiple-page="false"
                    :select-item="selectItem"
                    type="list"
                    current-key="id"
                    @on-select="onSelect"
                    @on-current="onCurrent"
                    @on-change="changePage"
                    @on-sort-change="onSortChange"
                    @on-page-size-change="onPageSizeChange"
                  >
                    <el-table-column
                      v-for="item in fileColumnsViewArr"
                      :key="item"
                      :min-width="colMinWidth"
                      :width="fileColumnsObj[item].colWidth"
                      :label="fileColumnsObj[item].title"
                      :fixed="fileColumnsObj[item].master ? 'left' : false"
                      show-overflow-tooltip
                    >
                      <template slot-scope="scope">
                        <div v-if="item == 'index'">
                          {{ scope.$index + 1 }}
                        </div>
                        <div v-else-if="item == 'fileSize'">
                          {{ formatFileSize(scope.row.fileSize) }}
                        </div>
                        <div v-else-if="item == 'handle'">
                          <el-button style="color:var(--color-600)" type="text" size="small" @click="handleFileView(scope.row)">查看</el-button>
                          <el-button style="color:var(--color-600)" type="text" size="small" @click="handleDownload(scope.row)">下载</el-button>
                        </div>
                        <span v-else>{{ scope.row[item] || "-" }}</span>
                      </template>
                    </el-table-column>
                  </t-table-view>
                </div>
              </div>
              <div class="env-tabs">
                <div class="label-block">审核结果：</div>
                <div class="tab-buttons">
                  <el-badge :type="statusMapping[applyDetail.auditAttachmentStatus]" is-dot />
                  {{ statusObj[applyDetail.auditAttachmentStatus] ? statusObj[applyDetail.auditAttachmentStatus].label : '-' }}
                </div>
              </div>
              <!-- 附件不通过原因 -->
              <div v-if="deployType3 === 'external'" class="env-tabs">
                <div class="label-block">原因：</div>
                <div v-if="applyDetail.fileRejectReason" style="white-space: pre-line" v-html="applyDetail.fileRejectReason" />
                <div v-else>-</div>
              </div>
            </el-card>
          </div>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script>
import listModule from './config'
import {
  getNetworkListAPI,
  getResourceApplyDetailAPI,
  processTaskAPI,
  queryAttachmentsAPI
} from '@/api/testing/index'
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
import filePreview from '@/components/testing/utils/filePreview'
import mixinsPageTable from '@/packages/mixins/page_table'
import tTableView from '@/packages/table-view/index.vue'
import tTableConfig from '@/packages/table-config/table-col-config.vue'
import CountPopover from '@/components/CountPopover/index'
import radioGroup from '@/components/commonRadioGroup/index.vue'
import statusConf from '../../config.js'
export default {
  name: 'TestingItemsSubmit',
  components: {
    tTableView,
    tTableConfig,
    CountPopover,
    radioGroup
  },
  mixins: [mixinsActionMenu, mixinsPageTable, filePreview],
  props: {
    id: {
      type: String,
      default: ''
    },
    projectId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      statusObj: statusConf.statusObj,
      statusMapping: statusConf.statusMapping,
      loading: false,
      deployType: 'local', // local-本平台部署, external-外部部署
      resourceType: 'virtual', // virtual-虚拟资源, network-网络编排
      activeAttachmentType: 678708226361989, // 使用第一个任务的ID作为初始值
      activeAttachmentTypeList: [],
      formData: {
        id: '',
        deployType: 'local',
        resourceType: 'virtual',
        devices: [],
        virtualMachines: [],
        scenarioId: '',
        scenarioName: '',
        scenarioCategory: '',
        scenarioDescription: '',
        testingTasks: [] // 选中的检测任务ID
      },
      processTasks: [],
      testingTasksData: [], // 检测任务树形数据
      testingTaskProps: {
        label: 'name',
        children: 'children'
      },
      applyId: '',
      applyDetail: {}, // 资源申请详情数据

      // 审核状态
      deployType2: 'local', // 环境审核状态，默认通过
      deployType3: 'local', // 附件审核状态，默认通过
      auditStatus: 1, // 总体审核状态，默认通过
      auditEnvStatus: 1, // 环境审核状态，默认通过
      auditAttachmentStatus: 1, // 附件审核状态，默认通过
      auditComment: '', // 审核意见
      networkId: '', // 网络ID
      networkDialogVisible: false,
      networkList: [], // 网络列表
      title: '',
      disableDetail: false,
      virtualColumnsObj: {
        index: { title: '序号', master: true, colWidth: 50 },
        deviceName: { title: '设备名称', master: true },
        imageName: { title: '镜像名称', colWidth: 130 },
        cpu: { title: 'CPU (核)', colWidth: 70 },
        memory: { title: '内存', colWidth: 60 },
        system: { title: '系统盘 (GB)', colWidth: 90 },
        data: { title: '数据盘 (GB)', colWidth: 90 }
      },
      virtualColumnsViewArr: listModule.virtualColumnsViewArr,
      deviceColumnsObj: {
        index: { title: '序号', master: true, colWidth: 70 },
        deviceName: { title: '设备名称', master: true },
        ipAddress: { title: 'IP地址/URL' },
        port: { title: '端口号', colWidth: 100 },
        credentials: { title: '用户名/密码' },
        notes: { title: '备注' }
      },
      deviceColumnsViewArr: listModule.deviceColumnsViewArr,
      fileColumnsObj: {
        index: { title: '序号', master: true, colWidth: 70 },
        fileName: { title: '文件名称', master: true },
        typeName: { title: '关联任务' },
        fileSize: { title: '文件大小', colWidth: 120 },
        handle: { title: '操作', colWidth: 120 }
      },
      fileColumnsViewArr: listModule.fileColumnsViewArr,
      tabsActive: 'view-detail',
      globalNetworkEnable: 0,
      envAttachments: []
    }
  },
  computed: {
    // 判断是否禁用"审核通过"按钮 - 当任一审核结果为不通过时禁用
    disableApproveButton() {
      return this.deployType2 === 'external' || this.deployType3 === 'external'
    },

    // 判断是否禁用"审核不通过"按钮 - 当所有审核结果都为通过时禁用
    disableRejectButton() {
      return this.deployType2 === 'local' && this.deployType3 === 'local'
    }
  },
  watch: {
    activeAttachmentType(newVal) {
      this.activeAttachmentTypeList = []
      this.activeAttachmentTypeList = this.applyDetail.attachments.filter(item => item.typeId == newVal)
      this.fileTableTotal = this.activeAttachmentTypeList.length || 0
    }
  },
  created() {
    this.getProjectDetail()
    this.getNetworkList()
  },
  methods: {
    // 获取网络列表
    getNetworkList() {
      getNetworkListAPI().then(res => {
        if (res.data && res.data.code === 0) {
          this.networkList = res.data.data || []
        } else {
          this.$message.error(res.data.msg || '获取网络列表失败')
        }
      }).catch(error => {
        console.error('获取网络列表失败:', error)
        this.$message.error('获取网络列表失败')
      })
    },
    async getProjectDetail() {
      this.applyId = this.id
      this.formData.id = this.projectId

      // 获取资源申请详情
      await getResourceApplyDetailAPI(this.applyId).then(res => {
        if (res.data && res.data.code === 0) {
          // 保存申请详情数据
          this.applyDetail = res.data.data || {}
          // 根据申请详情设置环境类型和资源类型
          if (this.applyDetail && this.applyDetail.projectEnvVO) {
            this.deployType = this.applyDetail.projectEnvVO.envType === 0 ? 'local' : 'external'
            this.globalNetworkEnable = this.applyDetail.projectEnvVO.globalNetworkEnable
            // 外部部署添加的附件
            if (this.applyDetail.projectEnvVO.envAttachments && this.applyDetail.projectEnvVO.envType === 1) {
              this.envAttachments = this.applyDetail.projectEnvVO.envAttachments.map(item => {
                this.$set(item, 'fileName', item.name)
                this.$set(item, 'fileUrl', item.path)
                return item
              })
            }
          }
          if (this.applyDetail.projectEnvVO.resourceType !== undefined) {
            if (this.applyDetail.projectEnvVO.resourceType === 0) {
              this.resourceType = 'virtual'
            } else if (this.applyDetail.projectEnvVO.resourceType === 1) {
              this.resourceType = 'network'
            }
          }
          // 回显网络ID
          if (this.applyDetail.projectEnvVO) {
            this.networkId = this.applyDetail.projectEnvVO.networkId || ''
          }
          // 设置场景信息
          if (this.applyDetail.projectEnvVO.projectEnvSceneVO) {
            this.formData.scenarioId = this.applyDetail.projectEnvVO.projectEnvSceneVO.sceneId || ''
            this.formData.scenarioName = this.applyDetail.projectEnvVO.projectEnvSceneVO.sceneName || ''
            this.formData.scenarioDescription = this.applyDetail.projectEnvVO.projectEnvSceneVO.description || ''
          }
          if (this.applyDetail.projectEnvVO.projectEnvDeviceVOList) {
            const formattedDevices = this.applyDetail.projectEnvVO.projectEnvDeviceVOList.map(device => {
            // 处理账号信息，转换为显示格式
              const credentials = (device.deviceAccountVOList || [])
                .map(acc => `${acc.username} / ${acc.password}`)
              return {
                id: device.id || '',
                deviceName: device.name || '',
                ipAddress: device.target || '',
                port: device.targetPort || '',
                credentials: credentials,
                notes: device.remark || ''
              }
            })
            this.formData.devices = formattedDevices
            this.deviceTableTotal = this.formData.devices.length || 0
          } else {
            this.formData.devices = []
            this.deviceTableTotal = 0
          }
          if (this.applyDetail.projectEnvVO.projectEnvVmVOList) {
            const formattedVMs = this.applyDetail.projectEnvVO.projectEnvVmVOList.map(vm => ({
              id: vm.id || '',
              deviceName: vm.deviceName || '',
              imageName: vm.imageName || '',
              cpu: vm.cpuCount || '',
              memory: vm.memSize || '',
              memUnit: vm.memUnit || '',
              system: vm.systemDiskSize || '',
              data: vm.dataDiskSize || ''
            }))
            this.formData.virtualMachines = formattedVMs
            this.tableTotal = this.formData.virtualMachines.length || 0
          }

          // 设置附件信息
          if (this.applyDetail.attachments && Array.isArray(this.applyDetail.attachments)) {
            this.activeAttachmentTypeList = this.applyDetail.attachments.filter(item => item.typeId == this.activeAttachmentType)
            this.fileTableTotal = this.activeAttachmentTypeList.length || 0
          }
        }
      }).catch(() => {

      })

      // 存储项目详情数据
      const taskRes = await processTaskAPI(this.applyId)
      // 处理任务数据
      const tasks = taskRes.data.data || []
      // 待审核和拒绝状态时，附件取检测任务的附件 1已通过  0待评审 2已拒绝
      if (this.applyDetail.auditStatus !== 1) {
        tasks.map((item) => {
          item.processName = item.name
          item.id = item.sourceTaskId
        })
      } else {
        tasks.map((item) => {
          item.processName = item.name
        })
      }
      if (this.applyDetail.auditStatus !== 0) {
        this.title = '查看详情'
        this.disableDetail = true
        this.deployType2 = this.applyDetail.auditEnvStatus === 1 ? 'local' : 'external'
        this.deployType3 = this.applyDetail.auditAttachmentStatus === 1 ? 'local' : 'external'
      } else {
        this.title = '审核项目资料'
        this.disableDetail = true
      }
      this.processTasks = this.getDisplayTasks(tasks)
      this.activeAttachmentType = this.processTasks.length > 0 ? this.processTasks[0].id : null
      this.testingTasksData = tasks
      this.loading = false
    },
    getDisplayTasks(tasks) {
      const result = []
      tasks.forEach(task => {
        if (Array.isArray(task.children) && task.children.length > 0) {
          task.children.forEach(child => {
            // auditStatus == 1 通过时取id
            // auditStatus == 0 || auditStatus == 2 不通过或者审核时取sourceTaskId
            result.push({ id: this.applyDetail.auditStatus !== 1 ? child.sourceTaskId : child.id, processName: child.name })
          })
        } else {
          result.push({ id: this.applyDetail.auditStatus === 2 ? task.sourceTaskId : task.id, processName: task.name })
        }
      })
      return result
    },
    // 获取附件列表
    getAttachments(taskId) {
      const params = {
        page: 1,
        limit: 999,
        taskId: taskId,
        projectId: this.projectId
      }
      queryAttachmentsAPI(params).then(res => {
        if (res.data.code === 0) {
          this.activeAttachmentTypeList = (res.data && res.data.data) ? res.data.data.records : []
          this.fileTableTotal = res.data.data.total || 0
        }
      })
    },

    // 处理环境审核状态变更
    handleEnvStatusChange(type) {
      if (this.disableDetail) return
      this.deployType2 = type
      this.auditEnvStatus = type === 'local' ? 1 : 2 // local=通过, external=不通过
    },

    // 处理附件审核状态变更
    handleAttachmentStatusChange(type) {
      if (this.disableDetail) return
      this.deployType3 = type
      this.auditAttachmentStatus = type === 'local' ? 1 : 2 // local=通过, external=不通过
    },
    handleView(row) {
      window.open(row.fileUrl)
    },
    handleDownload(item) {
      // 下载附件
      if (item.fileUrl) {
        fetch(item.fileUrl, {
          method: 'get',
          responseType: 'blob'
        })
          .then((response) => response.blob())
          .then((blob) => {
            const a = document.createElement('a')
            const URL = window.URL || window.webkitURL
            const href = URL.createObjectURL(blob)
            a.href = href
            a.download = item.fileName
            document.body.appendChild(a)
            a.click()
            document.body.removeChild(a)
            URL.revokeObjectURL(href)
          })
      } else {
        this.$message.warning('文件链接不存在，无法下载')
      }
    },

    formatFileSize(size) {
      if (size < 1024) {
        return size + ' B'
      } else if (size < 1024 * 1024) {
        return (size / 1024).toFixed(2) + ' KB'
      } else {
        return (size / 1024 / 1024).toFixed(2) + ' MB'
      }
    }

  }
}
</script>

<style scoped lang="scss">
.content-container {
  max-height: 93vh;
  padding-top: 20px;
  overflow-y: auto;
}
.content-wrap {
  display: flex;
  justify-content: space-between;
  gap: 10px;
  width: 100%;
  .left-info {
    width: calc(50% - 5px);
  }
  .right-file {
    width: calc(50% - 5px);
  }
}
.info-card, .form-card {
  margin-bottom: 10px;

  .card-header {
    font-weight: bold;
  }
  .tip-info {
    margin-bottom: 10px;
    color: #e7855b;
  }
}

.info-row {
  display: flex;
  margin-bottom: 15px;

  .info-item {
    flex: 1;
    display: flex;

    .label {
      color: var(--neutral-700);
      font-weight: 500;
      width: 100px;
      padding-right: 30px;
    }

    .value {
      flex: 1;
    }
  }
}

.env-tabs {
  display: flex;
  align-items: center;
  margin-bottom: 20px;

  .label-block {
    padding-right: 12px;
    width: 100px;
    text-align: left;
    color: var(--neutral-700);
    font-weight: 500;
    flex-shrink: 0;
  }

  .tab-buttons {
    display: flex;
    .el-button {
      padding: 8px 20px;
      border-radius: 0;
      cursor: not-allowed;
      // 添加hover效果覆盖
      &:not(.is-disabled):hover, &:not(.is-disabled):focus {
        background-color: inherit;
        border-color: #ececec;
        color: #606266;
      }
    }
    .is-disabled {
      background: #F6F8FA;
    }
    .el-button + .el-button {
      margin-left: 0;
    }

    .el-button:first-child {
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
    }

    .el-button:last-child {
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }
  }
}

.deploy-content, .resource-content {
  margin-bottom: 20px;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;

  span {
    width: 100px;
    text-align: left;
    color: var(--neutral-700);
    font-weight: 500;
  }
}

.empty-data {
  padding: 20px;
  text-align: center;
  color: #909399;
  background: #f5f7fa;
  border-radius: 4px;
}

.attachment-section {
  margin-bottom: 20px;

  .switch-group {
    margin-bottom: 15px;
  }
}

.el-input {
  width: 66.6%;
}

.el-tabs--card > .el-tabs__header .el-tabs__item.is-active {
  border-bottom-color: var(--color-600);
}

.required-item {
  ::v-deep .el-form-item__label:before {
    content: '*';
    color: #f56c6c;
    margin-right: 4px;
  }
}

.align-items {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

::v-deep.el-tag {
  padding: 0 10px !important;
}
.el-tree {
  margin-left: 20px;
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e6e6e6;
  padding: 10px;
  border-radius: 4px;
}
::v-deep.el-card.is-always-shadow,
  .el-card.is-hover-shadow:focus,
  .el-card.is-hover-shadow:hover {
    box-shadow: none;
  }
  .el-card__body {
    padding: 6px 20px;
  }
  .el-form-item {
    margin-bottom: 12px;
    word-break: break-all;
    .el-form-item__content,
    .el-form-item__label {
      line-height: 24px;
    }
  }
::v-deep .el-card__body {
  padding: 6px 20px;
}
.attachment-list {
  .attachment-item {
    display: flex;
    width: 100%;
    padding: 0 10px;
    align-items: center;
    border: 1px solid var(--color-601-border);
    background: var(--color-601-background);

    i {
      color: #909399;
      margin-right: 5px;
    }

    .attachment-name {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      color: #000000D9;
    }

    .attachment-actions {
      display: flex;

      .el-button {
        padding: 0 5px;
        color: var(--color-600);
      }
    }
  }
}
</style>
