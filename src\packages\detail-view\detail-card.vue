<template>
  <div :style="{ height: isCollapse ? 'auto' : '100%'}" class="detail-card">
    <div class="detail-card-head">
      <div class="detail-card-head-title">
        <slot v-if="$slots.title" name="title" />
        <span v-else> {{ title }}</span>
      </div>
      <i :class="isCollapse ? 'el-icon-arrow-up' : 'el-icon-arrow-down'" @click="isCollapse = !isCollapse" />
    </div>
    <div :style="{ 'display': isCollapse ? 'none' : 'block' }" class="detail-card-body">
      <slot v-if="$slots.content" name="content" />
      <span v-else> {{ content }}</span>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: () => {
        return ''
      }
    },
    content: {
      type: String,
      default: () => {
        return ''
      }
    }
  },
  data() {
    return {
      isCollapse: false // 是否折叠
    }
  }
}
</script>

<style lang="scss" scoped>
.detail-card {
  background-color: var(--neutral-0);
  border: 1px solid var(--neutral-300);
  font-size: 14px;
  line-height: 1.5715;
  border-radius: 2px;
  .detail-card-head {
    font-weight: 500;
    min-height: auto;
    padding: 0 20px;
    background-color: var(--neutral-100);
    display: flex;
    align-items: center;
    .detail-card-head-title {
      line-height: 22px;
      color: var(--neutral-800);
      padding: 8px 0;
      flex: 1 1;
    }
    i {
      cursor: pointer;
    }
  }
  .detail-card-body {
    padding: 20px;
    /deep/ .el-form .el-form-item {
      margin-bottom: 12px;
      word-break: break-all;
      .el-form-item__label, .el-form-item__content {
        line-height: 24px;
      }
      &.has-form {
        .el-form-item__label, .el-form-item__content {
          line-height: 32px;
        }
      }
    }
    /deep/ .el-form .el-tag {
      padding-right: 10px;
    }
  }
}
</style>
