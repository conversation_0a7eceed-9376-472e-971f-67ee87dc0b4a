<template>
  <div class="resource-table drawer-wrap">
    <!-- 操作区 -->
    <div class="operation-wrap">
      <div class="operation-left">
        <slot name="action" />
        <el-button type="primary" icon="el-icon-refresh" @click="refresh" />
      </div>
      <div class="operation-right">
        <el-badge :value="searchBtnShowNum">
          <el-button icon="el-icon-search" @click="openSearch">搜索</el-button>
        </el-badge>
      </div>
    </div>
    <!-- 搜索区 -->
    <t-search-box
      v-show="searchView"
      :search-key-list="searchKeyListView"
      default-placeholder="默认搜索名称"
      @search="searchMultiple"
    />
    <!-- 列表 -->
    <t-table-view
      ref="tableView"
      :height="height"
      :loading="tableLoading"
      :data="tableData"
      :total="tableTotal"
      :page-size="pageSize"
      :current="pageCurrent"
      :select-item="selectItem"
      :multiple-page="false"
      current-key="pointId"
      default-selected-key="pointId"
      @on-select="onSelect"
      @on-current="onCurrent"
      @on-change="changePage"
      @on-sort-change="onSortChange"
      @on-page-size-change="onPageSizeChange"
    >
      <el-table-column v-for="item in columnsViewArr" :key="item" :min-width="colMinWidth" :label="columnsObj[item].title" :fixed="columnsObj[item].master ? 'left' : false" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="item == 'abilityValue'">
            {{ Math.round(scope.row[item]) }}
          </span>
          <span v-else>{{ scope.row[item] || "-" }}</span>
        </template>
      </el-table-column>
    </t-table-view>
    <!-- 底部 -->
    <div class="drawer-footer">
      <el-button :disabled="!selectItem.length || selectItem.length < 3 || selectItem.length > 8" type="primary" @click="confirm">确定</el-button>
      <el-button type="text" @click="close">取消</el-button>
    </div>
  </div>
</template>

<script>
import tSearchBox from '@/packages/search-box/index.vue'
import tTableView from '@/packages/table-view/index.vue'
import tableTdMultiCol from '@/packages/table-config/table-td-multi-col.vue'
import mixinsPageTable from '@/packages/mixins/page_table'
export default {
  components: {
    tSearchBox,
    tTableView,
    tableTdMultiCol
  },
  mixins: [mixinsPageTable],
  props: {
    data: {
      type: Array
    }
  },
  data() {
    return {
      // 搜索配置项
      searchKeyList: [
        { key: 'pointName', label: '名称', master: true }
      ],
      // 所有可配置显示列 master：不可隐藏 title:列名称
      columnsObj: {
        'pointName': {
          title: '名称',
          master: true
        },
        'abilityValue': {
          title: '分数'
        }
      },
      // 当前显示列key表 默认，如果localStorage有数据将被覆盖
      columnsViewArr: [
        'pointName',
        'abilityValue'
      ],
      tableData: []
    }
  },
  methods: {
    getList: function() {
      const params = this.getPostData('page', 'limit')
      if (params.pointName) {
        this.tableData = this.data.filter(item => { return item.pointName.indexOf(params.pointName) !== -1 })
        this.tableTotal = this.data.filter(item => { return item.pointName.indexOf(params.pointName) !== -1 }).length
      } else {
        this.tableData = this.data
        this.tableTotal = this.data.length
      }
      this.handleSelection()
    },
    'close': function() {
      this.$emit('close')
    },
    'confirm': function() {
      this.$emit('call', 'confirm_skill', this.selectItem)
    }
  }
}
</script>
