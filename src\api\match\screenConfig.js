import request from '@/utils/request'
const headers = { 'Content-Type': 'application/json;charset=UTF-8' }

// 3d列表
export function queryScreenList(data) {
  return request({
    url: '/scene/sysLargeScreen/queryPage',
    method: 'post',
    data,
    headers
  })
}
// 新建配置
export function queryScreenCreate(data) {
  return request({
    url: '/scene/sysLargeScreen/create',
    method: 'post',
    data,
    headers
  })
}
// 编辑配置
export function queryScreenEdit(data) {
  return request({
    url: '/scene/sysLargeScreen/update',
    method: 'post',
    data,
    headers
  })
}
// 配置详情
export function queryScreenInfo(params) {
  return request({
    url: '/scene/sysLargeScreen/get',
    method: 'get',
    params,
    headers
  })
}
// 配置删除
export function queryScreenDelete(data) {
  return request({
    url: '/scene/sysLargeScreen/remove',
    method: 'post',
    data,
    headers
  })
}


// 配置下拉
export function queryScreenSelect(data) {
  return request({
    url: '/scene/sysLargeScreen/listOption',
    method: 'post',
    data,
    headers
  })
}
