import request from '@/utils/request'

// 查询标签列表
export function listLabel(query) {
  return request({
    url: '/accumulate/lable/list',
    method: 'get',
    params: query
  })
}


// 查询标签下拉树
export function listLabelTree(data) {
  return request({
    url: '/accumulate/lable/treeList',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data
  })
}

// 查询【请填写功能名称】详细
export function getLabel(id) {
  return request({
    url: '/accumulate/lable/' + id,
    method: 'get'
  })
}

// 新增【请填写功能名称】
export function addLabel(data) {
  return request({
    url: '/accumulate/lable/add',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

// 修改【请填写功能名称】
export function updateLabel(data) {
  return request({
    url: '/accumulate/lable/update',
    method: 'put',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

// 删除【请填写功能名称】
export function delLabel(id) {
  return request({
    url: '/accumulate/lable/delete/' + id,
    method: 'delete'
  })
}
