<template>
  <div class="resource-table">
    <!-- 操作区 -->
    <div class="operation-wrap">
      <div class="operation-left">
        <slot name="action" />
        <el-button type="primary" icon="el-icon-refresh" @click="refresh" />
      </div>
      <div class="operation-right">
        <el-badge :value="searchBtnShowNum">
          <el-button icon="el-icon-search" @click="openSearch">搜索</el-button>
        </el-badge>
        <!-- 自定义表格列 -->
        <t-table-config
          v-if="!customColData.length"
          :data="columnsObj"
          :active-key-arr="columnsViewArr"
          @on-change-col="onChangeCol"
        />
      </div>
    </div>
    <!-- 搜索区 -->
    <t-search-box
      v-show="searchView"
      :search-key-list="searchKeyListView"
      default-placeholder="默认搜索设备名称"
      @search="searchMultiple"
    />
    <!-- 列表 -->
    <t-table-view
      ref="tableView"
      :height="height"
      :single="single"
      :loading="tableLoading"
      :data="tableData"
      :total="tableTotal"
      :page-size="pageSize"
      :current="pageCurrent"
      :select-item="selectItem"
      @on-select="onSelect"
      @on-current="onCurrent"
      @on-change="changePage"
      @on-sort-change="onSortChange"
      @on-page-size-change="onPageSizeChange"
    >
      <el-table-column
        v-for="item in columnsViewArr"
        :key="item"
        :min-width="item === 'icon' ? '80' : colMinWidth"
        :label="columnsObj[item].title"
        :fixed="columnsObj[item].master ? 'left' : false"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span v-if="item === 'icon'">
            <el-image :src="currentApi + scope.row.icon.value" style="width: 30px; height: 30px;" />
          </span>
          <span v-else-if="item === 'virtual_type'">{{ scope.row[item] ? virtualTypeMap[scope.row[item]] : '-' }}</span>
          <span v-else-if="item === 'instance_count'">{{ scope.row[item] }}</span>
          <span v-else>{{ scope.row[item] || "-" }}</span>
        </template>
      </el-table-column>
    </t-table-view>
  </div>
</template>

<script>
import tSearchBox from '@/packages/search-box/index.vue'
import tTableView from '@/packages/table-view/index.vue'
import tTableConfig from '@/packages/table-config/table-col-config.vue'
import mixinsPageTable from '@/packages/mixins/page_table'
import { getNetworkElement } from '@/api/accumulate/questionBank'
import currentHost from '@/packages/topo/mixins/current_host.js'

export default {
  components: {
    tSearchBox,
    tTableView,
    tTableConfig
  },
  mixins: [mixinsPageTable, currentHost],
  data() {
    return {
      virtualTypeMap: {
        'qemu': '虚拟机',
        'docker': '容器',
        'cloud_router': '云路由',
        'global_network': '全局网络',
        'global_qemu': '全局虚拟机'
      },
      moduleName: 'virtual_device',
      // 搜索配置项
      searchKeyList: [
        { key: 'name', label: '设备名称', master: true }
      ],
      // 所有可配置显示列 master：不可隐藏 title:列名称
      columnsObj: {
        'icon': { title: '设备图标', master: true },
        'name': { title: '设备名称' },
        'virtual_type': { title: '虚拟设备类型' },
        'instance_count': { title: '虚拟实例数' }
      },
      // 当前显示列key表 默认，如果localStorage有数据将被覆盖
      columnsViewArr: [
        'icon',
        'name',
        'virtual_type',
        'instance_count'
      ]
    }
  },
  methods: {
    getList: function(showLoading = true) {
      if (showLoading) {
        this.tableLoading = true
      }
      const params = this.getPostData('page', 'limit')
      params['offset'] = (params['page'] - 1) * params['limit']
      delete params['page']
      getNetworkElement(params).then(res => {
        this.tableData = res.data.result
        this.tableTotal = res.data.total
        this.tableLoading = false
        this.handleSelection()
      }).catch(() => {
        this.tableLoading = false
      })
    }
  }
}
</script>
