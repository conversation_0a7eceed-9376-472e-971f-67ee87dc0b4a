<template>
  <div class="dialog-wrap">
    <el-alert :closable="false" type="warning">
      <div slot="title">
        <p>将从资源库中移出选中资源，不影响资源使用，请确认！</p >
      </div>
    </el-alert>
    <batch-template
      :data="data"
      :available-data="availableData"
      view-key="projectName"
    />
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>
<script>
import module from '../config.js'
import { lessonPlanUpdate } from '@/api/teacher/index.js'
import batchTemplate from '@/packages/batch-delete/modal-bat-template.vue'
export default {
  name: 'DeleteItem',
  components: {
    batchTemplate
  },
  mixins: [],
  props: {
    data: {
      type: Array,
      default: () => {
        return []
      }
    },
    name: {
      type: String
    }
  },
  data() {
    return {
      moduleName: module.name,
      form: {
        value: ''
      },
      availableData: [],
      options: []
    }
  },
  computed: {
    isChecked() {
      if (this.availableData.length == 0) {
        return false
      } else if (this.availableData.length > 0) {
        return true
      }
    }
  },
  mounted() {
    // this.filterData()
  },
  methods: {
    // filterData() {
    //   if (this.name === 'open') {
    //     this.availableData = this.data.filter(item => {
    //       return item.privacy === 0
    //     })
    //   } else {
    //     this.availableData = this.data.filter(item => {
    //       return item.privacy === 1
    //     })
    //   }
    // },
    close() {
      this.$emit('close')
    },
    confirm() {
      const data = {}
      lessonPlanUpdate(data)
        .then((res) => {
          this.$message.success(res.data)
          this.$emit('call', 'refresh')
          this.close()
        })
    }
  }
}
</script>
