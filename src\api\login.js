import request from '@/utils/request'

export function loginAPI(params) {
  return request({
    url: '/login',
    method: 'post',
    data: params,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export function logoutAPI() {
  return request({
    url: '/logout',
    method: 'post'
  })
}

export function codeAPI() {
  return request({
    url: 'code',
    method: 'get',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
