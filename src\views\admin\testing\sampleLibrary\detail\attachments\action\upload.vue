<!--
 * @Author: FinKiin
 * @Date: 2025-04-22 18:39:08
 * @LastEditTime: 2025-04-22 19:34:57
 * @FilePath: \hrm-zsk-manage-web\src\views\admin\testing\testingItems\detail\attachments\action\upload.vue
 * @Description:
 *
-->
<template>
  <div v-loading="loading" class="dialog-wrap">
    <!-- 表单内容 -->
    <el-form ref="form" :model="form" :rules="rules" label-width="100px">
      <el-form-item label="关联任务">
        <el-select
          v-model="form.taskId"
          style="width: 100%;"
          size="small"
          placeholder="请选择关联任务"
          clearable
          filterable
        >
          <el-option
            v-for="item in processTasks"
            :key="item.id"
            :label="item.processName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="附件" prop="file">
        <el-upload
          ref="upload"
          :action="uploadUrl"
          :headers="header"
          :on-change="handleFileChange"
          :on-remove="handleFileRemove"
          :on-success="handleUploadSuccess"
          :on-error="handleUploadError"
          :file-list="form.fileList"
          :before-upload="beforeUpload"
          multiple
          class="upload-demo"
        >
          <el-button
            slot="trigger" size="small" type="primary"
          >上传文件</el-button>
        </el-upload>
      </el-form-item>
    </el-form>
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button :loading="loading" type="primary" @click="handleSubmit">确定</el-button>
    </div>
  </div>
</template>

<script>
import modalMixins from '@/packages/mixins/modal_form'
import validate from '@/packages/validate/index'
import { getToken } from '@/utils/auth'
import { uploadAttachmentAPI, updateAttachmentAPI } from '@/api/testing/index'

export default {
  mixins: [modalMixins],
  props: {
    data: {
      type: Array,
      default: () => {
        return []
      }
    },
    processTasks: {
      type: Array,
      default: () => []
    },
    activeTaskId: {
      type: [Number, String]
    },
    taskName: {
      type: String,
      default: ''
    },
    projectId: {
      type: String,
      default: ''
    },
    currentUpdateFile: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      loading: false,
      form: {
        taskId: '',
        fileList: [],
        currentUpdateFile: {
          id: '',
          type: ''
        }
      },
      rules: {
        taskId: [validate.required('请选择关联任务')],
        file: [
          {
            required: true,
            message: '必填项',
            trigger: 'change',
            validator: (rule, value, callback) => {
              if (this.form.fileList && this.form.fileList.length > 0) {
                callback()
              } else {
                callback(new Error('必填项'))
              }
            }
          }
        ]
      },
      uploadedFiles: [], // 存储已上传成功的文件信息
      attachmentFile: {},
      maxFileSize: localStorage.getItem('maxFileSize')
    }
  },
  computed: {
    header() {
      return {
        'Admin-Token': getToken()
      }
    },
    uploadUrl() {
      return '/api/testing/attachment/upload'
    }
  },
  created() {
    // 设置初始任务ID，支持空字符串
    this.form.taskId = this.activeTaskId !== undefined ? this.activeTaskId : ''

    // 重置上传文件数组
    this.uploadedFiles = []

    // 如果是更新操作（selectItem中有数据）
    if (this.data && this.data.length > 0) {
      const fileData = this.data[0]
      // 设置当前更新文件信息
      this.form.currentUpdateFile = {
        id: fileData.id,
        type: fileData.taskId || this.activeTaskId || ''
      }

      // 如果有taskId，设置为当前任务
      if (fileData.taskId !== undefined) {
        this.form.taskId = fileData.taskId
      }
    }
  },
  methods: {
    beforeUpload(file) {
      const maxSize = this.maxFileSize * 1024 * 1024
      if (file.size > maxSize) {
        this.$message.error(`上传文件不能超过${this.maxFileSize}MB!`)
        // 清除 file 字段的校验错误提示
        this.$nextTick(() => {
          this.$refs['form'].clearValidate('file')
        })
        return false
      }
      return true
    },

    handleFileChange(file, fileList) {
      this.form.fileList = fileList
    },

    handleFileRemove(file, fileList) {
      this.form.fileList = fileList
      // 触发表单验证更新
      this.$refs.form && this.$refs.form.validateField('file')
    },

    handleUploadSuccess(res, file, fileList) {
      console.log('res, file, fileList', res, file, fileList)
      if (res.code === 0) {
        // 上传成功处理
        this.$message.success('文件上传成功')
        file.id = res.data.url
        file.path = res.data.url

        // 保存文件信息到uploadedFiles数组
        this.uploadedFiles.push({
          name: res.data.name,
          url: res.data.path,
          id: res.data.id,
          fileSize: file.size
        })

        this.form.fileList = fileList
        // 触发表单验证更新
        this.$refs.form.validateField('file')
      } else {
        this.$message.error(res.msg || '上传失败')
        // 从文件列表中移除失败的文件
        const index = fileList.indexOf(file)
        if (index !== -1) {
          fileList.splice(index, 1)
        }
        this.form.fileList = fileList
      }
      this.loading = false
    },

    handleUploadError(err, file, fileList) {
      console.error('上传失败', err)
      this.$message.error('文件上传失败，请稍后重试')
      this.form.fileList = []
      this.loading = false
    },

    close() {
      // 重置上传文件数组
      this.uploadedFiles = []
      this.form.fileList = []
      this.$emit('close')
    },

    submitUpload() {
      this.$refs.form.validate(valid => {
        if (!valid) {
          return
        }

        if (!this.uploadedFiles || this.uploadedFiles.length === 0) {
          this.$message.error('请先上传文件')
          return
        }

        this.loading = true

        // 记录成功和失败的数量
        let successCount = 0
        let failCount = 0
        const totalFiles = this.uploadedFiles.length

        // 为每个文件调用API
        this.uploadedFiles.forEach(file => {
          const params = {
            fileId: file.id,
            fileUrl: file.url,
            fileName: file.name,
            fileSize: file.fileSize,
            taskId: this.form.taskId,
            projectId: this.projectId
          }

          uploadAttachmentAPI(params).then(res => {
            if (res.data && res.data.code === 0) {
              successCount++
            } else {
              failCount++
              console.error('文件上传失败:', file.name, res.data.msg)
            }

            // 当所有文件处理完成时
            if (successCount + failCount === totalFiles) {
              this.loading = false
              if (successCount > 0) {
                this.$message.success(`成功添加${successCount}个文件${failCount > 0 ? '，' + failCount + '个文件添加失败' : ''}`)
                this.$emit('call', 'refresh')
                this.close()
              } else {
                this.$message.error('所有文件添加失败')
              }
            }
          }).catch(() => {
            failCount++
            // 当所有文件处理完成时
            if (successCount + failCount === totalFiles) {
              this.loading = false
              if (successCount > 0) {
                this.$message.success(`成功添加${successCount}个文件${failCount > 0 ? '，' + failCount + '个文件添加失败' : ''}`)
                this.$emit('call', 'refresh')
                this.close()
              } else {
                this.$message.error('所有文件添加失败')
              }
            }
          })
        })
      })
    },

    // 提交更新文件
    submitUpdateFile() {
      this.$refs.form.validate(valid => {
        if (!valid) {
          return
        }

        if (!this.uploadedFiles || this.uploadedFiles.length === 0) {
          this.$message.error('请先上传文件')
          return
        }

        // 更新操作只处理第一个文件
        if (this.uploadedFiles.length > 1) {
          this.$message.warning('更新操作只会使用第一个文件，其他文件将被忽略')
        }

        const file = this.uploadedFiles[0]
        const fileId = this.form.currentUpdateFile.id
        const params = {
          id: fileId,
          fileId: file.id,
          fileUrl: file.url,
          fileName: file.name,
          fileSize: file.fileSize,
          taskId: this.form.taskId,
          projectId: this.projectId
        }

        this.loading = true
        updateAttachmentAPI(params).then(res => {
          this.loading = false
          if (res.data && res.data.code === 0) {
            // 关闭弹窗并提示
            this.$message.success('文件更新成功')
            this.$emit('call', 'refresh')
            this.close()
          } else {
            this.$message.error(res.data.msg || '更新失败')
          }
        }).catch(() => {
          this.loading = false
        })
      })
    },

    // 处理提交操作
    handleSubmit() {
      // 根据是否有currentUpdateFile.id来判断是更新还是新增
      if (this.form.currentUpdateFile && this.form.currentUpdateFile.id) {
        this.submitUpdateFile()
      } else {
        this.submitUpload()
      }
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-form {
  padding: 0;
}
</style>
