<template>
  <div v-loading="loading" class="dialog-wrap">
    <batch-template
      :data="data"
      :available-data="availableArr"
      view-key="examName"
    />
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button :disabled="!availableArr.length" type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>

<script>
import moduleConf from '../config.js'
import batchTemplate from '@/packages/batch-delete/modal-bat-template.vue'
import { deleteExam } from '@/api/accumulate/exam.js'

export default {
  components: {
    batchTemplate
  },
  props: {
    data: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      moduleName: moduleConf.name,
      loading: false
    }
  },
  computed: {
    // 筛选返回可操作数据
    'availableArr': function() {
      const _data = []
      this.data.forEach((item, index) => {
        // 使用次数为0
        if (item.citedNum == 0) {
          _data.push(item)
        }
      })
      return _data
    }
  },
  methods: {
    close() {
      this.$emit('close')
    },
    confirm: function() {
      this.loading = true
      const postData = this.availableArr.map(item => item.id)
      deleteExam(postData).then(res => {
        this.$message.success('删除试卷成功')
        this.$bus.$emit(this.moduleName + '_module', 'reload')
      }).catch(() => {
        this.$bus.$emit(this.moduleName + '_module', 'reload')
      })
      // 点击确定，立刻关闭弹窗
      this.close()
    }
  }
}
</script>
