<template>
  <div v-loading="loading" class="dialog-wrap">
    <el-alert v-if="isJion" :closable="false" type="warning">
      <div slot="title">
        <p>学员侧加入自学的课程，删除课程后学员侧自学课程将同步删除！</p >
      </div>
    </el-alert>
    <batch-template
      :data="data"
      :available-data="data"
      :show-delete-warning="!isJion"
      view-key="name"
    />
    <el-checkbox v-if="isJion" v-model="checked">我已知晓上述风险</el-checkbox>
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button :disabled="!data.length || (!checked && isJion)" type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>

<script>
import batchTemplate from '@/packages/batch-delete/modal-bat-template.vue'
import modalMixins from '@/packages/mixins/modal_form'
import { deleteCourse } from '@/api/teacher/index.js'

export default {
  components: {
    batchTemplate
  },
  mixins: [modalMixins],
  props: {
    data: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      loading: false,
      checked: false,
      isJion: false
    }
  },
  mounted() {
    this.isJion = this.data[0].selective
  },
  methods: {
    close() {
      this.$emit('close')
    },
    confirm: function() {
      this.loading = true
      deleteCourse({ id: this.data[0].id }).then(res => {
        if (res.code == 0) {
          this.$message.success('删除成功')
          this.$emit('call', 'refresh')
          this.close()
        }
      })
        .finally(() => {
          this.loading = false
        })
    }
  }
}
</script>
