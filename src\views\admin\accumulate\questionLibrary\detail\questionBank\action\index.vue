<template>
  <div class="buttons-wrap">
    <el-dropdown placement="bottom-start" trigger="click" @command="handleCommand">
      <el-button type="primary">
        创建题目<i class="el-icon-arrow-down el-icon--right"/>
      </el-button>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item command="1">理论</el-dropdown-item>
        <el-dropdown-item command="2">靶机</el-dropdown-item>
        <el-dropdown-item command="3">仿真</el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
    <el-dropdown placement="bottom-start" trigger="click" @command="clickDrop">
      <el-button type="primary">
        操作<i class="el-icon-arrow-down el-icon--right"/>
      </el-button>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item command="batchExport">批量导入</el-dropdown-item>
        <el-dropdown-item :disabled="singleDisabled" command="edit">编辑</el-dropdown-item>
        <el-dropdown-item :disabled="multipleDisabled" command="modalDelete">删除</el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
    <!-- 中部弹窗 start-->
    <el-dialog
      :title="titleMapping[modalName]"
      :visible.sync="modalShow"
      :width="modalWidth"
      @close="modalClose"
    >
      <transition name="el-fade-in-linear">
        <component
          :is="modalName"
          :name="modalName"
          :bank-type="bankType"
          :data="selectItem"
          :module-name="moduleName"
          :question-depot-id="$route.params.id"
          page-type="questionLibrary"
          @close="modalClose"
          @call="confirmCall"
        />
      </transition>
    </el-dialog>
    <!-- 中部弹窗 end-->
  </div>
</template>
<script>
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
import batchExport from '@/views/admin/accumulate/questionBank/action/batch-export.vue'
import modalDelete from '@/views/admin/accumulate/questionBank/action/modal-delete'

export default {
  components: {
    batchExport,
    modalDelete
  },
  mixins: [mixinsActionMenu],
  props: {
    // 1-理论 2-靶机 3-仿真
    bankType: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      // 弹窗title映射
      titleMapping: {
        batchExport: '批量导入',
        modalDelete: '删除'
      }
    }
  },
  inject: ['questionBankDeatail'],
  methods: {
    handleCommand(bankType) {
      this.$router.push({ name: 'questionBankCreate', params: { bankType: bankType, questionDepotId: this.questionBankDeatail.id }})
    },
    'clickDrop': function(name) {
      if (this.drawerAction && this.drawerAction.includes(name)) {
        this.drawerName = name
      } else {
        if (name === 'edit') {
          this.$router.push({ name: 'questionBankEdit', params: { id: this.selectItem[0].id, bankType: this.selectItem[0].bankType }})
        } else {
          this.modalName = name
        }
      }
    },
    confirmCall: function(type, data) {
      if (type === 'close') {
        this.modalClose()
      } else if (type === 'refresh') {
        this.$emit('call', type)
      }
    }
  }
}
</script>
