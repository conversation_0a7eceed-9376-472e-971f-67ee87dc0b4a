<template>
  <div class="resource-table">
    <!-- 操作区 -->
    <div class="operation-wrap">
      <div class="operation-left">
        <slot name="action" />
        <el-button type="primary" icon="el-icon-refresh" @click="refresh" />
        <el-tooltip transfer placement="right">
          <i class="cr-icon cr-icon-wenhao" />
          <div slot="content">列表仅展示"激活"、"已修复"、"已关闭"三种状态的数据</div>
        </el-tooltip>
      </div>
      <div class="operation-right">
        <el-badge :value="searchBtnShowNum">
          <el-button icon="el-icon-search" @click="openSearch">搜索</el-button>
        </el-badge>
        <t-table-config
          v-if="!customColData.length"
          :data="columnsObj"
          :active-key-arr="columnsViewArr"
          @on-change-col="onChangeCol"
        />
      </div>
    </div>
    <!-- 搜索区 -->
    <t-search-box
      v-show="searchView"
      :search-key-list="searchKeyListView"
      default-placeholder="默认搜索问题ID"
      @search="searchMultiple"
    />
    <!-- 列表 -->
    <t-table-view
      ref="tableView"
      :height="height"
      :single="single"
      :loading="tableLoading"
      :data="tableData"
      :total="tableTotal"
      :page-size="pageSize"
      :current="pageCurrent"
      :select-item="selectItem"
      type="list"
      current-key="id"
      @on-select="onSelect"
      @on-current="onCurrent"
      @on-change="changePage"
      @on-sort-change="onSortChange"
      @on-page-size-change="onPageSizeChange"
    >
      <el-table-column
        v-for="item in columnsViewArr"
        :key="item"
        :min-width="columnsObj[item].colMinWidth || colMinWidth"
        :width="columnsObj[item].colWidth"
        :label="columnsObj[item].title"
        :fixed="columnsObj[item].master ? 'left' : false"
        :sortable="columnsObj[item].sortable"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span v-if="item === 'problemNo'">
            <a
              :href="`/testing/allQuestionList/detail/${scope.row.id}/overview?routeSearch=true&searchVal=${scope.row.problemNo}&searchKey=problemNo&moduleName=allQuestionList`"
              target="_blank"
            >
              {{ scope.row[item] || '-' }}
            </a>
          </span>
          <span v-else-if="item === 'title'">
            <a
              :href="`/testing/allQuestionList/detail/${scope.row.id}/overview?routeSearch=true&searchVal=${scope.row.title}&searchKey=title&moduleName=allQuestionList`"
              target="_blank"
            >
              {{ scope.row[item] || '-' }}
            </a>
          </span>
          <span v-else-if="item === 'impactLevel'">
            <span :class="module.levelTextObj[scope.row[item]].type">{{ (module.levelTextObj[scope.row[item]] && module.levelTextObj[scope.row[item]].label) || '-' }}</span>
          </span>
          <span v-else-if="item === 'status'">
            <el-badge :type="module.statusTextObj[scope.row[item]] ? module.statusTextObj[scope.row[item]].type : 'info'" is-dot />
            <span>{{ (module.statusTextObj[scope.row[item]] && module.statusTextObj[scope.row[item]].label) || '-' }}</span>
          </span>
          <span v-else>
            {{ scope.row[item] || '-' }}
          </span>
        </template>
      </el-table-column>
    </t-table-view>
  </div>
</template>

<script>
import module from '../config.js'
import tSearchBox from '@/packages/search-box/index.vue'
import tTableView from '@/packages/table-view/index.vue'
import tTableConfig from '@/packages/table-config/table-col-config.vue'
import mixinsPageTable from '@/packages/mixins/page_table'
import { getSampleProblemPage } from '@/api/testing/index'

export default {
  components: {
    tSearchBox,
    tTableView,
    tTableConfig
  },
  mixins: [mixinsPageTable],
  data() {
    return {
      module,
      moduleName: module.name,
      // 搜索配置项
      searchKeyList: [
        { key: 'problemNo', label: '问题ID', master: true },
        { key: 'title', label: '问题标题' },
        { key: 'impactLevel', label: '严重程度', type: 'select', valueList: module.levelStrArr },
        { key: 'status', label: '状态', type: 'select', valueList: module.statusSearchArr },
        { key: 'relatedTasksName', label: '关联测试任务' },
        { key: 'round', label: '测试轮次' },
        { key: 'type', label: '问题类型', type: 'select', valueList: module.typeStrArr },
        { key: 'createUser', label: '创建人' },
        { key: 'time', label: '创建时间', type: 'time_range' },
        { key: 'updateAt', label: '最后更新时间', type: 'time_range' }
      ],
      // 所有可配置显示列
      columnsObj: {
        'problemNo': {
          title: '问题ID',
          master: true,
          sortable: true,
          colMinWidth: 100
        },
        'title': {
          title: '问题标题',
          sortable: true,
          colMinWidth: 250
        },
        'impactLevel': {
          title: '严重程度',
          sortable: true,
          colMinWidth: 100
        },
        'status': {
          title: '状态',
          sortable: true,
          colMinWidth: 80
        },
        'relatedTasksName': {
          title: '关联测试任务',
          sortable: true
        },
        'round': {
          title: '测试轮次',
          sortable: true,
          colMinWidth: 100
        },
        'type': {
          title: '问题类型',
          sortable: true
        },
        'createUser': {
          title: '创建人',
          colMinWidth: 100,
          sortable: true
        },
        'createAt': {
          title: '创建时间',
          sortable: true
        },
        'updateAt': {
          title: '最后更新时间',
          sortable: true
        }
      },
      // 当前显示列
      columnsViewArr: [
        'problemNo',
        'title',
        'impactLevel',
        'status',
        'relatedTasksName',
        'round',
        'type',
        'createUser',
        'createAt',
        'updateAt'
      ],
      searchParams: {},
      // 排序相关
      tbSort: '',
      tbOrder: ''
    }
  },
  created() {
    if (this.$route.name == 'sampleLibraryTaskDetail') {
      const newColumnsObj = { ...this.columnsObj }
      delete newColumnsObj.relatedTasksName
      this.columnsObj = newColumnsObj
      this.columnsViewArr = this.columnsViewArr.filter(key => key !== 'relatedTasksName')
      this.searchKeyList = this.searchKeyList.filter(item => item.key !== 'relatedTasksName')
    }
  },
  methods: {
    getList(params = {}, showLoading = true) {
      if (showLoading) {
        this.tableLoading = true
      }
      const postParams = this.getPostData('page', 'limit')
      // 处理搜索参数
      if (params.problemNo) {
        postParams.problemNo = params.problemNo
      }
      if (params.title) {
        postParams.title = params.title
      }
      if (params.type) {
        postParams.type = params.type.split(',')
      }
      if (params.status) {
        postParams.status = params.status.split(',')
      }
      if (params.relatedTasksName) {
        postParams.relatedTasksName = params.relatedTasksName
      }
      if (params.impactLevel) {
        postParams.impactLevel = params.impactLevel.split(',')
      }
      if (params.createUser) {
        postParams.createUser = params.createUser
      }
      if (params.time) {
        postParams.createTimeStart = params.time.split(',')[0]
        postParams.createTimeEnd = params.time.split(',')[1]
        delete postParams.time
      }
      if (params.updateAt) {
        postParams.updateTimeStart = params.updateAt.split(',')[0]
        postParams.updateTimeEnd = params.updateAt.split(',')[1]
        delete postParams.updateAt
      }
      if (this.$route.name == 'sampleLibraryDetail') {
        postParams.projectId = this.$route.params.id
      }
      if (this.$route.name == 'sampleLibraryTaskDetail') {
        postParams.taskId = this.$route.params.id
      }
      postParams.isFilter = 1
      // 处理排序
      if (this.tbSort) {
        postParams.tbSort = this.tbSort
        postParams.tbOrder = this.tbOrder || ''
      }
      getSampleProblemPage(postParams).then(res => {
        this.tableData = res.data.data.records || []
        this.tableTotal = Number(res.data.data.total) || 0
        this.tableLoading = false
      }).catch(() => {
        this.tableLoading = false
      })
    },
    // 处理搜索
    searchMultiple(params) {
      this.pageSize = 10
      this.pageCurrent = 1
      this.searchParams = { ...params }
      this.searchData = this.searchParams
      // 结合当前树节点和搜索条件进行搜索
      this.getList(params)
    },
    onSortChange({ column, prop, order }) {
      // 映射排序字段
      const fieldMapping = {
        '问题ID': 'problemNo',
        '问题标题': 'title',
        '严重程度': 'impactLevel',
        '状态': 'status',
        '关联测试任务': 'relatedTasksName',
        '测试轮次': 'round',
        '问题类型': 'type',
        '创建人': 'createUser',
        '创建时间': 'createAt',
        '最后更新时间': 'updateAt'
      }

      this.tbSort = fieldMapping[column.label] || ''
      if (column.order) {
        this.tbOrder = column.order == 'ascending' ? 'asc' : 'desc'
      } else {
        this.tbOrder = ''
      }

      this.getList(this.searchParams)
    }
  }
}
</script>
<style scoped>
.success {
  color: var(--color-600);
}

.warning {
  color: #E6A23C;
}

.primary {
  color: #409EFF;
}

.danger {
  color: #F56C6C;
}
</style>
