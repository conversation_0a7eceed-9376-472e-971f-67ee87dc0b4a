<template>
  <div class="resource-table">
    <el-alert :closable="false" type="warning">
      <div slot="title">
        任务状态为“ 测试中、测试通过、测试不通过 ”的任务支持提交问题。
      </div>
    </el-alert>
    <!-- 操作区 -->
    <div class="operation-wrap">
      <div class="operation-left">
        <slot name="action" />
        <el-button type="primary" icon="el-icon-refresh" @click="refresh" />
      </div>
      <div class="operation-right">
        <el-badge :value="searchBtnShowNum">
          <el-button icon="el-icon-search" @click="openSearch">搜索</el-button>
        </el-badge>
        <!-- 自定义表格列 -->
        <t-table-config
          :data="columnsObj"
          :active-key-arr="columnsViewArr"
          @on-change-col="onChangeCol"
        />
      </div>
    </div>
    <!-- 搜索区 -->
    <t-search-box
      v-show="searchView"
      :search-key-list="searchKeyListView"
      :default-placeholder="'默认搜索测试任务'"
      @search="searchMultiple"
    />
    <!-- 列表 -->
    <t-table-view
      ref="tableView"
      :height="height"
      :single="true"
      :loading="tableLoading"
      :data="tableData"
      :total="tableTotal"
      :page-size="pageSize"
      :current="pageCurrent"
      :select-item="selectItem"
      current-key="id"
      @on-select="onSelect"
      @on-current="onCurrent"
      @on-change="changePage"
      @on-sort-change="onSortChange"
      @on-page-size-change="onPageSizeChange"
    >
      <el-table-column
        v-for="item in columnsViewArr"
        :key="item"
        :min-width="colMinWidth"
        :label="columnsObj[item].title"
        :fixed="columnsObj[item].master ? 'left' : false"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span v-if="item === 'status'">
            <el-badge :type="module.testStatusStrArr[scope.row[item]] && module.testStatusStrArr[scope.row[item]].type" is-dot />
            {{ module.testStatusStrArr[scope.row[item]] && module.testStatusStrArr[scope.row[item]].label }}
          </span>
          <span v-else-if="item == 'name'">
            <a
              v-if="scope.row[item]"
              :href="`/testing/testing/testingTaskDetail/${scope.row.id}/${projectId}/overview?routeSearch=true&searchVal=${scope.row.name}&searchKey=name&moduleName=TestingTaskDetail`"
              target="_blank"
            >
              {{ scope.row[item] }}
            </a>
            <span v-else>-</span>
          </span>
          <span v-else-if="item === 'testerName'">
            <table-td-multi-col v-if="scope.row.testers && scope.row.testers.length > 0" :data="scope.row.testers" :number="scope.row.testers.length">
              <div slot="reference">{{ scope.row.testers[0] }}</div>
              <div v-for="val in scope.row.testers" :key="val.id">{{ val }}</div>
            </table-td-multi-col>
            <span v-else>-</span>
          </span>
          <span v-else>{{ scope.row[item] || '-' }}</span>
        </template>
      </el-table-column>
    </t-table-view>
  </div>
</template>
<script>
import tSearchBox from '@/packages/search-box/index.vue'
import mixinsPageTable from '@/packages/mixins/page_table'
import tTableView from '@/packages/table-view/index.vue'
import tTableConfig from '@/packages/table-config/table-col-config.vue'
import tableTdMultiCol from '@/packages/table-config/table-td-multi-col.vue'
import module from './config.js'
import { getProblemTestTaskPage } from '@/api/testing/index.js'

// 引入封装的方法
export default {
  components: {
    tSearchBox,
    tTableView,
    tTableConfig,
    tableTdMultiCol
  },
  mixins: [mixinsPageTable],
  props: {
    projectId: {
      type: Number,
      default: null
    }
  },
  data() {
    return {
      module,
      moduleName: module.name,
      searchKeyList: [
        { key: 'name', label: '测试任务', master: true, placeholder: '请输入' },
        { key: 'statusList', label: '状态', type: 'select', placeholder: '请选择', valueList: module.searchTestStatusArr },
        { key: 'round', label: '测试轮次', placeholder: '请输入' },
        { key: 'testerName', label: '测试人员', placeholder: '请输入' }
      ],
      columnsObj: {
        'name': {
          title: '测试任务', master: true
        },
        'status': {
          title: '状态'
        },
        'round': {
          title: '测试轮次'
        },
        'testerName': {
          title: '测试人员'
        }
      },
      columnsViewArr: [
        'name',
        'status',
        'round',
        'testerName'
      ]
    }
  },
  methods: {
    getList: function(showLoading = true) {
      if (showLoading) {
        this.tableLoading = true
      }
      const params = this.getPostData('page', 'limit')
      params.projectId = this.projectId
      getProblemTestTaskPage(params).then((res) => {
        const data = { ...res.data.data }
        this.tableData = data ? data.records : []
        this.tableTotal = Number(data.total) || 0
        this.tableLoading = false
        this.handleSelection()
      }).catch(() => {
        this.tableLoading = false
      })
    }
  }
}
</script>
