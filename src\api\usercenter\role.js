import request from '@/utils/request'
const headers = {
  'Content-Type': 'application/json;charset=UTF-8'
}

// 权限数据返回
export function adminAuthAPI(data) {
  return request({
    url: 'adminRole/auth',
    method: 'post',
    data: data
  })
}

// 获取角色列表
export function getRoleListAPI(data) {
  return request({
    url: 'adminRole/getRoleList',
    method: 'post',
    data: data,
    headers
  })
}

// 复制员工角色
export function relatedDeptUserAPI(data) {
  return request({
    url: 'adminRole/relatedDeptUser',
    method: 'post',
    data: data,
    headers
  })
}

// 根据角色类型获取角色列表
export function getRoleByType(roleType) {
  return request({
    url: `adminRole/getRoleByType/${roleType}`,
    method: 'post'
  })
}

// 删除
export function unbindingUserAPI(data) {
  return request({
    url: 'adminRole/unbindingUser',
    method: 'post',
    data: data
  })
}

// 关联员工
export function relatedUser(data) {
  return request({
    url: 'adminRole/relatedUser',
    method: 'post',
    data: data,
    headers
  })
}
