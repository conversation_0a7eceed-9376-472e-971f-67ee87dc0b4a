// 内容区 menu 菜单样式
$content-menu-height: 53px;
.content-menu {
  display: block;
  height: $content-menu-height;
  list-style: none;
  position: relative;
  margin-top: 5px;
  margin-bottom: 10px;
  &:after {
    content: '';
    display: block;
    width: 100%;
    height: 1px;
    background: #dcdee2;
    position: absolute;
    bottom: 0;
    left: 0;
  }
  & > li {
    float: left;
  }
  .link-item {
    display: inline-block;
    height: $content-menu-height;
    line-height: $content-menu-height;
    font-size: 15px;
    font-weight: 300;
    padding: 0 12px;
    margin-right: 10px;
    color: #333;
    transition: all .3s ease-in-out;
    position: relative;
    &:hover {
      color: var(--color-600);
    }
    &.router-link-active {
      color: #000;
      font-weight: 500;
      &:after {
        content: '';
        display: block;
        width: 100%;
        height: 2px;
        background: var(--color-600);
        position: absolute;
        bottom: 0;
        left: 0;
        z-index: 1;
      }
    }
  }
}
