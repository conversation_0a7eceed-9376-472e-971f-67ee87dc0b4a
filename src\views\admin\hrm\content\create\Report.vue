<template>
  <div v-loading="loading" class="container">
    <div class="flex-col wrapper">
      <div v-if="editMode">
        <el-upload
          :data="{contentId:contentId,format:'courseware'}"
          :headers="{'Admin-Token':token}"
          :show-file-list="false"
          :file-list="fileList"
          :on-preview="handlePreview"
          :http-request="httpRequest"
          :before-upload="handleUpload"
          :drag="editMode"
          :accept="accept"
          :disabled="!editMode"
          :multiple="multipleCourseware"
          class="upload-demo"
          action="/cepoTraining/api/training/content/uploadAttachment"
        >
          <div class="icon">
            <i class="icon-add iconfont"/>
          </div>
          <div class="el-upload__text">
            点击此处或拖拽文件到此处上传文件
            <div class="el-upload__tip">文件仅支持: {{ fileTypes.join("、") }}，大小限制在{{ maxFileSize }}MB以内</div>
          </div>
        </el-upload>
      </div>
      <el-row v-if="fileList.length != 0" :gutter="10" style="width: 100%;height: 100%;">
        <el-col :span="editMode ? 24 : 6" style="height: auto;">
          <FileList v-model="currentFileIndex" :list="fileList" :deletable="editMode" :downloadable="isDownload" :on-download="downloadReport" @click="handlePreview" @delete="deleteFile"/>
        </el-col>
        <el-col v-if="fileList.length" :span="editMode ? 24 : 18" style="height: 100%;">
          <div class="grid-view" style="width: 100%;height: 595px;">
            <div v-if="previewUrl" class="button-wrap">
              <el-link :underline="false" type="primary" size="small" @click="toggleFullscreen">
                {{ isFullscreen ? '退出全屏' : '全屏预览' }}
              </el-link>
            </div>
            <iframe id="frame" :src="previewUrl" allow="fullscreen" style="width: 100%; height: 100%; border: 0px; margin:0;"/>
          </div>
        </el-col>
      </el-row>
      <el-empty
        v-if="fileList.length === 0"
        :image="img"
        :image-size="110"
        style="margin: 100px auto"
        description="暂无数据"
      />
    </div>
    <div v-if="!showPackage" class="wrapper mt-10">
      <el-form ref="form" label-width="80px">
        <el-form-item label="附件">
          <el-upload
            :data="{contentId:contentId, format:'package'}"
            :headers="{'Admin-Token':token}"
            :http-request="httpRequest1"
            :before-upload="handleUpload1"
            :on-preview="handlePreview1"
            :before-remove="beforeRemovePackage"
            :on-remove="handleRemove"
            :file-list="fileList1"
            class="upload-demo1"
            action="/cepoTraining/api/training/content/uploadAttachment"
            multiple>
            <el-button size="small">上传附件</el-button>
            <div slot="tip" class="el-upload__tip">支持学员下载，单个附件大小不超过{{ maxFileSize }}MB</div>
          </el-upload>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>
<script>
import { contentdetail, contentdetailDelete, uploadFileRequest } from '@/api/teacher/index.js'
import { getFileSuffix } from '@/utils'
import mixin from './mixin'
import FileList from '@/components/FileList/index.vue'
import { handlekkfilePreview } from './util'
import { api as fullscreen } from 'vue-fullscreen'

export default {
  components: {
    FileList
  },
  mixins: [mixin],
  props: {
    showPackage: Boolean, // 是否显示附件下载
    // 是否可以下载
    isDownload: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      multipleCourseware: false,
      fileTypes: ['pdf', 'doc', 'docx', 'xlsx', 'xls', 'ppt', 'pptx'],
      downloading: false,
      docxShow: false,
      pdfShow: false,
      curriculumCoursewareUrl: '',
      token: '',
      noShow: false,
      curriculumCoursewareName: '',
      fileList: [],
      fileList1: [],
      fileName: '',
      currentFileIndex: 0,
      curriculumCoursewareUrldocx: '',
      curriculumCoursewareNamedocx: '',
      previewUrl: '',
      isFullscreen: false,
      maxFileSize: localStorage.getItem('maxFileSize'),
      packageAccept: '*',
      uploadStatus: 0,
      uploadStatusMapping: { // 1:上传中 2:上传成功 3:上传失败
        1: { type: '', text: '上传中' },
        2: { type: 'success', text: '已上传' },
        3: { type: 'danger', text: '上传失败' }
      }
    }
  },
  computed: {
    accept() {
      const arr = this.fileTypes.map((type) => '.' + type)
      return arr.join(',')
    }
  },
  watch: {
    fileList1(val) {
      if (val && val.length > 0) {
        this.uploadStatus = 2
      }
    }
  },
  mounted() {
    this.token = JSON.parse(localStorage.getItem('Admin-Token')).data
    this.searchCurriculum()
    this.searchPackage()
  },
  methods: {
    async handleIframepreview() {
      await this.$nextTick()
      handlekkfilePreview()
    },
    async toggleFullscreen() {
      const wrapperEl = this.$el.querySelector('.grid-view')
      await fullscreen.toggle(wrapperEl, {
        callback: (val) => {
          this.isFullscreen = val
        }
      })
      this.isFullscreen = fullscreen.isFullscreen
    },
    handleUpload1(file) {
      if (file.size > 1024 * 1024 * this.maxFileSize) {
        this.$message.error(`上传文件不能超过${this.maxFileSize}MB!`)
        return false
      }
      const promise = new Promise((resolve) => {
        this.$nextTick(function() {
          resolve(true)
        })
      })
      return promise
    },
    async downloadPackageFile(item) {
      const fileUrl = item.url
      const response = await fetch(fileUrl, {
        method: 'get',
        responseType: 'blob'
      })
      const blob = await response.blob()
      const a = document.createElement('a')
      const URL = window.URL || window.webkitURL
      const herf = URL.createObjectURL(blob)
      a.href = herf
      a.download = item.fileName
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(herf)
    },
    httpRequest1(file) {
      const formData = new FormData()
      formData.append('file', file.file)
      formData.append('contentId', this.contentId)
      formData.append('format', 'package')
      formData.append('name', file.file.name)
      this.uploadStatus = 1
      uploadFileRequest(formData).then(res => {
        this.searchPackage()
        this.$message({
          message: '上传成功',
          type: 'success'
        })
        this.uploadStatus = 2
      }).catch(() => {
        this.$message({
          message: '上传失败',
          type: 'warning'
        })
        this.uploadStatus = 3
      })
    },
    beforeRemovePackage(file) {
      if (file && file.status == 'success') {
        return this.$confirm(`此操作将永久删除 ${file.name} 文件, 是否继续?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
      }
    },
    // 删除附件
    handleRemove(file) {
      if (file && file.status == 'success') {
        contentdetailDelete({ attachmentId: file.attachmentId }).then(res => {
          if (res.code === 0 || res.code === 200) {
            this.$message.success('删除成功')
            // this.searchPackage()
            this.uploadStatus = 0
          }
        })
      }
    },
    onSuccess(res, file, fileList) {
      if (file) {
        this.$set(file, 'status', 'success')
        this.$set(file, 'attachmentId', res.data.id)
        this.$set(file, 'attachmentUrl', res.data.attachmentUrl)
        const url = window.location.origin + res.data.attachmentUrl
        this.$set(file, 'url', url)
        // 默认显示第一个附件
        const data = this.fileList[0]
        if (data) {
          this.handlePreview(file, 0)
        }
      }
    },
    httpRequest(file) {
      this.$set(file, 'status', 'uploading')
      this.$set(file, 'name', file.file.name)
      if (this.multipleCourseware) {
        this.fileList.push(file)
      } else {
        if (this.fileList[0]) {
          this.deleteFile(this.fileList[0], false)
        }
        this.fileList = [file]
      }
      // 处理上传进度事件
      const onUploadProgress = (e) => {
        if (e.total) {
          e.percentage = e.loaded / e.total * 100
          this.$set(file, 'percentage', e.percentage)
        }
      }
      this.$emit('call', 'uploading', true)
      const formData = new FormData()
      formData.append('file', file.file)
      formData.append('contentId', this.contentId)
      formData.append('format', 'courseware')
      formData.append('name', file.file.name)
      uploadFileRequest(formData, onUploadProgress).then(res => {
        if (res.code == 0) {
          this.onSuccess(res, file, this.fileList)
          this.$message({
            message: '上传成功',
            type: 'success'
          })
        } else {
          this.$set(file, 'status', 'error')
          this.$message({
            message: '上传失败',
            type: 'warning'
          })
        }
      }).finally(() => {
        this.$emit('call', 'uploading', false)
      })
    },
    searchPackage() {
      contentdetail({ contentId: this.contentId, format: 'package' }).then(res => {
        if (res.data.length > 0) {
          this.fileList1 = res.data
          this.fileList1.map(item => {
            item.name = item.fileName
            item.url = window.location.origin + item.attachmentUrl
          })
        }
      })
    },
    searchCurriculum() {
      contentdetail({ contentId: this.contentId, format: 'courseware' }).then(res => {
        if (res.code == 0) {
          this.handleIframepreview()
          this.fileList = res.data
          this.fileList.map(item => {
            item.name = item.fileName || item.attachmentUrl
            item.url = window.location.origin + item.attachmentUrl
          })
          // 默认显示第一个附件
          const file = this.fileList[0]
          if (file) {
            this.handlePreview(file, 0)
          }
        }
      })
    },
    handlePreview(file, index) {
      this.currentFileIndex = index
      if (['pdf'].includes(getFileSuffix(file.url))) {
        this.previewUrl = this.viewFileUrl + encodeURIComponent(btoa(file.url)) // kkfile 预览
      } else {
        this.previewUrl = this.viewFileUrl + encodeURIComponent(btoa(file.url)) // kkfile 预览
      }
    },
    handlePreview1(file, index) {
      this.currentFileIndex1 = index
    },
    // 删除文件
    deleteFile(item, showMessage = true) {
      if (item.status == 'success') {
        contentdetailDelete({ attachmentId: item.attachmentId }).then(res => {
          if (res.code == 0) {
            if (showMessage) {
              this.$message.success('文件删除成功')
            }
            const index = this.fileList.findIndex(file => file.attachmentId == item.attachmentId)
            index > -1 && this.fileList.splice(index, 1)
          }
        })
      } else {
        // 删除未上传成功的文件
        const index = this.fileList.findIndex(file => file.uid == item.uid)
        index > -1 && this.fileList.splice(index, 1)
      }
    },
    // 下载文件
    downloadReport(item) {
      console.log(item)
      const fileTypes = ['doc', 'docx', 'xlsx', 'xls', 'ppt', 'pptx']
      const type = getFileSuffix(item.url)
      let newFileUrl = ''
      if (item.url) {
        if (fileTypes.includes(type)) {
          newFileUrl = new URL(item.url).pathname
        }
        const fileUrl = fileTypes.includes(type) ? newFileUrl : item.url
        console.log('fileUrl', fileUrl)
        fetch(fileUrl, {
          method: 'get',
          responseType: 'blob'
        }).then((response) => response.blob())
          .then((blob) => {
            const a = document.createElement('a')
            const URL = window.URL || window.webkitURL
            const herf = URL.createObjectURL(blob)
            a.href = herf
            a.download = item.fileName || item.name
            document.body.appendChild(a)
            a.click()
            document.body.removeChild(a)
            URL.revokeObjectURL(herf)
          })
      }
    },
    handleUpload(file) {
      this.fileName = file.name
      const maxSize = this.maxFileSize * 1024 * 1024
      if (file.size > maxSize) {
        this.$message.warning(`文件大小超出限制（${this.maxFileSize}MB）`)
        return false
      }
      const fileArr = file.name.split('.')
      const fileType = fileArr[fileArr.length - 1]
      if (!this.fileTypes.includes(fileType.toLowerCase())) {
        this.$message.error('不支持该文件类型')
        return false
      }
      const promise = new Promise((resolve) => {
        this.$nextTick(function() {
          resolve(true)
        })
      })
      return promise
    }

  }
}
</script>
<style lang="scss" scoped>
.container {
  .wrapper {
    border-radius: 4px;
    padding: 10px 15px;
    background-color: #ffffff;
    .upload-demo{
      width: 100%;
      ::v-deep{
        .el-upload {
          width: 100%;
          .el-upload-dragger{
            width: 100%;
            height: 102px;
            .icon {
              margin: 26px 0 18px;
            }
          }
        }
        .el-upload__tip{
          font-size: 12px;
          font-family: HarmonyOS Sans SC-Regular, HarmonyOS Sans SC;
          font-weight: 400;
          color: #86909C;
        }
      }
    }
  }
  .grid-view {
    position: relative;
    width: 100%;
    height: 100%;
    margin-top: 10px;
    .button-wrap {
      position: absolute;
      top: 8px;
      right: 60px;
      .el-link {
        color: var(--color-600);
      }
    }
  }
}
.upload-demo1 {
  position: relative;
  .status-tag {
    position: absolute;
    left: 85px;
    top: 5px;
    text-align: center;
    height: 32px;
    line-height: 32px;
    margin-left: 15px;
    padding: 0 10px !important;
  }
}
// 去掉按 delete 键可删除提示
::v-deep .el-icon-close-tip {
  display: none !important;
}
</style>
