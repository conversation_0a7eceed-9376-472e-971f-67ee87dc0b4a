@import './transition.scss';
@import './element-variables.scss';
@import './button.scss';
@import './table.scss';
@import './base.scss';
// 指令
@import '../directives/empty/empty.scss';
@import '../directives/style.scss';
// 自定义组件库样式
@import '@/packages/styles/index.scss';
// 图标字体
@import '../assets/iconfont/iconfont.css'; // font-family: "wukong";
@import './iconfont/iconfont.css'; // font-family: "wk";
@import '../packages/assets/cr-icon/cr-icon.css'; // font-family: "cr-icon";

html {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

body {
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  margin: 0;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: '<PERSON><PERSON>aH<PERSON>', 'Avenir', Helvetica, Arial, sans-serif;
  color: rgba(0, 0, 0, 0.85);
  font-size: 14px;
  background: var(--neutral-0);
  overflow: hidden;
  line-height: 1.5715;
}

// 滚动条样式
::-webkit-scrollbar-track-piece {
  background: #EFEFEF;
}

::-webkit-scrollbar {
  width: 7px;
  height: 7px;
}

::-webkit-scrollbar-thumb {
  background: #C7D1DA;
  border-radius: 3.5px;
}

#app {
  height: 100%;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  outline: none;
  text-decoration: none;
}

div:focus {
  outline: none;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

tr,
th,
td {
  font-weight: normal;
}

li {
  list-style: none;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* 右浮动 */
.rt {
  float: right;
  margin-right: 30px;
}

/* 左浮动 */
.lt {
  float: left;
  margin-left: 30px;
}

input:invalid {
  box-shadow: none;
}

// 通过 popper-class 去除默认padding
.no-padding-popover {
  padding: 0;
}

.no-padding-dialog {
  .el-dialog__body {
    padding: 0;
  }
}


// 去除百度地图 图标
.BMap_cpyCtrl {
  display: none;
}

.anchorBL {
  display: none;
}

/** 懒加载样式*/
img[lazy="loading"] {
  display: inline-block;
  padding: 8px;
  // width: 20px !important;
  // height: 20px !important;
  margin: 0 auto;
}

div[lazy=loading] {
  display: inline-block;
  padding: 8px;
  // width: 20px !important;
  // height: 20px !important;
  margin: 0 auto;
}

/** 拖拽时候的样式 */
.draggingStyle {
  cursor: pointer;
}

.router-view {
  width: 100%;
  position: relative;
  // height: 100%;
  flex: 1;
  overflow: hidden;
}

.project-settings-list-top {
  top: 110px !important;
}

.task-board-rechristen-popover {
  padding: 0;
  margin-top: -40px !important;
}

.tooltip-change-border {
  border-color: #eee !important;
  box-shadow: 0 0 12px 1px #eee;
  padding: 5px 10px !important;
}

.tooltip-change-border .popper__arrow {
  border-color: transparent !important;
}

.task-tooltip {
  z-index: 10 !important;
}

// 关闭message 内容样式
.el-close-message {
  p {
    line-height: 20px;
    max-height: 60px;
    overflow-y: scroll;
    white-space: pre-wrap;
    word-wrap: break-word;
    margin-right: 13px;
  }
}

// 去除焦点时的蓝色边框
* :focus {
  outline: none;
}

// Pagination 分页
.el-pagination {
  * {
    font-size: 12px !important;
  }

  .number.active {
    list-style-position: inside;
    border: 1px solid var(--color-600);
    color: var(--color-600) !important;
    background-color: #f4f4f5 !important;
  }
}

// 可访问
.can-visit,
.can-visit--underline {
  color: $--color-primary !important;
  cursor: pointer
}

.can-visit--bold {
  font-weight: bold;
}

.header-can-visit-backgroud {
  background-color: #ebeef5 !important;
}


.can-visit--underline:hover {
  text-decoration: underline;
}

.can-visit-default {
  cursor: pointer
}

.can-visit-default:hover {
  color: $--color-primary !important;
}

// 滚动
.scroll-bottom-tips {
  text-align: center;
  font-size: 12px;
  color: #666;
}

// 金额展示
.xr-money {
  font-weight: 600;
  color: #333;
}

.xr-money.green {
  color: #20B559 !important;
}

.xr-money.red {
  color: #F94E4E !important;
}


// 禁止交互
.xr-disabled {
  cursor: not-allowed;
  pointer-events: none;
}

// 文本
.text-one-line {
  // text-overflow: ellipsis;
  // display: -webkit-box;
  // -webkit-line-clamp: 1;
  // -webkit-box-orient: vertical;
  // overflow: hidden;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.text-one-ellipsis {
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

// 项目下的侧滑框
.project-drawer {
  user-select: none;
  .el-drawer__header {
    display: none;
  }

  .el-drawer__body {
    padding: 15px;
    overflow: auto;
  }
}

#project-container {
  .el-dialog__wrapper,
  .v-modal {
    position: absolute;
  }
}

// 帮助提示符
.wk-help-tips {
  cursor: pointer;
  font-size: 13px;
  color: #cbcbcb;
}

.wk-help-tips:hover {
  color: $xr-color-primary;
}

// form 表单样式
.el-form-item.is-error {
  .xh-form-border {
    border-color: #F56C6C !important;
  }
}


// flex el-form
.el-form--flex.el-form {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -23px;
  .el-form-item.is-required .el-form-item__label:before {
    margin-right: 0;
  }

  .el-form-item {
    flex: 0 0 50%;
    flex-shrink: 0;
    padding: 0 30px;
    padding-bottom: 10px;
    margin-bottom: 4px;
    .el-form-item__label {
      color: #333333;
    }

    .el-form-item__error {
      padding-top: 2px;
    }
  }
}

// 时间区间选择快捷键
.el-picker-panel {
  .el-picker-panel__sidebar {
    padding-bottom: 40px;
  }
}

// 富文本编辑器全屏
.w-e-full-screen-container {
  z-index: 999;
}

// 详情页侧拉框
.detail-view.el-drawer__wrapper {
  background-color: transparent !important;
  pointer-events: none;
  .el-drawer {
    pointer-events: visible;
    .el-drawer__header {
      position: absolute;
      right: 0;
      z-index: 2000;
      border: none;
    }
  }
}

body #nprogress .spinner {
  display: none;
}

.category-fold-wrap {
  position: absolute;
  width: 60px;
  height: 24px;
  line-height: 22px;
  font-size: 12px;
  padding: 0;
  text-align: center;
  border-radius: 12px;
  background: #fff;
  border: 1px solid #E5E6EB;
  box-shadow: 0 4px 10px 0 rgba(0, 0, 0, .1);
  left: 50%;
  top: -28px;
  cursor: pointer;
  background: var(--color-50);
  color: var(--color-600);
  &.folded {
    height: 22px;
    top: -16px;
    line-height: 20px;
    border-radius: 0 0 12px 12px;
  }
  i {
    font-weight: bold;
    margin-left: 3px;
  }
  .rotate90deg {
    transform: rotate(90deg);
  }
  .rotate-90deg {
    transform: rotate(-90deg);
  }
}
