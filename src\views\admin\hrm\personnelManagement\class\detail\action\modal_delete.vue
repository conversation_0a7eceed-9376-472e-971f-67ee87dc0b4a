<template>
  <div v-loading="loading" class="dialog-wrap">
    <batch-template
      :data="data"
      :available-data="data"
      view-key="realname"
    />
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button :disabled="!data.length" type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>

<script>
import batchTemplate from '@/packages/batch-delete/modal-bat-template.vue'
import modalMixins from '@/packages/mixins/modal_form'
import { deleteClassDetail } from '@/api/admin/training/student'

export default {
  components: {
    batchTemplate
  },
  mixins: [modalMixins],
  props: {
    data: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      loading: false
    }
  },
  methods: {
    close() {
      this.$emit('close')
    },
    saveJoinCourse(postData) {
      return new Promise((resolve, reject) => {
        deleteClassDetail(postData).then(res => {
          resolve(res)
        })
      })
    },
    confirm: function() {
      this.loading = true
      const idArr = this.data.map(item => {
        return { userId: item.userId, realname: item.realname }
      })
      idArr.map((item, index) => {
        this.saveJoinCourse({ userId: item.userId, realname: item.realname, classCode: this.$route.params.classCode, majorCode: this.$route.params.majorCode })
          .then((res) => {
            this.$message.success(res.data.object)
          })
      })
      setTimeout(() => {
        this.$emit('call', 'refresh')
        this.close()
        this.loading = false
      }, 500)
    }
  }
}
</script>
