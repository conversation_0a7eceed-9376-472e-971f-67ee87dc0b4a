<template>
  <div v-loading="loading" class="dialog-wrap">
    <el-alert :closable="false" type="warning">
      <div slot="title">
        <p>删除角色将同时解除角色与资产的关联关系，请确认是否删除此角色?</p >
      </div>
    </el-alert>
    <batch-template
      :data="data"
      :available-data="data"
      view-key="sceneRoleName"
      post-key="roleId"
    />
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button :disabled="!data.length" type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>

<script>
import batchTemplate from '../../batch-delete/modal-bat-template.vue'
import modalMixins from '../../mixins/modal_form'
import { removeSceneRoleAPI } from '../api/role'

export default {
  components: {
    batchTemplate
  },
  mixins: [modalMixins],
  props: {
    data: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      loading: false
    }
  },
  methods: {
    close() {
      this.$emit('close')
    },
    confirm() {
      this.loading = true
      const postData = [this.data[0].roleId]
      removeSceneRoleAPI(postData).then(res => {
        this.$message.success('删除成功')
        this.$emit('call', 'sceneRole')
        this.close()
      }).catch(() => {
        this.loading = false
      })
    }
  }
}
</script>
