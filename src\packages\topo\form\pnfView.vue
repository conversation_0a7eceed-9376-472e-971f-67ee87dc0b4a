<template>
  <div v-loading="loading" class="drawer-wrap orchestration-drawer-wrap">
    <div class="drawer-wrap-content">
      <el-form ref="form" :model="formData" :rules="rules" label-width="110px">
        <template v-if="!disabledType">
          <el-form-item label="物理设备名称" prop="deviceName">
            {{ formData.deviceName }}
          </el-form-item>
          <el-form-item label="物理设备厂商" prop="vendor">
            {{ formData.vendor }}
          </el-form-item>
          <el-form-item label="物理设备型号" prop="model">
            {{ formData.model }}
          </el-form-item>
        </template>
        <el-form-item label="名称" prop="name">
          {{ formData.name || '-' }}
        </el-form-item>
        <el-form-item label="描述" prop="description">
          {{ formData.description || '-' }}
        </el-form-item>
        <el-form-item label="产品端口" class="port-form">
          <t-table-view
            ref="tableView"
            :height="null"
            :loading="loading"
            :data="formData.devicePorts"
            :total="formData.devicePorts.length"
            :multiple-page="false"
            type="list"
          >
            <el-table-column label="名称" fixed="left" show-overflow-tooltip>
              <template slot-scope="scope">
                {{ scope.row.port_name }}
              </template>
            </el-table-column>
            <el-table-column label="占用情况" show-overflow-tooltip>
              <template slot-scope="scope">
                <el-popover
                  :disabled="!scope.row.link_to && !scope.row.imported"
                  popper-class="link-port-detail-popover"
                  placement="top"
                  width="300"
                  trigger="click">
                  <table class="description-table">
                    <tbody v-if="scope.row.imported && scope.row.imported.length == 1">
                      <tr>
                        <th>拓扑名称：</th>
                        <td>{{ scope.row.imported[0].topology_name || '-' }}</td>
                      </tr>
                      <tr>
                        <th>设备名称：</th>
                        <td>{{ scope.row.imported[0].node_info ? scope.row.imported[0].node_info.name : '-' }}</td>
                      </tr>
                    </tbody>
                    <tbody v-else-if="scope.row.imported && scope.row.imported.length > 1">
                      <div v-for="(item, index) in scope.row.imported" :key="index">
                        <tr>
                          <th>拓扑名称：</th>
                          <td>{{ item.topology_name || '-' }}</td>
                        </tr>
                        <tr>
                          <th>设备名称：</th>
                          <td>{{ item.node_info.name || '-' }}</td>
                        </tr>
                        <el-divider v-if="index + 1 != scope.row.imported.length" style="margin: 10px 0;"/>
                      </div>
                    </tbody>
                    <tbody v-else-if="scope.row.link_to">
                      <tr>
                        <th>设备名称：</th>
                        <td>{{ scope.row.link_to.device_name || '-' }}</td>
                      </tr>
                      <tr>
                        <th>关联端口：</th>
                        <td>{{ scope.row.link_to.port_name || '-' }}</td>
                      </tr>
                      <tr>
                        <th>设备厂商：</th>
                        <td>{{ scope.row.link_to.device_vendor || '-' }}</td>
                      </tr>
                      <tr>
                        <th>设备型号：</th>
                        <td>{{ scope.row.link_to.device_model || '-' }}</td>
                      </tr>
                      <tr>
                        <th>设备分类：</th>
                        <td>{{ scope.row.link_to.device_category }}</td>
                      </tr>
                    </tbody>
                  </table>
                  <div slot="reference">
                    <div v-if="scope.row.imported">
                      <a href="javascript:;">已引入</a>
                    </div>
                    <div v-else-if="scope.row.link_to">
                      <a href="javascript:;">已占用</a>
                    </div>
                    <span v-else>{{ scope.row.reserved ? '已预留' : '-' }}</span>
                  </div>
                </el-popover>
              </template>
            </el-table-column>
          </t-table-view>
        </el-form-item>
      </el-form>
    </div>
    <div class="drawer-footer">
      <el-button type="primary" @click="close">确定</el-button>
    </div>
  </div>
</template>
<script>
import tTableView from '../../table-view/index.vue'
import { getPort } from '../api/physical_device'
export default {
  components: { tTableView },
  props: {
    data: {
      type: Object,
      default: () => {
        return {}
      }
    },
    type: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      loading: true,
      formData: {
        'name': '',
        'deviceName': '',
        'description': '',
        'vendor': '',
        'model': '',
        'ports': [],
        'devicePorts': []
      },
      rules: {}
    }
  },
  computed: {
    disabledType() {
      return this.type !== 'allPermissions' && this.type !== 'templatePermissions'
    }
  },
  created() {
    const data = this.data.node.data
    this.formData['name'] = data['name']
    this.formData['deviceName'] = data['deviceName']
    this.formData['description'] = data['description']
    this.formData['vendor'] = data['vendor']
    this.formData['model'] = data['model']
    this.formData['ports'] = data['ports']
    this.formData['devicePorts'] = data['devicePorts']
    this.getDevicePorts()
  },
  methods: {
    // 重新获取设备的端口
    'getDevicePorts': function() {
      const postData = { 'device_id': this.data.node.data.id }
      getPort(postData)
        .then(res => {
          this.formData['devicePorts'] = res.data.data.result
          this.loading = false
        })
        .catch((error) => {
          this.loading = false
          console.log(error)
        })
    },
    'close': function() {
      this.$emit('close')
    }
  }
}
</script>
<style lang="less">
.orchestration-drawer-wrap {
  .el-form {
    & >.el-form-item > .el-form-item__content > .el-input,
    & >.el-form-item > .el-form-item__content > .el-select,
    & >.el-form-item > .el-form-item__content > .el-textarea {
      width: 90%;
    }
  }
  .port-form {
    th {
      padding: 0;
    }
    >.el-form-item__label {
      width: 70px !important;
    }
    >.el-form-item__content {
      margin-left: 70px !important;
      // margin-top: 40px;
      .data-table-footer {
        display: none;
      }
    }
  }
}
</style>
<style scoped>
.el-divider--horizontal {
  margin: 10px 0 ;
}
</style>
