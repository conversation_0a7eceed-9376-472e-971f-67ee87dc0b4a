<template>
  <div class="resource-table">
    <!-- 操作区 -->
    <div class="operation-wrap">
      <div class="operation-left">
        <slot name="action" />
        <el-button type="primary" icon="el-icon-refresh" @click="refresh" />
        <el-checkbox v-model="isAll" style="margin-left: 35px;">仅查看自己创建</el-checkbox>
      </div>
      <div class="operation-right">
        <el-badge :value="searchBtnShowNum">
          <el-button icon="el-icon-search" @click="openSearch">搜索</el-button>
        </el-badge>
        <!-- 自定义表格列 -->
        <t-table-config
          v-if="!customColData.length"
          :data="columnsObj"
          :active-key-arr="columnsViewArr"
          @on-change-col="onChangeCol"
        />
      </div>
    </div>
    <!-- 搜索区 -->
    <t-search-box
      v-show="searchView"
      :search-key-list="searchKeyListView"
      default-placeholder="默认搜索全部"
      @search="searchMultiple"
    />
    <!-- 列表 -->
    <t-table-view
      ref="tableView"
      :height="height"
      :single="false"
      :loading="tableLoading"
      :data="examQuestionList"
      :total="tableTotal"
      :page-size="pageSize"
      :current="pageCurrent"
      :select-item="selectItem"
      current-key="userId"
      @on-select="onSelect"
      @on-current="onCurrent"
      @on-change="changePage"
      @on-sort-change="onSortChange"
      @on-page-size-change="onPageSizeChange"
    >
      <el-table-column v-if="columnsViewArr.includes('name')" key="name" :min-width="colMinWidth" prop="name" label="名称" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.name }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="columnsViewArr.includes('contentCategoryName')" key="contentCategoryName" :min-width="colMinWidth" prop="contentCategoryName" label="分类" show-overflow-tooltip/>
      <el-table-column v-if="columnsViewArr.includes('contentPeriod')" key="contentPeriod" :min-width="colMinWidth" label="课时" prop="contentPeriod" show-overflow-tooltip/>
      <el-table-column v-if="columnsViewArr.includes('userName')" key="userName" :min-width="colMinWidth" label="创建人" prop="userName" show-overflow-tooltip/>
    </t-table-view>
  </div>
</template>

<script>
import module from '../config.js'
import tSearchBox from '@/packages/search-box/index.vue'
import tTableView from '@/packages/table-view/index.vue'
import tTableConfig from '@/packages/table-config/table-col-config.vue'
import tableTdMultiCol from '@/packages/table-config/table-td-multi-col.vue'
import mixinsPageTable from '@/packages/mixins/page_table'
import { pjtContentQuery } from '@/api/teacher/index.js'

export default {
  components: {
    tSearchBox,
    tTableView,
    tTableConfig,
    tableTdMultiCol
  },
  mixins: [mixinsPageTable],
  props: {
    categoryId: {
      type: String || Number
    }
  },
  data() {
    return {
      moduleName: module.name,
      // 搜索配置项
      searchKeyList: [
        { key: 'name', label: '名称', master: true }
      ],
      // 所有可配置显示列 master：不可隐藏 title:列名称
      columnsObj: {
        'name': {
          title: '名称',
          master: true
        },
        'contentCategoryName': {
          title: '分类'
        },
        'contentPeriod': {
          title: '课时'
        },
        'userName': {
          title: '创建人'
        }
      },
      // 当前显示列key表 默认，如果localStorage有数据将被覆盖
      columnsViewArr: [
        'name',
        'contentCategoryName',
        'contentPeriod',
        'userName'
      ],
      examQuestionList: [],
      isAll: false
    }
  },
  watch: {
    isAll() {
      this.refresh()
    }
  },
  methods: {
    getList: function(showLoading = true) {
      if (showLoading) {
        this.tableLoading = true
      }
      const data = {
        limit: this.pageSize,
        page: this.pageCurrent,
        contentCategoryId: this.categoryId,
        createdBy: this.isAll ? JSON.parse(localStorage.getItem('loginUserInfo')).userId : ''
      }
      pjtContentQuery(Object.assign(data, this.searchData)).then(res => {
        this.examQuestionList = res.data.records
        this.tableTotal = res.data.total
        this.tableLoading = false
      }).catch(() => {
        this.tableLoading = false
      })
    }
  }
}
</script>
