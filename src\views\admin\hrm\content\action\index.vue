<template>
  <div class="buttons-wrap">
    <div :class="{ 'folded': fold }" class="category-fold-wrap" @click="handleFold">
      <div v-if="fold">展开<i class="el-icon-d-arrow-left rotate-90deg" /></div>
      <div v-else>折叠<i class="el-icon-d-arrow-left rotate90deg" /></div>
    </div>
    <el-button type="primary" icon="el-icon-plus" @click="createContent('创建')">创建</el-button>
    <el-dropdown trigger="click" placement="bottom-start" @command="currentClickDrop">
      <el-button type="primary">
        操作<i class="el-icon-arrow-down el-icon--right" />
      </el-button>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item :disabled="singleDisabled" command="editTestPaper">编辑</el-dropdown-item>
        <el-dropdown-item :disabled="multipleDisabled" command="deleteTestPaper">删除</el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
    <!-- 中部弹窗 start-->
    <el-dialog
      :title="titleMapping[modalName]"
      :visible.sync="modalShow"
      :width="modalWidth"
      :destroy-on-close="true"
      @close="modalClose"
    >
      <transition name="el-fade-in-linear">
        <component
          :is="modalName"
          :name="modalName"
          :data="selectItem"
          :check-type="checkType"
          @close="modalClose"
          @call="confirmCall"
        />
      </transition>
    </el-dialog>
    <!-- 中部弹窗 end-->
    <!-- 侧拉弹窗 start -->
    <el-drawer
      :title="titleMapping[drawerName]"
      :visible.sync="drawerShow"
      :size="drawerWidth"
      @close="drawerClose"
    >
      <transition name="el-fade-in-linear">
        <component
          :is="drawerName"
          :name="drawerName"
          :data="selectItem"
          @close="drawerClose"
          @call="drawerConfirmCall"
        />
      </transition>
    </el-drawer>
    <!-- 侧拉弹窗 end -->
  </div>
</template>
<script>
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
import editChecked from './modal-checked.vue'
import deleteTestPaper from './modal-delete.vue'
import { EventBus } from '../eventBus.js'
export default {
  name: 'ActionContent',
  components: {
    deleteTestPaper,
    editChecked
  },
  mixins: [mixinsActionMenu],
  data() {
    return {
      fold: false,
      // 弹窗title映射
      titleMapping: {
        'deleteTestPaper': '删除',
        'editChecked': '编辑课程内容'
      },
      dialogLoading: false,
      confirmDisabled: false,
      setConfigObj: {},
      courseData: [],
      checkType: 'inSelective'
    }
  },
  created() {
    EventBus.$on('getCourseData', (data) => {
      this.courseData = data[0]
      const { contentType, name } = this.courseData
      this.setConfigObj = Object.assign({ curriculumName: '课程内容', teachingName: name, type: '编辑', curriculumType: contentType == 1 ? '理论' : '仿真', oneLevelTitle: '课程内容', oneLevelName: 'trainingContent' }, this.courseData)
    })
  },
  beforeDestroy() {
    EventBus.$off() // 解绑所有事件监听器
  },
  methods: {
    handleFold() {
      this.fold = !this.fold
      this.$emit('call', 'fold', this.fold)
    },
    createContent(type) {
      this.$router.push({ name: 'trainingContentCreate', query: { type: type }})
    },
    currentClickDrop(name) {
      if (name === 'editTestPaper') {
        // 内容加入课程 inCourse
        if (this.selectItem[0].inCourse) {
          this.checkType = 'inCourse'
          this.clickDrop('editChecked')
          return
        }
        // 是否加入自学 inSelective
        if (this.selectItem[0].inSelective) {
          this.checkType = 'inSelective'
          this.clickDrop('editChecked')
        } else {
          this.handleDetailClick(this.selectItem[0], '编辑')
        }
      } else {
        this.clickDrop(name)
      }
    },
    // 编辑
    handleDetailClick(row, type) {
      const { contentType, name } = row
      this.$router.push({ name: 'trainingContentCreate', query: Object.assign({ curriculumName: '课程内容', teachingName: name, type: '编辑', joinType: '编辑', curriculumType: contentType == 1 ? '理论' : '仿真', oneLevelTitle: '课程内容', oneLevelName: 'trainingContent' }, row) })
    },
    confirmCall: function(type, data) {
      if (type === 'close') {
        this.modalClose()
      } else if (type === 'refresh') {
        this.$emit('call', type, data)
      } else if (type === 'detail') {
        this.handleDetailClick(this.selectItem[0], '编辑')
      }
    },
    drawerConfirmCall: function(type, data) {
      if (type === 'close') {
        this.drawerClose()
      } else if (type === 'refresh') {
        this.$emit('call', type)
      }
    }
  }
}
</script>
