<template>
  <div class="plugin-view" style="padding-bottom: 0px;">
    <h3 class="plugin-title" style="margin-bottom: 20px;">{{ pluginTitle }}</h3>
    <div v-loading="true" v-if="loading" class="plugin-loading" />
    <div v-else-if="!apiData" class="flex-1 flex-center">暂无数据</div>
    <div v-else class="plugin-quota">
      <div
        v-for="(value, key) in apiData"
        :key="key"
        class="quota-item-wrap"
      >
        <div class="quota-item">
          <div class="title">{{ nameMap[key] }}</div>
          <div class="content">
            <div class="progress">
              <el-progress :percentage="(value / total) * 100" :stroke-width="16" :show-text="false" />
            </div>
            <div class="unit">
              <span> {{ reValueView(value, 'B') }} </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="less">
.plugin-quota {
  .quota-progress-wrong .ivu-progress-bg {
    background-color: #ed4014;
  }
  .has-icon-progress .ivu-progress-bg {
    background-color: #00a0e9;
  }
  .ivu-carousel-dots {
    margin-top: 0px;
  }
  max-height: 160px;
  overflow: hidden;
  .quota-item-wrap {
    width: 100%;
    display: flex;
    padding: 3px;
  }
  .quota-item {
    display: flex;
    width: 100%;
    padding: 4px;
    font-size: 14px;
    font-weight: 700;
    .title {
      width: 60px;
      height: 18px;
      padding: 0 3px;
      .ivu-tooltip {
        width: 100%;
        .ivu-tooltip-rel {
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
        }
      }
    }
    .content {
      display: flex;
      justify-content: flex-start;
      width: 100%;
      .progress {
        display: inline-block;
        flex: 1;
      }
      .unit {
        width: 137px;
        display: flex;
        align-items: flex-start;
        margin-left: 8px;
      }
    }
  }
}
</style>
<script>
import pluginMixin from './mixin_plugin.js'
export default {
  mixins: [
    pluginMixin
  ],
  props: {
    data: Object
  },
  data() {
    return {
      'total': 0,
      'nameMap': {
        'screenRecording': '录屏',
        'traffic': '流量',
        'tool': '工具',
        'report': '报告'
      }
    }
  },
  methods: {
    'reValueView': function(value, unit = '个') {
      const storUnit = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']
      let viewValue = value
      if (storUnit.includes(unit)) {
        viewValue = this.$options.filters['transStore'](Number(viewValue), unit, 2)
      } else {
        viewValue = viewValue + ' ' + unit
      }
      return viewValue
    },
    'getData': function(hideloading) {
      if (!hideloading) {
        this.loading = true
      }
      let url = ''
      let params = null
      switch (this.pluginApiType) {
        case 'storage_used':
          url = 'overviewQuery'
          params = { items: 'storageUsedStatistics' }
          break
        default:
          break
      }
      this.$api[url](params).then(res => {
        this.loading = false
        this.apiData = res.data
        this.total = Object.values(this.apiData).reduce(function(prev, item, index, arr) {
          return prev + item
        })
      }).catch(() => {
        this.loading = false
      })
    }
  }
}
</script>
