<template>
  <div style="padding: 10px 0;">
    <transverse-list :data="classificationList" :allow-deletion="false" :allow-add="false" :allow-edit="false" title="分类" @node-click="classificationType"/>
  </div>
</template>
<script>
import transverseList from '@/packages/transverse-list/index.vue'
import module from '../config.js'
import { queryCourseCategory } from '@/api/teacher/index.js'
export default {
  components: {
    transverseList
  },
  data() {
    return {
      moduleName: module.name,
      classificationList: []
    }
  },
  created() {
    this.searchPoint()
  },
  methods: {
    searchPoint() {
      queryCourseCategory({ id: '' }).then(res => {
        this.classificationList = res.data
        this.classificationList.map(item => {
          item.categoryName = item.name
          item.categoryCode = item.id
        })
      })
    },
    classificationType(item) {
      this.categoryCode = item.categoryCode
      this.$emit('classificationType', { courseCategoryId: item.categoryCode })
    }
  }
}
</script>
<style lang="scss" scoped>


</style>
