<template>
  <div class="buttons-wrap">
    <el-button slot="action" type="primary" @click="currentClickDrop('arrangeCourse')">排课</el-button>
    <el-dropdown trigger="click" placement="bottom-start" @command="currentClickDrop">
      <el-button type="primary">
        操作<i class="el-icon-arrow-down el-icon--right" />
      </el-button>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item :disabled="singleDisabled || selectItem[0]['classStatus'] != 1" type="primary" command="editTime">修改上课时间</el-dropdown-item>
        <el-dropdown-item :disabled="disabledOpenEnvBtn" command="openEnv">开启题目环境</el-dropdown-item>
        <el-dropdown-item :disabled="disabledCloseEnvBtn" command="closeEnv">关闭题目环境</el-dropdown-item>
        <el-dropdown-item command="exportStudent">导出</el-dropdown-item>
        <el-dropdown-item :disabled="singleDisabled || selectItem[0].schedulingType !== '模拟练习'" type="primary" command="examAnalyse">分析</el-dropdown-item>
        <el-dropdown-item :disabled="multipleDisabled" type="primary" command="cancel">取消排课</el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
    <!-- 中部弹窗 start-->
    <el-dialog
      :title="titleMapping[modalName]"
      :visible.sync="modalShow"
      :width="modalWidth"
      @close="modalClose"
    >
      <transition name="el-fade-in-linear">
        <component
          :is="modalName"
          :name="modalName"
          :data="selectItem"
          @close="modalClose"
          @call="confirmCall"
          @exportOut="exportAffairs"
        />
      </transition>
    </el-dialog>
    <!-- 中部弹窗 end-->
    <!-- 侧拉弹窗 start -->
    <el-drawer
      :title="titleMapping[drawerName]"
      :visible.sync="drawerShow"
      :size="drawerWidth"
      @close="drawerClose"
    >
      <transition name="el-fade-in-linear">
        <component
          :is="drawerName"
          :name="drawerName"
          :data="selectItem"
          @close="drawerClose"
          @call="drawerConfirmCall"
          @exportOut="exportAffairs"
        />
      </transition>
    </el-drawer>
  <!-- 侧拉弹窗 end -->
  </div>
</template>
<script>
import axios from 'axios'
import { Loading } from 'element-ui'
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
import exportStudent from './modal_export.vue'
import cancel from './modal-cancel.vue'
import openEnv from './modal-envHandle.vue'
import closeEnv from './modal-envHandle.vue'
import editTime from './modal_editTime.vue'
import arrangeCourse from './arrange-course.vue'
import allStudent from '@/components/modal-export-all.vue'
export default {
  components: {
    arrangeCourse,
    exportStudent,
    cancel,
    allStudent,
    editTime,
    openEnv,
    closeEnv
  },
  mixins: [mixinsActionMenu],
  props: {
    timeParams: {
      type: Object
    },
    paramsData: {
      type: Object
    },
    transmitTime: {
      type: Object
    }
  },
  data() {
    return {
      drawerAction: ['arrangeCourse'], // 需要侧拉打开的操作
      // 弹窗title映射
      titleMapping: {
        'arrangeCourse': '排课',
        'exportStudent': '导出',
        'allStudent': '导出',
        'openEnv': '开启题目环境',
        'closeEnv': '关闭题目环境',
        'editTime': '修改上课时间',
        'cancel': '取消排课'
      },
      confirmDisabled: false
    }
  },
  computed: {
    disabledOpenEnvBtn() {
      let bool = this.singleDisabled
      if (!bool) {
        const { schedulingType, curriculumType, topologyFlag, envStatus, classStatus } = this.selectItem[0]
        if (curriculumType == 1 || schedulingType == '项目') {
          bool = true
        } else if (curriculumType == 2 || schedulingType == '模拟练习') {
          if (topologyFlag == 1) {
            // 未开始
            if (classStatus == 1) {
              bool = envStatus != '0'
            } else if (classStatus == 2) { // 已结束
              bool = true
            } else if (classStatus == 3) { // 进行中
              bool = envStatus != '0'
            }
          } else {
            bool = true // 没有环境题禁用按钮
          }
        }
      }
      return bool
    },
    disabledCloseEnvBtn() {
      let bool = this.singleDisabled
      if (!bool) {
        const { schedulingType, curriculumType, topologyFlag, envStatus, classStatus } = this.selectItem[0]
        if (curriculumType == 1 || schedulingType == '项目') {
          bool = true
        } else if (curriculumType == 2 || schedulingType == '模拟练习') {
          if (topologyFlag == 1) {
            // 未开始
            if (classStatus == 1) {
              if (envStatus == '0') {
                bool = true
              } else if (envStatus == '1') {
                bool = false
              } else if (envStatus == '2') {
                bool = false
              } else if (envStatus == '3') {
                bool = true
              }
            } else if (classStatus == 2) { // 已结束
              if (envStatus == '0') {
                bool = true
              } else if (envStatus == '1') {
                bool = false
              } else if (envStatus == '2') {
                bool = false
              } else if (envStatus == '3') {
                bool = true
              }
            } else if (classStatus == 3) { // 进行中
              bool = envStatus != '2'
            }
          } else {
            bool = true // 没有环境题禁用按钮
          }
        }
      }
      return bool
    }
  },
  methods: {
    confirmCall: function(type, data) {
      if (type === 'close') {
        this.modalClose()
      } else if (type === 'refresh') {
        this.$emit('call', type)
      }
    },
    currentClickDrop(str) {
      if (str === 'examAnalyse') {
        this.examAnalyse(this.selectItem[0])
      } else if (str === 'exportAffairs') {
        this.exportAffairs(this.selectItem)
      } else {
        this.clickDrop(str)
      }
    },
    // 导出事务表
    exportAffairs() {
      const loading = Loading.service({ fullscreen: true, text: '导出中...' })
      const query = {
        ...this.timeParams,
        teachingAffairVoList: []
      }
      query.state = query.classStatus
      query.majorName = query.likeName
      delete query.likeName
      this.selectItem.forEach(item => {
        query.teachingAffairVoList.push(item)
      })
      query.teachingAffairVoList.length >= 1 ? query.exportType = 2 : query.exportType = 1
      axios({
        method: 'post',
        url: '/api/training/pjtTeacherScheduling/backExportqueryAffair',
        data: query,
        responseType: 'blob'
      }).then((res) => {
        const url = window.URL.createObjectURL(new Blob([res.data]))
        const link = document.createElement('a')
        link.style.display = 'none'
        link.href = url
        link.setAttribute('download', `教学事务表.xls`)
        document.body.appendChild(link)
        loading.close()
        this.$message.success('导出成功')
        link.click()
      }).catch(() => {
        loading.close()
        this.$message.warning('导出失败')
      })
    },
    drawerConfirmCall: function(type, data) {
      if (type === 'close') {
        this.drawerClose()
      } else if (type === 'refresh') {
        this.$emit('call', type)
      }
    },
    examAnalyse(item) {
      if (item.schedulingType === '模拟练习') {
        this.$router.push({
          name: 'examanalysis',
          query: {
            classCode: item.resultList[0].classCode,
            schedulingCode: item.resultList[0].schedulingCode,
            examCode: item.resultList[0].curriculumCode,
            content: item.content,
            resultList: JSON.stringify(item.resultList)
          }
        })
      }
    },
    'clickDrop': function(name) {
      if (this.drawerAction && this.drawerAction.includes(name)) {
        this.drawerName = name
      } else {
        this.modalName = name
      }
    }
  }
}
</script>
