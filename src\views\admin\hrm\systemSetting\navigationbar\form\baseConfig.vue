<template>
  <div v-loading="loading" class="dialog-wrap">
    <el-form ref="form" :model="formData" label-position="left" label-width="110px">
      <!-- 基本配置 -->
      <el-form-item v-if="name == 'baseConfig'" label="后台管理地址" prop="configName">
        <el-input v-model.trim="formData.configName" :maxlength="64"/>
      </el-form-item>
      <!-- logo配置 -->
      <div v-if="name == 'logoConfig'">
        <el-form-item label="导航栏logo" prop="configUrl">
          <el-upload
            v-if="!formData.configUrl"
            :show-file-list="false"
            :http-request="fileHeadUpload"
            drag
            class="upload"
            action="http"
            accept="image/png, image/jpeg, image/gif, image/jpg">
            <i class="el-icon-plus uploader-icon"/>
          </el-upload>
          <div v-else class="upload-show">
            <img v-src="formData.configUrl">
            <i class="el-icon-remove icon-delete" @click="deleteImage('configUrl')"/>
          </div>
        </el-form-item>
        <el-form-item label="登录页logo" prop="linkUrl">
          <el-upload
            v-if="!formData.linkUrl"
            :show-file-list="false"
            :http-request="fileLogoUpload"
            drag
            class="upload"
            action="http"
            accept="image/png, image/jpeg, image/gif, image/jpg">
            <i class="el-icon-plus uploader-icon"/>
          </el-upload>
          <div v-else class="upload-show">
            <img v-src="formData.linkUrl">
            <i class="el-icon-remove icon-delete" @click="deleteImage('linkUrl')"/>
          </div>
        </el-form-item>
        <el-form-item label="缩略站点logo" prop="iconUrl">
          <el-upload
            v-if="!formData.iconUrl"
            :show-file-list="false"
            :http-request="fileIconUpload"
            drag
            class="upload"
            action="http"
            accept="image/png, image/jpeg, image/gif, image/jpg, image/ico">
            <i class="el-icon-plus uploader-icon"/>
          </el-upload>
          <div v-else class="upload-show upload-show-rect">
            <img v-src="formData.iconUrl">
            <i class="el-icon-remove icon-delete" @click="deleteImage('iconUrl')"/>
          </div>
        </el-form-item>
      </div>
      <!-- 基本配置 -->
      <div v-if="name == 'loginConfig'">
        <el-form-item label="产品名称" prop="productName">
          <el-input
            v-model="formData.productName"
            :autosize="false"
            :rows="3"
            type="textarea"
            style="width: 100%"
            maxlength="64"
            placeholder="请输入"
          />
        </el-form-item>
        <el-form-item label="产品描述" prop="productDesc">
          <el-input
            v-model="formData.productDesc"
            :autosize="false"
            :rows="3"
            type="textarea"
            style="width: 100%"
            maxlength="255"
            placeholder="请输入"
          />
        </el-form-item>
        <el-form-item label="单位信息" prop="unitInformation">
          <myEditor
            :key="timer"
            :content="formData.unitInformation"
            :only-editor="false"
            :is-read-only="false"
            id-prefix="unitInformation"
            width="100%"
            height="200px"
            @contentChange="contentChange('unitInformation', $event)"
          />
        </el-form-item>
      </div>
    </el-form>
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>
<script>
import { saveBaseInfoConfig } from '@/api/admin/config'
import { RichTextUpload } from '@/api/admin/file.js'
import myEditor from '@/packages/editor/index.vue'
export default {
  components: {
    myEditor
  },
  props: {
    // 传入数据
    data: {
      type: Object,
      default: () => null
    },
    name: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      loading: false,
      formData: {
        configId: '',
        configName: '',
        configUrl: '',
        linkUrl: '',
        iconUrl: '',
        productName: '',
        productDesc: '',
        unitInformation: ''
      },
      timer: Date.now()
    }
  },
  mounted() {
    if (this.data) {
      for (const key in this.formData) {
        this.formData[key] = this.data[key]
      }
    }
  },
  methods: {
    // 通用上传方法
    handleFileUpload(params, acceptTypes, key, warnMsg) {
      const suffix = String(params.file.name).substring(String(params.file.name).lastIndexOf('.') + 1).toLowerCase()
      if (acceptTypes.includes(suffix)) {
        this.loading = true
        const formData = new FormData()
        formData.append('file', params.file)
        RichTextUpload(formData).then((res) => {
          if (res.code == 0) {
            this.formData[key] = res.data.url
          }
        }).finally(() => {
          this.loading = false
        })
      } else {
        this.$message.warning(warnMsg)
      }
    },
    fileHeadUpload(params) {
      this.handleFileUpload(
        params,
        ['jpg', 'png', 'jpeg'],
        'configUrl',
        '上传文件格式不支持，仅支持png,jpeg,jpg格式文件'
      )
    },
    fileLogoUpload(params) {
      this.handleFileUpload(
        params,
        ['jpg', 'png', 'jpeg'],
        'linkUrl',
        '上传文件格式不支持，仅支持png,jpeg,jpg格式文件'
      )
    },
    fileIconUpload(params) {
      this.handleFileUpload(
        params,
        ['jpg', 'png', 'jpeg', 'ico'],
        'iconUrl',
        '上传文件格式不支持，仅支持png,jpeg,jpg,ico格式文件'
      )
    },
    // 删除图片
    deleteImage(type) {
      this.$set(this.formData, type, '')
    },
    // 富文本编辑器改变内容时
    'contentChange': function(key, value) {
      if (this.delHtml(value)) {
        this.formData[key] = value
      } else {
        const imgStrs = value.match(/<img.*?>/g)
        if (imgStrs && imgStrs.length) { // 内容只有图片时
          this.formData[key] = value
        } else {
          this.formData[key] = ''
        }
      }
      if (String(this.delHtml(value)).length != 0) {
        this.$refs['form'].validateField(key)
      }
    },
    // 过滤html代码、空格、回车 空白字符
    'delHtml': function(str) {
      str = str.replace(/<("[^"]*"|'[^']*'|[^'">])*>/gi, '')
      str = str.replace(/[\r\n]/g, '')
      str = str.replace(/\s/g, '')
      str = str.replace(/&nbsp;/ig, '')
      return str
    },
    close: function() {
      this.$emit('close')
    },
    // 保存配置
    confirm() {
      this.loading = true
      const params = JSON.parse(JSON.stringify(this.formData))
      saveBaseInfoConfig(params).then(res => {
        this.loading = false
        this.$message.success('保存成功')
        this.close()
        this.$emit('call', 'refresh')
      }).catch(() => {
        this.loading = false
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.drawer-wrap {
  .el-input,
  .el-select,
  .el-data-picker {
    width: 100%;
  }
}
.uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 300px;
  height: 80px;
  line-height: 80px;
  text-align: center;
}
.upload /deep/ .el-upload-dragger {
  width: 300px;
  height: 80px;
}
.upload-show {
  position: relative;
  display: block;
  width: 300px;
  height: 80px;
  background: #ccc;
  img {
    width: 100%;
    height: 100%;
  }

  .icon-delete {
    position: absolute;
    top: -10px;
    right: -8px;
    color: red;
    cursor: pointer;
    font-size: 20px;
    display: none;
  }
}
.upload-show-rect {
  width: 150px;
  height: 150px;
}
.upload-show:hover {
  .icon-delete {
    display: block;
  }
}
</style>
