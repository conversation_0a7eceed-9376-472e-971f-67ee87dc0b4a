import streamSaver from 'streamsaver'
export default {
  methods: {
    downloadWithStreamSaver(url, filename) {
      fetch(url, {
        method: 'GET',
        headers: {
          'Admin-Token': JSON.parse(localStorage.getItem('Admin-Token')).data
        }
      }).then(res => {
        // 获取 Content-Disposition 响应头
        const contentDisposition = res.headers.get('Content-Disposition')
        // 使用正则表达式提取文件名
        let fileName = filename
        if (contentDisposition) {
          const fileNameMatch = contentDisposition.match(/filename[^;\n]*=((['"]).*?\2|[^;\n]*)/)
          if (fileNameMatch != null) {
            fileName = fileNameMatch[1].replace(/['"]/g, '')
            fileName = decodeURIComponent(fileName) // 解码文件名
          }
        }
        const fileStream = streamSaver.createWriteStream(fileName)
        const readableStream = res.body

        if (window.WritableStream && readableStream.pipeTo) {
          return readableStream.pipeTo(fileStream)
            .then(() => console.log('done writing'))
        }
        window.writer = fileStream.getWriter()

        const reader = res.body.getReader()
        const pump = () => reader.read()
          .then(res => res.done
            ? window.writer.close()
            : window.writer.write(res.value).then(pump))

        pump()
      })
    },
    download(data, filename) {
      if (window.navigator.msSaveBlob) {
        this._download4ie(data, filename)
      } else {
        this._download4chrome(data, filename)
      }
    },
    // ie浏览器下载
    _download4ie(data, filename) {
      const blobObject = new Blob([data])
      window.navigator.msSaveBlob(blobObject, filename)
    },
    // 谷歌浏览器下载
    _download4chrome(data, filename) {
      const urlObject = window.URL || window.webkitURL || window
      const exportBlob = new Blob([data])
      const saveLink = document.createElementNS('http://www.w3.org/1999/xhtml', 'a')
      saveLink.href = urlObject.createObjectURL(exportBlob)
      saveLink.download = filename
      const ev = document.createEvent('MouseEvents')
      ev.initMouseEvent('click', true, false, window, 0, 0, 0, 0, 0, false, false, false, false, 0, null)
      saveLink.dispatchEvent(ev)
      window.URL.revokeObjectURL(saveLink.href)
    }
  }
}
