<template>
  <div class="drawer-wrap">
    <selected-class
      ref="table"
      :filter-data="{}"
      :height="null"
      :link="false"
      :single="true"
      @on-select="onSelect"
    />
    <div class="drawer-footer">
      <div>
        <el-button :disabled="!selectedItem.length" type="primary" @click="confirm">确定</el-button>
        <el-button type="text" @click="close">取消</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import selectedClass from '../selectedClass/index.vue'
export default {
  components: {
    selectedClass
  },
  data() {
    return {
      selectedItem: [],
      notAllowedArr: []
    }
  },
  methods: {
    'onSelect': function(data) {
      this.selectedItem = data
    },
    'close': function() {
      this.$emit('close')
    },
    'confirm': function() {
      const transData = this.selectedItem
      transData[0].name = this.selectedItem[0].majorName
      transData[0].num = this.selectedItem[0].studentNum
      transData[0].mobile = this.selectedItem[0].assistantTeacher
      this.$emit('call', 'confirm_class', transData)
    }
  }
}
</script>
<style lang="scss">
.drawer-footer{
  display: flex;
  align-items: center;
  height: 8%;
}
</style>
