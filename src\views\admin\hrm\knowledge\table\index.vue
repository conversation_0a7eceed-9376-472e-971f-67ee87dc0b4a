<template>
  <div class="resource-table">
    <!-- 操作区 -->
    <div class="operation-wrap">
      <div class="operation-left">
        <slot name="action" />
        <el-button type="primary" icon="el-icon-refresh" @click="refresh" />
      </div>
      <div class="operation-right">
        <el-badge :value="searchBtnShowNum">
          <el-button icon="el-icon-search" @click="openSearch">搜索</el-button>
        </el-badge>
        <!-- 自定义表格列 -->
        <t-table-config
          v-if="!customColData.length"
          :data="columnsObj"
          :active-key-arr="columnsViewArr"
          @on-change-col="onChangeCol"
        />
      </div>
    </div>
    <!-- 搜索区 -->
    <t-search-box
      v-show="searchView"
      :search-key-list="searchKeyListView"
      default-placeholder="默认搜索知识点名称"
      @search="searchMultiple"
    />
    <!-- 列表 -->
    <t-table-view
      ref="tableView"
      :height="height"
      :single="single"
      :loading="tableLoading"
      :data="tableData"
      :total="tableTotal"
      :page-size="pageSize"
      :current="pageCurrent"
      :select-item="selectItem"
      current-key="knowledgeCode"
      @on-select="onSelect"
      @on-current="onCurrent"
      @on-change="changePage"
      @on-sort-change="onSortChange"
      @on-page-size-change="onPageSizeChange"
    >
      <el-table-column v-for="item in columnsViewArr" :key="item" :prop="item" :min-width="colMinWidth" :label="columnsObj[item].title" :fixed="columnsObj[item].master ? 'left' : false" show-overflow-tooltip>
        <template slot-scope="{row}">
          <template v-if="item === 'knowledgeName'">
            <a
              v-if="row.knowledgeName"
              :href="`/manage/training/knowledgeManage/detail/${row.knowledgeCode}/basicCourse/${row.knowledgeName}`"
              @click.prevent="linkEvent('knowledgeDetails', row, {id: row.knowledgeCode, view: 'basicCourse',name: row.knowledgeName})"
            >
              {{ row.knowledgeName }}
            </a>
            <span v-else>{{ row.knowledgeName || '-' }}</span>
          </template>
          <span v-else>{{ row[item] || '-' }}</span>
        </template>
      </el-table-column>
    </t-table-view>
  </div>
</template>

<script>
import module from '../config.js'
import tSearchBox from '@/packages/search-box/index.vue'
import tTableView from '@/packages/table-view/index.vue'
import tTableConfig from '@/packages/table-config/table-col-config.vue'
import tableTdMultiCol from '@/packages/table-config/table-td-multi-col.vue'
import mixinsPageTable from '@/packages/mixins/page_table'
import { searchAPI } from '@/api/admin/training/knowledge'

export default {
  components: {
    tSearchBox,
    tTableView,
    tTableConfig,
    tableTdMultiCol
  },
  mixins: [mixinsPageTable],
  data() {
    return {
      moduleName: module.name,
      // 搜索配置项
      searchKeyList: [
        { key: 'nameLike', label: '知识点名称', master: true }
      ],
      // 所有可配置显示列 master：不可隐藏 title:列名称
      columnsObj: {
        'knowledgeName': {
          title: '知识点名称', master: true
        },
        'curriculumCount': {
          title: '课程内容'
        },
        'taskCount': {
          title: '关联任务'
        },
        'questionCount': {
          title: '关联题目'
        }
      },
      // 当前显示列key表 默认，如果localStorage有数据将被覆盖
      columnsViewArr: [
        'knowledgeName',
        'curriculumCount',
        'taskCount',
        'questionCount'
      ]
    }
  },
  methods: {
    // 列表点击
    getList: function(showLoading = true) {
      if (showLoading) {
        this.tableLoading = true
      }
      const params = this.getPostData('page', 'limit')
      searchAPI(params).then((res) => {
        if (res.code === 0) {
          this.tableData = res.data.records || []
          this.tableTotal = res.data.total || 0
        }
      }).finally(() => {
        this.tableLoading = false
      })
    }
  }
}
</script>
