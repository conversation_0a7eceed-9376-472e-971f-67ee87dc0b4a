import request from '@/utils/request'
const headers = { 'Content-Type': 'application/json;charset=UTF-8' }

// 分页查询单兵演练列表
export function sceneBasicInfoInstance(data) {
  return request({
    url: 'scene/sceneBasicInfoInstance/queryPage',
    method: 'post',
    headers,
    data
  })
}

// 获取单兵演练概况
export function sceneBasicInfoInstanceItem(params) {
  return request({
    url: 'scene/sceneBasicInfoInstance/get',
    method: 'get',
    headers,
    params
  })
}

// 结束单兵演练
export function sceneBasicInfoInstanceFinish(data) {
  return request({
    url: 'scene/sceneBasicInfoInstance/finish',
    method: 'post',
    headers,
    data
  })
}

// 删除单兵演练
export function sceneBasicInfoInstanceRemove(data) {
  return request({
    url: 'scene/sceneBasicInfoInstance/remove',
    method: 'post',
    headers,
    data
  })
}

// 分页查询单兵演练记录
export function trpSceneInstanceTrainingRecord(data) {
  return request({
    url: 'scene/trpSceneInstanceTrainingRecord/queryPage',
    method: 'post',
    headers,
    data
  })
}

// 批量导出单兵演练记录
export function exportTrainingRecord(data) {
  return request({
    url: 'scene/trpSceneInstanceTrainingRecord/exportTrainingRecord',
    method: 'post',
    headers,
    data,
    responseType: 'blob'
  })
}

// 查询单兵演练记录详情（阶段跟任务列表）
export function sceneStageInstance(data) {
  return request({
    url: 'scene/sceneStageInstance/queryList',
    method: 'post',
    headers,
    data
  })
}

// 查询单兵演练记录详情（提交的题目列表）
export function getTrainingRecordQuestionList(data) {
  return request({
    url: 'scene/trpSceneInstanceTrainingRecord/getTrainingRecordQuestionList',
    method: 'post',
    headers,
    data
  })
}
