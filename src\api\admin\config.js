import request from '@/utils/request'

// 获取后台配置
export function adminSystemIndexAPI(data) {
  return request({
    url: 'adminConfig/queryAdminConfig',
    method: 'post',
    data: data
  })
}

// 网站配置 保存基本信息配置
export function saveBaseInfoConfig(data) {
  return request({
    url: 'admin/globalConfig/saveOrUpdate',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 网站配置 获取基本信息配置
export function queryBaseInfoConfig() {
  return request({
    url: 'admin/globalConfig/get',
    method: 'post'
  })
}

// 获取实训文件大小
export function getMaxFileSize() {
  return request({
    url: '/admin/adminFile/getMaxFileSize',
    method: 'post'
  })
}
