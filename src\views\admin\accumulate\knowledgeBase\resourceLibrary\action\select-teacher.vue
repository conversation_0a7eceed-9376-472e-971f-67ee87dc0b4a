<template>
  <div class="drawer-wrap">
    <selected-teacher
      ref="table"
      :filter-data="{}"
      :height="null"
      :link="false"
      :single="true"
      @on-select="onSelect"
    />
    <div class="drawer-footer">
      <div>
        <el-button :disabled="!selectedItem.length" type="primary" @click="confirm">确定</el-button>
        <el-button type="text" @click="close">取消</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import selectedTeacher from '@/views/admin/hrm/TeachingAffairs/selectedTeacher/index.vue'
export default {
  components: {
    selectedTeacher
  },
  data() {
    return {
      selectedItem: [],
      notAllowedArr: []
    }
  },
  methods: {
    'onSelect': function(data) {
      this.selectedItem = data
    },
    'close': function() {
      this.$emit('close')
    },
    'confirm': function() {
      this.$emit('call', 'confirm_teacher', this.selectedItem)
    }
  }
}
</script>
<style lang="scss">
.drawer-footer{
  display: flex;
  align-items: center;
  height: 8%;
}
</style>
