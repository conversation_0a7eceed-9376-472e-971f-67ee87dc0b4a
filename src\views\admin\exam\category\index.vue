<template>
  <div class="category-wrap">
    <transverse-list
      :data="examStatusArr"
      :allow-deletion="false"
      :allow-add="false"
      :module-name="categoryName + '_' + moduleName + '_examination'"
      :cache-pattern="true"
      :all="true"
      v-bind="categoryProps"
      title="考试状态"
      @node-click="handleNodeClick($event, 'examType')"
    />
    <transverse-list
      :data="examLevelArr"
      :allow-deletion="false"
      :allow-add="false"
      :all="true"
      :module-name="categoryName + '_' + moduleName + '_grade'"
      :cache-pattern="true"
      v-bind="categoryProps"
      title="证书等级"
      @node-click="handleNodeClick($event, 'examFinish')"
    />
    <transverse-list
      :data="publicStatusArr"
      :allow-deletion="false"
      :allow-add="false"
      :all="true"
      :module-name="categoryName + '_' + moduleName + '_release'"
      :cache-pattern="true"
      v-bind="categoryProps"
      title="发布状态"
      @node-click="handleNodeClick($event, 'publicStatus')"
    />
  </div>
</template>
<script>
import transverseList from '@/packages/transverse-list/index.vue'
import module from '../config.js'

export default {
  components: {
    transverseList
  },
  props: {
    examType: [String, Number],
    examFinish: [String, Number],
    publicStatus: [String, Number],
    categoryName: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      moduleName: module.name,
      examStatusArr: module.examStatus,
      examLevelArr: module.examLevel,
      publicStatusArr: module.publicStatus,
      categoryProps: {
        label: 'label',
        idName: 'value'
      },
      categoryQuery: {
        examType: this.examType,
        examFinish: this.examFinish,
        publicStatus: this.publicStatus
      }
    }
  },
  mounted() {
  },
  methods: {
    handleNodeClick(item, key) {
      this.categoryQuery[key] = item.value
      this.$emit('category-query', this.categoryQuery)
    }
  }
}
</script>
<style lang="scss" scoped>
.category-wrap {
  border-bottom: 1px solid var(--neutral-300) !important;
}
::v-deep {
  .transverse-list {
    border-bottom: 0px solid var(--neutral-300) !important;
    display: flex;
    justify-content: space-between;
    width: 100%;
    padding: 6px 16px;
    font-size: 16px;
    .transverse-list-operate {
      display: none !important;
    }
  }
}
.transverse-border {
  border-bottom: 1px solid var(--neutral-300) !important;
}

.transverse-list {
  .transverse-list-title {
    white-space: nowrap;
    font-size: 13px;
    font-family: Microsoft YaHei;
    font-weight: 400;
    color: #999999;
    margin-top: 10px;
  }
  .transverse-list-content {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    flex: 1;
    min-width: 0;
    position: relative;
    padding-top: 5px;
    .list-content {
      height: 30px;
      padding: 0 11px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      font-family: Microsoft YaHei;
      font-weight: 400;
      font-size: 13px;
      color: #5b5b5b;
      margin-bottom: 2px;
      margin-right: 10px;
    }

    .list-content:hover {
      background: #eaeeff;
      border-radius: 4px;
      color: #3152ef;
    }
    .content-selected {
      background: #eaeeff;
      color: #3152ef;
      font-size: 13px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      border-radius: 4px;
    }
    .type-item {
      position: relative;
      border-radius: 5px;
      z-index: 0;
    }
    .type-item:hover {
      background: rgba(35, 98, 251, 0.1);
    }
  }
}
</style>
