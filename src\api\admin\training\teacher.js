import request from '@/utils/request'

/**
 * 教师列表
 */
export function searchAPI(data) {
  return request({
    url: 'training/PjtSysUser/searchMajorLecturer',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 新增
 */
export function addAPI(data) {
  return request({
    url: '/training/PjtSysUser/insertLecturer',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}


/**
 * 编辑
 */
export function editAPI(data) {
  return request({
    url: '/training/PjtSysUser/updatePjtUserMajorRelation',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 删除
 */
export function dropAPI(data) {
  return request({
    url: '/training/PjtSysUser/deletePjtTeacher',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}


/**
 * 修改状态
 */
export function editStatusAPI(data) {
  return request({
    url: '/training/PjtSysUser/updatePjtUserMajorRelationStatus',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 模版下载
 */
export function templateAPI(data) {
  return request({
    url: '/training/PjtSysUser/exportUserTemplate',
    method: 'post',
    responseType: 'blob'
  })
}

/**
 * 导入
 */
export function importAPI(file) {
  const data = new FormData()
  data.append('file', file)
  return request({
    url: 'training/PjtSysUser/importUserTemplate',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data
  })
}

/**
 * 导出
 */
export function exportAPI(data) {
  return request({
    url: 'training/PjtSysUser/exportSearchMajorLecturer',
    method: 'post',
    responseType: 'blob',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data
  })
}

/**
 * 查询任务实训分类
 */
export function searchCategoryTask(data) {
  return request({
    url: 'training/pjtCategoryTask/searchCategoryTask',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data
  })
}
/**
 * 新增任务实训分类
 */
export function addCategoryTask(data) {
  return request({
    url: 'training/pjtCategoryTask/insertCategoryTask',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data
  })
}
/**
 * 编辑任务实训分类
 */
export function updateCategoryTask(data) {
  return request({
    url: 'training/pjtCategoryTask/updateCategoryTask',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data
  })
}
/**
 * 删除任务实训分类
 */
export function delCategoryTask(data) {
  return request({
    url: 'training/pjtCategoryTask/deleteCategoryTask',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data
  })
}
