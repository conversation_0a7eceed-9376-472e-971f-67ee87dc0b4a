<template>
  <div v-loading="loading" class="dialog-wrap">
    <el-form v-loading="loading" ref="form" :model="formData" :rules="rules" label-width="90px">
      <el-form-item label="分类" prop="name">
        <el-input v-model.trim="formData.name" />
      </el-form-item>
    </el-form>
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>

<script>

import validate from '@/packages/validate'
export default {
  data() {
    return {
      loading: false,
      validate: validate,
      formData: {
        name: '' // 分类名称
      },
      rules: {
        name: [validate.required(), { min: 1, max: 30, message: '输入范围：1-30', trigger: 'change' }]
      }
    }
  },
  methods: {
    close() {
      this.$emit('close')
    },
    confirm: function() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.loading = true
          this.$emit('call', this.formData.name)
          this.close()
        }
      })
    }
  }
}
</script>
