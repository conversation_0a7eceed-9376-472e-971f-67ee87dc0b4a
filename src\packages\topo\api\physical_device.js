import request from '../../request'
const _thisApi = window.NFVO_CONFIG.nfvo_api

export function getList(params) {
  return request({
    url: _thisApi + '/networkelement',
    method: 'get',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    params
  })
}

export function getPort(params) {
  return request({
    url: _thisApi + '/networkelement/ports',
    method: 'get',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    params
  })
}
