<template>
  <div class="admin-navbar">
    <el-breadcrumb separator="/">
      <el-breadcrumb-item>
        <i class="el-icon-s-home" />
        当前界面：
      </el-breadcrumb-item>
      <el-breadcrumb-item
        v-for="(item, index) in breadData"
        :key="index"
      >
        <a v-if="index > 0 && index < breadData.length -1" :href="item.path">{{ item.meta.title }}</a>
        <span v-else>{{ item.meta.title }}</span>
      </el-breadcrumb-item>
    </el-breadcrumb>
    <div class="go-out" @click="enterLogin">退出系统</div>
  </div>
</template>

<script>
import { Loading } from 'element-ui'

export default {
  data() {
    return {
      breadData: []
    }
  },
  watch: {
    $route(to, from) {
      this.breadData = this.$route.matched.filter(item => item.meta.title)
    }
  },
  methods: {
    enterLogin() {
      this.$confirm('退出登录？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          var loading = Loading.service({
            target: document.getElementById('#app')
          })
          this.$store
            .dispatch('LogOut')
            .then(() => {
              loading.close()
              location.reload()
            })
            .catch(() => {
              loading.close()
              location.reload()
            })
        })
        .catch(() => {})
    }
  }
}
</script>

<style lang="scss" scoped>
.admin-navbar {
  height: 60px;
  background-color: #fff;
  .el-breadcrumb {
    line-height: 60px;
    margin-left: 22px;
    display: inline-block;
  }
  .go-out {
    padding: 0 20px;
    float: right;
    line-height: 60px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
  }
}
</style>
