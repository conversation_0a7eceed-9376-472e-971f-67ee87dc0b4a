<template>
  <div class="content-wrap-layout">
    <category
      :style="{'height': fold ? '0' : 'unset', 'overflow': fold ? 'hidden' : 'unset'}"
      :category-name="moduleName"
      :exam-status="examStatus"
      :certificate-level="certificateLevel"
      :published="published"
      @category-query="categoryQuery"
    />
    <!-- 分类区 -->
    <page-table
      ref="table"
      :default-selected-arr="defaultSelectedArr"
      :filter-data="{ 'certificateLevel': certificateLevel, 'examStatus': examStatus, 'published': published }"
      :cache-pattern="true"
      default-selected-key="examCode"
      @refresh="refresh"
      @link-event="linkEvent"
      @on-select="tabelSelect"
      @on-current="tabelCurrent"
    >
      <action-menu
        slot="action"
        :module-name="moduleName"
        :select-item="selectItem"
        @call="actionHandler"
      />
    </page-table>
    <el-drawer
      :visible.sync="detailShow"
      :modal="false"
      size="75%"
      append-to-body
      class="detail-view"
      @close="closeDetail"
    >
      <t-detail v-if="detailShow" />
    </el-drawer>
  </div>
</template>

<script>
import moduleConf from './config'
import pageTable from './table/index.vue'
import actionMenu from './action/index.vue'
import category from './category/index.vue'
import lodash from 'lodash'
import tDetail from './detail/index.vue'
import moduleMixin from '@/packages/mixins/module_list'

export default {
  // 试卷管理
  name: 'PaperManage',
  components: {
    category,
    pageTable,
    actionMenu,
    tDetail
  },
  mixins: [moduleMixin],
  data() {
    return {
      listRouterName: 'ca-exam',
      fold: false,
      moduleName: moduleConf.name,
      selectItem: [],
      defaultSelectedArr: [],
      certificateLevel: '',
      examStatus: '',
      num: 0,
      published: ''
    }
  },
  methods: {
    // 列表点击
    linkEvent: function({ name, row, params }) {
      this.$router.push({ name: name, params: params })
    },
    // 返回已选
    tabelSelect: function(data) {
      this.selectItem = data
    },
    // 返回单选
    tabelCurrent: function(row) {
      this.selectItem = [row]
    },
    // action menu 事件
    actionHandler: function(type, data) {
      switch (type) {
        case 'refresh':
          this.selectItem = []
          this.$refs['table'].getList(false)
          break
        case 'fold':
          this.fold = data
          break
      }
    },
    refresh: function() {},
    categoryQuery: lodash.debounce(function(obj) {
      this.certificateLevel = obj.examFinish
      this.examStatus = obj.examType
      this.published = obj.publicStatus
      if (!this.$store.state.cache[this.moduleName]) {
        const obj = {
          data: { searchShow: true },
          key: this.moduleName
        }
        this.$store.commit('SET_CACHE', obj)
      }
      this.$nextTick(() => {
        if (this.num != 0) {
          this.$refs['table']['pageCurrent'] = 1
        } else {
          this.num = this.num + 1
        }
        this.$refs['table'].getList(false)
      })
    }, 500)
  }
}
</script>
