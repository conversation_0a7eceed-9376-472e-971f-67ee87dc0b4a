<template>
  <div class="course-classification">
    <transverse-list :allow-add="false" :allow-deletion="false" :data="classificationList" title="分类" @node-click="classificationType" @edit="openDialog" @add="dialogVisible = true" @delete="delType"/>
    <!--    中部弹窗 start-->
    <el-dialog
      :visible.sync="dialogVisible"
      title="添加"
      width="30%">
      <el-form ref="categoryData" :model="categoryData" :rules="rules" label-width="100px" class="demo-ruleForm">
        <el-form-item label="名称:" prop="name">
          <el-input v-model.trim="categoryData.categoryName" placeholder="分类名称"/>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="resetForm('categoryData')">取 消</el-button>
        <el-button type="primary" @click="submitForm('categoryData')">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog :visible.sync="typeDialog" title="编辑分类" width="600px" center>
      <div>
        <div class="flex ai-center jc-start">
          <span style="width:80px">修改项：</span>
          <el-select v-model="typeForm.categoryCode" placeholder="请选择" clearable>
            <el-option v-for="item in classificationList" :key="item.categoryCode" :label="item.categoryName" :value="item.categoryCode"/>
          </el-select>
        </div>
        <div class="flex ai-center jc-start mt-10">
          <span style="width:80px">新名称：</span>
          <el-input v-model.trim="typeForm.categoryName" style="width:450px" placeholder="请输入内容"/>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="typeDialog = false">取 消</el-button>
        <el-button type="primary" @click="editType">确 定</el-button>
      </span>
    </el-dialog>
    <!--    中部弹窗 end-->
  </div>
</template>
<script>
import module from '../config.js'
import transverseList from '@/packages/transverse-list/index.vue'
import { delBaseType, updateCategory, insertCategoryAPI } from '@/api/teachingAffairs/index.js'
import { queryMajor } from '@/api/teachingAffairs/index.js'
export default {
  components: {
    transverseList
  },
  props: {
    transferData: {
      type: Array
    }
  },
  data() {
    var validateplanName = (rule, value, callback) => {
      if (this.categoryData.categoryName.trim() === '') {
        callback(new Error('分类名称不能为空'))
      } else {
        callback()
      }
    }
    return {
      moduleName: module.name,
      classificationList: [],
      dialogVisible: false,
      categoryData: {
        categoryName: '',
        categoryType: 2
      },
      rules: {
        name: [
          { required: true, validator: validateplanName, trigger: 'blur' }
        ]
      },
      categoryCode: '',
      typeDialog: false,
      typeForm: { categoryCode: '', categoryName: '' }
    }
  },
  mounted() {
    this.pjtCategoryQuery()
  },
  methods: {
    pjtCategoryQuery() {
      // pjtCategoryQueryAPI({ categoryType: 2 }).then(res => {
      //   this.classificationList = res.data
      //   console.log(res.data, 'classificationListclassificationList')
      // })
      queryMajor().then(res => {
        const paramArr = []
        res.data.forEach((item) => {
          paramArr.push({ categoryCode: item.majorCode, categoryName: item.majorName })
        })
        this.classificationList = paramArr
      })
    },
    resetForm(formName) {
      this.dialogVisible = false
      this.categoryData.categoryName = ''
      this.$refs[formName].resetFields()
    },
    classificationType(item) {
      console.log(item, 'itemitem')
      this.categoryCode = item.categoryCode
      this.$emit('classificationType', this.categoryCode)
    },
    openDialog() {
      this.typeForm.categoryCode = ''
      this.typeForm.categoryName = ''
      this.typeDialog = true
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.insertCategory()
        } else {
          return false
        }
      })
    },
    insertCategory() {
      insertCategoryAPI(this.categoryData).then(res => {
        if (res.code == 0) {
          this.$message.success('分类添加成功')
        }
      }).finally(() => {
        this.resetForm('categoryData')
        this.pjtCategoryQuery()
      })
    },
    editType() {
      if (this.typeForm.categoryCode == '') { return this.$message.warning('请选择修改项') }
      if (this.typeForm.categoryName == '') { return this.$message.warning('请输入新名称') }
      updateCategory(this.typeForm).then(res => {
        if (res.code == 0) {
          this.$message.success('修改分类成功')
          this.typeDialog = false
          this.pjtCategoryQuery()
        }
      })
    },
    delType(row) {
      this.$confirm('此操作将永久删除该分类, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        delBaseType({ categoryCode: row.categoryCode }).then(res => {
          if (res.code == 0) {
            this.pjtCategoryQuery()
            this.$message.success('删除分类成功')
          }
        })
      })
    }
  }
}
</script>
<style lang="scss" scoped>
    .course-classification{
      display: flex;
      justify-content: space-between;
      width: 100%;
      font-size: 16px;
      .operation{
        cursor: pointer;
        font-size:14px;
        min-width: 45px;
        padding-top: 9px;
      }
      .course-classification_content{
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        .classification-content-title{
          margin: 0 10px 8px -6px;
        }
        div{
          height: 32px;
          padding: 0 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          color: #999999;
          font-size: 14px;
          font-family: Microsoft YaHei;
          font-weight: 400;
        }
        .classification-content_n{
          margin: 0 18px 8px;
        }
        .classification-content_selected{
          background: var(--color-600);
          color: #FFFFFF;
          font-size: 14px;
          font-family: Microsoft YaHei;
          font-weight: 400;;
          border-radius: 4px;
        }
        .classification-content:hover{
          background: var(--color-600);
          color: #FFFFFF;
          font-size: 14px;
          font-family: Microsoft YaHei;
          font-weight: 400;;
          border-radius: 4px;
        }
      }
      .type-item{
    position: relative;
    border-radius: 5px;
    z-index: 0;
  }
  .type-item:hover{
    background: rgba(35, 98, 251, 0.1);
    .reduce-type{
    display: inline;
  }
  }
  .reduce-type{
    display: none;
    position: absolute;
    top: 0px;
    right: -1px;
    color: red;
    z-index: 1;
  }
    }
</style>
