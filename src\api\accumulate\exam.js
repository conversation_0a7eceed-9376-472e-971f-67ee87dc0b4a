import request from '@/utils/request'
const headers = { 'Content-Type': 'application/json;charset=UTF-8' }

// 分页查询分类数据
export function getExamList(data) {
  return request({
    url: `admin/sysExam/queryPage`,
    method: 'post',
    data,
    headers
  })
}

// 获取试卷详情
export function getExam(params) {
  return request({
    url: `admin/sysExam/get`,
    method: 'get',
    params,
    headers
  })
}

// 获取试卷题目详情
export function getQueryQuestionPageByExamId(data) {
  return request({
    url: `admin/sysExam/queryQuestionPageByExamId`,
    method: 'post',
    data,
    headers
  })
}

// 创建试卷
export function createExam(data) {
  return request({
    url: `admin/sysExam/create`,
    method: 'post',
    data,
    headers
  })
}

// 修改试卷
export function updateExam(data) {
  return request({
    url: `admin/sysExam/update`,
    method: 'post',
    data,
    headers
  })
}

// 删除试卷
export function deleteExam(data) {
  return request({
    url: `admin/sysExam/remove`,
    method: 'post',
    data,
    headers
  })
}

// 克隆试卷
export function cloneExam(data) {
  return request({
    url: `admin/sysExam/extendsExam`,
    method: 'post',
    data,
    headers
  })
}
