<template>
  <div class="content-wrap-layout">
    <div class="category">分类
      <el-select v-model="categoryId" :popper-append-to-body="false" filterable @change="categoryChange">

        <el-option
          v-for="item in categoryArr"
          :key="item.id"
          :label="item.categoryName"
          :value="item.id"
        >
          <el-tooltip
            placement="bottom"
            width="200">
            <div slot="content">{{ item.categoryName }}</div>
            <span>
              {{ item.categoryName }}
            </span>
          </el-tooltip>
        </el-option>
      </el-select>
    </div>
    <page-table
      ref="table"
      :default-selected-arr="defaultSelectedArr"
      :filter-data="{ 'categoryId': categoryId}"
      @refresh="refresh"
      @link-event="linkEvent"
      @on-select="tabelSelect"
      @on-current="tabelCurrent"
    />
  </div>
</template>

<script>
import moduleConf from './config'
import pageTable from './table/index.vue'
import { queryCategoryPage } from '@/api/exam/index.js'

export default {
  name: moduleConf.name,
  components: {
    pageTable
  },
  data() {
    return {
      moduleName: moduleConf.name,
      selectItem: [],
      defaultSelectedArr: [],
      categoryArr: [],
      categoryId: ''
    }
  },
  mounted() {
    this.getCategoryList()
  },
  methods: {
    // 列表点击
    linkEvent: function({ name, row, params }) {
      this.$router.push({ name: name, params: params })
    },
    // 返回已选
    tabelSelect: function(data) {
      this.selectItem = data
      this.$emit('on-select', this.selectItem)
    },
    // 返回单选
    tabelCurrent: function(row) {
      this.selectItem = [row]
    },
    // action menu 事件
    actionHandler: function(type, data) {
      switch (type) {
        case 'refresh':
          this.$refs['table'].getList()
      }
    },
    refresh: function() {},
    categoryChange: function(id) {
      this.categoryId = id
      this.$nextTick(() => {
        this.$refs['table'].getList(false)
      })
    },
    getCategoryList: function() {
      const params = {
        pageNum: 1,
        limit: 1000
      }
      queryCategoryPage(params).then(res => {
        this.categoryArr = [{ categoryName: '全部', id: '' }, ...res.data.records]
      })
    }
  }
}
</script>

<style scoped lang="scss">
.content-wrap-layout {
  flex: 1;
  min-height: 0;
  .category {
    width: 100%;
    padding: 15px;
    border-bottom: 1px solid #dbdde0;
  }
  .el-select {
    margin-left: 10px;
  }
}
/deep/ .el-select-dropdown {
  width: 150px !important;
}
</style>
