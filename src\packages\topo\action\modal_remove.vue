<template>
  <div v-loading="loading" class="dialog-wrap">
    <el-alert :closable="false" type="warning">
      <div slot="title">
        <p>此操作会同时删除虚拟设备已创建的虚拟实例，以及会释放已应用的物理设备的端口。此操作无法撤销，请谨慎操作！</p>
      </div>
    </el-alert>
    <div>
      <div v-for="(item, index) in errList" :key="index">
        设备{{ item.nodeName }}已被角色{{ item.roleName }}的任务{{ item.taskName }}关联，请先解绑后再删除
      </div>
    </div>
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button :disabled="!availableArr.length" type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>

<script>
import { removeByNodeIdListAPI } from '../api/role'
import modalMixins from '../../mixins/modal_form'
export default {
  mixins: [modalMixins],
  props: {
    // 传入数据
    name: String,
    data: {
      type: Object,
      default: () => {
        return {}
      }
    },
    type: {
      type: String,
      default: ''
    },
    roleProperty: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      loading: false,
      errList: []
    }
  },
  computed: {
    // 筛选返回可操作数据
    'availableArr': function() {
      const _data = []
      this.data.cells.forEach((item) => {
        if (this.handleAvailable(item)) {
          _data.push(item)
        }
      })
      return _data
    }
  },
  methods: {
    // 可操作数据判断逻辑
    handleAvailable(item) {
      return true
    },
    // modal点击确定
    'confirm': function() {
      if (this.roleProperty === 1 || this.roleProperty === 2) {
        const arr = []
        this.data.cells.forEach(item => {
          if (item.data.node_id) {
            arr.push(item.data.node_id)
          }
        })
        removeByNodeIdListAPI(arr).then(res => {
          this.$emit('call', this.name)
          this.close()
        }).catch(err => {
          this.errList = err.data
        })
      } else {
        this.$emit('call', this.name)
        this.close()
      }
    },
    close() {
      this.$emit('close')
    }
  }
}
</script>
