<template>
  <!-- 资源排行 积分排行  渗透测试工具排行 -->
  <div class="plugin-view top-ranking">
    <h3 class="plugin-title" style="margin-bottom: 20px;">{{ pluginTitle }}</h3>
    <div v-if="studyNumList.length > 0" class="count-container">
      <div v-for="(item, index) in studyNumList" :key="index" class="count-div">
        <el-tooltip v-if="item" :content="item.courseName" class="item" effect="dark" placement="top">
          <div :title="item.content" class="course-name">
            {{ item.courseName }}
          </div>
        </el-tooltip>
        <el-tooltip v-if="item" :content="item.createBy" class="item" effect="dark" placement="top">
          <div :title="item.createdBy" class="course-by">
            {{ item.createBy }}
          </div>
        </el-tooltip>
        <el-progress :show-text="false" :stroke-width="17" :percentage="Number(item.percentage)" class="progress" />
        <div :title="`${item.courseNum}次`" class="count">{{ item.courseNum }}次</div>
      </div>
    </div>
    <div v-else class="flex-1 flex-center">暂无数据</div>
  </div>
</template>
<style lang="scss">
.top-ranking {
  padding: 20px;
  .chart-title {
    font-family: Source Han Sans CN;
    font-size: 16px;
    color: #333;
  }
  .empty-container {
    height: 150px;
    /deep/ .el-empty {
      height: 150px !important;
    }
  }
  .count-container {
    height: 220px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    .count-div {
      margin: 3px 0 !important;
      display: flex;
      justify-content: space-between;
      .course-name {
        width: 20%;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      .progress {
        width: 50%;
      }
      .course-by {
        width: 15%;
        margin: 0 10px 0 10px;
        text-align: left;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      .count {
        width: 8%;
        text-align: right;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
  }
}
.plugin-clogs-view {
  position: relative;
  width: 93%;
  margin-left: 4px;
  .plugin-clogs-item {
    width: 105%;
    display: flex;
    align-items: center;
  }
  .progress_person {
    width: 110%;
    margin: 5px auto;
    margin-left: 20px;
  }
  .fix_text_name {
    width: 200px;
    font-size: 14px;
    white-space: nowrap; /* 防止文字换行 */
    overflow: hidden; /* 隐藏溢出部分 */
    text-overflow: ellipsis; /* 显示省略号 */
  }
  .fix_text {
    width: 80px;
    text-align: end;
    font-size: 14px;
    white-space: nowrap; /* 防止文字换行 */
    overflow: hidden; /* 隐藏溢出部分 */
    text-overflow: ellipsis; /* 显示省略号 */
  }
}
.top-ranking ::v-deep .el-progress-bar__outer {
      height: 20px  !important;
}
</style>
<script>
import pluginMixin from './mixin_plugin.js'
import {
  getStudentSelectiveTop5,
  getSchedulingCourseTop5
} from '@/api/teacher/index.js'
export default {
  mixins: [
    pluginMixin
  ],
  props: {
    data: {
      type: Object
    }
  },
  data() {
    return {
      apiData: [],
      standard: 0,
      dayAgoParams: '',
      studyNumList: []
    }
  },
  methods: {
    getData(hideloading) {
      if (!hideloading) {
        // this.loading = true
      }
      if (this.pluginTitle === '热门自学课程top5') {
        getStudentSelectiveTop5().then((res) => {
          if (res.code == 0) {
            this.studyNumList = res.data
            const maxNum = this.studyNumList.reduce((prev, current) => (prev.courseNum > current.courseNum) ? prev : current).courseNum
            this.studyNumList.map(item => {
              item.percentage = Math.round((item.courseNum / maxNum) * 100)
            })
          }
        })
      } else {
        getSchedulingCourseTop5().then((res) => {
          if (res.code == 0) {
            this.studyNumList = res.data
            const maxNum = this.studyNumList.reduce((prev, current) => (prev.courseNum > current.courseNum) ? prev : current).courseNum
            this.studyNumList.map(item => {
              item.percentage = Math.round((item.courseNum / maxNum) * 100)
            })
          }
        })
      }
    },
    format() {
      return ''
    }
  }
}
</script>
