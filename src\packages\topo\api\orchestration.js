import request from '../../request'
const _thisApi = window.NFVO_CONFIG.nfvo_api

export function getWsToken(data) {
  return request({
    url: _thisApi + '/auth/login',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export function getListApi(params) {
  return request({
    url: _thisApi + '/topology',
    method: 'get',
    params
  })
}

// 查询路由子网网络列表
export function accessInfo(params) {
  return request({
    url: _thisApi + `/topology/${params.id}/access_info`,
    method: 'get',
    params
  })
}


export function getItem(id) {
  return request({
    url: _thisApi + '/topology/' + id,
    method: 'get',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export function createTopology(data) {
  return request({
    url: _thisApi + '/topology',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

export function editTopology(id, data) {
  return request({
    url: _thisApi + '/topology/' + id,
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

export function deleteTopology(id) {
  return request({
    url: _thisApi + '/topology/' + id,
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export function startTopology(id, data) {
  return request({
    url: _thisApi + '/topology/' + id + '/start',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

export function cleanTopology(id, data) {
  return request({
    url: _thisApi + '/topology/' + id + '/clean',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

export function vmStop(id, data) {
  return request({
    url: _thisApi + '/topology/' + id + '/stop',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

export function vmPause(id, data) {
  return request({
    url: _thisApi + '/topology/' + id + '/pause',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

export function vmSuspend(id, data) {
  return request({
    url: _thisApi + '/topology/' + id + '/suspend',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

export function vmResume(id, data) {
  return request({
    url: _thisApi + '/topology/' + id + '/resume',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

export function pnfImport(id, data) {
  return request({
    url: _thisApi + '/topology/' + id + '/import_node',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

export function pnfRelease(id, data) {
  return request({
    url: _thisApi + '/topology/' + id + '/release_node',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

export function getNode(params) {
  return request({
    url: _thisApi + '/topology/nodes',
    method: 'get',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    params
  })
}

export function getNodeItem(id) {
  return request({
    url: _thisApi + '/topology/nodes/' + id,
    method: 'get',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export function editNode(id, data) {
  return request({
    url: _thisApi + '/topology/nodes/' + id,
    method: 'put',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

export function createTemplate(data) {
  return request({
    url: _thisApi + '/topology/' + data.topology_id + '/copy',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

export function modalDelete(id) {
  return request({
    url: _thisApi + '/topology/nodes' + id,
    method: 'delete',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export function importNode(id) {
  return request({
    url: _thisApi + '/topology/nodes/' + id + '/import_node',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export function release(id) {
  return request({
    url: _thisApi + '/topology/nodes/' + id + '/release_node',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export function activate(id) {
  return request({
    url: _thisApi + '/topology/nodes/' + id + '/start',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export function clean(id) {
  return request({
    url: _thisApi + '/topology/nodes/' + id + '/clean',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export function start(id) {
  return request({
    url: _thisApi + '/topology/nodes/' + id + '/start',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export function stop(id) {
  return request({
    url: _thisApi + '/topology/nodes/' + id + '/stop',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export function reboot(id) {
  return request({
    url: _thisApi + '/topology/nodes/' + id + '/reboot',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export function suspend(id) {
  return request({
    url: _thisApi + '/topology/nodes/' + id + '/suspend',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export function resume(id) {
  return request({
    url: _thisApi + '/topology/nodes/' + id + '/resume',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export function rebuild(id) {
  return request({
    url: _thisApi + '/topology/nodes/' + id + '/rebuild',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export function pause(id) {
  return request({
    url: _thisApi + '/topology/nodes/' + id + '/pause',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 批量开机
export function batchStart(id, data) {
  return request({
    url: _thisApi + '/topology/' + id + '/batch_start_node',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}
// 批量关机
export function batchStop(id, data) {
  return request({
    url: _thisApi + '/topology/' + id + '/batch_stop_node',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

// 批量重启
export function batchReboot(id, data) {
  return request({
    url: _thisApi + '/topology/' + id + '/batch_reboot_node',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

// 批量暂停
export function batchPause(id, data) {
  return request({
    url: _thisApi + '/topology/' + id + '/batch_pause_node',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

// 批量挂起
export function batchSuspend(id, data) {
  return request({
    url: _thisApi + '/topology/' + id + '/batch_suspend_node',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

// 批量恢复
export function batchResume(id, data) {
  return request({
    url: _thisApi + '/topology/' + id + '/batch_resume_node',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

export function createSnapshot(id, data) {
  return request({
    url: _thisApi + '/topology/nodes/' + id + '/create_snapshot',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

export function getSnapshot(data) {
  const id = data.node_id
  const postData = JSON.parse(JSON.stringify(data))
  delete postData.node_id
  return request({
    url: _thisApi + '/topology/nodes/' + id + '/list_snapshot',
    method: 'get',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    params: postData
  })
}

export function deleteSnapshot(id, data) {
  const postData = { 'snapshot_id': id }
  return request({
    url: _thisApi + '/topology/nodes/' + data.node_id + '/delete_snapshot',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: postData
  })
}

export function restoreSnapshot(id, data) {
  return request({
    url: _thisApi + '/topology/nodes/' + id + '/restore_snapshot',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

export function toImage(id, data) {
  return request({
    url: _thisApi + '/topology/nodes/' + id + '/create_image',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

export function enable_webssh(id, data) {
  return request({
    url: _thisApi + '/topology/nodes/' + id + '/enable_webssh',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

export function webssh_console(id, params) {
  return request({
    url: _thisApi + '/topology/nodes/' + id + '/webssh_console',
    method: 'get',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    params: params
  })
}

export function getConsole(id, params) {
  return request({
    url: _thisApi + '/topology/nodes/' + id + '/get_console',
    method: 'get',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    params: params
  })
}

export function vnc(id) {
  return request({
    url: _thisApi + '/topology/nodes/' + id + '/vnc_console',
    method: 'get',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export function serial(id) {
  return request({
    url: _thisApi + '/topology/nodes/' + id + '/serial_console',
    method: 'get',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export function webshell(id, params) {
  return request({
    url: _thisApi + '/topology/nodes/' + id + '/webshell_console',
    method: 'get',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    params: params
  })
}

export function getExternalNetwork() {
  return request({
    url: _thisApi + '/networkelement/external_network',
    method: 'get',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 开启拓扑(实训、场景)
export function sceneStartTopoApi(data) {
  return request({
    url: '/scene/scencNodeCleanTask/startTopo',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 释放拓扑(实训、场景)
export function sceneReleaseTopoApi(data) {
  return request({
    url: '/scene/scencNodeCleanTask/cleanTopo',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 延时拓扑(实训、场景)
export function sceneDelayedTopoApi(data) {
  return request({
    url: '/scene/scencNodeCleanTask/releaseLatency',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 查询当前拓扑的任务信息
export function queryReleaseTaskTopoApi(data) {
  return request({
    url: '/scene/scencNodeCleanTask/queryReleaseTask',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
