import request from '@/utils/request'

// 查询【请填写功能名称】列表
export function listBase(query) {
  return request({
    url: '/accumulate/toolBase/list',
    method: 'get',
    params: query
  })
}

// 查询【请填写功能名称】详细
export function getBase(id) {
  return request({
    url: '/accumulate/toolBase/' + id,
    method: 'get'
  })
}

// 新增【请填写功能名称】
export function addBase(data) {
  return request({
    url: '/accumulate/toolBase/add',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

// 修改【请填写功能名称】
export function updateBase(data) {
  return request({
    url: '/accumulate/toolBase',
    method: 'put',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

// 删除【请填写功能名称】
export function delBase(id) {
  return request({
    url: '/accumulate/toolBase/' + id,
    method: 'delete'
  })
}

export function increaseDownloadCount(data) {
  return request({
    url: '/accumulate//toolBase/increaseDownloadCount/' + data,
    method: 'put'
  })
}
// Test


// export function listBase(query) {
// 	return {
// 		code:200,
// 		msg:'success',
// 		rows:[
// 			{
// 				attachmentName:"apt-get upgrade",
// 				createBy:"Admin",
// 				createTime:"2021-01-01 00:00:00",
// 				desc:"文件描述文件描述文件描述",
// 				downloadCount:1467,
// 				id:"ID000001",
// 				name:"apt-get upgrade",
// 				ownerId:"Admin",
// 				toolType:1,
// 				url:"www"
// 			},
// 			{
// 				attachmentName:"apt-get upgrade",
// 				createBy:"Admin",
// 				createTime:"2021-01-01 00:00:00",
// 				desc:"文件描述文件描述文件描述",
// 				downloadCount:1467,
// 				id:"ID000001",
// 				name:"apt-get upgrade",
// 				ownerId:"Admin",
// 				toolType:1,
// 				url:"www"
// 			},
// 			{
// 				attachmentName:"apt-get upgrade",
// 				createBy:"Admin",
// 				createTime:"2021-01-01 00:00:00",
// 				desc:"文件描述文件描述文件描述",
// 				downloadCount:1467,
// 				id:"ID000001",
// 				name:"apt-get upgrade",
// 				ownerId:"Admin",
// 				toolType:1,
// 				url:"www"
// 			},
// 			{
// 				attachmentName:"apt-get upgrade",
// 				createBy:"Admin",
// 				createTime:"2021-01-01 00:00:00",
// 				desc:"文件描述文件描述文件描述",
// 				downloadCount:1467,
// 				id:"ID000001",
// 				name:"apt-get upgrade",
// 				ownerId:"Admin",
// 				toolType:1,
// 				url:"www"
// 			},
// 			{
// 				attachmentName:"apt-get upgrade",
// 				createBy:"Admin",
// 				createTime:"2021-01-01 00:00:00",
// 				desc:"文件描述文件描述文件描述",
// 				downloadCount:1467,
// 				id:"ID000001",
// 				name:"apt-get upgrade",
// 				ownerId:"Admin",
// 				toolType:1,
// 				url:"www"
// 			},
// 			{
// 				attachmentName:"apt-get upgrade",
// 				createBy:"Admin",
// 				createTime:"2021-01-01 00:00:00",
// 				desc:"文件描述文件描述文件描述",
// 				downloadCount:1467,
// 				id:"ID000001",
// 				name:"apt-get upgrade",
// 				ownerId:"Admin",
// 				toolType:1,
// 				url:"www"
// 			},
// 			{
// 				attachmentName:"apt-get upgrade",
// 				createBy:"Admin",
// 				createTime:"2021-01-01 00:00:00",
// 				desc:"文件描述文件描述文件描述",
// 				downloadCount:1467,
// 				id:"ID000001",
// 				name:"apt-get upgrade",
// 				ownerId:"Admin",
// 				toolType:1,
// 				url:"www"
// 			},
// 			{
// 				attachmentName:"apt-get upgrade",
// 				createBy:"Admin",
// 				createTime:"2021-01-01 00:00:00",
// 				desc:"文件描述文件描述文件描述",
// 				downloadCount:1467,
// 				id:"ID000001",
// 				name:"apt-get upgrade",
// 				ownerId:"Admin",
// 				toolType:1,
// 				url:"www"
// 			},
// 			{
// 				attachmentName:"apt-get upgrade",
// 				createBy:"Admin",
// 				createTime:"2021-01-01 00:00:00",
// 				desc:"文件描述文件描述文件描述",
// 				downloadCount:1467,
// 				id:"ID000001",
// 				name:"apt-get upgrade",
// 				ownerId:"Admin",
// 				toolType:1,
// 				url:"www"
// 			},
// 			{
// 				attachmentName:"apt-get upgrade",
// 				createBy:"Admin",
// 				createTime:"2021-01-01 00:00:00",
// 				desc:"文件描述文件描述文件描述",
// 				downloadCount:1467,
// 				id:"ID000001",
// 				name:"apt-get upgrade",
// 				ownerId:"Admin",
// 				toolType:1,
// 				url:"www"
// 			},
// 			],
// 		total:10,
// 	}
// }
