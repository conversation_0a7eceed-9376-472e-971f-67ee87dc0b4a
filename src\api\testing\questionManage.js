import request from '@/utils/request'
const headers = { 'Content-Type': 'application/json;charset=UTF-8' }

// 获取问题管理列表
export function getProblemListApi(data) {
  return request({
    url: '/testing/problem/page',
    method: 'post',
    data,
    headers
  })
}

// 新增问题
export function addQuestionApi(data) {
  return request({
    url: '/testing/problem/save',
    method: 'post',
    data,
    headers
  })
}

// 修改问题
export function updateQuestionApi(data) {
  return request({
    url: '/testing/problem/update',
    method: 'post',
    data,
    headers
  })
}

// 删除问题
export function deleteQuestionApi(data) {
  return request({
    url: '/testing/problem/remove',
    method: 'post',
    data,
    headers
  })
}

// 根据id查询问题详情
export function getQuestionDetailApi(data) {
  return request({
    url: `/testing/problem/getInfo?id=${data.id}`,
    method: 'get',
    data,
    headers
  })
}

// 根据id获取历史记录
export function getOperationHistoryApi(data) {
  return request({
    url: `/testing/problem/getOperationHistoryList?id=${data.id}&tbOrder=${data.tbOrder}`,
    method: 'get',
    data,
    headers
  })
}

// 添加备注
export function addQuestionRemarkApi(data) {
  return request({
    url: '/testing/problem/addRemark',
    method: 'post',
    data,
    headers
  })
}

// 修改备注
export function updateQuestionRemarkApi(data) {
  return request({
    url: '/testing/problem/updateRemark',
    method: 'post',
    data,
    headers
  })
}

// 审核问题
export function auditQuestionApi(data) {
  return request({
    url: '/testing/problem/auditProblem',
    method: 'post',
    data,
    headers
  })
}

// 修复问题
export function fixedQuestionApi(data) {
  return request({
    url: '/testing/problem/fixProblem',
    method: 'post',
    data,
    headers
  })
}

// 验收问题
export function acceptQuestionApi(data) {
  return request({
    url: '/testing/problem/acceptProblem',
    method: 'post',
    data,
    headers
  })
}

// 批量导入问题模板
export function batchImportQuestionApi(data) {
  return request({
    url: '/testing/problem/excelImport',
    method: 'post',
    headers,
    data
  })
}

// 批量导入问题模板
export function downloadExcelQuestionApi(data) {
  return request({
    url: '/testing/problem/downloadExcel',
    method: 'post',
    data,
    responseType: 'blob'
  })
}

// 导出问题管理
export function exportQuestionApi(data) {
  return request({
    url: '/testing/problem/backExportQuery',
    method: 'post',
    headers,
    data,
    responseType: 'blob'
  })
}

// 导出问题管理
export function downExcelErrApi(data) {
  return request({
    url: '/testing/problem/downExcel',
    method: 'post',
    headers,
    data,
    responseType: 'blob'
  })
}

// 上传附件
export function uploadQuestionFile(data) {
  return request({
    url: '/testing/problem/uploadFile',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 更新附件
export function updateQuestionFile(data) {
  return request({
    url: '/testing/problem/updateFile',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 获取附件列表
export function getQuestionFileListApi(data) {
  return request({
    url: '/testing/problem/filePage',
    method: 'post',
    data,
    headers
  })
}

// 删除附件
export function deleteFileApi(data) {
  return request({
    url: '/testing/problem/deleteFile',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 下载附件
export function downloadFileApi(data) {
  return request({
    url: '/testing/problem/downloadFile',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    responseType: 'blob'
  })
}
