<template>
  <div>
    <!-- 列表 功能 -->
    <page-table
      ref="table"
      :filter-data="{ 'node_id': id }"
      :default-selected-arr="defaultSelectedArr"
      :resource-id="id"
      @link-event="linkEvent"
      @on-select="tabelSelect"
      @on-current="tabelCurrent"
    >
      <action-menu
        slot="action"
        :resource-id="id"
        :resource-data="data"
        :module-name="moduleName"
        :select-item="selectItem"
        :type="type"
        @call="actionHandler"
      />
    </page-table>
  </div>
</template>
<script>
import config from './config.js'
import pageTable from './table/index.vue'
import actionMenu from './action/index.vue'
export default {
  components: {
    pageTable,
    actionMenu
  },
  props: {
    id: String,
    data: Object,
    type: String
  },
  data() {
    return {
      moduleName: config.name,
      selectItem: [],
      defaultSelectedArr: []
    }
  },
  // watch:{}
  mounted() {
    console.log(this.type, 'type')
  },
  methods: {
    // 列表点击
    'linkEvent': function({ name, row, params }) {
      this.$router.push({ name: name, params: params })
    },
    // 返回已选
    'tabelSelect': function(data) {
      this.selectItem = data
    },
    // 返回单选
    'tabelCurrent': function(row) {
      this.selectItem = [row]
    },
    // action menu 事件
    'actionHandler': function(type, data) {
      switch (type) {
        case 'refresh':
          this.$refs['table'].getList()
      }
    }
  }
}
</script>
