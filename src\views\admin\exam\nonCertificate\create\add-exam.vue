<template>
  <create-view :loading="loading" title="创建考试">
    <el-form slot="content" ref="form" :model="formData" :rules="rules" label-position="left" label-width="150px">
      <el-card>
        <el-form-item label="考试名称" prop="examName">
          <el-input v-model.trim="formData.examName" show-word-limit/>
        </el-form-item>
        <el-form-item label="考试试卷" prop="selectedExamPaper">
          <el-tag
            v-if="formData.selectedExamPaper"
            :disable-transitions="true"
            closable
            @click="drawerName = 'selectedExamPaper'"
            @close="formData.selectedExamPaper = null">
            {{ formData.selectedExamPaper[0].examName }}
          </el-tag>
          <el-button v-else type="ghost" @click="drawerName = 'selectedExamPaper'">选择试卷</el-button>
        </el-form-item>
        <el-form-item prop="open">
          <span slot="label">
            <span>开放报名</span>
            <el-tooltip transfer>
              <i class="el-icon-warning-outline" />
              <div slot="content">开放报名时需要考生自行报名参加考试</div>
            </el-tooltip>
          </span>
          <el-switch v-model="formData.open" />
        </el-form-item>
        <el-form-item v-if="formData.open" label="报名时间">
          <div class="block w-66">
            <el-date-picker
              v-model="formData.startEndTime"
              :picker-options="pickerOptions"
              :append-to-body="false"
              :default-time="['00:00:00', '23:59:59']"
              type="datetimerange"
              size="small"
              align="center"
              format="yyyy-MM-dd HH:mm:ss"
              range-separator="-"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              @change="startEndTimeChange($event, formData.startEndTime)"
            />
          </div>
        </el-form-item>
        <el-form-item label="考试开始时间" prop="date1">
          <div class="block w-33">
            <el-date-picker
              v-model="formData.date1"
              :picker-options="pickerOptions"
              :append-to-body="false"
              align="center"
              format="yyyy-MM-dd HH:mm:ss"
              value-format="yyyy-MM-dd HH:mm:ss"
              type="datetime"
              placeholder="开始时间"
              @change="enterDateChange($event, formData.date1)"
            />
          </div>
        </el-form-item>
        <el-form-item label="考试时长" prop="time">
          <el-input-number v-model="formData.time" :min="10" class="w-33"/> 分钟
        </el-form-item>
        <el-form-item label="考试须知" prop="notice">
          <myEditor
            :key="timer"
            :content="formData.notice"
            width="100%"
            height="300px"
            @contentChange="contentChange"
          />
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            :autosize="{ minRows: 4, maxRows: 4 }"
            v-model.trim="formData.description"
            resize="none"
            type="textarea"
            maxlength="255"
            show-word-limit
            placeholder="请输入255字以内的备注"
          />
        </el-form-item>
      </el-card>
      <!-- 侧拉弹窗 start -->
      <el-drawer
        :title="titleMapping[drawerName]"
        :visible.sync="drawerShow"
        :size="drawerWidth"
        @close="drawerClose"
      >
        <transition name="el-fade-in-linear">
          <component
            :is="drawerName"
            :name="drawerName"
            @close="drawerClose"
            @call="drawerConfirmCall"
          />
        </transition>
      </el-drawer>
    <!-- 侧拉弹窗 end -->
    </el-form>
    <div slot="footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </div>
  </create-view>
</template>
<script>
import module from '../config.js'
import createView from '@/packages/create-view/index'
import validate from '@/packages/validate'
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
import myEditor from '@/packages/editor/index.vue'
import selectedExamPaper from '../action/select-exam-paper.vue'
import { insertExamNon, queryCaCertificateApi } from '@/api/exam/index.js'
import moment from 'moment'

export default {
  components: {
    createView,
    myEditor,
    selectedExamPaper
  },
  mixins: [mixinsActionMenu],
  data() {
    return {
      drawerAction: ['selectedExamPaper'], // 需要侧拉打开的操作
      titleMapping: {
        'selectedExamPaper': '选择试卷'
      },
      loading: false,
      validate: validate,
      formData: {
        open: false,
        examName: '',
        selectedExamPaper: '',
        date1: moment().format('YYYY-MM-DD HH:mm:ss'),
        date2: '',
        time: 30,
        startEndTime: [],
        notice: '',
        description: '',
        endTime: ''
      },
      rules: {
        examName: [validate.required(), validate.base_name],
        selectedExamPaper: [validate.required()],
        date1: [{ required: true, message: '请选择时间', trigger: 'change' }, { validator: this.validatedDate1, trigger: 'change' }],
        time: [validate.required('change'), { validator: this.timeValidator, trigger: 'change' }],
        startEndTime: [{ validator: this.validatedStartEndTime, trigger: 'change' }],
        notice: [validate.required()]
      },
      credentials: module,
      examLevel: module.examLevel,
      timeList: module.timeList,
      timer: '1000',
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() <= (Date.now() - 24 * 60 * 60 * 1000)
        }
      }
    }
  },
  created() {
    this.queryCaCertificate()
  },
  methods: {
    validatedStartEndTime(rule, value, callback) {
      if (this.formData.startEndTime) {
        const regEndTimeStamp = new Date(this.formData.startEndTime[1]).getTime()
        const startTimeStamp = new Date(this.formData.date1 + ' ' + this.formData.date2).getTime()
        if (value === '') {
          callback(new Error('请安排时间'))
        } else if (startTimeStamp < regEndTimeStamp) {
          callback(new Error('请合理安排时间'))
        } else {
          callback()
        }
      }
    },
    validatedDate1(rule, value, callback) {
      if (this.formData.startEndTime) {
        const regEndDate = this.dateFilter(this.formData.startEndTime[1])
        const regEndDateStamp = new Date(regEndDate).getTime()
        const startDateStamp = new Date(this.formData.date1).getTime()
        if (value === '') {
          callback(new Error('请安排时间'))
        } else if (startDateStamp < regEndDateStamp) {
          callback(new Error('请合理安排时间'))
        } else {
          callback()
        }
      }
    },
    timeValidator(rule, value, callback) {
      const reg = /^[0-9]*[1-9][0-9]*$/
      if (value === '') {
        callback(new Error('必填项'))
      } else if (!reg.test(value)) {
        callback(new Error('请正确输入时长'))
      } else {
        callback()
      }
    },
    queryCaCertificate() {
      queryCaCertificateApi().then((res) => {
        if (res.code === 0) {
          this.credentials = res.data
        }
      })
    },
    contentChange(value) {
      if (this.filterHtml(value)) {
        this.formData.notice = value
      } else {
        const imgStrs = value.match(/<img.*?>/g)
        if (imgStrs && imgStrs.length) { // 内容只有图片时
          this.formData.notice = value
        } else {
          this.formData.notice = ''
        }
      }
      this.$nextTick(() => {
        this.$refs['form'].clearValidate('notice')
      })
    },
    // 过滤html代码、空格、回车 空白字符
    filterHtml(str) {
      str = str.replace(/<("[^"]*"|'[^']*'|[^'">])*>/gi, '')
      str = str.replace(/[\r\n]/g, '')
      str = str.replace(/\s/g, '')
      str = str.replace(/&nbsp;/ig, '')
      return str
    },
    drawerConfirmCall: function(type, data) {
      if (type === 'close') {
        this.drawerClose()
      } else if (type === 'confirm_examPaper') {
        this.formData.selectedExamPaper = data
        this.drawerClose()
        this.$nextTick(() => {
          this.$refs['form'].clearValidate('selectedExamPaper')
        })
      }
    },
    dateFilter(time) {
      const y = new Date(time).getFullYear()
      let m = new Date(time).getMonth() + 1
      m = m < 10 ? ('0' + m) : m
      let d = new Date(time).getDate()
      d = d < 10 ? ('0' + d) : d
      let h = new Date(time).getHours()
      h = h < 10 ? ('0' + h) : h
      let M = new Date(time).getMinutes()
      M = M < 10 ? ('0' + M) : M
      let s = new Date(time).getSeconds()
      s = s < 10 ? ('0' + s) : s
      const dateTime = y + '-' + m + '-' + d + ' ' + h + ':' + M + ':' + s
      return dateTime
    },
    startEndTimeChange(event, row) {
      if (!row) {
        this.$nextTick(() => {
          this.formData.startEndTime = []
        })
      }
      if (row && this.formData.date1 && this.formData.date2) {
        this.$refs['form'].clearValidate(['date1'])
      }
    },
    enterDateChange(event, row) {
      if (row && this.formData.startEndTime) {
        this.$refs['form'].clearValidate(['startEndTime'])
      }
    },
    close: function() {
      this.$router.go(-1)
    },
    confirm: function() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.loading = true
          const endTime = moment(this.formData.date1).add(this.formData.time, 'minute').format('YYYY-MM-DD HH:mm:ss')
          if (this.formData.startEndTime.length != 0) {
            this.formData.startEndTime[0] = this.dateFilter(this.formData.startEndTime[0])
            this.formData.startEndTime[1] = this.dateFilter(this.formData.startEndTime[1])
          }
          const params = {
            name: this.formData.examName,
            paperId: this.formData.selectedExamPaper[0].id,
            paperName: this.formData.selectedExamPaper[0].examName,
            examStatus: 0,
            regBeginTime: this.formData.open && this.formData.startEndTime[0] ? this.formData.startEndTime[0] : '',
            regEndTime: this.formData.open && this.formData.startEndTime[1] ? this.formData.startEndTime[1] : '',
            beginTime: this.formData.date1,
            endTime: endTime,
            published: 0,
            description: this.formData.notice,
            mark: this.formData.description
          }
          insertExamNon(params).then((res) => {
            if (res.code === 0) {
              this.$message({
                message: '新建成功',
                type: 'success'
              })
              this.$emit('call', 'refresh')
              this.close()
            }
          }).catch(() => {
            this.loading = false
          })
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.el-input {
  width: 66.6%;
}
.el-input__inner {
  width: 66.6%;
}
.el-select {
  width: 66.6%;
}
.el-date-editor {
  width: 100%;
}
.w-33 {
  width: 33.3%;
}
.w-66 {
  width: 66.6%;
}
</style>
