import splitPane from 'vue-splitpane'
export default {
  components: {
    splitPane
  },
  data() {
    return {
      fold: false, // 是否折叠
      minPercent: 0, // 树的最小宽度
      percent: 20, // 树的宽度
      treeWidth: null
    }
  },
  mounted() {
    this.minPercent = (10 / this.$refs['split-pane'].$el.offsetWidth) * 100
  },
  methods: {
    // 拖拽面板时
    resize(val) {
      this.percent = val
      this.treeWidth = this.$refs['split-pane'].$el.offsetWidth * (val / 100)
    },
    // 折叠面板时
    handleFold(flag) {
      this.fold = flag
      if (flag) {
        this.percent = this.minPercent
      } else {
        this.percent = 20
      }
    }
  }
}
