<template>
  <div v-loading="loading" class="dialog-wrap">
    <el-form ref="form" :model="formData" :rules="rules" label-width="100px" @submit.native.prevent>
      <el-form-item label="拓扑名称" prop="name">
        <el-input v-model.trim="formData.name"/>
      </el-form-item>
      <el-form-item label="拓扑描述" prop="description">
        <el-input v-model.trim="formData.description" type="textarea"/>
      </el-form-item>
    </el-form>
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button type="primary" @click="confirm" >确定</el-button>
    </div>
  </div>
</template>

<script>
import { editTopology } from '../api/orchestration'
import validate from '../../validate'
import modalMixins from '../../mixins/modal_form'
export default {
  mixins: [modalMixins],
  props: {
    // 传入数据
    data: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      loading: false,
      formData: {
        'name': '',
        'description': ''
      },
      rules: {
        'name': [
          validate.required(),
          validate.base_name
        ],
        'description': [
          validate.description
        ]
      }
    }
  },
  created() {
    const data = this.data
    this.formData['name'] = data[0].name
    this.formData['description'] = data[0].description
  },
  methods: {
    'changeEditing': function(value) {
      this.editing = true
    },
    // modal点击确定
    'confirm': function() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.loading = true
          const postData = this.formData
          this.$bus.$emit('SINGLE_TASK_API', {
            taskName: '编辑拓扑',
            resource: postData,
            apiObj: editTopology,
            data: {
              id: this.data[0].id,
              data: postData
            },
            sucsessCallback: () => {
              this.$bus.$emit('orchestration_module', 'reload')
            },
            errorCallback: () => {}
          })
          this.$emit('close')
        }
      })
    },
    close() {
      this.$emit('close')
    }
  }
}
</script>
