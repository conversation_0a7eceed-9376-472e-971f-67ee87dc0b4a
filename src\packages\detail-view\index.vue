<template>
  <div v-loading="loading" class="detail-wrap-layout">
    <template v-if="data && switchId">
      <!-- 路由模式才展示头部 -->
      <template v-if="mode === 'router'">
        <el-breadcrumb class="detail-breadcrumb" separator-class="el-icon-arrow-right">
          <el-breadcrumb-item :to="{ name: $route.meta.parentName, params}">{{ $route.meta.parentTitle }}</el-breadcrumb-item>
          <el-breadcrumb-item>{{ (labelName ? (labelName + '：') : '') + showName(data) }}</el-breadcrumb-item>
        </el-breadcrumb>
        <div v-if="showHeader" class="detail-header">
          <div class="detail-header-container">
            <div class="detail-header-icon">
              <i :class="$route.meta.icon" />
            </div>
            <div class="detail-header-info">
              <div class="detail-header-title">{{ (labelName ? (labelName + '：') : '') + showName(data) }}</div>
              <div v-overflow-tooltip class="detail-header-description">{{ showDesc(data) }}</div>
            </div>
          </div>
          <slot name="action" />
        </div>
        <div v-else style="position: absolute; right: 15px; top: 20px;">
          <slot name="action" />
        </div>
      </template>
      <el-tabs
        v-model="tabsActive"
        :style="{'margin-top': mode === 'router' ? (showHeader ? '5px' : '0px') : '26px'}"
        :class="{'detail-tabs-drawer': mode === 'drawer', 'has-slot': $slots.action}"
        :before-leave="beforeTabLeave"
        class="detail-tabs"
        type="card"
        @tab-click="handleTabClick"
      >
        <el-tab-pane v-for="item in viewItem" :label="item.transName" :name="item.name" :key="item.name">
          <component v-if="tabsActive === item.name" :is="item.component" :id="id" :data="data" :class="isPadding && tabsActive != 'announcement' ? 'detail-tabs-content-example' : 'detail-tabs-content'" />
        </el-tab-pane>
      </el-tabs>
      <div v-if="mode !== 'router'" class="detail-operation-right">
        <slot name="action" />
      </div>
    </template>
  </div>
</template>

<script>
export default {
  props: {
    params: {
      type: Object,
      default: () => {
        return {}
      }
    },
    // 载入状态
    loading: Boolean,
    // 页头默认展示key
    titleKey: {
      type: String,
      default: 'name'
    },
    // 描述默认展示key
    descKey: {
      type: String,
      default: 'description'
    },
    // 页头需要展示的额外内容
    labelName: {
      type: String,
      default: ''
    },
    // 资源ID
    id: {
      type: [String, Number]
    },
    // 资源数据对象
    data: {
      type: Object,
      default: () => {
        return null
      }
    },
    // 显示标签页
    viewItem: {
      type: Array,
      default: () => {
        return []
      }
    },
    // 是否需要调整padding 防止搜索框数字遮挡
    isPadding: {
      type: Boolean,
      default: false
    },
    // 是否需要路由
    router: {
      type: Boolean,
      default: true
    },
    // 展示模式 drawer侧拉 router独立路由
    mode: {
      type: String,
      default: 'router'
    },
    showHeader: {
      type: Boolean,
      default: true
    },
    // 不允许切换的tab页签数组
    notAllowClickTabs: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      tabsActive: 'overview', // 标签页默认Key
      switchId: true
    }
  },
  watch: {
    id(val) {
      this.switchId = false
      setTimeout(_ => {
        this.switchId = true
      }, 200)
    }
  },
  created() {
    try {
      if (this.router) {
        this.tabsActive = this.$route['params']['view'] || this.viewItem[0]['name']
      } else {
        this.tabsActive = this.viewItem[0]['name']
      }
    } catch (err) {
      if (this.viewItem.length && this.viewItem[0].hasOwnProperty('name')) {
        this.tabsActive = this.viewItem[0]['name']
      }
    }
  },
  methods: {
    'showName': function(data) {
      return data[this.titleKey] || '-'
    },
    'showDesc': function(data) {
      return data[this.descKey] || '暂无描述'
    },
    'handleTabClick': function(data) {
      if (this.router) {
        this.$router.replace({ name: this.$route.name, params: { ...this.$route.params, 'id': this.id, 'view': data.name }, query: { ...this.$route.query }})
      }
    },
    // 阻止tab页签切换
    beforeTabLeave(activeName, oldActiveName) {
      if (this.notAllowClickTabs && this.notAllowClickTabs.includes(activeName)) {
        return false
      }
      return true
    },
    'goBack': function() {
      this.$router.go(-1)
    }
  }
}
</script>
<style lang="less" scoped>
.detail-wrap-layout {
  height: 100%;
  display: flex;
  flex-direction: column;
  .detail-breadcrumb {
    padding: 12px 24px;
    font-size: 12px;
    line-height: 1.5715;
    /deep/ .el-breadcrumb__inner.is-link, .el-breadcrumb__inner a {
      font-weight: normal;
      color: rgba(0, 0, 0, 0.45);
      &:hover {
        color: var(--color-600)
      }
    }
    /deep/ .el-breadcrumb__item:last-child .el-breadcrumb__inner {
      color: rgba(0, 0, 0, 0.85);
    }
  }
  .detail-header {
    padding: 0 24px 20px;
    display: flex;
    justify-content: space-between;
    .detail-header-container {
      display: flex;
      flex-grow: 1;
      overflow: hidden;
      .detail-header-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        margin-right: 12px;
        background-color: var(--color-600);
        border-radius: 50%;
        flex-shrink: 0;
        i {
          color: var(--neutral-0);
          font-size: 20px;
        }
      }
      .detail-header-info {
        flex-grow: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        overflow: hidden;
        .detail-header-title {
          font-weight: 500;
          font-size: 20px;
          line-height: 28px;
          width: 100%;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
        .detail-header-description {
          color: var(--neutral-500);
          font-size: 14px;
          line-height: 22px;
          margin-top: 8px;
          width: 100%;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
    }
  }
  .detail-tabs {
    flex: 1;
    min-height: 0;
    /deep/ .el-tabs__content {
      height: calc(100% - 34px);
    }
    /deep/ .el-tab-pane {
      height: 100%;
    }
    .detail-tabs-content {
      height: 100%;
      overflow-x: hidden;
      overflow-y: auto;
      padding: 15px;
    }
    .detail-tabs-content-example {
      height: 100%;
      overflow-x: hidden;
      overflow-y: auto;
    }
  }
  /deep/ .detail-tabs-drawer {
    &.has-slot > .el-tabs__header {
      padding-right: 165px;
    }
    > .el-tabs__header {
      padding-right: 70px;
    }
    .el-tabs__nav-next, .el-tabs__nav-prev {
      line-height: 34px;
      z-index: 9999;
    }
  }
  .detail-operation-right {
    position: absolute;
    top: 14px;
    right: 65px;
  }
}
</style>
