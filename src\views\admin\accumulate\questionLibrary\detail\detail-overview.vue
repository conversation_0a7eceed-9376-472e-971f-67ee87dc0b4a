<template>
  <el-row :gutter="20">
    <el-col :span="12">
      <detail-card title="基本信息">
        <el-form slot="content" label-position="left" label-width="110px" label-suffix=":">
          <el-form-item label="名称">{{ data.questionDepotName || '-' }}</el-form-item>
          <el-form-item label="题目数量">{{ data.questionNumber || 0 }}</el-form-item>
          <el-form-item label="版本">{{ data.questionDepotVersion || '-' }}</el-form-item>
          <el-form-item label="描述">{{ data.questionDepotDescription || '-' }}</el-form-item>
          <el-form-item label="创建时间">{{ data.createTime || '-' }}</el-form-item>
        </el-form>
      </detail-card>
    </el-col>
  </el-row>
</template>

<script>
import moduleConf from '../config.js'
import detailCard from '@/packages/detail-view/detail-card.vue'
export default {
  name: 'DetailOverview',
  components: {
    detailCard
  },
  props: {
    id: {
      type: [Number, String]
    },
    data: {
      type: Object
    }
  },
  data() {
    return {
      moduleConf: moduleConf
    }
  }
}
</script>
