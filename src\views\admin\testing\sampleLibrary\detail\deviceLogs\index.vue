<template>
  <div class="content-wrap-layout">
    <div class="switch-group">
      <el-radio-group v-model="activeName" size="small" class="common-radio-group" @change="changeType">
        <el-radio-button label="1">会话记录</el-radio-button>
        <el-radio-button label="2">命令记录</el-radio-button>
        <el-radio-button label="3">文件传输记录</el-radio-button>
      </el-radio-group>
    </div>
    <div class="vertical-wrap">
      <!-- 会话记录-->
      <sessionRecord v-if="activeName == '1'" :data="data"/>
      <!-- 命令记录 -->
      <commandRecord v-if="activeName == '2'"/>
      <!-- 文件传输记录 -->
      <fileTransfer v-if="activeName == '3'"/>
    </div>
  </div>
</template>

<script>
import sessionRecord from './sessionRecord/index.vue'
import commandRecord from './commandRecord/index.vue'
import fileTransfer from './fileTransfer/index.vue'
export default {
  components: {
    sessionRecord,
    commandRecord,
    fileTransfer
  },
  props: {
    data: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      loading: false,
      activeName: '1' // 1.会话记录 2.命令记录 3.文件传输记录
    }
  },
  methods: {
    changeType(val) {
      this.activeName = val
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-radio-group.common-radio-group {
  border-radius: 10px;
  border: none;
  padding: 2px;
  background-color: #F5F6F9;
  .el-radio-button {
    width: 110px;
    .el-radio-button__inner {
      width: 100%;
      background-color: #F5F6F9;
      border: none;
      border-radius: 8px;
      font-size: 14px;
      font-weight: bold;
      color: var(--neutral-700);
    }
  }
  .is-active {
    .el-radio-button__inner {
      background-color: #fff;
      color: var(--color-600);
      border-radius: 8px;
      font-size: 14px;
      border: none;
      font-weight: bold;
    }
  }
  .el-radio-button__orig-radio:checked + .el-radio-button__inner {
    box-shadow: none;
    -webkit-box-shadow: none;
  }
}
</style>
