<template>
  <div class="resource-table">
    <!-- 操作区 -->
    <div class="operation-wrap">
      <div class="operation-left">
        <slot name="action" />
        <el-button type="primary" icon="el-icon-refresh" @click="refresh" />
      </div>
      <div class="operation-right">
        <el-badge :value="searchBtnShowNum">
          <el-button icon="el-icon-search" @click="openSearch">搜索</el-button>
        </el-badge>
        <!-- 自定义表格列 -->
        <t-table-config
          v-if="!customColData.length"
          :data="columnsObj"
          :active-key-arr="columnsViewArr"
          @on-change-col="onChangeCol"
        />
      </div>
    </div>
    <!-- 搜索区 -->
    <t-search-box
      v-show="searchView"
      :search-key-list="searchKeyListView"
      default-placeholder="默认搜索名称"
      @search="searchMultiple"
    />
    <!-- 列表 -->
    <t-table-view
      ref="tableView"
      :height="height"
      :single="single"
      :loading="tableLoading"
      :data="tableData"
      :total="tableTotal"
      :page-size="pageSize"
      :current="pageCurrent"
      :select-item="selectItem"
      current-key="knowledgeCode"
      type="list"
      @on-select="onSelect"
      @on-current="onCurrent"
      @on-change="changePage"
      @on-sort-change="onSortChange"
      @on-page-size-change="onPageSizeChange"
    >
      <el-table-column v-for="item in columnsViewArr" :key="item" :prop="item" :min-width="colMinWidth" :label="columnsObj[item].title" :fixed="columnsObj[item].master ? 'left' : false" show-overflow-tooltip>
        <template slot-scope="{row}">
          <template v-if="item === 'taskName'">
            <span>{{ row.taskName || '-' }}</span>
          </template>
          <span v-else>{{ row[item] || '-' }}</span>
        </template>
      </el-table-column>
    </t-table-view>
  </div>
</template>

<script>
import moduleConf from '../config'
import tSearchBox from '@/packages/search-box/index.vue'
import tTableView from '@/packages/table-view/index.vue'
import tTableConfig from '@/packages/table-config/table-col-config.vue'
import mixinsPageTable from '@/packages/mixins/page_table'
import { queryTaskByKnowledge } from '@/api/admin/training/knowledge'
import { searchKeyList, columnsObj, columnsViewArr } from './utils'

export default {
  components: {
    tSearchBox,
    tTableView,
    tTableConfig
  },
  mixins: [mixinsPageTable],
  data() {
    return {
      moduleName: moduleConf.name,
      // 搜索配置项
      searchKeyList: [...searchKeyList],
      // 所有可配置显示列 master：不可隐藏 title:列名称
      columnsObj: { ...columnsObj },
      // 当前显示列key表 默认，如果localStorage有数据将被覆盖
      columnsViewArr: [...columnsViewArr],
      id: this.$route.params.id
    }
  },
  methods: {
    getList: function(showLoading = true) {
      if (!this.id) {
        return
      }
      if (showLoading) {
        this.tableLoading = true
      }
      const postData = this.getPostData('page', 'limit')
      const params = { ...postData, knowledgeCode: this.id }
      queryTaskByKnowledge(params).then((res) => {
        if (res.code === 0) {
          this.tableData = res.data.records || []
          this.tableTotal = res.data.total || 0
        }
      }).finally(() => {
        this.tableLoading = false
      })
    }
  }
}
</script>
