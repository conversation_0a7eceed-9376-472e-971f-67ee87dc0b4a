import { loginAP<PERSON>, logoutAP<PERSON> } from '@/api/login'
import { adminAuthAPI } from '@/api/usercenter/role'
import { getDeptListAPI } from '@/api/usercenter/dept'
import { getLoginUserAPI } from '@/api/usercenter/user'
import { addAuth, removeAuth } from '@/utils/auth'
import { resetRouter } from '@/router'
import { request } from '@/utils'
import Lockr from 'lockr'

const user = {
  state: {
    userInfo: null, // 用户信息
    // 权限信息
    allAuth: null, // 总权限信息 默认空 调整动态路由
    userList: [], // 管理后台员工和部门
    deptList: [],
    manage: {} // 管理后台
  },

  mutations: {
    SET_USERINFO: (state, userInfo) => {
      state.userInfo = userInfo
      localStorage.setItem('loginUserInfo', JSON.stringify(userInfo))
    },
    SET_ALLAUTH: (state, allAuth) => {
      state.allAuth = allAuth
    },
    SET_MANAGE: (state, manage) => {
      state.manage = manage
    },
    SET_AUTH: (state, data) => {
      const token = data.adminToken
      Lockr.set('Admin-Token', token)
      addAuth(token)
    },
    SET_USERLIST: (state, data) => {
      state.userList = data
    },
    SET_DEPTLIST: (state, data) => {
      state.deptList = data
    }
  },

  actions: {
    // 登录
    Login({ commit, dispatch }, userInfo) {
      return new Promise((resolve, reject) => {
        loginAPI(userInfo).then(res => {
          const data = res.data || {}
          if (!data.hasOwnProperty('companyList')) {
            commit('SET_AUTH', data)
            dispatch('GetUserInfo')
          }
          resolve(res)
        }).catch(error => {
          reject(error)
        })
      })
    },
    // 获取权限
    getAuth({ commit, dispatch }) {
      return new Promise((resolve, reject) => {
        adminAuthAPI().then((response) => {
          const data = response.data
          Lockr.set('authList', data)
          data.wkFirstModel = data.firstModel
          commit('SET_ALLAUTH', data)
          commit('SET_MANAGE', data.manage)
          // 获取部门信息
          dispatch('GetDeptList')
          resolve(data)
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 获取用户信息
    GetUserInfo({ commit, dispatch }) {
      return new Promise((resolve, reject) => {
        getLoginUserAPI().then(response => {
          commit('SET_USERINFO', response.data)
          dispatch('SystemLogoAndName')
          request(response.data)
          resolve(response)
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 登出
    LogOut({ commit }) {
      return new Promise((resolve, reject) => {
        logoutAPI().then(() => {
          localStorage.removeItem('__admintoken__')
          removeAuth()
          resetRouter()
          resolve()
        }).catch(error => {
          localStorage.removeItem('__admintoken__')
          removeAuth()
          resetRouter()
          reject(error)
        })
      })
    },

    // 管理后台部门列表
    GetDeptList({ commit }) {
      return new Promise((resolve, reject) => {
        getDeptListAPI({ type: 'tree' }).then(res => {
          commit('SET_DEPTLIST', res.data || [])
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    }
  }
}

export default user
