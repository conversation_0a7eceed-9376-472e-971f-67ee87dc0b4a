const state = {
  'globalSocket': null,
  'instanceSocket': null,
  'snapshotSocket': null,
  'topologySocket': null,
  'imageSocket': null
}
const actions = {
  'SOCKET_connect'() {
    console.log('Websocket success!')
  },
  'SOCKET_disconnect'() {
    console.log('Websocket 断开')
  },
  'SOCKET_nfvo_event'({ dispatch, commit, rootState }, data) {
    commit('CHANGE_WEBSOCKETINFO', data)
  }
}
const mutations = {
  CHANGE_WEBSOCKETINFO(state, data = null) {
    state[data['message_type'] + 'Socket'] = data
    state['globalSocket'] = data
  }
}
export default {
  namespaced: true,
  state,
  actions,
  mutations
}
