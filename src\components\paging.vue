<template>
  <div class="paging-div">
    <el-pagination
      :current-page="pageNum"
      :page-sizes="pageSizesList"
      :page-size="pageSize"
      :total="total"
      layout="sizes, prev, pager, next"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"/>
  </div>
</template>

<script>
export default {
  props: {
    pageNum: {
      type: Number,
      default: 1
    },
    pageSizesList: {
      type: Array
    },
    pageSize: {
      type: Number,
      default: 10
    },
    total: {
      type: Number,
      default: 0
    },
    handleSizeChange: {
      type: Function,
      default: null
    },
    handleCurrentChange: {
      type: Function,
      default: null
    }
  }
}
</script>

<style lang="scss" scoped>
 .paging-div{
    margin-top: 20px;
    display: flex;
    justify-content: end;
    ::v-deep {
      .el-pager{
        .active{
          background: none !important;
          border: none;
        }
      }
    }
  }
</style>
