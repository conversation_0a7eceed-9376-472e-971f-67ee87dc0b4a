<template>
  <div>
    <el-dialog :title="publishName" :visible.sync="dialogVisible" width="520px" @closed="close">
      <div v-loading="loading" class="dialog-wrap">
        <el-alert :closable="false" type="warning">
          <div v-show="publishName == '发布'" slot="title">
            <p>课程发布后可被助教进行排课，若课程状态为“公开"，则展示在学员"自选课程中心"，请确认课程完整性后发布。</p>
          </div>
          <div v-show="publishName == '取消发布'" slot="title">
            <p>课程取消发布后不可被助教进行排课，若课程状态为“公开”，则取消展示在学员”自选课程中心”，请确认是否取消发布。</p>
          </div>
        </el-alert>
        <el-checkbox v-model="checked">我已知晓上述风险</el-checkbox>
        <div class="dialog-footer">
          <el-button type="text" @click="close">取消</el-button>
          <el-button :disabled="!checked" type="primary" @click="confirm">确定</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import module from '../config.js'
import { updataCourseCategory } from '@/api/teacher/index.js'

export default {
  name: 'ModalPublish',
  components: {},
  mixins: [],
  props: {
    data: Object,
    publishName: {
      type: String
    },
    publishControl: {
      type: Boolean
    },
    publishItem: {
      type: Object
    }
  },
  data() {
    return {
      moduleName: module.name,
      loading: false,
      checked: false,
      dialogVisible: false
    }
  },
  watch: {
    publishControl() {
      this.dialogVisible = this.publishControl
    }
  },
  mounted() {
  },
  methods: {
    close() {
      this.checked = false
      this.$emit('close')
    },
    confirm: function() {
      console.log('this.data', this.data, this.publishItem)
      const params = {
        id: this.publishItem.id,
        coursePublish: this.publishName == '发布' ? 1 : 0
      }
      this.loading = true
      updataCourseCategory(params).then(res => {
        if (res.code === 0) {
          this.$message.success(`${this.publishName == '发布' ? '发布成功' : '取消发布成功'}`)
          this.$emit('close', 'refresh')
          this.loading = false
        }
      }).catch(() => {
        this.$emit('close')
        this.loading = false
      })
    }
  }
}
</script>
