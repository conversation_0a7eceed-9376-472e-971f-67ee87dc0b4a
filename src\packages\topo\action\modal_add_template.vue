<template>
  <div v-loading="loading" class="dialog-wrap">
    <el-form ref="form" :model="formData" :rules="rules" label-width="80px" @submit.native.prevent>
      <el-form-item label="名称" prop="name">
        <el-input v-model.trim="formData.name"/>
      </el-form-item>
      <el-form-item label="描述" prop="description">
        <el-input v-model.trim="formData.description" type="textarea" />
      </el-form-item>
    </el-form>
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>

<script>
import { createTemplate } from '../api/orchestration'
import modalMixins from '../../mixins/modal_form'
import { mapGetters } from 'vuex'
import validate from '../../validate'
import { Graph } from '@antv/x6'
export default {
  mixins: [modalMixins],
  props: {
    // 传入数据
    data: {
      type: Object,
      default: () => {
        return {}
      }
    },
    graph: {
      type: [Object, Graph]
    }
  },
  data() {
    return {
      loading: false,
      formData: {
        'name': '',
        'description': ''
      },
      rules: {
        'name': [
          validate.required(),
          validate.name_64_char
        ],
        'description': [
          validate.description
        ]
      }
    }
  },
  computed: {
    ...mapGetters([
      'userInfo'
    ])
  },
  methods: {
    // modal点击确定
    'confirm': function() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.loading = true
          const postData = this.formData
          postData['topology_id'] = this.data.topology_id
          postData.type = 'template'
          postData.user_id = this.userInfo.userId
          const json = this.graph.toJSON()
          this.graph.toPNG((data) => {
            postData.thumbnail = json.cells.length ? data : ''
            createTemplate(postData).then(res => {
              this.$message.success(`模板生成成功`)
            })
          })
          this.close()
        }
      })
    },
    close() {
      this.$emit('close')
    }
  }
}
</script>
