import request from '@/utils/request'
const headers = { 'Content-Type': 'application/json;charset=UTF-8' }

// 分页查询场景列表权限
export function sceneQueryPageWebAPI(data) {
  return request({
    url: 'scene/sceneBasicInfo/queryPageWeb',
    method: 'post',
    headers,
    data
  })
}

// 分页查询场景类型
export function sceneTypePageAPI(data) {
  return request({
    url: 'scene/sceneType/queryPage',
    method: 'post',
    headers,
    data
  })
}

// 通过主键查询单条数据
export function getSceneBasicInfoAPI(id) {
  return request({
    url: 'scene/sceneBasicInfo/get?id=' + id,
    method: 'get',
    headers
  })
}

// 配置角色-获取角色列表
export function queryTrainingRoleByInstanceId(data) {
  return request({
    url: '/scene/sceneRoleInstance/queryTranningRoleByInstanceId',
    method: 'POST',
    headers,
    data
  })
}

// 学生关联角色接口
export function studentAllocateRole(data) {
  return request({
    url: '/scene/sceneInstancePlayerRel/createWithTraining',
    method: 'POST',
    headers,
    data
  })
}

// 通过实例id获取拓扑
export function getTopologyIdBySceneInstanceId(data) {
  return request({
    url: '/scene/sceneBasicInfoInstance/getTopologyIdBySceneInstanceId',
    method: 'POST',
    headers,
    data
  })
}

// 查看是否拥有拓扑场景
export function topologyQueryById(data) {
  return request({
    url: '/training/content/topology/queryById',
    method: 'POST',
    headers,
    data
  })
}
