<!--
虚拟机配置表格组件 - 本地数据版本
改造说明：
1. 将原来的实时API调用改为本地数据存储
2. 实现了本地分页功能，数据存储在 localVirtualMachines 中
3. 通过 computed 属性 paginatedData 实现分页显示
4. 提供了 addVirtualMachine, updateVirtualMachine, removeVirtualMachineLocal 等方法操作本地数据
5. 通过 syncToParent 方法保持与父组件数据同步
6. 所有增删改操作都在本地进行，不再调用后端API
7. 数据只在最终提交时发送到后端
-->
<template>
  <div class="resource-table" style="height: 100%; padding: 0;">
    <!-- 虚拟机配置列表 -->
    <t-table-view
      ref="tableView"
      :height="height"
      :single="single"
      :loading="tableLoading"
      :data="paginatedData"
      :total="virtualTableTotal"
      :page-size="pageSize"
      :current="pageCurrent"
      :select-item="selectItem"
      type="list"
      current-key="id"
      @on-select="onSelect"
      @on-current="onCurrent"
      @on-change="changePage"
      @on-sort-change="onSortChange"
      @on-page-size-change="onPageSizeChange"
    >
      <el-table-column
        v-for="item in columnsViewArr"
        :key="item"
        :min-width="colMinWidth"
        :width="columnsObj[item].colWidth"
        :label="columnsObj[item].title"
        :fixed="columnsObj[item].master ? 'left' : false"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <div v-if="item == 'memory'">
            {{ scope.row.memory ? `${scope.row.memory} ${scope.row.memoryUnit}` : '-' }}
          </div>
          <div v-else-if="item == 'workstation'">
            <span v-if="scope.row[item] == '0'">否</span>
            <span v-else-if="scope.row[item] == '1'">是</span>
            <span v-else>-</span>
          </div>
          <div v-else-if="item == 'handle'">
            <el-button
              type="text"
              size="small"
              style="color: var(--color-600)"
              @click="editVirtualMachine('edit', scope.row)"
            >编辑</el-button>
            <el-button
              type="text"
              size="small"
              style="color: var(--color-600)"
              @click="removeVirtualMachine(getOriginalIndex(scope.$index))"
            >删除</el-button>
          </div>
          <span v-else>{{ scope.row[item] || "-" }}</span>
        </template>
      </el-table-column>
    </t-table-view>
  </div>
</template>
<script>
import moduleConf from '../config'
import mixinsPageTable from '@/packages/mixins/page_table'
import tTableView from '@/packages/table-view/index.vue'
import tTableConfig from '@/packages/table-config/table-col-config.vue'
import tableTdMultiCol from '@/packages/table-config/table-td-multi-col.vue'

export default {
  components: {
    tTableView,
    tTableConfig,
    tableTdMultiCol
  },
  mixins: [mixinsPageTable],
  props: {
    projectId: {
      type: String,
      default: ''
    }
  },
  inject: ['testSubmitVm'],
  data() {
    return {
      searchKeyList: [],
      columnsObj: moduleConf.virtualColumnsObj,
      columnsViewArr: moduleConf.virtualColumnsViewArr,
      // 本地虚拟机数据存储
      localVirtualMachines: [],
      // 下一个ID计数器（用于新增数据的临时ID）
      nextTempId: 1
    }
  },
  computed: {
    // 计算分页后的数据
    paginatedData() {
      const start = (this.pageCurrent - 1) * this.pageSize
      const end = start + this.pageSize
      return this.localVirtualMachines.slice(start, end)
    },
    // 更新总数为本地数据的长度
    virtualTableTotal() {
      return this.localVirtualMachines.length
    }
  },
  watch: {
    // 监听testSubmitVm中的虚拟机数据变化，同步到本地
    'testSubmitVm.formData.virtualMachines': {
      handler(newVal) {
        if (newVal && Array.isArray(newVal)) {
          this.localVirtualMachines = [...newVal]
        }
      },
      immediate: true,
      deep: true
    }
  },
  created() {
    // 初始化时如果有数据，同步到本地
    if (this.testSubmitVm.formData.virtualMachines) {
      this.localVirtualMachines = [...this.testSubmitVm.formData.virtualMachines]
    }
  },
  methods: {
    // 重写getList方法，改为本地数据处理
    getList: function(showLoading = true) {
      if (showLoading) {
        this.tableLoading = true
      }

      // 模拟异步加载
      setTimeout(() => {
        this.tableLoading = false
        // 同步本地数据到父组件
        this.testSubmitVm.formData.virtualMachines = [...this.localVirtualMachines]
      }, 100)
    },

    // 添加虚拟机到本地数据
    addVirtualMachine(vmData) {
      this.localVirtualMachines.push(vmData)
      this.syncToParent()

      // 如果当前页显示不下新数据，跳到最后一页
      const totalPages = Math.ceil(this.localVirtualMachines.length / this.pageSize)
      if (this.pageCurrent < totalPages) {
        this.pageCurrent = totalPages
      }
    },

    // 编辑虚拟机
    editVirtualMachine(type, data) {
      // 添加原始索引属性
      const originalIndex = this.getOriginalIndex(this.paginatedData.indexOf(data))
      const dataWithIndex = { ...data, _index: originalIndex }
      this.$emit('editVirtualMachine', type, dataWithIndex)
    },

    // 删除虚拟机
    removeVirtualMachine(index) {
      this.$emit('removeVirtualMachine', index)
    },

    // 从本地数据中删除虚拟机
    removeVirtualMachineLocal(index) {
      if (index >= 0 && index < this.localVirtualMachines.length) {
        this.localVirtualMachines.splice(index, 1)
        this.syncToParent()

        // 如果当前页没有数据了，回到上一页
        if (this.paginatedData.length === 0 && this.pageCurrent > 1) {
          this.pageCurrent = this.pageCurrent - 1
        }
      }
    },

    // 更新虚拟机数据
    updateVirtualMachine(index, vmData) {
      if (index >= 0 && index < this.localVirtualMachines.length) {
        this.$set(this.localVirtualMachines, index, vmData)
        this.syncToParent()
      }
    },

    // 同步本地数据到父组件
    syncToParent() {
      this.testSubmitVm.formData.virtualMachines = [...this.localVirtualMachines]
    },

    // 获取显示的序号（考虑分页）
    getDisplayIndex(pageIndex) {
      return (this.pageCurrent - 1) * this.pageSize + pageIndex + 1
    },

    // 获取原始数组中的索引
    getOriginalIndex(pageIndex) {
      return (this.pageCurrent - 1) * this.pageSize + pageIndex
    },

    // 重写changePage方法
    changePage(num) {
      this.pageCurrent = num
    },

    // 重写onPageSizeChange方法
    onPageSizeChange(pageSize) {
      this.pageSize = pageSize
      // 如果当前页超出了新的页数范围，调整到最后一页
      const maxPage = Math.ceil(this.localVirtualMachines.length / pageSize)
      if (this.pageCurrent > maxPage && maxPage > 0) {
        this.pageCurrent = maxPage
      }
    }
  }
}
</script>
