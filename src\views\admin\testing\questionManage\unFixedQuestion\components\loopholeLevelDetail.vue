<template>
  <div class="drawer-wrap">
    <!-- 漏洞等级说明 -->
    <div v-if="loopholeLevelDetail" class="level-detail" v-html="loopholeLevelDetail"/>
    <!-- 底部 -->
    <div class="drawer-footer">
      <el-button type="primary" @click="confirm">关闭</el-button>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    name: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      loopholeLevelDetail: ''
    }
  },
  created() {
    this.loopholeLevelDetail = `漏洞等级说明
所提交的安全问题评级划分等级：超危、高危、中危、低危，和无效（驳回）。所有漏洞评级流程以所提交的漏洞内容为准。

[超危]
1.默认配置下直接稳定获取设备或主机 root 权限的漏洞，包括但不限于上传 Webshell （需要登录为高危，不能上传 Webshell 的文件上传等文件操作为中危）、任意代码执行、远程命令执行等。同时需满足以下规定：
*无需用户交互
*不借助第三方漏洞
*不借助其他方式才能获取最高权限
注：由于主机侧影响面没有设备侧危害大，客户端最高到高危评级。
2.不需登录直接导致严重的信息泄露漏洞，特别是涉及的可影响客户身份信息安全的信息，包括但不限于重要数据库的 SQL 注入、系统权限控制不严格等导致的敏感数据泄露漏洞等。
3.不需登录直接导致严重影响的逻辑漏洞，包括但不限于核心账户体系的账密校验逻辑、支付逻辑漏洞等。如果漏洞利用时没有明显恶意利用操作，则不适用于定级为超危漏洞。

[高危]
1.需要登录的重要业务敏感数据信息泄露漏洞，包括但不限于重要用户信息、配置信息、数据文件信息等。
2.需要登录的重要业务的逻辑漏洞，包括但不限于权限绕过等。权限绕过需要获取到权限后造成较大影响或者具体敏感信息等明显恶意效果。
3.包含重要业务敏感信息的非授权访问，包括但不仅限于绕过认证直接访问管理后台、后台弱密码、SSRF 等。 SSRF 需要能够直接访问并连接内网设备且可获取到回显的。例如，SSRF 只能获取各个服务器等连接或者端口等信息;后台弱密码仅能查看非敏感文档信息等影响不高的操作，或非后台用户账户弱密码泄露则不适用于定级为高危，可降级处理。
4.登录前影响应用服务正常运转，包括但不限于应用层远程拒绝服务等。比如，服务器或进程崩溃后不在被持续攻击的情况下仍然处于崩溃状态，需要人工干预恢复正常的情况等定级为高，该情况登录后的漏洞可评级为中;单纯造成进程崩溃、短时间占用所有访问资源的情况定级为低或无效，具体视情况而定。
5.越权使用他人身份进行敏感操作，比如修改配置设定、删除备份文件等;越权后无任何操作或进行非核心功能的操作，无较大影响，例如访问非敏感项目信息等不能定级为高危，可降级处理。

[中危]
1.不需交互对用户产生危害的安全漏洞，包括但不限于存储型 XSS、DOM-XSS、CSRF 等。
2.普通信息泄露漏洞，包括但不限于用户敏感信息泄露（例如身份证号、姓名、地址、手机号等）、业务敏感信息泄露（例如明文存储密码、含敏感信息的源代码压缩包泄漏等）等漏洞。
3.普通的逻辑设计缺陷和流程缺陷，包括但不限于越权查看非核心系统的订单信息、记录等。
4.部分本地任意代码执行漏洞，根据实际触发漏洞的条件、难易程度、实际危害等可进行降级或升级处理。
5.其他造成中度影响的漏洞，例如：没有敏感信息的 SQL 注入、无法回显的 SSRF 漏洞等。

[低危]
1.轻微信息泄露，包括但不限于路径信息泄露、SVN 信息泄露、phpinfo、日志文件、配置信息等。
2.本地拒绝服务，包括但不限于客户端本地拒绝服务等引起的问题。
3.需点击链接进行交互的 OAuth 登录或绑定劫持。
4.可能存在安全隐患但利用成本很高的漏洞，包括但不限于反射型 XSS、只能在特定或非主流浏览器环境下才能成功利用的存储型 XSS、DOM-XSS 等 XSS 漏洞、需要用户连续交互的敏感安全漏洞。例如：解析漏洞、暴力破解等。

[无效]（驳回）
1.不涉及安全问题的 BUG 问题，包括但不限于功能缺陷、网页乱码、样式混乱、静态文件遍历、应用兼容性等问题。
2.难以或无法利用的漏洞，包括但不限于以下漏洞或情形：
*SELF-XSS、无法获取 Cookie 的 XSS
*phpinfo 相关漏洞
*无敏感操作的 CSRF，如收藏、非重要业务的订阅、非敏感信息或资料修改等
*无意义的异常堆栈
*没有实际意义的扫描器漏洞报告（如WebServer的低版本）
*无敏感信息的 JSON Hijacking
*无意义的信息漏洞，如源码泄漏、内网 IP 地址/域名泄漏、401基础认证钓鱼、程序路径信任问题、无敏感信息的 logcat、无敏感信息的日志文件泄露等
*文件上传后不配其他漏洞无法直接解析的情况
*无实际意义的破解或授权的安全问题，如破解功能不完全、破解后无法更新使用核心功能（例如病毒库无法更新使用最新版）、新版刷降级固件破解或者屏蔽授权等
3.无任何证据的猜测，包括但不限于扫描后发现组件低于某版本则断定存在某漏洞等。非资产范围的任何安全问题。
4.部分本地任意代码执行漏洞：文件关联的 DLL 劫持（如若属于但不限于以下:加载不存在的 DLL 文件、加载正常 DLL 未校验合法性，需要管理员权限操作需要用户大量交互以及基于 KnownDUs 缺陷所导致的 DU 劫持等情况）。`
  },
  methods: {
    confirm() {
      this.$emit('close')
    }
  }
}
</script>

<style lang="scss" scoped>
.drawer-wrap {
  display: flex;
  flex-direction: column;
  .level-detail {
    white-space: pre-line;
    line-height: 1.5;
    font-size: 14px;
    overflow-y: auto;
    flex: 1;
    padding: 20px;
  }
  .drawer-footer {
    height: 60px;
  }
}
</style>
