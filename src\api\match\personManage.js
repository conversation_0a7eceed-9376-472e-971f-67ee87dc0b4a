import request from '@/utils/request'
const headers = { 'Content-Type': 'application/json;charset=UTF-8' }

// 分页查询赛事所有战队
export function queryBigMatchTeamPage(data) {
  return request({
    url: '/match/bigMatchTeamPlayerRelation/queryTeamPage',
    method: 'post',
    data: data,
    headers
  })
}

// 分页查询赛事所有人员
export function queryBigMatchPlayerPage(data) {
  return request({
    url: '/match/bigMatchTeamPlayerRelation/queryPlayerPage',
    method: 'post',
    data: data,
    headers
  })
}

// 新增赛事战队
export function createBigMatchTeam(data) {
  return request({
    url: '/match/bigMatchTeamPlayerRelation/createTeam',
    method: 'post',
    data: data,
    headers
  })
}

// 新增赛事人员
export function createBigMatchPlayer(data) {
  return request({
    url: '/match/bigMatchTeamPlayerRelation/createPlayer',
    method: 'post',
    data: data,
    headers
  })
}

// 删除赛事战队
export function removeBigMatchTeam(data) {
  return request({
    url: '/match/bigMatchTeamPlayerRelation/removeMatchTeam',
    method: 'post',
    data: data,
    headers
  })
}

// 删除赛事人员
export function removeBigMatchPlayer(data) {
  return request({
    url: '/match/bigMatchTeamPlayerRelation/removeMatchPlayer',
    method: 'post',
    data: data,
    headers
  })
}

// 查询赛事战队下的人员列表
export function pageByTeamAndBigMatch(data) {
  return request({
    url: '/match/bigMatchTeamPlayerRelation/pageByTeamAndBigMatch',
    method: 'post',
    data: data,
    headers
  })
}

// 获取已添加选手id
export function queryPlayerByBigMatch(query) {
  return request({
    url: '/match/bigMatchTeamPlayerRelation/queryPlayerByBigMatch',
    method: 'get',
    params: query
  })
}

// 批量导入战队
export function excelImportTeam(data) {
  return request({
    url: '/match/bigMatchTeamPlayerRelation/excelImportTeam',
    method: 'post',
    data,
    headers
  })
}

// 批量导入赛事战队模板
export function downExcelImportTeamModel(data) {
  return request({
    url: '/match/bigMatchTeamPlayerRelation/downExcelImportTeamModel',
    method: 'post',
    headers,
    data,
    responseType: 'blob'
  })
}

// 批量导入赛事战队错误列表
export function downImportTeamErrorExcel(data) {
  return request({
    url: '/match/bigMatchTeamPlayerRelation/downImportTeamErrorExcel',
    method: 'post',
    headers,
    data,
    responseType: 'blob'
  })
}

// 导出赛事战队信息
export function exportTeam(data) {
  return request({
    url: '/match/bigMatchTeamPlayerRelation/exportTeam',
    method: 'post',
    headers,
    data,
    responseType: 'blob'
  })
}

// 批量导入人员
export function excelImportPlayer(data) {
  return request({
    url: '/match/bigMatchTeamPlayerRelation/excelImportPlayer',
    method: 'post',
    data,
    headers
  })
}

// 批量导入赛事人员模板
export function downExcelImportPlayerModel(data) {
  return request({
    url: '/match/bigMatchTeamPlayerRelation/downExcelImportPlayerModel',
    method: 'post',
    headers,
    data,
    responseType: 'blob'
  })
}

// 批量导入赛事人员错误列表
export function downImportPlayerErrorExcel(data) {
  return request({
    url: '/match/bigMatchTeamPlayerRelation/downImportPlayerErrorExcel',
    method: 'post',
    headers,
    data,
    responseType: 'blob'
  })
}

// 导出赛事人员信息
export function exportPlayer(data) {
  return request({
    url: '/match/bigMatchTeamPlayerRelation/exportPlayer',
    method: 'post',
    headers,
    data,
    responseType: 'blob'
  })
}


/**
 * 比赛
 */
// 分页查询比赛战队
export function querySeasonTeamPage(data) {
  return request({
    url: '/match/bigMatchSeasonTeamPlayerRelation/querySeasonTeamPage',
    method: 'post',
    data: data,
    headers
  })
}

// 分页查询比赛所有人员
export function querySeasonPlayerPage(data) {
  return request({
    url: '/match/bigMatchSeasonTeamPlayerRelation/querySeasonPlayerPage',
    method: 'post',
    data: data,
    headers
  })
}

// 新增比赛战队
export function createSeasonTeam(data) {
  return request({
    url: '/match/bigMatchSeasonTeamPlayerRelation/createSeasonTeam',
    method: 'post',
    data: data,
    headers
  })
}

// 新增比赛人员
export function createSeasonPlayer(data) {
  return request({
    url: '/match/bigMatchSeasonTeamPlayerRelation/createSeasonPlayer',
    method: 'post',
    data: data,
    headers
  })
}

// 新增比赛战队(无赛事)
export function createSeasonTeamNoCompetition(data) {
  return request({
    url: '/match/bigMatchSeasonTeamPlayerRelation/createSeasonTeamNoCompetition',
    method: 'post',
    data: data,
    headers
  })
}

// 新增比赛人员(无赛事)
export function createSeasonPlayerNoCompetition(data) {
  return request({
    url: '/match/bigMatchSeasonTeamPlayerRelation/createSeasonPlayerNoCompetition',
    method: 'post',
    data: data,
    headers
  })
}

// 比赛批量导入战队
export function excelSeasonImportTeam(data) {
  return request({
    url: '/match/bigMatchSeasonTeamPlayerRelation/excelImportTeam',
    method: 'post',
    data,
    headers
  })
}

// 比赛批量导入人员
export function excelSeasonImportPlayer(data) {
  return request({
    url: '/match/bigMatchSeasonTeamPlayerRelation/excelImportPlayer',
    method: 'post',
    data,
    headers
  })
}

// 比赛战队下载错误导出
export function downImportTeamSeasonErrorExcel(data) {
  return request({
    url: '/match/bigMatchSeasonTeamPlayerRelation/downImportTeamErrorExcel',
    method: 'post',
    headers,
    data,
    responseType: 'blob'
  })
}

// 比赛人员下载错误导出
export function downImportPlayerSeasonErrorExcel(data) {
  return request({
    url: '/match/bigMatchSeasonTeamPlayerRelation/downImportPlayerErrorExcel',
    method: 'post',
    headers,
    data,
    responseType: 'blob'
  })
}

// 删除比赛战队
export function removeSeasonTeam(data) {
  return request({
    url: '/match/bigMatchSeasonTeamPlayerRelation/removeMatchSeasonTeam',
    method: 'post',
    data: data,
    headers
  })
}
// 删除比赛战队 -- 添加比赛id
export function removeSeasonIdTeam(data) {
  return request({
    url: '/match/bigMatchSeasonTeamPlayerRelation/removeSeasonIdTeam',
    method: 'post',
    data: data,
    headers
  })
}

// 删除比赛人员
export function removeSeasonPlayer(data) {
  return request({
    url: '/match/bigMatchSeasonTeamPlayerRelation/removeMatchSeasonPlayer',
    method: 'post',
    data: data,
    headers
  })
}

// 删除比赛人员 -- 添加比赛id
export function removeSeasonIdPlayer(data) {
  return request({
    url: '/match/bigMatchSeasonTeamPlayerRelation/removeSeasonIdPlayer',
    method: 'post',
    data: data,
    headers
  })
}

// 导出比赛战队信息
export function exportSeasonTeam(data) {
  return request({
    url: '/match/bigMatchSeasonTeamPlayerRelation/exportSeasonTeam',
    method: 'post',
    headers,
    data,
    responseType: 'blob'
  })
}

// 导出比赛人员信息
export function exportSeasonPlayer(data) {
  return request({
    url: '/match/bigMatchSeasonTeamPlayerRelation/exportSeasonPlayer',
    method: 'post',
    headers,
    data,
    responseType: 'blob'
  })
}

// 查询人员列表
export function queryByTeamAndBigMatchSeason(data) {
  return request({
    url: '/match/bigMatchSeasonTeamPlayerRelation/queryByTeamAndBigMatchSeason',
    method: 'post',
    data: data,
    headers
  })
}

// 获取已添加选手id
export function queryPlayerByBigMatchSeason(query) {
  return request({
    url: '/match/bigMatchSeasonTeamPlayerRelation/queryPlayerByBigMatchSeason',
    method: 'get',
    params: query
  })
}

// 比赛人员启用/禁用
export function changeSeasonPlayerStatus(data) {
  return request({
    url: '/match/bigMatchSeasonTeamPlayerRelation/changeSeasonPlayerStatus',
    method: 'post',
    data: data,
    headers
  })
}

// 比赛战队启用/禁用
export function changeSeasonTeamStatus(data) {
  return request({
    url: '/match/bigMatchSeasonTeamPlayerRelation/changeSeasonTeamStatus',
    method: 'post',
    data: data,
    headers
  })
}

// 赛事人员管理切换战队队长
export function changeCaptain(data) {
  return request({
    url: '/match/bigMatchTeamPlayerRelation/changeCaptain',
    method: 'post',
    data: data,
    headers
  })
}

// 赛事人员管理获取队长信息
export function getCaptainByTeamAndBigMatch(data) {
  return request({
    url: '/match/bigMatchTeamPlayerRelation/getCaptainByTeamAndBigMatch',
    method: 'post',
    data: data,
    headers
  })
}

// 赛事人员管理战队添加人员
export function addTeamPlayer(data) {
  return request({
    url: '/match/bigMatchTeamPlayerRelation/addTeamPlayer',
    method: 'post',
    data: data,
    headers
  })
}

// 赛事人员管理战队移除人员
export function removeTeamPlayer(data) {
  return request({
    url: '/match/bigMatchTeamPlayerRelation/removeTeamPlayer',
    method: 'post',
    data: data,
    headers
  })
}
