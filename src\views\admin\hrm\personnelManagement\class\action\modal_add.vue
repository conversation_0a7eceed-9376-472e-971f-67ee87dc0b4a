<template>
  <div v-loading="loading" class="dialog-wrap">
    <el-form ref="form" :model="formData" :rules="rules" label-position="left" label-width="80px">
      <el-form-item label="名称" prop="className">
        <el-input v-model.trim="formData.className" show-word-limit/>
      </el-form-item>
      <el-form-item label="专业" prop="majorCode">
        <el-select v-model="formData.majorCode" style="width: 100%;" no-data-text="无数据，请联系管理员创建专业" clearable filterable placeholder="请选择">
          <el-option
            v-for="item in majorTreeData"
            :key="item.majorCode"
            :label="item.majorName"
            :value="item.majorCode"/>
        </el-select>
      </el-form-item>
      <el-form-item label="助教" prop="assistantTeacherId">
        <el-select v-model="formData.assistantTeacherId" style="width: 100%;" clearable filterable placeholder="请选择">
          <el-option
            v-for="item in assistantList"
            :key="item.userId"
            :label="item.realname"
            :value="item.userId"/>
        </el-select>
      </el-form-item>
    </el-form>
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>
<script>
import module from '../config.js'
import validate from '@/packages/validate'
import { saveClass, updateClass, queryAllAssistantTeacher, queryMajor } from '@/api/admin/training/student'
export default {
  components: {},
  mixins: [],
  props: {
    // 传入数据
    data: {
      type: Array,
      default: () => {
        return []
      }
    },
    isEdit: Boolean,
    majorCode: {
      type: Number
    }
  },
  data() {
    return {
      moduleName: module.name,
      loading: false,
      validate: validate,
      formData: {
        className: '',
        assistantTeacherId: '',
        majorCode: ''

      },
      rules: {
        assistantTeacherId: [validate.required()],
        className: [validate.required(), validate.base_name],
        majorCode: [validate.required()]
      },
      apiType: saveClass,
      assistantList: [],
      majorTreeData: []
    }
  },
  computed: {
    activeItem: function() {
      return this.data[0]
    }
  },
  created() {
    this.initData()
  },
  methods: {
    async initData() {
      const res = await queryAllAssistantTeacher()
      this.assistantList = res.data
      const resMajor = await queryMajor()
      this.majorTreeData = resMajor.data
      this.addMajorId = resMajor.data[0].id
      this.$emit('setClassCode', resMajor.data[0].majorCode, resMajor.data[0].majorName)
      if (this.isEdit) {
        this.formData['className'] = this.activeItem['majorName']
        this.formData['majorCode'] = this['majorCode'] // 回显专业
        this.formData['assistantTeacherId'] = this.activeItem['assistantTeacherId']
      } else {
        this.formData.className = ''
        this.formData['majorCode'] = this['majorCode'] // 新增需要专业id
        this.formData.assistantTeacherId = ''
        this.ApiType = saveClass
        this.$nextTick(() => {
          this.$refs['form'].clearValidate()
        })
      }
    },
    close: function() {
      this.$emit('close')
    },
    confirm: function() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.loading = true
          const postData = JSON.parse(JSON.stringify(this.formData))
          postData.majorName = this.majorTreeData.filter(item => item.majorCode == this.formData.majorCode)[0].majorName
          if (this.isEdit) {
            this.apiType = updateClass
            this.formData.id = this.activeItem.id
            postData.majorCode = this.activeItem['majorCode'] // 班级code
            postData.newMajorParentCode = this.formData.majorCode
          }
          this.apiType(postData).then(res => {
            this.$message.success(`${this.isEdit ? '编辑班级' : '添加班级'}成功`)
            this.$emit('call', 'refresh')
            this.close()
          }).catch(() => {
            this.loading = false
          })
        }
      })
    }
  }
}
</script>
