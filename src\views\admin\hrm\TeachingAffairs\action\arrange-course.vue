<template>
  <div v-loading="loading" class="drawer-wrap">
    <div class="drawer-wrap-content">
      <el-form ref="form" :model="formData" :rules="formData.rules" class="form-wrap" label-position="right" label-width="130px">
        <el-form-item label="下发给" prop="resource">
          <el-radio-group v-model="formData.resource">
            <el-radio label="0">班级</el-radio>
            <el-radio v-if="formData.schedulingType !== '项目'" label="1">学员</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="formData.resource == '0'" label="上课班级" prop="selectedClass">
          <el-tag
            v-if="formData.selectedClass"
            :disable-transitions="true"
            closable
            @click="drawerName = 'selectedClass'"
            @close="formData.selectedClass = null">
            {{ formData.selectedClass.name }}
          </el-tag>
          <el-button v-else type="ghost" @click="drawerName = 'selectedClass'">选择班级</el-button>
        </el-form-item>
        <el-form-item v-if="formData.resource == '1'" label="上课学员" prop="selectedStudent">
          <el-tag
            v-for="item in formData.selectedStudent"
            :key="item.userId"
            :disable-transitions="true"
            style="vertical-align:middle;margin-right: 8px;"
            closable
            @click="drawerName = 'selectedStudent'"
            @close="handleCloseStudent(item)">
            {{ item.realname }}
          </el-tag>
          <el-button type="ghost" @click="drawerName = 'selectedStudent'">选择学员</el-button>
        </el-form-item>
        <el-form-item label="上课教师" prop="selectedTeacher">
          <el-tag
            v-if="formData.selectedTeacher"
            :disable-transitions="true"
            closable
            @click="drawerName = 'selectedTeacher'"
            @close="formData.selectedTeacher = null">
            {{ formData.selectedTeacher.realname }}
          </el-tag>
          <el-button v-else type="ghost" @click="drawerName = 'selectedTeacher'">选择教师</el-button>
        </el-form-item>
        <el-form-item v-if="formData.resource == '1'" label="上课助教" prop="selectedHeadTeacher">
          <el-tag
            v-if="formData.selectedHeadTeacher"
            :disable-transitions="true"
            closable
            @click="drawerName = 'selectedHeadTeacher'"
            @close="formData.selectedHeadTeacher = null">
            {{ formData.selectedHeadTeacher.realname }}
          </el-tag>
          <el-button v-else type="ghost" @click="drawerName = 'selectedHeadTeacher'">选择助教</el-button>
        </el-form-item>
        <el-form-item label="上课类型" required>
          <el-radio-group v-model="formData.schedulingType">
            <el-radio label="课程">课程</el-radio>
            <el-radio label="模拟练习">模拟练习</el-radio>
            <el-radio v-if="formData.resource == '0'" label="项目">项目</el-radio>
            <el-radio label="考试">考试</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="formData.schedulingType == '课程'" label="上课课程" prop="select">
          <el-radio-group v-model="formData.select">
            <el-radio label="0">从课程库选择</el-radio>
            <el-radio label="1">从教学方案选择</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="formData.select == '1'" prop="selectedProgram">
          <el-tag
            v-if="formData.selectedProgram && formData.schedulingType == '课程'"
            :disable-transitions="true"
            style="vertical-align:middle"
            closable
            @click="drawerName = 'selectedProgram'"
            @close="formData.selectedProgram = null">
            {{ formData.selectedProgram.name }}
          </el-tag>
          <el-button v-if="formData.select == '1' && formData.schedulingType == '课程'" type="ghost" @click="drawerName = 'selectedProgram'">选择教学方案</el-button>
        </el-form-item>
        <el-form-item
          v-if="formData.select == '0' && formData.schedulingType == '课程'
          || formData.selectedProgram && formData.schedulingType == '课程' " prop="selectedCourse">
          <el-tag
            v-if="formData.selectedCourse && formData.schedulingType == '课程'"
            :disable-transitions="true"
            style="vertical-align:middle"
            closable
            @click="selectedCourseFn"
            @close="formData.selectedCourse = null">
            {{ formData.selectedCourse.name }}
          </el-tag>
          <el-button type="ghost" @click="selectedCourseFn">选择课程</el-button>
        </el-form-item>
        <el-form-item v-if="formData.schedulingType == '模拟练习'" label="考试试卷" prop="selectedExamPaper">
          <el-tag
            v-if="formData.selectedExamPaper"
            :disable-transitions="true"
            style="vertical-align:middle"
            closable
            @click="drawerName = 'selectedExamPaper'"
            @close="formData.selectedExamPaper = null">
            {{ formData.selectedExamPaper[0].examName }}
          </el-tag>
          <el-button type="ghost" @click="drawerName = 'selectedExamPaper'">选择试卷</el-button>
        </el-form-item>
        <el-form-item v-if="formData.schedulingType == '考试'" label="考试名称" prop="selectedExamCenterPaper">
          <el-tag
            v-if="formData.selectedExamCenterPaper"
            :disable-transitions="true"
            closable
            style="vertical-align:middle"
            @click="drawerName = 'selectedExamCenterPaper'"
            @close="formData.selectedExamCenterPaper = null">
            {{ formData.selectedExamCenterPaper[0].name }}
          </el-tag>
          <el-button type="ghost" @click="drawerName = 'selectedExamCenterPaper'">选择考试</el-button>
        </el-form-item>
        <el-form-item v-if="formData.schedulingType == '项目'" label="项目实训" prop="selectedProject">
          <el-tag
            v-if="formData.selectedProject"
            :disable-transitions="true"
            style="vertical-align:middle"
            closable
            @click="drawerName = 'selectedProject'"
            @close="formData.selectedProject = null">
            {{ formData.selectedProject[0].taskName }}
          </el-tag>
          <el-button type="ghost" @click="drawerName = 'selectedProject'">选择项目</el-button>
        </el-form-item>
        <el-form-item v-if="formData.schedulingType == '项目'" label="小组数量" prop="teamNum">
          <el-input-number v-model="formData.teamNum" :min="1" label="小组数量"/>
          <span>班级人数: {{ formData.studentNum || '-' }}</span>
        </el-form-item>
        <el-form-item v-if="formData.schedulingType == '项目'" prop="memberNum">
          <span slot="label">
            最大组员数量
            <el-tooltip class="member-num-popper" placement="bottom-start">
              <span slot="content">
                为保证所有学员都能加入小组, 请按照如下规则进行设置:
                <br >小组数量 * 最大组员数量 ≥ 班级人数
              </span>
              <i class="el-icon-warning-outline"/>
            </el-tooltip>
          </span>
          <el-input-number v-model="formData.memberNum" :min="1" label="最大组员数量"/>
        </el-form-item>
        <div v-if="formData.selectedCourse" ref="tableDom" class="tableData">
          <el-table :data="formData.tableData" :max-height="tableHeight" style="width: 100%">
            <el-table-column prop="name" label="课程内容" show-overflow-tooltip>
              <template slot-scope="scope">
                <el-form-item label-width="0px">
                  <div class="ellipsis">{{ scope.row.name || "-" }}</div>
                  <div v-if="scope.row.checkContentMsg" class="ellipsis">
                    <i class="el-icon-warning-outline"/>{{ scope.row.checkContentMsg }}
                  </div>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column prop="contentPeriod" label="课时" show-overflow-tooltip min-width="60">
              <template slot-scope="scope">
                <el-form-item label-width="0px">
                  <span>{{ scope.row.contentPeriod + '课时' || "-" }}</span>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column prop="date" label="上课日期" min-width="180">
              <template slot-scope="scope">
                <el-form-item :prop="'tableData.' + scope.$index + '.date'" :rules="formData.rules.date" label-width="0px">
                  <el-date-picker
                    v-model="scope.row.date"
                    :picker-options="pickerOptions"
                    type="date"
                    size="mini"
                    prefix-icon="-"
                    value-format="yyyy-MM-dd"
                    placeholder="上课日期"
                    style="width: 100%"
                    @change="sectionTimeChange($event, scope.row)"
                  />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column prop="learning" label="上课开始时间" min-width="100">
              <template slot-scope="scope">
                <el-form-item :prop="'tableData.' + scope.$index + '.learning'" :rules="formData.rules.learning" label-width="0px">
                  <el-time-picker
                    v-model="scope.row.learning"
                    :disabled="!scope.row.date"
                    :append-to-body="false"
                    :picker-options="{
                      selectableRange: `${disabledStartMinTime(scope.row)}:00 - 23:59:00`
                    }"
                    format="HH:mm"
                    value-format="HH:mm"
                    editable
                    size="mini"
                    prefix-icon="-"
                    style="width: 100%"
                    placeholder="开始时间"
                    @change="startTimeChange($event, scope.row, scope.$index)"/>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column prop="endTime" label="上课结束时间" min-width="100">
              <template slot-scope="scope">
                <el-form-item :prop="'tableData.' + scope.$index + '.endTime'" :rules="formData.rules.endTime" label-width="0px">
                  <el-time-picker
                    v-model="scope.row.endTime"
                    :disabled="!scope.row.learning"
                    :append-to-body="false"
                    :picker-options="{
                      selectableRange: `${scope.row.learning || '00:00'}:00 - 23:59:00`
                    }"
                    format="HH:mm"
                    value-format="HH:mm"
                    editable
                    size="mini"
                    prefix-icon="-"
                    style="width: 100%"
                    placeholder="结束时间"
                    @change="endTimeChange($event, scope.row, scope.$index)"/>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column label="" width="40" align="center">
              <template slot-scope="scope">
                <el-form-item label-width="0px">
                  <i v-if="scope.row.iseditor == true" class="el-icon-remove-outline pointer" @click="delRow(scope.row, scope.$index)"/>
                  <i v-else class="el-icon-circle-plus-outline pointer" @click="addRow(scope.row, scope.$index)"/>
                </el-form-item>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div v-if="formData.selectedExamPaper" ref="tableDom" class="tableData">
          <el-table :data="formData.selectedExamPaper" :max-height="tableHeight" style="width: 100%">
            <el-table-column prop="examName" label="试卷名称" show-overflow-tooltip>
              <template slot-scope="scope">
                <el-form-item label-width="0px">
                  <div class="ellipsis">{{ scope.row.examName || "-" }}</div>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column prop="suggestTime" label="建议时长" show-overflow-tooltip width="70">
              <template slot-scope="scope">
                <el-form-item label-width="0px">
                  <span>{{ scope.row.suggestTime + '分钟' || "-" }}</span>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column prop="date" label="上课日期" min-width="180">
              <template slot-scope="scope">
                <el-form-item :prop="'selectedExamPaper.' + scope.$index + '.date'" :rules="formData.rules.date" label-width="0px">
                  <el-date-picker
                    v-model="scope.row.date"
                    :picker-options="pickerOptions"
                    type="date"
                    size="mini"
                    prefix-icon="-"
                    value-format="yyyy-MM-dd"
                    placeholder="上课日期"
                    style="width: 100%"
                    @change="sectionTimeChange($event, scope.row)"
                  />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column prop="learning" label="上课开始时间" min-width="100">
              <template slot-scope="scope">
                <el-form-item :prop="'selectedExamPaper.' + scope.$index + '.learning'" :rules="formData.rules.learning" label-width="0px">
                  <el-time-picker
                    v-model="scope.row.learning"
                    :disabled="!scope.row.date"
                    :append-to-body="false"
                    :picker-options="{
                      selectableRange: `${disabledStartMinTime(scope.row)}:00 - 23:59:00`
                    }"
                    format="HH:mm"
                    value-format="HH:mm"
                    editable
                    size="mini"
                    prefix-icon="-"
                    style="width: 100%"
                    placeholder="开始时间"
                    @change="startTimeChange($event, scope.row, scope.$index)"/>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column prop="endTime" label="上课结束时间" min-width="100">
              <template slot-scope="scope">
                <el-form-item :prop="'selectedExamPaper.' + scope.$index + '.endTime'" :rules="formData.rules.endTime" label-width="0px">
                  <el-time-picker
                    v-model="scope.row.endTime"
                    :disabled="!scope.row.learning"
                    :append-to-body="false"
                    :picker-options="{
                      selectableRange: `${scope.row.learning || '00:00'}:00 - 23:59:00`
                    }"
                    format="HH:mm"
                    value-format="HH:mm"
                    editable
                    size="mini"
                    prefix-icon="-"
                    style="width: 100%"
                    placeholder="结束时间"
                    @change="endTimeChange($event, scope.row, scope.$index)"/>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column label="" width="40" align="center">
              <template slot-scope="scope">
                <el-form-item label-width="0px">
                  <i v-if="scope.row.iseditor == true" class="el-icon-remove-outline pointer" @click="delRow(scope.row, scope.$index)"/>
                  <i v-else class="el-icon-circle-plus-outline pointer" @click="addRow(scope.row, scope.$index)"/>
                </el-form-item>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div v-if="formData.selectedProject" ref="tableDom" class="tableData">
          <el-table :data="formData.selectedProject" :max-height="tableHeight" style="width: 100%">
            <el-table-column prop="taskName" label="项目名称" show-overflow-tooltip>
              <template slot-scope="scope">
                <el-form-item label-width="0px">
                  <div class="ellipsis">{{ scope.row.taskName || "-" }}</div>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column prop="classHour" label="课时" show-overflow-tooltip min-width="60">
              <template slot-scope="scope">
                <el-form-item label-width="0px">
                  <span>{{ scope.row.classHour + '课时' || "-" }}</span>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column prop="date" label="上课日期" min-width="180">
              <template slot-scope="scope">
                <el-form-item :prop="'selectedProject.' + scope.$index + '.date'" :rules="formData.rules.date" label-width="0px">
                  <el-date-picker
                    v-model="scope.row.date"
                    :picker-options="pickerOptions"
                    type="date"
                    size="mini"
                    prefix-icon="-"
                    value-format="yyyy-MM-dd"
                    placeholder="上课日期"
                    style="width: 100%"
                    @change="sectionTimeChange($event, scope.row)"
                  />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column prop="learning" label="上课开始时间" min-width="100">
              <template slot-scope="scope">
                <el-form-item :prop="'selectedProject.' + scope.$index + '.learning'" :rules="formData.rules.learning" label-width="0px">
                  <el-time-picker
                    v-model="scope.row.learning"
                    :disabled="!scope.row.date"
                    :append-to-body="false"
                    :picker-options="{
                      selectableRange: `${disabledStartMinTime(scope.row)}:00 - 23:59:00`
                    }"
                    format="HH:mm"
                    value-format="HH:mm"
                    editable
                    size="mini"
                    prefix-icon="-"
                    style="width: 100%"
                    placeholder="开始时间"
                    @change="startTimeChange($event, scope.row, scope.$index)"/>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column prop="endTime" label="上课结束时间" min-width="100">
              <template slot-scope="scope">
                <el-form-item :prop="'selectedProject.' + scope.$index + '.endTime'" :rules="formData.rules.endTime" label-width="0px">
                  <el-time-picker
                    v-model="scope.row.endTime"
                    :disabled="!scope.row.learning"
                    :append-to-body="false"
                    :picker-options="{
                      selectableRange: `${scope.row.learning || '00:00'}:00 - 23:59:00`
                    }"
                    format="HH:mm"
                    value-format="HH:mm"
                    editable
                    size="mini"
                    prefix-icon="-"
                    style="width: 100%"
                    placeholder="结束时间"
                    @change="endTimeChange($event, scope.row, scope.$index)"/>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column label="" width="40" align="center">
              <template slot-scope="scope">
                <el-form-item label-width="0px">
                  <i v-if="scope.row.iseditor == true" class="el-icon-remove-outline pointer" @click="delRow(scope.row, scope.$index)"/>
                  <i v-else class="el-icon-circle-plus-outline pointer" @click="addRow(scope.row, scope.$index)"/>
                </el-form-item>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div v-if="formData.selectedExamCenterPaper" ref="tableDom" class="tableData">
          <el-table :data="formData.selectedExamCenterPaper" :max-height="tableHeight" style="width: 100%">
            <el-table-column prop="name" label="考试名称" show-overflow-tooltip>
              <template slot-scope="scope">
                <div class="ellipsis">{{ scope.row.name || "-" }}</div>
              </template>
            </el-table-column>
            <el-table-column prop="distanceTime" label="时长">
              <template slot-scope="scope">
                <span>{{ scope.row.distanceTime || "-" }}</span>
              </template>
            </el-table-column>
            <el-table-column label-width="0px" prop="beginTime" label="考试开始时间">
              <template slot-scope="scope">
                <span>{{ scope.row.beginTime || "-" }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="endTime" label="考试结束时间">
              <template slot-scope="scope">
                <span>{{ scope.row.endTime || "-" }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-form>

      <!-- 侧拉弹窗 start -->
      <el-drawer
        :title="titleMapping[drawerName]"
        :visible.sync="drawerShow"
        :size="drawerWidth"
        append-to-body
        @close="drawerClose"
      >
        <transition name="el-fade-in-linear">
          <component
            :is="drawerName"
            :name="drawerName"
            :data="programId"
            :class-code="classId"
            @close="drawerClose"
            @call="drawerConfirmCall"
          />
        </transition>
      </el-drawer>
      <!-- 侧拉弹窗 end -->
    </div>
    <div class="drawer-footer">
      <el-button :loading="submitLoading" type="primary" @click="confirm">确定</el-button>
      <el-button type="text" @click="close">取消</el-button>
    </div>
  </div>
</template>
<script>
import modules from '../../config'
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
import validate from '@/packages/validate'
import selectedTeacher from './select-teacher.vue'
import selectedHeadTeacher from './select-headTeacher.vue'
import selectedStudent from './select-student.vue'
import selectedClass from './select-class.vue'
import selectedCourse from './select-course.vue'
import selectedExamPaper from './select-exam-paper.vue'
import selectedExamCenterPaper from './select-exam-center-paper.vue'
import selectedProject from './select-project.vue'
import selectedProgram from './select-program.vue'
import { getTimeItems } from '@/views/admin/hrm/TeachingAffairs/utils.js'
import moment from 'moment'

import {
  insertTeacherScheduling,
  studentCourseSave,
  getStudentNum,
  checkContentAPI,
  insertClassSchedule
} from '@/api/teachingAffairs/index.js'
import { findContentByCourseIdAndChapterUnit, lessonPlanDetailQuery } from '@/api/teachingAffairs/index.js'

export default {
  components: {
    selectedTeacher,
    selectedHeadTeacher,
    selectedStudent,
    selectedClass,
    selectedCourse,
    selectedExamPaper,
    selectedProject,
    selectedProgram,
    selectedExamCenterPaper
  },
  mixins: [mixinsActionMenu],
  props: {
    // 传入数据
    data: {
      type: Array,
      default: () => {
        return []
      }
    },
    name: {
      type: String
    }
  },
  data() {
    const validateCourseDate = (rule, value, callback) => {
      const currentIndex = rule.field.split('.')[1]
      let withTip = false // 存在提示代表被排课
      if (this.isCourse) {
        withTip = !!this.formData.tableData[currentIndex].checkContentMsg
      } else if (this.isExamPaper) {
        withTip = !!this.formData.selectedExamPaper[currentIndex].checkContentMsg
      } else if (this.isProject) {
        withTip = !!this.formData.selectedProject[currentIndex].checkContentMsg
      }
      if (!value) {
        if (withTip) {
          callback()
        } else {
          callback(new Error('必填项'))
        }
      } else {
        callback()
      }
    }
    const validateCourseTime = (rule, value, callback) => {
      // 当前课程下标，判断是否时间重复
      const currentIndex = rule.field.split('.')[1]
      let withTip = false // 存在提示代表被排课
      if (this.isCourse) {
        withTip = !!this.formData.tableData[currentIndex].checkContentMsg
      } else if (this.isExamPaper) {
        withTip = !!this.formData.selectedExamPaper[currentIndex].checkContentMsg
      } else if (this.isProject) {
        withTip = !!this.formData.selectedProject[currentIndex].checkContentMsg
      }
      // 未填写排课日期不进行校验
      if (withTip) {
        callback()
        return
      }

      let completeSum = 0 // 开始和结束时间都填写完成的课程数量
      this.formData.tableData.map((each, i) => {
        if (each.learning && each.endTime) {
          completeSum += 1
        }
      })
      if (completeSum > 0 && this.formData.tableData.length > 1 && this.formData.tableData[1].learning) {
        // 两门及以上课程
        let currentLearning = 0
        let currentEndTime = 0
        if (currentIndex && this.formData.tableData[currentIndex].learning) {
          currentLearning = this.dateTimeToTimestamp(`${this.formData.tableData[currentIndex].date} ${this.formData.tableData[currentIndex].learning}`)
        }
        if (currentIndex && this.formData.tableData[currentIndex].endTime) {
          currentEndTime = this.dateTimeToTimestamp(`${this.formData.tableData[currentIndex].date} ${this.formData.tableData[currentIndex].endTime}`)
        }
        this.formData.tableData.some((item, index) => {
          if (currentIndex != index) {
            let learning = 0
            let endTime = 0
            if (item.learning) {
              learning = this.dateTimeToTimestamp(`${item.date} ${item.learning}`)
            }
            if (item.endTime) {
              endTime = this.dateTimeToTimestamp(`${item.date} ${item.endTime}`)
            }
            // 上课时间重复
            if ((currentLearning && currentLearning >= learning && currentLearning <= endTime) ||
              (currentEndTime && currentEndTime >= learning && currentEndTime <= endTime) ||
              (currentLearning && currentEndTime && currentLearning <= learning && currentEndTime >= endTime)) {
              callback(new Error('上课时间重复'))
            } else {
              if (!value) {
                callback(new Error('必填项'))
              } else {
                callback()
              }
            }
          }
        })
      } else { // 只有一个内容时
        if (!value) {
          callback(new Error('必填项'))
        } else {
          callback()
        }
      }
    }
    return {
      drawerAction: ['selectedTeacher', 'selectedHeadTeacher', 'selectedStudent', 'selectedClass', 'selectedCourse', 'selectedExamPaper', 'selectedProject', 'selectedProgram'], // 需要侧拉打开的操作
      titleMapping: {
        'selectedTeacher': '选择教师',
        'selectedHeadTeacher': '选择助教',
        'selectedStudent': '选择学员',
        'selectedClass': '选择班级',
        'selectedCourse': '选择课程',
        'selectedExamPaper': '选择试卷',
        'selectedProject': '选择项目',
        'selectedProgram': '选择教学方案',
        'selectedExamCenterPaper': '选择考试'
      },
      loading: false,
      submitLoading: false,
      validate: validate,
      formData: {
        schedulingType: '课程',
        resource: '0',
        selectedClass: '',
        selectedStudent: '',
        selectedTeacher: '',
        selectedHeadTeacher: '',
        selectedCourse: '',
        selectedExamPaper: '',
        selectedExamCenterPaper: '',
        selectedProject: '',
        selectedProgram: '',
        select: '0',
        teamNum: 1,
        memberNum: 1,
        studentNum: 0,
        tableData: [],
        rules: {
          date: [{ required: true, validator: validateCourseDate, trigger: 'blur' }],
          learning: [{ required: true, validator: validateCourseTime, trigger: 'blur' }],
          endTime: [{ required: true, validator: validateCourseTime, trigger: 'blur' }],
          resource: [validate.required(['blur', 'change'])],
          selectedClass: [validate.required(['blur', 'change'])],
          selectedStudent: [validate.required(['blur', 'change'])],
          selectedTeacher: [validate.required(['blur', 'change'])],
          selectedHeadTeacher: [validate.required(['blur', 'change'])],
          selectedCourse: [validate.required(['blur', 'change'])],
          selectedExamPaper: [validate.required(['blur', 'change'])],
          selectedExamCenterPaper: [validate.required(['blur', 'change'])],
          selectedProject: [validate.required(['blur', 'change'])],
          selectedProgram: [validate.required(['blur', 'change'])],
          select: [validate.required(['blur', 'change'])],
          tableData: [validate.required(['blur', 'change'])],
          teamNum: [validate.required(['blur', 'change'])],
          memberNum: [validate.required(['blur', 'change'])]
        }
      },
      disabledDate(time) {
        return time.getTime() > Date.now()
      },
      timeList: modules.timeList,
      timeList1: modules.timeList1,
      timeSection: getTimeItems().map(item => item.name),
      timeSection1: getTimeItems().map(item => item.name),
      timeDisList: [],
      timeDisList1: [],
      programId: '',
      classId: '',
      timeStep: '00:15', // 时刻间隔
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() <= (Date.now() - 24 * 60 * 60 * 1000)
        }
      },
      disList: [],
      requestList: [],
      filterList: [],
      students: [],
      tableHeight: 'auto'
    }
  },
  computed: {
    // 是课程
    isCourse: function() {
      return this.formData.schedulingType == '课程'
    },
    // 是模拟练习
    isExamPaper: function() {
      return this.formData.schedulingType == '模拟练习'
    },
    // 是项目
    isProject: function() {
      return this.formData.schedulingType == '项目'
    },
    // 是考试
    isExamCenterPaper: function() {
      return this.formData.schedulingType == '考试'
    }
  },
  watch: {
    'formData.schedulingType'(newVal, oldVal) {
      this.formData.selectedCourse = ''
      this.formData.selectedProgram = ''
      this.formData.selectedProject = ''
      this.formData.selectedExamPaper = ''
      this.formData.selectedExamCenterPaper = ''
      this.formData.tableData = []
      this.requestList = []
      this.$refs['form'].clearValidate(['selectedCourse', 'selectedProgram', 'selectedProject', 'selectedExamPaper', 'selectedExamCenterPaper'])
      if (newVal === '项目') {
        this.formData.resource = '0'
      }
    },
    'formData.select'(newVal, oldVal) {
      this.formData.selectedCourse = ''
      this.formData.selectedProgram = ''
    },
    'formData.resource'(newVal, oldVal) {
      this.formData.selectedClass = ''
      this.formData.selectedStudent = ''
    },
    'formData.selectedClass'(newVal, oldVal) {
      if (this.formData.select == '1') {
        this.formData.selectedCourse = ''
        this.classId = ''
      }
    },
    'formData.selectedStudent'(newVal, oldVal) {
      if (this.formData.select == '1') {
        this.classId = ''
      }
    },
    // 监听是否满足条件再调用检测接口
    'formData': {
      handler: function(newVal, oldVal) {
        // 选择了班级/学员
        const selectedClassOrStudent = (this.formData.selectedClass && this.formData.selectedClass.majorCode) || (this.formData.selectedStudent && this.formData.selectedStudent.length > 0)
        if (selectedClassOrStudent) {
          // 是课程类型且课程内容不为空
          if (this.formData.schedulingType == '课程' && this.formData.tableData.length > 0) {
            this.checkContent()
          }
        } else {
          // 不选择班级/学员时，不显示提示信息
          this.formData.tableData.forEach(row => {
            this.$set(row, 'checkContentMsg', null)
          })
        }
      },
      deep: true,
      immediate: true
    }
  },
  mounted() {
    // 设置上课时间
    this.timeList = getTimeItems()
    this.timeList1 = getTimeItems()
    // 组织下拉数据
    this.timeDisList = JSON.parse(JSON.stringify(this.timeList))
    this.timeDisList1 = JSON.parse(JSON.stringify(this.timeList1))
  },
  destroyed() {
    window.onresize = null
  },
  methods: {
    // table自动高度
    returnTableHeight(ref, bottomOffset) {
      let height = null
      height = window.innerHeight - ref.getBoundingClientRect().top - bottomOffset
      return height
    },
    resizeTable() {
      // this.tableHeight = this.returnTableHeight(this.$refs.tableDom, 100)
    },
    delRow(row, idx) {
      if (this.formData.schedulingType === '课程') {
        this.formData.tableData.splice(idx, 1)
        this.formData.tableData.forEach((item, index) => {
          this.$refs['form'].clearValidate(`tableData.${index + 1}.date`)
          this.$refs['form'].clearValidate(`tableData.${index + 1}.learning`)
        })
      }
      if (this.formData.schedulingType === '模拟练习') {
        this.formData.selectedExamPaper.splice(idx, 1)
        this.formData.selectedExamPaper.forEach((item, index) => {
          this.$refs['form'].clearValidate(`selectedExamPaper.${index + 1}.date`)
          this.$refs['form'].clearValidate(`selectedExamPaper.${index + 1}.learning`)
        })
      }
      if (this.formData.schedulingType === '项目') {
        this.formData.selectedProject.splice(idx, 1)
        this.formData.selectedProject.forEach((item, index) => {
          this.$refs['form'].clearValidate(`selectedProject.${index + 1}.date`)
          this.$refs['form'].clearValidate(`selectedProject.${index + 1}.learning`)
        })
      }
    },
    // 新增行
    addRow(row, idx) {
      const rows = {
        ...row,
        date: '',
        learning: '',
        endTime: '',
        iseditor: true
      }
      if (this.formData.schedulingType === '课程') {
        this.formData.tableData.splice(idx + 1, 0, rows)
        this.formData.tableData.forEach((item, index) => {
          this.$refs['form'].clearValidate(`tableData.${index + 1}.date`)
          this.$refs['form'].clearValidate(`tableData.${index + 1}.learning`)
        })
      }
      if (this.formData.schedulingType === '模拟练习') {
        this.formData.selectedExamPaper.splice(idx + 1, 0, rows)
        this.formData.selectedExamPaper.forEach((item, index) => {
          this.$refs['form'].clearValidate(`selectedExamPaper.${index + 1}.date`)
          this.$refs['form'].clearValidate(`selectedExamPaper.${index + 1}.learning`)
        })
      }
      if (this.formData.schedulingType === '项目') {
        this.formData.selectedProject.splice(idx + 1, 0, rows)
        this.formData.selectedProject.forEach((item, index) => {
          this.$refs['form'].clearValidate(`selectedProject.${index + 1}.date`)
          this.$refs['form'].clearValidate(`selectedProject.${index + 1}.learning`)
        })
      }
    },
    // 时间格式为 "YYYY-MM-DD HH:MM"转化为时间戳
    dateTimeToTimestamp(dateTimeStr) {
      const dateObj = new Date(dateTimeStr)
      const timestamp = dateObj.getTime()
      return timestamp
    },
    getClassNum() {
      getStudentNum({ classCode: this.formData.selectedClass.majorCode }).then(res => {
        this.formData.studentNum = res.data.num
      })
    },
    // 检测课程内容是否被排过
    checkContent() {
      let postList = []
      const requestList = []
      if (this.formData.tableData && this.formData.tableData.length > 0) {
        this.formData.tableData.forEach(item => {
          // if (!item.learning) return
          if (this.formData.resource === '1') {
            requestList.push({
              planType: this.formData.resource,
              classCode: this.formData.selectedClass.majorCode,
              courseCode: this.formData.selectedCourse.id,
              curriculumCode: item.id,
              schedulingType: this.formData.schedulingType
            })
          } else {
            requestList.push({
              planType: this.formData.resource,
              majorName: this.formData.selectedClass.name,
              classCode: this.formData.selectedClass.majorCode,
              courseCode: this.formData.selectedCourse.id,
              curriculumCode: item.id,
              schedulingType: this.formData.schedulingType
            })
          }
        })
        if (this.formData.schedulingType === '课程' && this.formData.resource === '1') {
          const studentIdArr = this.formData.selectedStudent.map(item => {
            return { id: item.userId }
          })
          const arr = []
          for (let j = 0; j < studentIdArr.length; j++) {
            for (let i = 0; i < requestList.length; i++) {
              arr.push({
                ...requestList[i],
                classCode: studentIdArr[j].id,
                realName: this.formData.selectedStudent[j].realname
              })
            }
          }
          postList = arr
        } else if (this.formData.schedulingType === '课程' && this.formData.resource === '0') {
          postList = requestList
        }
        checkContentAPI(postList).then(res => {
          if (res.code == 0 && res.data.length > 0) {
            res.data.forEach(item => {
              // 查询被排过的内容
              const row = this.formData.tableData.find(content => content.id == item.id)
              this.$set(row, 'checkContentMsg', item.checkContentMsg)
            })
          } else {
            this.formData.tableData.forEach(row => {
              this.$set(row, 'checkContentMsg', null)
            })
          }
        })
      }
    },
    // 删除某个学员
    handleCloseStudent(item) {
      const idx = this.formData.selectedStudent.findIndex(v => v.userId == item.userId)
      idx > -1 && this.formData.selectedStudent.splice(idx, 1)
    },
    selectedCourseFn() {
      this.programId = this.formData.selectedProgram.id
      if (this.formData.select === '1' && this.formData.resource === '0') {
        if (this.formData.selectedClass == '') {
          this.$message.error('请先选择排课班级')
        } else {
          this.drawerName = 'selectedCourse'
          this.classId = this.formData.selectedClass.majorCode ? this.formData.selectedClass.majorCode : this.formData.selectedStudent.userId
        }
      } else {
        this.drawerName = 'selectedCourse'
      }
    },
    // 校验排课日期时间表单
    validateDateTimeFields() {
      // 获取排课日期时间 prop
      const getDateTimeFormProps = (index) => [
        `tableData.${index}.date`,
        `tableData.${index}.learning`,
        `tableData.${index}.endTime`
      ]
      if (this.isCourse) {
        this.formData.tableData.forEach((item, index) => {
          const fields = getDateTimeFormProps(index)
          this.$refs['form'].clearValidate(fields)
        })
      }
      if (this.isExamPaper) {
        this.formData.selectedExamPaper.forEach((item, index) => {
          const fields = getDateTimeFormProps(index)
          this.$refs['form'].clearValidate(fields)
        })
      }
      if (this.isProject) {
        this.formData.selectedProject.forEach((item, index) => {
          const fields = getDateTimeFormProps(index)
          this.$refs['form'].clearValidate(fields)
        })
      }
    },
    // 上课日期改变
    sectionTimeChange(date, row) {
      // 清空日期时，上课的起始时间应也被清空
      if (!date) {
        this.$set(row, 'learning', null)
        this.$set(row, 'endTime', null)
      } else {
        const time = row.learning || row.endTime
        if (date && time) {
          const selectTime = this.getFullDateTime(date, time)
          const isBefore = moment(selectTime).isBefore(new Date())
          // 选中的上课时间在当前时间之前时，清空开始时间、结束时间，重新选择
          if (isBefore) {
            this.$set(row, 'learning', null)
            this.$set(row, 'endTime', null)
          }
        }
      }
      this.validateDateTimeFields()
    },
    // 禁用开始时间选项
    disabledStartMinTime(row) {
      if (!row.date) {
        return '00:00' // 未选择日期时，返回00:00，避免时间选择组件报错
      }
      const isAfterStart = moment(row.date).isAfter(new Date(), 'days')
      if (isAfterStart) {
        return '00:00'
      } else {
        const { hours, minutes } = moment(new Date()).toObject()
        const timeStr = String(hours).padStart('0', 2) + ':' + String(minutes).padStart('0', 2)
        return timeStr
      }
    },
    // 禁用开始时间-最大时间选项
    disabledStartMaxTime(row) {
      if (row.endTime) {
        return row.endTime
      } else {
        return null
      }
    },
    // 开始时间改变回调
    startTimeChange(learning, row, idx) {
      // 开始时间清空，同时也清空结束时间
      if (!learning && row.endTime) {
        this.$set(row, 'endTime', null)
      } else if (row.date && learning && row.endTime) { // 开始时间在结束时间之后，清空结束时间重新选择
        const start = this.getFullDateTime(row.date, learning)
        const end = this.getFullDateTime(row.date, row.endTime)
        if (start >= end) {
          if (row.endTime) {
            this.$set(row, 'endTime', null)
          }
        }
      }
      this.validateDateTimeFields()
    },
    // 结束时间改变回调
    endTimeChange(learning, row, idx) {
      this.validateDateTimeFields()
    },
    // 获取上课完整时间 YYYYY-MM-DD HH:mm:ss
    getFullDateTime(date, time) {
      return `${date} ${time}:00`
    },
    // 确定上课日期
    lessonsChange(event, row) {
      this.timeDisList = JSON.parse(JSON.stringify(this.timeList))
      this.timeDisList1 = JSON.parse(JSON.stringify(this.timeList1))
      if (row) {
        const currentTiem = new Date().getTime()
        if (this.formData.schedulingType === '课程') {
          this.formData.tableData.forEach(item => {
            if (item.date && item.learning) {
              const selectTime = new Date(item.date + ' ' + item.learning + ':00').getTime()
              if (currentTiem > selectTime) {
                item.learning = []
                item.endTime = []
              }
            }
          })
        } else if (this.formData.schedulingType === '模拟练习') {
          this.formData.selectedExamPaper.forEach(item => {
            if (item.date && item.learning) {
              const selectTime = new Date(item.date + ' ' + item.learning + ':00').getTime()
              if (currentTiem > selectTime) {
                item.learning = []
                item.endTime = []
              }
            }
          })
        } else if (this.formData.schedulingType === '项目') {
          this.formData.selectedProject.forEach(item => {
            if (item.date && item.learning) {
              const selectTime = new Date(item.date + ' ' + item.learning + ':00').getTime()
              if (currentTiem > selectTime) {
                item.learning = []
                item.endTime = []
              }
            }
          })
        }

        const choice = new Date(row.date)
        const now = new Date()
        const hours = now.getHours()
        const min = now.getMinutes()
        const formattedHours = hours < 10 ? `0${hours}` : hours.toString()
        const formattedMin = min < 10 ? `0${min}` : min.toString()
        if (choice.getFullYear() == now.getFullYear() && choice.getMonth() == now.getMonth() && choice.getDate() == now.getDate()) {
          this.timeDisList.forEach(item => {
            if (formattedHours > item.hour || formattedHours == item.hour && formattedMin >= item.min) {
              item.disabled = true
            }
          })
          this.timeDisList = this.timeDisList.filter(item => {
            return item.disabled === false
          })
        } else {
          this.timeDisList.forEach(item => {
            item.disabled = false
          })
        }
        row.disabled = []
      }
    },
    // 班级排课Fn
    insertClassCourse(data) {
      if (!data.list.length) {
        this.submitLoading = false
        return this.$message.warning(`请填写至少一个以上课程内容的排课日期和时间`)
      }
      insertTeacherScheduling(data).then(res => {
        if (res.code === 0) {
          this.$message({
            message: res.data,
            type: 'success'
          })
          this.close()
          this.$emit('call', 'refresh')
        }
        this.tableLoading = false
      }).catch(() => {
        this.submitLoading = false
      })
    },
    // 学员排课Fn
    insertStudentCourse(data) {
      if (!data.list.length) {
        this.submitLoading = false
        return this.$message.warning(`请填写至少一个以上课程内容的排课日期和时间`)
      }
      studentCourseSave(data).then(res => {
        if (res.code === 0) {
          this.$message({
            message: res.data,
            type: 'success'
          })
          this.close()
          this.$emit('call', 'refresh')
        }
        this.tableLoading = false
      }).catch(() => {
        this.submitLoading = false
      })
    },
    insertExamCenterPaper(data) {
      if (!data.list.length) {
        this.submitLoading = false
        return this.$message.warning(`请填写至少一个以上课程内容的排课日期和时间`)
      }
      insertClassSchedule(data).then(res => {
        if (res.code === 0) {
          this.$message({
            message: res.data,
            type: 'success'
          })
          this.close()
          this.$emit('call', 'refresh')
        }
        this.tableLoading = false
      }).catch(() => {
        this.submitLoading = false
      })
    },
    // 根据教案id查询课程
    getClassDetail() {
      const data = {
        lessonPlanId: this.formData.selectedProgram.id,
        pageNum: 1,
        pageSize: 10
      }
      lessonPlanDetailQuery(data).then(res => {
        this.formData.tableData = res.data.records
      }).catch(() => {})
    },
    // 根据课程id查询内容
    getCourseDetail() {
      const data = {
        courseId: this.formData.selectedCourse.id,
        pageNum: 1,
        pageSize: 1000
      }
      findContentByCourseIdAndChapterUnit(data).then(res => {
        this.formData.tableData = res.data.map(item => {
          return {
            ...item,
            checkContentMsg: null
          }
        })
      }).catch(() => {
      })
    },
    // 数组对象去重
    arrayUnique(arr, name) {
      var hash = {}
      return arr.reduce(function(acc, cru, index) {
        if (!hash[cru[name]]) {
          hash[cru[name]] = { index: index }
          acc.push(cru)
        } else {
          acc.splice(hash[cru[name]]['index'], 1, cru)
        }
        return acc
      }, [])
    },
    // 侧拉选择资源回调
    drawerConfirmCall: function(type, data) {
      if (type === 'close') {
        this.drawerClose()
      } else if (type === 'confirm_teacher') {
        this.formData.selectedTeacher = data[0]
        this.drawerClose()
        this.$nextTick(() => {
          this.$refs['form'].clearValidate('selectedTeacher')
        })
      } else if (type === 'confirm_headTeacher') {
        this.formData.selectedHeadTeacher = data[0]
        this.drawerClose()
        this.$nextTick(() => {
          this.$refs['form'].clearValidate('selectedHeadTeacher')
        })
      } else if (type === 'confirm_student') {
        let studentData = []
        this.students.length > 0 ? studentData = this.arrayUnique([...this.students, ...data], 'userId') : studentData = data
        this.formData.selectedStudent = studentData
        this.students = studentData

        this.drawerClose()
        this.$nextTick(() => {
          this.$refs['form'].clearValidate('selectedStudent')
        })
      } else if (type === 'confirm_class') {
        this.formData.selectedClass = data[0]
        this.drawerClose()
        this.getClassNum()
        this.$nextTick(() => {
          this.$refs['form'].clearValidate('selectedClass')
        })
      } else if (type === 'confirm_course') {
        this.formData.selectedCourse = data[0]
        this.getCourseDetail()
        this.drawerClose()
        this.$nextTick(() => {
          this.$refs['form'].clearValidate('selectedCourse')
          this.resizeTable()
          window.onresize = function() {
            this.resizeTable()
          }
        })
      } else if (type === 'confirm_examPaper') {
        this.formData.selectedExamPaper = data
        this.drawerClose()
        this.$nextTick(() => {
          this.$refs['form'].clearValidate('selectedExamPaper')
          this.resizeTable()
          window.onresize = function() {
            this.resizeTable()
          }
        })
      } else if (type === 'confirm_project') {
        this.formData.selectedProject = data
        this.drawerClose()
        this.$nextTick(() => {
          this.$refs['form'].clearValidate('selectedProject')
          this.resizeTable()
          window.onresize = function() {
            this.resizeTable()
          }
        })
      } else if (type === 'confirm_program') {
        this.formData.selectedProgram = data[0]
        this.drawerClose()
        this.$nextTick(() => {
          this.$refs['form'].clearValidate('selectedProgram')
        })
      } else if (type === 'confirm_examCenterPaper') {
        this.formData.selectedExamCenterPaper = data
        this.drawerClose()
        this.$nextTick(() => {
          this.$refs['form'].clearValidate('selectedExamCenterPaper')
          this.resizeTable()
          window.onresize = function() {
            this.resizeTable()
          }
        })
      }
    },
    close: function() {
      this.$emit('close')
    },
    confirm: function() {
      this.$refs['form'].validate((valid, object) => {
        if (valid) {
          // 排课类型为课程时，判断内容为空则提示
          if (this.formData.schedulingType == '课程' && (!this.formData.tableData || this.formData.tableData.length == 0)) {
            this.$message.error('请添加课程内容')
            return
          }
          this.submitLoading = true
          this.requestList = []
          if (this.formData.tableData && this.formData.tableData.length > 0) {
            this.formData.tableData.forEach(item => {
              if (!item.learning) return
              if (this.formData.resource === '1') {
                this.requestList.push({
                  planCode: this.formData.selectedProgram.id,
                  sectionSeason: `${item.learning}-${item.endTime}`,
                  sectionTime: item.date,
                  classCode: this.formData.selectedCourse.id,
                  courseCode: this.formData.selectedCourse.id,
                  curriculumCode: item.id,
                  schedulingType: this.formData.schedulingType,
                  teacherName: this.formData.selectedTeacher.realname,
                  teacherCode: this.formData.selectedTeacher.userId,
                  examType: item.examType,
                  assistantTeacherId: this.formData.selectedHeadTeacher.userId
                })
              } else {
                this.requestList.push({
                  planCode: this.formData.selectedProgram.id,
                  sectionSeason: `${item.learning}-${item.endTime}`,
                  sectionTime: item.date,
                  examType: item.examType,
                  directUrlStudent: '',
                  directUrlTeacher: '',
                  classCode: this.formData.selectedClass.majorCode,
                  courseCode: this.formData.selectedCourse.id,
                  curriculumCode: item.id,
                  schedulingType: this.formData.schedulingType,
                  teacherName: this.formData.selectedTeacher.realname,
                  teacherId: this.formData.selectedTeacher.userId,
                  teacherCode: this.formData.selectedTeacher.userId,
                  teachingMethod: '班级授课'
                })
              }
            })
          } else if (this.formData.selectedExamPaper && this.formData.selectedExamPaper.length > 0) {
            this.formData.selectedExamPaper.forEach(item => {
              if (!item.learning) return
              if (this.formData.resource === '1') {
                this.requestList.push({
                  planType: 'student',
                  sectionSeason: `${item.learning}-${item.endTime}`,
                  sectionTime: item.date,
                  classCode: this.formData.selectedClass.majorCode,
                  curriculumCode: item.id,
                  schedulingType: this.formData.schedulingType,
                  teacherName: this.formData.selectedTeacher.realname,
                  teacherId: this.formData.selectedTeacher.userId,
                  teacherCode: this.formData.selectedTeacher.userId,
                  assistantTeacherId: this.formData.selectedHeadTeacher.userId
                })
              } else {
                this.requestList.push({
                  sectionSeason: `${item.learning}-${item.endTime}`,
                  sectionTime: item.date,
                  directUrlStudent: '',
                  directUrlTeacher: '',
                  classCode: this.formData.selectedClass.majorCode,
                  curriculumCode: item.id,
                  schedulingType: this.formData.schedulingType,
                  teacherName: this.formData.selectedTeacher.realname,
                  teacherId: this.formData.selectedTeacher.userId,
                  teacherCode: this.formData.selectedTeacher.userId,
                  teachingMethod: '班级授课'
                })
              }
            })
          } else if (this.formData.selectedProject && this.formData.selectedProject.length > 0) {
            this.formData.selectedProject.forEach(item => {
              if (!item.learning) return
              this.requestList.push({
                sectionSeason: `${item.learning}-${item.endTime}`,
                sectionTime: item.date,
                classCode: this.formData.selectedClass.majorCode,
                curriculumCode: item.taskCode,
                schedulingType: this.formData.schedulingType,
                teacherName: this.formData.selectedTeacher.realname,
                teacherId: this.formData.selectedTeacher.userId,
                teacherCode: this.formData.selectedTeacher.userId,
                taskGroupAmount: this.formData.teamNum,
                taskGroupMaxnum: this.formData.memberNum,
                assistantTeacherId: this.formData.selectedHeadTeacher.userId
              })
            })
          } else if (this.formData.selectedExamCenterPaper && this.formData.selectedExamCenterPaper.length > 0) {
            this.formData.selectedExamCenterPaper.forEach(item => {
              if (this.formData.resource === '1') {
                this.requestList.push({
                  planType: '0',
                  beginTime: item.beginTime,
                  endTime: item.endTime,
                  classCode: this.formData.selectedClass.majorCode,
                  schedulingResId: item.id,
                  paperId: item.paperId,
                  schedulingType: this.formData.schedulingType,
                  teacherName: this.formData.selectedTeacher.realname,
                  teacherId: this.formData.selectedTeacher.userId,
                  teacherCode: this.formData.selectedTeacher.userId,
                  assistantTeacherId: this.formData.selectedHeadTeacher.userId
                })
              } else {
                this.requestList.push({
                  planType: '1',
                  beginTime: item.beginTime,
                  endTime: item.endTime,
                  directUrlStudent: '',
                  directUrlTeacher: '',
                  classCode: this.formData.selectedClass.majorCode,
                  schedulingResId: item.id,
                  paperId: item.paperId,
                  schedulingType: this.formData.schedulingType,
                  teacherName: this.formData.selectedTeacher.realname,
                  teacherId: this.formData.selectedTeacher.userId,
                  teacherCode: this.formData.selectedTeacher.userId
                })
              }
            })
          }
          if (this.formData.schedulingType === '课程' && this.formData.resource === '1') {
            const studentIdArr = this.formData.selectedStudent.map(item => {
              return { id: item.userId }
            })
            const arr = []
            for (let j = 0; j < studentIdArr.length; j++) {
              for (let i = 0; i < this.requestList.length; i++) {
                arr.push({
                  ...this.requestList[i],
                  classCode: studentIdArr[j].id,
                  realName: this.formData.selectedStudent[j].realname
                })
              }
            }
            const data = { 'list': arr }
            this.insertStudentCourse(data)
          } else if (this.formData.schedulingType === '课程' && this.formData.resource === '0') {
            const data = { 'list': this.requestList }
            this.insertClassCourse(data)
          } else if (this.formData.schedulingType === '模拟练习' && this.formData.resource === '0') {
            const data = { 'list': this.requestList }
            this.insertClassCourse(data)
          } else if (this.formData.schedulingType === '模拟练习' && this.formData.resource === '1') {
            const studentIdArr = this.formData.selectedStudent.map(item => {
              return { id: item.userId }
            })
            const arr = []
            for (let j = 0; j < studentIdArr.length; j++) {
              for (let i = 0; i < this.requestList.length; i++) {
                arr.push({
                  ...this.requestList[i],
                  classCode: studentIdArr[j].id
                })
              }
            }
            const data = { 'list': arr }
            this.insertStudentCourse(data)
          } else if (this.formData.schedulingType === '项目') {
            const data = { 'list': this.requestList }
            this.insertClassCourse(data)
          } else if (this.formData.schedulingType === '考试' && this.formData.resource === '0') {
            const data = { 'list': this.requestList }
            this.insertExamCenterPaper(data)
          } else if (this.formData.schedulingType === '考试' && this.formData.resource === '1') {
            const studentIdArr = this.formData.selectedStudent.map(item => {
              return { id: item.userId }
            })
            const arr = []
            for (let j = 0; j < studentIdArr.length; j++) {
              for (let i = 0; i < this.requestList.length; i++) {
                arr.push({
                  ...this.requestList[i],
                  studentCode: studentIdArr[j].id
                })
              }
            }
            const data = { 'list': arr }
            this.insertExamCenterPaper(data)
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.pointer {
  cursor: pointer;
}
.ellipsis {
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
/deep/ .el-button--ghost {
    border: 1px dashed #c8cacd !important;
}
/deep/ .el-table__cell {
  padding: 0;
}
/deep/ .el-tooltip__popper {
    max-width: 300px;
}
/deep/ .el-form .tableData .el-form-item:not(.is-error) {
  margin-bottom: 0;
}
.drawer-wrap {
  .drawer-wrap-content {
    overflow: auto;
    .tableData {
      /deep/ .el-input .el-input__inner {
        padding: 0 10px;
      }
    }
  }
  .form-wrap {
    /deep/ .el-select-dropdown__item.is-disabled {
      display: none;
    }
    /deep/ .time-select-item.disabled {
      display: none;
    }
  }
}
</style>
