<template>
  <div ref="container" :gutter="10" class="handleTopo">
    <div ref="leftDiv" class="manual-wrap">
      <detail-card title="操作手册">
        <Manual slot="content" style="height: 100%;" />
      </detail-card>
    </div>
    <img src="@/assets/images/dragable.png" class="drag-bar" @mousedown="startDrag">
    <div ref="rightDiv" class="topo-wrap">
      <detail-card title="拓扑">
        <div slot="content" style="height: 100%;">
          <slot name="topo">
            <Topo
              v-if="topologyId"
              ref="topo"
              :scene-name="contentName"
              :scene-id="(topologyAllocation == 0 && topologyMode !== 1) ? '' : sourceSceneId"
              :topo-id="topologyId"
              :role-property="topologyAllocation == 0 ? 0 : 1"
              :topo-type="getTopoType"
              :role-filterate="enterType == 'student' && roleId ? 3 : 0"
              :user-identity="enterType == 'student' && roleId ? roleId : ''"
              :is-training-module="true"
              style="height: 100%;"
            />
            <el-empty
              v-else
              :image="img"
              :image-size="110"
              style="margin: 100px auto"
              description="暂无数据"
            />
          </slot>
        </div>
      </detail-card>
    </div>
  </div>
</template>

<script>
import moduleConf from '../config.js'
import detailCard from '@/packages/detail-view/detail-card.vue'
import Topo from '@/packages/topo/index'
import Manual from '@/views/admin/hrm/content/create/Manual.vue'
import { getSceneBasicInfoAPI, topologyQueryById } from '@/api/topo.js'
import { contentTopology, getContentById } from '@/api/teacher/index.js'

export default {
  name: 'HandleTopo',
  components: {
    detailCard,
    Topo,
    Manual
  },
  props: {
    enterType: {
      type: String,
      default: 'course' // course:课程，student:学生, teacher:老师
    }
  },
  data() {
    return {
      moduleConf: moduleConf,
      img: require('@/assets/empty_state.png'),
      topologyAllocation: this.$route.query.topologyAllocation, // 独享还是共享
      topologyMode: this.$route.query.topologyMode, // // 拓扑方式 0:自定义拓扑 1:仿真场景
      topologyId: '',
      roleId: this.$route.query.roleId || '',
      sourceSceneId: '', // 场景id
      contentId: this.$route.query.id || this.$route.query.examCode,
      contentName: this.$route.query.name,
      sceneInstanceId: this.$route.query.sceneInstanceId || ''
    }
  },
  computed: {
    getTopoType() {
      return this.topologyAllocation == 0 ? 'firingPermissions' : 'readPermissions'
    }
  },
  mounted() {
    if (this.topologyAllocation == 0 && this.topologyMode !== 1) {
      this.contentTopology()
    } else {
      this.getContentByIdFn()
    }
    this.$nextTick(() => {
      this.$refs.leftDiv.style.width = `${this.$refs.container.offsetWidth / 2}px`
      this.$refs.rightDiv.style.width = `${this.$refs.container.offsetWidth / 2}px`
    })
  },
  methods: {
    // 拖拽拓扑div宽度
    startDrag(event) {
      event.preventDefault()
      const startX = event.clientX
      const leftDivWidth = parseFloat(window.getComputedStyle(this.$refs.leftDiv).width)
      const containerWidth = this.$refs.container.offsetWidth
      const moveHandler = (event) => {
        let newWidth = leftDivWidth + event.clientX - startX
        if (newWidth < 300) newWidth = 300
        if (newWidth > containerWidth) newWidth = containerWidth
        this.$refs.leftDiv.style.width = `${newWidth}px`
        this.$refs.rightDiv.style.width = `${containerWidth - newWidth}px`
      }
      const upHandler = () => {
        document.removeEventListener('mousemove', moveHandler)
        document.removeEventListener('mouseup', upHandler)
      }
      document.addEventListener('mousemove', moveHandler)
      document.addEventListener('mouseup', upHandler)
    },
    // 根据场景id获取topologyId
    contentTopologyFn() {
      if (this.sourceSceneId) {
        getSceneBasicInfoAPI(this.sourceSceneId).then(res => {
          if (res.data) {
            const postData = new FormData()
            const topologyId = res.data.topologyTemplateId
            postData.append('topologyId', res.data.topologyTemplateId)
            topologyQueryById(postData).then(res => {
              if (res.data) {
                this.topologyId = topologyId
              } else {
                this.$message.error('当前课程内容缺少场景，无法正常加载。请确认场景状态或联系管理员。')
                this.topologyId = ''
              }
            })
          } else {
            this.$message.error('当前课程内容缺少场景，无法正常加载。请确认场景状态或联系管理员。')
            this.topologyId = ''
          }
        })
      } else {
        this.$message.error('当前课程内容缺少场景，无法正常加载。请确认场景状态或联系管理员。')
        this.topologyId = ''
      }
    },
    // 获取场景id（共享）
    getContentByIdFn() {
      const params = { id: this.contentId }
      getContentById(params).then(res => {
        if (res.code === 0) {
          this.sourceSceneId = res.data.sourceSceneIdStr
          this.contentTopologyFn()
        }
      })
    },
    // 获取拓扑id（独享）(教师)
    contentTopology() {
      contentTopology({ contentId: this.contentId, schedulingCode: null }).then(res => {
        const postData = new FormData()
        const topologyId = res.data.topologyId
        postData.append('topologyId', res.data.topologyId)
        topologyQueryById(postData).then(res => {
          if (res.data) {
            this.topologyId = topologyId
          } else {
            this.$message.error('当前课程内容缺少场景，无法正常加载。请确认场景状态或联系管理员。')
            this.topologyId = ''
          }
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.handleTopo {
  height: 100%;
  padding: 15px;
  display: flex;
  .manual-wrap, .topo-wrap {
    flex-grow: 1;
    min-width: 0px;
    height: 100%;
  }
  .drag-bar {
    cursor: ew-resize;
    width: 20px;
    height: 20px;
  }
  ::v-deep .detail-card-body {
    height: calc(100% - 38px);
    padding: 8px 0 0 0;
  }
}
</style>
