<template>
  <div v-loading="loading" class="dialog-wrap">
    <div class="dialog-wrap-content">
      <el-form ref="form" :model="formData" :rules="rules" label-position="left" label-width="90px" @submit.native.prevent>
        <el-form-item :label="isChapterLevel ? '章节名称' : '单元名称'" prop="name">
          <el-input v-model.trim="formData.name" placeholder="请输入内容"/>
        </el-form-item>
      </el-form>
    </div>
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>
<script>
import validate from '@/packages/validate'
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
import { updateChapterApi } from '@/api/teacher/index.js'
export default {
  components: {
  },
  mixins: [mixinsActionMenu],
  props: {
    // 传入数据
    data: {
      type: Array,
      default: () => {
        return []
      }
    },
    courseId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      loading: false,
      validate: validate,
      formData: {
        name: ''
      },
      rules: {
        name: [validate.required()]
      },
      treeData: [],
      addItemId: ''
    }
  },
  computed: {
    // 是否为章节
    isChapterLevel() {
      return this.data[0].type === 1
    }
  },
  created() {
    this.formData.name = this.data[0].name
  },
  methods: {
    close: function() {
      this.$emit('close')
    },
    confirm: function() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.loading = true
          const params = {
            name: this.formData.name,
            type: this.data[0].type,
            id: this.data[0].id,
            pjtCourseId: this.courseId
          }
          updateChapterApi(params).then((res) => {
            if (res.code === 0) {
              this.$message.success(`${this.isChapterLevel ? '章节' : '单元'}编辑成功`)
              this.data[0].name = this.formData.name // 实时刷新节点名称
              this.$emit('call', 'refresh')
              this.close()
            }
          }).catch(() => {
            this.loading = false
          })
        } else {
          return false
        }
      })
    }
  }
}
</script>


