<template>
  <div v-loading="loading" class="dialog-wrap">
    <el-alert :closable="false" type="warning">
      <div slot="title">输入数值调整面板层叠顺序，数值越大，面板越靠上。</div>
    </el-alert>
    <el-form ref="form" :model="formData" :rules="rules" label-position="left" label-width="80px" @submit.native.prevent>
      <el-form-item label="层叠顺序" prop="zIndex">
        <el-input-number v-model="formData.zIndex" placeholder="1-99"/>
      </el-form-item>
    </el-form>
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>
<script>
import validate from '../../validate'
export default {
  props: {
    // 传入数据
    data: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      loading: false,
      validate: validate,
      formData: {
        zIndex: 0
      },
      rules: {
        zIndex: [
          validate.required('change'),
          validate.all_number_integer,
          { type: 'number', min: 0, max: 99, message: '输入范围：0-99', trigger: 'change' }
        ]
      }
    }
  },
  created() {
    this.formData.zIndex = this.data.node.zIndex || 0
  },
  methods: {
    close: function() {
      this.$emit('close')
    },
    confirm: function() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          const postData = {
            'zIndex': this.formData['zIndex']
          }
          this.$emit('call', 'setZIndex', postData)
          this.close()
        }
      })
    }
  }
}
</script>
