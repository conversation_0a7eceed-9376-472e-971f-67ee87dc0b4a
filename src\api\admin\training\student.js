import request from '@/utils/request'

/**
 * 教师列表
 */
export function searchAPI(data) {
  return request({
    url: 'training/PjtSysUser/searchMajorStudent',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 新增
 */
export function addAPI(data) {
  return request({
    url: '/training/PjtSysUser/insertStudent',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 编辑
 */
export function editAPI(data) {
  return request({
    url: '/training/PjtSysUser/updatePjtUserMajorRelation',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 删除
 */
export function dropAPI(data) {
  return request({
    url: '/training/PjtSysUser/deletePjtUserMajorRelation' + `?classCode=${data[0].classCode}`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 修改状态
 */
export function editStatusAPI(data) {
  return request({
    url: '/training/PjtSysUser/updatePjtUserMajorRelationStatus',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}


/**
 * 专业树
 */
export function majorTreeAPI(data) {
  return request({
    url: '/training/pjtMajorClass/backSearchMajorClass',
    method: 'post'
  })
}

/**
 * 模版下载
 */
export function templateAPI(data) {
  return request({
    url: '/training/PjtSysUser/exportUserTemplate',
    method: 'post',
    responseType: 'blob'
  })
}

/**
 * 导入
 */
export function importAPI(data) {
  return request({
    url: 'training/PjtSysUser/importUserStudentTemplate',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data
  })
}

/**
 * 导出
 */
export function exportAPI(data) {
  return request({
    url: 'training/PjtSysUser/exportSearchMajorStudent',
    method: 'post',
    responseType: 'blob',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
/**
 * 人员管理
 *
 */
export function queryLearnProcess(data) {
  return request({
    url: 'training/studentManager/learn/process',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

// 班级必修学习历程
export function classRequiredProcess(data) {
  return request({
    url: 'training/studentManager/classRequired/process',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
// 学员必修学习历程查询
export function studentRequiredProcess(data) {
  return request({
    url: 'training/studentManager/studentRequired/process',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export function queryStudentList(data) {
  return request({
    url: 'training/studentManager/queryStudentList',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export function queryStudentManageList(data) {
  return request({
    url: 'training/studentManager/queryStudentManageList',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export function queryAllStudentList(data) {
  return request({
    url: 'training/studentManager/student/query',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
export function addStudentRole(data) {
  return request({
    url: 'training/studentManager/student/save',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
export function deleteStudentRole(data) {
  return request({
    url: 'training/studentManager/student/delete',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export function exportStudentAPI(data) {
  return request({
    url: 'training/studentManager/student/exportStudent',
    method: 'post',
    responseType: 'blob',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export function exportStudentNewAPI(data) {
  return request({
    url: 'training/studentManager/student/exportStudentNew',
    method: 'post',
    responseType: 'blob',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export function exportAssistantNewAPI(data) {
  return request({
    url: 'training/assistantTeacher/assistant/exportAssistantNew',
    method: 'post',
    responseType: 'blob',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export function exportTeacherNewAPI(data) {
  return request({
    url: 'training/teacherManager/teacher/exportTeacherNew',
    method: 'post',
    responseType: 'blob',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 班级管理
 */
export function queryMajor(data) {
  return request({
    url: 'training/class/major/query',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
export function saveMajor(data) {
  return request({
    url: 'training/class/major/save',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
export function updateMajor(data) {
  return request({
    url: 'training/class/major/update',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
export function deleteMajor(data) {
  return request({
    url: 'training/class/major/delete',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export function classQuery(data) {
  return request({
    url: 'training/class/query',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
export function saveClass(data) {
  return request({
    url: 'training/class/save',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
export function deleteClass(data) {
  return request({
    url: 'training/class/delete',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
export function updateClass(data) {
  return request({
    url: 'training/class/update',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
export function queryClassDetail(data) {
  return request({
    url: 'training/class/detail/query',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
export function saveClassDetail(data) {
  return request({
    url: 'training/class/student/save',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
export function deleteClassDetail(data) {
  return request({
    url: 'training/class/student/delete',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
/**
 * 教师管理
 */
export function queryTeacherManager(data) {
  return request({
    url: 'training/teacherManager/query',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
export function queryAddTeacherManager(data) {
  return request({
    url: 'training/teacherManager/teacher/query',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
export function addTeacherManager(data) {
  return request({
    url: 'training/teacherManager/teacher/save',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
export function deleteTeacherManager(data) {
  return request({
    url: 'training/teacherManager/teacher/delete',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
export function exportTeacherAPI(data) {
  return request({
    url: 'training/teacherManager/teacher/exportTeacher',
    method: 'post',
    responseType: 'blob',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
/**
 * 助教管理
 */

export function queryAssistantTeacher(data) {
  return request({
    url: 'training/assistantTeacher/query',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

export function queryAllAssistantTeacher(params) {
  return request({
    url: 'training/assistantTeacher/allAssistantTeacher/query',
    method: 'get',
    params,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
export function queryAddAssistantTeacher(data) {
  return request({
    url: 'training/assistantTeacher/assistant/query',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
export function saveAssistantTeacher(data) {
  return request({
    url: 'training/assistantTeacher/assistant/save',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
export function deleteAssistantTeacher(data) {
  return request({
    url: 'training/assistantTeacher/assistant/delete',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
export function exportAssistantAPI(data) {
  return request({
    url: 'training/assistantTeacher/assistant/exportAssistant',
    method: 'post',
    responseType: 'blob',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
