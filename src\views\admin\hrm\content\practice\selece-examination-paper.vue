<template>
  <div class="resource-table">
    <!-- 分类区 -->
    <div style="margin-bottom: 10px;" class="category">分类
      <el-select v-model="categoryCode" :popper-append-to-body="false" filterable @change="categoryChange">
        <el-option
          v-for="item in categoryArr"
          :key="item.categoryCode"
          :label="item.categoryName"
          :value="item.categoryCode"
        >
          <el-tooltip
            placement="bottom"
            width="200">
            <div slot="content">{{ item.categoryName }}</div>
            <span>
              {{ item.categoryName }}
            </span>
          </el-tooltip>
        </el-option>
      </el-select>
    </div>
    <!-- 操作区 -->
    <div class="operation-wrap">
      <div class="operation-left">
        <slot name="action" />
        <el-button type="primary" icon="el-icon-refresh" @click="refresh" />
      </div>
      <div class="operation-right">
        <el-badge :value="searchBtnShowNum">
          <el-button icon="el-icon-search" @click="openSearch">搜索</el-button>
        </el-badge>
        <!-- 自定义表格列 -->
        <t-table-config
          v-if="!customColData.length"
          :data="columnsObj"
          :active-key-arr="columnsViewArr"
          @on-change-col="onChangeCol"
        />
      </div>
    </div>
    <!-- 搜索区 -->
    <t-search-box
      v-show="searchView"
      :search-key-list="searchKeyListView"
      default-placeholder="默认搜索名称"
      @search="searchMultiple"
    />
    <!-- 列表 -->
    <t-table-view
      ref="tableView"
      :height="height"
      :single="true"
      :loading="tableLoading"
      :data="tableData"
      :total="tableTotal"
      :page-size="pageSize"
      :current="pageCurrent"
      :select-item="selectItem"
      @on-select="onSelect"
      @on-current="onCurrent"
      @on-change="changePage"
      @on-sort-change="onSortChange"
      @on-page-size-change="onPageSizeChange"
    >
      <el-table-column
        v-for="item in columnsViewArr"
        :key="item"
        :min-width="colMinWidth"
        :label="columnsObj[item].title"
        :fixed="columnsObj[item].master ? 'left' : false"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span v-if="item == 'score' || item == 'questionNum'">{{ scope.row[item] || 0 }}</span>
          <span v-else>{{ scope.row[item] || "-" }}</span>
        </template>
      </el-table-column>
    </t-table-view>
    <!-- 底部 -->
    <div class="drawer-footer">
      <div>
        <el-button :disabled="!selectItem.length" type="primary" @click="confirm">确定</el-button>
        <el-button type="text" @click="close">取消</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import tSearchBox from '@/packages/search-box/index.vue'
import tTableView from '@/packages/table-view/index.vue'
import tTableConfig from '@/packages/table-config/table-col-config.vue'
import tableTdMultiCol from '@/packages/table-config/table-td-multi-col.vue'
import mixinsPageTable from '@/packages/mixins/page_table'
import { getExamList, getCategory } from '@/api/teacher/index.js'

export default {
  components: {
    tSearchBox,
    tTableView,
    tTableConfig,
    tableTdMultiCol
  },
  mixins: [mixinsPageTable],
  data() {
    return {
      // 搜索配置项
      searchKeyList: [
        { key: 'examName', label: '名称', master: true, placeholder: '默认搜索名称' }
      ],
      // 所有可配置显示列 master：不可隐藏 title:列名称
      columnsObj: {
        'examName': { title: '名称', master: true },
        'categoryName': { title: '分类' },
        'score': { title: '分数' },
        'questionNum': { title: '题目数量' }
      },
      // 当前显示列key表 默认，如果localStorage有数据将被覆盖
      columnsViewArr: [
        'examName',
        'categoryName',
        'score',
        'questionNum'
      ],
      generateList: {
        '1': '初级',
        '2': '中级',
        '3': '高级'
      },
      categoryArr: [],
      categoryCode: ''
    }
  },
  methods: {
    getList: function(showLoading = true) {
      if (showLoading) {
        this.tableLoading = true
      }
      const params = this.getPostData('page', 'limit')
      params.categoryId = this.categoryCode || ''
      getExamList(params).then((res) => {
        if (res.code === 0) {
          this.tableData = res.data.records
          this.tableTotal = res.data.total
          this.tableLoading = false
          this.handleSelection()
        }
      }).catch(() => {
        this.tableLoading = false
      })
      this.pjtCategoryQuery()
    },
    'categoryChange': function(value) {
      this.categoryCode = value
      this.getList()
    },
    pjtCategoryQuery() {
      getCategory({ pageType: 0, categoryType: 4 }).then(res => {
        const paramArr = []
        res.data.records.forEach((item) => {
          paramArr.push({ categoryCode: String(item.id), categoryName: item.categoryName })
        })
        this.categoryArr = [{ categoryName: '全部', categoryCode: '' }, ...paramArr]
      })
    },
    'close': function() {
      this.$emit('close')
    },
    'confirm': function() {
      this.$emit('call', 'selece_examination_paper', this.selectItem)
    }
  }
}
</script>
<style lang="scss">
.resource-table {
  height: 100%;
}
.drawer-footer{
  display: flex;
  align-items: center;
  // height: 8%;
}
</style>
