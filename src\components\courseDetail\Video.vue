<template>
  <div class="h-100 flex-col">
    <div v-if="fileList.length != 0" class="grid-view">
      <video v-if="curriculumVideoUrl" id="showVideo" :key="curriculumVideoUrl" height="100%" controlsList="nodownload" style="overflow-y:scroll" width="100%" controls>
        <source :src="curriculumVideoUrl" type="video/mp4">
      </video>
    </div>
    <el-empty
      v-if="fileList.length === 0"
      :image="img"
      :image-size="110"
      style="margin: 100px auto"
      description="暂无数据"
    />
  </div>
</template>
<script>
import { contentdetail } from '@/api/teacher/index.js'
export default {
  props: {
    id: [String, Number]
  },
  data() {
    return {
      img: require('@/assets/empty_state.png'),
      curriculumVideoUrl: '',
      fileList: [],
      fileName: ''
    }
  },
  watch: {
    id: {
      handler() {
        this.searchCurriculum()
      },
      immediate: true
    }
  },
  created() {
    this.searchCurriculum()
  },
  methods: {
    searchCurriculum() {
      contentdetail({ contentId: this.id, format: 'video' }).then(res => {
        this.fileList = res.data
        this.fileList.map(item => {
          item.name = item.attachmentUrl
          item.url = item.attachmentUrl
        })
        const file = this.fileList[0]
        if (file) {
          this.handlePreview(file)
        }
      })
    },
    handlePreview(file) {
      this.curriculumVideoUrl = file.url
    }
  }
}
</script>
<style lang="scss" scoped>
  .grid-view {
    height: 100%;
    width: 100%;
    background: #fff;
    #showVideo {
      background: #fff;
    }
  }
</style>
