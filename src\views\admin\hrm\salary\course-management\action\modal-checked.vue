<template>
  <div class="dialog-wrap">
    <el-alert v-if="isJoin" :closable="false" type="warning">
      <div slot="title">
        <p>该课程已被学员加入自学，修改课程后学员侧自学课程将同步修改！</p >
      </div>
    </el-alert>
    <el-checkbox v-if="isJoin" v-model="checked">我已知晓上述风险</el-checkbox>
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button :disabled="(!checked || !isJoin)" type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    data: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      checked: false,
      isJoin: false
    }
  },
  mounted() {
    this.isJoin = this.data.selective
  },
  methods: {
    close() {
      this.$emit('close')
    },
    confirm: function() {
      this.$emit('call', 'openEditModal')
    }
  }
}
</script>
