<template>
  <el-form v-loading="loading" ref="buildForm" :model="buildForm" :rules="buildRules" label-width="130px">
    <el-card class="mb-10">
      <el-form-item class="first-child" label="类型" prop="contentType">
        <el-radio-group v-model="buildForm.contentType" class="flex-left ai-end" @change="contentTypeChange">
          <div class="flex">
            <el-radio :label="1" :disabled="contentId ? true : false" class="mr-3">理论</el-radio>
            <el-tooltip class="item" effect="dark" placement="top">
              <!--  问号的图标   -->
              <i class="el-icon-warning-outline" style="font-size: 14px; vertical-align: middle;"/>
              <!--  提示的内容 -->
              <div slot="content">
                理论知识课程，不包含实操
              </div>
            </el-tooltip>
          </div>
          <div class="flex">
            <el-radio :label="2" :disabled="contentId ? true : false" class="ml-30 mr-3">仿真</el-radio>
            <el-tooltip class="item" effect="dark" placement="top">
              <!--  问号的图标   -->
              <i class="el-icon-warning-outline" style="font-size: 14px; vertical-align: middle;"/>
              <!--  提示的内容 -->
              <div slot="content">
                包含仿真拓扑的实操课
              </div>
            </el-tooltip>
          </div>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="buildForm.contentType == '2'" label="场景分配" prop="topologyAllocation">
        <el-radio-group v-model="buildForm.topologyAllocation" @change="changeTopoType">
          <el-radio :label="0" :disabled="contentId ? true : false">独享</el-radio>
          <el-radio :label="1" :disabled="contentId ? true : false" style="margin-left: 16px;">共享</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="buildForm.contentType == '2' && buildForm.topologyAllocation == '0'" label="场景" prop="topologyMode">
        <el-radio-group v-model="buildForm.topologyMode" @change="changeTopologyMode">
          <el-radio :label="0" :disabled="contentId ? true : false">新建</el-radio>
          <el-radio :label="1" :disabled="contentId ? true : false" style="margin-left: 16px;">选择已有</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="名称" prop="name">
        <el-input :disabled="type == '查看'" v-model.trim="buildForm.name" placeholder="名称" style="width: 320px;"/>
      </el-form-item>
      <el-form-item label="分类" prop="contentCategoryId">
        <el-select :disabled="type == '查看'" v-model="buildForm.contentCategoryId" size="medium" clearable placeholder="请选择" style="width: 320px;">
          <el-option
            v-for="item in parentVm.contentCategoryList"
            :key="item.id"
            :label="item.name"
            :value="item.id"/>
        </el-select>
      </el-form-item>
      <el-form-item label="难度" prop="contentLevel">
        <el-select :disabled="type == '查看'" v-model="buildForm.contentLevel" size="medium" clearable placeholder="请选择" style="width: 320px;">
          <el-option
            v-for="item in contentLevelList"
            :key="item.value"
            :label="item.label"
            :value="item.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="课时" prop="contentPeriod">
        <el-input-number :disabled="type == '查看'" v-model="buildForm.contentPeriod" :min="1" :max="100" label="课时"/>
      </el-form-item>
      <el-form-item label="包含知识点" class="includes-knowledge" prop="contentKnowledgeIdList">
        <el-select :disabled="type == '查看'" v-model="buildForm.contentKnowledgeIdList" :multiple-limit="10" multiple collapse-tags placeholder="请选择" style="width: 320px;">
          <el-option
            v-for="item in parentVm.knowledgeList"
            :key="item.knowledgeCode"
            :label="item.knowledgeName"
            :value="item.knowledgeCode"/>
        </el-select>
      </el-form-item>
    </el-card>
  </el-form>
</template>
<style lang="scss" scoped>
.first-child{
  display: flex;
  align-items: center;
  justify-content: flex-start;
  ::v-deep .el-form-item__content{
    margin-left: 0px !important;
  }
  .mr-3{
    margin-right: 3px;
  }
}
::v-deep .includes-knowledge {
  .el-tag{
    max-width: 95px;
  }
}
</style>
<script>
import module from '../config.js'
import validate from '@/packages/validate'
import { addContent, updateContent, addContentTopo, getContentById } from '@/api/teacher/index.js'
import mixin from './mixin'
export default {
  name: 'AddContent',
  mixins: [mixin],
  props: {
    data: {
      type: Object
    }
  },
  data() {
    return {
      moduleName: module.name,
      validate: validate,
      contentLevelList: [{ label: '初级', value: 1 }, { label: '中级', value: 2 }, { label: '高级', value: 3 }],
      buildRules: {
        contentType: [validate.required()],
        name: [validate.required(), validate.name_64_char],
        contentPeriod: [validate.required()],
        contentKnowledgeIdList: [validate.required()],
        contentCategoryId: [validate.required()],
        contentLevel: [validate.required()]
      },
      buildForm: {
        contentType: 1,
        topologyAllocation: 0, // 拓扑分配 0:独享 1:共享
        topologyMode: 0, // 拓扑方式 0:自定义拓扑 1:选择仿真场景
        name: '',
        contentCategoryId: '',
        contentLevel: '',
        contentPeriod: '',
        contentKnowledgeIdList: []
      },
      apiType: addContent
    }
  },
  inject: ['parentVm'],
  watch: {
    'data': {
      handler: function(nval, oval) {
        // 回显信息表单
        for (const key in this.buildForm) {
          this.buildForm[key] = nval[key]
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    getContentByIdAPI() {
      this.loading = true
      const params = { id: this.contentId }
      getContentById(params).then(res => {
        if (res.code == 0) {
          for (const key in this.buildForm) {
            if (key == 'contentType') {
              this.buildForm['contentType'] = Number(res.data['contentType'])
            } else if (key == 'contentKnowledgeIdList') {
              const numArray = res.data['contentKnowledgeIdList'].split(',').map(str => Number(str))
              this.buildForm['contentKnowledgeIdList'] = numArray
            } else {
              this.buildForm[key] = res.data[key]
            }
          }
        }
      }).finally(() => {
        this.loading = false
      })
    },
    contentTypeChange(val) {
      this.parentVm.buildForm['contentType'] = val
      this.buildForm['contentType'] = this.data['contentType']
    },
    // 改变仿真拓扑类型
    changeTopoType(val) {
      this.parentVm.buildForm['topologyAllocation'] = val
      this.buildForm['topologyAllocation'] = this.data['topologyAllocation']
      if (this.buildForm['topologyAllocation'] == 0) {
        this.parentVm.buildForm['topologyMode'] = 0
      }
    },
    // 仿真独享改变拓扑模式
    changeTopologyMode(val) {
      this.parentVm.buildForm['topologyMode'] = val
      this.buildForm['topologyMode'] = this.data['topologyMode']
    },
    close: function() {
      this.$emit('close')
    },
    confirm: function(callback) {
      this.$refs['buildForm'].validate((valid) => {
        if (valid) {
          this.saveContentInfo()
          callback()
        }
      })
    },
    // 保存课程内容基本信息
    saveContentInfo: function(showMessage) {
      const postData = JSON.parse(JSON.stringify(this.buildForm))
      this.loading = true
      if (this.contentId) {
        this.apiType = updateContent
        postData.id = this.contentId
      }
      // 理论不传仿真拓扑分配
      if (this.buildForm.contentType == 1) {
        delete postData.topologyAllocation
        delete postData.topologyMode
      }
      postData.contentKnowledgeIdList = this.buildForm.contentKnowledgeIdList.join(',')
      if (this.buildForm.contentType == 2 && !this.contentId) {
        addContentTopo(postData).then(res => {
          if (res.code === 0) {
            this.dataId = res.data && res.data.id
            if (this.dataId) {
              localStorage.setItem('dataId', JSON.stringify(this.dataId))
            }
            this.$emit('call', 'info', res.data)
          }
        })
      } else {
        this.apiType(postData).then(res => {
          if (res.code === 0) {
            if (this.contentId) {
              this.$emit('call', 'info', res.data)
              // if (showMessage) {
              //   this.$message.success(`编辑成功`)
              // }
              postData['contentKnowledgeIdList'] = this.buildForm['contentKnowledgeIdList'].split(',').map(str => Number(str))
            } else {
              this.dataId = res.data && res.data.id
              if (this.dataId) {
                localStorage.setItem('dataId', JSON.stringify(this.dataId))
              }
            }
            this.$emit('call', 'info', res.data)
            this.loading = false
          }
        }).catch(() => {
          this.loading = false
        })
      }
    }
  }
}
</script>
