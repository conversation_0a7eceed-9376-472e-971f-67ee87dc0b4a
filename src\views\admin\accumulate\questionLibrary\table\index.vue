<template>
  <div class="resource-table">
    <!-- 操作区 -->
    <div class="operation-wrap">
      <div class="operation-left">
        <slot name="action" />
        <el-button type="primary" icon="el-icon-refresh" @click="refresh" />
      </div>
      <div class="operation-right">
        <el-badge :value="searchBtnShowNum">
          <el-button icon="el-icon-search" @click="openSearch">搜索</el-button>
        </el-badge>
        <!-- 自定义表格列 -->
        <t-table-config
          v-if="!customColData.length"
          :data="columnsObj"
          :active-key-arr="columnsViewArr"
          @on-change-col="onChangeCol"
        />
      </div>
    </div>
    <!-- 搜索区 -->
    <t-search-box
      v-show="searchView"
      :search-key-list="searchKeyListView"
      default-placeholder="默认搜索名称"
      @search="searchMultiple"
    />
    <!-- 列表 -->
    <t-table-view
      ref="tableView"
      :height="height"
      :single="single"
      :loading="tableLoading"
      :data="tableData"
      :total="tableTotal"
      :page-size="pageSize"
      :current="pageCurrent"
      :select-item="selectItem"
      @on-select="onSelect"
      @on-current="onCurrent"
      @on-change="changePage"
      @on-sort-change="onSortChange"
      @on-page-size-change="onPageSizeChange"
    >
      <el-table-column
        v-for="item in columnsViewArr"
        :key="item"
        :min-width="colMinWidth"
        :label="columnsObj[item].title"
        :fixed="columnsObj[item].master ? 'left' : false"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span v-if="item === 'questionDepotName' && link">
            <a
              v-if="scope.row.questionDepotName"
              :href="`/baseResource/library/detail/${scope.row.id}/overview`"
              @click.prevent="linkEvent('libraryDetail', scope.row, { id: scope.row.id, view: 'overview' })"
            >
              {{ scope.row[item] }}
            </a>
            <span v-else>{{ "-" }}</span>
          </span>
          <span v-else-if="item === 'questionNumber'">{{ scope.row[item] }}</span>
          <span v-else-if="item === 'bankType'">
            {{ questionCategoryObj[scope.row.bankType].label }}
          </span>
          <span v-else-if="item === 'questionDepotVersion'">
            {{ scope.row[item] || "-" }}
          </span>
          <span v-else>{{ scope.row[item] || "-" }}</span>
        </template>
      </el-table-column>
    </t-table-view>
  </div>
</template>

<script>
import moduleConf from '../config.js'
import questionConf from '@/views/admin/accumulate/questionLibrary/detail/questionBank/config.js'
import tSearchBox from '@/packages/search-box/index.vue'
import tTableView from '@/packages/table-view/index.vue'
import tTableConfig from '@/packages/table-config/table-col-config.vue'
import mixinsPageTable from '@/packages/mixins/page_table'
import { getQuestionDepotList } from '@/api/accumulate/questionLibrary.js'

export default {
  components: {
    tSearchBox,
    tTableView,
    tTableConfig
  },
  mixins: [mixinsPageTable],
  data() {
    return {
      moduleConf: moduleConf,
      moduleName: moduleConf.name,
      questionCategoryObj: questionConf.questionCategoryObj,
      // 搜索配置项
      searchKeyList: [
        { key: 'questionDepotName', label: '名称', master: true, placeholder: '默认搜索名称' }
      ],
      // 所有可配置显示列 master：不可隐藏 title:列名称
      columnsObj: moduleConf.columnsObj,
      // 当前显示列key表 默认，如果localStorage有数据将被覆盖
      columnsViewArr: [
        ...Object.keys(moduleConf.columnsObj)
      ]
    }
  },
  methods: {
    getList: function(showLoading = true) {
      if (showLoading) {
        this.tableLoading = true
      }
      const params = this.getPostData('page', 'limit')
      getQuestionDepotList(params).then((res) => {
        if (res.code === 0) {
          this.tableData = res.data.records
          this.tableTotal = res.data.total
          this.tableLoading = false
          this.handleSelection()
        }
      }).catch(() => {
        this.tableLoading = false
      })
    }
  }
}
</script>
