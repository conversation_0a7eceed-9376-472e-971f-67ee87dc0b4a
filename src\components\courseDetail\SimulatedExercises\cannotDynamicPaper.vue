<template>
  <div class="content-wrap-container" style="width: 100%;height:100%">
    <div>
      <el-empty
        :image="img"
        :image-size="118"
        style="margin: 200px auto"
        description="该课程内容为动态试卷，暂不支持查看详情"
      />
    </div>
  </div>
</template>

<script>
export default {
  // 考试试卷
  name: 'CannotDynamicPaper',
  components: {
  },
  props: {
    // 隐藏倒计时
    hiddenCountdown: Boolean,
    // 随堂练习和模拟练习的时间
    sectionSeason: String,
    sectionTime: String,
    // 1未开始，2进行中，3已结束
    status: Number,
    examName: String
  },
  data() {
    return {
      img: require('@/assets/empty_state.png'),
      sign: this.$route.query.sign,
      courseId: this.$route.query.courseId || '',
      contentId: this.$route.query.curriculumCode || '',
      schedulingCode: this.$route.query.schedulingId || '',
      isShowPracticeTip: false // 是否显示开放试卷提示
    }
  },
  mounted() {
  },
  methods: {
  }
}
</script>
