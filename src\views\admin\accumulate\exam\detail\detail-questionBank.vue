<template>
  <div class="detail-question-bank">
    <div class="_paper_search">
      <div class="_paper_search_1">
        总题数
        <span style="font-weight: bold; color: var(--color-600); font-size: 14px; margin-right: 10px;">
          {{ getQuestionNum }}
        </span>
      </div>
      <div class="_paper_search_1">
        总分数
        <span style="font-weight: bold; color: var(--color-600); font-size: 14px;">
          {{ getScore }}
        </span>
      </div>
    </div>
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane :label="`理论（${ theoryQuestionList.length }）`" name="theory">
        <!-- 题型筛选 -->
        <div style="margin: 0 0 10px 0">
          题型：
          <el-select v-model="questionType" clearable placeholder="请选择" @change="screenQuestionType()">
            <el-option
              v-for="item in questionTypeArr"
              :key="item.value"
              :label="item.label"
              :value="item.value"/>
          </el-select>
        </div>
        <!-- 题目列表 -->
        <div v-if="theoryQuestionList.length" class="_question_list">
          <div
            v-for="(q, index) in theoryQuestionList"
            :class="`_question_item`"
            :key="q.id"
          >
            <div>
              <div class="_question_item_content">{{ index + 1 }}.&nbsp;<span v-html="q.content"/></div>
              <div class="_question_item_type">
                <el-tag size="small">{{ questionConf.questionTypObj[q.questionType].label }}</el-tag>
              </div>
            </div>
            <div v-if="q.questionType == 1" class="_question_option">
              <el-radio-group
                v-for="(op, i) in JSON.parse(q.questionOptions)"
                :key="op"
                :value="q.questionAnswer"
                disabled
              >
                <el-radio :label="optionLabel[i]">{{ op }}</el-radio>
              </el-radio-group>
            </div>
            <div v-else-if="q.questionType == 2">
              <el-checkbox-group :value="q.questionAnswer.split('')" disabled>
                <div class="_question_option">
                  <el-checkbox
                    v-for="(op, i) in JSON.parse(q.questionOptions)"
                    :key="op"
                    :label="optionLabel[i]"
                  >{{ op }}</el-checkbox
                  >
                </div>
              </el-checkbox-group>
            </div>
            <div v-else-if="q.questionType == 3" class="_question_option">
              <el-radio-group :value="q.questionAnswer" disabled>
                <el-radio label="A">正确</el-radio>
                <el-radio label="B">错误</el-radio>
              </el-radio-group>
            </div>
            <div v-else class="_question_option">
              <span style="padding-bottom: 20px">{{ q.questionAnswer }}</span>
            </div>
            <div class="_question_score">
              <div class="flex-left">
                <div>该题：<span>{{ q.questionScore }}</span> 分</div>
                <div class="ml-20">
                  <span>备用：</span>
                  <el-radio
                    v-for="item in isSpareArr"
                    :key="item.value"
                    v-model="q.isSpare"
                    :label="item.value"
                    disabled
                    style="margin: 0 20px 0 0 !important;"
                  >{{ item.label }}</el-radio>
                </div>
                <div v-if="q.questionType == 4 || q.questionType == 5" class="ml-20">
                  <span>题目作答方式：</span>
                  <el-radio
                    v-for="item in answeringMethod"
                    :key="item.value"
                    v-model="q.questionAnswerMethod"
                    :label="item.value"
                    disabled
                    style="margin: 0 20px 0 0 !important;"
                  >{{ item.label }}</el-radio>
                </div>
              </div>
              <a href="javascript:;" @click="toDetail('theory', q.id)">
                查看详情
              </a>
            </div>
          </div>
        </div>
        <el-empty
          v-else
          :image="img"
          :image-size="110"
          style="margin: 100px auto"
          description="暂无数据"
        />
      </el-tab-pane>
      <el-tab-pane :label="`靶机（${ targetQuestionList.length }）`" name="target">
        <!-- 题型筛选 -->
        <div style="margin: 0 0 10px 0">
          题型：
          <el-select v-model="questionType" clearable placeholder="请选择" @change="screenQuestionType()">
            <el-option
              v-for="item in questionTypeArr"
              :key="item.value"
              :label="item.label"
              :value="item.value"/>
          </el-select>
        </div>
        <!-- 题目列表 -->
        <div v-if="targetQuestionList.length" class="_question_list">
          <div
            v-for="(q, index) in targetQuestionList"
            :class="`_question_item`"
            :key="q.id"
          >
            <div>
              <div class="_question_item_content">{{ index + 1 }}.&nbsp;<span v-html="q.content"/></div>
              <div class="_question_item_type">
                <el-tag size="small">{{ questionConf.questionTypObj[q.questionType].label }}</el-tag>
              </div>
            </div>
            <div v-if="q.questionType == 1" class="_question_option">
              <el-radio-group
                v-for="(op, i) in JSON.parse(q.questionOptions)"
                :key="op"
                :value="q.questionAnswer"
                disabled
              >
                <el-radio :label="optionLabel[i]">{{ op }}</el-radio>
              </el-radio-group>
            </div>
            <div v-else-if="q.questionType == 2">
              <el-checkbox-group :value="q.questionAnswer.split('')" disabled>
                <div class="_question_option">
                  <el-checkbox
                    v-for="(op, i) in JSON.parse(q.questionOptions)"
                    :key="op"
                    :label="optionLabel[i]"
                  >{{ op }}</el-checkbox
                  >
                </div>
              </el-checkbox-group>
            </div>
            <div v-else-if="q.questionType == 3" class="_question_option">
              <el-radio-group :value="q.questionAnswer" disabled>
                <el-radio label="A">正确</el-radio>
                <el-radio label="B">错误</el-radio>
              </el-radio-group>
            </div>
            <div v-else class="_question_option">
              <span style="padding-bottom: 20px">{{ q.questionAnswer }}</span>
            </div>
            <div class="_question_score">
              <!-- <div>
                该题：<span>{{ q.questionScore }}</span> 分
              </div> -->
              <div class="flex-left">
                <div>该题：<span>{{ q.questionScore }}</span> 分</div>
                <div class="ml-20">
                  <span>备用：</span>
                  <el-radio
                    v-for="item in isSpareArr"
                    :key="item.value"
                    v-model="q.isSpare"
                    :label="item.value"
                    disabled
                    style="margin: 0 20px 0 0 !important;"
                  >{{ item.label }}</el-radio>
                </div>
                <div v-if="q.questionType == 4 || q.questionType == 5" class="ml-20">
                  <span>题目作答方式：</span>
                  <el-radio
                    v-for="item in answeringMethod"
                    :key="item.value"
                    v-model="q.questionAnswerMethod"
                    :label="item.value"
                    disabled
                    style="margin: 0 20px 0 0 !important;"
                  >{{ item.label }}</el-radio>
                </div>
              </div>
              <a href="javascript:;" @click="toDetail('targetDevice', q.id)">
                查看详情
              </a>
            </div>
          </div>
        </div>
        <el-empty
          v-else
          :image="img"
          :image-size="110"
          style="margin: 100px auto"
          description="暂无数据"
        />
      </el-tab-pane>
      <el-tab-pane :label="`仿真（${ emulationQuestionList.length }）`" name="emulation">
        <!-- 题型筛选 -->
        <div style="margin: 0 0 10px 0">
          题型：
          <el-select v-model="questionType" clearable placeholder="请选择" @change="screenQuestionType()">
            <el-option
              v-for="item in questionTypeArr"
              :key="item.value"
              :label="item.label"
              :value="item.value"/>
          </el-select>
        </div>
        <!-- 题目列表 -->
        <div v-if="emulationQuestionList.length" class="_question_list">
          <div
            v-for="(q, index) in emulationQuestionList"
            :class="`_question_item`"
            :key="q.id"
          >
            <div>
              <div class="_question_item_content">{{ index + 1 }}.&nbsp;<span v-html="q.questionType != 10 ? q.content : q.questionName"/></div>
              <div class="_question_item_type">
                <el-tag size="small">{{ questionConf.questionTypObj[q.questionType].label }}</el-tag>
              </div>
            </div>
            <div v-if="q.questionType == 1" class="_question_option">
              <el-radio-group
                v-for="(op, i) in JSON.parse(q.questionOptions)"
                :key="op"
                :value="q.questionAnswer"
                disabled
              >
                <el-radio :label="optionLabel[i]">{{ op }}</el-radio>
              </el-radio-group>
            </div>
            <div v-else-if="q.questionType == 2">
              <el-checkbox-group :value="q.questionAnswer.split('')" disabled>
                <div class="_question_option">
                  <el-checkbox
                    v-for="(op, i) in JSON.parse(q.questionOptions)"
                    :key="op"
                    :label="optionLabel[i]"
                  >{{ op }}</el-checkbox
                  >
                </div>
              </el-checkbox-group>
            </div>
            <div v-else-if="q.questionType == 3" class="_question_option">
              <el-radio-group :value="q.questionAnswer" disabled>
                <el-radio label="A">正确</el-radio>
                <el-radio label="B">错误</el-radio>
              </el-radio-group>
            </div>
            <div v-else-if="q.questionType == 10" class="combination-question-wrap">
              <div v-for="(item, index) in q.combinationQuestionBOS" :key="index">
                <div class="comp-question">
                  综合题{{ index + 1 }}.&nbsp;<span v-html="item.questionName"/>
                </div>
                <div v-for="(sub, subIndex) in item.content" :key="subIndex" class="comp-content-wrap">
                  <div>题目{{ subIndex + 1 }}.&nbsp;<span v-html="sub.contentName"/></div>
                  <div class="_question_score">
                    <div>该题：<el-input-number v-model="sub.questionScore" :min="0" :controls="false" size="mini" disabled/> 分</div>
                  </div>
                </div>
              </div>
            </div>
            <div v-else class="_question_option">
              <span style="padding-bottom: 20px">{{ q.questionAnswer }}</span>
            </div>
            <div v-if="q.questionType != 10" class="_question_score">
              <div class="flex-left">
                <div>该题：<span>{{ q.questionScore }}</span> 分</div>
                <div class="ml-20">
                  <span>备用：</span>
                  <el-radio
                    v-for="item in isSpareArr"
                    :key="item.value"
                    v-model="q.isSpare"
                    :label="item.value"
                    disabled
                    style="margin: 0 20px 0 0 !important;"
                  >{{ item.label }}</el-radio>
                </div>
                <div v-if="q.questionType == 4 || q.questionType == 5" class="ml-20">
                  <span>题目作答方式：</span>
                  <el-radio
                    v-for="item in answeringMethod"
                    :key="item.value"
                    v-model="q.questionAnswerMethod"
                    :label="item.value"
                    disabled
                    style="margin: 0 20px 0 0 !important;"
                  >{{ item.label }}</el-radio>
                </div>
              </div>
              <a href="javascript:;" @click="toDetail('simulation', q.id)">
                查看详情
              </a>
            </div>
          </div>
        </div>
        <el-empty
          v-else
          :image="img"
          :image-size="110"
          style="margin: 100px auto"
          description="暂无数据"
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import questionConf from '../../questionBank/config.js'
export default {
  props: {
    id: {
      type: [Number, String]
    },
    data: {
      type: Object
    }
  },
  data() {
    return {
      questionConf: questionConf,
      optionLabel: Array.from({ length: 26 }, (_, index) => String.fromCharCode('A'.charCodeAt(0) + index)),
      activeName: 'theory',
      questionType: '',
      theoryQuestionList: [],
      targetQuestionList: [],
      emulationQuestionList: [],
      questionTypeArr: [],
      img: require('@/assets/empty_state.png'),
      isSpareArr: [ // 备用题map
        { label: '是', value: 1 },
        { label: '否', value: 0 }
      ],
      answeringMethod: [
        { label: '提交flag', value: 1 },
        { label: '上传修补包', value: 2 }
      ]
    }
  },
  computed: {
    getQuestionNum() {
      let emulationNum = 0
      this.emulationQuestionList.forEach(item => {
        if (item.questionType == 10 && item.combinationQuestionBOS) {
          emulationNum += item.combinationQuestionBOS.length
        } else {
          emulationNum += 1
        }
      })
      return this.theoryQuestionList.length + this.targetQuestionList.length + emulationNum
    },
    getScore() {
      let score = 0
      this.theoryQuestionList.forEach(item => {
        if (item.questionScore) {
          score = score + item.questionScore
        }
      })
      this.targetQuestionList.forEach(item => {
        if (item.questionScore) {
          score = score + item.questionScore
        }
      })
      this.emulationQuestionList.forEach(item => {
        // 组合题的分数计算的是各个小题的分数
        if (item.questionType == 10 && item.combinationQuestionBOS) {
          item.combinationQuestionBOS.forEach(comp => {
            comp.content.forEach(con => {
              if (con.questionScore) {
                score += con.questionScore
              }
            })
          })
        } else {
          if (item.questionScore) {
            score = score + item.questionScore
          }
        }
      })
      return score
    }
  },
  mounted() {
    this.screenQuestionType()
    this.handleClick()
  },
  methods: {
    toDetail(name, id) {
      this.$router.push({ name: `${name}Detail`, params: { id: id, view: 'overview' }})
    },
    screenQuestionType() {
      if (this.questionType) {
        if (this.activeName === 'theory') {
          this.theoryQuestionList = this.data.questionList.filter(item => { return item.bankType == 1 && item.questionType == this.questionType })
        } else if (this.activeName === 'target') {
          this.targetQuestionList = this.data.questionList.filter(item => { return item.bankType == 2 && item.questionType == this.questionType })
        } else {
          this.emulationQuestionList = this.data.questionList.filter(item => { return item.bankType == 3 && item.questionType == this.questionType })
        }
      } else {
        this.theoryQuestionList = this.data.questionList.filter(item => { return item.bankType == 1 })
        this.targetQuestionList = this.data.questionList.filter(item => { return item.bankType == 2 })
        this.emulationQuestionList = this.data.questionList.filter(item => { return item.bankType == 3 })
      }
    },
    handleClick() {
      this.questionType = ''
      let typeMap = []
      if (this.activeName === 'theory') { // 理论
        typeMap = ['1', '2', '3', '4', '7', '8']
      } else if (this.activeName === 'target') { // 靶机
        typeMap = ['4', '5', '9', '6']
      } else if (this.activeName === 'emulation') { // 仿真
        typeMap = ['1', '2', '3', '4', '7', '8', '10']
      }
      const questionTypeArr = []
      typeMap.forEach(item => {
        const val = questionConf.questionTypeArr.find(val => val.value == item)
        if (val) {
          questionTypeArr.push(val)
        }
      })
      this.questionTypeArr = questionTypeArr
      this.screenQuestionType()
    }
  }
}
</script>
<style lang="scss" scoped>
.detail-question-bank {
  position: relative;
  ._paper_search {
    position: absolute;
    right: 30px;
    display: flex;
    ._paper_search_1 {
      font-size: 14px;
      font-weight: 500;
      color: #333;
    }
  }
  /deep/ .el-tabs {
    display: flex;
    flex-direction: column;
    height: 100%;
    .el-tabs__header {
      .el-tabs__item {
        margin-right: 0;
        border: none !important;
      }
    }
    .el-tabs__content {
      overflow-y: auto;
    }
    ._question_list {
      ._question_item {
        padding: 15px 20px;
        min-height: 90px;
        background: #ffffff;
        display: flex;
        flex-direction: column;
        font-size: 14px;
        color: #4e5969;
        position: relative;
        border: 1px solid #e5e6eb;
        border-radius: 4px;
        margin-bottom: 10px;
        ._question_option {
          margin-top: 10px;
          margin-left: 15px;
          font-size: 14px;
          color: #4e5969;
          display: flex;
          flex-direction: column;
          line-height: 22px;
          word-break: break-all;
          .el-radio {
            margin-bottom: 8px;
            display: flex;
            align-items: flex-start;
            .el-radio__label {
              font-size: 14px;
              color: #4e5969;
              white-space: normal;
              word-break: break-all;
            }
          }
          .el-checkbox {
            margin-bottom: 8px;
            display: flex;
            align-items: flex-start;
            .el-checkbox__label {
              font-size: 14px;
              color: #4e5969;
              white-space: normal;
              word-break: break-all;
            }
          }
        }
        ._question_score {
          border-top: 1px solid #e5e6eb;
          padding: 10px 0 0 0 ;
          display: flex;
          align-items: center;
          justify-content: space-between;
          ._question_delete {
            color: #F56C6C;
            cursor: pointer;
          }
        }
        ._question_item_content {
          display: flex;
          // max-height: 200px;
          // overflow-y: auto;
          overflow-x: auto;
          margin-right: 55px;
        }
        ._question_item_type {
          position: absolute;
          right: 15px;
          top: 15px;
        }
        .combination-question-wrap {
          >div {
            border: 1px solid rgb(229, 230, 235);
            margin: 5px 0px 10px;
            padding: 15px 20px 5px;
            .comp-question {
              display: flex;
              // max-height: 200px;
              // overflow-y: auto;
              overflow-x: auto;
              >span {
                flex: 1;
                word-break: break-all;
              }
            }
            .comp-content-wrap {
              border: 1px solid #e5e6eb;
              margin: 5px 0 10px;
              padding: 15px 20px;
              >div:first-child {
                display: flex;
                // max-height: 200px;
                // overflow-y: auto;
                overflow-x: auto;
                margin-bottom: 10px;
                >span {
                  flex: 1;
                  word-break: break-all;
                }
              }
            }
          }
        }
      }
      ._question_item_check {
        border: 1px solid var(--color-600);
      }
    }
  }
}
</style>

