<template>
  <div class="_paper_container">
    <div class="question_wrap">
      <el-tabs v-model="activeName" class="question_tabs">
        <el-tab-pane v-if="judgeQuestionList.length" :label="`判断题（${ judgeQuestionList.length }）`" name="judgeArr">
          <!-- 题目列表 -->
          <div v-if="judgeQuestionList.length" class="_question_list">
            <div
              v-for="(q, index) in judgeQuestionList"
              :class="`_question_item`"
              :key="q.id"
              :id="`qt_judgeArr${index}`"
            >
              <div>
                <div class="_question_item_content">
                  <div class="rich-text">{{ index + 1 }}.&nbsp;<span v-html="handleRichContent(q.content).contentWithoutImgs"/></div>
                  <el-image
                    v-if="handleRichContent(q.content).imgSrcArr.length > 0"
                    :src="handleRichContent(q.content).imgSrcArr[0]"
                    :preview-src-list="handleRichContent(q.content).imgSrcArr"
                    style="width: 485px; height: 300px"
                  />
                </div>
                <div class="_question_item_type">
                  <el-tag size="small">{{ questionConf.questionTypObj[q.questionType].label }}</el-tag>
                </div>
              </div>
              <div v-if="q.questionType == 3" class="_question_option">
                <el-radio-group
                  :value="q.questionUserAnswer"
                >
                  <el-radio label="A" disabled>正确</el-radio>
                  <el-radio label="B" disabled>错误</el-radio>
                </el-radio-group>
              </div>
              <div class="_question_score">
                <div>该题：<span>{{ q.questionScore }}</span> 分</div>
                <div v-if="showUserAnswer">学员答案：<span class="question-user-answer">{{ q.questionUserAnswer == '未作答' ? '未作答' : q.questionUserAnswer == 'A' ? '正确' : '错误' }}</span></div>
                <div>参考答案：<span>{{ q.questionAnswer == 'A' ? '正确' : q.questionAnswer == 'B' ? '错误' : '-' }}</span></div>
                <div>题目解析：<span style="display: inline-block" v-html="q.questionAnalysis || '-'"/></div>
                <div v-if="showUserAnswer">
                  <span>学员得分：</span>
                  <span>{{ q.questionStudentScore === null ? '0' : q.questionStudentScore }}&nbsp;分</span>
                </div>
              </div>
            </div>
          </div>
          <el-empty
            v-else
            :image="img"
            :image-size="110"
            style="margin: 100px auto"
            description="暂无数据"
          />
        </el-tab-pane>
        <el-tab-pane v-if="singleQuestionList.length" :label="`单选题（${ singleQuestionList.length }）`" name="singleArr">
          <div v-if="singleQuestionList.length" class="_question_list">
            <div
              v-for="(q, index) in singleQuestionList"
              :class="`_question_item`"
              :key="q.id"
              :id="`qt_singleArr${index}`"
            >
              <div>
                <div class="_question_item_content">
                  <div class="rich-text">{{ index + 1 }}.&nbsp;<span v-html="handleRichContent(q.content).contentWithoutImgs"/></div>
                  <el-image
                    v-if="handleRichContent(q.content).imgSrcArr.length > 0"
                    :src="handleRichContent(q.content).imgSrcArr[0]"
                    :preview-src-list="handleRichContent(q.content).imgSrcArr"
                    style="width: 485px; height: 300px"
                  />
                </div>
                <div class="_question_item_type">
                  <el-tag size="small">{{ questionConf.questionTypObj[q.questionType].label }}</el-tag>
                </div>
              </div>
              <div v-if="q.questionType == 1" class="_question_option">
                <el-radio-group
                  v-for="(op, i) in JSON.parse(q.questionOptions)"
                  :key="op"
                  :value="q.questionUserAnswer"
                >
                  <el-radio v-overflow-tooltip="{ content: op }" :label="optionLabel[i]" disabled>{{ optionLabel[i] }}. {{ op }}</el-radio>
                </el-radio-group>
              </div>
              <div class="_question_score">
                <div>该题：<span>{{ q.questionScore }}</span> 分</div>
                <div v-if="showUserAnswer">学员答案：<span class="question-user-answer">{{ q.questionUserAnswer || '-' }}</span></div>
                <div>参考答案：<span>{{ q.questionAnswer }}</span></div>
                <div>题目解析：<span style="display: inline-block" v-html="q.questionAnalysis || '-'"/></div>
                <div v-if="showUserAnswer">
                  <span>学员得分：</span>
                  <span>{{ q.questionStudentScore === null ? '0' : q.questionStudentScore }}&nbsp;分</span>
                </div>
              </div>
            </div>
          </div>
          <el-empty
            v-else
            :image="img"
            :image-size="110"
            style="margin: 100px auto"
            description="暂无数据"
          />
        </el-tab-pane>
        <el-tab-pane v-if="multiQuestionList.length" :label="`多选题（${ multiQuestionList.length }）`" name="multiArr">
          <div v-if="multiQuestionList.length" class="_question_list">
            <div
              v-for="(q, index) in multiQuestionList"
              :class="`_question_item`"
              :key="q.id"
              :id="`qt_multiArr${index}`"
            >
              <div>
                <div class="_question_item_content">
                  <div class="rich-text">{{ index + 1 }}.&nbsp;<span v-html="handleRichContent(q.content).contentWithoutImgs"/></div>
                  <el-image
                    v-if="handleRichContent(q.content).imgSrcArr.length > 0"
                    :src="handleRichContent(q.content).imgSrcArr[0]"
                    :preview-src-list="handleRichContent(q.content).imgSrcArr"
                    style="width: 485px; height: 300px"
                  />
                </div>
                <div class="_question_item_type">
                  <el-tag size="small">{{ questionConf.questionTypObj[q.questionType].label }}</el-tag>
                </div>
              </div>
              <div v-if="q.questionType == 2" class="_question_option">
                <el-checkbox-group :value="q.questionUserAnswer && q.questionUserAnswer.split('')">
                  <el-checkbox
                    v-overflow-tooltip="{ content: op }"
                    v-for="(op, i) in JSON.parse(q.questionOptions)"
                    :key="op"
                    :label="optionLabel[i]" disabled
                  >{{ optionLabel[i] }}. {{ op }}</el-checkbox
                  >
                </el-checkbox-group>
              </div>
              <div class="_question_score">
                <div>该题：<span>{{ q.questionScore }}</span> 分</div>
                <div v-if="showUserAnswer">学员答案：<span class="question-user-answer">{{ q.questionUserAnswer || '-' }}</span></div>
                <div>参考答案：<span>{{ q.questionAnswer }}</span></div>
                <div>题目解析：<span style="display: inline-block" v-html="q.questionAnalysis || '-'"/></div>
                <div v-if="showUserAnswer">
                  <span>学员得分：</span>
                  <span>{{ q.questionStudentScore === null ? '0' : q.questionStudentScore }}&nbsp;分</span>
                </div>
              </div>
            </div>
          </div>
          <el-empty
            v-else
            :image="img"
            :image-size="110"
            style="margin: 100px auto"
            description="暂无数据"
          />
        </el-tab-pane>
        <el-tab-pane v-if="ctfQuestionList.length" :label="`CTF题（${ ctfQuestionList.length }）`" name="CTFArr">
          <div v-if="ctfQuestionList.length" class="_question_list">
            <div
              v-for="(q, index) in ctfQuestionList"
              :class="`_question_item`"
              :key="q.id"
              :id="`qt_CTFArr${index}`"
            >
              <div>
                <div class="_question_item_content">
                  <div class="rich-text">{{ index + 1 }}.&nbsp;<span v-html="handleRichContent(q.content).contentWithoutImgs"/></div>
                  <el-image
                    v-if="handleRichContent(q.content).imgSrcArr.length > 0"
                    :src="handleRichContent(q.content).imgSrcArr[0]"
                    :preview-src-list="handleRichContent(q.content).imgSrcArr"
                    style="width: 485px; height: 300px"
                  />
                </div>
                <div class="_question_item_type">
                  <el-tag size="small">{{ questionConf.questionTypObj[q.questionType].label }}</el-tag>
                </div>
              </div>
              <div class="_question_score">
                <div>该题：<span>{{ q.questionScore }}</span> 分</div>
                <div v-if="showUserAnswer">学员答案：<span class="question-user-answer">{{ q.questionUserAnswer || '-' }}</span></div>
                <div>参考答案：<span>{{ q.questionAnswer }}</span></div>
                <div>题目解析：<span style="display: inline-block" v-html="q.questionAnalysis || '-'"/></div>
                <div v-if="q.questionFileUrl" style="text-align: left;margin-bottom: 10px;">
                  <span>题目附件：</span>
                  <el-link :underline="false" type="primary" @click="handleDown(q.questionFileUrl, q.questionFileName)"><i class="el-icon-view el-icon-download"/>{{ q.questionFileName }}</el-link>
                </div>
                <div v-if="showUserAnswer">
                  <span>学员附件：</span>
                  <el-link v-if="q.fileUrl" :underline="false" type="primary" @click="handleDown(q.fileUrl, q.fileName)"><i class="el-icon-view el-icon-download"/>{{ q.fileName }}</el-link>
                  <span v-else>-</span>
                </div>
                <div v-if="showUserAnswer">
                  <span>学员得分：</span>
                  <el-input
                    v-model.trim="q.questionStudentScore"
                    :min="0"
                    :disabled="studentDetail"
                    :max="q.questionScore"
                    style="width: 100px"
                    class="inputNumber"
                    size="mini"
                    type="number"
                    placeholder="请输入分数"
                    oninput="value = value.replace(/[^0-9.]/g, '')"
                    @input="handleStudentScore(ctfQuestionList[index].questionStudentScore, q.questionScore, 'ctf', index)"
                  />
                  <span>分</span>
                </div>
              </div>
            </div>
          </div>
          <el-empty
            v-else
            :image="img"
            :image-size="110"
            style="margin: 100px auto"
            description="暂无数据"
          />
        </el-tab-pane>
        <el-tab-pane v-if="awdQuestionList.length" :label="`AWD题（${ awdQuestionList.length }）`" name="AWDArr">
          <div v-if="awdQuestionList.length" class="_question_list">
            <div
              v-for="(q, index) in awdQuestionList"
              :class="`_question_item`"
              :key="q.id"
              :id="`qt_singleArr${index}`"
            >
              <div>
                <div class="_question_item_content">
                  <div class="rich-text">{{ index + 1 }}.&nbsp;<span v-html="handleRichContent(q.content).contentWithoutImgs"/></div>
                  <el-image
                    v-if="handleRichContent(q.content).imgSrcArr.length > 0"
                    :src="handleRichContent(q.content).imgSrcArr[0]"
                    :preview-src-list="handleRichContent(q.content).imgSrcArr"
                    style="width: 485px; height: 300px"
                  />
                </div>
                <div class="_question_item_type">
                  <el-tag size="small">{{ questionConf.questionTypObj[q.questionType].label }}</el-tag>
                </div>
              </div>
              <div class="_question_score">
                <div>该题：<span>{{ q.questionScore }}</span> 分</div>
                <div v-if="showUserAnswer">学员答案：<span class="question-user-answer">{{ q.questionUserAnswer || '-' }}</span></div>
                <div>参考答案：<span>{{ q.questionAnswer }}</span></div>
                <div>题目解析：<span style="display: inline-block" v-html="q.questionAnalysis || '-'"/></div>
                <div v-if="q.questionFileUrl" style="text-align: left;margin-bottom: 10px;">
                  <span>题目附件：</span>
                  <el-link :underline="false" type="primary" @click="handleDown(q.questionFileUrl, q.questionFileName)"><i class="el-icon-view el-icon-download"/>{{ q.questionFileName }}</el-link>
                </div>
                <div v-if="showUserAnswer">
                  <span>学员附件：</span>
                  <el-link v-if="q.fileUrl" :underline="false" type="primary" @click="handleDown(q.fileUrl, q.fileName)"><i class="el-icon-view el-icon-download"/>{{ q.fileName }}</el-link>
                  <span v-else>-</span>
                </div>
                <div v-if="showUserAnswer">
                  <span>学员得分：</span>
                  <el-input
                    v-model.trim="q.questionStudentScore"
                    :min="0"
                    :max="q.questionScore"
                    :disabled="studentDetail"
                    style="width: 100px"
                    class="inputNumber"
                    size="mini"
                    type="number"
                    placeholder="请输入分数"
                    oninput="value = value.replace(/[^0-9.]/g, '')"
                    @input="handleStudentScore(awdQuestionList[index].questionStudentScore, q.questionScore, 'awd', index)"
                  />
                  <span>分</span>
                </div>
              </div>
            </div>
          </div>
          <el-empty
            v-else
            :image="img"
            :image-size="110"
            style="margin: 100px auto"
            description="暂无数据"
          />
        </el-tab-pane>
        <el-tab-pane v-if="loopholeQuestionList.length" :label="`漏洞题（${ loopholeQuestionList.length }）`" name="loopholeArr">
          <div v-if="loopholeQuestionList.length" class="_question_list">
            <div
              v-for="(q, index) in loopholeQuestionList"
              :class="`_question_item`"
              :key="q.id"
              :id="`qt_loopholeArr${index}`"
            >
              <div>
                <div class="_question_item_content">
                  <div class="rich-text">{{ index + 1 }}.&nbsp;<span v-html="handleRichContent(q.content).contentWithoutImgs"/></div>
                  <el-image
                    v-if="handleRichContent(q.content).imgSrcArr.length > 0"
                    :src="handleRichContent(q.content).imgSrcArr[0]"
                    :preview-src-list="handleRichContent(q.content).imgSrcArr"
                    style="width: 485px; height: 300px"
                  />
                </div>
                <div class="_question_item_type">
                  <el-tag size="small">{{ questionConf.questionTypObj[q.questionType].label }}</el-tag>
                </div>
              </div>
              <div class="_question_score">
                <div>该题：<span>{{ q.questionScore }}</span> 分</div>
                <div v-if="showUserAnswer">学员答案：<span class="question-user-answer">{{ q.questionUserAnswer || '-' }}</span></div>
                <div>参考答案：<span>{{ q.questionAnswer }}</span></div>
                <div>题目解析：<span style="display: inline-block" v-html="q.questionAnalysis || '-'"/></div>
                <div v-if="q.questionFileUrl" style="text-align: left;margin-bottom: 10px;">
                  <span>题目附件：</span>
                  <el-link :underline="false" type="primary" @click="handleDown(q.questionFileUrl, q.questionFileName)"><i class="el-icon-view el-icon-download"/>{{ q.questionFileName }}</el-link>
                </div>
                <div v-if="showUserAnswer">
                  <span>学员附件：</span>
                  <el-link v-if="q.fileUrl" :underline="false" type="primary" @click="handleDown(q.fileUrl, q.fileName)"><i class="el-icon-view el-icon-download"/>{{ q.fileName }}</el-link>
                  <span v-else>-</span>
                </div>
                <div v-if="showUserAnswer">
                  <span>学员得分：</span>
                  <el-input
                    v-model.trim="q.questionStudentScore"
                    :min="0"
                    :max="q.questionScore"
                    :disabled="studentDetail"
                    style="width: 100px"
                    class="inputNumber"
                    size="mini"
                    type="number"
                    placeholder="请输入分数"
                    oninput="value = value.replace(/[^0-9.]/g, '')"
                    @input="handleStudentScore(loopholeQuestionList[index].questionStudentScore, q.questionScore, 'loophole', index)"
                  />
                  <span>分</span>
                </div>
              </div>
            </div>
          </div>
          <el-empty
            v-else
            :image="img"
            :image-size="110"
            style="margin: 100px auto"
            description="暂无数据"
          />
        </el-tab-pane>
        <el-tab-pane v-if="fillBlankQuestionList.length" :label="`填空题（${ fillBlankQuestionList.length }）`" name="fillBlankArr">
          <div v-if="fillBlankQuestionList.length" class="_question_list">
            <div
              v-for="(q, index) in fillBlankQuestionList"
              :class="`_question_item`"
              :key="q.id"
              :id="`qt_fillBlankArr${index}`"
            >
              <div>
                <div class="_question_item_content">
                  <div class="rich-text">{{ index + 1 }}.&nbsp;<span v-html="handleRichContent(q.content).contentWithoutImgs"/></div>
                  <el-image
                    v-if="handleRichContent(q.content).imgSrcArr.length > 0"
                    :src="handleRichContent(q.content).imgSrcArr[0]"
                    :preview-src-list="handleRichContent(q.content).imgSrcArr"
                    style="width: 485px; height: 300px"
                  />
                </div>
                <div class="_question_item_type">
                  <el-tag size="small">{{ questionConf.questionTypObj[q.questionType].label }}</el-tag>
                </div>
              </div>
              <div class="_question_score">
                <div>该题：<span>{{ q.questionScore }}</span> 分</div>
                <div v-if="showUserAnswer">学员答案：<span class="question-user-answer">{{ q.questionUserAnswer || '-' }}</span></div>
                <div>参考答案：<span>{{ q.questionAnswer }}</span></div>
                <div>题目解析：<span style="display: inline-block" v-html="q.questionAnalysis || '-'"/></div>
                <div v-if="q.questionFileUrl" style="text-align: left;margin-bottom: 10px;">
                  <span>题目附件：</span>
                  <el-link :underline="false" type="primary" @click="handleDown(q.questionFileUrl, q.questionFileName)"><i class="el-icon-view el-icon-download"/>{{ q.questionFileName }}</el-link>
                </div>
                <div v-if="showUserAnswer">
                  <span>学员附件：</span>
                  <el-link v-if="q.fileUrl" :underline="false" type="primary" @click="handleDown(q.fileUrl, q.fileName)"><i class="el-icon-view el-icon-download"/>{{ q.fileName }}</el-link>
                  <span v-else>-</span>
                </div>
                <div v-if="showUserAnswer">
                  <span>学员得分：</span>
                  <el-input
                    v-model.trim="q.questionStudentScore"
                    :min="0"
                    :max="q.questionScore"
                    :disabled="studentDetail"
                    style="width: 100px"
                    class="inputNumber"
                    size="mini"
                    type="number"
                    placeholder="请输入分数"
                    oninput="value = value.replace(/[^0-9.]/g, '')"
                    @input="handleStudentScore(fillBlankQuestionList[index].questionStudentScore, q.questionScore, 'fillBlank', index)"
                  />
                  <span>分</span>
                </div>
              </div>
            </div>
          </div>
          <el-empty
            v-else
            :image="img"
            :image-size="110"
            style="margin: 100px auto"
            description="暂无数据"
          />
        </el-tab-pane>
        <el-tab-pane v-if="otherQuestionList.length" :label="`其他题（${ otherQuestionList.length }）`" name="otherArr">
          <div v-if="otherQuestionList.length" class="_question_list">
            <div
              v-for="(q, index) in otherQuestionList"
              :class="`_question_item`"
              :key="q.id"
              :id="`qt_otherArr${index}`"
            >
              <div>
                <div class="_question_item_content">
                  <div class="rich-text">{{ index + 1 }}.&nbsp;<span v-html="handleRichContent(q.content).contentWithoutImgs"/></div>
                  <el-image
                    v-if="handleRichContent(q.content).imgSrcArr.length > 0"
                    :src="handleRichContent(q.content).imgSrcArr[0]"
                    :preview-src-list="handleRichContent(q.content).imgSrcArr"
                    style="width: 485px; height: 300px"
                  />
                </div>
                <div class="_question_item_type">
                  <el-tag size="small">{{ questionConf.questionTypObj[q.questionType].label }}</el-tag>
                </div>
              </div>
              <div class="_question_score">
                <div>该题：<span>{{ q.questionScore }}</span> 分</div>
                <div v-if="showUserAnswer">学员答案：<span class="question-user-answer">{{ q.questionUserAnswer || '-' }}</span></div>
                <div>参考答案：<span>{{ q.questionAnswer }}</span></div>
                <div>题目解析：<span style="display: inline-block" v-html="q.questionAnalysis || '-'"/></div>
                <div v-if="q.questionFileUrl" style="text-align: left;margin-bottom: 10px;">
                  <span>题目附件：</span>
                  <el-link :underline="false" type="primary" @click="handleDown(q.questionFileUrl, q.questionFileName)"><i class="el-icon-view el-icon-download"/>{{ q.questionFileName }}</el-link>
                </div>
                <div v-if="showUserAnswer">
                  <span>学员附件：</span>
                  <el-link v-if="q.fileUrl" :underline="false" type="primary" @click="handleDown(q.fileUrl, q.fileName)"><i class="el-icon-view el-icon-download"/>{{ q.fileName }}</el-link>
                  <span v-else>-</span>
                </div>
                <div v-if="showUserAnswer">
                  <span>学员得分：</span>
                  <el-input
                    v-model.trim="q.questionStudentScore"
                    :min="0"
                    :max="q.questionScore"
                    :disabled="studentDetail"
                    style="width: 100px"
                    class="inputNumber"
                    size="mini"
                    type="number"
                    placeholder="请输入分数"
                    oninput="value = value.replace(/[^0-9.]/g, '')"
                    @input="handleStudentScore(otherQuestionList[index].questionStudentScore, q.questionScore, 'other', index)"
                  />
                  <span>分</span>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
        <el-empty
          v-if="!questionList.length"
          :image="img"
          :image-size="110"
          style="margin: 100px auto"
          description="暂无数据"
        />
      </el-tabs>
      <div v-if="answerSheet" class="content-wrap-layout _question_sn">
        <div v-if="showAnswerInfo" class="_paper_header">
          <div class="_paper_search">
            <div v-if="pageType !== 'testPaper'" class="_paper_search_1">
              答题时长
              <span style="font-weight: bold;font-size: 18px; margin-right: 10px;">
                {{ answerTimeHms }}
              </span>
            </div>
            <div class="_paper_search_1">
              总题数
              <span style="font-weight: bold; color: var(--color-600); font-size: 18px; margin-right: 10px;">
                {{ getQuestionNum }}
              </span>
            </div>
            <div class="_paper_search_1">
              题目总分数
              <span style="font-weight: bold; color: var(--color-600); font-size: 18px;margin-right: 10px;">
                {{ getScore }}
              </span>
            </div>
            <div v-if="answerSheet && courseRole == 1" class="_paper_search_1">
              总成绩
              <span style="font-weight: bold; color: var(--color-600); font-size: 18px;">
                {{ getStudentScore }}
              </span>
            </div>
          </div>
        </div>
        <div class="content-wrap-layout question_sn">
          <div class="paper_info_title">答题卡</div>
          <div class="paper_content">
            <template v-if="questionList.length > 0">
              <!-- 判断题 -->
              <QuestionNo :data="judgeQuestionList" @click="handleOrderClick"/>
              <!-- 单选题 -->
              <QuestionNo :data="singleQuestionList" @click="handleOrderClick"/>
              <!-- 多选题 -->
              <QuestionNo :data="multiQuestionList" @click="handleOrderClick"/>
              <!-- CTF题 -->
              <QuestionNo :data="ctfQuestionList" @click="handleOrderClick"/>
              <!-- AWD题 -->
              <QuestionNo :data="awdQuestionList" @click="handleOrderClick"/>
              <!-- 漏洞题 -->
              <QuestionNo :data="loopholeQuestionList" @click="handleOrderClick"/>
              <!-- 填空题 -->
              <QuestionNo :data="fillBlankQuestionList" @click="handleOrderClick"/>
              <!-- 其他题 -->
              <QuestionNo :data="otherQuestionList" @click="handleOrderClick"/>
            </template>
            <el-empty
              v-else
              :image="img"
              :image-size="110"
              style="margin: 100px auto"
              description="暂无数据"
            />
          </div>
          <div v-if="showFooter" class="footer">
            <!-- 教师保存修订后分数 -->
            <el-button type="primary" size="small" class="save-button" @click="saveStudentScore">保存</el-button>
          </div>
        </div>
      </div>
    </div>
    <div v-if="showAnswerInfo && !answerSheet" class="_paper_header">
      <el-form ref="form" label-position="left" label-width="100px">
        <el-form-item v-if="pageType !== 'testPaper'" label="答题时长">
          <span style="font-weight: bold; font-size: 18px">{{ answerTimeHms }}</span>
        </el-form-item>
        <el-form-item v-if="pageType !== 'testPaper'" label="是否是考试">
          <el-switch v-model="isExamMode" :active-value="1" :inactive-value="0" disabled/>
        </el-form-item>
        <el-form-item label="总题数">
          <span style="font-weight: bold; color: var(--color-600); font-size: 18px;">{{ getQuestionNum }}</span>
        </el-form-item>
        <el-form-item label="题目总分数">
          <span style="font-weight: bold; color: var(--color-600); font-size: 18px;">{{ getScore }}</span>
        </el-form-item>
        <el-form-item label="总成绩">
          <span style="font-weight: bold; color: var(--color-600); font-size: 18px;">{{ getStudentScore }}</span>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
import { teacherEditPracticeScore, teacherEditExamPracticeScore } from '@/api/teacher/index.js'
import questionConf from '../QuestionDetail/config.js'
import { formatSecondsToHHMMSS, handleRichContent } from '@/utils/index.js'
import QuestionNo from './QuestionNo.vue'

export default {
  components: {
    QuestionNo
  },
  props: {
    // 学员点击 我的成绩 查看详情
    studentDetail: {
      type: Boolean,
      default: () => {
        return false
      }
    },
    // 题目列表
    questionList: {
      type: Array,
      default: () => {
        return []
      }
    },
    // 有答题卡
    answerSheet: {
      type: Boolean,
      default: () => {
        return false
      }
    },
    // 是否回显学员答案
    showUserAnswer: {
      type: Boolean,
      default: true
    },
    // 是否回显答题信息
    showAnswerInfo: {
      type: Boolean,
      default: true
    },
    // 2 是教师.助教课程表展示（带学员答案）   1 课程内容详情展示（不带学员答案）
    courseRole: {
      type: Number,
      default: () => {
        return 1
      }
    },
    // 状态 '1': '未开始', '2': '已结束', '3': '进行中'
    status: {
      type: [Number, String],
      default: 2
    },
    // 答题时间
    answerTime: {
      type: Number,
      default: 0
    },
    // 模拟练习(testPaper)、课程(simulation)
    pageType: {
      type: String,
      default: 'simulation'
    }
  },
  data() {
    return {
      questionConf: questionConf,
      activeName: '',
      img: require('@/assets/empty_state.png'),
      optionLabel: Array.from({ length: 26 }, (_, index) => String.fromCharCode('A'.charCodeAt(0) + index)),
      questionType: '',
      handleRichContent,
      isExamMode: this.$route.query.isExamMode || 0 // 考试模式
    }
  },
  provide() {
    return {
      'questionDetail': this
    }
  },
  computed: {
    // 是否显示底部保存按钮
    showFooter() {
      return this.questionList.length > 0 && (this.activeName != 'multiArr' && this.activeName != 'singleArr' && this.activeName != 'judgeArr') && !this.studentDetail
    },
    // 答题时间
    answerTimeHms() {
      const answerTime = this.answerTime
      if (answerTime === 0) return '不限时'
      return formatSecondsToHHMMSS(answerTime)
    },
    singleQuestionList() {
      return this.questionList.filter(item => { return item.questionType == '1' })
    },
    multiQuestionList() {
      return this.questionList.filter(item => { return item.questionType == '2' })
    },
    judgeQuestionList() {
      return this.questionList.filter(item => { return item.questionType == '3' })
    },
    ctfQuestionList() {
      return this.questionList.filter(item => { return item.questionType == '4' })
    },
    awdQuestionList() {
      return this.questionList.filter(item => { return item.questionType == '5' })
    },
    otherQuestionList() {
      return this.questionList.filter(item => { return item.questionType == '6' })
    },
    fillBlankQuestionList() {
      return this.questionList.filter(item => { return item.questionType == '7' })
    },
    shortAnswerQuestionList() {
      return this.questionList.filter(item => { return item.questionType == '8' })
    },
    loopholeQuestionList() {
      return this.questionList.filter(item => { return item.questionType == '9' })
    },
    questionTypes() {
      return [
        this.judgeQuestionList.length > 0 ? 'judgeArr' : null,
        this.singleQuestionList.length > 0 ? 'singleArr' : null,
        this.multiQuestionList.length > 0 ? 'multiArr' : null,
        this.ctfQuestionList.length > 0 ? 'CTFArr' : null,
        this.awdQuestionList.length > 0 ? 'AWDArr' : null,
        this.loopholeQuestionList.length > 0 ? 'loopholeArr' : null,
        this.fillBlankQuestionList.length > 0 ? 'fillBlankArr' : null,
        this.otherQuestionList.length > 0 ? 'otherArr' : null
      ]
    },
    numberedQuestionTypes() {
      return this.questionTypes.filter(type => type !== null).map((type, index) => ({ index, type }))
    },
    getQuestionNum() {
      return this.singleQuestionList.length +
      this.multiQuestionList.length +
      this.judgeQuestionList.length +
      this.ctfQuestionList.length +
      this.awdQuestionList.length +
      this.otherQuestionList.length +
      this.fillBlankQuestionList.length +
      this.shortAnswerQuestionList.length +
      this.loopholeQuestionList.length
    },
    getScore() {
      let score = 0
      this.singleQuestionList.forEach(item => {
        if (item.questionScore) {
          score = score + Number(item.questionScore)
        }
      })
      this.multiQuestionList.forEach(item => {
        if (item.questionScore) {
          score = score + Number(item.questionScore)
        }
      })
      this.judgeQuestionList.forEach(item => {
        if (item.questionScore) {
          score = score + Number(item.questionScore)
        }
      })
      this.ctfQuestionList.forEach(item => {
        if (item.questionScore) {
          score = score + Number(item.questionScore)
        }
      })
      this.awdQuestionList.forEach(item => {
        if (item.questionScore) {
          score = score + Number(item.questionScore)
        }
      })
      this.otherQuestionList.forEach(item => {
        if (item.questionScore) {
          score = score + Number(item.questionScore)
        }
      })
      this.fillBlankQuestionList.forEach(item => {
        if (item.questionScore) {
          score = score + Number(item.questionScore)
        }
      })
      this.shortAnswerQuestionList.forEach(item => {
        if (item.questionScore) {
          score = score + Number(item.questionScore)
        }
      })
      this.loopholeQuestionList.forEach(item => {
        if (item.questionScore) {
          score = score + Number(item.questionScore)
        }
      })
      return score
    },
    getStudentScore() {
      let score = 0
      this.questionList.forEach(item => {
        if (item.questionStudentScore) {
          score = score + Number(item.questionStudentScore)
        }
      })
      return score
    }
  },
  watch: {
    numberedQuestionTypes: {
      handler(newVal) {
        if (newVal) {
          this.activeName = newVal[0].type
        }
      },
      deep: true
    }
  },
  mounted() {
    this.activeName = this.numberedQuestionTypes[0].type
  },
  methods: {
    handleOrderClick(data, index) {
      if (data.questionType == '3') {
        this.activeName = 'judgeArr'
      } else if (data.questionType == '1') {
        this.activeName = 'singleArr'
      } else if (data.questionType == '2') {
        this.activeName = 'multiArr'
      } else if (data.questionType == '4') {
        this.activeName = 'CTFArr'
      } else if (data.questionType == '5') {
        this.activeName = 'AWDArr'
      } else if (data.questionType == '6') {
        this.activeName = 'otherArr'
      } else if (data.questionType == '7') {
        this.activeName = 'fillBlankArr'
      } else if (data.questionType == '9') {
        this.activeName = 'loopholeArr'
      }
      const el = document.getElementById(`qt_${this.activeName}${index}`)
      this.$nextTick(() => {
        el && el.scrollIntoView({
          behavior: 'smooth'
        })
      })
    },
    handleStudentScore(val, questionScore, type, index) {
      // 检查输入值是否在0到题目分数之间
      if (parseFloat(val) < 0 || parseFloat(val) > questionScore) {
        this.$set(this[`${type}QuestionList`][index], 'questionStudentScore', '')
        this.$message.warning(`输入值必须在0到${questionScore}之间`)
      } else {
        this.$set(this[`${type}QuestionList`][index], 'questionStudentScore', val)
      }
    },
    // 附件下载
    async handleDown(fileUrl, fileName) {
      const response = await fetch(fileUrl, {
        method: 'get',
        responseType: 'blob'
      })
      const blob = await response.blob()
      const a = document.createElement('a')
      const URL = window.URL || window.webkitURL
      const herf = URL.createObjectURL(blob)
      a.href = herf
      a.download = fileName
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(herf)
    },
    // 修改学生题目分数
    saveStudentScore() {
      this.studentSubmitQuestionBoList = []
      const questionType = ['ctfQuestionList', 'awdQuestionList', 'loopholeQuestionList', 'otherQuestionList', 'fillBlankQuestionList']
      questionType.map((item, index) => {
        this[`${item}`].map((data, i) => {
          this.studentSubmitQuestionBoList.push({ detailId: data.id, score: Number(data.questionStudentScore) })
        })
      })
      const params = {
        schedulingCode: this.$route.query.schedulingCode,
        studentSubmitQuestionBoList: this.studentSubmitQuestionBoList
      }
      const fetchUrl = this.pageType == 'testPaper' ? teacherEditExamPracticeScore : teacherEditPracticeScore
      fetchUrl(params).then(res => {
        if (res.code === 0 || res.code === 200) {
          this.$message.success('修改成功')
          this.$emit('queryPracticeAffair', 'refresh')
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
._paper_container {
  height: 100%;
  width: 100%;
  background: #fff;
  display: flex;
  .question_wrap {
    flex: 1;
    min-width: 0;
    display: flex;
    height: 100%;
    border: 1px solid var(--neutral-300);
    border-radius: 2px;
  }
  /deep/.question_tabs {
    padding: 15px;
    padding-top: 5px;
    .el-tabs__nav-wrap::after {
      height: 1px;
      background-color: #e5e6eb;
    }
    .el-tabs__header {
      margin-bottom: 15px;
    }
    .el-tabs__item {
      font-size: 14px;
      font-weight: 600;
    }
  }
  .question-user-answer {
    word-break: break-all;
  }
  ._paper_header {
    width: 300px;
    height: fit-content;
    margin-left: 10px;
    display: flex;
    flex-direction: column;
    border: 1px solid var(--neutral-300);
    padding: 15px;
    padding-top: 10px;
    .el-form {
      padding: 0;
      ::v-deep .el-form-item {
        margin-bottom: 0;
        .el-form-item__label {
          color: #333;
        }
      }
    }
    ._paper_search {
      display: flex;
      flex-direction: column;
      ._paper_search_1 {
        font-size: 16px;
        font-weight: 500;
        color: #333;
      }
    }
  }
  /deep/ .el-tabs {
    overflow: hidden;
    min-height: 0;
    flex: 1;
    display: flex;
    flex-direction: column;
    .el-tabs__content {
      flex: 1;
      min-height: 0;
      overflow-y: auto;
    }
  }
  ._question_list {
    ._question_item {
      padding: 15px 20px;
      min-height: 90px;
      background: #ffffff;
      display: flex;
      flex-direction: column;
      font-size: 14px;
      color: #4e5969;
      position: relative;
      border: 1px solid #e5e6eb;
      border-radius: 4px;
      margin-bottom: 10px;
      ._question_option {
        margin-top: 10px;
        margin-left: 15px;
        font-size: 14px;
        color: #4e5969;
        display: flex;
        flex-direction: column;
        line-height: 22px;
        word-break: break-all;
        ::v-deep .el-radio {
          margin-bottom: 12px;
          display: flex;
          align-items: center;
          .el-radio__label {
            font-size: 14px;
            color: #4e5969;
            width: 200px;
            flex: 1;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
        }
        ::v-deep .el-checkbox {
          margin-bottom: 12px;
          display: flex;
          align-items: center;
          .el-checkbox__label {
            font-size: 14px;
            color: #4e5969;
            width: 200px;
            flex: 1;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
        }
      }
      ._question_score {
        border-top: 1px solid #e5e6eb;
        padding: 10px 0 0 0 ;
        ._question_delete {
          color: #F56C6C;
          cursor: pointer;
        }
      }
      ._question_item_content {
        overflow-x: auto;
        margin-right: 55px;
        .rich-text {
          display: flex;
          justify-content: flex-start;
        }
        ::v-deep .el-image {
          margin: 10px 0 0 16px;
        }
      }
      ._question_item_type {
        position: absolute;
        right: 15px;
        top: 15px;
      }
    }
    img {
      position: absolute;
      left: 20px;
      top: 23px;
    }

  }
}
._question_sn {
  width: 318px;
  height: 100%;
  background: #ffffff;
}
._question_sn_order {
  margin-left: 20px;
  margin-top: 29px;
  display: flex;
  flex-wrap: wrap;
}
.question_sn {
  flex: 1;
  overflow: hidden;
  border: 1px solid #ebebeb;
  margin: 10px 10px 10px 0;
  .paper_info_title {
    font-size: 16px;
    font-weight: 600;
    padding: 8px 30px;
  }
  .paper_content {
    border-top: 1px solid #ebebeb;
    padding: 15px 25px;
    flex: 1;
    overflow-y: auto;
  }
  .footer {
    height: 55px;
    text-align: center;
    padding: 15px 30px;
  }
}
</style>
