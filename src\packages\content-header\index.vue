<template>
  <div class="content-header">
    <div>
      <div :title="config.name" class="header-list-title">{{ config.name }}</div>
      <div class="header-list-content">
        <span :title="config.description" class="header-list-description">{{ config.description || '暂无描述' }}</span>
        <!-- <span class="header-list-more">了解更多</span> -->
      </div>
    </div>
    <!-- TODO -->
    <div class="header-list-extra" />
  </div>
</template>

<script>
export default {
  props: {
    config: {
      type: Object,
      default: () => {
        return {}
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.content-header {
  display: flex;
  justify-content: space-between;
  padding: 15px 20px;
  .header-list-title {
    color: #1d2129;
    font-weight: 500;
    font-size: 24px;
    line-height: 32px;
  }
  .header-list-content {
    margin-top: 6px;
    color: #4e5969;
    font-size: 12px;
    line-height: 20px;
    .header-list-more {
      color: #4e5969;
      cursor: pointer;
    }
  }
}
</style>
