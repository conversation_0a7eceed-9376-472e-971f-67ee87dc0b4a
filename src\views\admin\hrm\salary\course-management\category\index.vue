<template>
  <div>
    <transverse-list :data="classificationList" :cache-pattern="true" :module-name="categoryName + '_' + moduleName + '_classification'" title="分类" style="border: none;" @node-click="classificationType" @edit="openDialog" @add="dialogVisible = true" @delete="delType"/>
    <div class="transverse-list">
      <div class="transverse-list-title">{{ '难度' }}：</div>
      <div class="separate-line" />
      <div :class="false ? 'transverse-list-content ':'transverse-list-content transverse-list-content-hide'">
        <div :class="selectTagDif == 'all' ? 'content-selected list-content':'list-content'" title="全部" @click="classificationType({type: 'difTag'}, 0, {label:'all'})">全部</div>
        <div v-for="item in difficultyList" :key="item.value">
          <div :class="selectTagDif == item['label'] ? 'content-selected list-content':'list-content'" class="type-item">
            <span @click="classificationType({type: 'difTag'}, 0, item)"> {{ item.label }}</span>
          </div>
        </div>
      </div>
    </div>
    <el-dialog
      :visible.sync="dialogVisible"
      title="新增分类"
      width="30%"
      @close="closeDialogVisible">
      <el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-width="100px" class="demo-ruleForm" @submit.native.prevent>
        <el-form-item label="名称:" prop="categoryName">
          <el-input v-model.trim="ruleForm.categoryName" placeholder="分类名称"/>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="resetForm('ruleForm')">取 消</el-button>
        <el-button type="primary" @click="submitForm('ruleForm')">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog :visible.sync="typeDialog" title="编辑分类" width="600px">
      <div>
        <el-form ref="typeFormRef" :model="typeForm" :rules="rulesEidit" label-width="100px" class="demo-ruleForm">
          <el-form-item label="修改项:" prop="categoryCode">
            <el-select v-model="typeForm.categoryCode" placeholder="请选择" clearable>
              <el-option v-for="item in classificationList" :key="item.categoryCode" :label="item.categoryName" :value="item.categoryCode"/>
            </el-select>
          </el-form-item>
          <el-form-item label="新名称:" prop="categoryName">
            <el-input v-model.trim="typeForm.categoryName" placeholder="分类名称"/>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="typeDialog = false">取 消</el-button>
        <el-button type="primary" @click="editType('typeFormRef')">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import module from '../config.js'
import transverseList from '@/packages/transverse-list/index.vue'
import validate from '@/packages/validate'
import { queryCourse } from '@/api/teacher/index.js'
import { queryCourseCategory, updateCourseCategory, deleteCourseCategory, saveCourseCategory } from '@/api/teacher/index.js'
export default {
  components: {
    transverseList
  },
  props: {
    categoryName: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      moduleName: module.name,
      classificationList: [],
      dialogVisible: false,
      typeDialog: false,
      ruleForm: {
        categoryName: ''
      },
      rulesEidit: {
        categoryCode: [validate.required()],
        categoryName: [validate.required(), validate.base_name]
      },
      typeForm: { categoryCode: '', categoryName: '' },
      rules: {
        categoryName: [validate.required(), validate.base_name]
      },
      difficultyList: [{ label: '初级', value: 1 }, { label: '中级', value: 2 }, { label: '高级', value: 3 }],
      selectTagDif: 'all',
      difficultyValue: '',
      categoryCode: ''
    }
  },
  created() {
    this.searchPoint()
    if (this.$store.state.cache[this.categoryName + '_' + this.moduleName + '_difficulty']) {
      this.classificationType({ type: 'difTag' }, 1, this.$store.state.cache[this.categoryName + '_' + this.moduleName + '_difficulty'])
    }
  },
  methods: {
    searchPoint() {
      queryCourseCategory({ id: '' }).then(res => {
        this.classificationList = res.data
        this.classificationList.map(item => {
          item.categoryName = item.name
          item.categoryCode = item.id
        })
      })
    },
    classificationType(item, type, value) {
      if (item.type !== 'difTag') {
        if (item.categoryCode) {
          this.categoryCode = item.categoryCode
        } else {
          this.categoryCode = ''
        }
      }
      if (item.type === 'difTag') {
        const obj = {
          data: value,
          key: this.categoryName + '_' + this.moduleName + '_difficulty'
        }
        this.$store.commit('SET_CACHE', obj)
        this.selectTagDif = value['label']
        this.difficultyValue = value['value'] || ''
      }
      this.$emit('classificationType', {
        contentCategoryId: this.categoryCode,
        contentLevel: this.difficultyValue,
        type: type
      })
    },
    openDialog() {
      this.typeForm.categoryCode = ''
      this.typeForm.categoryName = ''
      this.typeDialog = true
      this.$refs['typeFormRef'].resetFields()
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
      this.ruleForm.categoryName = ''
      this.dialogVisible = false
    },
    closeDialogVisible() {
      this.$refs['ruleForm'].resetFields()
      this.ruleForm.categoryName = ''
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          saveCourseCategory({ name: this.ruleForm.categoryName }).then(res => {
            if (res.code == 0) {
              this.dialogVisible = false
              this.$message.success('新建分类成功')
              this.$refs[formName].resetFields()
              this.ruleForm.categoryName = ''
              this.searchPoint()
            }
          })
        }
      })
    },
    editType(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          updateCourseCategory({ id: this.typeForm.categoryCode, name: this.typeForm.categoryName }).then(res => {
            if (res.code == 0) {
              this.$message.success('修改分类成功')
              this.typeDialog = false
              this.searchPoint()
            }
          })
        }
      })
    },
    delType(row) {
      const data = {
        courseCategoryId: row.id,
        createdBy: '',
        difficulty: '',
        page: 1,
        limit: 8
      }
      queryCourse(data).then(res => {
        if (res.data.total >= 1) {
          this.$message.error('该分类下存在内容，请先删除内容，再删除分类')
          return
        } else {
          this.$confirm('此操作将永久删除该分类, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            deleteCourseCategory({ id: row.categoryCode }).then(res => {
              if (res.code == 0) {
                this.searchPoint()
                this.$message.success('删除分类成功')
              }
            })
          })
        }
      })
    }
  }

}
</script>
<style lang="scss" scoped>
.transverse-list {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 15px 12px 24px;
  width: 100%;
  font-size: 16px;
  border-bottom: 1px solid var(--neutral-300);
  & + .transverse-list {
    padding-top: 0px;
  }
  .transverse-list-title{
    white-space: nowrap;
    font-size: 14px;
    line-height: 18px;
    font-weight: 500;
    color: #35445d;
  }
  .separate-line {
    height: 14px;
    width: 1px;
    margin: 0 12px;
    background-color: #e1e6ef;
  }
  .transverse-list-content {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    flex: 1;
    min-width: 0;
    position: relative;
    .list-content {
      height: 32px;
      padding: 7px 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      font-size: 14px;
      line-height: 18px;
      font-weight: 500;
      border-radius: 6px;
      color: #35445d;
      margin-right: 6px;
    }

    .list-content:hover{
      background-color: #ebeff5;
      border-radius: 6px;
    }
    .content-selected {
      background-color: #ebeff5;
    }
    .type-item{
      position: relative;
      border-radius: 6px;
      z-index: 0;
    }
    .type-item:hover{
      background: rgba(35, 98, 251, 0.1);
      .reduce-type{
        display: inline;
      }
    }
    .reduce-type{
      display: none;
      position: absolute;
      top: 0px;
      right: -1px;
      color: red;
      z-index: 1;
    }
  }
}
</style>
