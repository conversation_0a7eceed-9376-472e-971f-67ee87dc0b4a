<template>
  <div v-loading="loading" class="attachment-wrap">
    <el-empty
      v-if="!fileList.length && !loading"
      :image="img"
      :image-size="110"
      style="margin: 100px auto"
      description="暂无数据"
    />
    <FileList v-else :list="fileList" :deletable="false" @download="downloadFile"/>
  </div>
</template>

<script>
import { contentdetail } from '@/api/teacher/index.js'
import mixin from './mixin'
import FileList from '@/components/FileList'

export default {
  components: {
    FileList
  },
  mixins: [mixin],
  data() {
    return {
      loading: false,
      fileList: []
    }
  },
  mounted() {
    this.searchPackage()
  },
  methods: {
    searchPackage() {
      this.loading = true
      contentdetail({ contentId: this.contentId, format: 'package' }).then(
        (res) => {
          if (res.code == 0 && res.data) {
            this.fileList = res.data || []
            this.fileList.forEach((item) => {
              item.name = item.fileName
              item.url = window.location.origin + item.attachmentUrl
            })
          }
        }
      ).finally(() => {
        this.loading = false
      })
    },
    async downloadFile(item) {
      const a = document.createElement('a')
      a.href = item.url
      a.download = item.fileName
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
    }
  }
}
</script>

<style lang="scss" scoped>
.attachment-wrap {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  padding-right: 5px !important;
  background-color: #ffffff;
}
</style>
