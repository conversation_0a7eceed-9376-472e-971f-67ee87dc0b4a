.side-detail-main {
  height: 100%;
  position: relative;
  overflow: hidden;
}

.d-full-view {
  position: fixed;
  min-width: 926px;
  width: 75%;
  top: 0px;
  bottom: 0px;
  right: 0px;
}

.d-view {
  position: fixed;
  min-width: 926px;
  width: 75%;
  top: 60px;
  bottom: 0px;
  right: 0px;
}

// 详情头
.wk-detail-header {
  flex-shrink: 0;
}

// 详情
.side-detail__tabs {
  /deep/ .el-tabs__item {
    color: #333;
    font-size: 12px;
    top: 2px;
    margin-top: -2px;
  }

  /deep/ .el-tabs__nav-scroll {
    min-height: 39px;
  }

  /deep/ .el-tabs__item.is-active {
    border-top: 2px solid $xr-color-primary;
    color: #333;
  }

  /deep/ .el-tabs {
    height: calc(100% - 15px) !important;
  }

  /deep/ .el-tabs__content {
    height: calc(100% - 40px) !important;
    padding: 0;
    overflow: hidden;
    position: relative;

    .el-tab-pane {
      height: 100%;
      overflow: hidden;
    }
  }

  &--left {
    flex: 1;
    box-shadow: none;
    position: relative;
    overflow: hidden;
  }

  &--right {
    width: 300px;
    min-width: 300px;
    box-shadow: none;
    flex-shrink: 0;
    height: calc(100% - 15px);
    background-color: white;
    margin-left: 15px;
    border-left: 1px solid $xr-border-line-color;
    border-top: 1px solid $xr-border-line-color;
    border-bottom: 1px solid $xr-border-line-color;
  }
}

// 详情
/deep/ .side-detail__tabs--default {
  flex: 1;
  overflow: hidden;

  .el-tabs__header {
    padding: 0 20px;
  }

  .el-tabs__item {
    color: #333;
    font-size: 12px;
    top: 2px;
    margin-top: -2px;
  }

  .el-tabs__nav-scroll {
    min-height: 39px;
  }

  .el-tabs__item.is-active {
    color: #333;
  }

  .el-tabs {
    height: calc(100% - 15px) !important;
  }

  .el-tabs__content {
    height: calc(100% - 55px) !important;
    padding: 0;
    overflow: hidden;
    position: relative;

    .el-tab-pane {
      height: 100%;
      overflow-y: auto;
    }
  }

  .el-tabs__nav-wrap::after {
    height: 1px;
  }

  &--left {
    flex: 1;
    box-shadow: none;
    position: relative;
    overflow: hidden;
  }

  &--right {
    width: 300px;
    min-width: 300px;
    box-shadow: none;
    flex-shrink: 0;
    height: calc(100% - 15px);
    background-color: white;
    margin-left: 15px;
    border-left: 1px solid $xr-border-line-color;
    border-top: 1px solid $xr-border-line-color;
    border-bottom: 1px solid $xr-border-line-color;
  }
}

.side-detail-tabs-content {
  height: 100%;
  overflow: auto;
}
