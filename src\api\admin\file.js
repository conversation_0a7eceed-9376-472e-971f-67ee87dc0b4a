import request from '@/utils/request'

// wangeditor 富文本编辑器上传文件
export function RichTextUpload(data) {
  return request({
    url: 'accumulate/physics/fileUpload',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 上传封面图片
export function upTeachingCoverAPI(data) {
  return request({
    url: '/admin/portal/upTeachingCover',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
