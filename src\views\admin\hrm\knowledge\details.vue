<template>
  <div class="_teacher_container flex-col">

    <el-tabs v-model="activeName" class="mb-10" @tab-click="handleClick">
      <el-tab-pane v-for="item in activeList" :label="item.name" :key="item.id" :name="item.component"/>
    </el-tabs>
    <div class="back-btn" @click="gotoBack">
      返回
    </div>
    <component :is="activeName"/>
    <!-- <div class="_teacher_table flex-1">
      <el-table
        :data="tableData"
        style="width: 100%"
        @selection-change="handleSelectionChange">
        <el-table-column
          type="selection"
          width="55"/>
        <el-table-column
          prop="realname"
          label="内容名称"
        />
        <el-table-column
          prop="realname"
          label="课时"
        >
          <template slot-scope="scope">
            <span>{{ `${scope.row.sex}课时` }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="sex"
          label="类型"
        />
        <el-table-column
          label="操作">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="handleEdit(scope.row)">查看</el-button>
            <el-button type="text" size="small" @click="handleEdit(scope.row)">断开连接</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="flex jc-end">
      <el-pagination
        :current-page="pager.currentPage"
        :page-sizes="[10, 20, 30, 40, 50, 100, 200]"
        :page-size="pager.pageSize"
        :total="pager.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"/>
    </div> -->
  </div>
</template>

<script>
import {
  searchAPI,
  addAPI,
  dropAPI,
  editAPI,
  editStatusAPI
} from '@/api/admin/training/teacher'
import BasicCourse from '@/components/Knowledge/BasicCourse'
import QuestionNum from '@/components/Knowledge/QuestionNum'
import TaskTrain from '@/components/Knowledge/TaskTrain'

export default {
  components: { BasicCourse, QuestionNum, TaskTrain },
  data() {
    return {
      tableData: [],
      activeList: [{ name: '基础课程', component: 'BasicCourse', id: 1 }, { name: '任务实训', component: 'TaskTrain', id: 2 }, { name: '题库题目', component: 'QuestionNum', id: 3 }],
      activeName: 'BasicCourse',
      addTeacherDialogVisible: false,
      editTeacherDialogVisible: false,
      addTeacherForm: {
        username: '19999999999',
        realname: 'yiyu',
        sex: '1',
        password: '11111qqqqq'
      },
      editTeacherForm: {
        username: '',
        realname: '',
        sex: '',
        password: '',
        userId: ''
      },
      formLabelWidth: '100px',
      // 表单校验规则
      formRules: {
        username: {
          required: true,
          message: '账号不能为空',
          trigger: 'blur'
        },
        realname: {
          required: true,
          message: '姓名不能为空',
          trigger: 'blur'
        },
        sex: {
          required: true,
          message: '性别不能为空',
          trigger: 'blur'
        },
        password: {
          required: true,
          message: '密码不能为空',
          trigger: 'blur'
        }
      },
      // 分页器
      pager: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      // 多选
      selectList: [],
      // 搜索关键字
      searchName: ''
    }
  },
  mounted() {
    // this.searchList()
  },
  methods: {
    searchList(pageNum = 1, pageSize = 10, realname = this.searchName) {
      searchAPI({
        pageNum,
        pageSize,
        realname
      }).then((ret) => {
        if (ret.data) {
          if (ret.code === 0) {
            // 分页器
            this.pager = {
              currentPage: ret.data.current,
              pageSize: ret.data.size,
              total: ret.data.total
            }
            // 表格数据
            this.tableData = ret.data.records.map(el => ({
              username: el.username,
              realname: el.realname,
              sex: el.sex,
              status: el.status === 1,
              userId: el.userId
            }))
            return
          }
          this.$message.warning('加载失败.')
        }
      })
    },
    handleClick(item) {
      this.activeName = this.activeList[item.index].component
    },
    handleAddItem() {
      this.addTeacherDialogVisible = true
    },
    handleEdit(row) {
      this.editTeacherDialogVisible = true
      this.editTeacherForm = {
        ...row,
        sex: row.sex.toString()
      }
    },
    handleSubmitForm() {
      // 校验表单
      this.$refs.addTeacherForm.validate((valid) => {
        if (valid) {
          addAPI(this.addTeacherForm).then((ret) => {
            if (ret.code === 0) {
              // 添加成功刷新列表
              this.searchList()
              this.$message.success('添加成功.')
              return
            }
            this.$message.warning('添加失败.')
          }).finally(() => {
            this.addTeacherDialogVisible = false
          })
        }
      })
    },
    // 更新表单
    handleSubmitEditForm() {
      // 校验表单
      this.$refs.editTeacherForm.validate((valid) => {
        if (valid) {
          editAPI({
            ...this.editTeacherForm,
            status: this.editTeacherForm.status ? '1' : '0'
          }).then((ret) => {
            if (ret.code === 0) {
              // 添加成功刷新列表
              this.searchList()
              this.$message.success('添加成功.')
              return
            }
            this.$message.warning('添加失败.')
          }).finally(() => {
            this.editTeacherDialogVisible = false
          })
        }
      })
    },
    handleCancel() {
      this.$refs.addTeacherForm.resetFields()
      this.addTeacherDialogVisible = false
    },
    handleEditCancel() {
      this.$refs.editTeacherForm.resetFields()
      this.editTeacherDialogVisible = false
    },
    handleSizeChange(pageSize) {
      this.searchList(this.pager.currentPage, pageSize)
    },
    handleCurrentChange(currentPage) {
      this.searchList(currentPage, this.pager.pageSize)
    },
    handleSelectionChange(selectList) {
      this.selectList = selectList
    },
    // 修改状态
    handleEditStatus(row) {
      this.doEditStatus([{ userId: row.userId, status: row.status ? '1' : '0' }])
    },
    // 批量禁用
    handleEditStatusBatch() {
      if (this.selectList.length < 1) {
        this.$message.warning('未选择教师.')
        return
      }
      this.doEditStatus(this.selectList.map(el => ({
        userId: el.userId,
        status: '0'
      })))
    },
    // 删除教师
    handleDrop(row) {
      this.doDrop([{ userId: row.userId }])
    },
    // 批量删除
    handleDropBatch() {
      if (this.selectList.length < 1) {
        this.$message.warning('未选择教师.')
        return
      }
      this.doDrop(this.selectList.map(el => ({
        userId: el.userId
      })))
    },
    doDrop(data) {
      dropAPI(data).then((ret) => {
        if (ret.code === 0) {
          // 删除成功刷新列表
          this.searchList()
          this.$message.success('删除成功.')
          return
        }
        this.$message.warning('删除失败.')
      })
    },
    doEditStatus(data) {
      editStatusAPI(data).then((ret) => {
        if (ret.code === 0) {
          this.searchList()
          this.$message.success('修改成功.')
          return
        }
        this.$message.warning('修改失败.')
      })
    },
    // 搜索
    handleSearch() {
      this.searchList(this.pager.currentPage, this.pager.pageSize, this.searchName)
    },
    // 返回操作
    gotoBack() {
      this.$router.push({
        path: 'knowledgeManage'
      })
    }
  }

}
</script>

<style lang="scss" scoped>

/deep/.el-tabs__active-bar {
    height: 1px !important;
    // transform: scaleY(0.5);
}

/deep/ .el-tabs__nav-wrap::after {
    height: 1px !important;
    background: #E6EAED;
    // transform: scaleY(0.5);
}

/deep/ .el-tabs__header {
    margin: 0px !important;
}

/deep/.el-tabs__item.is-active {
    color: #006eff;
}

/deep/ .el-tabs__item {
    font-size: 16px;
    color: #999;
}

/deep/ .el-tabs__item:hover {
    color:#006eff;
}

._teacher_container {
  padding: 15px;
  background: #FFFFFF;
  height: calc(100vh - 90px);
  position: relative;

  .back-btn{
    display: inline-block;
    position: absolute;
    top: 15px;
    right: 30px;
    padding: 8px 15px;
    color: #fff;
    background:#006eff;
    border-radius: 4px;
    cursor: pointer;
  }
  ._teacher_table {
    height: calc(100vh - 300px);
    overflow: auto;
  }
  ._tools {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    ._search {
      width: 220px;
      padding-right: 20px;
    }
  }
}
</style>
