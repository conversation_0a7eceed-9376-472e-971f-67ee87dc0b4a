<template>
  <div v-loading="loading" class="dialog-wrap">
    <batch-template
      :data="data"
      :available-data="availableData"
      view-key="suiteName"
    />
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button :disabled="!availableData.length" type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>

<script>
import batchTemplate from '@/packages/batch-delete/modal-bat-template.vue'
import modalMixins from '@/packages/mixins/modal_form'
import { caseUnbindSuite } from '@/api/testing/testCase'

export default {
  components: {
    batchTemplate
  },
  mixins: [modalMixins],
  props: {
    data: {
      type: Array,
      default: () => {
        return []
      }
    },
    caseId: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    return {
      loading: false
    }
  },
  computed: {
    availableData: function() {
      const tempArr = this.data.filter((item) => {
        return item.id
      })
      return tempArr
    }
  },
  methods: {
    close() {
      this.$emit('close')
    },
    confirm: function() {
      this.loading = true
      const postData = {
        caseId: this.caseId,
        suiteIds: this.availableData.map(item => {
          return item.suiteId
        }),
        ids: this.availableData.map(item => {
          return item.id
        })
      }
      caseUnbindSuite(postData).then((res) => {
        if ([0, 200].includes(res.code)) {
          this.$message.success('解绑成功')
          this.$emit('call', 'refresh')
          this.close()
        }
      }).catch(() => {
        this.close()
        this.loading = false
      })
    }
  }
}
</script>
