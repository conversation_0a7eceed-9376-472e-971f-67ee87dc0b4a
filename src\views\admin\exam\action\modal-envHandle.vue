<template>
  <div class="dialog-wrap">
    <div v-if="name == 'openEnv'">请确认是否开启"{{ data[0].name }}"考试的环境?</div>
    <div v-else>请确认是否关闭"{{ data[0].name }}"考试的环境?</div>
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>

<script>
import modalMixins from '@/packages/mixins/modal_form'
import { openExamEnv, closeExamEnv } from '@/api/exam/index.js'
import { formatDate } from '@/utils/index'

export default {
  components: {
  },
  mixins: [modalMixins],
  props: {
    name: {
      type: String,
      default: ''
    },
    data: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {}
  },
  computed: {
  },
  methods: {
    close() {
      this.$emit('close')
    },
    confirm: function() {
      if (this.name == 'openEnv') {
        const now = formatDate(new Date(), 'yy-mm-dd hh:mm:ss')
        const nowStamp = new Date(now).getTime()

        const regEndTime = formatDate(new Date(this.data[0].regEndTime), 'yy-mm-dd hh:mm:ss')
        const regEndTimeStamp = new Date(regEndTime).getTime()
        if (nowStamp < regEndTimeStamp) {
          this.$message.warning(`“${this.data[0].name}”考试报名未结束，请等报名截止后再开启环境`)
          return
        }
        const params = {
          id: this.data[0].id,
          paperId: this.data[0].paperId,
          name: this.data[0].name
        }
        openExamEnv(params).then((res) => {
          if (res.code === 0) {
            this.$message({
              message: '正在开启环境',
              type: 'success'
            })
            this.$emit('call', 'refresh')
            this.close()
          }
        }).catch(() => {
          this.loading = false
        })
      } else {
        const now = formatDate(new Date(), 'yy-mm-dd hh:mm:ss')
        const nowStamp = new Date(now).getTime()

        const beginTime = formatDate(new Date(this.data[0].beginTime), 'yy-mm-dd hh:mm:ss')
        const beginTimeStamp = new Date(beginTime).getTime()

        const endTime = formatDate(new Date(this.data[0].endTime), 'yy-mm-dd hh:mm:ss')
        const endTimeStamp = new Date(endTime).getTime()
        if (nowStamp > beginTimeStamp && nowStamp < endTimeStamp) {
          this.$message.warning(`${this.data[0].name}考试未结束，请勿关闭环境`)
          return
        }
        const params = {
          id: this.data[0].id,
          paperId: this.data[0].paperId,
          name: this.data[0].name
        }
        closeExamEnv(params).then((res) => {
          if (res.code === 0) {
            this.$message({
              message: '正在关闭环境',
              type: 'success'
            })
            this.$emit('call', 'refresh')
            this.close()
          }
        }).catch(() => {
          this.loading = false
        })
      }
    }
  }
}
</script>
