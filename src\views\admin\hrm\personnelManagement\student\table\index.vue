<template>
  <div class="resource-table">
    <!-- 操作区 -->
    <div class="operation-wrap">
      <div class="operation-left">
        <slot name="action" />
        <el-button type="primary" icon="el-icon-refresh" @click="refresh" />
      </div>
      <div class="operation-right">
        <el-badge :value="searchBtnShowNum">
          <el-button icon="el-icon-search" @click="openSearch">搜索</el-button>
        </el-badge>
        <!-- 自定义表格列 -->
        <t-table-config
          v-if="!customColData.length"
          :data="columnsObj"
          :active-key-arr="columnsViewArr"
          @on-change-col="onChangeCol"
        />
      </div>
    </div>
    <!-- 搜索区 -->
    <t-search-box
      v-show="searchView"
      :search-key-list="searchKeyListView"
      default-placeholder="默认搜索姓名"
      @search="searchMultiple"
    />
    <!-- 列表 -->
    <t-table-view
      ref="tableView"
      :height="height"
      :single="single"
      :loading="tableLoading"
      :data="tableData"
      :total="tableTotal"
      :page-size="pageSize"
      :current="pageCurrent"
      :select-item="selectItem"
      current-key="userId"
      @on-select="onSelect"
      @on-current="onCurrent"
      @on-change="changePage"
      @on-sort-change="onSortChange"
      @on-page-size-change="onPageSizeChange"
    >
      <el-table-column v-for="item in columnsViewArr" :key="item" :min-width="colMinWidth" :label="columnsObj[item].title" :fixed="columnsObj[item].master ? 'left' : false" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="item == 'sex'">{{ scope.row[item] ? (scope.row[item] == 1 ? '男':'女') : "-" }}</span>
          <div v-else-if="item == 'learningProcess'"><a @click.stop="handleProcess(scope.row)">学习历程</a></div>
          <div v-else-if="item == 'className'">
            <div class="flex jc-between ai-center">
              <div class="ellipsis overflow-tooltip">
                <div class="ellipsis">{{ scope.row.classData[0] || "-" }}</div>
              </div>
              <CountPopover :list="scope.row.classData" />
            </div>
          </div>
          <span v-else>{{ scope.row[item] || "-" }}</span>
        </template>
      </el-table-column>
    </t-table-view>
  </div>
</template>

<script>
import module from '../config.js'
import tSearchBox from '@/packages/search-box/index.vue'
import tTableView from '@/packages/table-view/index.vue'
import tTableConfig from '@/packages/table-config/table-col-config.vue'
import tableTdMultiCol from '@/packages/table-config/table-td-multi-col.vue'
import mixinsPageTable from '@/packages/mixins/page_table'
import { queryStudentManageList } from '@/api/admin/training/student'
import CountPopover from '@/components/CountPopover/index'

export default {
  components: {
    tSearchBox,
    tTableView,
    tTableConfig,
    tableTdMultiCol,
    CountPopover
  },
  mixins: [mixinsPageTable],
  data() {
    return {
      moduleName: module.name,
      // 搜索配置项
      searchKeyList: [
        { key: 'realname', label: '姓名', master: true },
        { key: 'username', label: '账号' },
        { key: 'className', label: '班级' }
      ],
      // 所有可配置显示列 master：不可隐藏 title:列名称
      columnsObj: {
        'realname': {
          title: '姓名',
          master: true
        },
        'username': {
          title: '账号'
        },
        'className': {
          title: '班级'
        },
        'sex': {
          title: '性别'
        },
        'createTime': {
          title: '添加时间'
        },
        'learningProcess': {
          title: '学习历程'
        }

      },
      // 当前显示列key表 默认，如果localStorage有数据将被覆盖
      columnsViewArr: [
        'realname',
        'username',
        'className',
        'sex',
        'createTime',
        'learningProcess'
      ]
    }
  },
  methods: {
    getList: function(showLoading = true) {
      if (showLoading) {
        this.tableLoading = true
      }
      const params = this.getPostData('page', 'limit')
      this.$emit('transmitTime', params)
      queryStudentManageList(params).then((res) => {
        if (res.code === 0) {
          this.tableData = res.data.records
          this.tableData.map((item) => {
            const classData = item.classList.map((tarItem) => {
              if (tarItem.majorName == 'null') {
                return '-'
              } else {
                return tarItem.majorName
              }
            })
            item.classData = classData
          })
          this.tableTotal = res.data.total
          this.tableLoading = false
        }
      }).catch(() => {
        this.tableLoading = false
      })
    },
    handleProcess(item) {
      this.$router.push({
        name: 'learningProcess',
        query: {
          processId: item.userId,
          realname: item.realname
        }
      })
    }
  }
}
</script>
