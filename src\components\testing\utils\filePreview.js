export default {
  data() {
    return {
      fileCouldPreview: [
        'png',
        'jpg',
        'jpeg',
        'pdf',
        'txt',
        'html',
        'htm',
        'xml',
        'svg',
        'gif',
        'bmp',
        'ico',
        'webp',
        'mp4',
        'mp3',
        'wav',
        'ogg',
        'webm',
        'json',
        'doc',
        'docx',
        'xls',
        'xlsx',
        'ppt',
        'pptx',
        'pdf'
      ],
      previewUrl: ''
    }
  },
  methods: {
    // 查看文件
    handleFileView(file) {
      let fileType = file.fileType
      if (fileType) {
        if (
          file &&
          !this.fileCouldPreview.includes(fileType.toLowerCase())
        ) {
          this.$message.warning('该文件不支持预览，请下载查看')
          return
        }
      }
      if (!fileType) {
        if (file.fileName) {
          fileType = file.fileName.split('.')[file.fileName.split('.').length - 1]
        }
        if (file.name) {
          fileType = file.name.split('.')[file.name.split('.').length - 1]
        }
        if (
          file &&
          !this.fileCouldPreview.includes(fileType.toLowerCase())
        ) {
          this.$message.warning('该文件不支持预览，请下载查看')
          return
        }
      }
      // 判断是否为xlsx类型
      const isXlsx = fileType && fileType.toLowerCase() === 'xlsx'
      if (file.path) {
        const fileUrl = window.ADMIN_CONFIG.VIP_URL + file.path
        if (isXlsx) {
          this.previewUrl = this.viewFileUrl + encodeURIComponent(btoa(fileUrl)) + '&officePreviewType=html'
        } else {
          this.previewUrl = this.viewFileUrl + encodeURIComponent(btoa(fileUrl))
        }
        window.open(this.previewUrl)
        return
      }
      if (file.fileUrl) {
        const fileUrl = window.ADMIN_CONFIG.VIP_URL + file.fileUrl
        if (isXlsx) {
          this.previewUrl = this.viewFileUrl + encodeURIComponent(btoa(fileUrl)) + '&officePreviewType=html'
        } else {
          this.previewUrl = this.viewFileUrl + encodeURIComponent(btoa(fileUrl))
        }
        window.open(this.previewUrl)
        return
      }
      if (file.path) {
        const fileUrl = window.ADMIN_CONFIG.VIP_URL + file.path
        if (isXlsx) {
          this.previewUrl = this.viewFileUrl + encodeURIComponent(btoa(fileUrl)) + '&officePreviewType=html'
        } else {
          this.previewUrl = this.viewFileUrl + encodeURIComponent(btoa(fileUrl))
        }
        window.open(this.previewUrl)
        return
      }
      if (file.url) {
        const fileUrl = window.ADMIN_CONFIG.VIP_URL + file.url
        if (isXlsx) {
          this.previewUrl = this.viewFileUrl + encodeURIComponent(btoa(fileUrl)) + '&officePreviewType=html'
        } else {
          this.previewUrl = this.viewFileUrl + encodeURIComponent(btoa(fileUrl))
        }
        window.open(this.previewUrl)
        return
      }
    }
  }
}
