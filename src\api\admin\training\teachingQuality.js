import request from '@/utils/request'
const roleValue = 'cepo_traning_admin_role'

/**
 * 班级能力统计
 */
export function classAbilityStatistics(data) {
  data.roleValue = roleValue
  return request({
    url: '/training/teachingQualityEvaluation/classAbilityStatistics',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 学员课程成绩排名
 */
export function studentCourseRanking(data) {
  return request({
    url: '/training/teachingQualityEvaluation/studentCourseRanking',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 学员成绩成绩排名
 */
export function studentTestRanking(data) {
  return request({
    url: '/training/teachingQualityEvaluation/studentTestRanking',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 学员项目成绩排名
 */
export function studentProjectRanking(data) {
  return request({
    url: '/training/teachingQualityEvaluation/studentProjectRanking',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 学员学习次数排名
 */
export function studyNumRanking(data) {
  return request({
    url: '/training/teachingQualityEvaluation/courseStudyNum',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 考试试卷使用次数
 */
export function testPaperNumRanking(data) {
  return request({
    url: '/training/teachingQualityEvaluation/courseTestNum',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 教案使用次数
 */
export function teachingPlanNumRanking(data) {
  return request({
    url: '/training/teachingQualityEvaluation/teachingPlanNum',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 班级考试得分
 */
export function classTestScoreRanking(data) {
  data.roleValue = roleValue
  return request({
    url: '/training/teachingQualityEvaluation/classTestScore',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}

/**
 * 学员考试得分统计
 */
export function studentTestScoreRanking(data) {
  data.roleValue = roleValue
  return request({
    url: '/training/teachingQualityEvaluation/studentTestScore',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
