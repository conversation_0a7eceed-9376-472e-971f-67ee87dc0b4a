<template>
  <div class="dialog-wrap">
    <div>请确认是否导出考生列表数据?</div>
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>

<script>
import axios from 'axios'
import modalMixins from '@/packages/mixins/modal_form'
import { formatDate } from '@/utils/index'
import { exportExcelFile } from '@/utils'

export default {
  components: {
  },
  mixins: [modalMixins],
  props: {
    data: {
      type: Array,
      default: () => {
        return []
      }
    },
    searchName: {
      type: Object
    }
  },
  data() {
    return {}
  },
  computed: {
  },
  created() {
  },
  methods: {
    close() {
      this.$emit('close')
    },
    confirm: function() {
      const params = {
        examId: this.$route.params.id,
        examName: this.$route.params.name,
        realname: this.searchName.realname
      }
      axios({
        method: 'post',
        url: '/api/ca-exam/exam/studentListExport',
        data: params,
        responseType: 'blob'
      }).then((res) => {
        exportExcelFile(res, `${this.$route.params.name}_考生列表_${formatDate(new Date(), 'yy-mm-dd')}`)
        this.close()
      })
    }
  }
}
</script>
