<template>
  <div class="resource-table">
    <!--    操作区 start-->
    <div class="operation-wrap">
      <div class="operation-left">
        <slot name="action"/>
        <el-button type="primary" icon="el-icon-refresh" @click="refresh"/>
      </div>
      <div class="operation-right">
        <el-badge :value="searchBtnShowNum">
          <el-button icon="el-icon-search" @click="openSearch">搜索</el-button>
        </el-badge>
      </div>
    </div>
    <!--    操作区 end-->
    <!--    搜索标签显示区 start-->
    <t-search-box
      v-show="searchView"
      :search-key-list="searchKeyListView"
      :default-placeholder="'默认搜索设备名称'"
      @search="searchMultiple"
    />
    <!--    搜索标签显示区 end-->

    <!--    列表 start-->
    <t-table-view
      ref="tableView"
      :single="single"
      :height="height"
      :loading="tableLoading"
      :data="tableData"
      :total="tableTotal"
      :page-size="pageSize"
      :current="pageCurrent"
      :select-item="selectItem"
      current-key="id"
      @on-select="onSelect"
      @on-current="onCurrent"
      @on-change="changePage"
      @on-page-size-change="onPageSizeChange"
    >
      <el-table-column :min-width="colMinWidth" label="实例名称" fixed="left" show-overflow-tooltip>
        <template slot-scope="scope">
          <a href="javascript:;" @click.stop="showDetail(scope.row)">
            {{ scope.row.name }}
          </a>
        </template>
      </el-table-column>
      <el-table-column :min-width="colMinWidth" label="设备类型" show-overflow-tooltip>
        <template slot-scope="scope">
          {{ resource_type[scope.row.resource_type] }}
        </template>
      </el-table-column>
      <el-table-column :min-width="colMinWidth" label="设备分类" show-overflow-tooltip>
        <template slot-scope="scope">
          {{ scope.row.resource_type === 'pnf'? '物理设备' : (scope.row.virtual_type === 'qemu' ? '虚拟机设备' : (scope.row.virtual_type === 'docker' ? '容器设备' : '云设备')) }}
        </template>
      </el-table-column>
      <el-table-column :min-width="colMinWidth" label="设备名称" show-overflow-tooltip>
        <template slot-scope="scope">
          {{ scope.row.network_element_data ? scope.row.network_element_data.name : '-' }}
        </template>
      </el-table-column>
      <el-table-column :min-width="colMinWidth" label="状态" show-overflow-tooltip>
        <template slot-scope="scope">
          <template v-if="scope.row.status">
            <el-badge
              :type="scope.row.resource_type === 'pnf' ? 'success' : getStatus(scope.row.status)"
              is-dot />{{ scope.row.resource_type === 'pnf' ? '已引入' : node_status_info[scope.row.status.toLowerCase()] }}
          </template>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column :min-width="colMinWidth" label="任务" show-overflow-tooltip>
        <template slot-scope="scope">
          {{ scope.row.task || '-' }}
        </template>
      </el-table-column>
      <el-table-column :min-width="colMinWidth" label="创建时间" show-overflow-tooltip>
        <template slot-scope="scope">
          {{ $options.filters['nfvoMoment'](scope.row.created_at) }}
        </template>
      </el-table-column>
      <el-table-column :min-width="colMinWidth" label="更新时间" show-overflow-tooltip>
        <template slot-scope="scope">
          {{ $options.filters['nfvoMoment'](scope.row.updated_at) }}
        </template>
      </el-table-column>
    </t-table-view>
    <!--    列表 end-->
  </div>
</template>
<script>
import { mapState } from 'vuex'
import moduleConf from '../config'
// 列表列配置
import mixinsPageTable from '../../../mixins/page_table.js'
// 设置列表列UI组件
import tTableConfig from '../../../table-config/table-col-config.vue'
import tTableView from '../../../table-view/index.vue'
import tSearchBox from '../../../search-box/index.vue'
import { getNode, getNodeItem } from '../../api/orchestration'
export default {
  components: {
    tTableConfig,
    tTableView,
    tSearchBox
  },
  mixins: [
    mixinsPageTable
  ],
  props: {
    topoId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      'resource_type': {
        'pnf': '物理设备',
        'vnf': '虚拟设备',
        'inf': '图形设备',
        'base': '基础组件'
      },
      'node_status_info': {
        'pending': '添加成功',
        'starting': '启动中',
        'running': '运行中',
        'powering_on': '开机中',
        'shutoff': '关机',
        'powering_off': '关机中',
        'deleting': '删除中',
        'suspended': '挂起',
        'suspending': '挂起中',
        'paused': '暂停',
        'pausing': '暂停中',
        'rebooting': '重启中',
        'rebuilding': '重建中',
        'error': '错误',
        'resuming': '恢复中'
      },
      // 模块名称
      moduleName: moduleConf.name,
      // 列表数据请求对象
      tableFetch: {
        'getList': fetch.getNode,
        'getItem': fetch.getNodeItem
      },
      // 搜索项配置
      searchKeyList: [
        { key: 'name', label: '名称', master: true, placeholder: '默认搜索名称' }
      ]
    }
  },
  computed: {
    ...mapState('socketListener', [
      'instanceSocket'
    ])
  },
  watch: {
    'instanceSocket': function(nval, oval) {
      this.socketHandle(nval, oval)
    }
  },
  methods: {
    // socket消息处理
    'socketHandle': function(nval, oval) {
      const option = nval.payload.hasOwnProperty('option') ? nval.payload['option'] : (nval.payload.hasOwnProperty('event_type') ? nval.payload['event_type'] : null)
      const status = nval.payload.hasOwnProperty('state') ? nval.payload['state'] : null
      const id = nval.payload.hasOwnProperty('resource_id') ? nval.payload['resource_id'] : null
      if (option === 'delete' || option === 'created' || status === 'created') {
        return
      } else {
        this.reloadItem(id)
        return
      }
    },
    'showDetail': function(row) {
      this.$emit('on-show-detail', row)
      this.$refs['tableView'].clearSelection()
      this.setHighlightRow(row)
      this.$refs['tableView'].toggleRowSelection(row)
    },
    // 根据状态返回color
    'getStatus': function(data) {
      let color = null
      switch (data.toLowerCase()) {
        case 'running':
          color = 'success'
          break
        case 'shutoff':
        case 'error':
          color = 'danger'
          break
        case 'pending':
          color = 'info'
          break
        default:
          color = 'warning'
          break
      }
      return color
    },
    'reloadItem': function(id) {
      const reloadTimestamp = new Date().getTime()
      const tableIdArr = this.tableData.map(item => item['id'])
      if (!tableIdArr.includes(id)) {
        return
      }
      getNodeItem(id)
        .then(res => {
          const saveLastData = JSON.parse(JSON.stringify(this.selectItem))
          const _itemData = res['data']['data']
          for (let i = 0; i < this.tableData.length; i++) {
            if (this.tableData[i]['id'] === id) {
              if (!this.tableData[i].hasOwnProperty('reload_timestamp') || (reloadTimestamp > this.tableData[i]['reload_timestamp'])) {
                _itemData['reload_timestamp'] = reloadTimestamp
                this.$set(this.tableData, i, _itemData)
              }
              break
            }
          }
          const arr = saveLastData.map(item => item[this.defaultSelectedKey]).concat(this.defaultSelectedArr)
          this.tableData.forEach((item, index) => {
            if (arr.indexOf(item[this.defaultSelectedKey]) > -1) {
              this.$refs['tableView'].toggleRowSelection(this.tableData[index], true)
            }
          })
        })
    },
    getList: function(showLoading = true) {
      if (showLoading) {
        this.tableLoading = true
      }
      const params = this.getPostData('page', 'limit')
      params['offset'] = (params['page'] - 1) * params['limit']
      delete params['page']
      getNode(params).then((res) => {
        this.tableData = res.data.data.result
        this.tableTotal = res.data.data.total
        this.tableLoading = false
        this.handleSelection()
      }).catch(() => {
        this.tableLoading = false
      })
    }
  }
}
</script>
