<template>
  <div class="tpl-upload">
    <div class="upload-select">
      <input
        ref="input"
        :multiple="multiple"
        :accept="accept"
        type="file"
        class="upload-select-input"
        @change="handleChange"
      >
      <div class="upload-click-wrap" @click="handleClick">
        <slot />
      </div>
      <div
        v-show="path"
        :style="{
          'border-color': error ? '#F56C6C' : '#abdcff',
          'background-color': error ? '#fff6f4' : '#f0faff'
        }"
        class="file-container"
        @mouseenter="hover = true" @mouseleave="hover = false"
      >
        <i :style="{ 'color': error ? '#F56C6C' : '#2d8cf0' }" class="el-icon-document" size="16" />
        <span :title="path" style="margin: 10px 10px 0 0;">{{ path }}</span>
        <i v-show="hover" :style="{ 'color': error ? '#F56C6C' : '#2d8cf0' }" class="el-icon-delete delete-icon delete" @click.stop="handleDeleteFile" />
      </div>
    </div>
    <slot name="tip" />
  </div>
</template>
<style lang="less">
.tpl-upload {
  .upload-select-input {
    display: none;
  }
  .upload-click-wrap {
    display: inline-block;
  }
  .file-container {
    overflow-wrap: break-word;
    word-break: normal;
    line-height: 1.5;
    border: 1px solid;
    margin-top: 5px;
    padding: 6px 10px;
    overflow: hidden;
    text-overflow:ellipsis;
    white-space: nowrap;
    cursor: pointer;
    position: relative;
    .delete {
      position: absolute;
      top: 10px;
      right: 10px;
    }
  }
}
</style>
<script>
import { fileSize } from '@/utils/index'
export default {
  name: 'Upload',
  props: {
    fileData: {
      type: File
    },
    filePath: {
      type: String
    },
    maxSize: {
      type: Number
    },
    multiple: {
      type: Boolean,
      default: false
    },
    accept: {
      type: String
    },
    disabled: {
      type: Boolean
    }
  },
  data() {
    return {
      path: null,
      file: null,
      error: false,
      hover: false
    }
  },
  watch: {
  },
  created() {
    this.path = this.filePath
    this.file = this.fileData
  },
  methods: {
    handleClick() {
      if (this.disabled) return
      this.$refs.input.click()
    },
    handleChange(e) {
      const files = e.target.files
      if (!files || !files.length) {
        return
      }
      this.file = files[0]
      this.path = this.file.name
      if (this.maxSize && this.file.size > this.maxSize) {
        this.error = true
        this.$message.error(`文件大小不能超过${fileSize(this.maxSize, 0)}`)
        this.$emit('change', [], '')
        return
      } else {
        this.error = false
        this.$emit('change', files, this.path)
      }
    },
    // 删除文件
    handleDeleteFile() {
      this.file = null
      this.path = ''
      this.$emit('removeFile', this.file, this.path)
    }
  }
}
</script>
