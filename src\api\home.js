import request from '@/utils/request'
const headers = { 'Content-Type': 'application/json;charset=UTF-8' }

// 进入系统
export function enterHomePageApi(name) {
  return request({
    url: `admin/userBehavior/enterSystem?systemName=${name}`,
    method: 'post',
    headers,
    data: {}
  })
}

// 查看系统介绍
export function viewSystemIntroApi(name) {
  return request({
    url: `admin/userBehavior/viewSystemIntro?systemName=${name}`,
    method: 'post',
    headers,
    data: {}
  })
}

// 查看应用场景
export function viewApplicationSceneApi(name) {
  return request({
    url: `admin/userBehavior/viewScene?sceneName=${name}`,
    method: 'post',
    headers,
    data: {}
  })
}
