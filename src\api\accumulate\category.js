import request from '@/utils/request'
const headers = { 'Content-Type': 'application/json;charset=UTF-8' }

// 获取分类
export function getCategory(data) {
  return request({
    url: `admin/sysExamCategory/queryPage`,
    method: 'post',
    headers,
    data
  })
}

// 新增分类
export function addCategory(data) {
  return request({
    url: 'admin/sysExamCategory/create',
    method: 'post',
    headers,
    data
  })
}

// 编辑分类
export function editCategory(data) {
  return request({
    url: 'admin/sysExamCategory/update',
    method: 'post',
    headers,
    data
  })
}

// 删除分类
export function deleteCategory(data) {
  return request({
    url: 'admin/sysExamCategory/remove',
    method: 'post',
    headers,
    data
  })
}

// 根据分类Id获取 题目数量
export function categoryComplexityNumber(data) {
  return request({
    url: 'admin/sysExamQuestion/categoryComplexityNumber',
    method: 'post',
    headers,
    data
  })
}
