<template>
  <div class="dialog-wrap">
    <el-form ref="form" :model="form" :rules="rules" class="form-wrap" label-position="left" label-width="100px">
      <el-form-item label="上传文件" prop="file">
        <div style="display: flex;">
          <div class="file-up">
            <el-button type="ghost" @click="selectFile()">上传文件</el-button>
          </div>
          <div v-show="form.file" :style="{ 'border-color': '#abdcff', 'background-color': '#f0faff' }" class="file-container">
            <i :style="{ 'color': '#2d8cf0' }" class="el-icon-document" size="16" />
            <span :title="form.file" style="margin: 10px 10px 0 0;">{{ form.file }}</span>
            <i :style="{ 'color': '#2d8cf0' }" class="el-icon-delete delete-icon delete" @click.stop="clearFileName()" />
          </div>
        </div>
      </el-form-item>
      <el-form-item label="知识标签" prop="value">
        <el-select v-model="form.value" placeholder="请选择">
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="分类" prop="value1">
        <el-select v-model="form.value1" placeholder="请选择">
          <el-option
            v-for="item in options1"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <input
      id="importInputFile"
      ref="userFileInput"
      type="file"
      @change="handleChange">
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>
<script>
import module from '../config.js'
import validate from '@/packages/validate'
// import { lessonPlanUpdate } from '@/api/teacher/index.js'

export default {
  name: 'UploadFile',
  components: {
  },
  mixins: [],
  props: {
    data: {
      type: Array,
      default: () => {
        return []
      }
    },
    name: {
      type: String
    }
  },
  data() {
    return {
      moduleName: module.name,
      validate: validate,
      form: {
        file: '',
        value: '',
        value1: ''
      },
      rules: {
        file: [validate.required(['blur', 'change'])],
        value: [validate.required(['blur', 'change'])],
        value1: [validate.required(['blur', 'change'])]
      },
      options: [],
      options1: [],
      maxSize: 1024 * 1024 * 10, // 文件最大大小
      fileTypes: ['xlsx', 'xls']
    }
  },
  computed: {
  },
  mounted() {
  },
  methods: {
    /**
     * 选择文件
     */
    selectFile() {
      this.$refs.userFileInput.click()
    },
    clearFileName() {
      this.form.file = ''
      this.$refs.userFileInput.value = ''
    },
    handleChange(e) {
      const files = e.target.files
      console.log('files', files)
      if (!files || !files.length) {
        return
      }
      const fileArr = files[0].name.split('.')
      const fileType = fileArr[fileArr.length - 1]
      if (!this.fileTypes.includes(fileType.toLowerCase())) {
        this.$message.error('支持上传.xlsx、.xls文件')
        return
      }
      if (this.maxSize && files[0].size > this.maxSize) {
        this.$message.error(`文件大小不能超过${this.$options.filters['transStore'](this.maxSize, 'B', 0)}`)
        return
      } else {
        this.form.file = files[0].name
      }
      this.$refs['form'].clearValidate('file')
    },
    updateFile(result) {
      if (!this.form.file) {
        this.$message.error('请选择导入文件')
      } else {
        this.loading = true
        const formData = new FormData()
        formData.append('file', this.form.file)
        formData.append('questionDepotId', this.form.questionDepotId)
        // importQuestion(formData).then(res => {
        //   this.loading = false
        //   if (result) {
        //     result(res)
        //   }
        //   this.$emit('success')
        // }).catch(() => {
        //   if (result) {
        //     result(false)
        //   }
        //   this.loading = false
        // })
      }
    },
    close: function() {
      this.$emit('close')
    },
    confirm: function() {
      this.$refs['form'].validate((valid, object) => {
        if (valid) {
          // this.submitLoading = true
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
.file-up {
  display: flex;
  align-items: center;
}
.file-container {
  overflow-wrap: break-word;
  word-break: normal;
  line-height: 1.5;
  border: 1px solid;
  margin-left: 5px;
  padding: 6px 18px 6px 10px;
  overflow: hidden;
  text-overflow:ellipsis;
  white-space: nowrap;
  cursor: pointer;
  position: relative;
  .delete {
    position: absolute;
    top: 10px;
    right: 10px;
  }
}

#importInputFile {
  display: none;
}
</style>
