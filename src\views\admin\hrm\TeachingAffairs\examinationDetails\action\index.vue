<template>
  <div class="buttons-wrap">
    <span v-if="options[0].className!==null">
      班级：<el-select v-model="value" filterable placeholder="请选择班级" @change="changeValue">
        <el-option
          v-for="(item,index) in options"
          :key="item.className"
          :label="item.className"
          :value="index"/>
      </el-select>
    </span>
    <el-button v-show="selectItem.length == 0" slot="action" type="primary" @click="clickDrop('allStudent')">导出</el-button>
    <el-button v-show="selectItem.length >= 1" slot="action" type="primary" @click="clickDrop('exportExcel')">导出</el-button>
    <el-dialog
      :title="titleMapping[modalName]"
      :visible.sync="modalShow"
      :width="modalWidth"
      @close="modalClose"
    >
      <transition name="el-fade-in-linear">
        <component
          :is="modalName"
          :name="modalName"
          :data="selectItem"
          @close="modalClose"
          @call="confirmCall"
          @exportOut="exportDetails('select')"
        />
      </transition>
    </el-dialog>
  </div>
</template>
<script>
import axios from 'axios'
import allStudent from '@/components/modal-export-all.vue'
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
import exportExcel from './modal-export.vue'
import { exportExcelFile } from '@/utils'

export default {
  components: {
    exportExcel,
    allStudent
  },
  mixins: [mixinsActionMenu],
  props: {
    timeParams: {
      type: Object
    },
    paramsData: {
      type: Object
    },
    transmitTime: {
      type: Object
    }
  },
  data() {
    return {
      // 弹窗title映射
      titleMapping: {
        'exportExcel': '导出',
        'allStudent': '导出'
      },
      confirmDisabled: false,
      classCode: this.$route.query.classCode,
      schedulingCode: this.$route.query.schedulingCode,
      options: JSON.parse(this.$route.query.resultList),
      value: 0
    }
  },
  inject: ['tableVm'],
  methods: {
    confirmCall: function(type, data) {
      if (type === 'close') {
        this.modalClose()
      } else if (type === 'changeClass') {
        this.$emit('changeClass', data)
      } else if (type === 'refresh') {
        this.$emit('call', type)
      }
    },
    // 选择班级
    changeValue(val) {
      this.confirmCall('changeClass', JSON.parse(this.$route.query.resultList)[val])
    },
    // 导出事务表
    exportDetails(value) {
      if (value === 'all') {
        this.exportAll()
      } else if (value === 'select') {
        this.exportSelect()
      }
    },
    exportAll() {
      const query = {
        ...this.timeParams
      }
      axios({
        method: 'post',
        url: '/api/training/PjtSysUser/exportStudentAffairDetails',
        data: query,
        responseType: 'blob'
      }).then((res) => {
        exportExcelFile(res, '考试列表')
      })
    },
    exportSelect() {
      const query = {
        ...this.timeParams,
        userId: []
      }
      this.selectItem.forEach(item => {
        query.userId.push(item.userId)
      })
      axios({
        method: 'post',
        url: '/api/training/PjtSysUser/exportStudentAffairDetails',
        data: query,
        responseType: 'blob'
      }).then((res) => {
        exportExcelFile(res, '考试列表')
      })
    }
  }
}
</script>
