<template>
  <div ref="orchestration-create-warp" class="orchestration-create-warp">
    <div v-loading="loading" class="orchestration-content-wrap">
      <!-- 右键菜单 -->
      <context-menu
        v-if="showContextMenu"
        ref="menuBar"
        :topology-data="topologyData"
        :graph="graph"
        :detail-type="type"
        :allow-links="allowLinks"
        :topic-node-list="topicNodeList"
        :show-console="showConsole"
        :system-module="testingModule"
        @close="showContextMenu = false"
        @open="open"
        @createNode="createNode"
        @batchCreateNode="batchCreateNode"
      />
      <!-- 左键控制台 -->
      <console-info
        v-if="showConsoleInfo"
        ref="consolInfo"
        :graph="graph"
        @close="showConsoleInfo = false"
        @open="open"
      />
      <div
        v-if="type === 'templatePermissions' || type === 'allPermissions' || showTestingElement"
        :class="{ 'hidden-left': hiddenLeft }"
        class="left-content scene-left-content"
      >
        <!-- 左侧操作 -->
        <div v-if="roleProperty === 1 || roleProperty === 2" class="back scene-back">
          <!-- 请选择下拉模板 -->
          <el-button type="primary" @click="open('selectTemplate')">选择模板</el-button>
          <el-button type="primary" @click="open('toTemplate')">生成模板</el-button>
        </div>
        <div v-else-if="(type === 'templatePermissions' && roleProperty === 0) || type === 'allPermissions'" class="back">
          <el-button v-if="type !== 'allPermissions'" type="primary" @click="canUndo ? open('back') : forceBack()">返回</el-button>
          <el-button type="primary" icon="el-icon-refresh" @click="refreshLeft"/>
          <el-button type="primary" @click="dndNodeIconSmall = !dndNodeIconSmall">切换</el-button>
        </div>
        <el-tabs v-if="roleProperty === 1" v-model="operationName" class="operation-content">
          <el-tab-pane label="网元设备" name="device"/>
          <el-tab-pane label="角色资产" name="scene"/>
        </el-tabs>
        <div v-loading="leftLoading" v-if="operationName === 'device'" class="dnd-warp">
          <!-- 设备搜索 -->
          <div v-if="roleProperty === 1 || roleProperty === 2" class="scene-back-content">
            <el-input v-model.trim="searchVal" :maxlength="64" placeholder="请输入设备名称" style="width:260px;">
              <i slot="suffix" class="el-input__icon el-icon-search"/>
            </el-input>
            <i class="el-icon-refresh" title="刷新" @click="refreshLeft"/>
            <i class="el-icon-sort rotate-90" title="切换" style="margin:0 20px 0 10px;" @click="dndNodeIconSmall = !dndNodeIconSmall"/>
          </div>
          <el-input v-else v-model.trim="searchVal" :maxlength="64" placeholder="请输入设备名称">
            <i slot="suffix" class="el-input__icon el-icon-search"/>
          </el-input>
          <div class="menu-dnd">
            <!-- 设备类型菜单 -->
            <el-tabs v-model="config.left.activeType" tab-position="right" class="device-list-menu" mode="vertical" @on-select="changeDeviceType">
              <el-tab-pane v-if="isShowpnf" label="物 理 设 备" name="pnf"/>
              <el-tab-pane label="虚 拟 设 备" name="vnf"/>
              <el-tab-pane label="图 形 设 备" name="inf"/>
              <el-tab-pane label="基 础 组 件" name="base"/>
            </el-tabs>
            <!-- 设备可拖拽列表 -->
            <div v-bar="{ preventParentScroll: true, scrollThrottle: 30}" id="dnd">
              <div>
                <div v-for="(value, key) in viewElementData" v-show="key === config.left.activeType" :key="key">
                  <el-collapse v-model="config.left.collapsed[key]">
                    <div v-if="!value.length" style="height: 48px; line-height: 48px; padding-left: 10px;">暂无分类</div>
                    <el-collapse-item v-for="category in value" v-else :key="category.category_id" :name="category.category_id">
                      <div slot="title">{{ category.category_name }}</div>
                      <div v-if="!category.devices.length" style="text-align: center; margin-bottom: 10px;">暂无数据</div>
                      <div v-for="device in category.devices" v-else :key="device.id" :class="{'is-disabled': device.disabled, 'small-icon': dndNodeIconSmall}" class="dnd-custom-node" @mousedown="startDrag($event, device)">
                        <div :class="device.virtual_type + '-node'" class="vue-node" >
                          <span class="dnd-node-icon">
                            <el-image v-if="device.icon && device.icon.value" :src="currentApi + device.icon.value"/>
                          </span>
                          <strong>{{ device.name }}</strong>
                        </div>
                      </div>
                    </el-collapse-item>
                  </el-collapse>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-if="operationName === 'scene'" class="scene-collapse">
          <el-collapse :value="expandList.length ? expandList : getRoleList(roleList)" @change="handleChange">
            <el-collapse-item v-for="(item, index) in roleList" :name="index" :key="item.id">
              <template slot="title">
                <div class="scene-collapse-title">
                  <div>
                    {{ item.initRoleName }} <i :title="roleIntroduceList[index]" class="header-icon el-icon-warning-outline"/>
                  </div>
                  <div @click.stop>
                    <el-switch
                      v-model="item.status"
                      :active-value="0"
                      :inactive-value="1"
                      @change="updateSceneTypeInitRole(item.id, item.status, item.initRoleName, index)"/>
                  </div>
                </div>
              </template>
              <div v-if="index === 0 || index === 1">
                <div class="add-role" @click="clickRole('addSceneRole', {sceneId: sceneId, initRoleId: item.id})"><i class="el-icon-plus"/> 创建角色</div>
                <div v-for="(role, roleIndex) in item.sceneRoleVOList" :key="roleIndex" style="margin-top:10px;">
                  <div class="scene-collapse-content">
                    <div v-overflow-tooltip class="role-name">
                      {{ role.sceneRoleName }}
                    </div>
                    <div>
                      <i :style="roleId === role.id ? 'color: var(--color-600)':''" title="查看当前角色资产" class="el-icon-view" @click="clickView(role)"/>
                      <i class="el-icon-edit" title="编辑当前角色资产" @click="clickRole('editSceneRole', {sceneId: sceneId, initRoleId: item.id, roleId: role.id, sceneRoleName: role.sceneRoleName})"/>
                      <i class="el-icon-delete" title="删除当前角色资产" @click="clickRole('deleteSceneRole', {roleId: role.id, sceneRoleName: role.sceneRoleName})"/>
                    </div>
                  </div>
                  <div style="padding: 10px 10px;">
                    <div>
                      物理设备（{{ role.networkElementList.filter(network => { return network.networkElementTypeName === '物理设备'}).length }}）
                    </div>
                    <div>
                      虚拟设备（{{ role.networkElementList.filter(network => { return network.networkElementTypeName === '虚拟设备'}).length }}）
                    </div>
                    <div>
                      图形设备（{{ role.networkElementList.filter(network => { return network.networkElementTypeName === '图形设备'}).length }}）
                    </div>
                  </div>
                </div>
              </div>
              <div v-else>
                <div v-for="(role, roleIndex) in item.sceneRoleVOList" :key="roleIndex">
                  <div class="scene-collapse-content" style="border:none;">
                    <div>
                      {{ role.sceneRoleName }}
                    </div>
                    <i class="el-icon-edit" title="编辑当前角色资产" @click="clickRole('editSceneRole', {sceneId: sceneId, initRoleId: item.id, roleId: role.id, sceneRoleName: role.sceneRoleName})"/>
                  </div>
                </div>
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>
        <div class="left-content-footer">
          <i v-if="!hiddenLeft" class="el-icon-s-fold" @click="hiddenLeft = !hiddenLeft" />
        </div>
        <div v-if="hiddenLeft" class="show-left-icon" @click="hiddenLeft = !hiddenLeft">
          <i class="el-icon-s-unfold" />
        </div>
      </div>
      <div :class="{'view-list': viewType === 'list'}" class="content" >
        <div v-if="showMasking" :class="{ 'percentage-0': percentage === 0 }" class="topo-masking">
          <i v-if="percentage === 100 && percentageData.error > 0" class="el-icon-close topo-masking-close" @click="showMasking = false" />
          <el-progress
            :text-inside="true"
            :stroke-width="20"
            :percentage="percentage"
            color="var(--color-600)"
          />
          <div v-if="percentage === 100 && percentageData.error > 0" style="margin-top: 5px;">启动拓扑失败，<span style="color: var(--color-600)">{{ percentageData.success }}</span> 个节点启动成功，<span style="color: #f56c6c">{{ percentageData.error }}</span> 个节点启动失败</div>
          <div v-else style="margin-top: 5px;">{{ percentageLabel }}</div>
        </div>
        <!-- 操作 -->
        <div class="operation-wrap">
          <div class="toolbar-left">
            <template v-if="titleButton === 1">
              <el-button type="primary" @click="saveAs('editTopology')">保存</el-button>
              <el-button v-if="!roleId" :disabled="!selectedCells.length" type="primary" @click="property('propertyAdd')">设为角色资产</el-button>
              <el-button v-if="roleId" :disabled="!selectedCells.length" type="primary" @click="property('propertySecure')">解除角色资产</el-button>
            </template>
            <template v-if="titleButton === 2 || (type === 'teamPermissions' && allowLinks === 1)">
              <el-button type="primary" @click="saveAs('editTopology')">保存</el-button>
            </template>
            <template v-if="titleButton === 3 || titleButtonQu === 3 || (titleButton === 0 && type === 'allPermissions')">
              <el-button v-if="titleButton === 3 || titleButtonQu === 3" type="primary" @click="open('selectTemplate')">选择模板</el-button>
              <el-button type="primary" @click="saveAs('editTopology')">保存</el-button>
            </template>
            <template v-if="type === 'allPermissions' || type === 'firingPermissions' || type === 'teamPermissions' || type === 'personalPermissions' || (testingModule === 'testingEdit')">
              <el-button type="primary" @click="saveAs('startTopology')">启动</el-button>
              <el-button type="primary" @click="open('cleanTopology')">释放</el-button>
              <el-button v-if="testingModule === 'testingEdit'" type="primary" @click="testingEditTopology()">{{ showTestingElement ? '取消编辑' : '编辑拓扑' }}</el-button>
              <el-tooltip v-if="isAutoRelease" class="item" effect="light" placement="top">
                <el-button v-if="releaseTopologyInfo && releaseTopologyInfo.isJoin != 0" :disabled="['', null, undefined].includes(releaseTopologyInfo)" type="primary" @click="delayTopology()">延时</el-button>
                <div slot="content">
                  【延时规则】每次点击可延长一个完整生命周期，仅限剩余时间不满足一个生命周期时使用。需取消定时释放请联系管理员配置。
                </div>
              </el-tooltip>
              <span v-if="releaseTopologyInfo && releaseTopologyInfo.releaseTime && isAutoRelease && releaseTopologyInfo.isJoin != 0" class="ml-5 orange-text">
                <div style="display: inline;">
                  <img :src="warningIcon" alt="" style="width: 16px; height: 16px; cursor: pointer; margin-bottom: -2px;"> 场景释放时间：{{ releaseTopologyInfo.releaseTime }}
                </div>
              </span>
            </template>
            <el-button v-if="showToConsole&&!showTestingElement" type="primary" @click="toConsole">进入控制台</el-button>
          </div>
          <div class="toolbar-right">
            <!-- 画布/实例切换 -->
            <el-radio-group v-if="false" v-model="viewType" size="mini" @change="changeViewType">
              <el-radio-button label="graph">画布</el-radio-button>
              <el-radio-button label="list">实例</el-radio-button>
            </el-radio-group>
            <!-- 任务 图标 end -->
            <el-select v-if="roleFilterate === 1 || roleFilterate === 2" v-model="ceneRoleVO" clearable placeholder="请选择角色" @change="getSceneRoleVO">
              <el-option
                v-for="item in sceneRoleVOList"
                :key="String(item.id)"
                :label="item.sceneRoleName"
                :value="String(item.id)"/>
            </el-select>
            <slot name="rightIcon"/>
            <a href="javascript:;" title="全屏" @click="toggleFullscreen()">
              <i class="el-icon-full-screen"/>
            </a>
            <a href="javascript:;" title="下载" @click="toPng()">
              <i class="el-icon-download"/>
            </a>
            <a v-if="!showGrid" href="javascript:;" title="设置网格" @click="showGrid = true">
              <svg t="1730684887692" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="11733" width="18" height="18">
                <path d="M853.333333 85.333333H170.666667C123.52 85.333333 85.333333 123.52 85.333333 170.666667v682.666666c0 47.146667 38.186667 85.333333 85.333334 85.333334h682.666666c47.146667 0 85.333333-38.186667 85.333334-85.333334V170.666667c0-47.146667-38.186667-85.333333-85.333334-85.333334zM341.333333 853.333333H170.666667v-170.666666h170.666666v170.666666z m0-256H170.666667v-170.666666h170.666666v170.666666z m0-256H170.666667V170.666667h170.666666v170.666666z m256 512h-170.666666v-170.666666h170.666666v170.666666z m0-256h-170.666666v-170.666666h170.666666v170.666666z m0-256h-170.666666V170.666667h170.666666v170.666666z m256 512h-170.666666v-170.666666h170.666666v170.666666z m0-256h-170.666666v-170.666666h170.666666v170.666666z m0-256h-170.666666V170.666667h170.666666v170.666666z" p-id="11734" fill="#181A1D" />
              </svg>
            </a>
            <a v-if="showGrid" href="javascript:;" title="取消网格" @click="showGrid = false">
              <svg t="1730683234462" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="10441" width="18" height="18">
                <path d="M0 118.186667L54.613333 64 960 969.386667 905.813333 1024l-85.333333-85.333333H170.666667c-46.08 0-85.333333-38.4-85.333334-85.333334V203.52l-85.333333-85.333333M426.666667 170.666667v157.013333l-85.333334-85.333333V170.666667H269.653333l-85.333333-85.333334H853.333333a85.333333 85.333333 0 0 1 85.333334 85.333334v669.866666l-85.333334-85.333333V682.666667h-71.68l-85.333333-85.333334H853.333333v-170.666666h-170.666666v157.013333l-85.333334-85.333333V426.666667h-71.68l-85.333333-85.333334H597.333333V170.666667h-170.666666m256 0v170.666666h170.666666V170.666667h-170.666666m0 682.666666h52.48L682.666667 800.853333V853.333333M170.666667 341.333333h52.48L170.666667 288.853333V341.333333m256 256h52.48L426.666667 544.853333V597.333333m170.666666 256v-137.813333l-32.853333-32.853333H426.666667v170.666666h170.666666m-256 0v-170.666666H170.666667v170.666666h170.666666m0-256v-137.813333L308.48 426.666667H170.666667v170.666666h170.666666z" fill="#181A1D" p-id="10442" />
              </svg>
            </a>
            <a href="javascript:;" title="放大" @click="setZoom(0.2)">
              <i class="el-icon-zoom-in"/>
            </a>
            <a href="javascript:;" title="缩小" @click="setZoom(-0.2)">
              <i class="el-icon-zoom-out"/>
            </a>
            <a href="javascript:;" title="恢复默认比例" @click="setZoomTo(1)">
              <i class="el-icon-c-scale-to-original"/>
            </a>
            <a v-if="type === 'templatePermissions' || type === 'allPermissions'" :disabled="!selectedCells.length" href="javascript:;" title="删除" @click="open('remove')">
              <i class="el-icon-delete"/>
            </a>
            <a v-if="type === 'templatePermissions' || type === 'allPermissions'" href="javascript:;" title="清空画布" @click="open('clear')">
              <i class="el-icon-brush"/>
            </a>
          </div>
        </div>
        <!-- 画布容器 -->
        <div class="container">
          <div :id="idPrefix + 'container'" />
        </div>
        <!-- 实例列表 -->
        <div v-if="viewType === 'list'" class="list-warp">
          <instance-table
            ref="table"
            :filter-data="{ 'topology_id': topoId || $route.query.id, 'instance_list': true }"
            :default-selected-arr="defaultSelectedArr"
            @refresh="refresh"
            @on-show-detail="showInsDetail"
            @on-select="tabelSelect"
            @on-current="tabelCurrent"
          >
            <action-menu
              slot="action"
              :select-item="selectItem"
              module-name="orchestration"
              @on-show-detail="showInsDetail"
              @call="actionHandler"
            />
          </instance-table>
        </div>
      </div>
    </div>
    <!--    实例侧拉弹窗详情 start -->
    <el-drawer
      :title="'实例：' + (selectItem.length ? selectItem[0].name : '')"
      :visible.sync="insDetailShow"
      :size="'720px'"
      direction="rtl"
      append-to-body
      class="ins-detail-view"
      @on-close="closeInsDetail"
    >
      <t-instance-detail v-if="insDetailShow" ref="insDetail" :ins-id="insDetailId" :type="type" :ins-data="selectItem" />
    </el-drawer>
    <!--    实例侧拉弹窗详情 end -->
    <!--    中部弹窗 start-->
    <el-dialog
      :title="titleMap[modalName]"
      :visible.sync="modalShow"
      :width="'520px'"
      append-to-body
      @close="modalClose"
    >
      <transition name="el-fade-in-linear">
        <component
          :is="modalName"
          :name="modalName"
          :node-id="activeData ? activeData.node_id : ''"
          :data="activeData"
          :type="type"
          :allow-links="allowLinks"
          :role-property="roleProperty"
          :graph="graph"
          @close="modalClose"
          @call="confirmCall"
        />
      </transition>
    </el-dialog>
    <!--    中部弹窗 end-->
    <!--    右侧弹窗 start-->
    <el-drawer
      :title="titleMap[drawerName]"
      :visible.sync="drawerShow"
      :before-close="drawerClose"
      :size="'720px'"
      direction="rtl"
      append-to-body>
      <transition name="el-fade-in-linear">
        <component
          :is="drawerName"
          :data="activeData"
          :type="type"
          :not-allowed-arr="[topoId || $route.query.id]"
          :graph="graph"
          @close="drawerClose"
          @call="confirmCall"
        />
      </transition>
    </el-drawer>
    <!--    右侧弹窗 end-->
  </div>
</template>

<script>
import moduleConf from './config'
import { mapState } from 'vuex'
// 公共组件
import tTableView from '../table-view/index.vue'
// 业务组件
import ContextMenu from './tpl/ContextMenu'
import ConsoleInfo from './tpl/ConsoleInfo'
import pnfForm from './form/pnfForm'
import vnfForm from './form/vnfForm'
import infForm from './form/infForm'
import baseForm from './form/baseForm'
import pnfView from './form/pnfView'
import vnfView from './form/vnfView'
import infView from './form/infView'
import baseView from './form/baseView'
import configPort from './action/modal_config_port'
import viewEdge from './action/modal_config_port'
import setZIndex from './action/modal_set-z-index'
import viewDetail from './action/modal_view_detail'
import addSceneRole from './action/modal_add'
import editSceneRole from './action/modal_add'
import deleteSceneRole from './action/modal_delete'
import propertyAdd from './action/property_add'
import propertySecure from './action/property_delete'
import toTemplate from './action/modal_add_template'
import remove from './action/modal_remove'
import clear from './action/modal_remove'
import back from './action/modal_back'
import webssh from './action/modal_webssh'
import webshell from './action/modal_webshell'
import selectTemplate from './action/select_template'
import createSnapshot from './action/modal_create_snapshot'
import toImage from './action/modal_to_image'
import modalCleanTopology from './action/modal_clean_topology'
// mixins
import mixinsActionMenu from './mixins/action_menu.js'
import graphMixin from './mixins/graph'
import getDevice from './mixins/get_device'
import deviceType from './mixins/device_type'
import instance from './mixins/instance'
// eslint-disable-next-line no-unused-vars
import { getItem, editNode, editTopology, getNodeItem, startTopology, sceneStartTopoApi, sceneReleaseTopoApi, queryReleaseTaskTopoApi, cleanTopology, sceneDelayedTopoApi, vmResume, pnfRelease, createTemplate } from './api/orchestration'
import { sceneTypeInitRole, updateSceneTypeInitRoleAPI, sceneTypeInitRoleInstance, createSceneRoleResourceRelAPI } from './api/role'
import currentHost from './mixins/current_host.js'
import { api as fullscreen } from 'vue-fullscreen'
import warningIcon from '../assets/images/warning.png'
import { queryFirstConfigByName } from '../api/index'

export default {
  name: 'Topo',
  components: {
    tTableView,
    ContextMenu,
    ConsoleInfo,
    pnfForm,
    vnfForm,
    infForm,
    baseForm,
    pnfView,
    vnfView,
    infView,
    baseView,
    configPort,
    viewEdge,
    setZIndex,
    viewDetail,
    toTemplate,
    remove,
    clear,
    back,
    webssh,
    webshell,
    createSnapshot,
    toImage,
    modalCleanTopology,
    addSceneRole,
    editSceneRole,
    deleteSceneRole,
    propertyAdd,
    propertySecure,
    selectTemplate
  },
  mixins: [currentHost, mixinsActionMenu, graphMixin, getDevice, deviceType, instance],
  props: {
    showToConsole: {// 进入控制台按钮是否展示
      type: Boolean,
      default: false
    },
    layout: { // 自动布局方式：不传则按照节点位置信息布局（目前支持 dagre：层次布局 ）
      type: String,
      default: ''
    },
    showConsole: { // 是否展示控制台
      type: Boolean,
      default: true
    },
    // 打开控制台方式
    // _self方式，将url通过consoleUrl传给原界面，由原界面进行iframe打开
    consoleTarget: {
      type: String,
      default: '_blank', // _blank：在新标签页打开；_self：当前页面加载
      validator: function(target) {
        return ['_blank', '_self'].indexOf(target) > -1
      }
    },
    topoId: {
      type: String,
      default: ''
    },
    /**
     * allPermissions: 所有权限
     * templatePermissions: 模板权限
     * firingPermissions: 启动权限
     * consolePermissions: 控制台权限
     * readPermissions: 只读权限
     * personalPermissions: 单兵演练权限
     * teamPermissions: 团体演练权限
     * examinationPermissions: 考试权限
     * matchPermissions: 比赛权限
     */
    topoType: {
      type: String,
      default: ''
    },
    // 头部按钮展示方式 0：不要头部按钮（默认），1：场景-场景编排“非单兵演练”头部按钮，2：场景-场景编排“单兵演练”头部按钮，3：“拓扑模板”头部按钮
    titleButton: {
      type: Number,
      default: 0
    },
    // 角色资产展示方式 0：“不展示角色资产”并且“不调用角色资产接口”，1：“展示角色资产”并且“调用角色资产接口”，2：“不展示角色资产”并且“调用角色资产接口”
    roleProperty: {
      type: Number,
      default: 0
    },
    // 角色过滤展示方法 0：不需要过滤方法，1：模板过滤方法，2：实例过滤方法，3：传入角色身份id进行过滤展示
    roleFilterate: {
      type: Number,
      default: 0
    },
    // 团队演练是否允许连线 0不允许 1允许
    allowLinks: {
      type: Number,
      default: 0
    },
    sceneId: {
      type: String,
      default: ''
    },
    sceneName: {
      type: String,
      default: ''
    },
    idPrefix: {
      type: String,
      default: ''
    },
    // 是否展示物理设备菜单
    isShowpnf: {
      type: Boolean,
      default: true
    },
    zoomToFit: { // 是否缩放（将画布中元素缩小或者放大一定级别，让画布正好容纳所有元素）
      type: Boolean,
      default: false
    },
    // 传入用户的身份id进行过滤展示
    userIdentity: {
      type: String,
      default: ''
    },
    topicNodeList: { // 团体演练：当前角色下所有的靶机题目
      type: Array,
      default: () => []
    },
    // 是否为实训模块
    isTrainingModule: {
      type: Boolean,
      default: false
    },
    // 检测管理系统模块: testingEdit 编辑权限，testingNo 无权限
    testingModule: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      showGrid: true, // 是否显示网格
      hiddenLeft: false, // 是否隐藏左侧菜单
      showMasking: false, // 是否展示蒙版
      percentageLabel: '', // 启动和释放的文字
      percentageData: { // 启动和释放的数据
        total: 0,
        success: 0,
        error: 0
      },
      titleMap: {
        'null': '',
        'pnfForm': '配置物理设备',
        'vnfForm': '配置虚拟设备',
        'infForm': '配置图形设备',
        'baseForm': '配置基础组件',
        'pnfView': '查看',
        'vnfView': '查看',
        'infView': '查看',
        'baseView': '查看',
        'selectTemplate': '选择拓扑模板',
        'configPort': '配置连接端口',
        'viewEdge': '查看配置',
        'setZIndex': '层叠顺序',
        'viewDetail': '查看拓扑详情',
        'toTemplate': '生成模板',
        'propertyAdd': '设为角色资产',
        'propertySecure': '解除角色资产',
        'addSceneRole': '创建角色',
        'editSceneRole': '编辑角色',
        'deleteSceneRole': '删除角色',
        'remove': '删除',
        'clear': '清空画布',
        'back': '返回',
        'webssh': '打开WebSSH',
        'webshell': '打开WebShell',
        'createSnapshot': '创建快照',
        'toImage': '生成镜像',
        'snapshotDel': '删除',
        'resumeSnapshot': '恢复快照',
        'startTopology': '启动',
        'cleanTopology': '释放',
        'modalCleanTopology': '释放',
        'vmStart': '虚拟机实例一键开机',
        'vmStop': '虚拟机实例一键关机',
        'vmPause': '虚拟机实例一键暂停',
        'vmSuspend': '虚拟机实例一键挂起',
        'vmResume': '虚拟机实例一键恢复',
        'vmDelete': '虚拟机实例一键删除',
        'containerStart': '容器实例一键启动',
        'containerDelete': '容器实例一键删除',
        'cloudStart': '云设备一键启动',
        'cloudDelete': '云设备一键删除',
        'pnfImport': '物理设备一键引入',
        'pnfRelease': '物理设备一键释放'
      },
      operationName: 'device',
      timer: null,
      batchTimer: null,
      modalMain: true, // 全屏弹窗
      loading: true, // 界面loading
      leftLoading: false, // 左侧分组loading
      viewType: 'graph', // 展示画布还是实例
      dndNodeIconSmall: true, // 左侧分组中节点图标是否是小图标
      showContextMenu: false, // 右键菜单
      showConsoleInfo: false, // 左键控制台
      activeData: null, // 弹窗和侧拉数据
      topologyData: null, // 拓扑数据
      topologyNodes: [], // 拓扑中的节点
      searchVal: '', // 搜索框的值
      elementData: { // 左侧分组的节点信息
        'pnf': [],
        'vnf': [],
        'inf': [],
        'base': []
      },
      config: { // 缓存在localStorage中的配置
        left: {
          activeType: 'pnf', // 当前所在的设备类型
          collapsed: { // 每个设备类型下处于关闭状态的分组
            'pnf': [],
            'vnf': [],
            'inf': [],
            'base': []
          }
        },
        positional: []
      },
      titleButtonQu: this.$route.query.titleButtonQu || 0,
      // sceneId: this.$route.query.sceneId || this.sceneId,
      releaseTopologyInfo: null, // 释放拓扑的信息
      warningIcon,
      roleList: [],
      expandType: true,
      activeNames: [0],
      expandList: [],
      roleId: '',
      networkElementList: [],
      sceneRoleVOList: [],
      ceneRoleVO: '',
      showTestingElement: false,
      isAutoRelease: false,
      ceneRoleVO2: '',
      roleIntroduceList: ['训练任务的执行者，通常是指攻防演练中的攻击方。', '训练任务的执行者，通常是指攻防演练中的防守方。', '训练的管理方，对红方、蓝方的成绩进行裁定评判，以及监控训练过程', '训练的管理方，负责训练活动的任务调度，控制训练流程和进度。', '训练的运维保障方，负责训练环境的维护、训练过程中的“日常事务”处理。']
    }
  },
  provide() {
    return {
      'topoVM': this
    }
  },
  computed: {
    ...mapState('socketListener', [
      'instanceSocket',
      'snapshotSocket',
      'topologySocket'
    ]),
    ...mapState('global', [
      'logoCode'
    ]),
    'type': function() {
      return this.topoType || this.$route.query.type
    },
    'percentage': function() {
      return this.percentageData.total ? Math.floor(((this.percentageData.success + this.percentageData.error) / this.percentageData.total) * 100) : 0
    },
    // 模糊搜索
    'viewElementData': function() {
      if (this.searchVal) {
        const data = {}
        for (const key in this.elementData) {
          data[key] = []
          const value = this.elementData[key]
          value.length && value.forEach(item => {
            const devices = item.devices.filter(val => val.name.toLowerCase().indexOf(this.searchVal.toLowerCase()) !== -1)
            data[key].push({
              'category_id': item.category_id,
              'category_name': item.category_name,
              'devices': devices
            })
          })
        }
        return data
      } else return this.elementData
    }
  },
  watch: {
    'topologySocket': function(val) {
      const payload = val.payload
      const message = payload.message ? JSON.parse(payload.message) : null
      if (payload.resource_id === this.topologyData.id && payload.option === 'progress') {
        this.percentageData = message
        if (message.success === message.total) {
          setTimeout(() => {
            this.showMasking = false
          }, 1000)
        }
      }
    },
    graph: async function() {
      if (this.roleFilterate == 3) {
        const result = await this.getSceneTypeInitRole()
        if (result) {
          this.getSceneRoleVO(this.userIdentity)
        }
      }
    },
    // 显示/隐藏网格
    'showGrid': function(bool) {
      bool ? this.graph.showGrid() : this.graph.hideGrid()
    },
    // 监听instance的websocket，只用于来更新画布上node的状态
    'instanceSocket': function(nval, oval) {
      this.socketHandle(nval, oval)
    },
    // 监听快照的websocket，只用于弹出创建快照成功的气泡
    snapshotSocket(val) {
      const payload = val.payload
      if (payload.state === 'created') {
        this.$bus.$emit('SHOW_NOTICE', {
          type: 'success',
          message: '快照创建操作成功！'
        })
      }
    }
  },
  created() {
    this.loadBase()
    this.graph = null
    const getList = [this.getItem()]
    const list = ['category_list', 'device_list']
    list.forEach(item => {
      if (!this[item]) getList.push(this['get_' + item]())
    })
    if (!this.isShowpnf) {
      this.config.left.activeType = 'vnf'
    }
    Promise.all(getList).then(() => {
      this.elementData = this.getElementData()
      // 有缓存时，走缓存
      const config = localStorage.getItem(this.topologyData.id + '_config')
      if (config) this.config = JSON.parse(config)
      this.initGraph()
    })
  },
  mounted() {
    this.$bus.$on('orchestration_module', (type, data) => {
      switch (type) {
        // 更新拓扑的数据
        case 'reloadItem':
          this.getItem()
          break
      }
    })
    this.$bus.$on('node_module', (type, data) => {
      switch (type) {
        // 发送请求更新节点的状态
        case 'reloadItem':
          this.reloadNodeItem(data)
          break
        // 根据数据更新节点状态
        case 'updateItem':
          data.node.updateData({ status: data.data.status })
          break
      }
    })
    if (this.sceneId && ((this.roleProperty === 1 || this.roleProperty === 2 || this.roleProperty === 3) || this.roleFilterate !== 0)) {
      this.getSceneTypeInitRole()
    }
    // 实训、场景获取延时信息
    if (this.isTrainingModule) {
      this.queryReleaseTaskTopo()
    }
  },
  beforeDestroy() {
    this.$bus.$off('orchestration_module')
    this.$bus.$off('node_module')
  },
  methods: {
    toConsole() {
      this.$emit('toConsole')
    },
    // 获取资源配置
    'loadBase': function(showLoading = true) {
      queryFirstConfigByName('automaticSceneRelease').then(res => {
        this.isAutoRelease = JSON.parse(res.data.value).isAutoRelease
      }).finally(() => {
        this.loading = false
      })
    },
    // 检测管理系统编辑拓扑
    testingEditTopology() {
      this.showTestingElement = !this.showTestingElement
      if (this.showTestingElement && this.testingModule === 'testingEdit') {
        this.$set(this, 'topoType', 'allPermissions')
        this.showContextMenu = true
      } else {
        this.$set(this, 'topoType', 'consolePermissions')
      }
    },
    toggleFullscreen() {
      const wrapperEl = this.$el.querySelector('.x6-graph-scroller')
      fullscreen.toggle(wrapperEl)
    },
    // socket消息处理
    'socketHandle': function(nval, oval) {
      const option = nval.payload.hasOwnProperty('option') ? nval.payload['option'] : (nval.payload.hasOwnProperty('event_type') ? nval.payload['event_type'] : null)
      const status = nval.payload.hasOwnProperty('state') ? nval.payload['state'] : null
      const id = nval.payload.hasOwnProperty('resource_id') ? nval.payload['resource_id'] : null
      if (option === 'delete' || option === 'created' || status === 'created') {
        return
      } else {
        if (status) {
          const cells = this.graph.getNodes()
          const node = cells.find(item => item.data.node_id === id)
          node && node.updateData({ status: status })
        }
        this.reloadNodeItem(id)
        return
      }
    },
    getRoleList(list) {
      if (this.expandType) {
        const arr = []
        list.forEach((item, index) => {
          if (item.status === 0) {
            arr.push(index)
          }
        })
        return arr
      }
    },
    handleChange(val) {
      this.expandType = false
      this.expandList = val
    },
    getSceneRoleVO(val) {
      if (!val) {
        this.clickView(this.sceneRoleVOList.filter(item => {
          return item.id == this.ceneRoleVO2
        })[0])
      } else {
        this.ceneRoleVO2 = val
        this.clickView(this.sceneRoleVOList.filter(item => {
          return item.id == this.ceneRoleVO2
        })[0])
      }
    },
    nextStep() {
      if (this.roleProperty === 2) {
        const arr = []
        const typeObj = {
          'pnf': '物理设备',
          'vnf': '虚拟设备',
          'inf': '图形设备'
        }
        this.graph.getCells().forEach(item => {
          if ((item.data && item.data.console !== 'base') && item.shape !== 'edge') {
            arr.push({ nodeId: item.data.node_id, networkElementTypeName: typeObj[item.data.type] })
          }
        })
        const postData = { topologyTemplateId: this.topoId || this.$route.query.id, sceneId: this.sceneId, sceneRoleId: this.roleList[0].sceneRoleVOList[0].id, networkElementList: arr }
        createSceneRoleResourceRelAPI(postData).then(res => {})
        return true
      }
      const arr = this.roleList.filter(item => {
        return (item.initRoleName === '红方' || item.initRoleName === '蓝方') && item.status === 0
      })
      if (!arr.length) {
        this.$message.error('至少需要开启红方或者蓝方')
        return false
      }
      for (let index = 0; index < arr.length; index++) {
        if (!arr[index].sceneRoleVOList.length) {
          this.$message.error('尚未为' + arr[index].initRoleName + '设置角色')
          return false
        }
      }
      return true
      // this.$router.push({ path: '/sceneStageTask', query: { sceneId: this.sceneId }})
    },
    clickView(item, type = true) {
      this.graph.getNodes().forEach(item => {
        item.show()
      })
      this.graph.cleanSelection()
      this.selectedCells = []
      if (this.roleId === item.id && type) {
        this.roleId = ''
        this.networkElementList = []
      } else {
        this.roleId = item.id
        const arr = []
        this.networkElementList = item.networkElementList
        item.networkElementList.forEach(item => {
          arr.push(item.nodeId)
        })
        const nodeList = this.graph.getNodes().filter(item => {
          return !arr.includes(item.data.node_id)
        })
        nodeList.forEach(item => {
          item.hide()
        })
      }
    },
    property(name) {
      this.modalName = name
      const arr = []
      this.selectedCells.forEach(item => {
        if (item.data.console !== 'base' && item.shape !== 'edge') {
          arr.push({ nodeId: item.data.node_id, name: item.data.name, type: item.data.type })
        }
      })
      if (name === 'propertySecure') {
        this.activeData = {
          sceneId: this.sceneId,
          topologyTemplateId: this.topoId || this.$route.query.id,
          data: arr,
          networkElementList: this.networkElementList,
          roleId: this.roleId
        }
      } else {
        this.activeData = {
          sceneId: this.sceneId,
          topologyTemplateId: this.topoId || this.$route.query.id,
          data: arr
        }
      }
    },
    clickRole(name, obj) {
      this.modalName = name
      if (this.modalName !== 'deleteSceneRole') {
        this.activeData = obj
      } else {
        this.activeData = [obj]
      }
    },
    getSceneTypeInitRole(name) {
      if (this.roleFilterate === 2 || this.roleFilterate === 3) {
        return sceneTypeInitRoleInstance({ pageType: 0, sceneInstanceId: this.sceneId, isInitRole: 0 }).then(res => {
          this.roleList = res.data.data.records
          const arr = this.roleList.filter(item => {
            return (item.initRoleName === '红方' || item.initRoleName === '蓝方') && item.status === 0 && item.sceneRoleVOList.length !== 0
          })
          if (arr.length == 1) {
            this.sceneRoleVOList = arr[0].sceneRoleVOList
          } else if (arr.length == 2) {
            this.sceneRoleVOList = [...arr[0].sceneRoleVOList, ...arr[1].sceneRoleVOList]
          }
          return this.sceneRoleVOList
        })
      } else {
        sceneTypeInitRole({ pageType: 0, sceneId: this.sceneId, isInitRole: 0 }).then(res => {
          this.roleList = res.data.data.records
          const arr = this.roleList.filter(item => {
            return (item.initRoleName === '红方' || item.initRoleName === '蓝方') && item.status === 0 && item.sceneRoleVOList.length !== 0
          })
          if (name === 'sceneRoleProperty') {
            this.roleList.forEach(item => {
              item.sceneRoleVOList.forEach(element => {
                if (element.id === this.roleId) {
                  this.clickView(element, false)
                }
              })
            })
          }
          if (arr.length == 1) {
            this.sceneRoleVOList = arr[0].sceneRoleVOList
          } else if (arr.length == 2) {
            this.sceneRoleVOList = [...arr[0].sceneRoleVOList, ...arr[1].sceneRoleVOList]
          }
        })
      }
    },
    updateSceneTypeInitRole(id, status, name, index) {
      this.expandType = false
      updateSceneTypeInitRoleAPI({ id: id, status: status }).then(res => {
        const str = res.data.data.status ? '角色关闭成功' : '角色开启成功'
        if (res.data.data.status) {
          if (this.expandList.indexOf(index) !== -1) {
            this.expandList.splice(this.expandList.indexOf(index), 1)
          }
        } else {
          this.expandList.push(index)
        }
        this.$message({
          message: name + str,
          type: 'success'
        })
      })
    },
    // 查询拓扑释放时间
    queryReleaseTaskTopo() {
      const postData = new FormData()
      postData.append('topologyId', this.topoId)
      queryReleaseTaskTopoApi(postData).then(res => {
        if ([0, 200].includes(res.data.code)) {
          this.releaseTopologyInfo = res.data.data
        }
      })
    },
    delayTopology() {
      this.loading = true
      const postData = new FormData()
      postData.append('topologyId', this.topologyData.id)
      sceneDelayedTopoApi(postData).then(res => {
        this.loading = false
        // 实训、场景获取延时信息
        if (this.isTrainingModule) {
          this.queryReleaseTaskTopo()
        }
        this.$bus.$emit('SHOW_NOTICE', {
          type: 'success',
          message: '延时成功！'
        })
        this.graph.cleanHistory()
      }).catch(() => {
        this.loading = false
        this.graph.cleanHistory()
      })
    },
    // 直接返回
    'forceBack': function() {
      this.config.positional = []
      localStorage.setItem(this.topologyData.id + '_config', JSON.stringify(this.config))
      this.$router.go(-1)
    },
    // 刷新左侧菜单，即更新所有设备
    'refreshLeft': function() {
      this.leftLoading = true
      const getList = [this.get_category_list(), this.get_device_list()]
      Promise.all(getList).then(() => {
        this.elementData = this.getElementData()
        // 刷新后置灰画布上已经有的物理设备
        const cells = this.graph.getCells()
        cells.forEach(item => {
          if (item.data.type === 'pnf') {
            this.setPDStatus(item.data.deviceName, true)
          }
        })
        this.leftLoading = false
      })
    },
    // 切换设备类型
    'changeDeviceType': function(value) {
      this.config.left.activeType = value
    },
    // 切换展示画布还是实例
    'changeViewType': function(value) {
      this.closeInsDetail()
      this.selectItem = []
    },
    // 下载图片
    toPng() {
      const name = this.sceneName + (this.sceneName ? '_' : '') + '拓扑图_' + this.$options.filters['moment'](new Date(), 'YYYYMMDD') + '.svg'
      const bbox = this.graph.getContentBBox()
      const padding = 20
      this.graph.exportSVG(name, {
        viewBox: {
          x: bbox.x - padding,
          y: bbox.y - padding,
          width: bbox.width + 2 * padding,
          height: bbox.height + 2 * padding
        },
        padding: padding,
        preserveDimensions: true,
        copyStyles: false,
        stylesheet: moduleConf.stylesheet
      })
    },
    // 保存、全局启动
    'saveAs': function(name, exit) {
      const json = this.graph.toJSON()
      this.loading = true
      this.graph.toPNG((data) => {
        const positional = []
        json.cells.forEach(item => {
          positional.push({
            id: item.id,
            position: item.position,
            zIndex: item.zIndex,
            fill: item.data.virtual_type === 'panel' && item.attrs && item.attrs.body ? item.attrs.body.fill : (item.data.virtual_type === 'text' && item.attrs && item.attrs.text ? item.attrs.text.fill : ''),
            size: item.size,
            text: item.data.virtual_type === 'text' ? item.attrs.text.text : '',
            connector: item.shape === 'edge' ? (item.connector || []) : null,
            vertices: item.shape === 'edge' ? (item.vertices || []) : [],
            edgeKey: item.shape === 'edge' ? `${item.data.source.node_name}_${item.data.source.port_name}_${item.data.dest.node_name}_${item.data.dest.port_name}` : ''
          })
        })
        const postData = {
          thumbnail: json.cells.length ? data : '',
          positional: JSON.stringify(positional)
        }
        if (name == 'startTopology') { // 启动
          this.loading = false
          this.showMasking = true
          this.percentageLabel = '场景创建进行中'
          this.percentageData = {
            total: 0,
            success: 0,
            error: 0
          }
          this.$bus.$emit('SINGLE_TASK_API', {
            contentType: this.isTrainingModule ? 'formData' : 'default',
            taskName: this.titleMap[name],
            resource: { id: this.topologyData.id, data: postData },
            apiObj: this.isTrainingModule ? sceneStartTopoApi : startTopology,
            data: { id: this.topologyData.id, data: postData },
            sucsessCallback: (res) => {
              // 实训、场景获取延时信息
              if (this.isTrainingModule) {
                this.queryReleaseTaskTopo()
              }
              this.loading = false
              this.$bus.$emit('orchestration_module', 'reload')
              this.graph.cleanHistory()
              if (exit) this.forceBack()
            },
            errorCallback: () => {
              this.loading = false
              this.$bus.$emit('orchestration_module', 'reload')
              this.graph.cleanHistory()
            }
          })
        } else if (name == 'editTopology') { // 保存
          editTopology(this.topologyData.id, postData).then(res => {
            this.$message.success(`保存成功`)
            this.loading = false
            this.graph.cleanHistory()
            if (exit) this.forceBack()
          }).catch(() => {
            this.loading = false
            this.graph.cleanHistory()
          })
        } else if (name == 'cleanTopology') { // 释放
          this.loading = false
          this.showMasking = true
          this.percentageLabel = '场景释放进行中'
          this.percentageData = {
            total: 0,
            success: 0,
            error: 0
          }
          this.$bus.$emit('SINGLE_TASK_API', {
            contentType: this.isTrainingModule ? 'formData' : 'default',
            taskName: this.titleMap[name],
            resource: { id: this.topologyData.id, data: postData },
            apiObj: this.isTrainingModule ? sceneReleaseTopoApi : cleanTopology,
            data: { id: this.topologyData.id, data: postData },
            sucsessCallback: (res) => {
              this.loading = false
              // 实训、场景获取延时信息
              if (this.isTrainingModule) {
                this.queryReleaseTaskTopo()
              }
              this.$bus.$emit('orchestration_module', 'reload')
              this.graph.cleanHistory()
              if (exit) this.forceBack()
            },
            errorCallback: () => {
              this.loading = false
              this.$bus.$emit('orchestration_module', 'reload')
              this.graph.cleanHistory()
            }
          })
        }
      }, {
        padding: 20,
        preserveDimensions: true,
        copyStyles: false,
        quality: 0.1,
        stylesheet: moduleConf.stylesheet
      })
    },
    'getTargetStatus': function(name) {
      let targetStatus = ''
      switch (name) {
        case 'vmStop':
          targetStatus = 'shutoff'
          break
        case 'vmPause':
          targetStatus = 'paused'
          break
        case 'vmSuspend':
          targetStatus = 'suspended'
          break
        case 'vmResume':
          targetStatus = 'running'
          break
        default:
          break
      }
      return targetStatus
    },
    // 一键操作、一键引入（待调试）
    'clickDrop': function(name) {
      switch (name) {
        case 'globalStart': // 全局启动
          this.saveAs('startTopology')
          break
        case 'vmStart': // 虚拟机实例一键开机
        case 'containerStart': // 容器实例一键启动
        case 'cloudStart': {
          // 云设备一键启动
          const postData = {}
          switch (name) {
            case 'vmStart':
              postData['instance_type'] = 'qemu'
              break
            case 'containerStart':
              postData['instance_type'] = 'docker'
              break
            case 'cloudStart':
              postData['instance_type'] = 'cloud_router'
              break
            default:
              break
          }
          this.$bus.$emit('SINGLE_TASK_API', {
            taskName: this.titleMap[name],
            resource: { id: this.topologyData.id, data: postData },
            apiObj: startTopology,
            data: { id: this.topologyData.id, data: postData },
            sucsessCallback: () => {},
            errorCallback: () => {}
          })
          break
        }
        case 'vmStop': // 虚拟机实例一键关机
        case 'vmPause': // 虚拟机实例一键暂停
        case 'vmSuspend': // 虚拟机实例一键挂起
        case 'vmResume': // 虚拟机实例一键恢复
          this.$bus.$emit('SINGLE_TASK_API', {
            taskName: this.titleMap[name],
            resource: this.topologyData,
            apiObj: this[name],
            data: { id: this.topologyData.id },
            sucsessCallback: (res) => {},
            errorCallback: () => {}
          })
          break
        case 'pnfImport': // 物理设备一键引入
        case 'pnfRelease': // 物理设备一键释放
          this.$bus.$emit('SINGLE_TASK_API', {
            taskName: this.titleMap[name],
            resource: this.topologyData,
            apiObj: this[name],
            data: { id: this.topologyData.id },
            sucsessCallback: (res) => {},
            errorCallback: () => {}
          })
          break
        case 'vmDelete': // 虚拟机实例一键删除
        case 'containerDelete': // 容器实例一键删除
        case 'cloudDelete': {
          // 容器实例一键删除
          let cells = this.graph.getCells()
          switch (name) {
            case 'vmDelete':
              cells = cells.filter(cell => cell.data.type === 'vnf' && cell.data.virtual_type === 'qemu')
              break
            case 'containerDelete':
              cells = cells.filter(cell => cell.data.type === 'vnf' && cell.data.virtual_type === 'docker')
              break
            case 'cloudDelete':
              cells = cells.filter(cell => cell.data.type === 'vnf' && cell.data.virtual_type === 'cloud_router')
              break
            default:
              break
          }
          this.deleteCells(cells)
          break
        }
        default:
          break
      }
    },
    consoleUrl(type, url, nodeId) {
      this.$emit('console-url', type, url, nodeId)
    },
    // 打开弹窗或者侧拉
    'open': function(name, data) {
      const cells = this.graph.toJSON().cells
      switch (name) {
        case 'selectTemplate':
          this.drawerName = 'selectTemplate'
          this.activeData = { cells }
          break
        // 配置节点
        case 'configNode':
          this.drawerName = data.data.type + 'Form'
          this.activeData = { 'node': data }
          break
        case 'viewConfigNode':
          this.drawerName = data.data.type + 'View'
          this.activeData = { 'node': data }
          break
        // 配置连接端口（连线）
        case 'configPort': {
          this.modalName = 'configPort'
          const sourceNode = this.graph.getCellById(data.source.cell) // 原节点
          const targetNode = this.graph.getCellById(data.target.cell) // 目标节点
          this.activeData = {
            'edge': data,
            'sourceNode': sourceNode,
            'targetNode': targetNode,
            'sourceNodeEdges': this.graph.getConnectedEdges(sourceNode), // 源节点的所有连线
            'targetNodeEdges': this.graph.getConnectedEdges(targetNode) // 目标节点所有连线
          }
          this.resetEdgePosition(data)
          break
        }
        // 查看连线配置（打开连线弹窗、只读Read only）
        case 'viewEdge': {
          this.modalName = 'viewEdge'
          const sourceNode = this.graph.getCellById(data.source.cell) // 原节点
          const targetNode = this.graph.getCellById(data.target.cell) // 目标节点
          this.activeData = {
            'edge': data,
            'sourceNode': sourceNode,
            'targetNode': targetNode
          }
          break
        }
        // 设置层级
        case 'setZIndex': {
          this.modalName = 'setZIndex'
          this.activeData = { 'node': data }
          break
        }
        // 拓扑信息
        case 'viewDetail': {
          this.modalName = 'viewDetail'
          this.activeData = Object.assign({}, this.topologyData, { cells })
          break
        }
        // 生成模板
        case 'toTemplate': {
          this.modalName = 'toTemplate'
          this.activeData = { 'topology_id': this.topologyData.id }
          break
        }
        // 删除元素
        case 'remove': {
          const deleteCells = typeof data === 'string' ? [this.graph.getCellById(data)] : this.selectedCells
          if (deleteCells.length) {
            this.modalName = 'remove'
            this.activeData = { cells: deleteCells }
          }
          break
        }
        // 清楚画布
        case 'clear': {
          this.modalName = 'clear'
          this.activeData = { cells }
          break
        }
        // 返回
        case 'back': {
          this.activeData = {}
          this.modalName = 'back'
          break
        }
        case 'cleanTopology': {
          this.activeData = {}
          this.modalName = 'modalCleanTopology'
          break
        }
        case 'webshell':
        case 'createSnapshot':
        case 'toImage':
          this.modalName = name
          this.activeData = data
          break
        default:
          break
      }
    },
    // 侧拉和弹窗确认
    'confirmCall': function(name, data) {
      switch (name) {
        // 配置节点
        case 'configNode': {
          this.editNode(this.activeData.node, data)
          break
        }
        case 'selectTemplate': {
          this.loading = true
          const cells = this.graph.getCells()
          getItem(data[0].id)
            .then(async res => {
              if (res.data.data.nodes.length) {
                await this.deleteCells(cells, false)
                createTemplate({ topology_id: this.topologyData.id, template_id: data[0].id })
                  .then(res => {
                    this.getItem()
                    this.loading = false
                  })
                  .catch((error) => {
                    this.$message.error(error.response.data.message)
                    this.loading = false
                  })
              } else {
                this.$set(data, 'toPngLoading', false)
                this.$bus.$emit('SHOW_NOTICE', {
                  type: 'error',
                  message: '该拓扑中暂无节点'
                })
                this.loading = false
              }
            })
            .catch((error) => {
              console.log(error)
              this.loading = false
            })
          break
        }
        // 配置连接端口（连线）
        case 'configPort': {
          this.createLinks(this.activeData, data)
          this.modalName = null
          break
        }
        // 删除
        case 'remove': {
          if (this.roleProperty === 1 || this.roleProperty === 2) {
            this.getSceneTypeInitRole()
          }
          this.deleteCells(this.activeData.cells)
          break
        }
        // 清楚画布
        case 'clear': {
          const cells = this.graph.getCells()
          this.deleteCells(cells, false)
          break
        }
        // 返回
        case 'back': {
          if (data) { // 保存变更
            this.saveAs('editTopology', true)
          } else { // 不保存变更直接返回
            this.forceBack()
          }
          break
        }
        // 查询角色
        case 'sceneRole': {
          this.getSceneTypeInitRole()
          break
        }
        case 'sceneRoleProperty': {
          this.getSceneTypeInitRole('sceneRoleProperty')
          break
        }
        // 释放
        case 'cleanTopology': {
          this.saveAs('cleanTopology')
          break
        }
        // 设置层级
        case 'setZIndex': {
          this.activeData.node.setZIndex(data.zIndex)
          break
        }
        default:
          break
      }
    },
    // 创建节点（复制节点）
    'createNode': function(node, isCopy = false) {
      const postData = {
        'topology_id': this.topologyData.id,
        'update_nodes': {
          'adds': [{
            'network_element_id': node.data.id,
            'name': node.data.name,
            'description': node.data.description,
            'cpu': node.data.cpu,
            'ram': node.data.ram,
            'admin_user': node.data['image_os_type'] === 'linux' ? 'root' : 'administrator',
            'admin_pass': 'Password@2',
            'console_type': node.data.console_type,
            'position': JSON.stringify({
              id: node.id,
              position: node.toJSON().position,
              size: node.toJSON().size,
              text: node.data.virtual_type === 'text' ? node.attrs.text.text : ''
            })
          }]
        }
      }
      // 如果不是物理设备，需要拼接名称
      if (node.data.type !== 'pnf' && !isCopy) {
        const cells = this.graph.getNodes()
        let name = node.data.name + '-' + this.randomNum(1, 9999)
        while (cells.find(item => item.data.name === name)) {
          name = node.data.name + '-' + this.randomNum(1, 9999)
        }
        postData['update_nodes']['adds'][0]['name'] = name
      }
      const name = postData['update_nodes']['adds'][0]['name']
      const deviceName = node.data.name
      const devicePorts = JSON.parse(JSON.stringify(node.data.ports))
      node.updateData({
        'loading': true,
        'name': name,
        'description': node.data.description,
        'admin_user': node.data['image_os_type'] === 'linux' ? 'root' : 'administrator',
        'admin_pass': 'Password@2'
      })
      if (!this.viewComponent.includes(node.data.virtual_type)) {
        const textWidth = this.getTextWidth(name, 13, node.data)
        node.size(textWidth < 50 ? 50 : textWidth, 70)
      }
      editTopology(this.topologyData.id, postData)
        .then(res => {
          this.checkCreate(name, (data) => {
            const obj = {
              'loading': false,
              'topology_id': this.topologyData.id,
              'node_id': data.id,
              'cidr': data.cidr,
              'gateway': data.gateway,
              'status': data.status,
              'ports': data.ports,
              'resource_type': data.resource_type
            }
            if (!isCopy) { // 如果是拷贝的节点则不用设置设备name和ports
              obj.deviceName = deviceName
              obj.devicePorts = devicePorts
            }
            node.updateData(obj)
          })
        })
        .catch((error) => {
          node.updateData({
            'status': 'error',
            'loading': false
          })
          this.$message.error(error.response.data.message)
        })
    },
    // 批量调用接口创建节点（批量复制）
    'batchCreateNode': function(nodes) {
      const postData = {
        'topology_id': this.topologyData.id,
        'update_nodes': {
          'adds': []
        }
      }
      nodes.forEach(node => {
        postData['update_nodes']['adds'].push({
          'network_element_id': node.data.id,
          'name': node.data.name,
          'description': node.data.description,
          'cpu': node.data.cpu,
          'ram': node.data.ram,
          'admin_user': node.data['image_os_type'] === 'linux' ? 'root' : 'administrator',
          'admin_pass': 'Password@2',
          'console_type': node.data.console_type,
          'position': JSON.stringify({
            id: node.id,
            position: node.toJSON().position,
            size: node.toJSON().size,
            text: node.data.virtual_type === 'text' ? node.attrs.text.text : ''
          })
        })
        node.updateData({ 'loading': true })
        if (!this.viewComponent.includes(node.data.virtual_type)) {
          const textWidth = this.getTextWidth(node.data.name, 13, node.data)
          node.size(textWidth < 50 ? 50 : textWidth, 70)
        }
      })
      editTopology(this.topologyData.id, postData)
        .then(res => {
          this.batchCheckCreate(nodes, (arr) => {
            arr.forEach(data => {
              const node = nodes.find(item => item.data.name === data.name)
              node.updateData({
                'loading': false,
                'topology_id': this.topologyData.id,
                'node_id': data.id,
                'cidr': data.cidr,
                'gateway': data.gateway,
                'status': data.status,
                'ports': data.ports,
                'resource_type': data.resource_type
              })
            })
          })
        })
        .catch((error) => {
          nodes.forEach(node => {
            node.updateData({
              'status': 'error',
              'loading': false
            })
            this.$message.error(error.response.data.message)
          })
        })
    },
    // 轮询检查多个节点是否创建成功
    'batchCheckCreate': function(arr, callback) {
      getItem(this.topologyData.id)
        .then(res => {
          clearTimeout(this.batchTimer)
          const successArr = res.data.data.nodes.filter(item => arr.some(sub => item.name === sub.data.name))
          if (successArr.length === arr.length) {
            callback(successArr)
          } else {
            this.batchTimer = setTimeout(() => {
              this.batchCheckCreate(arr, callback)
            }, 2000)
          }
        })
        .catch(() => {
          clearTimeout(this.batchTimer)
          this.batchTimer = setTimeout(() => {
            this.batchCheckCreate(arr, callback)
          }, 2000)
        })
    },
    // 轮询检查单个节点是否创建成功
    'checkCreate': function(name, callback) {
      getItem(this.topologyData.id)
        .then(res => {
          clearTimeout(this.timer)
          const data = res.data.data.nodes.find(item => item.name === name)
          if (data) {
            callback(data)
          } else {
            this.timer = setTimeout(() => {
              this.checkCreate(name, callback)
            }, 2000)
          }
        })
        .catch(() => {
          clearTimeout(this.timer)
          this.timer = setTimeout(() => {
            this.checkCreate(name, callback)
          }, 2000)
        })
    },
    // 编辑节点
    'editNode': function(node, data) {
      const originData = node.data
      const postData = data
      postData['position'] = JSON.stringify({
        id: node.id,
        position: node.toJSON().position,
        size: node.toJSON().size,
        text: node.data.virtual_type === 'text' ? node.attrs.text.text : ''
      })
      // 更新数据
      node.updateData(data)
      if (!this.viewComponent.includes(node.data.virtual_type)) {
        const textWidth = this.getTextWidth(data.name, 13, originData)
        node.size(textWidth < 50 ? 50 : textWidth, 70)
      }
      return new Promise((resolve, reject) => {
        editNode(node.data.node_id, postData)
          .then(res => {
            resolve()
          })
          .catch((error) => {
            // 更新节点失败,画布上对应的节点数据更新为之前的数据
            node.replaceData(originData)
            const textWidth = this.getTextWidth(originData.name, 13, originData)
            node.size(textWidth < 50 ? 50 : textWidth, 70)
            this.$bus.$emit('SHOW_NOTICE', {
              type: 'error',
              message: error.response.data.message
            })
            reject()
          })
      })
    },
    // 创建连线
    'createLinks': async function(activeData, data) {
      // 更新逻辑交换机的cidr和gateway
      if (data.source_cidr || data.source_gateway) {
        await this.editNode(activeData.sourceNode, {
          name: activeData.sourceNode.data.name,
          cidr: data.source_cidr,
          gateway: data.source_gateway
        })
      }
      if (data.target_cidr || data.target_gateway) {
        await this.editNode(activeData.targetNode, {
          name: activeData.targetNode.data.name,
          cidr: data.target_cidr,
          gateway: data.target_gateway
        })
      }
      // 更新双方节点的ports
      activeData.sourceNode.updateData({ ports: data.sourcePorts })
      activeData.targetNode.updateData({ ports: data.targetPorts })
      // 设置连线的数据和标签
      activeData.edge.setData(data.edgeData)
      activeData.edge.setLabels(data.edgeLabels)
      const postData = {
        'topology_id': this.topologyData.id,
        'update_links': {
          'adds': [{
            source: data.edgeData.source,
            dest: data.edgeData.dest,
            network_type: data.edgeData.network_type,
            segmentation_id: data.edgeData.segmentation_id
          }]
        }
      }
      editTopology(this.topologyData.id, postData)
        .then(res => {})
        .catch((error) => {
          activeData.edge.updateData({ 'status': 'error' })
          this.setEdgeAttr([activeData.edge], false)
          this.$message.error(error.response.data.message)
        })
    },
    // 删除节点或连线
    'deleteCells': async function(cells, isDelLink = true) {
      if (cells.length) {
        const postData = {
          'topology_id': this.topologyData.id,
          'update_nodes': {
            'removes': []
          },
          'update_links': {
            'removes': []
          }
        }
        // 是否删除连线
        if (isDelLink) {
          cells.forEach(item => {
            if (item.shape === 'edge') {
              postData['update_links']['removes'].push({
                'source': item.data.source,
                'dest': item.data.dest
              })
              const sourceNode = this.graph.getCellById(item.source.cell)
              const targetNode = this.graph.getCellById(item.target.cell)
              // 更新双方节点的ports
              const sourcePorts = sourceNode.data.ports
              const targetPorts = targetNode.data.ports
              sourcePorts.forEach(port => {
                if (port.name === item.data.source.port_name) {
                  port['topology_link_to'] = null
                }
              })
              targetPorts.forEach(port => {
                if (port.name === item.data.dest.port_name) {
                  port['topology_link_to'] = null
                }
              })
              sourceNode.updateData({ ports: sourcePorts })
              targetNode.updateData({ ports: targetPorts })
              this.graph.removeCell(item.id)
              this.resetEdgePosition(item)
            }
          })
        }
        cells.forEach(item => {
          if (item.shape === 'custom-vue-node' || item.shape === 'rect') {
            if (item.data.node_id) {
              postData['update_nodes']['removes'].push({
                'id': item.data.node_id,
                'name': item.data.name
              })
            }
            // 如果是物理设备节点，恢复置灰状态
            if (item.data.type === 'pnf') {
              this.setPDStatus(item.data.node_id ? item.data.deviceName : item.data.name, false)
            }
            this.graph.removeCell(item.id)
          }
        })
        if (postData['update_nodes']['removes'].length || postData['update_links']['removes'].length) {
          if (postData.update_nodes.removes.length) {
            const nodes = this.graph.getNodes()
            nodes.forEach(element => {
              if (element.data.ports) {
                element.data.ports.forEach(port => {
                  if (port.topology_link_to) {
                    postData.update_nodes.removes.forEach(item => {
                      if (port.topology_link_to.node_name === item.name) {
                        port.topology_link_to = null
                      }
                    })
                  }
                })
              }
            })
          }
          return new Promise((resolve, reject) => {
            editTopology(this.topologyData.id, postData)
              .then(res => {
                resolve()
              })
              .catch((error) => {
                this.$message.error(error.response.data.message)
                reject()
              })
          })
        }
      }
    },
    // 更新单个节点状态
    'reloadNodeItem': function(data) {
      let id = ''
      let node = null
      if (typeof data === 'string') {
        id = data
        const cells = this.graph.getNodes()
        node = cells.find(item => item.data.node_id === data)
      } else {
        id = data.node.data.node_id
        node = data.node
      }
      getNodeItem(id)
        .then(res => {
          node.updateData({ status: res.data.data.status })
        })
        .catch((error) => {
          console.log(error)
        })
    },
    // 批量更新节点的状态
    'updateNodesStatus': function(data) {
      const cells = this.graph.getNodes()
      let nodes = []
      if (data) nodes = data.nodes
      nodes.forEach(node => {
        // 更新状态
        cells.forEach(cell => {
          if (cell.data.node_id === node.id) {
            cell.updateData({ status: node.status })
          }
        })
      })
    },
    // 获取拓扑详情数据
    'getItem': function() {
      return new Promise((resolve, reject) => {
        getItem(this.topoId || this.$route.query.id)
          .then(res => {
            this.topologyData = res.data.data
            this.topologyNodes = res.data.data.nodes
            resolve()
            this.echoGraph()
          })
          .catch((error) => {
            console.log(error)
            reject()
          })
      })
    },
    // 处理左侧菜单需要的数据
    'getElementData': function() {
      const data = { 'pnf': [], 'vnf': [], 'inf': [], 'base': [] }
      this.category_list.forEach(category => {
        const typeData = {
          'category_id': category.id,
          'category_name': category.name,
          'devices': []
        }
        this.device_list.forEach(device => {
          if (category.id === device.category) {
            typeData['devices'].push(device)
          }
        })
        data[category.type].push(typeData)
      })
      return data
    },
    // 生成随机数 min-max
    'randomNum': function(min, max) {
      return Math.floor(Math.random() * (max - min) + min)
    },
    // 修改左侧物理节点的置灰状态
    'setPDStatus': function(name, flag) {
      this.elementData.pnf.length && this.elementData.pnf.forEach((category, index) => {
        category.devices.length && category.devices.forEach((device, subIndex) => {
          if (device.name === name) {
            this.$set(this.elementData.pnf[index].devices[subIndex], 'disabled', flag)
          }
        })
      })
    }
  }
}
</script>
<style lang="less">
.x6-node foreignObject > body {
  min-width: 0;
}
</style>
<style lang="less" scoped>
.orchestration-create-warp {
  height: 100%;
  overflow: hidden;
  .scene-orchestration-content-wrap {
    height: calc(100% - 60px) !important;
  }
  .orchestration-content-wrap {
    flex: 1;
    height: 100%;
    display: flex;
    position: relative;
    ::v-deep .x6-graph-scroller {
      display: block;
      // background-image: url('./background.png');
      // background-size: 100% 100%;
      // background-repeat: no-repeat;
    }
    .list-warp {
      display: none;
    }
    .view-list {
      ::v-deep .x6-graph-scroller {
        display: none;
      }
      .list-warp {
        flex: 1;
        display: block;
        height: calc(100% - 48px);
        padding-bottom: 0;
      }
      .resource-table {
        height: 100%;
      }
    }
    .scene-left-content {
      .scene-collapse {
        height: 100%;
        background: #Fff;
        overflow: auto;
        ::v-deep {
          .el-collapse {
            margin-top: 10px;
            padding: 0 10px;
            .scene-collapse-title {
              display: flex;
              width: 80%;
              align-items: center;
              justify-content: space-between;
            }
            .el-collapse-item__wrap {
              background-color: #f6f6f6;
              border: none;
              .el-collapse-item__content {
                padding: 10px 0;
              }
            }
            .add-role {
              display: flex;
              color: var(--color-600);
              align-items: center;
              margin-left: 8px;
              cursor: pointer;
            }
            .role-name {
              width: 110px;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
            }
            .scene-collapse-content {
              display: flex;
              align-items: center;
              justify-content: space-between;
              font-size: 14px;
              padding: 0 10px;
              border-bottom: 1px solid #dcdee2;
              i {
                cursor: pointer;
              }
            }
          }
        }
      }
    }
    .left-content {
      width: 230px;
      display: flex;
      flex-direction: column;
      &.hidden-left {
        width: 0;
        position: relative;
        .dnd-warp .el-input {
          display: none;
        }
      }
      .back {
        height: 48px;
        padding: 8px;
        background: #Fff;
        border-bottom: 1px solid #dcdee2;
        border-right: 1px solid #dcdee2;
      }
      .operation-content {
        background: #fff;
        ::v-deep {
          .el-tabs__header{
            margin: 0;
          }
          .el-tabs__nav-scroll {
            padding: 0 20px;
          }
        }
      }
      .scene-back {
        border-right: 0;
      }
      .dnd-warp {
        flex: 1;
        height: calc(100% - 127px);
        display: flex;
        flex-direction: column;
        z-index: 99;
        background: #fff;
        box-shadow: 6px 0px 6px 0 rgba(246, 246, 246, 1);
        .scene-back-content {
          display: flex;
          align-items: center;
          justify-content: space-between;
          i {
            cursor: pointer;
          }
          .rotate-90 {
            transform: rotate(90deg)
          }
        }
        .el-input {
          margin: 10px;
          width: auto;
          ::v-deep input {
            border-radius: 8px;
          }
          ::v-deep .el-input__suffix {
            cursor: pointer;
          }
        }
        .menu-dnd {
          display: flex;
          flex: 1;
          height: calc(100% - 52px);
          ::v-deep{
            .is-scrollable {
              min-width: 40px;
            }
          }
        }
      }
      .left-content-footer {
        width: 100%;
        border-top: 1px solid var(--neutral-300);
        padding: 12px 0;
        text-align: center;
        cursor: pointer;
        height: 40px;
        box-shadow: 6px 0px 6px 0 #f6f6f6;
        background: #ffffff;
        z-index: 99;
      }
      .show-left-icon {
        position: absolute;
        bottom: 0;
        width: 60px;
        height: 40px;
        line-height: 40px;
        text-align: center;
        cursor: pointer;
        box-shadow: 4px -4px 6px 0 #f6f6f6;
        background: #ffffff;
        z-index: 100;
      }
    }
    .device-list-menu {
      margin-top: 0px;
      z-index: 99;
      box-shadow: 3px 3px 6px 0 rgba(0,0,0,.1);
      min-width: 40px;
      ::v-deep {
        .el-tabs__item {
          height: auto;
          writing-mode: tb-rl;
          padding: 20px 0 !important;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 15px;
          font-weight: 500;
          border: none;
        }
      }
      ::v-deep .ivu-menu-item-active:not(.ivu-menu-submenu):after {
        left: 0;
        right: unset;
      }
    }
    ::v-deep {
      .vb > .vb-dragger > .vb-dragger-styler {
        background-color: rgba(127, 127, 127, 0);
      }
      .vb.vb-scrolling-phantom > .vb-dragger > .vb-dragger-styler {
        background-color: rgba(127, 127, 127, 0.2);
      }
      .vb > .vb-dragger:hover > .vb-dragger-styler {
        background-color: rgba(127, 127, 127, 0.5);
      }
      .vb.vb-dragging > .vb-dragger > .vb-dragger-styler {
        background-color: rgba(127, 127, 127,.5);
      }
      .vb.vb-dragging-phantom > .vb-dragger > .vb-dragger-styler {
        background-color: rgba(127, 127, 127,.5);
      }
    }
    #dnd {
      position: relative;
      width: 170px;
      margin-left: 20px;
      ::v-deep .vb-content {
        &::-webkit-scrollbar {
          display: none; /* Chrome Safari */
        }
        scrollbar-width: none; /* firefox /
        -ms-overflow-style: none; / IE 10+ */
      }
      .dnd-custom-node {
        display: inline-block;
        margin-left: 10px;
        margin-bottom: 10px;
        .vue-node {
          display: flex;
          flex-direction: column;
          align-items: center;
          text-align: center;
          cursor: pointer;
          .dnd-node-icon {
            display: block;
            height: 50px;
            width: 70px;
            position: relative;
            & ::v-deep img {
              width: 50px;
              height: 50px;
            }
          }
          strong {
            text-align: center;
            display: block;
            font-size: 13px;
            font-weight: 400;
            word-break: break-all;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
            width: 70px;
          }
        }
        .panel-node, .text-node {
          position: relative;
          .dnd-node-icon {
            width: 50px;
            height: 50px;
            margin: 10px;
            border: 1px solid #8F8F8F;
          }
          >strong {
            width: auto;
            position: absolute;
            top: 23px;
            left: 23px;
          }
        }
        .text-node .dnd-node-icon {
          border-style: dashed;
        }
        &.small-icon {
          width: 100%;
          margin-left: 0px;
          padding-left: 10px;
          .vue-node {
            flex-direction: row;
            .dnd-node-icon {
              height: 30px;
              width: 30px;
              margin-right: 8px;
              & ::v-deep img {
                width: 30px;
                height: 30px;
              }
            }
            strong {
              text-align: start;
              font-size: 13px;
              width: auto;
            }
          }
          .panel-node, .text-node {
            .dnd-node-icon {
              width: 30px;
              height: 30px;
              margin: 0;
            }
            >strong {
              top: 2px;
              left: 38px;
              font-size: 13px;
            }
          }
        }
        &.is-disabled {
          .vue-node {
            background-color: #f7f7f7;
            cursor: not-allowed;
            strong {
              color: #c5c8ce;
            }
          }
        }
      }
      .el-collapse {
        border: none;
        ::v-deep .el-collapse-item__header {
          font-weight: normal;
          padding-left: 10px;
          padding-right: 10px;
          border: none;
          font-size: 14px;
          font-weight: 400;
          >div {
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
          }
        }
        ::v-deep .el-collapse-item__wrap {
          background-color: #f6f6f6;
          border: none;
          .el-collapse-item__content {
            padding: 10px 0 0 0;
          }
        }
      }
    }
    .scene-content {
      width: calc(100% - 300px) !important;
    }
    .content {
      flex: 1;
      width: calc(100% - 230px);
      display: flex;
      flex-direction: column;
      position: relative;
      .topo-masking {
        position: absolute;
        height: 100%;
        width: 100%;
        background: linear-gradient(rgba(0, 0, 0, 0.15), rgba(0, 0, 0, 0.15)), rgba(255, 255, 255, 0.9);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        z-index: 999;
        .topo-masking-close {
          position: absolute;
          top: 15px;
          right: 15px;
          font-size: 20px;
          cursor: pointer;
        }
        ::v-deep .el-progress {
          width: 50%;
          margin-top: -90px;
        }
        &.percentage-0 {
          ::v-deep .el-progress-bar__innerText {
            color: rgba(0, 0, 0, 0.85);
          }
        }
      }
      >.operation-wrap {
        height: 48px;
        padding: 8px;
        background: #fff;
        border-bottom: 1px solid #dcdee2;
        margin-bottom: 0px;
      }
      >.operation-wrap:after {
        display: block;
        content: ' ';
        clear: both;
      }
      .toolbar-left {
        float: left;
      }
      .toolbar-right {
        float: right;
        margin-right: 10px;
        .el-select {
          top: -2px;
        }
        .el-radio-group {
          margin: -7px 10px 0 0;
        }
        .el-radio-button:first-child .el-radio-button__inner {
          border-radius: 8px 0 0 8px;
          padding: 7px 10px;
        }
        .el-radio-button:last-child .el-radio-button__inner {
          border-radius: 0 8px 8px 0;
          padding: 7px 10px;
        }
        .el-dropdown {
          top: 1px;
          color: #000;
          cursor: pointer;
          &:hover {
            background-color: #edf5ff;
          }
          i {
            padding: 0 10px;
            height: 32px;
            line-height: 32px;
          }
          i.ivu-icon {
            vertical-align: baseline;
          }
        }
        a {
          width: 38px;
          height: 32px;
          color: #000;
          cursor: pointer;
          display: inline-block;
          &:has(svg) {
            vertical-align: middle;
            text-align: center;
            padding-top: 7px;
            margin-top: -7px;
          }
          &:hover {
            background-color: #edf5ff;
          }
          &[disabled=disabled] {
            color: #c5c8ce!important;
            cursor: not-allowed;
            background-color: transparent !important;
            border-color: transparent !important;
          }
          i {
            font-size: 18px;
            padding: 0 10px;
            height: 32px;
            line-height: 32px;
          }
        }
      }
      .container {
        flex: 1;
        min-height: 0;
        width: 100% !important;
        background: #fff;
        ::v-deep .x6-widget-transform {
          margin: -1px 0 0 -1px;
          padding: 0;
          border: 1px solid #37668c;
        }
        ::v-deep .x6-node-tool-editor {
          background-color: transparent !important;
        }
      }
    }
  }
  .scene-create-footer {
    width: 100%;
    padding: 0 24px;
    border-top: 1px solid var(--neutral-300);
    background-color: var(--neutral-0);
    .create-footer-content {
      height: 60px;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      margin: 0 auto;
      width: 80%;
      min-width: 832px;
      max-width: 1200px;
    }
    .el-button--text {
      padding: 4px 15px;
    }
  }
}
::v-deep .selection-node {
  .x6-widget-selection-inner, .x6-widget-selection-box {
    border-color: #37668c;
  }
}
.remove-warning-tip {
  font-size: 14px;
  display: block;
  padding: 5px 0px 15px;
  i {
    color: rgb(246, 105, 2);
    font-size: 16px;
  }
}
.detail-view {
  &.header-job-detail-view, &.ins-detail-view {
    position: relative;
    z-index: 10000;
    .ivu-drawer {
      top: 0px;
      height: 100%;
    }
  }
  &.ins-detail-view {
    z-index: 1000;
    .ivu-drawer {
      top: 161px !important;
    }
  }
}
.orange-text {
  color: #ff9900;
}
</style>
