<template>
  <div class="buttons-wrap">
    <!-- 弹窗 -->
    <el-dialog
      :title="titleMapping[modalName]"
      :visible.sync="modalShow"
      :width="modalWidth"
      @close="modalClose"
    >
      <transition name="el-fade-in-linear">
        <component
          :is="modalName"
          :name="modalName"
          :data="selectItem"
          @close="modalClose"
          @call="confirmCall"
        />
      </transition>
    </el-dialog>
    <!-- 侧拉弹窗 start -->
    <el-drawer
      :title="titleMapping[drawerName]"
      :visible.sync="drawerShow"
      :size="drawerName==='addCase'?'55%':drawerWidth"
      @close="drawerClose"
    >
      <transition name="el-fade-in-linear">
        <component
          :is="drawerName"
          :name="drawerName"
          :data="selectItem"
          @close="drawerClose"
          @call="drawerConfirmCall"
        />
      </transition>
    </el-drawer>
    <!-- 侧拉弹窗 end -->
  </div>
</template>

<script>
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
export default {
  name: 'ActionMenu',
  components: {
  },
  mixins: [mixinsActionMenu],
  inject: ['tableVm'],
  props: {
    moduleName: {
      type: String,
      default: ''
    },
    selectItem: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      drawerAction: [], // 需要侧拉打开的操作
      // 弹窗title映射
      titleMapping: {}
    }
  },
  methods: {
    confirmCall: function(type, data) {
      if (type === 'close') {
        this.modalClose()
      } else if (type === 'refresh') {
        this.$emit('call', type)
      }
    },
    drawerConfirmCall: function(type, data) {
      if (type === 'close') {
        this.drawerClose()
      } else if (type === 'refresh') {
        this.$emit('call', type)
      } else if (type === 'caseSelect') {
        this.drawerName = 'addCase'
        this.caseSelect(data)
      } else if (type === 'kitSelect') {
        this.kitSelect(data)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.action-menu {
  display: inline-block;
}
</style>
