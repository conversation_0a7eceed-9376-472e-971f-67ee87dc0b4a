import request from '@/utils/request'

// 操作日志分页查询
export function querySysLogAPI(data) {
  return request({
    url: 'adminSysLog/sysLog/query',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data,
    baseURL: '/v2.0.0/'
  })
}
// 操作日志搜索查询
export function searchSysLogAPI(data) {
  return request({
    url: 'adminSysLog/sysLog/search',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data,
    baseURL: '/v2.0.0/'
  })
}
// 操作日志导出
export function handleLogExportAPI(data) {
  return request({
    url: 'adminSysLog/sysLog/export',
    method: 'post',
    data: data,
    responseType: 'blob',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    timeout: 60000,
    baseURL: '/v2.0.0/'
  })
}

// 登录日志分页查询
export function queryLoginLogAPI(data) {
  return request({
    url: 'adminSysLog/loginLog/query',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data,
    baseURL: '/v2.0.0/'
  })
}
// 登录日志搜索查询
export function searchLoginLogAPI(data) {
  return request({
    url: 'adminSysLog/loginLog/search',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data,
    baseURL: '/v2.0.0/'
  })
}
// 登录日志导出
export function loginLogExport(data) {
  return request({
    url: 'adminSysLog/loginLog/export',
    method: 'post',
    data: data,
    responseType: 'blob',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    timeout: 60000,
    baseURL: '/v2.0.0/'
  })
}
