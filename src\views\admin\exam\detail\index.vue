<template>
  <detail-view
    :loading="loading"
    :data="data"
    :id="id"
    :view-item="viewItem"
    :show-header="false"
    mode="drawer"
    title-key="detailTitle"
  />
</template>
<script>
import moduleConf from '../config'
import actionMenu from '../action/index'
import detailView from '@/packages/detail-view/index'
import overview from './overview'
import students from './students'
import answerLog from './answerLog'

export default {
  components: {
    actionMenu,
    detailView,
    overview,
    students,
    answerLog
  },
  data() {
    return {
      moduleName: moduleConf.name,
      id: null, // 资源ID
      data: {
        detailTitle: this.$route.params.name,
        examStatus: this.$route.params.examStatus,
        questionType: this.$route.params.questionType
      }, // 资源数据对象
      loading: false,
      viewItem: [
        {
          transName: '概况',
          name: 'overview',
          component: overview
        },
        {
          transName: '考生列表',
          name: 'students',
          component: students
        },
        {
          transName: '答题记录',
          name: 'answerLog',
          component: answerLog
        }
      ]
    }
  },
  watch: {
    '$route': function(to, from) {
      const toId = to.params.hasOwnProperty('id') ? to.params.id : null
      const fromId = from.params.hasOwnProperty('id') ? from.params.id : null
      if (toId !== fromId) {
        this.loadBase()
      }
    }
  },
  created() {
    this.loadBase()
  },
  methods: {
    actionHandler: function(type, data) {
      switch (type) {
        case 'refresh':
          this.loadBase()
      }
    },
    'loadBase': function() {
      this.id = this.$route.params.id
      this.data.detailTitle = this.$route.params.name
    }
  }
}
</script>
