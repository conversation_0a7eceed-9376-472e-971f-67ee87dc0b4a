<template>
  <div class="content-wrap-layout">
    <category :exam-status="examStatus" @category-query="categoryQuery" />
    <!-- 分类区 -->
    <page-table
      ref="table"
      :default-selected-arr="defaultSelectedArr"
      :filter-data="{ 'examStatus': examStatus }"
      default-selected-key="examCode"
      @refresh="refresh"
      @link-event="linkEvent"
      @on-select="tabelSelect"
      @on-current="tabelCurrent"
      @emitSearchName="emitSearchName"
    >
      <action-menu
        slot="action"
        :module-name="moduleName"
        :select-item="selectItem"
        :search-name="searchName"
        @call="actionHandler"
      />
    </page-table>
  </div>
</template>

<script>
import moduleConf from './config'
import pageTable from './table/index.vue'
import actionMenu from './action/index.vue'
import category from './category/index.vue'

export default {
  name: 'Students',
  components: {
    pageTable,
    actionMenu,
    category
  },
  data() {
    return {
      moduleName: moduleConf.name,
      selectItem: [],
      defaultSelectedArr: [],
      searchName: {},
      examStatus: ''
    }
  },
  methods: {
    categoryQuery: function(obj) {
      this.examStatus = obj.examStatus
      this.$nextTick(() => {
        this.$refs['table'].getList(false)
      })
    },
    // 列表点击
    linkEvent: function({ name, row, params }) {
      this.$router.push({ name: name, params: params })
    },
    // 返回已选
    tabelSelect: function(data) {
      this.selectItem = data
    },
    // 返回单选
    tabelCurrent: function(row) {
      this.selectItem = [row]
    },
    emitSearchName(val) {
      this.searchName = val
    },
    // action menu 事件
    actionHandler: function(type, data) {
      switch (type) {
        case 'refresh':
          this.selectItem = []
          this.$refs['table'].getList(false)
      }
    },
    refresh: function() {}
  }
}
</script>

<style scoped lang="scss">
.content-wrap-layout {
  padding: 0 !important;
}
</style>
