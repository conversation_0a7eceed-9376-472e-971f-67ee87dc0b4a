import request from '@/utils/request'
const headers = { 'Content-Type': 'application/json;charset=UTF-8' }

// 获取系统配置
export function queryFirstConfigByName(name, data) {
  return request({
    url: `adminConfig/queryFirstConfigByName?name=${name}`,
    method: 'post',
    data,
    headers
  })
}

// 设置授权到期提醒
export function updateAdminConfigValue(name, data) {
  return request({
    url: `adminConfig/updateAdminConfigValue/${name}`,
    method: 'post',
    data,
    headers
  })
}

// 资源自动管理配置
export function resourceConfigApi(data) {
  return request({
    url: '/scene/scencNodeCleanTask/releaseConfig',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
