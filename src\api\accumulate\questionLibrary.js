import request from '@/utils/request'
const headers = { 'Content-Type': 'application/json;charset=UTF-8' }

// 分页查询题库数据
export function getQuestionDepotList(data) {
  return request({
    url: `admin/sysExamQuestionDepot/queryPage`,
    method: 'post',
    data,
    headers
  })
}

// 查询题库分类
export function getQuestionCategory(data) {
  return request({
    url: `admin/sysExamQuestionDepot/queryList`,
    method: 'post',
    data,
    headers
  })
}

// 获取题库详情
export function getQuestionDepot(params) {
  return request({
    url: `admin/sysExamQuestionDepot/get`,
    method: 'get',
    params,
    headers
  })
}

// 创建题库
export function createQuestionDepot(data) {
  return request({
    url: `admin/sysExamQuestionDepot/create`,
    method: 'post',
    data,
    headers
  })
}

// 修改题库
export function updateQuestionDepot(data) {
  return request({
    url: `admin/sysExamQuestionDepot/update`,
    method: 'post',
    data,
    headers
  })
}

// 删除题库
export function deleteQuestionDepot(data) {
  return request({
    url: `admin/sysExamQuestionDepot/remove`,
    method: 'post',
    data,
    headers
  })
}
