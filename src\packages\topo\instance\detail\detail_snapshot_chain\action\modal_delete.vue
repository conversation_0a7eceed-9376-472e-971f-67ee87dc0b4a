<template>
  <div v-loading="loading" class="dialog-wrap" >
    <!-- 批量组件 开始 -->
    <batch-template :available-data="validArr" :data="data" :show-delete-warning="true"/>
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button :disabled="!validArr.length" type="primary" @click="submit">确定</el-button>
    </div>
  </div>
</template>
<script>
import { deleteSnapshot } from '../../../../api/orchestration'
import batchTemplate from '../../../../../batch-delete/modal-bat-template.vue'
import module from '../config'
import operationValid from './operation_valid.js'
export default {
  components: { batchTemplate },
  mixins: [operationValid],
  props: {
    // 传入数据
    data: {
      type: Array
    },
    resourceId: {
      type: String
    },
    resourceData: {
      type: Object
    }
  },
  data() {
    return {
      moduleName: module.name,
      loading: false
    }
  },
  computed: {
    'validArr': function() {
      const _data = []
      this.data.forEach((item) => {
        item['node_id'] = this.resourceId
        if (this.canDel(item)) {
          _data.push(item)
        }
      })
      return _data
    }
  },
  methods: {
    canDel(item) {
      const _canDelete = this.canDelete && !this.noActive(this.data[0].status)
      return _canDelete
    },
    // 点击取消
    close() {
      this.$emit('close')
    },
    'submit': function(modal) {
      this.loading = true
      this.$bus.$emit(
        'BAT_TASK_API',
        {
          taskName: '删除快照',
          resource: this.validArr,
          apiObj: deleteSnapshot,
          batParam: (item) => {
            return {
              'node_id': item['node_id']
            }
          },
          sucsessCallback: (res) => {
            this.$bus.$emit(this.moduleName + '_module', 'reload')
          },
          errorCallback: () => {
            this.$bus.$emit(this.moduleName + '_module', 'reload')
          }
        }
      )
      this.$emit('call', 'close')
    }
  }
}
</script>
