<template>
  <!-- 上传附件 -->
  <div :class="{ 'height-100': height100 }" class="resource-table">
    <!-- 操作区 -->
    <div class="operation-wrap">
      <div class="operation-left">
        <slot name="action" />
        <el-button
          style="margin-right: 5px;"
          type="primary"
          icon="el-icon-refresh"
          @click="refresh"
        />
      </div>
      <div class="operation-right">
        <el-badge :value="searchBtnShowNum">
          <el-button icon="el-icon-search" @click="openSearch">搜索</el-button>
        </el-badge>
        <!-- 自定义表格列 -->
        <t-table-config
          v-if="!customColData.length"
          :data="columnsObj"
          :active-key-arr="columnsViewArr"
          @on-change-col="onChangeCol"
        />
      </div>
    </div>
    <!-- 搜索区 -->
    <t-search-box
      v-show="searchView"
      :search-key-list="searchKeyListView"
      default-placeholder="默认搜索文件名称"
      @search="searchMultiple"
    />
    <t-table-view
      ref="tableView"
      :height="height"
      :single="single"
      :data="tableData"
      :total="tableTotal"
      :page-size="pageSize"
      :current="pageCurrent"
      :select-item="selectItem"
      current-key="id"
      type="list"
      @on-select="onSelect"
      @on-current="onCurrent"
      @on-change="changePage"
      @on-sort-change="onSortChange"
      @on-page-size-change="onPageSizeChange"
    >
      <el-table-column
        v-for="item in columnsViewArr"
        :key="item"
        :min-width="colMinWidth"
        :width="columnsObj[item].colWidth || colWidth"
        :label="columnsObj[item].title"
        :fixed="columnsObj[item].master ? 'left' : false"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span v-if="item == 'sort'">
            {{ scope.$index + 1 }}
          </span>
          <div v-else-if="item === 'operate'">
            <el-link :disabled="false" :underline="false" type="primary" @click.stop="handlePreview(scope.row)">查看</el-link>
            <el-link :disabled="false" :underline="false" type="primary" @click.stop="handleDown(scope.row)">下载</el-link>
          </div>
          <span v-else>{{ scope.row[item] || "-" }}</span>
        </template>
      </el-table-column>
    </t-table-view>
  </div>
</template>
<script>
import tSearchBox from '@/packages/search-box/index.vue'
import tTableView from '@/packages/table-view/index'
import tTableConfig from '@/packages/table-config/table-col-config.vue'
import mixinsPageTable from '@/packages/mixins/page_table'
// import { testingItemsQueryPageAPI } from '@/api/testing/index'
export default {
  name: 'Appendix',
  components: {
    tTableView,
    tTableConfig,
    tSearchBox
  },
  mixins: [mixinsPageTable],
  props: {
    // 在详情页单独展示的时候传 ture 高度会填充满
    height100: {
      type: Boolean,
      default: false
    },
    // 自定义列宽度
    customizeColumnsObj: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      searchKeyList: [],
      columnsObj: {
        'sort': {
          title: '序号', master: true, colWidth: 50
        },
        name: {
          title: '文件名称',
          master: true
        },
        fileSource: {
          title: '文件大小'
        },
        fileSize: {
          title: '提交人'
        },
        remark: {
          title: '提交时间'
        },
        createByName: {
          title: '最后更新人'
        },
        createTime: {
          title: '最后更新时间'
        },
        operate: {
          title: '操作'
        }
      },
      columnsViewArr: [
        'sort',
        'name',
        'fileSource',
        'fileSize',
        'remark',
        'createByName',
        'createTime',
        'operate'
      ],
      tableData: [],
      tableTotal: 0,
      selectItem: [],
      previewUrl: '',
      loading: false,
      pageNum: 1,
      pageSize: 10
    }
  },
  computed: {},
  watch: {},
  mounted() {
    // 如果有自定义列，则使用自定义列
    if (
      Object.keys(this.customizeColumnsObj ? this.customizeColumnsObj : {})
        .length > 0
    ) {
      this.columnsObj = JSON.parse(JSON.stringify(this.customizeColumnsObj))
    }
  },
  methods: {
    getList(showLoading = true) {
      if (showLoading) {
        this.tableLoading = true
      }
      // const params = this.getPostData('page', 'limit')
      // if (params.time) {
      //   params.createTimeBegin = params.time.split(',')[0]
      //   params.createTimeEnd = params.time.split(',')[1]
      // }
      this.tableData = [
        {
          id: '1000',
          name: '文件1',
          fileSource: '2MB', // 中
          version: 'v1.0',
          status: '1',
          testTimes: 2,
          result: '1', // 通过
          planStartDate: '2023-12-01',
          actualStartTime: '李四',
          actualEndTime: '李四',
          createByName: '张三',
          createTime: '2025-04-09 09:00:00'
        },
        {
          id: '10001',
          name: '文件2',
          fileSource: '3MB', // 高
          version: 'v2.1',
          status: '2',
          testTimes: 1,
          result: '2', // 失败
          planStartDate: '2023-12-15',
          actualStartTime: '李四',
          actualEndTime: '王五',
          createByName: '李四',
          createTime: '2025-04-09 09:00:00'
        }
      ]
      this.tableTotal = 2
      // testingItemsQueryPageAPI(params).then(res => {
      //   this.tableData = res.data.records || []
      //   this.tableTotal = Number(res.data.total) || 0
      //   this.tableLoading = false
      // }).catch(() => {
      //   this.tableLoading = false
      // })
    },
    // 查看附件
    handlePreview(file) {
      this.previewUrl =
        this.viewFileUrl + encodeURIComponent(btoa(file.previewUrl))
      window.open(this.previewUrl)
    },
    // 下载附件
    handleDown(file) {
      const nameArr = file.name.split('.')
      // let fileName = file.name
      if ((nameArr.at(-1) !== file.ext || nameArr.length == 1) && file.ext) {
        // fileName = file.name + '.' + file.ext
      }
    }
  }
}
</script>

<style scoped lang="scss">
.height-100 {
  .layout-table-wrap {
    height: 100%;
  }
}
</style>
