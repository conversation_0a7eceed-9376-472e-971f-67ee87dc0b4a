._question_list {
  // height: calc(100vh - 220px);
  flex: 1;
  overflow-y: auto;
  margin-top: 10px;
  // padding-right: 20px;
  ._question_item {
    padding: 15px 20px 10px 40px;
    min-height: 90px;
    background: #ffffff;
    display: flex;
    flex-direction: column;
    font-size: 14px;
    color: #4e5969;
    position: relative;
    border: 1px solid #e5e6eb;
    border-radius: 4px;
    margin-bottom: 10px;
    ._question_option {
      margin-top: 10px;
      font-size: 14px;
      color: #4e5969;
      display: flex;
      flex-direction: column;
      line-height: 22px;
      word-break: break-all;
      ::v-deep .el-radio {
        margin-bottom: 8px;
        display: flex;
        align-items: flex-start;

        .el-radio__label {
          font-size: 14px;
          color: #4e5969;
          flex: 1;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
      ::v-deep .el-checkbox {
        margin-bottom: 8px;
        display: flex;
        align-items: flex-start;
        .el-checkbox__label {
          font-size: 14px;
          color: #4e5969;
          flex: 1;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
      }
    }
    ._question_item_content {
      display: flex;
      // max-height: 200px;
      // overflow-y: auto;
      overflow-x: auto;
      margin-right: 55px;
    }
    ._question_item_type {
      position: absolute;
      right: 15px;
      top: 15px;
    }
    .combination-question-wrap {
      >div {
        border: 1px solid rgb(229, 230, 235);
        margin: 5px 0px 10px;
        padding: 15px 20px 5px;
        .comp-question {
          display: flex;
          // max-height: 200px;
          // overflow-y: auto;
          overflow-x: auto;
          >span {
            flex: 1;
            word-break: break-all;
          }
        }
        .comp-content-wrap {
          border: 1px solid #e5e6eb;
          margin: 5px 0 10px;
          padding: 15px 20px;
          >div:first-child {
            display: flex;
            // max-height: 200px;
            // overflow-y: auto;
            overflow-x: auto;
            >span {
              flex: 1;
              word-break: break-all;
            }
          }
        }
      }
    }
  }

  img {
    position: absolute;
    left: 12px;
    top: 17px;
    width: 16px;
    height: 16px
  }

  ._question_item_check {
    border: 1px solid var(--color-600);
  }
}

.data-table-footer {
  position: relative;
  margin-top: 10px;
  background-color: #fff;
  height: 32px;
  .page-wrap {
    position: absolute;
    right: 5px;
  }
  .data-table-total {
    position: absolute;
    left: 15px;
    line-height: 32px;
  }
}
