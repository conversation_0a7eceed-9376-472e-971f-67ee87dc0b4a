<template>
  <div class="left-tree">
    <div v-if="iSearch" class="top-handle">
      <el-button
        v-if="isShowExpandTree"
        :class="isExpandTree ? 'el-icon-caret-bottom' : 'el-icon-caret-top'"
        size="mini"
        type="primary"
        class="expand-tree mr-5"
        @click="expandTreeFn(isExpandTree)"
      />
      <el-input
        v-model="dirName"
        size="mini"
        placeholder="请输入"
        class="input-with-select"
        clearable
        @clear="handleSearch()"
        @keyup.enter.native="handleSearch"
      >
        <el-button slot="append" type="primary" size="mini" @click="handleSearch">搜索</el-button>
      </el-input>
      <el-button
        v-if="isShowRefresh"
        class="el-icon-refresh refresh"
        size="mini"
        type="primary"
        @click="refreshTreeList()"
      />
    </div>
    <div v-if="false" class="top-handle">
      <span>当前：</span>
      <el-tag closable @close="handleClose($event)">{{ currentNodeName || '全部' }}</el-tag>
    </div>
    <div class="center-tree">
      <el-tree
        ref="treeRef"
        :data="treeData"
        :default-expand-all="isExpandTree"
        :default-expanded-keys="expandRowKeys"
        :default-checked-keys="defaultCheckedKeys"
        :highlight-current="true"
        :props="defaultProps"
        :expand-on-click-node="false"
        :filter-node-method="filterNode"
        :node-key="nodeKey"
        @node-click="handleNodeClick"
      >
        <div
          slot-scope="{ node, data }"
          :id="data[nodeKey]"
          :node-data="data"
          style="width:100%"
        >
          <div class="directory-item flex-space-between">
            <div class="tree-item flex-left">
              <div v-if="isShowTreeIcon">
                <img v-if="currentNodeId == data[nodeKey]" :src="require(`../assets/${themeKey}/mulu-tree-active.png`)" class="mr-5" alt="">
                <img v-else :src="require(`../assets/${themeKey}/mulu-tree.png`)" class="mr-5" alt="">
              </div>
              <el-tooltip
                v-if="isShowChildrenNum"
                :content="data[defaultProps.label] + (isShowChildrenNum ? getChildrenCountText(node) : '')"
                placement="top"
                width="200"
              >
                <div :style="{ maxWidth: treeInfoWidth + 'px' }" class="tree-info">
                  <span>{{ data[defaultProps.label] }}</span>
                  <span v-if="isShowChildrenNum">{{ getChildrenCountText(node) }}</span>
                </div>
              </el-tooltip>
              <el-tooltip
                v-if="isShowDataCount"
                :content="data[defaultProps.label] + '（'+(data[defaultProps.count]||0) + '）'"
                placement="top"
                width="200"
              >
                <div :style="{ maxWidth: treeInfoWidth + 'px' }" class="tree-info">
                  <span>{{ data[defaultProps.label] }}</span>
                  <span v-if="isShowDataCount">{{ '（' + (data[defaultProps.count] || 0) + '）' }}</span>
                </div>
              </el-tooltip>
              <el-tooltip
                v-else
                :content="data[defaultProps.label]"
                placement="top"
                width="200"
              >
                <div :style="{ maxWidth: treeInfoWidth + 'px' }" class="tree-info">
                  <span>{{ data[defaultProps.label] }}</span>
                </div>
              </el-tooltip>
            </div>
            <el-dropdown
              v-if="isShowDropdown && node.level <= maxLevel && currentNodeId === data[nodeKey]"
              trigger="click"
              @command="handleCommand($event, data)"
            >
              <span class="el-dropdown-link">
                <img :src="require(`../assets/${themeKey}/mulu-active.png`)" style="margin-top: 5px; z-index: 9;" alt="">
              </span>
              <el-dropdown-menu slot="dropdown" class="tree-wrap">
                <el-dropdown-item
                  v-for="item in getDropdownItems(node.level)"
                  :key="item.command"
                  :command="item.command"
                >
                  {{ item.label }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </div>
      </el-tree>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    treeData: {
      type: Array,
      default: () => []
    },
    // 节点标识
    nodeKey: {
      type: String,
      default: 'id'
    },
    // 节点最大层级
    maxLevel: {
      type: Number,
      default: 4
    },
    defaultProps: {
      type: Object,
      default: () => {
        return {
          children: 'children',
          label: 'label'
        }
      }
    },
    // 是否显示搜索，默认搜索
    iSearch: {
      type: Boolean,
      default: true
    },
    // 是否显示展开收起树按钮
    isShowExpandTree: {
      type: Boolean,
      default: true
    },
    // 是否显示刷新按钮
    isShowRefresh: {
      type: Boolean,
      default: true
    },
    // 是否显示节点目录图标
    isShowTreeIcon: {
      type: Boolean,
      default: false
    },
    // 是否显示每个节点子节点数量
    isShowChildrenNum: {
      type: Boolean,
      default: true
    },
    // 是否显示数据数量
    isShowDataCount: {
      type: Boolean,
      default: false
    },
    // 子节点数量显示方式
    childrenNumType: {
      type: String,
      default: 'count', // 'count': 使用getChildrenCount, 'custom': 使用自定义属性
      validator: value => ['count', 'custom'].includes(value)
    },
    // 自定义数量属性名
    childrenNumKey: {
      type: String,
      default: 'num'
    },
    // 自定义数量单位
    childrenNumUnit: {
      type: String,
      default: ''
    },
    // 是否展开树
    isExpandTree: {
      type: Boolean,
      default: true
    },
    // 默认展开的层级，0 表示全部展开，1 表示展开第一级，2 表示展开第二级，以此类推
    defaultExpandLevel: {
      type: Number,
      default: 0
    },
    // 下拉菜单配置项
    dropdownItems: {
      type: Array,
      default: () => [
        { command: 'add', label: '添加', level: [1, 2, 3] },
        { command: 'edit', label: '编辑', level: [2, 3, 4] },
        { command: 'delete', label: '删除', level: [2, 3, 4] },
        { command: 'move', label: '移动至', level: [2, 3, 4] }
      ]
    },
    // 是否显示下拉菜单
    isShowDropdown: {
      type: Boolean,
      default: true
    },
    // 默认选中的节点
    defaultSelectedNode: {
      type: [String, Number],
      default: ''
    },
    treeWidth: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      themeKey: window.ADMIN_CONFIG ? (window.ADMIN_CONFIG.THEME || 'green') : (window.WEB_CONFIG.THEME || 'green'),
      modalName: 'treeModule',
      dirName: '', // 搜索分组名称
      expandRowKeys: [],
      nodePath: [], // 节点路径
      currentNodeId: '',
      currentNodeName: '', // 选择树节点名称
      defaultCheckedKeys: [],
      expandedKeys: [], // 展开的节点key数组
      treeNodeMap: new Map(), // 存储节点层级信息
      isInitialized: false // 添加初始化标记
    }
  },
  computed: {
    treeInfoWidth() {
      return this.treeWidth > 0 ? this.treeWidth - 90 : 240 // 240为默认
    }
  },
  watch: {
    treeData: {
      handler(newVal) {
        if (!newVal || !newVal.length) return
        this.$nextTick(() => {
          if (!this.isInitialized) {
            this.initTreeList()
            this.isInitialized = true
          } else {
            this.handleTreeDataUpdate()
          }
        })
      },
      immediate: true
    }
  },
  mounted() {
    // 如果 treeData 已经有数据，直接初始化
    if (this.treeData && this.treeData.length > 0) {
      this.initTreeList()
    }
  },
  methods: {
    // 搜索
    handleSearch() {
      this.$refs.treeRef.filter(this.dirName)
    },
    handleClose(e) {
      this.currentNodeName = ''
      this.$emit('clearCurrentNode', e)
    },
    // 处理树数据更新
    handleTreeDataUpdate() {
      if (!this.treeData || !this.treeData.length) return
      // 如果当前有选中节点，保持选中状态
      if (this.currentNodeId) {
        this.$nextTick(() => {
          if (this.$refs.treeRef) {
            this.$refs.treeRef.setCurrentKey(this.currentNodeId)
          }
        })
      }
    },
    // 初始化树分组
    initTreeList() {
      if (!this.treeData || !this.treeData.length) return
      // 只在第一次初始化时选中第一个节点
      if (!this.isInitialized) {
        const firstNode = this.treeData[0]
        this.currentNodeId = Number(this.defaultSelectedNode) || firstNode[this.nodeKey]
        this.defaultCheckedKeys = [this.currentNodeId]
        this.$emit('setTreeNode', this.currentNodeId, firstNode.level)
        this.$nextTick(() => {
          if (this.$refs.treeRef) {
            this.$refs.treeRef.setCurrentKey(this.currentNodeId)
          }
        })
        this.isInitialized = true
      }
      // 处理树的展开状态
      if (this.isExpandTree) {
        this.$nextTick(() => {
          this.expandTreeByLevel()
        })
      }
    },
    // 节点过滤
    filterNode(value, data) {
      if (!value) return true
      return data[this.defaultProps.label].indexOf(value) !== -1
    },
    // 节点点击
    handleNodeClick(data, node) {
      this.currentNodeName = data['label']
      this.currentNodeId = data[this.nodeKey]
      this.nodePath = this.$refs.treeRef.getNodePath(node).map(v => v[this.nodeKey])
      this.$emit('setTreeNode', data[this.nodeKey], data.level)
      this.$emit('currentTreeNode', data, node)
    },
    // 节点操作
    handleCommand(event, data) {
      this.$emit('setTreeHandle', event, data)
    },
    // 刷新树列表
    refreshTreeList() {
      this.$emit('refreshTree')
    },
    // 展开/收起树
    expandTreeFn(isExpand) {
      this.isExpandTree = !isExpand
      if (this.isExpandTree) {
        this.expandTreeByLevel()
      } else {
        this.expandedKeys = []
        if (this.$refs.treeRef) {
          // 收起所有节点
          this.$refs.treeRef.store._getAllNodes().forEach(node => {
            node.expanded = false
          })
        }
      }
    },
    // 根据层级展开树
    expandTreeByLevel() {
      if (!this.treeData || !this.treeData.length) return
      this.expandedKeys = []
      this.treeNodeMap.clear()
      // 递归处理树节点
      const handleTreeData = (data, level = 1) => {
        data.forEach(node => {
          // 存储节点层级信息
          this.treeNodeMap.set(node[this.nodeKey], level)
          // 根据层级决定是否展开
          if (this.defaultExpandLevel === 0 || level < this.defaultExpandLevel) {
            this.expandedKeys.push(node[this.nodeKey])
          }
          if (node.children && node.children.length) {
            handleTreeData(node.children, level + 1)
          }
        })
      }
      handleTreeData(this.treeData)
      this.$nextTick(() => {
        if (this.$refs.treeRef) {
          this.$refs.treeRef.store._getAllNodes().forEach(node => {
            const level = this.treeNodeMap.get(node.data[this.nodeKey])
            node.expanded = this.defaultExpandLevel === 0 || level < this.defaultExpandLevel
          })
        }
      })
    },
    // 获取子节点数量
    getChildrenCount(node) {
      if (this.childrenNumType === 'count') {
        if (!node.childNodes || node.childNodes.length === 0) {
          return 0
        }
        return node.childNodes.length
      } else {
        // 使用自定义属性
        const data = node.data
        if (!data[this.childrenNumKey]) {
          return 0
        }
        return data[this.childrenNumKey]
      }
    },
    // 获取子节点数量显示文本
    getChildrenCountText(node) {
      if (!this.isShowChildrenNum) return ''
      const count = this.getChildrenCount(node)
      if (count <= 0) return ''
      let text = count.toString()
      if (this.childrenNumUnit) {
        text += this.childrenNumUnit
      }
      return `(${text})`
    },
    // 根据当前节点层级过滤下拉菜单项
    getDropdownItems(level) {
      return this.dropdownItems.filter(item => {
        return item.level.includes(level)
      })
    }
  }
}
</script>
<style lang='scss' scoped>
.left-tree {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  .top-handle {
    display: flex;
    justify-content: flex-start;
    height: 46px;
    padding: 10px;
    width: 100%;
    align-items: center;
    border-bottom: 1px solid #e5e5e5;
    .expand-tree {
      width: 40px;
      border-radius: 0;
    }
    .refresh {
      margin-left: 5px;
      width: 40px;
      border-radius: 0;
    }
    ::v-deep .input-with-select {
      flex: 1;
      border-radius: 0;
      .el-input__inner {
        border-radius: 0;
        border-right: none;
      }
      .el-input-group__append {
        padding: 0 !important;
        .el-button--primary {
          background-color: var(--color-600);
          border-color: var(--color-600);
          color: #fff;
          margin: 0;
          font-size: 12px;
          border-radius: 0;
          font-weight: normal;
          width: 50px;
          border: 1px solid var(--color-600);
        }
      }
    }
  }
  .center-tree {
    padding: 10px 5px;
    overflow-y: auto;
    overflow-x: hidden;
    // border-top: 1px solid #e5e5e5;
    width: 100%;
    background: #ffffff;
    position: relative;
    display: flex;
    flex-direction: column;
    .active-node {
      color: var(--color-600);
    }
    img {
      width: 16px;
      height: 16px;
    }
    .tree-info {
      max-width: 100%;
      width: 100%;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
    }
    // 树的自定义样式
    ::v-deep {
      .el-tree {
        flex: 1;
        min-height: 0;
        // 如果只有一个节点，删除竖线
        > .el-tree-node:not(:has(+ .el-tree-node)) > .el-tree-node__children:before {
          display: none;
        }
      }
      // 一级节点的样式
      .el-tree-node__content{
        height: 32px;
        margin-bottom: 3px;
        color: var(--neutral-700);
        font-size: 14px;
        font-weight: 500;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
      }
      .el-tree-node__children{
        position: relative;
        overflow: visible !important;
        padding-left: 16px;
        // 竖线
        &:before {
          content: "";
          height: calc(100% + 6px);
          width: 1px;
          position: absolute;
          left: 12px;
          top: -3px;
          border-left: 1px solid #d8d8d8;
        }
        &:empty {
          &:before {
            display: none;
          }
        }
        // 字节点的样式
        .custom-tree-node-label, .el-tree-node__label {
          color: var(--neutral-700);
          font-size: 13px;
          font-weight: 500;
        }
      }
      .el-tree-node__content{
        padding-left: 0 !important;
      }
      // 选中的样式
      .is-current > .el-tree-node__content{
        background: var(--color-50);
        border-radius: 0px 10px 10px 0px;
        color: var(--color-600);
      }
      // hover样式
      .el-tree-node__content:hover {
        background: var(--color-50);
        border-radius: 0px 10px 10px 0px;
        color: var(--color-600);
      }
    }
  }
}
::v-deep .el-button--mini {
  padding: 5px !important;
  height: 26px;
  font-size: 14px;
}
</style>
<style lang='scss'>
.el-dropdown-menu.tree-wrap {
  box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.12);
  border-radius: 2px;
  width: 82px;
  .el-dropdown-menu__item {
    padding: 0 10px;
    font-size: 12px;
    height: 22px;
    line-height: 22px;
    width: 80px !important;
    min-width: 0px !important;
  }
}
</style>
