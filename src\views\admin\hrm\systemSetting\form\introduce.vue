<template>
  <el-form ref="form" :model="formData" :rules="rules" label-position="left" label-width="120px">
    <el-form-item prop="enable">
      <span slot="label">
        <span>开启靶场介绍</span>
        <el-tooltip transfer>
          <i class="el-icon-warning-outline" />
          <div slot="content">此配置项用于设置是否可以查看靶场产品介绍</div>
        </el-tooltip>
      </span>
      <el-switch v-if="editMode" v-model="formData.enable" />
      <span v-else>
        <el-badge :type="formData.enable ? 'success' : 'danger'" is-dot />{{ formData.enable ? '开' : '关' }}
      </span>
    </el-form-item>
    <el-form-item v-if="formData.enable" label="登录后默认页" prop="default">
      <el-switch v-if="editMode" v-model="formData.default" />
      <span v-else>
        <el-badge :type="formData.default ? 'success' : 'danger'" is-dot />{{ formData.default ? '开' : '关' }}
      </span>
    </el-form-item>
  </el-form>
</template>
<script>
import validate from '@/packages/validate'
export default {
  props: {
    // 传入数据
    data: {
      type: Object | Number,
      default: () => null
    },
    editMode: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      loading: false,
      validate: validate,
      formData: {
        enable: false,
        default: false
      },
      rules: {}
    }
  },
  watch: {
    data(val) {
      if (val) {
        this.formData['enable'] = val['enable']
        this.formData['default'] = val['default']
      }
    },
    'formData.enable'(val) {
      if (!val) {
        this.formData['default'] = false
      }
    }
  },
  created() {
    if (this.data) {
      this.formData['enable'] = this.data['enable']
      this.formData['default'] = this.data['default']
    }
  }
}
</script>
