<template>
  <div class="dialog-wrap">
    <div v-if="name == 'publicExam'">请确认是否发布"{{ data[0].name }}"考试?</div>
    <div v-else>请确认是否取消发布"{{ data[0].name }}"考试?</div>
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>

<script>
import modalMixins from '@/packages/mixins/modal_form'
import { published, cancelPublished } from '@/api/exam/index.js'

export default {
  components: {
  },
  mixins: [modalMixins],
  props: {
    name: {
      type: String,
      default: ''
    },
    data: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      loading: false
    }
  },
  computed: {
  },
  created() {
  },
  methods: {
    close() {
      this.$emit('close')
    },
    confirm: function() {
      this.loading = true
      if (this.name == 'publicExam') {
        const params = {
          id: this.data[0].id,
          name: this.data[0].name
        }
        published(params).then((res) => {
          if (res.code === 0) {
            this.$message({
              message: '发布成功',
              type: 'success'
            })
            this.$emit('call', 'refresh')
            this.close()
          }
        }).catch(() => {
          this.loading = false
        })
      } else {
        const params = {
          id: this.data[0].id,
          name: this.data[0].name
        }
        cancelPublished(params).then((res) => {
          if (res.code === 0) {
            this.$message({
              message: '取消发布成功',
              type: 'success'
            })
            this.$emit('call', 'refresh')
            this.close()
          }
        }).catch(() => {
          this.loading = false
        })
      }
    }
  }
}
</script>
