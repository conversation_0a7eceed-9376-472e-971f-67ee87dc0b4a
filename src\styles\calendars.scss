#calendar {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
}

#calendar /deep/ .fc-license-message {
  display: none !important;
}

#calendar /deep/ .fc-toolbar {
  padding-top: 1em;

  .fc-left {
    padding-left: 20px;

    .fc-button-group {
      .fc-button {
        text-shadow: none;
      }

      .fc-state-default {
        background: #f0f0f0;
        padding: 0 1.2em;
        border: 0;
        margin-right: 3px;
      }

      .fc-state-down {
        box-shadow: none;
        text-shadow: none;
      }

      .fc-state-active {
        background: var(--color-600);
        color: #fff;
      }
    }

    .fc-today-button {
      background: #fff;
      margin-right: 50px;
    }
  }

  .fc-center {
    margin-left: -277px;

    h2 {
      font-size: 20px;
      font-weight: normal;
      margin-top: 5px;
    }

    .fc-prevYear-button,
    .fc-prev-button,
    .fc-next-button,
    .fc-nextYear-button {
      background: none;
      border: 0;
      color: #aaa;
      outline: none;
      box-shadow: none;
    }

    .fc-button-group {

      .fc-prev-button .fc-icon-left-single-arrow:after,
      .fc-next-button .fc-icon-right-single-arrow:after {
        font-size: 160%;
        font-weight: none;
      }
    }
  }
}

#calendar /deep/ .fc-view-container {
  .fc-body {
    .fc-row {
      .fc-day {
        border-color: #e9e9e9;
      }

      .fc-bg {

        .fc-sat,
        .fc-sun {
          background: #fbfbfb;
        }

        .fc-today {
          background: none;
        }
      }

      .fc-content-skeleton {
        .fc-today {
          .fc-day-number {
            background: var(--color-600);
            border-radius: 50%;
            padding: 3px;
            color: #fff;
            min-width: 16px;
            text-align: center;
          }
        }

        .fc-day-grid-event {
          border: 0 !important;
          border-radius: 23px;
          margin-left: 5px;
          margin-right: 5px;

          .fc-content {
            color: #fff;

            .fc-time {
              display: none;
            }

            .fc-title {
              float: left;
              width: 100%;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
            }
          }
        }
      }

      .fc-day-number {
        margin: 5px;
      }

      .fc-day-grid-event {
        border-left: 2px solid #ff9668 !important;
        border-radius: 0;
        margin-left: 0;
        margin-right: 0;
        padding: 2px 15px;
      }
    }
  }

  .fc-body>tr>.fc-widget-content {
    border: 0;
  }

  .fc-head {
    .fc-head-container {
      border: 0;
      border-bottom: 1px solid #ddd;
    }
  }
}

#calendar /deep/ .fc-day-header {
  background: #f5f5f5;
  border-width: 0;
  font-weight: normal;

  span {
    height: 50px;
    line-height: 50px;
  }
}
