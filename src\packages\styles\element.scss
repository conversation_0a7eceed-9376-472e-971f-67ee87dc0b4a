// 覆盖element-ui的样式
// 表单样式
body .el-form {
  // 表单之间间距调整
  .el-form-item {
    margin-bottom: 15px;
  }
  .el-form-item__label {
    color: var(--neutral-700);
    font-weight: 500;
  }
  // 报错信息能换行展示
  .el-form-item__error {
    position: relative;
  }
  // 将必填星号放到右边
  .el-form-item__label::before {
    display: none;
  }
  .el-form-item.is-required:not(.is-no-asterisk) .el-form-item__label-wrap > .el-form-item__label:after, .el-form-item.is-required:not(.is-no-asterisk) > .el-form-item__label:after {
    content: '*';
    color: #F56C6C;
    margin-left: 4px;
  }
}

// 输入框内边距调整
body .el-input__inner {
  padding: 0 10px;
}

// 多行输入框框内边距调整
body .el-textarea .el-textarea__inner {
  padding: 5px 10px;
}

// tabs type=card 样式修改
body .el-tabs--card {
  > .el-tabs__header {
    padding: 0 24px;
    margin-bottom: 0px;
    .el-tabs__nav-scroll {
      height: 34px;
    }
    .el-tabs__nav {
      border: none;
      border-radius: 0;
      margin-bottom: -1px;
    }
    .el-tabs__item.is-active {
      border-top: 2px solid var(--color-600);
      color: var(--neutral-800);
      font-weight: bold;
    }
    .el-tabs__item {
      height: 34px;
      line-height: 32px;
      margin-right: 10px;
      padding: 0 15px !important;
      border-left: 1px solid #E4E7ED !important;
      border-right: 1px solid #E4E7ED;
      border-top: 1px solid #E4E7ED;
      border-radius: 2px 2px 0 0;
    }
  }
}

// 按钮样式覆盖
body .el-button.is-disabled, .el-button.is-disabled:hover, .el-button.is-disabled:focus, .el-button.is-disabled:active {
  color: #999;
  border-color: #ececec;
  background-color: #f7f7f7;
}
body .el-button--primary:active {
  background: var(--color-600);
  border-color: var(--color-600);
}
body .el-button:active {
  color: var(--color-600);
  border-color: var(--color-600);
}
body .el-button {
  height: 32px;
  &.el-button--mini {
    padding: 7px 15px;
  }
}
body .el-button--text {
  color: var(--neutral-700);
}
body .el-button--ghost {
  border-style: dashed;
  border-color: #c8cacd;
}
body .el-button + .el-button {
  margin-left: 0;
}
body .el-form-item.is-error .el-button--ghost {
  border-color: #ed4014;
  color: #ed4014;
}

// 徽标样式（供状态使用）
body .el-badge__content.is-dot:not(.is-fixed) {
  width: 9px;
  height: 9px;
  border: 2px solid hsla(0,0%,100%,.5);
  display: inline-block;
  margin-right: 5px;
  position: relative;
  top: -1px
}

// 下拉菜单样式
body .el-dropdown-menu {
  padding: 5px 0;
  &.el-popper[x-placement^=bottom] {
    margin-top: 2px;
  }
  &.el-popper[x-placement^=right] {
    margin-left: 0px;
  }
  .popper__arrow {
    display: none;
  }
  .el-dropdown-menu__item {
    line-height: 31px;
    font-size: 12px;
    min-width: 120px;
    color: #252525;
    &.is-disabled {
      color: #bbb;
    }
    &:focus, &:not(.is-disabled):hover {
      background-color: #F5F7FA;
      color: #252525;
    }
  }
}

// tag样式覆盖
body .el-form .el-tag {
  display: inline-block;
  vertical-align: top;
  max-width: 200px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  position: relative;
  padding-right: 24px;
  .el-icon-close {
    position: absolute;
    top: 8px;
    right: 5px;
  }
}
body .el-form .el-select .el-tag {
  display: flex;
  padding-right: 8px;
  .el-icon-close {
    position: relative;
    top: 0px;
    right: -5px;
  }
}

// tooltip的最大宽度
body .el-tooltip__popper {
  max-width: 340px;
}

// el-alert warning 背景色及边框颜色调整
body .el-alert--warning.is-light {
  border: 1px solid #ffd77a;
  background-color: #fff9e6;
  color: #252525;
}
// el-alert info 背景色及边框颜色调整
body .el-alert--info.is-light {
  border: 1px solid #c0c4cc;
  background-color: #f2f2f2;
  color: #252525;
}

// 表格样式调整
body .el-table {
  &.el-table--border .el-table__cell.radio .cell {
    padding-left: 13px;
    overflow: hidden;
    text-overflow: unset;
  }
}

// 表单下方的说明信息
body .el-form-item__tips {
  line-height: normal;
  position: relative;
  color: #707275;
  font-size: 12px;
}

// loading 样式
body .el-notification .el-icon-loading {
  color: var(--color-600);
}

// confirm 样式
body .el-message-box__wrapper {
  .el-message-box__btns {
    .el-button + .el-button {
      margin-left: 5px;
    }
  }
}

// el-timeline 样式
body .el-timeline {
  .el-timeline-item {
    .el-timeline-item__tail, .el-timeline-item__node--normal {
      top: 2px;
    }
  }
}

// 分组下拉框 样式
body .el-select-group .el-select-dropdown__item {
  padding-left: 35px;
}
