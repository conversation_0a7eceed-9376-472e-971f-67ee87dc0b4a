export default {
  data() {
    return {
      detailId: null,
      detailShow: false,
      defaultSelectedArr: []
    }
  },
  created() {
    this.routeInit('created')
  },
  watch: {
    '$route': function(to, from) {
      this.routeInit()
    }
  },
  methods: {
    // 列表刷新动作
    'refresh': function() {
      this.closeDetail()
    },
    // 详情请求错误动作
    'detailError': function(error) {
      if (error.response.status === 404) {
        const id = this.$route.params.id
        this.$bus.$emit('SHOW_NOTICE', {
          type: 'error',
          message: '资源' + id + '不存在'
        })
      }
      this.closeDetail()
    },
    // 路由初始化
    'routeInit': function(type) {
      const routeName = this.$route.name
      // 如果存在detailShowOfName，则只显示detailShowOfName对应路由的详情
      if (this.detailShowOfName) {
        this.detailShow = this.detailShowOfName.includes(routeName)
      } else {
        // 否则将通过Detail关键字判断
        this.detailShow = routeName.indexOf('Detail') > -1 || routeName.indexOf('_detail') > -1 || routeName.indexOf('detail') > -1
      }
      if (this.$route.params.hasOwnProperty('id')) {
        this.defaultSelectedArr = []
        this.defaultSelectedArr.push(this.$route.params.id)
      }
      // 其他资源跳转而来，而不是点击列表名称触发
      if (type === 'created') {
        this.detailId = this.$route.params.hasOwnProperty('id') ? this.$route.params['id'] : null
      }
    },
    // 关闭侧拉详情
    'closeDetail': function(type) {
      this.detailShow = false
      this.$router.replace({ name: this.listRouterName, params: this.listRouterParams || {}, query: this.listRouterQuery || {}})
      if (this.$refs.hasOwnProperty('table')) this.$refs['table'].setHighlightRow(null)
      this.defaultSelectedArr = []
    }
  }
}
