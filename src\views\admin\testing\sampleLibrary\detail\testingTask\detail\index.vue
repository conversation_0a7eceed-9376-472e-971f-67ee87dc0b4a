<template>
  <detail-view
    :loading="loading"
    :data="data"
    :id="id"
    :view-item="viewItem"
    mode="drawer"
    title-key="projectName"
  />
</template>

<script>
import moduleConf from '../config'
import detailView from '@/packages/detail-view/index'
import detailOverview from './detail_overview'
import detailTestingCase from './testingCase/index'
import detailIssuesList from '@/views/admin/testing/sampleLibrary/detail/issuesList/index.vue'
import detailTestingReport from './testingReport/index'
import detailEnvironment from '@/views/admin/testing/sampleLibrary/detail/environment/index.vue'
import Appendix from './Appendix/index.vue'
import { getSampleTaskDetail } from '@/api/testing/index'

export default {
  name: 'TestingTaskDetail',
  components: {
    detailView,
    detailOverview,
    detailTestingCase,
    detailEnvironment,
    detailTestingReport,
    detailIssuesList,
    Appendix
  },
  data() {
    return {
      moduleName: moduleConf.name,
      id: null, // 资源ID
      data: null, // 资源数据对象
      loading: true,
      viewItem: [
        {
          transName: '概况',
          name: 'overview',
          component: detailOverview
        },
        {
          transName: '测试用例',
          name: 'testingCase',
          component: detailTestingCase
        },
        {
          transName: '测试环境',
          name: 'environment',
          component: detailEnvironment
        },
        {
          transName: '测试报告',
          name: 'testingReport',
          component: detailTestingReport
        },
        {
          transName: '问题清单',
          name: 'issuesList',
          component: detailIssuesList
        },
        {
          transName: '附件',
          name: 'appendix',
          component: Appendix
        }
      ]
    }
  },
  watch: {
    '$route': function(to, from) {
      const toId = to.params.hasOwnProperty('id') ? to.params.id : null
      const fromId = from.params.hasOwnProperty('id') ? from.params.id : null
      if (toId !== fromId) {
        this.loadBase()
      }
    }
  },
  created() {
    this.loadBase()
  },
  methods: {
    actionHandler: function(type, data) {
      switch (type) {
        case 'refresh':
          this.loadBase()
      }
    },
    'loadBase': function() {
      this.id = this.$route.params.id
      this.getData(this.id)
    },
    // 根据id获取详情数据
    'getData': function(id) {
      if (id) {
        this.loading = true
        getSampleTaskDetail({ id: id }).then(res => {
          if (res.data.code == 0) {
            this.data = res.data.data
            this.loading = false
          }
        }).catch(() => {
          this.loading = false
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
