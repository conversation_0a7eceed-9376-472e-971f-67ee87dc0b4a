export default {
  props: {
    queryId: [String, Number] // 兼容个别页面路由传参不一致
  },
  data() {
    return {
      loading: false,
      dataId: '', // 保存内容基本信息创建成功返回数据id
      id: this.queryId || this.$route.query.id || this.$route.query.examCode, // 共享数据 ID
      type: this.$route.query.type || '查看',
      img: require('@/packages/table-view/nodata.png')
    }
  },
  computed: {
    // 内容id
    contentId() {
      const dataId = JSON.parse(localStorage.getItem('dataId'))
      return dataId || this.id
    },
    // 是否编辑模式
    editMode() {
      return this.type == '编辑' || this.type == '创建'
    }
  }
}
