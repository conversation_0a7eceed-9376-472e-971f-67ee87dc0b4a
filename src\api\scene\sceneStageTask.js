import request from '@/utils/request'
// 查询场景任务初始化数据
export function getSceneAllDataAPI(params) {
  return request({
    url: 'scene/sceneStageTask/getSceneAllData',
    method: 'get',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    params
  })
}

// 根据场景角色id查询其下所有阶段即任务
export function queryBySceneRoleIdAPI(params) {
  return request({
    url: 'scene/sceneStage/queryStageVOBySceneRoleId',
    method: 'get',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    params
  })
}

// 通过场景角色id查询相关资产列表
export function sceneRoleResourceRelAPI(params) {
  return request({
    url: 'scene/sceneRoleResourceRel/queryBySceneRoleId',
    method: 'get',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    params
  })
}

// 角色下新增阶段
export function createSceneStageAPI(data) {
  return request({
    url: 'scene/sceneStage/create',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

// 角色下修改阶段
export function updateSceneStageAPI(data) {
  return request({
    url: 'scene/sceneStage/update',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

// 角色下删除阶段
export function removeSceneStageAPI(data) {
  return request({
    url: 'scene/sceneStage/remove',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

// 创建阶段下的任务
export function createSceneStageTaskAPI(data) {
  return request({
    url: 'scene/sceneStageTask/create',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

// 修改阶段下的任务
export function updateSceneStageTaskAPI(data) {
  return request({
    url: 'scene/sceneStageTask/update',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

// 通过场景ID查询任务列表
export function queryByStageIdAPI(params) {
  return request({
    url: 'scene/sceneStageTask/queryByStageId',
    method: 'get',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    params
  })
}

// 根据任务id删除任务
export function removeSceneStageTaskAPI(data) {
  return request({
    url: 'scene/sceneStageTask/remove',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

// 根据任务模板覆盖角色任务
export function taskTemplateCopy(data) {
  return request({
    url: 'scene/taskTemplate/copy',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

/**
 * 场景任务提交
 *
 */
export function createAllSceneStage(data) {
  return request({
    url: '/scene/sceneStage/createAll',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}
