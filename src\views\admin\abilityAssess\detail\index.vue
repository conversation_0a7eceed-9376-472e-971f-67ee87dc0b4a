<template>
  <detail-view
    :loading="loading"
    :data="data"
    :id="id"
    :view-item="viewItem"
    mode="drawer"
    title-key="realname"
  />
</template>
<script>
import moduleConf from '../config'
import detailView from '@/packages/detail-view/index'
import abilityDetail from './abilityDetail/index'
import { getUserDetailAPI } from '@/api/usercenter/user'


export default {
  components: {
    detailView,
    abilityDetail
  },
  data() {
    return {
      moduleName: moduleConf.name,
      id: null, // 资源ID
      data: null, // 资源数据对象
      loading: true,
      viewItem: [
        {
          transName: '详情',
          name: 'overview',
          component: abilityDetail
        }
      ]
    }
  },
  watch: {
    '$route': function(to, from) {
      const toId = to.params.hasOwnProperty('id') ? to.params.id : null
      const fromId = from.params.hasOwnProperty('id') ? from.params.id : null
      if (toId !== fromId) {
        this.loadBase()
      }
    }
  },
  created() {
    this.loadBase()
  },
  methods: {
    'loadBase': function() {
      this.id = this.$route.params.id
      this.getData(this.id)
    },
    // 根据id获取详情数据
    'getData': function(id) {
      getUserDetailAPI({ userId: id })
        .then(res => {
          this.loading = false
          this.data = res.data
        })
        .catch(() => {})
    }
  }
}
</script>
