<template>
  <div v-loading="loading" class="dialog-wrap">
    <el-form ref="form" :model="formData" :rules="rules" label-width="90px">
      <el-form-item label="名称" prop="name">
        <el-input v-model.trim="formData.name" />
      </el-form-item>
    </el-form>
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>

<script>
import moduleConf from '../config.js'
import validate from '@/packages/validate'
import { addSkillPoint, editSkillPoint } from '@/api/accumulate/skillPoint.js'

export default {
  components: {
  },
  props: {
    data: {
      type: Array,
      default: () => {
        return []
      }
    },
    name: {
      type: String
    }
  },
  data() {
    return {
      moduleName: moduleConf.name,
      loading: false,
      validate: validate,
      formData: {
        name: '' // 技能名称
      },
      rules: {
        name: [validate.required(), {
          min: 1,
          max: 32,
          message: '1-32个字符',
          trigger: 'blur'
        }]
      },
      apiType: addSkillPoint
    }
  },
  mounted() {
    if (this.name === 'modalEdit') {
      this.formData.id = this.data[0].id
      this.formData.name = this.data[0].name
    }
  },
  methods: {
    close() {
      this.$emit('close')
    },
    confirm: function() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.loading = true
          const postData = JSON.parse(JSON.stringify(this.formData))
          if (this.name === 'modalEdit') {
            this.apiType = editSkillPoint
          }
          this.apiType(postData).then(res => {
            this.$message.success(`技能点${this.name === 'modalEdit' ? '编辑' : '新增'}成功`)
            this.$bus.$emit(this.moduleName + '_module', 'reload')
            this.close()
          }).catch(() => {
            this.loading = false
          })
        }
      })
    }
  }
}
</script>
