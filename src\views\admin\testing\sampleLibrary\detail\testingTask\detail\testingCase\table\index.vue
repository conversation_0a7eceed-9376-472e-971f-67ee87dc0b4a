<template>
  <div class="resource-table">
    <split-pane ref="split-pane" :min-percent="minPercent" :default-percent="percent" split="vertical" @resize="resize">
      <div
        slot="paneL"
        class="collapse-transition"
        style="padding-right: 0;height: 100%;"
      >
        <div class="tree-container">
          <tree
            ref="treeRef"
            :tree-width="treeWidth"
            :i-search="false"
            :tree-data="treeData"
            :default-props="defaultProps"
            :default-expanded-keys="treeData.length > 0 ? [treeData[0].id] : []"
            :default-checked-keys="treeData.length > 0 ? [treeData[0].id] : []"
            :is-show-dropdown="false"
            :is-show-data-count="false"
            :is-show-children-num="false"
            @currentTreeNode="currentTreeNode"
            @clearCurrentNode="clearCurrentNode"
          />
        </div>
      </div>
      <div
        slot="paneR"
        ref="tableColRef"
        class="resource-table collapse-transition"
      >
        <!-- 操作区 -->
        <div class="operation-wrap">
          <div class="operation-left">
            <slot name="action" />
            <el-button type="primary" icon="el-icon-refresh" @click="refresh" />
          </div>
          <div class="operation-right">
            <el-button v-permission="'manage.testing.project.projectDetail.projectTask.taskDetailView.taskCase.taskCaseExecuteStatistics'" class="exeBtn" style="margin-right: 5px;" @click="handleExecuteStatistics"><svg t="1750324323977" class="icon mr-5" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="6119" xmlns:xlink="http://www.w3.org/1999/xlink" width="14" height="14"><path d="M168 640a40 40 0 0 0-40 40v240a40 40 0 0 0 80 0V680a40 40 0 0 0-40-40z m229.33-256a40 40 0 0 0-40 40v496a40 40 0 0 0 80 0V424a40 40 0 0 0-40-40zM856 384a40 40 0 0 0-40 40v496a40 40 0 0 0 80 0V424a40 40 0 0 0-40-40zM626.67 576a40 40 0 0 0-40 40v304a40 40 0 0 0 40 40 40 40 0 0 0 40-40V616a40 40 0 0 0-40-40zM920.33 64H776a40 40 0 0 0 0 80h47.5L631.14 334.11l-194.95-195a39.49 39.49 0 0 0-54.51-1.27q-0.82 0.74-1.62 1.53L75.72 443.76a39.85 39.85 0 0 0 56.36 56.36l276.17-276.18L600.34 416c0.69 0.82 1.4 1.61 2.16 2.38a39.59 39.59 0 0 0 20.82 11.12l0.54 0.09 1.16 0.2a39.42 39.42 0 0 0 33.18-10.5l0.61-0.58L880.33 199.9V248a40 40 0 0 0 80 0V104a40 40 0 0 0-40-40z" p-id="6120"/></svg>执行统计</el-button>
            <el-badge :value="searchBtnShowNum" style="margin-right: 5px;">
              <el-button icon="el-icon-search" @click="openSearch">搜索</el-button>
            </el-badge>
            <t-table-config
              v-if="!customColData.length"
              :data="columnsObj"
              :active-key-arr="columnsViewArr"
              @on-change-col="onChangeCol"
            />
          </div>
        </div>
        <div
          class="collapse-btn"
          @click="toggleCollapse"
        >
          <i
            :class="fold ? 'el-icon-caret-right' : 'el-icon-caret-left'"
          />
        </div>
        <!-- 搜索区 -->
        <t-search-box
          v-show="searchView"
          ref="searchBoxRef"
          :search-key-list="searchKeyListView"
          default-placeholder="默认搜索测试用例标题"
          @search="searchMultiple"
        />
        <!-- 列表 -->
        <t-table-view
          ref="tableView"
          :height="height"
          :single="single"
          :loading="tableLoading"
          :data="tableData"
          :total="tableTotal"
          :page-size="pageSize"
          :current="pageCurrent"
          :select-item="selectItem"
          :current-row="currentRow"
          :default-sort="{ prop: 'sortNumber', order: 'descending' }"
          current-key="id"
          @selection-change="handleSelection"
          @row-click="handleRowClick"
          @on-select="onSelect"
          @on-current="onCurrent"
          @on-change="changePage"
          @on-sort-change="onSortChange"
          @on-page-size-change="onPageSizeChange"
        >
          <el-table-column
            v-for="item in columnsViewArr" :key="item" :min-width="colMinWidth" :label="columnsObj[item].title" :fixed="columnsObj[item].master ? 'left' : false"
            :width="columnsObj[item].colWidth" :show-overflow-tooltip="true" :sortable="columnsObj[item].sortable" :prop="item">
            <template slot-scope="scope">
              <span v-if="item === 'sortNumber'">
                <a :href="`/testing/testingCaseDetail/${scope.row.caseId}/overview?routeSearch=true&searchVal=${scope.row.sortNumber}&searchKey=sortNumber&moduleName=testCases`" target="_blank">
                  {{ scope.row[item] || '-' }}
                </a>
              </span>
              <span v-else-if="item === 'title'">
                <a :href="`/testing/testingCaseDetail/${scope.row.caseId}/overview?routeSearch=true&searchVal=${scope.row.title}&searchKey=title&moduleName=testCases`" target="_blank">
                  {{ scope.row[item] || '-' }}
                </a>
              </span>
              <span v-else-if="item === 'priority'">
                <el-badge
                  :type="
                    (module.priorityObj[scope.row.priority] &&
                      module.priorityObj[scope.row.priority].type) ||
                      'info'
                  "
                  is-dot
                />
                {{
                  (module.priorityObj[scope.row.priority] &&
                    module.priorityObj[scope.row.priority].label) ||
                    "-"
                }}
              </span>
              <span v-else-if="item === 'type'">
                {{
                  (module.typeObj[String(scope.row.type)] &&
                    module.typeObj[String(scope.row.type)].label) ||
                    "-"
                }}
              </span>
              <span v-else-if="item === 'projectName'">
                <span v-if="scope.row.projectId">
                  <a
                    :href="scope.row.archiveStatus ? `/testing/testing/detail/${scope.row.projectId}/overview` : `/testing/sampleLibrary/detail/${scope.row.projectId}/overview`"
                    @click.prevent="toProjectDetail(scope.row)"
                  >
                    {{ scope.row.projectName }}
                  </a>
                </span>
                <span v-else>-</span>
              </span>
              <span v-else-if="item === 'result'">
                <span :class="module.resultObj[scope.row[item]].type">{{ (module.resultObj[scope.row[item]] && module.resultObj[scope.row[item]].label) || '-' }}</span>
              </span>
              <span v-else>
                {{ scope.row[item] || '-' }}
              </span>
            </template>
          </el-table-column>
        </t-table-view>
      </div>
    </split-pane>
    <el-dialog
      :visible.sync="executeStatisticsVisible"
      title="执行统计"
      width="820px"
      append-to-body
    >
      <div class="execute-statistics-content">
        <el-empty v-if="!functionData.length && !performanceData.length && !securityData.length" :image="noDataImg" style="padding: 0;" description="暂无数据"/>
        <div v-else class="chart-section">
          <div v-if="functionData.length > 0" class="chart-item">
            <div class="chart-layout">
              <div class="chart-left">
                <div ref="functionChart" class="chart-container" />
              </div>
              <div class="chart-right">
                <el-table :data="functionData" height="100%" border size="small">
                  <el-table-column prop="name" label="条目" align="center" />
                  <el-table-column prop="value" label="值" align="center" />
                  <el-table-column prop="percentage" label="百分比" align="center" />
                </el-table>
              </div>
            </div>
          </div>
          <div v-if="performanceData.length > 0" class="chart-item">
            <div class="chart-layout">
              <div class="chart-left">
                <div ref="performanceChart" class="chart-container" />
              </div>
              <div class="chart-right">
                <el-table :data="performanceData" height="100%" border size="small">
                  <el-table-column prop="name" label="条目" align="center" />
                  <el-table-column prop="value" label="值" align="center" />
                  <el-table-column prop="percentage" label="百分比" align="center" />
                </el-table>
              </div>
            </div>
          </div>
          <div v-if="securityData.length > 0" class="chart-item">
            <div class="chart-layout">
              <div class="chart-left">
                <div ref="securityChart" class="chart-container" />
              </div>
              <div class="chart-right">
                <el-table :data="securityData" height="100%" border size="small">
                  <el-table-column prop="name" label="条目" align="center" />
                  <el-table-column prop="value" label="值" align="center" />
                  <el-table-column prop="percentage" label="百分比" align="center" />
                </el-table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { caseCategoryTree, caseProjectPage, taskCaseGetExecuteStatistics, taskCasePage } from '@/api/testing/testCase'
import mixinsPageTable from '@/packages/mixins/page_table'
import splitPane from '@/packages/mixins/split-pane'
import tSearchBox from '@/packages/search-box/index.vue'
import tTableConfig from '@/packages/table-config/table-col-config.vue'
import tTableView from '@/packages/table-view/index.vue'
import tree from '@/packages/tree/index.vue'
import * as echarts from 'echarts'
import module from '../config.js'

export default {
  components: {
    tree,
    tSearchBox,
    tTableView,
    tTableConfig
  },
  mixins: [mixinsPageTable, splitPane],
  data() {
    return {
      noDataImg: require('@/packages/table-view/nodata.png'),
      defaultProps: {
        children: 'children',
        label: 'label',
        count: 'count'
      },
      module,
      moduleName: module.name,
      // 结果统计
      passCount: 0,
      failCount: 0,
      blockCount: 0,
      notRunCount: 0,
      // 表格相关
      tableLoading: false,
      tableData: [],
      tableTotal: 0,
      currentRow: {},
      // 搜索配置项
      searchKeyList: [
        { key: 'sortNumber', label: '用例编号' },
        { key: 'title', label: '用例标题', master: true },
        { key: 'type', label: '类型', type: 'select', valueList: module.typeArr },
        { key: 'priority', label: '优先级', type: 'select', valueList: module.priorityArr },
        { key: 'projectName', label: '关联项目', type: 'select', valueList: [] },
        { key: 'categoryName', label: '分类' },
        { key: 'assignUserName', label: '指派给' },
        { key: 'executeUserName', label: '执行人' },
        { key: 'executeResult', label: '执行结果' },
        { key: 'executeTime', label: '执行时间', type: 'time_range' }
      ],
      // 所有可配置显示列
      columnsObj: {
        'sortNumber': {
          title: '用例编号',
          master: true,
          sortable: true,
          colWidth: 100
        },
        'title': {
          title: '用例标题',
          colMinWidth: 150,
          sortable: true
        },
        'categoryName': {
          title: '分类',
          sortable: true
        },
        'priority': {
          title: '优先级',
          sortable: true,
          colWidth: 90
        },
        'type': {
          title: '类型',
          sortable: true
        },
        'projectName': {
          title: '关联项目',
          sortable: true
        },
        'assignUserName': {
          title: '指派给',
          sortable: true
        },
        'executeUserName': {
          title: '执行人',
          sortable: true
        },
        'executeResult': {
          title: '执行结果',
          sortable: true
        },
        'executeTime': {
          title: '执行时间',
          sortable: true
        }
      },
      // 当前显示列
      columnsViewArr: [
        'sortNumber',
        'title',
        'type',
        'priority',
        'projectName',
        'categoryName',
        'assignUserName',
        'executeUserName',
        'executeResult',
        'executeTime'
      ],
      currentNodeId: '0',
      treeWidth: 280,
      treeData: [],
      // 记录搜索条件
      searchParams: {},
      requestParams: {}, // 处理后的搜索参数
      // 排序相关
      sortField: '',
      sortOrder: '',
      executeStatisticsVisible: false,
      // 图表数据
      functionData: [
        { name: '未执行', value: 70, percentage: '35%' },
        { name: '通过', value: 30, percentage: '15%' },
        { name: '阻塞', value: 30, percentage: '15%' },
        { name: '失败', value: 70, percentage: '35%' }
      ],
      performanceData: [
        { name: '未执行', value: 70, percentage: '35%' },
        { name: '合规', value: 30, percentage: '15%' },
        { name: '待确认', value: 30, percentage: '15%' },
        { name: '不合规', value: 70, percentage: '35%' }
      ],
      securityData: [
        { name: '未执行', value: 70, percentage: '35%' },
        { name: '合格', value: 30, percentage: '15%' },
        { name: '待确认', value: 30, percentage: '15%' },
        { name: '不合格', value: 70, percentage: '35%' }
      ],
      chartColors: ['#3E71F6', '#67C23A', '#E6A23C', '#F56C6C']
    }
  },
  computed: {
    ...mapGetters(['manage']),
    'searchBtnShowNum': function() { // 搜索项的数量
      if (this.searchView) return null
      return Object.keys(this.searchParams).length || null
    }
  },
  async created() {
    await this.getTreeData()
    await this.getProjectList()
    // 初始加载数据
    this.initDefaultTree()
  },
  methods: {
    toProjectDetail(row) {
      if (row.taskUserCount === null) {
        this.$message.warning('无权限访问该项目')
        return
      }
      if (row.archiveStatus == 0 && this.manage.testing.project) {
        window.open(`/testing/testing/detail/${row.projectId}/overview`, '_blank')
      } else if (row.archiveStatus == 1 && this.manage.testing.sample) {
        window.open(`/testing/sampleLibrary/detail/${row.projectId}/overview`, '_blank')
      } else {
        this.$message.warning('暂无权限')
      }
    },
    async getProjectList() {
      const res = await caseProjectPage({ pageType: 0 })
      if (res && res.code === 0) {
        const projectItem = this.searchKeyList.find(item => item.key === 'projectName')
        if (projectItem) {
          projectItem.valueList = res.data.records.map(item => ({
            label: item.projectName,
            value: String(item.id)
          }))
        }
      }
    },
    toggleCollapse() {
      this.fold = !this.fold
      if (this.fold) {
        this.percent = this.minPercent
      } else {
        this.percent = 20
      }
    },
    openNewPageInTab(name, row, params) {
      this.$router.push({
        name,
        params
      })
    },
    // 计算统计数据
    calculateStatistics(data) {
      this.passCount = data.filter(item => item.result === '1').length
      this.failCount = data.filter(item => item.result === '2').length
      this.blockCount = data.filter(item => item.result === '3').length
      this.notRunCount = data.filter(item => item.result === '4').length
    },
    // 初始化默认树选择
    initDefaultTree() {
      // 使用树的根分类ID
      if (this.treeData && this.treeData.length > 0) {
        this.currentNodeId = this.treeData[0].id
      } else {
        this.currentNodeId = '0' // 如果没有树数据，使用默认值
      }
      // 加载数据
      this.getList()
      // 确保根节点被选中
      this.$nextTick(() => {
        if (this.$refs.treeRef) {
          this.$refs.treeRef.setCurrentKey(this.currentNodeId)
        }
      })
    },
    clearCurrentNode() {
      // 重置为根分类
      if (this.treeData && this.treeData.length > 0) {
        this.currentNodeId = this.treeData[0].id
      } else {
        this.currentNodeId = '0'
      }
      this.getList()
    },
    currentTreeNode(data, node) {
      if (data && data.id !== undefined) {
        this.currentNodeId = data.id
        // 保留当前搜索条件，结合树节点进行搜索
        this.getList(this.searchParams)
      }
    },
    // 转换树结构数据，将id作为nodeKey，name作为label
    transformTreeData(data) {
      if (!data || !Array.isArray(data)) return []

      return data.map(item => {
        const node = {
          nodeKey: item.id,
          label: item.name || '未命名分类',
          id: item.id,
          parentId: item.parentId,
          type: item.type,
          count: item.count
        }

        if (
          item.children &&
          Array.isArray(item.children) &&
          item.children.length > 0
        ) {
          node.children = this.transformTreeData(item.children)
        }

        return node
      })
    },
    // 获取分类树结构
    async getTreeData() {
      try {
        this.tableLoading = true
        const params = {
          type: 1 // 测试项目的测试用例分类
        }
        const res = await caseCategoryTree(params)

        if (res && res.data) {
          // 转换树结构数据
          this.treeData = this.transformTreeData(res.data)
        } else {
          this.treeData = []
        }
      } catch (error) {
        console.error('获取分类树结构失败:', error)
        this.treeData = []
      } finally {
        this.tableLoading = false
      }
    },
    // 处理表格选择
    handleSelection(selection) {
      this.selectionArr = selection || []
      this.$emit('on-select', this.selectionArr)
    },
    // 处理行点击
    handleRowClick(row) {
      this.currentRow = row
      this.$emit('on-current', row)
    },
    // 处理搜索
    searchMultiple(params) {
      this.pageSize = 10
      this.pageCurrent = 1
      // 结合当前树节点和搜索条件进行搜索
      this.getList(params)
    },
    // 打开搜索
    openSearch() {
      this.searchView = !this.searchView
    },
    // 自定义表格列
    onChangeCol(arr) {
      this.columnsViewArr = arr
    },
    // 处理排序变化
    onSortChange({ column, prop, order }) {
      // 映射排序字段
      const fieldMapping = {
        '用例编号': 'sortNumber',
        '用例标题': 'title',
        '分类': 'categoryName',
        '优先级': 'priority',
        '类型': 'type',
        '指派给': 'assignUserName',
        '执行人': 'executeUserName',
        '执行结果': 'executeResult',
        '执行时间': 'executeTime'
      }
      if (column.order) {
        this.sortOrder = column.order == 'ascending' ? 'asc' : 'desc'
      } else {
        this.sortOrder = ''
      }
      this.sortField = fieldMapping[column.label] || ''
      this.getList(this.searchParams)
    },
    getList(params = {}, showLoading = true) {
      if (!params || Object.keys(params).length === 0) {
        params = {}
      }

      // 保存当前搜索条件
      this.searchParams = { ...params }

      // 设置loading状态
      if (showLoading) {
        this.tableLoading = true
      }

      // 构建请求参数
      const requestParams = this.getPostData('page', 'limit')
      requestParams.taskId = this.$route.params.id
      // 处理标题
      if (params.title) {
        requestParams.title = params.title
      }
      if (params.sortNumber) {
        requestParams.sortNumber = params.sortNumber
      }

      // 处理类型
      if (params.type) {
        requestParams.type = params.type.split(',')
      }

      // 处理优先级
      if (params.priority) {
        requestParams.priority = params.priority.split(',')
      }
      // 处理关联项目
      if (params.projectName) {
        requestParams.projectIds = params.projectName.split(',')
      }
      if (params.assignUserName) {
        requestParams.assignUserName = params.assignUserName
      }

      // 处理分类
      if (params.categoryName) {
        requestParams.categoryName = params.categoryName
      }
      // 处理创建人
      if (params.createByName) {
        requestParams.createByName = params.createByName
      }
      // 处理执行人
      if (params.executeUserName) {
        requestParams.executeUserName = params.executeUserName
      }
      // 处理执行结果
      if (params.executeResult) {
        requestParams.executeResult = params.executeResult
      }

      // 处理时间范围
      if (params.executeTime) {
        const timeArr = params.executeTime.split(',')
        if (timeArr.length === 2) {
          requestParams.executeStartTime = timeArr[0]
          requestParams.executeEndTime = timeArr[1]
        }
      }

      // 处理排序
      if (this.sortField) {
        requestParams.sortField = this.sortField
        requestParams.sortOrder = this.sortOrder || ''
      } else if (!this.sortField && !params.sortField) {
        // 默认排序
        requestParams.sortField = 'sortNumber'
        requestParams.sortOrder = 'desc'
      }

      // 添加分类ID
      if (this.currentNodeId !== undefined && this.currentNodeId !== '0') {
        requestParams.categoryId = this.currentNodeId
      }
      this.requestParams = requestParams

      // 调用API获取数据
      taskCasePage(requestParams).then(res => {
        if (res && res.code === 0) {
          this.tableData = res.data.records || []
          this.tableTotal = Number(res.data.total) || 0
          this.calculateStatistics(this.tableData)
        } else {
          this.tableData = []
          this.tableTotal = 0
          this.$message.warning(res.msg || '获取数据失败')
        }
        this.tableLoading = false
        this.handleSelection()
      }).catch(error => {
        console.error('获取测试用例列表失败:', error)
        this.tableData = []
        this.tableTotal = 0
        this.tableLoading = false
      })
    },
    // 刷新表格
    'refresh': function() {
      this.$emit('refresh')
      this.selectItem = []
      if (this.single) {
        this.$emit('on-select', [])
        this.setHighlightRow(null)
      }
      this.getList(this.searchParams)
    },
    // 执行统计
    handleExecuteStatistics() {
      this.executeStatisticsVisible = true
      // 调用API获取数据
      this.getExecuteStatistics()
    },

    // 获取执行统计数据
    getExecuteStatistics() {
      const params = {
        taskId: this.$route.params.id
      }

      this.tableLoading = true
      taskCaseGetExecuteStatistics(params).then(res => {
        if (res && res.code === 0) {
          // 处理返回的数据
          this.processStatisticsData(res.data)

          // 初始化图表
          this.$nextTick(() => {
            this.initCharts()
          })
        }
        this.tableLoading = false
      }).catch(error => {
        console.error('获取执行统计数据失败:', error)
        this.tableLoading = false
      })
    },

    // 处理统计数据
    processStatisticsData(data) {
      // 处理功能类型数据
      if (data.functionResultType && Array.isArray(data.functionResultType)) {
        this.functionData = data.functionResultType.map(item => ({
          name: item.resultType || '-',
          value: item.sum || 0,
          percentage: item.proportion + '%' || '0%'
        }))
      }

      // 处理性能类型数据
      if (data.behaviorResultType && Array.isArray(data.behaviorResultType)) {
        this.performanceData = data.behaviorResultType.map(item => ({
          name: item.resultType || '-',
          value: item.sum || 0,
          percentage: item.proportion + '%' || '0%'
        }))
      }

      // 处理安全类型数据
      if (data.securityResultType && Array.isArray(data.securityResultType)) {
        this.securityData = data.securityResultType.map(item => ({
          name: item.resultType || '-',
          value: item.sum || 0,
          percentage: item.proportion + '%' || '0%'
        }))
      }
    },

    // 初始化所有图表
    initCharts() {
      this.initFunctionChart()
      this.initPerformanceChart()
      this.initSecurityChart()
    },

    // 初始化功能类型图表
    initFunctionChart() {
      const chartDom = this.$refs.functionChart
      if (!chartDom) return

      const chart = echarts.init(chartDom)
      const option = this.getChartOption(this.functionData, '功能类型')
      chart.setOption(option)
    },

    // 初始化性能类型图表
    initPerformanceChart() {
      const chartDom = this.$refs.performanceChart
      if (!chartDom) return

      const chart = echarts.init(chartDom)
      const option = this.getChartOption(this.performanceData, '性能类型')
      chart.setOption(option)
    },

    // 初始化安全类型图表
    initSecurityChart() {
      const chartDom = this.$refs.securityChart
      if (!chartDom) return

      const chart = echarts.init(chartDom)
      const option = this.getChartOption(this.securityData, '安全类型')
      chart.setOption(option)
    },

    // 获取图表配置
    getChartOption(data, title) {
      return {
        title: {
          text: title,
          left: 'center',
          top: 0,
          textStyle: {
            fontSize: 16,
            fontWeight: 'bold'
          }
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        color: this.chartColors,
        series: [
          {
            name: title,
            type: 'pie',
            radius: '65%',
            center: ['50%', '55%'],
            data: data.map(item => ({
              name: item.name,
              value: item.value
            })),
            label: {
              show: false
            },
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.resource-table {
  height: 100%;
  padding: 0;
  padding-bottom: 15px;

  .collapse-btn {
    z-index: 200;
    position: absolute;
    top: calc(50% - 80px);
    left: -10px;
    width: 10px;
    height: 60px;
    background-color: var(--color-600);
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }

  .collapse-transition {
    transition: all 0.3s ease;
  }
}

.operation-right {
  display: flex;
  align-items: center;
  .exeBtn{
    svg {
      fill: #606266;
      vertical-align: top;
    }
    &:hover {
      svg {
        fill: var(--color-600);
        color: var(--color-600);
      }
    }
  }
}

.operation-left {
  display: flex;
  align-items: center;
}

.result-statistics {
  font-size: 14px;
}

.tree-container {
  height: 100%;
  overflow-y: auto;
  border: 1px solid var(--neutral-300);
}

.operation-wrap {
  flex-shrink: 0;
}

.t-table-view {
  flex: 1;
  overflow: hidden;
}

.success {
  color: #67C23A;
}

.warning {
  color: #E6A23C;
}

.primary {
  color: var(--color-600);
}

.danger {
  color: #F56C6C;
}

.chart-section {
  display: flex;
  flex-direction: column;
}

.chart-item {
  width: 100%;
  border-bottom: 1px solid #f7f7f7;
  padding: 10px 0;
  &:last-child {
    border-bottom: none;
  }
}

.chart-title {
  text-align: center;
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 10px;
}

.chart-layout {
  display: flex;
}

.chart-left {
  flex: 1;
  background-color: #f2f2f2;
  border-radius: 4px;
  padding: 10px;
}

.chart-right {
  flex: 1;
  margin-left: 20px;
}

.chart-container {
  width: 100%;
  height: 200px;
}
</style>
