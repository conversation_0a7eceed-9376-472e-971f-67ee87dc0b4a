<template>
  <el-avatar
    v-if="disabled"
    slot="reference"
    v-bind="$attrs"
    :src="src"
    :key="src"
    :style="{ fontSize: fontSize, background: background }"
    :class="{ 'cursor-pointer': !disabled }"
    :size="size"
    fit="fill">{{ showName }}</el-avatar>

  <el-popover
    v-else
    v-model="popoverShow"
    :visible-arrow="false"
    :trigger="trigger"
    :disabled="popoverDisabled"
    placement="bottom"
    width="250"
    popper-class="no-padding-popover">
    <xr-user-view
      v-loading="loading"
      :data="userData"
      :src="src" />
    <el-avatar
      slot="reference"
      v-bind="$attrs"
      :src="src"
      :key="src"
      :style="{ fontSize: fontSize, background: background }"
      :class="{ 'cursor-pointer': !disabled }"
      :size="size"
      fit="fill">{{ showName }}</el-avatar>
  </el-popover>
</template>

<script>
import { getUserDetailAPI } from '@/api/usercenter/user'
import XRTheme from '@/styles/xr-theme.scss'

export default {
  // Avatar 头像
  name: 'XrAvatar',
  components: {
    XrUserView: () => import('../XrUserView')
  },
  inheritAttrs: false,
  props: {
    name: String,
    id: [Number, String],
    size: {
      type: [Number, String],
      default: 38
    },
    src: String,
    disabled: {
      type: Boolean,
      default: true
    },
    trigger: {
      type: String,
      default: 'click'
    },
    background: {
      type: String,
      default: XRTheme.xrColorPrimary
    }
  },
  data() {
    return {
      popoverShow: false,
      loading: false,
      userData: null
    }
  },
  computed: {
    fontSize() {
      if (this.size <= 30) {
        return '12px'
      }
      return '14px'
    },

    showName() {
      return this.name && this.name.length > 2 ? this.name.slice(-2) : this.name
    },

    popoverDisabled() {
      if (this.disabled) {
        return true
      }

      return !this.id
    }
  },
  watch: {
    popoverShow(val) {
      if (!this.userData) {
        this.getUserData()
      }
    }
  },
  created() {},

  beforeDestroy() {},
  methods: {

    getUserData() {
      getUserDetailAPI({ userId: this.id })
        .then(res => {
          this.userData = res.data
        })
        .catch(() => {})
    }
  }
}
</script>

<style lang="scss" scoped>
.cursor-pointer {
  cursor: pointer;
}

.el-avatar {
  /deep/ img {
    width: 100%;
    background: white !important;
  }
}
</style>
