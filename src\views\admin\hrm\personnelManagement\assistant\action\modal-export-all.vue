<template>
  <div class="dialog-wrap">
    <div>请确认是否导出列表数据?</div>
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>

<script>
import modalMixins from '@/packages/mixins/modal_form'

export default {
  components: {
  },
  mixins: [modalMixins],
  props: {
    data: {
      type: Array,
      default: () => {
        return []
      }
    },
    searchName: {
      type: Object
    }
  },
  data() {
    return {}
  },
  computed: {
  },
  created() {
  },
  methods: {
    close() {
      this.$emit('close')
    },
    confirm: function() {
      this.$emit('exportOut', 'all')
      this.close()
    }
  }
}
</script>
