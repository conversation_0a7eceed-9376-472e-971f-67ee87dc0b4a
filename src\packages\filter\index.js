import Vue from 'vue'
import moment from 'moment'

/**
 * 存储单位转换
 * #num: 原始数据 【number】
 * #unit: 原始单位 【大写】【string】
 * #outUnit: 固定单位 【大写】【string】【不填写自动匹配单位】
 * #fixed：固定小数点后数位 默认2
 * 示例1： this.$options.filters['transStore'](storeSize, 'KB')
 * 示例： this.$options.filters['transStore'](storeSize, 'KB', 'GB')
 */
Vue.filter('transStore', function(num, unit, fixed, outUnit) {
  const viewUnit = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']
  let viewNum = num || 0
  let unitNum = viewUnit.indexOf(unit)
  if (!outUnit) {
    while (viewNum >= 1024) {
      viewNum = viewNum / 1024
      unitNum++
    }
    return viewNum.toFixed(fixed !== undefined ? fixed : 2) + ' ' + viewUnit[unitNum]
  } else {
    const outUnitNum = viewUnit.indexOf(outUnit)
    viewNum = viewNum / Math.pow(1024, (outUnitNum - unitNum))
    return viewNum.toFixed(fixed !== undefined ? fixed : 2) + ' ' + outUnit
  }
})
/**
 * 原始值及单位 转换为 最小整数值及单位，例如(1024 MB --> 1G；1500MB --> 1500MB；2048 MB --> 2G)
 * #num: 原始数据 【number】
 * #unit: 原始单位 【大写】【string】
 * 示例1： this.$options.filters['transStoreInt'](storeSize, 'MB')
 */
Vue.filter('transStoreInt', function(num, unit) {
  const viewUnit = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']
  let viewNum = num
  let unitNum = viewUnit.indexOf(unit)
  while (viewNum >= 1024 && /^[1-9]\d*$/.test(viewNum / 1024)) {
    viewNum = viewNum / 1024
    unitNum++
  }
  return viewNum + ' ' + viewUnit[unitNum]
})
/**
 * 存储单位转换【如果转换完为小数则括号展示最小整数值】
 * #num: 原始数据 【number】
 * #unit: 原始单位 【大写】【string】
 * #outUnit: 固定单位 【大写】【string】【不填写自动匹配单位】
 * #fixed：固定小数点后数位 默认2
 * 示例1：this.$options.filters['transStoreShowInt'](2048000, 'MB') --> '1.95 TB（2000 GB）'
 * 示例2：this.$options.filters['transStoreShowInt'](1150701, 'MB') --> '1.10 TB（1150701 MB）'
 * 示例2：this.$options.filters['transStoreShowInt'](2048, 'MB') --> '2.00 GB'
 */
Vue.filter('transStoreShowInt', function(num, unit, fixed, outUnit) {
  const viewUnit = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']
  let viewNum = Number(num) || 0
  let unitNum = viewUnit.indexOf(unit)
  let viewNumInt = Number(num) || 0
  let unitNumInt = viewUnit.indexOf(unit)
  while (viewNumInt >= 1024 && /^[1-9]\d*$/.test(viewNumInt / 1024)) {
    viewNumInt = viewNumInt / 1024
    unitNumInt++
  }
  if (!outUnit) {
    while (viewNum >= 1024) {
      viewNum = viewNum / 1024
      unitNum++
    }
    const strInt = viewNum === viewNumInt ? '' : `（${viewNumInt} ${viewUnit[unitNumInt]}）`
    return viewNum.toFixed(fixed !== undefined ? fixed : 2) + ' ' + viewUnit[unitNum] + strInt
  } else {
    const outUnitNum = viewUnit.indexOf(outUnit)
    viewNum = viewNum / Math.pow(1024, (outUnitNum - unitNum))
    const strInt = viewNum === viewNumInt ? '' : `（${viewNumInt} ${viewUnit[unitNumInt]}）`
    return viewNum.toFixed(fixed !== undefined ? fixed : 2) + ' ' + outUnit + strInt
  }
})

Vue.filter('nfvoMoment', function(value, formatString) {
  formatString = formatString || 'YYYY-MM-DD HH:mm:ss'
  return value ? moment.utc(value).format(formatString) : value
})
