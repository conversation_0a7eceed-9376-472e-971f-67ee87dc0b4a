<template>
  <page-table
    ref="table"
    :default-selected-arr="defaultSelectedArr"
    :cache-pattern="true"
    :data="data"
    @refresh="refresh"
    @link-event="linkEvent"
    @on-select="tabelSelect"
    @on-current="tabelCurrent"
  >
    <action-menu
      slot="action"
      :module-name="moduleName"
      :data="data"
      :select-item="selectItem"
      @call="actionHandler"
    />
  </page-table>
</template>

<script>
import pageTable from './table/index.vue'
import actionMenu from './action/index.vue'
import moduleConf from './config.js'

export default {
  name: 'TestingReport',
  components: {
    pageTable,
    actionMenu
  },
  props: {
    data: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      moduleName: moduleConf.name,
      selectItem: [],
      defaultSelectedArr: []
    }
  },
  created() {
  },
  methods: {
    // 列表点击
    linkEvent({ name, row, params }) {
      this.$router.push({ name: name, params: params })
    },
    // 返回已选
    tabelSelect(data) {
      this.selectItem = data
    },
    // 返回单选
    tabelCurrent(row) {
      this.selectItem = [row]
    },
    // action menu 事件
    actionHandler(type, data) {
      switch (type) {
        case 'refresh':
          this.$refs['table'].getList()
          break
        case 'create':
          break
        case 'edit':
          break
        case 'delete':
          // 调用删除接口
          break
      }
    },
    refresh() {}
  }
}
</script>
