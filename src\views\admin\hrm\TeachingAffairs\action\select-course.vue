<template>
  <div class="drawer-wrap">
    <selected-course
      ref="table"
      :filter-data="{}"
      :height="null"
      :link="false"
      :single="true"
      :data="programId"
      :class-code="classId"
      @on-select="onSelect"
    />
    <div class="drawer-footer">
      <div>
        <el-button :disabled="!selectedItem.length" type="primary" @click="confirm">确定</el-button>
        <el-button type="text" @click="close">取消</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import selectedCourse from '../selectedCourse/index.vue'
export default {
  components: {
    selectedCourse
  },
  props: {
    data: String,
    classCode: String
  },
  data() {
    return {
      selectedItem: [],
      programId: '',
      classId: ''
    }
  },
  created() {
    this.programId = this.data
    this.classId = this.classCode
  },
  methods: {
    'onSelect': function(data) {
      this.selectedItem = data
    },
    'close': function() {
      this.$emit('close')
    },
    'confirm': function() {
      this.$emit('call', 'confirm_course', this.selectedItem)
    }
  }
}
</script>
<style lang="scss">
.drawer-footer{
  display: flex;
  align-items: center;
  height: 8%;
}
</style>
