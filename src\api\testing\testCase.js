import request from '@/utils/request'
const headers = { 'Content-Type': 'application/json;charset=UTF-8' }

// 各个地址获取分类树结构
export function caseCategoryTreeUrlName(data) {
  return request({
    url: '/testing/case/category/tree/urlName',
    method: 'post',
    data,
    headers
  })
}
// 获取树状分类列表
export function caseCategoryList(data) {
  return request({
    url: '/testing/case/category/list',
    method: 'post',
    data,
    headers
  })
}

// 新增树状分类
export function caseCategoryCreate(data) {
  return request({
    url: '/testing/case/category/create',
    method: 'post',
    data,
    headers
  })
}

// 修改分类名称
export function caseCategoryUpdate(data) {
  return request({
    url: '/testing/case/category/update',
    method: 'post',
    data,
    headers
  })
}

// 删除分类及子分类
export function caseCategoryDelete(id) {
  return request({
    url: `/testing/case/category/delete/${id}`,
    method: 'post',
    headers
  })
}

// 分类详情
export function caseCategoryGet(id) {
  return request({
    url: '/testing/case/category/get',
    method: 'post',
    data: { id },
    headers
  })
}

// 移动分类至目标父分类
export function caseCategoryMove(data) {
  return request({
    url: '/testing/case/category/move',
    method: 'post',
    data,
    headers
  })
}

// 创建测试用例
export function testCaseCreate(data) {
  return request({
    url: '/testing/case/create',
    method: 'post',
    data,
    headers
  })
}

// 编辑测试用例
export function testCaseUpdate(data) {
  return request({
    url: '/testing/case/update',
    method: 'post',
    data,
    headers
  })
}

// 复制测试用例
export function testCaseCopy(data) {
  return request({
    url: '/testing/case/copy',
    method: 'post',
    data,
    headers
  })
}

// 批量导入用例
export function testCaseImport(data, type) {
  return request({
    url: `/testing/case/import?type=${type}`,
    method: 'post',
    data,
    headers
  })
}
// 批量导入用例
export function downErrorExcel(data) {
  return request({
    url: `/testing/case/downErrorExcel`,
    method: 'post',
    data,
    headers,
    responseType: 'blob'
  })
}

// 分页搜索用例
export function testCasePage(data) {
  return request({
    url: '/testing/case/page',
    method: 'post',
    data,
    headers
  })
}

// 用例列表(不分页)
export function testCaseList(data) {
  return request({
    url: '/testing/case/list',
    method: 'post',
    data,
    headers
  })
}

// 用例详情
export function testCaseGet(id) {
  return request({
    url: `/testing/case/get/${id}`,
    method: 'get',
    headers
  })
}

// 删除用例
export function testCaseDelete(data) {
  return request({
    url: '/testing/case/delete',
    method: 'post',
    data,
    headers
  })
}

// 下载导入模板
export function downloadTemplate(params) {
  return request({
    url: '/testing/case/download/template',
    method: 'get',
    params,
    headers,
    responseType: 'blob'
  })
}
// 导出用例
export function testCaseExport(data) {
  return request({
    url: '/testing/case/export',
    method: 'post',
    data,
    headers,
    responseType: 'blob'
  })
}

// 创建测试套件
export function testSuiteCreate(data) {
  return request({
    url: '/testing/suite/create',
    method: 'post',
    data,
    headers
  })
}

// 编辑测试套件
export function testSuiteUpdate(data) {
  return request({
    url: '/testing/suite/update',
    method: 'post',
    data,
    headers
  })
}

// 复制测试套件
export function testSuiteCopy(data) {
  return request({
    url: '/testing/suite/copy',
    method: 'post',
    data,
    headers
  })
}

// 删除测试套件
export function testSuiteDelete(data) {
  return request({
    url: '/testing/suite/delete',
    method: 'post',
    data,
    headers
  })
}

// 添加用例到套件
export function testSuiteAddCases(data) {
  return request({
    url: '/testing/suite/add/cases',
    method: 'post',
    data,
    headers
  })
}

// 套件详情
export function testSuiteGet(id) {
  return request({
    url: `/testing/suite/get/${id}`,
    method: 'get',
    headers
  })
}

// 从套件移除用例
export function testSuiteRemoveCases(data) {
  return request({
    url: '/testing/suite/remove/cases',
    method: 'post',
    data,
    headers
  })
}

// 查询套件用例列表接口
export function testSuiteCasesPage(data) {
  return request({
    url: '/testing/suite/cases/page',
    method: 'post',
    data,
    headers
  })
}

// 根据套件导出用例
export function testSuiteCasesExport(data) {
  return request({
    url: '/testing/suite/cases/export',
    method: 'post',
    data,
    headers
  })
}
// 获取分类树结构
export function caseCategoryTree(params) {
  return request({
    url: '/testing/case/category/tree',
    method: 'get',
    params,
    headers
  })
}

// 上传附件
export function uploadApplicationFile(data) {
  return request({
    url: '/testing/testProcesses/uploadFile',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
// 详情删除附件
export function deleteFileById(data) {
  return request({
    url: '/testing/case/deleteFileById',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
// 详情更新附件
export function updateFile(data) {
  return request({
    url: '/testing/case/updateFile',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
// 删除附件
export function deleteApplicationFile(data) {
  return request({
    url: '/testing/testProcesses/deleteFile',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 根据id获取附件
export function getTestProcessesFileById(data) {
  return request({
    url: '/testing/testProcesses/getFileBySource',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
// 通过用例id分页查询问题清单
export function getIssueList(data) {
  return request({
    url: '/testing/case/issue/page',
    method: 'post',
    data,
    headers
  })
}
// 根据用例id查询套件分页
export function getCaseSuiteList(data) {
  return request({
    url: '/testing/case/suite/page',
    method: 'post',
    data,
    headers
  })
}
// 查询套件分页
export function getSuiteList(data) {
  return request({
    url: '/testing/suite/page',
    method: 'post',
    data,
    headers
  })
}

// 根据套件id分页查询用例
export function getSuiteCaseList(data) {
  return request({
    url: '/testing/suite/case/page',
    method: 'post',
    data,
    headers
  })
}

// 套件中的分页选择用例
export function getSuiteSelectCaseList(data) {
  return request({
    url: '/testing/suite/select/case',
    method: 'post',
    data,
    headers
  })
}

// 套件关联用例
export function suiteAssociationCase(data) {
  return request({
    url: '/testing/suite/suiteAssociationCase',
    method: 'post',
    data,
    headers
  })
}
// 从套件中移除用例
export function suiteCaseRemove(data) {
  return request({
    url: '/testing/suite/case/remove',
    method: 'post',
    data,
    headers
  })
}
// 根据套件导出用例列表
export function suiteCaseExport(data) {
  return request({
    url: '/testing/suite/case/export',
    method: 'post',
    data,
    headers,
    responseType: 'blob'
  })
}
// 测试任务用例分页查询
export function taskCasePage(data) {
  return request({
    url: '/testing/task/case/page',
    method: 'post',
    data,
    headers
  })
}
// 选择用例分页查询
export function taskCaseSelectPage(data) {
  return request({
    url: '/testing/task/case/select/page',
    method: 'post',
    data,
    headers
  })
}
// 任务关联用例
export function taskCaseAssociation(data) {
  return request({
    url: '/testing/task/case/association',
    method: 'post',
    data,
    headers
  })
}
// 选择套件分页
export function taskCaseSuitePage(data) {
  return request({
    url: '/testing/task/case/suite/page',
    method: 'post',
    data,
    headers
  })
}
// 关联套件
export function taskCaseAssociationSuite(data) {
  return request({
    url: '/testing/task/case/association/suite',
    method: 'post',
    data,
    headers
  })
}
// 指派给
export function taskCaseAssignTo(data) {
  return request({
    url: '/testing/task/case/assignTo',
    method: 'post',
    data,
    headers
  })
}
// 移除
export function taskCaseDelete(data) {
  return request({
    url: '/testing/task/case/delete',
    method: 'post',
    data,
    headers
  })
}
// 测试任务用例导出
export function taskCaseExport(data) {
  return request({
    url: '/testing/task/case/export',
    method: 'post',
    data,
    headers,
    responseType: 'blob'
  })
}
// 创建动态表单
export function dynamicFormCreate(data) {
  return request({
    url: '/testing/dynamic/form/create',
    method: 'post',
    data,
    headers
  })
}
// 获取动态表单
export function getByModelByKey(modelKey) {
  return request({
    url: `/testing/dynamic/form/getByModelKey/${modelKey}`,
    method: 'get',
    headers
  })
}
// 查看测试任务用例详情
export function taskCaseDetail(id) {
  return request({
    url: `/testing/task/case/get/${id}`,
    method: 'post',
    headers
  })
}

// 获取用户列表
export function getUserListAPI(data) {
  return request({
    url: '/adminUser/queryUserList',
    method: 'post',
    data,
    headers
  })
}
// 保存执行用例
export function taskCaseSaveResult(data) {
  return request({
    url: '/testing/task/case/save/result',
    method: 'post',
    data,
    headers
  })
}
// 根据任务用例id查询执行记录
export function taskCaseGetExecuteResult(taskCaseId) {
  return request({
    url: `/testing/task/case/get/execute/result/${taskCaseId}`,
    method: 'post',
    headers
  })
}
// 执行统计
export function taskCaseGetExecuteStatistics(data) {
  return request({
    url: `/testing/task/case/execute/statistics`,
    method: 'post',
    data,
    headers
  })
}
// 获取历史记录
export function getOperationHistoryList(params) {
  return request({
    url: `/testing/problem/getOperationHistoryList`,
    method: 'get',
    params,
    headers
  })
}

// 新增操作记录备注
export function operaRecordAddRemark(data) {
  return request({
    url: '/testing/problem/addRemark',
    method: 'post',
    data,
    headers
  })
}

// 编辑操作记录备注
export function operaRecordUpdateRemark(data) {
  return request({
    url: '/testing/problem/updateRemark',
    method: 'post',
    data,
    headers
  })
}

// 项目检测分页查询
export function caseProjectPage(data) {
  return request({
    url: '/testing/case/project/page',
    method: 'post',
    data,
    headers
  })
}
// 解绑套件
export function caseUnbindSuite(data) {
  return request({
    url: '/testing/case/unbind/suite',
    method: 'post',
    data,
    headers
  })
}
