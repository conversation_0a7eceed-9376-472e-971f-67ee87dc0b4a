import request from '@/utils/request'
const headers = { 'Content-Type': 'application/json;charset=UTF-8' }

// 获取厂商列表
export function getManufacturerListApi(data) {
  return request({
    url: '/testing/vendor/page',
    method: 'post',
    data,
    headers
  })
}

// 新增厂商
export function addManufacturerApi(data) {
  return request({
    url: '/testing/vendor/create',
    method: 'post',
    data,
    headers
  })
}

// 编辑厂商
export function editManufacturerApi(data) {
  return request({
    url: '/testing/vendor/update',
    method: 'post',
    data,
    headers
  })
}

// 查询厂商信息
export function getManufacturerByIdApi(id) {
  return request({
    url: `/testing/vendor/get/${id}`,
    method: 'get',
    headers
  })
}

// 删除厂商
export function deleteManufacturerApi(data) {
  return request({
    url: '/testing/vendor/delete',
    method: 'post',
    data,
    headers
  })
}

// 导出厂商
export function exportManufacturer<PERSON><PERSON>(data) {
  return request({
    url: '/testing/vendor/export',
    method: 'post',
    headers,
    data,
    responseType: 'blob'
  })
}

// 联系人列表
export function getUserListApi(data) {
  return request({
    url: '/testing/vendor/user/list',
    method: 'post',
    data,
    headers
  })
}

// 分页查询联系人信息
export function getContractListApi(data) {
  return request({
    url: '/testing/vendorContact/page',
    method: 'post',
    data,
    headers
  })
}

// 新增联系人
export function addContractApi(data) {
  return request({
    url: '/testing/vendorContact/create',
    method: 'post',
    data,
    headers
  })
}

// 根据id查询联系人信息
export function getContractByIdApi(id) {
  return request({
    url: `/testing/vendorContact/get/${id}`,
    method: 'get',
    headers
  })
}

// 删除联系人
export function deleteContractApi(data) {
  return request({
    url: '/testing/vendorContact/delete',
    method: 'post',
    data,
    headers
  })
}

// 设为主联系人
export function setMainContractApi(vendorId, contractId) {
  return request({
    url: `/testing/vendorContact/${vendorId}/${contractId}/setMain`,
    method: 'post',
    headers
  })
}

// 导出联系人
export function exportContactApi(data) {
  return request({
    url: '/testing/vendorContact/export',
    method: 'post',
    headers,
    data,
    responseType: 'blob'
  })
}

// 获取厂商联系人
export function getVendorContactUserList(data) {
  return request({
    url: '/testing/vendorContact/get/user/list',
    method: 'post',
    data,
    headers
  })
}
