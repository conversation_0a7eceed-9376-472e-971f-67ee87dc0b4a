import request from '@/utils/request'
const headers = { 'Content-Type': 'application/json;charset=UTF-8' }

// 分页查询单兵演练列表
export function sceneBasicInfoInstance(data) {
  return request({
    url: 'scene/sceneBasicInfoInstance/queryPage',
    method: 'post',
    headers,
    data
  })
}

// 概览布局配置
export function getLayout(params) {
  return request({
    url: `scene/overview/config/layout/info`,
    method: 'get',
    params
  })
}
export function saveLayoutConfig(data) {
  return request({
    url: 'scene/overview/config/layout/save',
    method: 'post',
    data,
    headers
  })
}
// 数量 统计
export function overviewCountInfo(params) {
  return request({
    url: 'scene/overview/count/info',
    method: 'get',
    headers,
    params
  })
}
// 单兵演练参训次数top5 统计
export function individualDrillTop5(params) {
  return request({
    url: 'scene/overview/individualDrillTop5',
    method: 'get',
    headers,
    params
  })
}
// 团体演练人员规模top5 统计
export function groupDrillTop5(params) {
  return request({
    url: 'scene/overview/groupDrillTop5',
    method: 'get',
    headers,
    params
  })
}
// 团体演练人员规模top5 统计
export function groupDrillPersonnelScaleTop5(params) {
  return request({
    url: 'scene/overview/groupDrillPersonnelScaleTop5',
    method: 'get',
    headers,
    params
  })
}
// 单兵/团体演练次数 统计
export function numberDrills(params) {
  return request({
    url: 'scene/overview/numberDrills',
    method: 'get',
    headers,
    params
  })
}

// 测试相关概览接口
// 测试项目数量统计
// 注意：当前使用mockData.js中的testProjectCountMock模拟数据
export function testProjectCountInfo(params) {
  return request({
    url: 'test/overview/project/count',
    method: 'get',
    headers,
    params
  })
}

// 测试通过率统计
// 注意：当前使用mockData.js中的testPassRateMock模拟数据
export function testPassRateInfo(params) {
  return request({
    url: 'test/overview/pass/rate',
    method: 'get',
    headers,
    params
  })
}

// 检测申请统计
// 注意：当前使用mockData.js中的testApplicationMock模拟数据
export function testApplicationInfo(params) {
  return request({
    url: 'test/overview/application/info',
    method: 'get',
    headers,
    params
  })
}

// 未完成检测项目统计
// 注意：当前使用mockData.js中的unfinishedTestProjectMock模拟数据
export function unfinishedTestProjectInfo(params) {
  return request({
    url: 'test/overview/unfinished/project',
    method: 'get',
    headers,
    params
  })
}

// 我的代办统计
// 注意：当前使用mockData.js中的myTodoMock模拟数据
export function myTodoInfo(params) {
  return request({
    url: 'test/overview/my/todo',
    method: 'get',
    headers,
    params
  })
}

// 在测项目进度统计
export function testingProjectProgressInfo(params) {
  return request({
    url: 'test/overview/project/progress',
    method: 'get',
    headers,
    params
  })
}

// 未关闭问题统计
export function unclosedIssuesInfo(params) {
  return request({
    url: 'test/overview/unclosed/issues',
    method: 'get',
    headers,
    params
  })
}

// 测试用例统计
export function testCasesInfo(params) {
  return request({
    url: 'test/overview/test/cases',
    method: 'get',
    headers,
    params
  })
}

// 问题统计
export function issueStatisticsInfo(params) {
  return request({
    url: 'test/overview/issue/statistics',
    method: 'get',
    headers,
    params
  })
}

// 项目问题数量排行
export function projectIssueRank(params) {
  return request({
    url: 'test/overview/project/issue/rank',
    method: 'get',
    headers,
    params
  })
}
