<template>
  <div class="resource-table">
    <category @classificationType="classificationType"/>
    <!-- 操作区 -->
    <div class="operation-wrap">
      <div class="operation-left">
        <slot name="action" />
        <el-button type="primary" icon="el-icon-refresh" @click="refresh" />
      </div>
      <div class="operation-right">
        <el-badge :value="searchBtnShowNum">
          <el-button icon="el-icon-search" @click="openSearch">搜索</el-button>
        </el-badge>
        <!-- 自定义表格列 -->
        <t-table-config
          v-if="!customColData.length"
          :data="columnsObj"
          :active-key-arr="columnsViewArr"
          @on-change-col="onChangeCol"
        />
      </div>
    </div>
    <!-- 搜索区 -->
    <t-search-box
      v-show="searchView"
      :search-key-list="searchKeyListView"
      default-placeholder="默认搜索全部"
      @search="searchMultiple"
    />
    <!-- 列表 -->
    <t-table-view
      ref="tableView"
      :height="height"
      :single="true"
      :loading="tableLoading"
      :data="examQuestionList"
      :total="tableTotal"
      :page-size="pageSize"
      :current="pageCurrent"
      :select-item="selectItem"
      current-key="id"
      @on-select="onSelect"
      @on-current="onCurrent"
      @on-change="changePage"
      @on-sort-change="onSortChange"
      @on-page-size-change="onPageSizeChange"
    >
      <el-table-column v-if="columnsViewArr.includes('name')" key="name" :min-width="colMinWidth" prop="name" label="名称" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row.name }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="columnsViewArr.includes('courseCategoryName')" key="courseCategoryName" :min-width="colMinWidth" prop="courseCategoryName" label="分类" show-overflow-tooltip/>
      <el-table-column v-if="columnsViewArr.includes('coursePeriod')" key="coursePeriod" :min-width="colMinWidth" label="课时" prop="coursePeriod" show-overflow-tooltip/>
    </t-table-view>
  </div>
</template>

<script>
import module from '../config.js'
import category from '../category/index.vue'
import tSearchBox from '@/packages/search-box/index.vue'
import tTableView from '@/packages/table-view/index.vue'
import tTableConfig from '@/packages/table-config/table-col-config.vue'
import tableTdMultiCol from '@/packages/table-config/table-td-multi-col.vue'
import mixinsPageTable from '@/packages/mixins/page_table'
import { queryCourse } from '@/api/teacher/index.js'

export default {
  components: {
    tSearchBox,
    tTableView,
    tTableConfig,
    tableTdMultiCol,
    category
  },
  mixins: [mixinsPageTable],
  data() {
    return {
      moduleName: module.name,
      // 搜索配置项
      searchKeyList: [
        { key: 'name', label: '名称', master: true }
      ],
      // 所有可配置显示列 master：不可隐藏 title:列名称
      columnsObj: {
        'name': {
          title: '名称',
          master: true
        },
        'courseCategoryName': {
          title: '分类'
        },
        'coursePeriod': {
          title: '课时'
        }
      },
      // 当前显示列key表 默认，如果localStorage有数据将被覆盖
      columnsViewArr: [
        'name',
        'courseCategoryName',
        'coursePeriod'
      ],
      examQuestionList: [],
      queryParameters: {}
    }
  },
  methods: {
    getList: function(showLoading = true) {
      if (showLoading) {
        this.tableLoading = true
      }
      const data = {
        limit: this.pageSize,
        page: this.pageCurrent
      }
      queryCourse(Object.assign(data, this.queryParameters, this.searchData)).then(res => {
        this.examQuestionList = res.data.records
        this.tableTotal = res.data.total
        this.tableLoading = false
      }).catch(() => {
        this.tableLoading = false
      })
    },
    classificationType(param) {
      this.queryParameters = param
      this.getList()
    }

  }
}
</script>
