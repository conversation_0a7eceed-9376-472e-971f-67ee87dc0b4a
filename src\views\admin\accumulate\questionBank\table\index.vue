<template>
  <div class="resource-table">
    <!-- 操作区 -->
    <div class="operation-wrap">
      <div class="operation-left">
        <slot name="action" />
        <el-button type="primary" icon="el-icon-refresh" @click="refresh" />
      </div>
      <div class="operation-right">
        <el-badge :value="searchBtnShowNum">
          <el-button icon="el-icon-search" @click="openSearch">搜索</el-button>
        </el-badge>
        <!-- 自定义表格列 -->
        <t-table-config
          v-if="!customColData.length"
          :data="columnsObj"
          :active-key-arr="columnsViewArr"
          @on-change-col="onChangeCol"
        />
      </div>
    </div>
    <!-- 搜索区 -->
    <t-search-box
      v-show="searchView"
      :search-key-list="searchKeyListView"
      default-placeholder="默认搜索名称"
      @search="searchMultiple"
    />
    <!-- 列表 -->
    <t-table-view
      ref="tableView"
      :height="height"
      :single="single"
      :multiple-page="multiplePage"
      :loading="tableLoading"
      :data="tableData"
      :total="tableTotal"
      :page-size="pageSize"
      :current="pageCurrent"
      :select-item="selectItem"
      @on-select="onSelect"
      @on-current="onCurrent"
      @on-change="changePage"
      @on-sort-change="onSortChange"
      @on-page-size-change="onPageSizeChange"
    >
      <el-table-column
        v-for="item in columnsViewArr"
        :key="item"
        :min-width="colMinWidth"
        :label="columnsObj[item].title"
        :fixed="columnsObj[item].master ? 'left' : false"
        :show-overflow-tooltip="item !== 'uses'"
      >
        <template slot-scope="scope">
          <!-- 名称 -->
          <span v-if="item === 'questionName' && link">
            <a
              :href="`/baseResource/${moduleName}/detail/${scope.row.id}/overview`"
              @click.prevent="linkEvent(`${moduleName}Detail`, scope.row, { id: scope.row.id, view: 'overview' })"
            >{{ scope.row[item] || "-" }}</a>
          </span>
          <!-- 题型 -->
          <span v-else-if="item === 'questionType'">
            {{ questionConf.questionTypObj[scope.row[item]].label }}
          </span>
          <!-- 难度 -->
          <span v-else-if="item === 'complexity'">
            {{ questionConf.complexityObj[scope.row[item]].label }}
          </span>
          <span v-else-if="item === 'uses'">
            <table-td-multi-col :data="scope.row.uses" :number="scope.row.uses.length">
              <div slot="reference">{{ questionConf.usesObj[scope.row.uses[0]].label }}</div>
              <div v-for="val in scope.row.uses" :key="val">{{ questionConf.usesObj[val].label }}</div>
            </table-td-multi-col>
          </span>
          <span v-else-if="item === 'nodeScale'">{{ scope.row[item] || "0" }}</span>
          <span v-else>{{ scope.row[item] || "-" }}</span>
        </template>
      </el-table-column>
    </t-table-view>
  </div>
</template>

<script>
import questionConf from '../config.js' // 题库配置
import tSearchBox from '@/packages/search-box/index'
import tTableView from '@/packages/table-view/index'
import tTableConfig from '@/packages/table-config/table-col-config'
import tableTdMultiCol from '@/packages/table-config/table-td-multi-col'
import mixinsPageTable from '@/packages/mixins/page_table'
import { getQuestionBank } from '@/api/accumulate/questionBank'

export default {
  components: {
    tSearchBox,
    tTableView,
    tTableConfig,
    tableTdMultiCol
  },
  mixins: [mixinsPageTable],
  props: {
    // 当前模块名称
    moduleName: String,
    // 1-理论 2-靶机 3-仿真
    bankType: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      questionConf: questionConf,
      searchKeyList: [
        { key: 'questionName', label: '名称', master: true },
        { key: 'content', label: '题干' },
        {
          key: 'questionType',
          label: '题型',
          type: 'radio',
          valueList: this.bankType == 1 ? questionConf.theoryTypeArr : this.bankType == 2 ? questionConf.targetDeviceTypeArr : questionConf.simulationTypeArr
        },
        {
          key: 'complexity',
          label: '难度',
          type: 'radio',
          valueList: questionConf.complexityArr
        },
        {
          key: 'useList',
          label: '用途',
          type: 'select',
          valueList: questionConf.usesArr
        },
        { key: 'time_range', type: 'time_range', label: '创建时间' },
        { key: 'skillPoints', label: '技能点' }
      ],
      // 所有可配置显示列 master：不可隐藏 title:列名称
      columnsObj: {},
      // 当前显示列key表 默认，如果localStorage有数据将被覆盖
      columnsViewArr: []
    }
  },
  created() {
    if (this.bankType == 1) {
      this.columnsObj = {
        'questionName': { title: '名称', master: true },
        'questionType': { title: '题型' },
        'complexity': { title: '难度' },
        'questionDepotName': { title: '题库' },
        'categoryName': { title: '分类' },
        'uses': { title: '用途' },
        'createTime': { title: '创建时间' }
      }
      this.columnsViewArr = ['questionName', 'questionType', 'complexity', 'questionDepotName', 'categoryName', 'uses', 'createTime']
    } else if (this.bankType == 2) {
      this.columnsObj = {
        'questionName': { title: '名称', master: true },
        'networkElementName': { title: '靶机' },
        'questionType': { title: '题型' },
        'complexity': { title: '难度' },
        'questionDepotName': { title: '题库' },
        'categoryName': { title: '分类' },
        'uses': { title: '用途' },
        'createTime': { title: '创建时间' }
      }
      this.searchKeyList = [
        { key: 'questionName', label: '名称', master: true },
        { key: 'content', label: '题干' },
        {
          key: 'questionType',
          label: '题型',
          type: 'radio',
          valueList: this.bankType == 1 ? questionConf.theoryTypeArr : this.bankType == 2 ? questionConf.targetDeviceTypeArr : questionConf.simulationTypeArr
        },
        {
          key: 'complexity',
          label: '难度',
          type: 'radio',
          valueList: questionConf.complexityArr
        },
        {
          key: 'useList',
          label: '用途',
          type: 'select',
          valueList: questionConf.usesArr
        },
        { key: 'time_range', type: 'time_range', label: '创建时间' },
        { key: 'skillPoints', label: '技能点' },
        { key: 'networkElementName', label: '靶机' }
      ]
      this.columnsViewArr = ['questionName', 'networkElementName', 'questionType', 'complexity', 'questionDepotName', 'categoryName', 'uses', 'createTime']
    } else if (this.bankType == 3) {
      this.columnsObj = {
        'questionName': { title: '名称', master: true },
        'questionType': { title: '题型' },
        'nodeScale': { title: '节点数量' },
        'complexity': { title: '难度' },
        'questionDepotName': { title: '题库' },
        'categoryName': { title: '分类' },
        'uses': { title: '用途' },
        'createTime': { title: '创建时间' }
      }
      this.columnsViewArr = ['questionName', 'questionType', 'nodeScale', 'complexity', 'questionDepotName', 'categoryName', 'uses', 'createTime']
    }
  },
  methods: {
    getList: function(showLoading = true) {
      if (showLoading) {
        this.tableLoading = true
      }
      const params = this.getPostData('page', 'limit')
      params.createTimeBegin = params.startTime
      params.createTimeEnd = params.endTime
      delete params.startTime
      delete params.endTime
      getQuestionBank(params).then((res) => {
        if (res.code === 0 || res.code === 200) {
          res.data.records.forEach(item => {
            item.uses = item.uses.split(',')
          })
          this.tableData = res.data.records
          this.tableTotal = Number(res.data.total)
          this.tableLoading = false
          this.handleSelection()
        }
      }).catch(() => {
        this.tableLoading = false
      })
    }
  }
}
</script>
