<template>
  <el-dialog
    :visible.sync="dialogVisible"
    :destroy-on-close="false"
    title="执行配置"
    width="520px"
    append-to-body
    @close="handleClose"
  >
    <div v-loading="loading" class="dialog-wrap">
      <el-radio-group v-model="configType" size="small" class="common-radio-group" @change="handleConfigTypeChange">
        <el-radio-button v-for="item in executeConfigList" :key="item.value" :label="item.value">
          {{ item.label }}
        </el-radio-button>
      </el-radio-group>

      <el-form style="padding: 0;" class="config-form" label-width="100px" label-position="left">
        <div v-for="(field, fieldIndex) in currentConfigFields" :key="field.uuid" :class="fieldIndex>0?'form-item-bg':''" class="form-item mb-10">
          <!-- 执行结果 -->
          <div v-if="field.type === 'select'" class="select-field-wrap">
            <div class="select-options">
              <div v-for="(option, optIndex) in field.options" :key="optIndex" class="option-item">
                <el-form-item :label="optIndex === 0 ? field.label : ''" :required="optIndex === 0 && field.required" :class="optIndex > 0 ? 'no-label-item' : ''">
                  <div class="option-input-wrap">
                    <el-input
                      v-model="field.options[optIndex].value"
                      :maxlength="64"
                      placeholder="请输入"
                      @input="validateOption(field, optIndex)"
                    />
                    <i
                      v-if="optIndex >= 2"
                      class="el-icon-delete"
                      style="color: var(--color-600);"
                      @click="removeOption(field, optIndex)"
                    />
                  </div>
                </el-form-item>
              </div>
              <div class="add-option">
                <el-button icon="el-icon-plus" @click="addOption(field)">添加选项</el-button>
              </div>
            </div>
            <!-- 添加删除整个字段的功能，但只对非第一个select字段生效 -->
            <i
              v-if="fieldIndex > 0 && field.key !== 'execResult'"
              class="el-icon-delete field-delete-icon"
              @click="removeField(field)"
            />
          </div>

          <!-- 文本区域 -->
          <div v-else-if="field.type === 'textarea'">
            <el-form-item :label="field.label" :required="field.required">
              <div class="option-input-wrap">
                <el-input
                  v-model="field.value"
                  :rows="4"
                  type="textarea"
                  readonly
                  placeholder="请输入"
                />
                <i
                  class="el-icon-delete field-delete-icon"
                  @click="removeField(field)"
                />
              </div>
            </el-form-item>
          </div>
          <!-- 富文本 -->
          <div v-else-if="field.type === 'richtext'">
            <el-form-item :label="field.label" :required="field.required">
              <div class="option-input-wrap">
                <Editor
                  :key="field.key"
                  :editor-config="blurEditorFocusConfig"
                  :only-editor="false"
                  :is-read-only="true"
                  :upload-img-api="richTextTestingUploadImgApi"
                  width="290px"
                  height="200px"
                />
                <i
                  class="el-icon-delete field-delete-icon"
                  @click="removeField(field)"
                />
              </div>
            </el-form-item>
          </div>

          <!-- 其他自定义字段 -->
          <el-form-item v-else :label="field.label" :required="field.required">
            <el-input
              v-model="field.value"
              placeholder="请输入"
            />
          </el-form-item>
        </div>

        <!-- 添加字段按钮 -->
        <div class="add-field">
          <el-button icon="el-icon-plus" @click="showAddFieldDialog">添加字段</el-button>
        </div>
      </el-form>

      <div class="dialog-footer">
        <el-button type="text" @click="cancel">取消</el-button>
        <el-button type="primary" @click="confirm">确定</el-button>
      </div>
    </div>

    <!-- 添加字段弹窗 -->
    <el-dialog
      :visible.sync="addFieldVisible"
      :destroy-on-close="false"
      title="添加字段"
      width="520px"
      append-to-body
      @close="addFieldCancel"
    >
      <div class="dialog-wrap">
        <el-form ref="fieldForm" :model="newField" :rules="fieldRules" label-width="100px">
          <el-form-item label="字段类型" prop="type">
            <el-select v-model="newField.type" placeholder="请选择字段类型" style="width: 100%">
              <el-option label="输入框" value="textarea" />
              <el-option label="下拉框" value="select" />
              <el-option label="富文本" value="richtext" />
            </el-select>
          </el-form-item>
          <el-form-item label="字段名称" prop="label">
            <el-input v-model="newField.label" :maxlength="64" placeholder="请输入" />
          </el-form-item>
          <el-form-item label="字段键名" prop="key">
            <el-input
              v-model="newField.key"
              :maxlength="64"
              placeholder="请输入"
              @input="validateKeyName"
            />
          </el-form-item>
        </el-form>
        <div class="dialog-footer">
          <el-button type="text" @click="addFieldCancel">取消</el-button>
          <el-button type="primary" @click="addField">确定</el-button>
        </div>
      </div>
    </el-dialog>
  </el-dialog>
</template>

<script>
import { dynamicFormCreate, getByModelByKey } from '@/api/testing/testCase.js'
import { richTextTestingUploadImgApi } from '@/components/testing/utils/config.js'
import Editor from '@/packages/editor/index.vue'

export default {
  name: 'ExecuteConfig',
  components: {
    Editor
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      richTextTestingUploadImgApi,
      blurEditorFocusConfig: {
        placeholder: '请输入',
        autoFocus: false
      },
      dialogVisible: false,
      loading: false,
      configType: 'case_function_test',
      executeConfigList: [
        {
          label: '功能类型',
          value: 'case_function_test'
        },
        {
          label: '性能类型',
          value: 'case_behavior_test'
        },
        {
          label: '安全类型',
          value: 'case_security_test'
        }
      ],
      configData: {
        case_function_test: [],
        case_security_test: [],
        case_behavior_test: []
      },
      addFieldVisible: false,
      newField: {
        type: '',
        label: '',
        key: ''
      },
      fieldRules: {
        type: [
          { required: true, message: '必填项', trigger: ['blur', 'change'] }
        ],
        label: [
          { required: true, message: '必填项', trigger: ['blur', 'change'] },
          { min: 1, max: 64, message: '1-64个字符', trigger: ['blur', 'change'] }
        ],
        key: [
          { required: true, message: '必填项', trigger: ['blur', 'change'] },
          { min: 1, max: 64, message: '1-64个字符', trigger: ['blur', 'change'] },
          { pattern: /^[a-zA-Z]+$/, message: '只能输入英文', trigger: ['blur', 'change'] }
        ]
      },
      // 是否已初始化
      isInitialized: false
    }
  },
  computed: {
    currentConfigFields() {
      return this.configData[this.configType] || []
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
      if (val) {
        this.initializeComponent()
      }
    },
    dialogVisible(val) {
      if (val !== this.visible) {
        this.$emit('update:visible', val)
      }
    },
    configType: {
      handler(newVal, oldVal) {
        // 如果已经初始化且存在旧值，则在切换前保存当前类型的表单
        if (this.isInitialized && oldVal) {
          this.saveCurrentConfig(oldVal)
        }
      }
    }
  },
  created() {
    // 初始化默认配置数据
    this.initDefaultConfigData()
  },
  methods: {
    // 初始化组件
    initializeComponent() {
      this.loading = true
      // 重置所有配置数据
      this.resetConfigData()
      // 获取配置数据
      this.fetchConfigData(this.configType)
    },

    // 重置配置数据
    resetConfigData() {
      this.executeConfigList.forEach(item => {
        this.configData[item.value] = []
      })
    },

    // 获取配置数据
    fetchConfigData(configType) {
      this.loading = true
      getByModelByKey(configType)
        .then(res => {
          if (res && res.code === 0 && res.data && res.data.template) {
            try {
              const templateData = JSON.parse(res.data.template)
              if (Array.isArray(templateData) && templateData.length > 0) {
                this.configData[configType] = templateData
              } else {
                // 如果接口返回的数据为空数组，则使用默认配置
                this.useDefaultConfig(configType)
              }
            } catch (error) {
              console.error('解析模板数据失败:', error)
              this.useDefaultConfig(configType)
            }
          } else {
            // 如果接口返回的数据不符合要求，则使用默认配置
            this.useDefaultConfig(configType)
          }
        })
        .catch(error => {
          console.error('获取配置数据失败:', error)
          this.useDefaultConfig(configType)
        })
        .finally(() => {
          this.loading = false
          this.isInitialized = true
        })
    },

    // 使用默认配置
    useDefaultConfig(configType) {
      // 使用默认配置
      this.configData[configType] = this.getDefaultConfig(configType)
    },

    // 配置类型切换处理
    handleConfigTypeChange(value) {
      if (this.configData[value].length === 0) {
        // 只有当该类型没有数据时才请求
        this.fetchConfigData(value)
      }
    },

    // 初始化默认配置数据
    initDefaultConfigData() {
      // 初始化所有类型的默认配置
      this.executeConfigList.forEach(item => {
        this.configData[item.value] = this.getDefaultConfig(item.value)
      })
    },

    // 获取默认配置
    getDefaultConfig(configType) {
      const defaultConfigs = {
        case_function_test: [
          {
            uuid: 'execResult',
            key: 'execResult',
            label: '执行结果',
            value: '',
            type: 'select',
            defaultValue: '',
            section: '',
            required: true,
            change: false,
            options: [
              { value: '', label: '' },
              { value: '', label: '' }
            ],
            link: [],
            show: true,
            validates: []
          },
          {
            uuid: 'resultDes',
            key: 'resultDes',
            value: '',
            label: '结果说明',
            type: 'textarea',
            defaultValue: '',
            section: '',
            required: false,
            show: true
          }
        ],
        case_security_test: [
          {
            uuid: 'execResult',
            key: 'execResult',
            label: '执行结果',
            value: '',
            type: 'select',
            defaultValue: '',
            section: '',
            required: true,
            change: false,
            options: [
              { value: '', label: '' },
              { value: '', label: '' }
            ],
            link: [],
            show: true,
            validates: []
          },
          {
            uuid: 'case_security_testAdvice',
            key: 'case_security_testAdvice',
            value: '',
            label: '安全建议',
            type: 'textarea',
            defaultValue: '',
            section: '',
            required: false,
            show: true
          }
        ],
        case_behavior_test: [
          {
            uuid: 'execResult',
            key: 'execResult',
            label: '执行结果',
            value: '',
            type: 'select',
            defaultValue: '',
            section: '',
            required: true,
            change: false,
            options: [
              { value: '', label: '' },
              { value: '', label: '' }
            ],
            link: [],
            show: true,
            validates: []
          },
          {
            uuid: 'resultDes',
            key: 'resultDes',
            value: '',
            label: '结果说明',
            type: 'textarea',
            defaultValue: '',
            section: '',
            required: false,
            show: true
          }
        ]
      }

      return defaultConfigs[configType] || []
    },

    // 添加字段弹窗关闭
    addFieldCancel() {
      this.$refs.fieldForm.clearValidate()
      this.addFieldVisible = false
      this.newField = {
        type: '',
        label: '',
        key: ''
      }
    },

    // 添加选项
    addOption(field) {
      if (!field.options) {
        field.options = [
          { value: '', label: '' },
          { value: '', label: '' }
        ]
      }
      const option = { value: '', label: '' }
      field.options.push(option)
      // 强制更新视图
      this.$forceUpdate()
    },

    // 删除选项
    removeOption(field, index) {
      if (index >= 2) {
        field.options.splice(index, 1)
        // 强制更新视图
        this.$forceUpdate()
      }
    },

    // 验证选项
    validateOption(field, index) {
      const option = field.options[index]
      if (!option) return

      if (option.value && option.value.length > 64) {
        option.value = option.value.substring(0, 64)
      }

      // 同步value和label
      option.label = option.value

      // 检查选项是否重复
      const currentValue = option.value
      if (currentValue) {
        const duplicateIndex = field.options.findIndex((item, i) => item.value === currentValue && i !== index)
        if (duplicateIndex !== -1) {
          this.$message.warning('选项不能重复')
          option.value = ''
          option.label = ''
          return
        }
      }
    },

    // 显示添加字段弹窗
    showAddFieldDialog() {
      this.newField = {
        type: '',
        label: '',
        key: ''
      }
      this.addFieldVisible = true
      this.$nextTick(() => {
        this.$refs.fieldForm.clearValidate()
      })
    },

    // 验证键名
    validateKeyName() {
      if (this.newField.key) {
        this.newField.key = this.newField.key.replace(/[^a-zA-Z]+/g, '')
      }
    },

    // 添加字段
    addField() {
      this.$refs.fieldForm.validate(valid => {
        if (valid) {
          // 检查字段名称和键名是否重复
          const isDuplicateLabel = this.currentConfigFields.some(field => field.label === this.newField.label)
          const isDuplicateKey = this.currentConfigFields.some(field => field.key === this.newField.key)

          if (isDuplicateLabel) {
            this.$message.warning('字段名称已存在')
            return
          }

          if (isDuplicateKey) {
            this.$message.warning('字段键名已存在')
            return
          }

          const newField = {
            uuid: this.newField.key,
            key: this.newField.key,
            label: this.newField.label,
            type: this.newField.type,
            value: '',
            defaultValue: '',
            section: '',
            required: false,
            show: true
          }

          if (this.newField.type === 'select') {
            newField.options = [
              { value: '', label: '' },
              { value: '', label: '' }
            ]
            newField.link = []
            newField.change = false
            newField.validates = []
          }

          this.configData[this.configType].push(newField)
          this.addFieldVisible = false

          // 强制更新视图
          this.$forceUpdate()
        }
      })
    },

    // 处理关闭
    handleClose() {
      this.cancel()
    },

    // 取消
    cancel() {
      this.dialogVisible = false
      this.$emit('cancel')
    },

    // 确定
    confirm() {
      // 保存当前配置类型的表单
      this.saveCurrentConfigWithLoading(this.configType, true)
    },

    // 带加载状态的保存配置
    saveCurrentConfigWithLoading(configType, shouldClose = false) {
      // 验证必填项
      let valid = true

      // 验证执行结果选项
      for (const field of this.configData[configType]) {
        if (field.required && field.type === 'select') {
          // 检查是否至少有两个选项且不为空
          if (!field.options || field.options.length < 2) {
            this.$message.error(`${field.label}至少需要两个选项`)
            valid = false
            break
          }

          // 检查每个选项是否都有值
          for (let i = 0; i < field.options.length; i++) {
            if (!field.options[i].value) {
              this.$message.error(`${field.label}的选项${i + 1}不能为空`)
              valid = false
              break
            }
          }

          if (!valid) break
        }

        if (field.required && field.type !== 'select') {
          if (field.type === 'richtext') {
            // 富文本内容可能包含HTML标签，需要特殊处理
            if (!field.value || field.value === '<p><br></p>' || field.value === '<p></p>') {
              this.$message.error(`${field.label}不能为空`)
              valid = false
              break
            }
          } else if (!field.value) {
            this.$message.error(`${field.label}不能为空`)
            valid = false
            break
          }
        }
      }

      if (valid) {
        // 处理所有select类型字段的选项，确保label和value一致
        const processedData = this.configData[configType].map(field => {
          const processedField = { ...field }
          if (field.type === 'select') {
            processedField.options = field.options.map(option => ({
              label: option.value,
              value: option.value
            }))
          }
          return processedField
        })

        const params = {
          modelKey: configType,
          templateRecordBOList: processedData
        }

        this.loading = true
        dynamicFormCreate(params)
          .then(res => {
            if ([0, 200].includes(res.code)) {
              this.$message.success('操作成功')
              if (shouldClose) {
                this.cancel()
              }
            } else {
              this.$message.warning(res.msg || '操作失败')
            }
          })
          .catch(error => {
            console.error('保存配置失败:', error)
          })
          .finally(() => {
            this.loading = false
          })
      }
    },

    // 删除字段
    removeField(field) {
      const fields = this.configData[this.configType]
      const idx = fields.indexOf(field)
      if (idx !== -1) {
        fields.splice(idx, 1)
        this.$forceUpdate()
      }
    },

    // 保存当前配置（静默方式，不显示错误提示）
    saveCurrentConfig(configType) {
      // 验证必填项
      let valid = true

      // 验证执行结果选项
      for (const field of this.configData[configType]) {
        if (field.required && field.type === 'select') {
          // 检查是否至少有两个选项且不为空
          if (!field.options || field.options.length < 2) {
            console.log(`${field.label}至少需要两个选项，当前配置未保存`)
            valid = false
            break
          }

          // 检查每个选项是否都有值
          for (let i = 0; i < field.options.length; i++) {
            if (!field.options[i].value) {
              console.log(`${field.label}的选项${i + 1}不能为空，当前配置未保存`)
              valid = false
              break
            }
          }

          if (!valid) break
        }

        if (field.required && field.type !== 'select') {
          if (field.type === 'richtext') {
            // 富文本内容可能包含HTML标签，需要特殊处理
            if (!field.value || field.value === '<p><br></p>' || field.value === '<p></p>') {
              console.log(`${field.label}不能为空，当前配置未保存`)
              valid = false
              break
            }
          } else if (!field.value) {
            console.log(`${field.label}不能为空，当前配置未保存`)
            valid = false
            break
          }
        }
      }

      if (valid) {
        // 处理所有select类型字段的选项，确保label和value一致
        const processedData = this.configData[configType].map(field => {
          const processedField = { ...field }
          if (field.type === 'select') {
            processedField.options = field.options.map(option => ({
              label: option.value,
              value: option.value
            }))
          }
          return processedField
        })

        const params = {
          modelKey: configType,
          templateRecordBOList: processedData
        }

        dynamicFormCreate(params)
          .then(res => {
          })
          .catch(error => {
            console.error('保存配置失败:', error)
          })
      }
    },

    // 获取配置类型名称
    getConfigTypeName(configType) {
      const config = this.executeConfigList.find(item => item.value === configType)
      return config ? config.label : '未知类型'
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-radio-group.common-radio-group {
  border-radius: 10px;
  border: none;
  padding: 2px;
  background-color: #F5F6F9;
  .el-radio-button {
    width: 90px;
    .el-radio-button__inner {
      width: 100%;
      background-color: #F5F6F9;
      border: none;
      border-radius: 8px;
      font-size: 14px;
      font-weight: bold;
      color: var(--neutral-700);
    }
  }
  .is-active {
    .el-radio-button__inner {
      background-color: #fff;
      color: var(--color-600);
      border-radius: 8px;
      font-size: 14px;
      border: none;
      font-weight: bold;
    }
  }
  .el-radio-button__orig-radio:checked + .el-radio-button__inner {
    box-shadow: none;
    -webkit-box-shadow: none;
  }
}
.dialog-wrap {
  .config-form {
    width: 450px;
    margin: 20px 0;
    .form-item {
      padding: 10px 20px;
      padding-right: 10px;
    }
    .form-item-bg {
      background-color: #E7EDF4;
      border-radius: 5px;
    }

    .select-options {
      width: 100%;
      flex: 1;
      .option-item {
        margin-bottom: 8px;
      }

      .add-option {
        padding-left: 100px;
      }
    }
    .add-field {
      margin-top: 12px;
    }
  }
}

::v-deep .el-dialog__body {
  padding: 20px;
}

::v-deep .no-label-item {
  margin-left: 100px;

  .el-form-item__content {
    margin-left: 0 !important;
  }
}

::v-deep .el-form-item {
  margin-bottom: 12px;
}

.option-input-wrap {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;

  ::v-deep .el-input {
    width: 290px;
  }
  ::v-deep .el-textarea {
    width: 290px;
  }

  .el-icon-delete {
    margin-left: 10px;
    color: #606266;
    cursor: pointer;
  }
  .field-delete-icon {
    position:absolute;
    transform:(-50%,-50%);
    top:50%;
    right: -38px;
    color: var(--color-600);
    cursor: pointer;
  }
}

.select-field-wrap {
  position: relative;
  display: flex;
  align-items: center;
  .field-delete-icon {
    position:absolute;
    transform:(-50%,-50%);
    top:50%;
    right: -38px;
    color: var(--color-600);
    cursor: pointer;
  }
}

</style>
