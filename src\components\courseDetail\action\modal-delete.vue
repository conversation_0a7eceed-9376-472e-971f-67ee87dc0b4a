<template>
  <div v-loading="loading" class="dialog-wrap">
    <el-alert :closable="false" type="warning">
      <div slot="title">
        <div v-show="chapterLevel">
          <span v-if="chapterIsInScheduling">章节下的课程已被排课，不允许删除章节！</span>
          <span v-else>删除章节会连带章节中的单元和课程一并移除，请确认是否删除?</span>
        </div>
        <div v-show="unitLevel">
          <span v-if="unitIsInScheduling">单元下的课程已被排课，不允许删除单元！</span>
          <span v-else>删除单元会连带单元中的课程一并移除，请确认是否删除?</span>
        </div>
        <div v-show="courseLevel">
          <span v-if="courseIsInScheduling">该课程已被排课，不允许删除！</span>
          <span v-else>请确认是否删除课程内容?</span>
        </div>
      </div>
    </el-alert>
    <batch-template
      :data="data"
      :available-data="data"
      :view-key="data.name"
    />
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button :disabled="chapterIsInScheduling || unitIsInScheduling || courseIsInScheduling" type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>

<script>
import batchTemplate from '@/packages/batch-delete/modal-bat-template.vue'
import modalMixins from '@/packages/mixins/modal_form'
import { deleteChapterApi, deleteContentRelApi } from '@/api/teacher/index.js'
export default {
  components: {
    batchTemplate
  },
  mixins: [modalMixins],
  props: {
    data: {
      type: Array,
      default: () => {
        return []
      }
    },
    courseId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      loading: false,
      treeData: [],
      addItemId: '',
      apiType: deleteChapterApi
    }
  },
  computed: {
    chapterLevel() { // 是否为章节
      return this.data[0].type == 0
    },
    unitLevel() { // 是否为单元
      console.log(this.data[0].type)
      return this.data[0].type == 1
    },
    courseLevel() { // 是否为课程
      return this.data[0].type == 3
    },
    chapterIsInScheduling() { // 章节是否在排课中
      let flag = false
      if (this.data[0].type == 0 && this.data[0].unitList) {
        this.data[0].unitList.map((unit) => {
          unit.contentList.some((course) => {
            if (course.inScheduling == true) {
              flag = course.inScheduling
              return true
            }
          })
        })
      }
      return flag
    },
    unitIsInScheduling() { // 单元是否在排课中
      let flag = false
      if (this.data[0].type == 1 && this.data[0].contentList) {
        this.data[0].contentList.some((course) => {
          if (course.inScheduling == true) {
            flag = course.inScheduling
            return true
          }
        })
      }
      return flag
    },
    courseIsInScheduling() { // 该课程是否被排课
      return this.data[0].inScheduling && this.data[0].type == 3
    }
  },
  created() {
  },
  methods: {
    close() {
      this.$emit('close')
    },
    confirm: function() {
      this.loading = true
      let params = {}
      if (this.courseLevel) { // 删除课程内容
        params = {
          unitId: this.data[0].unitId,
          contentId: this.data[0].id,
          courseId: this.courseId
        }
        this.apiType = deleteContentRelApi
      } else { // 章节/单元
        params = {
          id: this.data[0].id,
          courseId: this.courseId
        }
        this.apiType = deleteChapterApi
      }
      this.apiType(params).then((res) => {
        if (res.code == 0) {
          this.$message.success('删除成功')
          this.$emit('call', 'refresh')
          this.close()
        }
      }).catch(() => {
        this.loading = false
      })
    }
  }
}
</script>
