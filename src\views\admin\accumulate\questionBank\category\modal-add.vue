<template>
  <div v-loading="loading" class="dialog-wrap">
    <el-form ref="form" :model="formData" :rules="rules" label-position="left" label-width="80px" @submit.native.prevent>
      <el-form-item label="名称" prop="categoryName">
        <el-input v-model.trim="formData.categoryName"/>
      </el-form-item>
    </el-form>
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>
<script>
import module from './config.js'
import validate from '@/packages/validate'
import { addCategory } from '@/api/accumulate/category'
export default {
  props: {
    categoryType: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      moduleName: module.name,
      loading: false,
      validate: validate,
      formData: {
        categoryName: ''
      },
      rules: {
        categoryName: [validate.required(), validate.base_name]
      }
    }
  },
  methods: {
    close: function() {
      this.$emit('close')
    },
    confirm: function() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.loading = true
          const postData = JSON.parse(JSON.stringify(this.formData))
          postData['categoryType'] = this.categoryType
          addCategory(postData).then(res => {
            this.$message.success('新增分类成功')
            this.close()
          }).finally(() => {
            // 都完成之后（无论成功失败），才去刷新列表
            this.loading = false
            this.$bus.$emit(this.moduleName + '_module', 'reload')
          })
        }
      })
    }
  }
}
</script>
