import request from '../../request'
const _thisApi = window.NFVO_CONFIG.nfvo_api

export function sceneTypeInitRole(data) {
  return request({
    url: '/scene/sceneTypeInitRole/queryPage',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

export function sceneTypeInitRoleInstance(data) {
  return request({
    url: '/scene/sceneTypeInitRoleInstance/queryPage',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

export function updateSceneTypeInitRoleAPI(data) {
  return request({
    url: '/scene/sceneTypeInitRole/update',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

export function createSceneRoleAPI(data) {
  return request({
    url: '/scene/sceneRole/create',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

export function updateSceneRoleAPI(data) {
  return request({
    url: '/scene/sceneRole/update',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

export function removeSceneRoleAPI(data) {
  return request({
    url: '/scene/sceneRole/remove',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

export function createSceneRoleResourceRelAPI(data) {
  return request({
    url: '/scene/sceneRoleResourceRel/create',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

export function removeByNodeIdListAPI(data) {
  return request({
    url: '/scene/sceneRoleResourceRel/removeByNodeIdList',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    data: data
  })
}

export function getPort(params) {
  return request({
    url: _thisApi + '/networkelement/ports',
    method: 'get',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    params
  })
}
