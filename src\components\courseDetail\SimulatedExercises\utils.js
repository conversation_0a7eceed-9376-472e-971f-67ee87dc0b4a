// 往富文本插入节点内容
export const insertNodeInRichText = (richText = '<p></p>', node = '') => {
  if (typeof node === 'string') {
    node = document.createTextNode(node)
  }
  let newRichText = ''
  const tempEl = document.createElement('div')
  tempEl.setAttribute('class', 'wrapper')
  tempEl.innerHTML = richText
  const p = tempEl.querySelector('.wrapper>p')
  if (p) {
    p.appendChild(node)
    newRichText = tempEl.innerHTML
  } else {
    const p2 = document.createElement('p')
    p2.innerHTML = richText
    const tempEl2 = document.createElement('div')
    tempEl2.setAttribute('class', 'wrapper')
    tempEl2.appendChild(p2)
    const p = tempEl2.querySelector('.wrapper>p')
    p.appendChild(node)
    newRichText = tempEl2.innerHTML
  }

  return newRichText
}
