<template>
  <div class="category-wrap">
    <transverse-list
      :data="questionLevelList"
      :allow-deletion="false"
      :allow-add="false"
      :all="true"
      :is-show-expand="false"
      v-bind="categoryProps"
      title="难度"
      style="border: none; padding-left: 0px;"
      @node-click="handleNodeClick($event, 'complexity')"
    />
    <transverse-list
      :data="questionTypeList"
      :allow-deletion="false"
      :allow-add="false"
      :all="true"
      :is-show-expand="false"
      v-bind="categoryProps"
      title="题型"
      class="mb-10"
      style="padding-left: 0px;"
      @node-click="handleNodeClick($event, 'questionType')"
    />
  </div>
</template>
<script>
import transverseList from '@/packages/transverse-list/index.vue'
import { questionTypeArr, questionTypeSimulationArr } from './constants'

export default {
  components: {
    transverseList
  },
  props: {
    contentType: {
      type: [String, Number]
    }
  },
  data() {
    return {
      questionTypeList: questionTypeArr,
      questionLevelList: [
        { label: '初级', value: '1' },
        { label: '中级', value: '2' },
        { label: '高级', value: '3' }
      ],
      categoryProps: {
        label: 'label',
        idName: 'value'
      },
      categoryQuery: {
        complexity: null,
        questionType: null
      }
    }
  },
  mounted() {
    if (this.contentType == 1) {
      this.questionTypeList = questionTypeArr
    } else if (this.contentType == 2) {
      this.questionTypeList = questionTypeSimulationArr
    }
  },
  methods: {
    handleNodeClick(item, key) {
      this.categoryQuery[key] = +item.value
      this.$emit('category-query', this.categoryQuery)
    }
  }
}
</script>
