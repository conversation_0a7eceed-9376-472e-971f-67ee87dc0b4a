import request from '@/packages/request'
const _thisApi = window.NFVO_CONFIG.nfvo_api
const headers = { 'Content-Type': 'application/json', 'x-access-module': 'ADMIN' }

export function getListApi(params) {
  return request({
    url: _thisApi + '/image/container',
    method: 'get',
    params,
    headers
  })
}

export function createImage(data) {
  return request({
    url: _thisApi + '/image/container',
    method: 'post',
    data,
    headers
  })
}

export function editImage(id, data) {
  return request({
    url: _thisApi + '/image/container/' + id,
    method: 'PUT',
    data,
    headers
  })
}

export function deleteImage(id) {
  return request({
    url: _thisApi + '/image/container/' + id,
    method: 'DELETE',
    headers
  })
}

export function upload() {
  return _thisApi + '/image/container'
}
