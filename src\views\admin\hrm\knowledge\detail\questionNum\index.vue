<template>
  <page-table
    ref="table"
    :default-selected-arr="defaultSelectedArr"
    default-selected-key="knowledgeCode"
    @refresh="refresh"
    @link-event="linkEvent"
    @on-select="tabelSelect"
    @on-current="tabelCurrent"
  />
</template>

<script>
import moduleConf from './config'
import pageTable from './table/index.vue'
export default {
  name: moduleConf.name,
  components: {
    pageTable
  },
  data() {
    return {
      moduleName: moduleConf.name,
      selectItem: [],
      defaultSelectedArr: []
    }
  },
  methods: {
    // 列表点击
    linkEvent: function({ name, row, params }) {
      this.$router.push({ name: name, query: params })
    },
    // 返回已选
    tabelSelect: function(data) {
      this.selectItem = data
    },
    // 返回单选
    tabelCurrent: function(row) {
      this.selectItem = [row]
    },
    // action menu 事件
    actionHandler: function(type, data) {
      switch (type) {
        case 'refresh':
          this.$refs['table'].getList(false)
      }
    },
    refresh: function() {}
  }
}
</script>
