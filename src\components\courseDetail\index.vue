<template>
  <div class="_topo_body_ flex">
    <!-- 随堂练习的考试模式下-开始考试 -->
    <div v-if="contentInfo.isExamMode == 1 && isAnswerStatus == 1" class="mask">
      <el-button type="primary">正在考试中</el-button>
    </div>
    <div v-loading="loading" class="_c_middle_sidebar">
      <Video v-if="viewName == 'video' && id" :id="id"/>
      <report v-if="viewName == 'document' && id" :id="id" :is-download="false" show-package/>
      <liveTelecastUrl v-if="viewName == 'liveTelecast'" :content-name="showObj.name" :live-url="liveUrl" :role="roleType" :live-status="liveStatus" :scheduling-code="schedulingCode" type="查看"/>
      <div v-if="viewName != 'liveTelecast' && viewName != 'video' && viewName != 'document'" style="height:100%;width:100%;">
        <div v-if="viewName == 'experiment'" class="experiment-tip">
          <i class="cr-icon-diannao" />
          <div>请点击右侧“实验”，可进行课程实验</div>
        </div>
        <div v-else-if="(showObj.isHavePractice && roleType == 2) || (showObj.isHavePractice && editMode == 1)" class="experiment-tip">
          <i class="cr-icon-ceshi" />
          <div>请点击右侧“考试”，可进行课程考试</div>
        </div>
        <div v-else-if="showObj.isHavePackage" class="experiment-tip">
          <i class="cr-icon-fujian" />
          <div>请点击右侧“附件”，可下载课程附件</div>
        </div>
        <el-empty
          v-else
          :image="img"
          :image-size="110"
          style="margin: 100px auto"
          description="暂无数据"
        />
      </div>
    </div>
    <div class="details-list">
      <div class="list-left">
        <div v-if="showLiveTelecast" :class="{'is-active': viewName == 'liveTelecast'}" class="switch-tags" @click="changeView('liveTelecast')">
          <i class="cr-icon-zhibozhibo" />直播
        </div>
        <div v-if="showObj.isHaveVideo" :class="{'is-active': viewName == 'video', 'is-disabled' : liveStatus == 1 && roleType == 2}" class="switch-tags" @click="changeView('video')">
          <i class="cr-icon-shipin" />视频
        </div>
        <div v-if="showObj.isHaveCourseware" :class="{'is-active': viewName == 'document', 'is-disabled' : liveStatus == 1 && roleType == 2}" class="switch-tags" @click="changeView('document')">
          <i class="cr-icon-wendang" />文档
        </div>
        <div v-if="showExperiment" :class="{'is-active': viewName == 'experiment', 'is-disabled' : liveStatus == 1 && roleType == 2}" class="switch-tags" @click="changeView('experiment')">
          <i class="cr-icon-diannao" />实验
        </div>
        <div v-if="(showObj.isHavePractice && roleType == 2) || (showObj.isHavePractice && editMode == 1)" :class="{'is-active': viewName == 'examination', 'is-disabled' : (liveStatus == 1 && roleType == 2)}" class="switch-tags" @click="changeView('examination')">
          <i class="cr-icon-ceshi" />考试
        </div>
        <div v-if="showObj.isHavePackage" :class="{'is-active': viewName == 'annex', 'is-disabled' : liveStatus == 1 && roleType == 2}" class="switch-tags" @click="changeView('annex')">
          <i class="cr-icon-fujian" />附件
        </div>
      </div>
      <div class="list-right">
        <div class="course-name">{{ courseInfo.name }}</div>
        <div v-for="(item, index) in courseInfo.chapterUnitList" :key="index">
          <div :class="{'active': chapterIdNew == item.id}" class="title" >
            第{{ index + 1 }}章 | {{ item.name }}
          </div>
          <div v-for="(unit, unitIndex) in item.unitList" :key="unit.id" class="c_muli_info">
            <div :class="{'active': unitIdNew == unit.id}" class="c_mulu_content" >
              第{{ unitIndex + 1 }}单元 | {{ unit.name }}
            </div>
            <div v-for="(content, contentIndex) in unit.contentList" :key="content.id" :class="{'is-active': content.id == id}" class="content" @click="courseChange(item.id, unit.id, content.id, content)">
              <div>
                <div class="mr-5">{{ index + 1 }}.{{ unitIndex + 1 }}.{{ contentIndex + 1 }}</div>
                <div>{{ content.name }}</div>
              </div>
              <div>
                {{ content.contentPeriod }}课时
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 中部弹窗 start-->
    <el-dialog
      :title="titleMapping[modalName]"
      :visible.sync="modalShow"
      :width="modalWidth"
      :destroy-on-close="true"
      append-to-body
      @close="modalClose"
    >
      <transition name="el-fade-in-linear">
        <component
          :is="modalName"
          :id="id"
          :name="modalName"
          :data="selectItem"
          @close="modalClose"
          @call="confirmCall"
        />
      </transition>
    </el-dialog>
    <!-- 中部弹窗 end-->
  </div>
</template>

<script>
import Video from './Video'
import report from './report'
import annex from './annex'
import liveTelecastUrl from '@/components/liveTelecastUrl'
import { queryLive, queryPracticeUser, getCourseInfo, getContentInfo } from '@/api/teacher/index'

import mixinsActionMenu from '@/packages/mixins/action_menu.js'
import { mapState } from 'vuex'
import { tabManager } from '@/packages/utils/tabManager.js'

export default {
  components: {
    Video,
    report,
    liveTelecastUrl,
    annex
  },
  mixins: [mixinsActionMenu],
  props: {
    oneLevelName: String,
    // 自学：1
    type: [String, Number],
    roleType: [String, Number],
    chapterId: [String, Number],
    unitId: [String, Number],
    contentId: [String, Number],
    content: Object,
    editMode: {
      type: [String, Number],
      default: 0
    },
    refresh: Boolean
  },
  data() {
    return {
      userInfo: {},
      img: require('@/assets/empty_state.png'),
      viewName: 0,
      practiceIndex: '1',
      requiredCourse: this.oneLevelName == 'requiredCourse', // 是必修课
      loading: false,
      practiceList: [],
      studentAnswerList: [],
      id: '',
      showObj: {},
      courseId: this.$route.query.courseId, // 课程id
      schedulingCode: this.$route.query.schedulingCode, // 排课id
      courseInfo: {}, // 课程信息
      contentInfo: {}, // 选择的课程内容信息
      liveUrl: '', // 直播地址
      liveStatus: 0, // 直播是否开启
      isAnswerStatus: 0, // 随堂练习-考试模式-是否开始考试
      chapterIdNew: '', // 选中的章节
      unitIdNew: '', // 选中的单元
      titleMapping: {
        'annex': '下载附件'
      }
    }
  },
  provide() {
    return {
      'parentVm': this
    }
  },
  computed: {
    ...mapState('websocketListener', [
      'training_liveStatus_Socket', // 教师开始关闭直播
      'training_isAnswerStatus_Socket' // 实训-随堂练习-考试模式下-是否开始考试
    ]),
    showQuestionBank() {
      let flag = false
      if (this.oneLevelName == 'selfStudyCourses') {
        flag = this.viewName == 2 && this.studentAnswerList.length > 0
      } else {
        flag = this.viewName == 2 && this.oneLevelName == 'requiredCourse'
      }
      return flag
    },
    // 显示直播
    showLiveTelecast() {
      return this.courseInfo.isRequiredCourse && !(this.roleType == 3) // 必修课且不是助教
    },
    // 显示实验
    showExperiment() {
      return this.showObj.contentType == 2 || (this.editMode == 1 && this.showObj.contentType == 2)
    },
    // 动态试卷暂不支持教师/助教查看题目详情
    cannotDynamicPaper() {
      return this.contentInfo.examType == 1 && this.roleType != '2'
    },
    showExercisesPaper() {
      return this.viewName == 2 && (this.oneLevelName != 'requiredCourse' && this.studentAnswerList.length == 0)
    }
  },
  watch: {
    contentId: {
      handler() {
        this.courseChange(this.chapterId, this.unitId, this.contentId, this.content)
      }
    },
    // 教师开始关闭直播
    'training_liveStatus_Socket': function(newVal, oldVal) {
      if (newVal && newVal.contentId == this.id) {
        this.liveStatus = newVal.liveStatus
      }
    },
    // 实训-随堂练习-考试模式下-是否开始考试
    'training_isAnswerStatus_Socket': function(newVal, oldVal) {
      if (newVal) {
        this.isAnswerStatus = newVal.isAnswerStatus
      }
    },
    refresh: {
      handler() {
        this.getCourseInfo()
      }
    }
  },
  mounted() {
    this.userInfo = JSON.parse(localStorage.getItem('loginUserInfo'))
    this.getCourseInfo()
    if (this.$route.query.practiceIndex) {
      this.practiceIndex = this.$route.query.practiceIndex
    }
  },
  methods: {
    // 设置viewName
    setViewName() {
      if (this.showLiveTelecast) {
        this.viewName = 'liveTelecast'
      } else if (this.showObj.isHaveVideo) {
        this.viewName = 'video'
      } else if (this.showObj.isHaveCourseware) {
        this.viewName = 'document'
      } else if (this.showExperiment) {
        this.viewName = 'experiment'
      } else if (this.showObj.isHavePractice) {
        this.viewName = 'examination'
      } else if (this.showObj.isHavePackage) {
        this.viewName = 'annex'
      }
    },
    // 获取课程内容信息
    getContentInfo() {
      let num = 0
      if (this.courseInfo.isRequiredCourse) {
        num = 1
      } else if (this.courseInfo.isElectiveCourse) {
        num = 2
      } else {
        num = 3
      }
      if (!this.id) {
        return
      }
      const postData = {
        courseId: this.$route.query.courseId,
        contentId: this.id,
        roleType: this.roleType,
        requiredOrElective: num,
        schedulingCode: this.schedulingCode,
        sectionTime: this.$route.query.sectionTime,
        sectionSeason: this.$route.query.sectionSeason
      }
      getContentInfo(postData).then(res => {
        // 查看拓扑所需要用到的参数
        this.contentInfo = res.data
        this.schedulingCode = res.data.schedulingCode
        this.liveStatus = res.data.liveStatus
        if (this.courseInfo.isRequiredCourse) {
          this.queryLive()
        }
      })

      // 设置viewName
      this.setViewName()
    },
    // 随堂练习-考试模式-获取是否开始考试
    getCourseInfo() {
      const postData = {
        courseId: this.$route.query.courseId,
        roleType: this.roleType,
        schedulingCode: this.schedulingCode,
        sectionTime: this.$route.query.sectionTime,
        sectionSeason: this.$route.query.sectionSeason,
        editMode: this.editMode
      }
      getCourseInfo(postData).then(res => {
        this.courseInfo = res.data
        this.id = ''
        if (this.$route.query.contentId) {
          this.courseInfo.chapterUnitList.forEach(item => {
            item.unitList.forEach(unit => {
              // 课程为空时
              if (unit.contentList.length === 0) {
                this.showObj = {}
              } else {
                unit.contentList.forEach(content => {
                  if (this.$route.query.contentId == content.id) {
                    this.chapterIdNew = item.id
                    this.unitIdNew = unit.id
                    this.id = content.id
                    this.showObj = content
                    this.schedulingCode = content.schedulingCode
                  }
                })
              }
            })
          })
        } else {
          this.courseInfo.chapterUnitList.forEach(item => {
            item.unitList.forEach(unit => {
              // 课程为空时
              if (unit.contentList.length === 0) {
                this.showObj = {}
              } else {
                unit.contentList.forEach(content => {
                  if (content && !this.id) {
                    this.chapterIdNew = item.id
                    this.unitIdNew = unit.id
                    this.id = content.id
                    this.schedulingCode = content.schedulingCode
                    this.showObj = content
                  }
                })
              }
            })
          })
        }
        this.getContentInfo()
      })
    },
    courseChange(chapterId, unitId, contentId, content) {
      if (contentId == this.id) {
        return
      }
      this.chapterIdNew = chapterId
      this.unitIdNew = unitId
      this.id = contentId
      this.showObj = content
      this.schedulingCode = content.schedulingCode
      this.$emit('switch-courses', chapterId, unitId, contentId, content)
      this.getPracticeList()
      this.getContentInfo()
    },
    // getStudentAnswer() {
    //   const { courseId, contentId } = this.$route.query
    //   if (!courseId) {
    //     return
    //   }
    //   if (this.schedulingCode) {
    //     studentAnswer({ schedulingCode: this.schedulingCode }).then((res) => {
    //       if (res.code == 0) {
    //         this.studentAnswerList = res.data || []
    //         this.practiceList.forEach((k) => {
    //           const data = this.studentAnswerList.find(j => j.questionCode == k.questionCode)
    //           if (data) {
    //             this.$set(k, 'questionStudentScore', data.questionScore)
    //             this.$set(k, 'questionUserAnswer', data.questionUserAnswer)
    //           }
    //         })
    //       }
    //     })
    //   } else {
    //     studentSelfStudyAnswer({ courseId, contentId: contentId || this.id }).then((res) => {
    //       if (res.code == 0) {
    //         this.studentAnswerList = res.data || []
    //         this.practiceList.forEach((k) => {
    //           const data = this.studentAnswerList.find(j => j.questionCode == k.questionCode)
    //           if (data) {
    //             this.$set(k, 'questionStudentScore', data.questionScore)
    //             this.$set(k, 'questionUserAnswer', data.questionUserAnswer)
    //           }
    //         })
    //       }
    //     })
    //   }
    // },
    getPracticeList() {
      queryPracticeUser({ contentId: this.id || this.$route.query.id, courseCode: this.$route.query.courseId }).then(res => {
        if (res.code == 0) {
          this.practiceList = res.data || []
          if (!this.requiredCourse) {
            // this.getStudentAnswer()
          }
        }
      })
    },
    // 获取直播地址
    queryLive() {
      if (!this.schedulingCode) {
        return
      }
      queryLive({ roleType: this.roleType, schedulingCode: this.schedulingCode }).then(res => {
        if (res.code === 0 && res.data.length > 0) {
          this.liveUrl = res.data
        }
      })
    },
    // 视图切换
    changeView(name) {
      // 直播开始后不允许切换
      if (this.liveStatus == 1 && this.roleType == 2) {
        return
      }
      // 随堂练习-考试模式-开始后 不让看课件和视频
      if (name == 'annex') {
        this.modalShow = true
        this.modalName = 'annex'
        return
      }
      if (name == 'examination') {
        if (this.cannotDynamicPaper) {
          window.open(`/cannotDynamicPaper`, '_blank')
          return
        }
        if (this.studentAnswerList.length) {
          this.$message({
            message: `该考试已完成`,
            type: 'error'
          })
          return
        }
        let num = 0
        if (this.courseInfo.isRequiredCourse) {
          num = 1
        } else if (this.courseInfo.isElectiveCourse) {
          num = 2
        } else {
          num = 3
        }
        window.open(`/exercisesPaper?courseId=${this.$route.query.courseId}&curriculumCode=${this.id}&oneLevelName=${this.$route.query.oneLevelName}&schedulingId=${this.schedulingCode || ''}&sign=${num}&answerTime=${this.showObj.answerTime}&isExamMode=${this.contentInfo.isExamMode}&examType=${this.contentInfo.examType != '1' ? '' : this.contentInfo.examType}`, '_blank')
        return
      }
      if (name == 'experiment') {
        // 浏览器tab页签唯一值用课程id和课程内容id拼接
        const id = `experiment-${this.courseId}-${this.showObj.id}`
        const url =
          '/experiment_topo' +
          '?id=' + this.showObj.id +
          '&courseId=' + (this.courseId) +
          '&courseName=' + (this.courseInfo.name) +
          '&name=' + (this.showObj.name) +
          '&enterType=' + (this.$route.query.enterType || 'student') +
          '&topologyAllocation=' + (this.contentInfo.topologyAllocation) +
          '&roleId=' + (this.contentInfo.roleId || '') +
          '&schedulingId=' + (this.contentInfo.schedulingCode || '') +
          '&sceneInstanceId=' + (this.editMode == 1 ? this.showObj.sourceSceneId : this.contentInfo.sceneInstanceId || '')
        const existingRef = tabManager.getTabRef(id)
        if (existingRef && !existingRef.closed) {
          // 非当前打开者链的话，就新打开一个界面，不能打开空界面
          if (existingRef.location.href === 'about:blank') {
            existingRef.location.replace(url)
            setTimeout(() => existingRef.focus(), 300)
          } else {
            existingRef.focus()
          }
        } else {
          const newTab = window.open(url, `tab-${id}`)
          tabManager.setTabRef(id, newTab)
        }
        return
      }
      this.viewName = String(name)
    },
    confirmCall: function(type, data) {
      if (type === 'close') {
        this.modalClose()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.experiment-tip {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  i {
    font-size: 80px;
    margin-bottom: 10px;
  }
}
.practice-tabs {
  margin: 0px 0 0 20px;
}
.practice-wrap {
  flex: 1;
  height: 100%;
  /deep/.paper_container {
    padding: 0;
    .paper_left .question_details .question_list {
      margin-top: 0;
    }
  }
}
.title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  // margin-bottom: 20px;
  color: #999999;
  .span-one {
    margin-right: 10px;
    cursor: pointer;
    font-family: Microsoft YaHei;
    font-weight: 400;
  }
  .span-one:hover {
    margin-right: 10px;
    color: #333333;
    cursor: pointer;
    font-family: Microsoft YaHei;
    font-weight: 400;
  }
  .span-two {
    margin: 0 10px;
    cursor: pointer;
    font-family: Microsoft YaHei;
    font-weight: 400;
  }
  .span-two:hover {
    margin: 0 10px;
    color: #333333;
    cursor: pointer;
    font-family: Microsoft YaHei;
    font-weight: 400;
  }
  .span-three {
    margin: 0 10px;
    font-family: Microsoft YaHei;
    font-weight: 400;
    color: #333333;
  }
}
._topo_body_{
  box-shadow: 0 1px 8px #bfbcbc;
  background: #FFFFFF;
  border-radius: 4px;
  margin: 20px 20px 0 20px;
  height: 100% !important;
  overflow: hidden;
  position: relative;
}
._c_middle_sidebar {
  position: relative;
  width: 70%;
  height: 100%;
  background: #fff;
}
::v-deep {
  .orchestration-create-warp{
    height: 100%;
  }
}
.details-list {
  width: 30%;
  display: flex;
  font-weight: 500;
  .list-left {
    border-left: 1px solid #ccc;
    border-right: 1px solid #ccc;
    min-width: 80px;
    height: 100%;
    background: var(--neutral-100);
    display: flex;
    align-items: center;
    flex-direction: column;
    padding: 20px 0;
    .switch-tags {
      color: var(--neutral-700);
      font-size: 14px;
      cursor: pointer;
      display: flex;
      align-items: center;
      flex-direction: column;
      margin-bottom: 20px;
      i {
        font-size: 20px;
      }
    }
    .is-active {
      color: var(--color-700);
      font-weight: 800;
      background-color: var(--color-100);
      padding: 12px 18px 10px 18px;
      border-radius: 8px;
      i {
        font-weight: 800;
      }
    }
    .is-disabled {
      cursor: not-allowed;
    }
    .switch-tags:hover {
      color: var(--color-700);
    }
    .is-disabled:hover {
      color: #535360;
    }
    .is-active:hover {
      color: var(--color-700);
    }
  }
  .list-right {
    width: 100%;
    height: 100%;
    background: #fff;
    overflow-y: auto;
    .course-name {
      margin: 0;
      font-size: 16px;
      display: flex;
      align-items: center;
      padding: 10px 20px;
    }
    >div {
      border-bottom: 1px solid #ccc;
      margin: 0 20px;
    }
    .title {
      display: flex;
      min-height: 50px;
      align-items: center;
      color: var(--neutral-700);
      font-size: 16px;
    }
    .content {
      padding: 10px 20px 10px 40px;
      margin-bottom: 2px;
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      font-size: 12px;
      color: var(--neutral-700);
      cursor: pointer;
      >div:first-child {
        flex: 1;
        margin-right: 5px;
        display: flex;
      }
    }
    .content:hover {
      color: var(--color-700);
      background-color: var(--color-50);
    }
    .is-active {
      color: var(--color-700);
      background-color: var(--color-50);
    }
    .c_muli_info {
      .c_mulu_content {
        padding-left: 20px;
        font-size: 14px;
        padding-bottom: 10px;
        padding-top: 10px;
        color: var(--neutral-700);
      }
    }
  }
}
.mask {
  height: 100%;
  width: 100%;
  position: absolute;
  z-index: 100;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  >div {
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
::v-deep .dialog-wrap .attachment-wrap {
  overflow-y: auto;
  max-height: calc(60vh - 28px);
  padding-right: 5px !important;
}
</style>
