<template>
  <div class="buttons-wrap">
    <el-button :disabled="multipleDisabled || !isQemu" type="primary" @click="clickDrop('start')" >开机</el-button>
    <el-button :disabled="multipleDisabled || !isQemu" type="primary" @click="clickDrop('stop')" >关机</el-button>
    <el-button :disabled="multipleDisabled || !isQemu" type="primary" @click="clickDrop('reboot')" >重启</el-button>
    <el-dropdown trigger="click" placement="bottom-start" @command="clickDrop">
      <el-button type="primary">
        操作 <i class="el-icon-arrow-down el-icon--right"/>
      </el-button>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item :disabled="multipleDisabled" command="modalDelete" >删除</el-dropdown-item>
        <el-dropdown-item :disabled="multipleDisabled || !isQemu" command="suspend" >挂起</el-dropdown-item>
        <el-dropdown-item :disabled="multipleDisabled || !isQemu" command="resume" >恢复</el-dropdown-item>
        <el-dropdown-item :disabled="multipleDisabled || !isQemu" command="rebuild" >重建</el-dropdown-item>
        <el-dropdown-item :disabled="multipleDisabled || !isQemu" command="pause" >暂停</el-dropdown-item>
        <el-dropdown-item :disabled="singleDisabled || !isQemu" command="snapshot" >快照</el-dropdown-item>
        <!-- <el-dropdown-item :disabled="singleDisabled || !isQemu" command="toImage" >生成镜像</el-dropdown-item> -->
      </el-dropdown-menu>
    </el-dropdown>
    <!--    中部弹窗 start-->
    <el-dialog
      :title="titleMap[modalName]"
      :width="modalWidth"
      :visible.sync="modalShow"
      append-to-body
      @close="modalClose"
    >
      <transition name="el-fade-in-linear">
        <component
          :is="modalName"
          :name="modalName"
          :data="selectItem"
          :resource-data="selectItem[0]"
          @close="modalClose"
          @call="confirmCall"
        />
      </transition>
    </el-dialog>
    <!--    中部弹窗 end-->
  </div>
</template>
<script>
import mixinsActionMenu from '../../../mixins/action_menu.js'
import start from './modal_options.vue'
import stop from './modal_options.vue'
import reboot from './modal_options.vue'
import modalDelete from './modal_options.vue'
import suspend from './modal_options.vue'
import resume from './modal_options.vue'
import rebuild from './modal_options.vue'
import pause from './modal_options.vue'
import toImage from '../../action/modal_to_image.vue'
export default {
  components: {
    start,
    stop,
    reboot,
    modalDelete,
    suspend,
    resume,
    rebuild,
    pause,
    toImage
  },
  mixins: [
    mixinsActionMenu
  ],
  data() {
    return {
      titleMap: {
        'start': '开机',
        'stop': '关机',
        'reboot': '重启',
        'modalDelete': '删除',
        'suspend': '挂起',
        'resume': '恢复',
        'rebuild': '重建',
        'pause': '暂停',
        'toImage': '生成镜像'
      }
    }
  },
  computed: {
    'isQemu': function() {
      if (this.selectItem.length) {
        const data = this.selectItem.filter(item => {
          return item.resource_type === 'vnf' && (item.virtual_type === 'qemu' || item.virtual_type === 'global_qemu')
        })
        return this.selectItem.length === data.length
      } else return false
    }
  },
  methods: {
    'clickDrop': function(name) {
      if (this.drawerAction && this.drawerAction.includes(name)) {
        this.drawerName = name
      } else {
        if (name === 'snapshot') {
          this.$emit('on-show-detail', this.selectItem[0], 'snapshot')
        } else this.modalName = name
      }
    },
    // 模态框动态组件确定回调 {type}返回类型，规范为保持与模态框名称一致，{data}返回确认数据
    'confirmCall': function(type, data) {
      if (type === 'close') {
        this.modalClose()
      }
    }
  }
}
</script>
