<template>
  <div
    :class="{'is-disabled': nodeData.disabled, 'error': nodeData.status === 'error'}"
    class="custom-node"
  >
    <span class="custom-node-icon">
      <img v-if="nodeData.icon && nodeData.icon.value && imgBase64" :src="imgBase64">
    </span>
    <strong v-if="!nodeData.configPortModal">
      <i v-if="nodeData.loading" class="el-icon-loading"/>
      <template v-else-if="nodeData.type === 'vnf' && nodeData.status">
        <i v-if="nodeData.status === 'suspended'" style="color:#f90" class="custom-status-icon el-icon-d-arrow-right" />
        <i v-else-if="nodeData.status === 'paused'" style="color:#f90" class="custom-status-icon el-icon-video-pause" />
        <i v-else-if="nodeData.status === 'running'" style="color:#19be6b;font-size:14px;" class="custom-status-icon el-icon-video-play"/>
        <i
          v-else-if="nodeData.status === 'shutoff'"
          style="color: #666; background: #666"
          class="custom-status-badge"
        />
        <i
          v-else
          :class="{'fade': getStatus(nodeData.status.toLowerCase()) === '#f90'}"
          :style="'color:' + getStatus(nodeData.status.toLowerCase()) + ';background:' + getStatus(nodeData.status.toLowerCase())"
          class="custom-status-badge"
        />
      </template>
      {{ nodeData.name }}
    </strong>
    <i v-if="!nodeData.loading && nodeData.status !== 'error'" class="custom-port-icon el-icon-circle-plus"/>
  </div>
</template>

<script>
import currentHost from '../mixins/current_host.js'
export default {
  name: 'Node',
  mixins: [currentHost],
  inject: ['getNode'],
  data() {
    return {
      nodeData: {},
      imgBase64: ''
    }
  },
  created() {
    const node = this.getNode()
    this.nodeData = node.data
    this.getBase64(this.currentApi + this.nodeData.icon.value, (base64) => {
      this.imgBase64 = base64
    })
    node.on('change:data', ({ current }) => {
      this.nodeData = current
    })
  },
  methods: {
    getBase64: function(url, callback) {
      const image = new Image()
      image.crossOrigin = 'Anonymous'
      image.src = url
      image.onload = function() {
        const canvas = document.createElement('canvas')
        canvas.width = image.width
        canvas.height = image.height
        const ctx = canvas.getContext('2d')
        ctx.drawImage(image, 0, 0, image.width, image.height)
        const base64 = canvas.toDataURL('image/png')
        callback(base64)
      }
    },
    'getStatus': function(data) {
      let color = null
      switch (data.toLowerCase()) {
        case 'pending':
          color = '#777'
          break
        case 'error':
          color = '#ed4014'
          break
        // starting、powering_on、powering_off、deleting
        default:
          color = '#f90'
          break
      }
      return color
    }
  }
}
</script>

<style lang="less" scopd>
@keyframes warning-fade {
  from { opacity: 1.0; }
  50% { opacity: 0.5; }
  to { opacity: 1.0; }
}
.custom-node {
  .el-image__error {
    width: 40px;
    height: 40px;
  }
  &.error strong {
    color: #ed4014;
  }
  &.is-disabled {
    background-color: #f7f7f7;
    cursor: not-allowed;
    strong {
      color: #c5c8ce;
    }
  }
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  cursor: pointer;
  .custom-node-icon {
    display: block;
    height: 50px;
    position: relative;
    & img {
      width: 50px;
      height: 50px;
    }
    .custom-warning-icon {
      position: absolute;
      font-size: 16px;
      right: -8px;
      top: -8px;
    }
  }
  strong {
    text-align: center;
    display: block;
    font-size: 13px;
    font-weight: 400;
    word-break: break-all;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    background: rgba(255, 255, 255, 0.6);
    i.custom-status-icon {
      font-size: 12px !important;
      width: 10px;
      vertical-align: baseline;
    }
    i.custom-status-badge {
      width: 10px;
      height: 10px;
      border: solid 2px rgba(255,255,255,.5);
      display: inline-block;
      border-radius: 50%;
      vertical-align: middle;
      position: relative;
      top: -1px;
    }
    .fade.custom-status-badge {
      -webkit-animation: warning-fade 1s infinite linear;
      animation: warning-fade 1s infinite linear;
    }
    .ivu-badge-status-text {
      display: none;
    }
  }
  .custom-port-icon {
    position: absolute;
    top: 36px;
    font-size: 16px;
    color: var(--color-600);
    background-color: #fff;
    border-radius: 50%;
    visibility: hidden;
  }
}
</style>
