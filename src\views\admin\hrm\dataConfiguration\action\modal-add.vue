<template>
  <div v-loading="loading" class="dialog-wrap">
    <div class="form-content">
      <el-alert :closable="false" type="warning">
        <div slot="title">
          <p>此功能为系统管理员运维使用，请谨慎操作。</p >
        </div>
      </el-alert>
      <el-form ref="form" :model="formData" :rules="rules" label-position="left" label-width="80px" style="padding: 0;">
        <div v-for="(item, index) in formData.tableData" :key="index" class="project-card">
          <div v-if="formData.tableData.length > 1" class="project-delete">
            <i class="el-icon-delete" @click="pop(index)"/>
          </div>
          <el-row :gutter="24">
            <el-col :span="24">
              <el-form-item :rules="rules.groupName" :prop="`tableData.${index}.groupName`" label="group：">
                <el-input v-if="!editMode" v-model="item.groupName" placeholder="请输入内容"/>
                <span v-else>
                  {{ item.groupName }}
                </span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="24">
              <el-form-item :rules="rules.keyName" :prop="`tableData.${index}.keyName`" label="key：">
                <el-input v-if="!editMode" v-model="item.keyName" placeholder="请输入内容"/>
                <span v-else>
                  {{ item.keyName }}
                </span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="24">
              <el-form-item :rules="rules.valueName" :prop="`tableData.${index}.valueName`" label="value：">
                <el-input v-model="item.valueName" :rows="3" type="textarea" placeholder="请输入内容"/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="24">
            <el-col :span="24">
              <el-form-item :prop="`tableData.${index}.description`" :rules="rules.description" label="说明：" >
                <el-input v-model="item.description" :rows="3" type="textarea" placeholder="请输入内容"/>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <div v-if="!editMode" style="margin-top: 10px;">
          <a type="ghost" @click="add()">+ 添加</a>
        </div>
      </el-form>
    </div>
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>
<script>
import validate from '@/packages/validate'
import { sysDataConfigCreate, sysDataConfigUpdate } from '@/api/admin/sysDataConfig.js'
export default {
  components: {},
  mixins: [],
  props: {
    // 传入数据
    data: {
      type: Array,
      default: () => {
        return []
      }
    },
    info: {
      type: Object
    },
    name: {
      type: String
    }
  },
  data() {
    var validateKeyName = (rule, value, callback) => {
      if (String(value)) {
        const arr = rule.field.split('tableData.').join('').split('.keyName')
        const index = arr[0]
        const str = this.formData.tableData[index].groupName.concat('', value)
        let bool = false
        this.formData.tableData.forEach((item, i) => {
          if (i != index && item.groupName.concat('', item.keyName) == str) {
            bool = true
          }
        })
        if (bool) {
          callback(new Error('配置重复'))
        } else {
          callback()
        }
      } else {
        callback(new Error('必填项'))
      }
    }
    return {
      loading: false,
      validate: validate,
      publishStatus: 1,
      formData: {
        tableData: [{
          groupName: '',
          keyName: '',
          valueName: '',
          description: ''
        }]
      },
      rules: {
        groupName: [validate.required(), validate.name_64_char],
        keyName: [validate.required('blur'), validate.name_64_char, { validator: validateKeyName, trigger: 'blur' }],
        valueName: [validate.required()],
        'description': [validate.description]
      }
    }
  },
  computed: {
    editMode: function() {
      return this.name === 'edit'
    },
    activeItem: function() {
      return this.data[0]
    }
  },
  created() {
    if (this.editMode && this.activeItem) {
      this.$set(this.formData.tableData[0], 'groupName', this.activeItem['groupName'])
      this.$set(this.formData.tableData[0], 'id', this.activeItem['id'])
      this.$set(this.formData.tableData[0], 'keyName', this.activeItem['keyName'])
      this.$set(this.formData.tableData[0], 'valueName', this.activeItem['valueName'])
      this.$set(this.formData.tableData[0], 'description', this.activeItem['description'])
    }
  },
  methods: {
    add() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.formData.tableData.push({
            groupName: '',
            keyName: '',
            valueName: '',
            description: ''
          })
        }
      })
    },
    pop(index) {
      this.formData.tableData.splice(index, 1)
    },
    close: function() {
      this.$emit('close')
    },
    confirm: function() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.loading = true
          if (this.editMode) {
            sysDataConfigUpdate(this.formData.tableData[0]).then(res => {
              this.loading = false
              this.$message.success(`自定义数据编辑成功`)
              this.$emit('call', 'refresh')
              this.close()
            }).catch(() => {
              this.loading = false
            })
          } else {
            sysDataConfigCreate(this.formData.tableData).then(res => {
              this.loading = false
              this.$message.success(`自定义数据添加成功`)
              this.$emit('call', 'refresh')
              this.close()
            }).catch(() => {
              this.loading = false
            })
          }
        }
      })
    }
  }
}
</script>
<style scoped lang="scss">
.project-card {
  background: #f3f6fe;
  padding: 10px 30px 1px 20px;
  border-radius: 5px;
  position: relative;
  margin-bottom: 10px;
  .project-delete {
    position: absolute;
    top: 10px;
    right: 10px;
    cursor: pointer;
  }
}
.dialog-wrap {
  padding: 24px 0 !important;
  .dialog-footer {
    margin: 24px 0px -24px !important;
  }
}
.form-content {
  padding: 0 24px;
  max-height: 400px;
  overflow: auto;
}
</style>
