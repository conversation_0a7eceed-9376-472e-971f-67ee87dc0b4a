<template>
  <el-scrollbar :class="{ 'small-scrollbar-wrapper': type === 'small' }" class="tree-scrollbar-wrapper">
    <div class="layout-tree-list">
      <!-- 此处可放操作按钮 -->
      <el-tree
        v-if="defaultSelection"
        ref="tree"
        :indent="0"
        :key="datekey"
        :data="treeData"
        :props="defaultProps"
        :node-key="idName"
        :highlight-current="true"
        :current-node-key="defaultSelection"
        :default-expanded-keys="[defaultSelection]"
        @node-click="handleNodeClick"
      >
        <div v-overflow-tooltip slot-scope="{ node }" class="custom-tree-node-label">{{ node.label }}</div>
      </el-tree>
    </div>
  </el-scrollbar>
</template>

<script>
import splitPane from 'vue-splitpane'
export default {
  components: {
    splitPane
  },
  props: {
    // 渲染的数据
    data: {
      type: Array,
      default: () => {
        return []
      }
    },
    // 传入数组中的子级菜单名称
    children: {
      type: String,
      default: 'childMenus'
    },
    // 用来做展示的名称
    label: {
      type: String,
      default: 'majorName'
    },
    // 唯一值
    idName: {
      type: String,
      default: 'majorCode'
    },
    // 是否需要全部选项
    all: {
      type: Boolean,
      default: true
    },
    /**
     * 节点树的样式
     * normal: 正常
     * small: 小 （场景、团队训练展示左侧阶段和任务用）
     */
    type: {
      type: String,
      default: 'normal'
    },
    /**
     * 默认选中模式
     * first: 默认选中第一个父节点
     * child: 默认递归选中第一个子节点
     */
    selectionMode: {
      type: String,
      default: 'child'
    }
  },
  data() {
    return {
      treeData: [],
      defaultProps: {
        children: this.children,
        label: this.label
      },
      datekey: Date.now(),
      defaultSelection: ''
    }
  },
  watch: {
    'data': function() {
      this.treeData = JSON.parse(JSON.stringify(this.data))
      this.datekey = Date.now()
      if (this.treeData.length) {
        if (this.all) {
          this.treeData.unshift({ [this.idName]: 'all', [this.label]: '全部' })
          this.defaultSelection = 'all'
        } else if (this.selectionMode === 'child' && this.data[0][this.children] && this.data[0][this.children].length) {
          this.recursion(this.data[0])
        } else {
          this.defaultSelection = this.data[0][this.idName]
          this.$emit('node-click', this.data[0])
        }
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.init()
    })
  },
  methods: {
    handleNodeClick(node) {
      this.$emit('node-click', node)
    },
    init() {
      this.treeData = JSON.parse(JSON.stringify(this.data))
      if (this.data.length) {
        if (this.all) {
          this.treeData.unshift({ [this.idName]: 'all', [this.label]: '全部' })
          this.defaultSelection = 'all'
        } else if (this.selectionMode === 'child' && this.data[0][this.children] && this.data[0][this.children].length) {
          this.recursion(this.data)
        } else {
          this.defaultSelection = this.data[0][this.idName]
          this.$emit('node-click', this.data[0])
        }
      }
    },
    // 不需要全部按钮时根据传入 数据进行递归获取到第一个选项中最小的 数据
    recursion(item) {
      if (item[this.children] && item[this.children].length) {
        this.recursion(item[this.children][0])
      } else {
        this.defaultSelection = item[this.idName]
        this.$emit('node-click', item)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.tree-scrollbar-wrapper{
  height: 100%;
  overflow: hidden;
  border-right: 1px solid var(--neutral-300);
  transition: 0.5s;
  ::v-deep .el-scrollbar__wrap{
    overflow: hidden;
    overflow-y: scroll;
  }
  .layout-tree-list {
    width: 100%;
    padding: 15px;
    background: #ffffff;
    position: relative;
    display: flex;
    flex-direction: column;
  }
  // 树的自定义样式
  ::v-deep {
    .el-tree {
      flex: 1;
      min-height: 0;
      // 如果只有一个节点，删除竖线
      > .el-tree-node:not(:has(+ .el-tree-node)) > .el-tree-node__children:before {
        display: none;
      }
    }
    // 一级节点的样式
    .el-tree-node__content{
      height: 32px;
      margin-bottom: 3px;
      .custom-tree-node-label, .el-tree-node__label {
        color: var(--neutral-700);
        font-size: 14px;
        font-weight: 500;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
      }
    }
    .el-tree-node__children{
      position: relative;
      overflow: visible !important;
      padding-left: 16px;
      // 竖线
      &:before {
        content: "";
        height: calc(100% + 6px);
        width: 1px;
        position: absolute;
        left: 12px;
        top: -3px;
        border-left: 1px solid #d8d8d8;
      }
      &:empty {
        &:before {
          display: none;
        }
      }
      // 字节点的样式
      .custom-tree-node-label, .el-tree-node__label {
        color: var(--neutral-700);
        font-size: 13px;
        font-weight: 500;
      }
    }
    // 选中的样式
    .is-current > .el-tree-node__content{
      background: var(--color-50);
      border-radius: 0px 10px 10px 0px;
      .custom-tree-node-label, .el-tree-node__label{
        color: var(--color-600);
      }
    }
    // hover样式
    .el-tree-node__content:hover {
      background: var(--color-50);
      border-radius: 0px 10px 10px 0px;
      .custom-tree-node-label, .el-tree-node__label{
        color: var(--color-600);
      }
    }
  }
  // small模式下自定义样式（调整字体大小和圆角）
  &.small-scrollbar-wrapper {
    ::v-deep {
      .el-tree-node__content{
        .custom-tree-node-label, .el-tree-node__label {
          font-size: 13px;
          font-weight: normal;
        }
      }
      .el-tree-node__children {
        &:before {
          display: none;
        }
        .custom-tree-node-label, .el-tree-node__label {
          font-size: 12px;
          font-weight: normal;
        }
      }
      .is-current > .el-tree-node__content{
        border-radius: 0px;
      }
      .el-tree-node__content:hover {
        border-radius: 0px;
      }
    }
  }
}
</style>
