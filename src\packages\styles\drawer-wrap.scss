// 侧拉弹窗样式
.el-drawer__wrapper {
  background-color: rgba(0, 0, 0, 0.45);
  .el-alert {
    margin-bottom: 12px;
  }
  .el-drawer__header {
    padding: 13px 24px;
    color: var(--neutral-700);
    border-bottom: 1px solid var(--neutral-300);
    border-radius: 2px 2px 0 0;
    margin: 0;
    >span {
      margin: 0;
      color: rgba(0, 0, 0, 0.85);
      font-weight: 500;
      font-size: 16px;
      line-height: 22px;
    }
  }
  .el-drawer__body {
    .resource-table {
      border-bottom: none;
    }
    .el-form {
      padding: 0 20px;
    }
    .drawer-wrap {
      height: 100%;
      display: flex;
      flex-direction: column;
      .drawer-fliter-wrap {
        position: relative;
        border: 1px solid #ececec;
        background-color: #f7f7f9;
        padding: 10px 15px;
        .drawer-fliter-item {
          margin-bottom: 5px;
          .drawer-fliter-title {
            display: inline-block;
            width: 75px;
            line-height: 30px;
            font-weight: 700;
            margin-right: 16px;
            color: #111;
          }
        }
      }
      .drawer-wrap-content {
        height: 100%;
        padding: 24px;
        overflow: auto;
      }
    }
    .drawer-footer {
      border-top: 1px solid var(--neutral-300);
      padding: 14px 24px;
      .el-button--text {
        padding: 4px 15px;
      }
    }
  }
}
