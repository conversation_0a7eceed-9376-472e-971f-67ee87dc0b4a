<template>
  <div class="resource-table container">
    <!-- 分类区 -->
    <category
      :category-name="moduleName"
      :style="{'height': fold ? '0' : 'unset', 'overflow': fold ? 'hidden' : 'unset'}"
      style="margin-bottom: 15px;"
      @classificationType="classificationType"
    />
    <!-- 操作区 -->
    <div style="margin: 0 15px 15px;" class="operation-wrap">
      <div :class="{ 'folded': fold }" class="category-fold-wrap" @click="fold = !fold">
        <div v-if="fold">展开<i class="el-icon-d-arrow-left rotate-90deg" /></div>
        <div v-else>折叠<i class="el-icon-d-arrow-left rotate90deg" /></div>
      </div>
      <div class="operation-left">
        <slot name="action" />
        <el-button type="primary" icon="el-icon-d-caret" @click="clickScore('score')">评分</el-button>
        <el-button type="primary" icon="el-icon-refresh" @click="refresh" />
        <el-checkbox v-model="isAll" style="margin-left: 35px;">仅查看自己创建</el-checkbox>
      </div>
      <div class="operation-right">
        <el-badge :value="searchBtnShowNum">
          <el-button icon="el-icon-search" @click="openSearch">搜索</el-button>
        </el-badge>
      </div>
    </div>
    <!-- 搜索区 -->
    <t-search-box
      v-show="searchView"
      :search-key-list="searchKeyListView"
      default-placeholder="默认搜索名称"
      class="ml-15 mr-15"
      @search="searchMultiple"
    />
    <courseCard :ranking-rating="rankingRating" :isrefresh="isrefresh" :storage="storage" :difficulty="difficulty" :course-category-id="courseCategoryId" :course-name="courseName" :is-all="isAll" style="padding: 0 15px 15px;"/>
  </div>
</template>

<script>
import module from '../config.js'
import tSearchBox from '@/packages/search-box/index.vue'
import mixinsPageTable from '@/packages/mixins/page_table'
import category from '../category/index.vue'
import courseCard from './courseCard.vue'

export default {
  components: {
    tSearchBox,
    category,
    courseCard
  },
  mixins: [mixinsPageTable],
  data() {
    return {
      fold: false,
      moduleName: module.name,
      // 搜索配置项
      searchKeyList: [
        { key: 'name', label: '名称', master: true },
        { key: 'userName', label: '创建人', master: true }
      ],
      isrefresh: false,
      courseCategoryId: '',
      difficulty: '',
      courseName: {},
      isAll: false,
      storage: 0,
      type: 0,
      rankingRating: '',
      searchArr: [],
      searchBtnShowNum: null
    }
  },
  created() {
    if (this.$store.state.cache[this.moduleName]) {
      this.searchBtnShowNum = Object.keys(this.$store.state.cache[this.moduleName]).length || null
    }
  },
  methods: {
    'openSearch': function() {
      this.searchView = !this.searchView
      !this.searchView && this.searchArr.length != 0 ? this.searchBtnShowNum = this.searchArr.length : this.searchBtnShowNum = null
    },
    searchMultiple(val) {
      if (Object.keys(val).length === 0) {
        this.searchArr = []
      } else {
        for (var key in val) {
          if (!this.searchArr.includes(key)) {
            this.searchArr.push(key)
          }
        }
      }
      this.courseName = val
      const obj = {
        data: this.courseName,
        key: this.moduleName
      }
      this.$store.commit('SET_CACHE', obj)
      if (this.storage == 0) {
        this.getList()
      } else {
        this.getListFn()
      }
    },
    getListFn: function() {
      this.isrefresh = !this.isrefresh
    },
    getList() {
      this.storage = this.storage + 1
    },
    classificationType(param) {
      this.courseCategoryId = param.contentCategoryId
      this.difficulty = param.contentLevel
      this.getListFn()
    },
    clickScore() {
      this.rankingRating = !this.rankingRating
      this.getListFn()
    }
  }
}
</script>
<style lang="scss" scoped>
.container {
  padding: 0 !important;
}
</style>
