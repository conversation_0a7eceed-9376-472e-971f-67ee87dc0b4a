<template>
  <div v-loading="loading" class="drawer-wrap">
    <div class="drawer-wrap-content">
      <el-form ref="form" :model="formData" :rules="rules" label-position="left" label-width="150px" class="dialog-form">
        <div v-if="questionList.length">
          <div class="p-10">
            <el-form-item
              v-if="questionList.filter(item => { return item.questionType == '1' }).length"
              :label="`单选题（${questionList.filter(item => { return item.questionType == '1' }).length}）`"
              prop="theoryVO.singleTypeNum"
            >
              <el-input-number
                v-model.trim="formData.theoryVO.singleTypeNum"
                :controls="false"
                placeholder="请输入分数"
                style="width: 300px"
              /> 分
            </el-form-item>
            <el-form-item
              v-if="questionList.filter(item => { return item.questionType == '2' }).length"
              :label="`多选题（${questionList.filter(item => { return item.questionType == '2' }).length}）`"
              prop="theoryVO.manyTypeNum"
            >
              <el-input-number
                v-model.trim="formData.theoryVO.manyTypeNum"
                :controls="false"
                placeholder="请输入分数"
                style="width: 300px"
              /> 分
            </el-form-item>
            <el-form-item
              v-if="questionList.filter(item => { return item.questionType == '3' }).length"
              :label="`判断题（${questionList.filter(item => { return item.questionType == '3' }).length}）`"
              prop="theoryVO.judgeTypeNum"
            >
              <el-input-number
                v-model.trim="formData.theoryVO.judgeTypeNum"
                :controls="false"
                placeholder="请输入分数"
                style="width: 300px"
              /> 分
            </el-form-item>
            <el-form-item
              v-if="questionList.filter(item => { return item.questionType == '4' }).length"
              :label="`CTF题（${questionList.filter(item => { return item.questionType == '4' }).length}）`"
              prop="theoryVO.ctfTypeNum"
            >
              <el-input-number
                v-model.trim="formData.theoryVO.ctfTypeNum"
                :controls="false"
                placeholder="请输入分数"
                style="width: 300px"
              /> 分
            </el-form-item>
            <el-form-item
              v-if="questionList.filter(item => { return item.questionType == '5' }).length"
              :label="`AWD题（${questionList.filter(item => { return item.questionType == '5' }).length}）`"
              prop="theoryVO.awdTypeNum"
            >
              <el-input-number
                v-model.trim="formData.theoryVO.awdTypeNum"
                :controls="false"
                placeholder="请输入分数"
                style="width: 300px"
              /> 分
            </el-form-item>
            <el-form-item
              v-if="questionList.filter(item => { return item.questionType == '9' }).length"
              :label="`漏洞题（${questionList.filter(item => { return item.questionType == '9' }).length}）`"
              prop="theoryVO.bugTypeNum"
            >
              <el-input-number
                v-model.trim="formData.theoryVO.bugTypeNum"
                :controls="false"
                placeholder="请输入分数"
                style="width: 300px"
              /> 分
            </el-form-item>
            <el-form-item
              v-if="questionList.filter(item => { return item.questionType == '7' }).length"
              :label="`填空题（${questionList.filter(item => { return item.questionType == '7' }).length}）`"
              prop="theoryVO.completionNum"
            >
              <el-input-number
                v-model.trim="formData.theoryVO.completionNum"
                :controls="false"
                placeholder="请输入分数"
                style="width: 300px"
              /> 分
            </el-form-item>
            <el-form-item
              v-if="questionList.filter(item => { return item.questionType == '6' }).length"
              :label="`其他题（${questionList.filter(item => { return item.questionType == '6' }).length}）`"
              prop="theoryVO.otherTypeNum"
            >
              <el-input-number
                v-model.trim="formData.theoryVO.otherTypeNum"
                :controls="false"
                placeholder="请输入分数"
                style="width: 300px"
              /> 分
            </el-form-item>
          </div>
        </div>
      </el-form>
    </div>
    <div class="drawer-footer">
      <el-button type="primary" @click="confirm">确定</el-button>
      <el-button type="text" @click="close">取消</el-button>
    </div>
  </div>
</template>
<script>
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
import validate from '@/packages/validate'
export default {
  components: {
  },
  mixins: [mixinsActionMenu],
  props: {
    // 传入数据
    data: {
      type: Array,
      default: () => {
        return []
      }
    },
    name: {
      type: String
    },
    questionList: {
      type: Array
    },
    getQuestionNum: {
      type: Number
    },
    getScore: {
      type: Number
    }
  },
  data() {
    return {
      loading: false,
      validate: validate,
      formData: {
        'theoryVO': {
          singleTypeNum: undefined,
          manyTypeNum: undefined,
          judgeTypeNum: undefined,
          ctfTypeNum: undefined,
          completionNum: undefined,
          awdTypeNum: undefined,
          bugTypeNum: undefined,
          otherTypeNum: undefined
        }
      },
      rules: {
        'theoryVO.singleTypeNum': [validate.number_integer, { type: 'number', min: 1, max: 9999, message: '输入范围：1-9999', trigger: 'change' }],
        'theoryVO.manyTypeNum': [validate.number_integer, { type: 'number', min: 1, max: 9999, message: '输入范围：1-9999', trigger: 'change' }],
        'theoryVO.judgeTypeNum': [validate.number_integer, { type: 'number', min: 1, max: 9999, message: '输入范围：1-9999', trigger: 'change' }],
        'theoryVO.ctfTypeNum': [validate.number_integer, { type: 'number', min: 1, max: 9999, message: '输入范围：1-9999', trigger: 'change' }],
        'theoryVO.completionNum': [validate.number_integer, { type: 'number', min: 1, max: 9999, message: '输入范围：1-9999', trigger: 'change' }],
        'theoryVO.awdTypeNum': [validate.number_integer, { type: 'number', min: 1, max: 9999, message: '输入范围：1-9999', trigger: 'change' }],
        'theoryVO.bugTypeNum': [validate.number_integer, { type: 'number', min: 1, max: 9999, message: '输入范围：1-9999', trigger: 'change' }],
        'theoryVO.otherTypeNum': [validate.number_integer, { type: 'number', min: 1, max: 9999, message: '输入范围：1-9999', trigger: 'change' }]
      }
    }
  },
  computed: {

  },
  methods: {
    close: function() {
      this.$emit('close')
    },
    confirm: function() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.$emit('call', 'setScore', this.formData)
          this.close()
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.p-10 {
  padding: 10px 20px;
}
</style>
