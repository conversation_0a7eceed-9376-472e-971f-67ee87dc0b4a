<template>
  <div class="category-wrap">
    <transverse-list
      :data="categoryArr"
      :allow-deletion="false"
      :allow-add="false"
      :all="true"
      v-bind="categoryProps"
      title="分类"
      @node-click="handleNodeClick($event, 'category')"
    />
  </div>
</template>
<script>
import module from '../config.js'
import transverseList from '@/packages/transverse-list/index.vue'
import { queryCategoryPage } from '@/api/exam/index.js'

export default {
  components: {
    transverseList
  },
  props: {
    category: [String, Number]
  },
  data() {
    return {
      moduleName: module.name,
      categoryArr: [],
      categoryProps: {
        label: 'categoryName',
        idName: 'id'
      },
      categoryQuery: {
        category: this.category
      }
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    handleNodeClick(item, key) {
      this.categoryQuery[key] = item.id
      this.$emit('category-query', this.categoryQuery)
    },
    getList: function() {
      const params = {
        pageNum: 1,
        limit: 1000
      }
      queryCategoryPage(params).then(res => {
        this.categoryArr = res.data.records
      }).catch(() => {})
    }
  }
}
</script>
<style lang="scss" scoped>
.category-wrap {
  margin-bottom: 10px;
  border-bottom: 1px solid var(--neutral-300) !important;
}
::v-deep {
  .transverse-list {
    border-bottom: 0px solid var(--neutral-300) !important;
    display: flex;
    justify-content: space-between;
    padding: 4px 24px;
    width: 100%;
    max-height: 300px;
    font-size: 16px;
    overflow-y: auto;
  }
}
.transverse-border {
  border-bottom: 1px solid var(--neutral-300) !important;
}
.transverse-list {
  .transverse-list-title {
    white-space: nowrap;
    font-size: 13px;
    font-family: Microsoft YaHei;
    font-weight: 400;
    color: #999999;
    margin-top: 10px;
  }
  .transverse-list-content {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    flex: 1;
    min-width: 0;
    position: relative;
    padding-top: 5px;
    .list-content {
      height: 30px;
      padding: 0 11px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      font-family: Microsoft YaHei;
      font-weight: 400;
      font-size: 13px;
      color: #5b5b5b;
      margin-bottom: 2px;
      margin-right: 10px;
    }

    .list-content:hover {
      background: #eaeeff;
      border-radius: 4px;
      color: #3152ef;
    }
    .content-selected {
      background: #eaeeff;
      color: #3152ef;
      font-size: 13px;
      font-family: Microsoft YaHei;
      font-weight: 400;
      border-radius: 4px;
    }
    .type-item {
      position: relative;
      border-radius: 5px;
      z-index: 0;
    }
    .type-item:hover {
      background: rgba(35, 98, 251, 0.1);
    }
  }
}
</style>
