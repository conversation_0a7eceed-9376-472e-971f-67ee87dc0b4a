<template>
  <div class="resource-table">
    <!-- 操作区 -->
    <div class="operation-wrap">
      <div class="operation-left">
        <slot name="action" />
        <el-button type="primary" icon="el-icon-refresh" @click="refresh" />
      </div>
      <div class="operation-right">
        <el-badge :value="searchBtnShowNum">
          <el-button icon="el-icon-search" @click="openSearch">搜索</el-button>
        </el-badge>
        <!-- 自定义表格列 -->
        <t-table-config
          v-if="!customColData.length"
          :data="columnsObj"
          :active-key-arr="columnsViewArr"
          @on-change-col="onChangeCol"
        />
      </div>
    </div>
    <!-- 搜索区 -->
    <t-search-box
      v-show="searchView"
      :search-key-list="searchKeyListView"
      default-placeholder="默认搜索考试名称"
      @search="searchMultiple"
    />
    <!-- 列表 -->
    <t-table-view
      ref="tableView"
      :height="height"
      :single="single"
      :loading="tableLoading"
      :data="tableData"
      :total="tableTotal"
      :page-size="pageSize"
      :current="pageCurrent"
      :select-item="selectItem"
      current-key="id"
      @on-select="onSelect"
      @on-current="onCurrent"
      @on-change="changePage"
      @on-sort-change="onSortChange"
      @on-page-size-change="onPageSizeChange"
    >考试管理
      <el-table-column
        v-for="item in columnsViewArr"
        :key="item"
        :min-width="colMinWidth"
        :label="columnsObj[item].title"
        :fixed="columnsObj[item].master ? 'left' : false"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span v-if="item == 'name'">
            <el-tooltip placement="top">
              <div slot="content">
                灰色图标表示环境未开启<br>
                绿色图标表示环境已开启<br>
                红色图标表示环境异常<br>
                转圈图标表示环境开启中/关闭中
              </div>
              <span>
                <img
                  v-if="scope.row.envStatus == 0"
                  src="@/assets/img/exam/server.svg"
                  alt=""
                  width="15">
                <img
                  v-else-if="scope.row.envStatus == 1"
                  src="@/assets/img/exam/server1.svg"
                  alt=""
                  width="15">
                <img
                  v-else-if="scope.row.envStatus == 2"
                  src="@/assets/img/exam/server2.svg"
                  alt=""
                  width="15">
                <img
                  v-else-if="scope.row.envStatus == 3"
                  class="loadding"
                  src="@/assets/img/exam/loadding.svg"
                  alt=""
                  width="15">
              </span>
            </el-tooltip>
            <!-- <el-tooltip placement="top">
              <div slot="content">
                灰色图标表示环境快照未创建<br>
                绿色图标表示环境快照创建成功<br>
                红色图标表示环境快照创建失败<br>
                转圈图标表示环境快照创建中
              </div>
              <span>
                <img
                  v-if="scope.row.snapshotStatus === 0"
                  src="@/assets/img/exam/snapshot.svg"
                  alt=""
                  width="15">
                <img
                  v-if="scope.row.snapshotStatus === 1"
                  src="@/assets/img/exam/snapshot2.svg"
                  alt=""
                  width="15">
                <img
                  v-if="scope.row.snapshotStatus === 2"
                  src="@/assets/img/exam/snapshot1.svg"
                  alt=""
                  width="15">
                <img
                  v-if="scope.row.snapshotStatus == 3"
                  class="loadding"
                  src="@/assets/img/exam/loadding.svg"
                  alt=""
                  width="15">
              </span>
            </el-tooltip> -->
            <a
              v-if="link"
              :href="`/manage/exam/detail/nonCertificate/${scope.row.id}/overview/${scope.row.name}/2`"
              @click.prevent="linkEvent('detailNonCertificateExam', scope.row, { id: scope.row.id, name: scope.row.name, examStatus: scope.row.examStatus, questionType: scope.row.questionType, view: 'overview', cerType: '2' })">{{ scope.row.name || "-" }}
            </a>
          </span>
          <span v-else-if="item == 'studentNum'">
            <a
              v-if="link"
              :href="`/manage/exam/detail/nonCertificate/${scope.row.id}/students/${scope.row.name}/2`"
              @click.prevent="linkEvent('detailNonCertificateExam', scope.row, { id: scope.row.id, name: scope.row.name, examStatus: scope.row.examStatus, questionType: scope.row.questionType, view: 'students', cerType: '2' })">{{ scope.row.studentNum }}
            </a>
          </span>
          <span v-else-if="item == 'published'">
            <span>{{ scope.row[item] == '1' ? '已发布' : '未发布' }}</span>
          </span>
          <span v-else-if="item == 'examStatus'">
            <span v-if="scope.row[item] == 'BEGIN'"><el-badge type="success" is-dot />进行中</span>
            <span v-if="scope.row[item] == 'PREPARE'"><el-badge type="info" is-dot />未开始</span>
            <span v-if="scope.row[item] == 'END'"><el-badge type="danger" is-dot />已结束</span>
          </span>
          <span v-else-if="item == 'certificateLevel'">
            <span v-if="scope.row[item] == 1">初级</span>
            <span v-if="scope.row[item] == 2">中级</span>
            <span v-if="scope.row[item] == 3">高级</span>
          </span>
          <span v-else-if="item == 'beginTime'">
            <span>{{ scope.row.beginTime + ' - ' + scope.row.endTime }}</span>
          </span>
          <span v-else>{{ scope.row[item] || "-" }}</span>
        </template>
      </el-table-column>
    </t-table-view>
  </div>
</template>

<script>
import module from '../config.js'
import tSearchBox from '@/packages/search-box/index.vue'
import tTableView from '@/packages/table-view/index.vue'
import tTableConfig from '@/packages/table-config/table-col-config.vue'
import tableTdMultiCol from '@/packages/table-config/table-td-multi-col.vue'
import mixinsPageTable from '@/packages/mixins/page_table'
import category from '../category/index.vue'
import { queryExamPage, envStatus, snapStatus } from '@/api/exam/index.js'

export default {
  components: {
    category,
    tSearchBox,
    tTableView,
    tTableConfig,
    tableTdMultiCol
  },
  mixins: [mixinsPageTable],
  props: {
    refreshEnvExamId: { // 需要刷新环境的考试id
      type: Number,
      default: undefined
    },
    refreshSnapshotId: { // 需要刷新快照的考试id
      type: Number,
      default: undefined
    }
  },
  data() {
    return {
      moduleName: module.name,
      // 搜索配置项
      searchKeyList: [
        // { key: 'certificateName', label: '证书名称', master: true, placeholder: '默认搜索证书名称' },
        { key: 'name', label: '考试名称', master: true }
      ],
      // 所有可配置显示列 master：不可隐藏 title:列名称
      columnsObj: module.columnsObj,
      // 当前显示列key表 默认，如果localStorage有数据将被覆盖
      columnsViewArr: module.columnsViewArr,
      envStatus: [],
      snapStatus: [],
      isUpdate: Math.random(),
      envTimer: null, // 环境定时器
      snapshotTimer: null // 快照定时器
    }
  },
  watch: {
    'refreshEnvExamId': function(nval, oval) {
      if (nval) {
        this.pollAsyncEnvRequest(nval)
      }
    },
    'refreshSnapshotId': function(nval, oval) {
      if (nval) {
        this.pollAsyncSnapshotRequest(nval)
      }
    }
  },
  beforeDestroy() {
    this.closePollAsyncEnvRequest(false)
    this.closePollAsyncSnapshotRequest(false)
  },
  methods: {
    envStatusFn(examId) {
      if (examId) { // 开启/关闭某个考试环境
        envStatus({ examId }).then(res => {
          if (res.code === 0) {
            const { total, running, error } = res.data
            if (total === 0) { // 未开启
              // 状态有结果就关闭轮询
              // this.closePollAsyncEnvRequest()
            } else {
              if (total === running) { // 启动成功
                this.closePollAsyncEnvRequest(true)
              } else {
                if (error !== 0) { // 启动失败
                  this.closePollAsyncEnvRequest(true)
                }
              }
            }
          }
        })
      } else {
        this.tableData.forEach(async(item) => {
          const params = { examId: item.id }
          const result = await envStatus(params)
          if (result.code === 0) {
            const { total, running, error } = result.data
            if (total === 0) { // 未开启
              item.envStatus = 0
            } else {
              if (total === running) { // 启动成功
                item.envStatus = 1
              } else {
                if (error !== 0) { // 启动失败
                  item.envStatus = 2
                } else { // 启动中
                  item.envStatus = 3
                }
              }
            }
          }
        })
      }
    },
    snapStatusFn(id) {
      if (id) { // 创建某个考试快照
        snapStatus({ examId: id }).then(res => {
          if (res.code === 0) {
            const { avail, creating } = res.data
            if (avail === 0 && creating === 0) { // 未创建
              // this.closePollAsyncSnapshotRequest()
            } else {
              if (creating === 0) { // 创建完成
                this.closePollAsyncSnapshotRequest(true)
              }
            }
          }
        })
      } else {
        this.tableData.forEach(async(item) => {
          const params = { examId: item.id }
          const result = await snapStatus(params)
          if (result.code === 0) {
            const { avail, creating } = result.data
            if (avail === 0 && creating === 0) { // 未创建
              item.snapshotStatus = 0
            } else {
              if (creating === 0) { // 创建完成
                item.snapshotStatus = 1
              } else { // 创建中
                item.snapshotStatus = 3
              }
            }
          }
        })
      }
    },
    // 环境轮询
    pollAsyncEnvRequest(id) {
      this.envStatusFn(id)
      this.closePollAsyncEnvRequest(false)
      this.envTimer = setInterval(() => {
        this.envStatusFn(id)
      }, 5000)
    },
    // 关闭环境轮询定时器
    closePollAsyncEnvRequest(bool) {
      this.envTimer && clearInterval(this.envTimer)
      if (!bool) {
        return
      }
      this.getList()
    },
    // 创建快照轮询
    pollAsyncSnapshotRequest(id) {
      this.snapStatusFn(id)
      this.closePollAsyncSnapshotRequest(false)
      this.snapshotTimer = setInterval(() => {
        this.snapStatusFn(id)
      }, 5000)
    },
    // 关闭创建快照轮询定时器
    closePollAsyncSnapshotRequest(bool) {
      this.snapshotTimer && clearInterval(this.snapshotTimer)
      if (!bool) {
        return
      }
      this.getList()
    },
    getList: function() {
      this.tableLoading = true
      const data = this.getPostData('page', 'limit')
      const params = {
        ...data
      }
      queryExamPage(params).then((res) => {
        if (res.code === 0) {
          this.tableData = res.data.records
          this.tableData = res.data.records.map((item) => {
            item.envStatus = 3
            item.snapshotStatus = 3
            return item
          })
          this.tableTotal = res.data.total
          this.envStatusFn()
          // this.snapStatusFn()
          this.handleSelection()
        }
        this.tableLoading = false
      }).catch(() => {
        this.tableLoading = false
      })
    }
  }
}
</script>

<style scoped lang="scss">
.loadding {
  animation: fadenum 5s infinite;
}
@keyframes fadenum {
  100% {
    transform: rotate(360deg);
  }
}
</style>
