export default {
  props: {
    // 当前模块名称
    moduleName: String,
    // 已选数据 单选为Object  多选为String
    selectItem: Array,
    page: {
      type: String,
      default: 'list'
    }
  },
  data() {
    return {
      modalWidth: '520px',
      modalShow: false,
      modalName: null,
      drawerWidth: '720px',
      drawerShow: false,
      drawerName: null
    }
  },
  computed: {
    // 是否在列表界面
    'isList': function() {
      return this.page === 'list'
    },
    // 单选禁用
    'singleDisabled': function() {
      return !this.selectItem.length || this.selectItem.length > 1
    },
    // 多选禁用
    'multipleDisabled': function() {
      return !this.selectItem.length
    }
  },
  watch: {
    // 监听模态框动态组件，如置空则关闭模态框
    'modalName': function(val) {
      this.modalShow = (!!val)
    },
    // 监听抽屉框动态组件，如置空则关闭模态框
    'drawerName': function(val) {
      this.drawerShow = (!!val)
    }
  },
  methods: {
    'modalClose': function() {
      this.modalName = null
      this.modalWidth = '520px'
    },
    'drawerClose': function() {
      this.drawerName = null
      this.drawerWidth = '720px'
    },
    'clickDrop': function(name) {
      if (this.drawerAction && this.drawerAction.includes(name)) {
        this.drawerName = name
      } else {
        this.modalName = name
      }
      setTimeout(() => {
        this.$nextTick(() => {
          const modals = document.querySelectorAll('.v-modal')
          modals.forEach(modal => {
            if (modal && modal.parentNode) {
              modal.parentNode.removeChild(modal)
            }
          })
        })
      }, 500)
    }
  }
}
