<template>
  <div class="wrapper">
    <Topo
      v-if="topologyId"
      :scene-name="data.name"
      :topo-id="topologyId"
      :is-training-module="true"
      :topo-type="type == '编辑'|| type==='创建' ? 'allPermissions' : 'readPermissions'"
    />
    <el-empty
      v-else
      :image="img"
      :image-size="110"
      style="margin: 100px auto"
      description="暂无数据"
    />
  </div>
</template>

<script>
import Topo from '@/packages/topo/index'
import { contentTopology } from '@/api/teacher/index'
import mixin from './mixin'
export default {
  components: { Topo },
  mixins: [mixin],
  props: {
    data: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      topologyId: ''
    }
  },
  mounted() {
    this.getTopo()
  },
  methods: {
    getTopo() {
      contentTopology({ contentId: this.contentId, schedulingCode: null }).then(res => {
        if (res.code == 0 && res.data) {
          this.topologyId = res.data.topologyId || ''
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.wrapper {
  height: 100%;
}
::v-deep {
  .orchestration-create-warp {
    height: 100% !important;
  }
}
</style>
