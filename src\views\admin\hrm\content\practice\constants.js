export const questionTypeArr = [
  { label: '判断题', value: '3' },
  { label: '单选题', value: '1' },
  { label: '多选题', value: '2' },
  { label: 'CTF题', value: '4' },
  { label: '填空题', value: '7' }
]

export const questionTypeObj = questionTypeArr.reduce((acc, prev) => {
  acc[prev.value] = prev.label
  return acc
}, {})

export const questionTypeSimulationArr = [
  { label: '判断题', value: '3' },
  { label: '单选题', value: '1' },
  { label: '多选题', value: '2' },
  { label: 'CTF题', value: '4' },
  { label: 'AWD题', value: '5' },
  { label: '漏洞题', value: '9' },
  { label: '填空题', value: '7' },
  { label: '其他', value: '6' }
]

export const questionTypeSimulationObj = questionTypeSimulationArr.reduce((acc, prev) => {
  acc[prev.value] = prev.label
  return acc
}, {})
