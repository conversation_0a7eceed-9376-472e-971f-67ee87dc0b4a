// 弹出层dialog样式
.el-dialog__wrapper {
  background-color: rgba(0, 0, 0, 0.45);
  .el-alert {
    margin-bottom: 16px;
  }
  .el-dialog__header {
    padding: 13px 24px 12px 24px;
    border-bottom: 1px solid #f0f0f0;
    border-radius: 2px 2px 0 0;
    .el-dialog__title {
      font-weight: bold;
      color: rgba(0, 0, 0, 0.85);
      font-size: 16px;
      line-height: 22px;
      word-wrap: break-word;
    }
    .el-dialog__headerbtn {
      top: 15px;
    }
  }
  .el-dialog__body {
    padding: 26px 16px;
    color: #252525;
    .el-form {
      padding: 0 20px;
    }
    .dialog-wrap {
      margin: -26px -16px;
      padding: 24px;
      word-break: break-all;
    }
    .dialog-footer {
      text-align: right;
      margin: 24px -24px -24px;
      padding: 14px 24px;
      background: transparent;
      border-top: 1px solid #f0f0f0;
      border-radius: 0 0 2px 2px;
      .el-button--text {
        padding: 4px 15px;
      }
    }
  }
}
