<template>
  <div>
    <el-upload
      :action="getImgUrl()"
      :headers="{'Admin-Token': token}"
      :show-file-list="false"
      :on-success="handleAvatarSuccess"
      :on-error="handlerErr"
      :before-upload="beforeAvatarUpload"
      class="avatar-uploader"
    >
      <template v-if="newImageUrl">
        <div class="image-container" @mouseenter="hover = true" @mouseleave="hover = false">
          <img :src="newImageUrl" class="avatar">
          <div v-show="hover" class="actions">
            <i class="el-icon-delete delete-icon" @click.stop="handleDeleteImage" />
            <i class="el-icon-view view-icon" @click.stop="previewImage" />
          </div>
        </div>
      </template>
      <div v-else class="upload-placeholder">
        <i class="el-icon-plus avatar-uploader-icon" />
      </div>
    </el-upload>
    <div class="tip">
      <!-- <span>请上传jpg, gif, png格式的图片</span> -->
      <span v-if="!(Array.isArray(imgTypes) && imgTypes.length)">请上传jpg格式的图片</span>
      <span v-else>{{ `请上传${imgTypes.join(',')}格式的图片` }}</span>
      <span>建议图片尺寸为{{ imageWidth }} × {{ imageHeight }}</span>
      <span>图片大小不能超过 2MB</span>
    </div>
    <!-- 查看大图 -->
    <el-dialog :visible.sync="dialogVisible">
      <el-image
        :src="newImageUrl"
        :preview-src-list="[newImageUrl]"
      />
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
export default {
  props: {
    imageUrl: {
      type: String,
      default: function() {
        return ''
      }
    },
    imageWidth: {
      type: String,
      default: function() {
        return ''
      }
    },
    imageHeight: {
      type: String,
      default: function() {
        return ''
      }
    },
    imgTypes: {
      type: Array,
      default: function() {
        return []
      }
    }
  },
  data() {
    return {
      headers: {
        Authorization: 'Bearer ' + localStorage.getItem('_token')
      },
      newImageUrl: '',
      hover: false,
      dialogVisible: false
    }
  },
  computed: {
    ...mapGetters(['accesstoken'])
  },
  watch: {
    imageUrl(newVal) {
      this.newImageUrl = newVal
    }
  },
  created() {
    this.newImageUrl = this.imageUrl
    this.token = JSON.parse(localStorage.getItem('Admin-Token')).data
  },
  mounted() {
    this.getImgUrl()
  },
  methods: {
    getImgUrl() {
      return '/api/admin/portal/upTeachingCover'
    },
    handlerErr(err, file, fileList) {
      console.log(err, file, fileList)
    },
    handleAvatarSuccess(res, file) {
      if (res.code === 200 || res.code === 0) {
        this.newImageUrl = URL.createObjectURL(file.raw)
        this.$emit('uploadImgSuccess', res.data)
        this.$message({
          message: '上传成功',
          type: 'success'
        })
      } else {
        this.$message.error(res.msg)
      }
    },
    beforeAvatarUpload(file) {
      console.log(file)
      const isLt = file.size / 1024 / 1024 < 2
      if (!isLt) {
        this.$message.error(`上传图片大小不能超过 2MB!`)
        return false
      }
      if (Array.isArray(this.imgTypes) && this.imgTypes.length) {
        const suffix = String(file.name).substring(String(file.name).lastIndexOf('.') + 1)
        if (!this.imgTypes.includes(suffix)) {
          this.$message.error('上传图片只能是 jpg,png,jpeg格式!')
          return false
        }
      } else {
        const arr = ['image/jpeg']
        const imgType = arr.indexOf(file.type)

        if (arr.indexOf(file.type) < 0) {
          this.$message.error('上传图片只能是 jpg  格式!')
          return false
        }
        return imgType >= 0 && isLt
      }
    },
    // 删除图片
    handleDeleteImage() {
      this.newImageUrl = ''
      this.$emit('removeImg', this.newImageUrl)
    },
    // 查看大图
    previewImage() {
      this.dialogVisible = true
    }
  }
}
</script>

<style lang="less">
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: var(--color-600);
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}
.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
.image-container {
  width: 178px;
  height: 178px;
  position: relative;
  .actions {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 0;
    transition: opacity 0.3s;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
  }
  .actions i {
    color: #fff;
    font-size: 24px;
    margin: 0 10px;
    cursor: pointer;
  }
  .overlay:hover, .actions:hover {
    opacity: 1;
  }
  .delete-icon, .view-icon {
    color: #fff;
    font-size: 24px;
    margin: 0 10px;
    cursor: pointer;
  }
  .upload-placeholder {
    text-align: center;
    color: #999;
  }
  .upload-text {
    margin-top: 10px;
  }
}
</style>
