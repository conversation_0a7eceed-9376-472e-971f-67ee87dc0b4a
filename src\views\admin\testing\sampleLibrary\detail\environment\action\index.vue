<template>
  <div class="buttons-wrap">
    <!-- 中部弹窗 start-->
    <el-dialog
      :title="titleMapping[modalName]"
      :visible.sync="modalShow"
      :width="modalWidth"
      :modal="false"
      :destroy-on-close="true"
      append-to-body
      @close="modalClose"
    >
      <transition name="el-fade-in-linear">
        <component
          :is="modalName"
          :name="modalName"
          :data="selectItem"
          :env-type="envType"
          @close="modalClose"
          @call="confirmCall"
        />
      </transition>
    </el-dialog>
    <!-- 中部弹窗 end-->
  </div>
</template>
<script>
import mixinsActionMenu from '@/packages/mixins/action_menu.js'
export default {
  components: {
  },
  mixins: [mixinsActionMenu],
  props: {
    selectItem: {
      type: Array,
      default: () => []
    },
    envType: {
      type: String,
      default: 'network'
    },
    // 0: 未完成 1: 完成
    deployed: {
      type: Number,
      default: 0
    },
    // 重新部署 0: 未完成 1: 完成
    redeploy: {
      type: Number,
      default: null
    },
    data: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      // 弹窗title映射
      titleMapping: {
      },
      roleIds: [],
      showDeployBtnRoles: [],
      routeName: '',
      userInfo: JSON.parse(localStorage.getItem('loginUserInfo')) || {}
    }
  },
  computed: {
  },
  mounted() {
    // 1.testing_detail: 检测项目测试环境
    // 2.testingTask_detail：测试任务详情测试环境
    this.routeName = this.$route.name
    // 检测厂商：181254 超管：180162
    this.roleIds = this.userInfo.roleIds ? JSON.parse(this.userInfo.roleIds) : []
    this.showDeployBtnRoles = [181254, 180162]
  },
  methods: {
    confirmCall: function(type, data) {
      if (type === 'close') {
        this.modalClose()
      } else if (type === 'refresh') {
        this.$emit('call', type)
      } else if (type === 'refreshDeploy') {
        this.$emit('call', type)
      }
    }
  }
}
</script>
