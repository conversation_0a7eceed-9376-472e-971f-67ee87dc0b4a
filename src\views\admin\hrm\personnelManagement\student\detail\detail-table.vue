<template>
  <div class="resource-table container">
    <category :course-type="courseType" :learn-type="learnType" style="margin-bottom: 15px;" @category-query="categoryQuery"/>
    <div style="margin: 0 15px 15px;" class="operation-wrap" >
      <div class="operation-left">
        <slot name="action" />
        <el-button type="primary" icon="el-icon-refresh" @click="refresh" />
      </div>
      <div class="operation-right">
        <el-badge :value="searchBtnShowNum">
          <el-button icon="el-icon-search" @click="openSearch">搜索</el-button>
        </el-badge>
        <!-- 自定义表格列 -->
        <t-table-config
          v-if="!customColData.length"
          :data="columnsObj"
          :active-key-arr="columnsViewArr"
          @on-change-col="onChangeCol"
        />
      </div>
    </div>
    <!-- 搜索区 -->
    <t-search-box
      v-show="searchView"
      :search-key-list="searchKeyListView"
      default-placeholder="默认搜索名称"
      class="ml-15 mr-15"
      @search="searchMultiple"
    />
    <!-- 列表 -->
    <t-table-view
      ref="tableView"
      :height="height"
      :single="false"
      :loading="tableLoading"
      :data="tableData"
      :total="tableTotal"
      :page-size="pageSize"
      :current="pageCurrent"
      :select-item="selectItem"
      type="list"
      current-key="courseId"
      style="padding: 0 15px;"
      @on-select="onSelect"
      @on-current="onCurrent"
      @on-change="changePage"
      @on-sort-change="onSortChange"
      @on-page-size-change="onPageSizeChange"
    >
      <el-table-column v-for="item in columnsViewArr" :key="item" :min-width="colMinWidth" :label="columnsObj[item].title" :fixed="columnsObj[item].master ? 'left' : false" show-overflow-tooltip>
        <template slot-scope="scope">
          <span>{{ scope.row[item] || "-" }}</span>
        </template>
      </el-table-column>
    </t-table-view>
  </div>
</template>
<script>
import category from './category'
import tSearchBox from '@/packages/search-box/index.vue'
import tTableView from '@/packages/table-view/index.vue'
import tTableConfig from '@/packages/table-config/table-col-config.vue'
import tableTdMultiCol from '@/packages/table-config/table-td-multi-col.vue'
import mixinsPageTable from '@/packages/mixins/page_table'
import { queryLearnProcess, classRequiredProcess, studentRequiredProcess } from '@/api/admin/training/student'
export default {
  components: {
    tSearchBox,
    category,
    tTableView,
    tableTdMultiCol,
    tTableConfig
  },
  mixins: [mixinsPageTable],
  data() {
    return {
      moduleName: 'process',
      // 搜索配置项
      searchKeyList: [
        { key: 'courseName', label: '名称', master: true }
      ],
      // 所有可配置显示列 master：不可隐藏 title:列名称
      columnsObj: {
        'courseName': {
          title: '名称',
          master: true
        },
        'courseCategoryName': {
          title: '分类'
        },
        'finishTime': {
          title: '完成时间'
        },
        'score': {
          title: '成绩'
        }

      },
      // 当前显示列key表 默认，如果localStorage有数据将被覆盖
      columnsViewArr: [
        'courseName',
        'courseCategoryName',
        'finishTime',
        'score'
      ],
      courseType: 0,
      learnType: 0,
      requestAPI: classRequiredProcess,
      processId: this.$route.query.processId || ''
    }
  },
  mounted() {
    this.courseType = 0
    this.learnType = 0
  },
  methods: {
    getList: function(showLoading = true) {
      this.tableData = []
      if (showLoading) {
        this.tableLoading = true
      }
      if (this.processId) {
        const params = { userId: this.processId, ...this.getPostData('page', 'limit') }
        this.requestAPI(params).then((res) => {
          if (res.code == 0) {
            this.tableData = res.data.records || []
            this.tableTotal = res.data.total || 0
            if (this.learnType == 1) {
              this.tableData.map((item) => {
                item.score = item.score === 0 ? '0' : item.score
              })
            }
          }
        }).finally(() => {
          this.tableLoading = false
        })
      }
    },
    categoryQuery(obj) {
      this.courseType = obj.courseType
      this.learnType = obj.learnType
      if (obj.learnType == 0) {
        if (obj.courseType == 0) {
          this.requestAPI = classRequiredProcess
        } else if (obj.courseType == 1) {
          this.requestAPI = studentRequiredProcess
        }
      } else if (obj.learnType == 1) {
        this.requestAPI = queryLearnProcess
      }
      this.getList()
    }
  }
}
</script>
<style lang="scss" scoped>
.resource-table {
  padding: 0;
}
/deep/.layout-table-wrap {
  height: 100%;
}
.container {
  padding: 0 0 10px 0 !important;
}
</style>
