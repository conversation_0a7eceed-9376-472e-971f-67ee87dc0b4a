<template>
  <div class="buttons-wrap">
    <!-- 中部弹窗 start-->
    <el-dialog
      :title="titleMapping[modalName]"
      :visible.sync="modalShow"
      :width="modalWidth"
      :destroy-on-close="true"
      append-to-body
      @close="modalClose"
    >
      <transition name="el-fade-in-linear">
        <component
          :is="modalName"
          :name="modalName"
          :data="selectItem"
          :search-name="searchName"
          @close="modalClose"
          @call="confirmCall"
        />
      </transition>
    </el-dialog>
    <!-- 中部弹窗 end-->
  </div>
</template>
<script>
import mixinsActionMenu from '@/packages/mixins/action_menu.js'

export default {
  components: {
  },
  mixins: [mixinsActionMenu],
  props: {
    searchName: {
      type: Object
    }
  },
  data() {
    return {
      // 弹窗title映射
      titleMapping: {
      },
      userId: ''
    }
  },
  computed: {
    isAdmin() {
      return this.$store.state.user.userInfo.roleId == '180162'
    }
  },
  mounted() {
    this.userId = this.$store.state.user.userInfo.userId
  },
  methods: {
    confirmCall: function(type, data) {
      if (type === 'close') {
        this.modalClose()
      } else if (type === 'refresh') {
        this.$emit('call', type)
      }
    }
  }
}
</script>
