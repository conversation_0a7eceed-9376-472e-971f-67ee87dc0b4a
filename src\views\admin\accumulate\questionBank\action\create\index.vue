<template>
  <create-view :title="`${editMode ? '编辑' : '创建'}题目`">
    <div v-loading="loading" v-if="active === 0" slot="content" class="create-question-base">
      <el-steps v-if="bankType == 3" :active="active" simple style="width: 80%;">
        <el-step title="基本配置" />
        <el-step title="拓扑编排" />
      </el-steps>
      <div class="step-wrap">
        <el-alert v-if="istips" :closable="false" type="warning" style="margin-bottom:10px">
          <div slot="title">
            <p>当前题目已被引用，请谨慎操作！</p >
          </div>
        </el-alert>
        <base-config ref="baseConfig" :base-data="baseData" @set-value="setValue" />
      </div>
    </div>
    <div v-loading="loading" v-if="active === 1" slot="topo" class="create-question-topo">
      <el-steps v-if="bankType == 3" :active="active" simple style="width: 57.5%;">
        <el-step title="基本配置" />
        <el-step title="拓扑编排" />
      </el-steps>
      <div class="step-wrap">
        <topo-config ref="topoConfig" :topology-id="topologyId" />
      </div>
    </div>
    <div slot="footer">
      <el-button type="text" @click="$router.go(-1)">取消</el-button>
      <el-button v-if="bankType == 3 && active === 1" type="primary" @click="active = 0">上一步</el-button>
      <el-button v-if="bankType == 3 && active === 0" :disabled="!canOpt" type="primary" @click="next()">下一步</el-button>
      <el-button v-if="bankType != 3 || active === 1" :disabled="!canOpt" type="primary" @click="confirm()">确定</el-button>
    </div>
  </create-view>
</template>
<script>
import createView from '@/packages/create-view/index'
import baseConfig from './base-config'
import topoConfig from './topo-config'
import { createQuestionBank, updateQuestionBank, getQuestionBankItem } from '@/api/accumulate/questionBank'

export default {
  components: {
    createView,
    baseConfig,
    topoConfig
  },
  data() {
    return {
      loading: false,
      editMode: false,
      active: 0,
      bankType: 1, // 题库类型: 1-理论 2-靶机 3-仿真
      baseData: null,
      topologyId: '',
      canOpt: true, // 是否能编辑
      istips: false
    }
  },
  created() {
    this.bankType = this.$route.params.bankType
    this.editMode = !!this.$route.params.id
    if (this.$route.params.id) {
      this.getQuestionBankItem(this.$route.params.id)
    }
  },
  methods: {
    // 编辑时候获取数据
    'getQuestionBankItem': function(id) {
      return new Promise((resolve, reject) => {
        getQuestionBankItem({ id }).then(res => {
          this.istips = res.data.citedNum > 0
          resolve()
        }).catch(() => {
          reject()
        })
      })
    },
    'setValue': function(key, value) {
      this[key] = value
    },
    'next': function() {
      this.$refs['baseConfig'].confirm((postData) => {
        this.loading = true
        const postType = (this.editMode || this.baseData) ? updateQuestionBank : createQuestionBank
        if (this.editMode || this.baseData) {
          postData['id'] = this.$route.params.id || this.baseData.id
        }
        console.log(postData)
        postType(postData).then(res => {
          this.baseData = postData
          this.baseData['id'] = res.data.id
          this.topologyId = res.data.topologyTemplateId
          this.active = 1
          this.loading = false
        }).catch(() => {
          this.loading = false
        })
      })
    },
    'confirm': function() {
      if (this.bankType != 3) {
        this.$refs['baseConfig'].confirm((postData) => {
          const postType = this.editMode ? updateQuestionBank : createQuestionBank
          if (this.editMode) {
            postData['id'] = this.$route.params.id
          }
          this.loading = true
          postType(postData).then(res => {
            this.$message.success(`${this.editMode ? '编辑' : '创建'}题目成功`)
            this.$router.go(-1)
          }).catch(() => {
            this.loading = false
          })
        })
      } else {
        if (this.$refs.topoConfig.$refs.topo.graph.getCells().length) {
          this.loading = true
          this.$refs.topoConfig.$refs.topo.saveAs('editTopology')
          this.$message.success(`${this.editMode ? '编辑' : '创建'}题目成功`)
          this.$router.go(-1)
        } else {
          this.$message.error(`拓扑不能为空`)
        }
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.create-question-base, .create-question-topo {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  /deep/ .el-steps {
    margin-bottom: 12px;
  }
  .step-wrap {
    flex: 1;
    width: 100%;
    min-height: 0;
    overflow: hidden;
  }
}
.create-question-topo {
  /deep/ .el-steps {
    margin-top: 0px !important;
    margin-bottom: 12px !important;
  }
  .orchestration-create-warp {
    height: 100% !important;
  }
}
</style>
