import { adminSystemIndexAPI } from '@/api/admin/config'
import Lockr from 'lockr'

const app = {
  state: {
    theme: localStorage.getItem('theme') || 'default',
    logo: '',
    name: '',
    lang: localStorage.lang || 'cn',
    sidebar: {
      collapse: Lockr.get('sideBarCollapse') || false
    },
    websiteInfo: {}, // 网站基本信息
    prohibit: false // 是否禁止界面操作（考试和直播使用）
  },

  mutations: {
    SET_THEME: (state, theme) => {
      state.theme = theme
      localStorage.setItem('theme', theme)
    },
    SET_COLLAPSE: (state, collapse) => {
      state.sidebar.collapse = collapse
      Lockr.set('sideBarCollapse', collapse)
    },
    SET_APPLOGO: (state, logo) => {
      state.logo = logo
    },
    SET_APPNAME: (state, name) => {
      state.name = name
    },
    SET_LANG: (state, lang) => {
      state.lang = lang
      window.app.$i18n.locale = lang
      localStorage.setItem('lang', lang)
      window.location.reload()
    },
    SET_PROHIBIT: (state, value) => {
      state.prohibit = value
    },
    SET_WEBSITEINFO: (state, value) => {
      state.websiteInfo = value
    }
  },

  actions: {
    setTheme({ commit, dispatch }, theme) {
      commit('SET_THEME', theme)
    },
    // 登录
    SystemLogoAndName({
      commit
    }) {
      return new Promise((resolve, reject) => {
        adminSystemIndexAPI().then(response => {
          const resData = response.data || {}
          commit('SET_APPNAME', resData.companyName)
          commit('SET_APPLOGO', resData.companyLogo)
          Lockr.set('systemLogo', resData.companyLogo)
          Lockr.set('systemName', resData.companyName)
          resolve(response)
        }).catch(error => {
          reject(error)
        })
      })
    }
  }
}

export default app
