<template>
  <el-dialog
    :visible.sync="visible"
    :title="title"
    :width="width"
    append-to-body
    @closed="$emit('close')"
  >
    <div class="dialog-wrap">
      <el-form ref="categoryForm" :model="categoryForm" :rules="categoryRules" style="padding:0;" label-width="80px" class="category-form">
        <el-form-item label="目录分类" prop="categoryId">
          <el-select
            ref="categorySelect"
            v-model="categoryForm.categoryName"
            :popper-append-to-body="false"
            placeholder="请选择"
            clearable
            class="category-select-tree"
            @clear="clearCategorySelect"
          >
            <el-option :value="categoryForm.categoryName" class="tree-select-option" disabled>
              <div class="tree-select-container">
                <div class="tree-filter-fixed">
                  <el-input
                    v-model="categoryFilterText"
                    class="tree-filter-input"
                    size="mini"
                    placeholder="按名称搜索"
                    clearable
                  />
                </div>
                <div class="tree-container-scroll">
                  <Tree
                    ref="categoryTree"
                    :tree-data="filteredCategoryList"
                    :default-props="{ children: 'children', label: 'name' }"
                    :is-show-dropdown="false"
                    :i-search="false"
                    :is-show-data-count="true"
                    :is-show-children-num="false"
                    node-key="id"
                    @currentTreeNode="handleCurrentTreeNode"
                  />
                </div>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div class="dialog-footer">
        <el-button type="text" @click="$emit('close')">取消</el-button>
        <el-button type="primary" @click="confirmCategory">确定</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import Tree from '@/packages/tree'

export default {
  name: 'CaseCategorySelect',
  components: {
    Tree
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '选择分类'
    },
    width: {
      type: String,
      default: '520px'
    },
    categoryList: {
      type: Array,
      default: () => []
    },
    currentCategory: {
      type: Object,
      default: () => null
    },
    type: {
      type: Number,
      default: 1 // 1: 测试用例, 2: 测试套件
    }
  },
  data() {
    return {
      categoryFilterText: '',
      categoryForm: {
        categoryId: '',
        categoryName: ''
      },
      categoryRules: {
        categoryId: [
          { required: true, message: '必填项', trigger: 'change' }
        ]
      },
      currentNodeData: null
    }
  },
  computed: {
    // 过滤后的分类列表
    filteredCategoryList() {
      if (!this.categoryFilterText) {
        return this.categoryList
      }

      // 深度递归过滤树节点
      return this.filterTreeNodes(JSON.parse(JSON.stringify(this.categoryList)), this.categoryFilterText)
    }
  },
  watch: {
    visible(val) {
      if (val && this.currentCategory) {
        this.categoryForm.categoryId = this.currentCategory.id || ''
        this.categoryForm.categoryName = this.currentCategory.name || ''
      }
    },
    currentCategory: {
      handler(val) {
        if (val) {
          this.categoryForm.categoryId = val.id || ''
          this.categoryForm.categoryName = val.name || ''
        }
      },
      immediate: true
    }
  },
  methods: {
    // 递归过滤树节点
    filterTreeNodes(treeData, keyword) {
      if (!treeData || !Array.isArray(treeData)) {
        return []
      }

      return treeData.filter(node => {
        // 检查当前节点是否匹配关键字
        const isMatch = node.name && node.name.indexOf(keyword) !== -1

        // 递归过滤子节点
        if (node.children && node.children.length) {
          node.children = this.filterTreeNodes(node.children, keyword)
          // 如果子节点有匹配项，也保留父节点
          return isMatch || node.children.length > 0
        }

        // 对于叶子节点，直接返回是否匹配
        return isMatch
      })
    },

    // 清除分类选择
    clearCategorySelect() {
      this.categoryForm.categoryId = ''
      this.categoryForm.categoryName = ''
      this.currentNodeData = null
    },

    // 获取当前选中的节点数据
    handleCurrentTreeNode(data, node) {
      this.currentNodeData = data
      this.categoryForm.categoryId = data.id
      this.categoryForm.categoryName = data.name
      // 关闭下拉框
      this.$nextTick(() => {
        this.$refs.categorySelect && this.$refs.categorySelect.blur()
      })
    },

    // 确认分类选择
    confirmCategory() {
      let selectedCategory = null

      if (this.categoryForm.categoryId) {
        selectedCategory = {
          id: this.categoryForm.categoryId,
          name: this.categoryForm.categoryName
        }
      } else if (this.currentNodeData) {
        selectedCategory = {
          id: this.currentNodeData.id,
          name: this.currentNodeData.name
        }
      }

      if (selectedCategory) {
        this.$emit('confirm', selectedCategory)
      } else {
        this.$message.warning('请选择分类')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-wrap {
  padding: 0 20px;
}

.category-select-tree {
  width: 100%;
}

.tree-select-option {
  padding: 0;
  height: auto;
}

.tree-select-container {
  display: flex;
  flex-direction: column;
  max-height: 255px;
}

.tree-filter-fixed {
  position: sticky;
  top: 0;
  z-index: 1;
  background-color: #fff;
  padding: 5px;
}

.tree-filter-input {
  width: 100%;
  margin-bottom: 5px;
}

.tree-container-scroll {
  flex: 1;
  overflow-y: auto;
  padding: 0 5px;
}

.category-form {
  margin-top: 15px;
}
</style>
