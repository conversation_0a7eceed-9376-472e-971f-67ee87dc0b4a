<template>
  <el-row :gutter="20">
    <el-col :span="12">
      <detail-card title="基本信息">
        <el-form slot="content" label-position="left" label-width="110px">
          <el-form-item label="名称：">{{ data.questionName || '-' }}</el-form-item>
          <el-form-item label="题库：">{{ data.questionDepotName || '-' }}</el-form-item>
          <el-form-item label="分类：">{{ data.categoryName || '-' }}</el-form-item>
          <el-form-item label="难度：">{{ questionConf.complexityObj[data.complexity].label }}</el-form-item>
          <el-form-item label="用途：">
            <el-tag v-for="item in data.uses.split(',')" :key="item" style="margin-right: 5px;">{{ questionConf.usesObj[item].label }}</el-tag>
          </el-form-item>
          <el-form-item label="知识点：">
            <el-tag v-for="item in data.knowledgeVos" :key="item.knowledgeCode" style="margin:0 5px 5px 0;">{{ item.knowledgeName }}</el-tag>
          </el-form-item>
          <el-form-item label="技能点：">
            <el-tag v-for="item in data.skillPointsBOS" :key="item.id" style="margin:0 5px 5px 0;">{{ item.name }}</el-tag>
          </el-form-item>
          <el-form-item label="题型：">{{ questionConf.questionTypObj[data.questionType].label }}</el-form-item>
          <template v-if="data.questionType != 10">
            <el-form-item label="题干：">
              <myEditor
                key="content"
                :content="data.content"
                :only-editor="true"
                :is-read-only="true"
                id-prefix="content"
                width="100%"
                height="200px"
              />
            </el-form-item>
            <!-- 单选、多选 -->
            <template v-if="data.questionType == 1 || data.questionType == 2">
              <el-form-item
                v-for="(item, index) in JSON.parse(data.questionOptions)"
                :key="item"
                :label="`选项${radioMap[index]}：`"
              >{{ item }}</el-form-item>
              <el-form-item label="答案">{{ data.questionAnswer || '-' }}</el-form-item>
            </template>
            <!-- 判断 -->
            <el-form-item v-else-if="data.questionType == 3" label="答案">
              {{ data.questionAnswer == 'A' ? '正确' : '错误' }}
            </el-form-item>
            <!-- CTF题、AWD、其他 -->
            <template v-else>
              <el-form-item :label="`${data.bankType == 2 ? 'flag' : '答案'}：`">{{ data.questionAnswer || '-' }}</el-form-item>
            </template>
            <el-form-item v-if="data.questionType == 4 || data.questionType == 5 || data.questionType == 6 || data.questionType == 9" label="附件：">
              <a v-if="data.fileUrl && data.fileName" :href="data.fileUrl" :download="data.fileName">{{ data.fileName }}</a>
              <span v-else>-</span>
            </el-form-item>
            <el-form-item label="人工判分：">{{ questionConf.manualScoringObj[data.manualScoring].label }}</el-form-item>
            <el-form-item label="上传解题方法：">{{ questionConf.solutionApproachObj[data.solutionApproach] ? questionConf.solutionApproachObj[data.solutionApproach].label : '-' }}</el-form-item>
            <el-form-item label="题目解析：">
              <myEditor
                key="questionAnalysis"
                :content="data.questionAnalysis"
                :only-editor="true"
                :is-read-only="true"
                id-prefix="questionAnalysis"
                width="100%"
                height="200px"
              />
            </el-form-item>
          </template>
          <template v-if="data.questionType == 10">
            <el-card v-for="(comp, index) in data.combinationQuestionBOS" :key="index" class="mb-10">
              <!-- 综合题N -->
              <el-form-item :label="`综合题${index + 1}`">
                <myEditor
                  :key="'question' + index + new Date().getTime()"
                  :content="comp.questionName"
                  :only-editor="true"
                  :is-read-only="true"
                  :id-prefix="`question${index}`"
                  width="100%"
                  height="200px"
                />
              </el-form-item>
              <!-- 题目N -->
              <el-form-item
                v-for="(con, subIndex) in JSON.parse(comp.content)"
                :key="subIndex"
                :label="`题目${subIndex + 1}`"
              >
                <myEditor
                  :key="'content' + index + subIndex + new Date().getTime()"
                  :content="con"
                  :only-editor="true"
                  :is-read-only="true"
                  :id-prefix="`content${index}${subIndex}`"
                  width="100%"
                  height="200px"
                />
              </el-form-item>
              <el-form-item label="参考答案">
                <a v-if="comp.fileUrl && comp.fileName" :href="comp.fileUrl" :download="comp.fileName">{{ comp.fileName }}</a>
                <span v-else>-</span>
              </el-form-item>
              <el-form-item label="题目解析">
                <myEditor
                  :key="'analysis' + index + new Date().getTime()"
                  :content="comp.questionAnalysis"
                  :only-editor="true"
                  :is-read-only="true"
                  :id-prefix="`analysis${index}`"
                  width="100%"
                  height="200px"
                />
              </el-form-item>
            </el-card>
          </template>
          <el-form-item v-if="data.bankType == 3" label="拓扑是否可见">
            <el-switch
              v-model="data.topologyVisible"
              :active-value="1"
              :inactive-value="0"
              disabled
            />
            <span class="ml-5">关闭后，答题者将不可见拓扑</span>
          </el-form-item>
          <tamplate v-if="data.bankType == 2">
            <el-form-item label="靶机：">
              <el-tag>{{ data.networkElementName }}</el-tag>
            </el-form-item>
            <el-form-item label="flag路径：">{{ data.flagPath || '-' }}</el-form-item>
            <el-form-item label="动态flag">
              <el-switch
                v-model="data.flagRefresh"
                :active-value="1"
                :inactive-value="0"
                disabled
              />
            </el-form-item>
            <el-form-item label="选手加固账号：">{{ data.playerAccount || '-' }}</el-form-item>
            <el-form-item label="题目检测脚本：">
              <a v-if="data.checkFileUrl && data.checkFileName" :href="data.checkFileUrl" :download="data.checkFileName">{{ data.checkFileName }}</a>
              <span v-else>-</span>
            </el-form-item>
            <el-form-item label="题目更新脚本">
              <a v-if="data.updateFileUrl && data.updateFileName" :href="data.updateFileUrl" :download="data.updateFileName">{{ data.updateFileName }}</a>
              <span v-else>-</span>
            </el-form-item>
            <el-form-item label="题目漏洞内容：">
              <template v-if="data.questionHoleRelationVOList && data.questionHoleRelationVOList.length">
                <el-tag v-for="item in data.questionHoleRelationVOList" :key="item.id" style="margin-right: 5px;">{{ item.bugName }}</el-tag>
              </template>
              <span v-else>-</span>
            </el-form-item>
            <el-form-item v-if="data.questionType == 9" label="漏洞端口：">{{ data.questionHoleRelationVOList[0].bugPort }}</el-form-item>
            <el-form-item v-if="data.questionType == 4" label="漏洞端口：">{{ data.targetPort }}</el-form-item>
          </tamplate>
          <el-form-item label="创建时间：">{{ data.createTime || '-' }}</el-form-item>
        </el-form>
      </detail-card>
    </el-col>
    <el-col :span="12">
      <detail-card title="关联试卷">
        <!-- 列表 -->
        <t-table-view
          ref="tableView"
          slot="content"
          :loading="tableLoading"
          :data="tableData"
          :total="tableTotal"
          :page-size="pageSize"
          :current="pageCurrent"
          :select-item="selectItem"
          height="null"
          type="list"
        >
          <el-table-column label="名称" fixed="left" show-overflow-tooltip>
            <template slot-scope="scope">
              <a
                :href="`/baseResource/exam/detail/${scope.row.id}/overview`"
                @click.prevent="jumpExam(scope.row)"
              >{{ scope.row.examName || '-' }}</a>
            </template>
          </el-table-column>
          <el-table-column label="试卷分类" show-overflow-tooltip>
            <template slot-scope="scope">
              {{ scope.row.categoryName || '-' }}
            </template>
          </el-table-column>
          <el-table-column label="创建时间" show-overflow-tooltip>
            <template slot-scope="scope">
              {{ scope.row.createTime || '-' }}
            </template>
          </el-table-column>
        </t-table-view>
      </detail-card>
    </el-col>
  </el-row>
</template>

<script>
import questionConf from '../config.js'
import detailCard from '@/packages/detail-view/detail-card.vue'
import myEditor from '@/packages/editor/index.vue'
import tTableView from '@/packages/table-view/index'
import { queryExamPageByQues } from '@/api/accumulate/questionBank'
import mixinsPageTable from '@/packages/mixins/page_table'
export default {
  name: 'DetailOverview',
  components: {
    detailCard,
    myEditor,
    tTableView
  },
  mixins: [mixinsPageTable],
  props: {
    id: {
      type: [Number, String]
    },
    data: {
      type: Object
    }
  },
  data() {
    return {
      moduleName: 'exam',
      searchKeyList: [],
      // ['A', 'B', 'C', 'D', 'E', ...]
      radioMap: Array.from({ length: 26 }, (_, index) => String.fromCharCode('A'.charCodeAt(0) + index)),
      questionConf: questionConf
    }
  },
  created() {
    this.queryExamPageByQues(this.id)
  },
  methods: {
    // 通过题目ID查询被引用的试卷列表
    'queryExamPageByQues': function(id) {
      return new Promise((resolve, reject) => {
        queryExamPageByQues({ pageType: 0, id: id }).then(res => {
          this.tableData = res.data.records
          this.tableTotal = res.data.total
          resolve()
        }).catch(() => {
          this.tableData = []
          this.tableTotal = 0
          reject()
        })
      })
    },
    jumpExam(row) {
      this.$router.push({
        name: 'examDetail',
        params: { id: row.id, view: 'overview' }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.bug-content-title {
  display: flex;
  color: var(--neutral-600);
  font-weight: 500;
  margin-bottom: 12px;
}
.jump-exam {
  color: var(--color-600);
  cursor: pointer;
}
</style>
