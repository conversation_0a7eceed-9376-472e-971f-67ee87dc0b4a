<template>
  <div class="category-wrap">
    <transverse-list
      :data="statusArr"
      :allow-deletion="false"
      :allow-add="false"
      :module-name="categoryName + '_' + moduleName + '_type'"
      :cache-pattern="true"
      :all="true"
      :is-show-expand="false"
      v-bind="categoryProps"
      title="上课状态"
      @node-click="handleNodeClick($event, 'examType')"
    />
  </div>
</template>
<script>
import transverseList from '@/packages/transverse-list/index.vue'
import module from './config.js'

export default {
  components: {
    transverseList
  },
  props: {
    examType: [String, Number],
    categoryName: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      moduleName: module.name,
      statusArr: module.status,
      categoryProps: {
        label: 'label',
        idName: 'value'
      },
      categoryQuery: {
        examType: this.examType
      }
    }
  },
  mounted() {
    console.log('statusArr', this.statusArr)
  },
  methods: {
    handleNodeClick(item, key) {
      this.categoryQuery[key] = item.value
      this.$emit('category-query', this.categoryQuery)
    }
  }
}
</script>
