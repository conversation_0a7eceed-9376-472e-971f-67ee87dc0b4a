<template>
  <div class="resource-table">
    <!-- 操作区 -->
    <div class="operation-wrap">
      <div class="operation-left">
        <el-button type="primary" icon="el-icon-refresh" @click="refresh" />
      </div>
      <div class="operation-right">
        <el-badge :value="searchBtnShowNum">
          <el-button icon="el-icon-search" @click="openSearch">搜索</el-button>
        </el-badge>
        <!-- 自定义表格列 -->
        <t-table-config
          v-if="!customColData.length"
          :data="columnsObj"
          :active-key-arr="columnsViewArr"
          @on-change-col="onChangeCol"
        />
      </div>
    </div>
    <!-- 搜索区 -->
    <t-search-box
      v-show="searchView"
      :search-key-list="searchKeyListView"
      default-placeholder="默认搜索申请人"
      @search="searchMultiple"
    />
    <!-- 列表 -->
    <t-table-view
      ref="tableView"
      :height="height"
      :single="single"
      :loading="tableLoading"
      :data="tableData"
      :total="tableTotal"
      :page-size="pageSize"
      :current="pageCurrent"
      :select-item="selectItem"
      type="list"
      current-key="id"
      @on-select="onSelect"
      @on-current="onCurrent"
      @on-change="changePage"
      @on-sort-change="onSortChange"
      @on-page-size-change="onPageSizeChange"
    >
      <el-table-column
        v-for="item in columnsViewArr"
        :key="item"
        :min-width="colMinWidth"
        :width="columnsObj[item].colWidth"
        :label="columnsObj[item].title"
        :fixed="columnsObj[item].master ? 'left' : false"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span v-if="item == 'sort'">
            {{ scope.$index + 1 }}
          </span>
          <div v-else-if="item == 'auditStatus'">
            <el-badge :type="statusMapping[scope.row[item]]" is-dot />
            {{ statusObj[scope.row[item]].label || '-' }}
          </div>
          <span v-else-if="item == 'option'">
            <el-link :underline="false" type="primary" @click.stop="handleDetail(scope.row)">查看详情</el-link>
          </span>
          <span v-else>{{ scope.row[item] || "-" }}</span>
        </template>
      </el-table-column>
    </t-table-view>
  </div>
</template>
<script>
import tSearchBox from '@/packages/search-box/index.vue'
import mixinsPageTable from '@/packages/mixins/page_table'
import mixinsActionMenu from '../../delayApply/table/action_menu.js'
import tTableView from '@/packages/table-view/index.vue'
import tTableConfig from '@/packages/table-config/table-col-config.vue'
import tableTdMultiCol from '@/packages/table-config/table-td-multi-col.vue'
import module from '../config.js'
import { getSampleTestApplyPage } from '@/api/testing/index'
import statusConf from '../../config.js'
export default {
  components: {
    tSearchBox,
    tTableView,
    tTableConfig,
    tableTdMultiCol
  },
  mixins: [mixinsPageTable, mixinsActionMenu],
  props: {
    projectId: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    return {
      moduleName: module.name,
      statusObj: statusConf.statusObj,
      statusMapping: statusConf.statusMapping,
      titleMapping: {
      },
      drawerAction: [],
      drawerData: {},
      searchKeyList: [
        { key: 'applyName', label: '申请人', placeholder: '请输入申请人', master: true },
        { key: 'auditStatus', label: '审核状态', type: 'select', placeholder: '请选择审核状态', valueList: statusConf.statusArr },
        { key: 'applyTime', label: '申请时间', type: 'time_range', placeholder: '请选择申请时间' }
      ],
      columnsObj: {
        'sort': {
          title: '序号', master: true, colWidth: 50
        },
        'applyName': {
          title: '申请人', master: true
        },
        'applyTime': {
          title: '申请时间'
        },
        'auditStatus': {
          title: '审核状态'
        },
        'option': {
          title: '操作'
        }
      },
      columnsViewArr: [
        'sort',
        'applyName',
        'applyTime',
        'auditStatus',
        'option'
      ]
    }
  },
  mounted() {

  },
  methods: {
    getList: function(showLoading = true) {
      if (showLoading) {
        this.tableLoading = true
      }

      // 构建查询参数
      const params = this.getPostData('page', 'limit')
      params.pageType = 1 // 分页
      params.projectId = this.projectId || this.$route.params.id

      // 处理日期范围参数
      if (params.applyTime) {
        const dateRange = params.applyTime.split(',')
        if (dateRange.length === 2) {
          params.applyTimeBegin = dateRange[0]
          params.applyTimeEnd = dateRange[1]
        }
        delete params.applyTime
      }

      // 调用接口获取数据
      getSampleTestApplyPage(params).then((res) => {
        if (res.data && res.data.code === 0) {
          // 处理返回的数据
          this.tableData = res.data.data ? res.data.data.records : []
          this.tableTotal = res.data.data ? res.data.data.total : 0
        }
        this.tableLoading = false
        this.handleSelection()
      }).catch(() => {
        this.tableLoading = false
        this.tableData = []
        this.tableTotal = 0
      })
    },
    handleDetail(data) {
      const params = {
        showDrawer: true,
        id: data.id,
        projectId: this.projectId || this.$route.params.id
      }
      this.$emit('show-drawer-detail', params)
    }
  }
}
</script>
<style lang="scss" scoped>
.resource-table {
  height: 100%;
  padding: 15px 0 0 0;
}
</style>
