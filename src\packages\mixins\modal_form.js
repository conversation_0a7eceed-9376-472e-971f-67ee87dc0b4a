// 父级组件必须传入参数
// ++++++ show: modal显示状态 Boolean类型

// 组件内必须声明方法
// ------ open: modal显示前被调用
// ------ submit: 点击确定被调用
// ------ close: 点击关闭取消调用

export default {
  props: {
    // 传入modal显示状态
    show: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    // 监听modal显示状态
    show: function(val) {
      if (val) {
        this.open()
      } else {
        this.close()
      }
    }
  },
  mounted() {
    // 上层模态框对象
    const modal = this.$parent
    // 组件form提交方法
    const submit = this.submit
    // 上次模态框点击确定触发表单提交
    modal.ok = function() {
      submit(this)
    }
  }
}
