const defaultTheme = 'green' // 默认主题（绿色）
import { chalkCss } from './chalk.js'
export default {
  data() {
    return {
      chalk: '',
      themeColor: '', // 默认主题色号（绿色）
      themeKey: '', // 默认主题key（绿色）
      colorMap: { // 支持的主题色号Map
        'green': '#069072',
        'blue': '#005BD4'
      }
    }
  },
  watch: {
    themeColor(val) {
      const oldVal = this.chalk ? this.themeColor : this.colorMap[defaultTheme]
      if (typeof val !== 'string') return
      const themeCluster = this.getThemeCluster(val.replace('#', ''))
      const originalCluster = this.getThemeCluster(oldVal.replace('#', ''))

      const colors = require('../theme/' + this.themeKey).default
      Object.keys(colors).forEach(function(key) {
        document.body.style.setProperty(key, colors[key])
      })

      const getHandler = (variable, id) => {
        return () => {
          const originalCluster = this.getThemeCluster(this.colorMap[defaultTheme].replace('#', ''))
          const newStyle = this.updateStyle(this[variable], originalCluster, themeCluster)

          let styleTag = document.getElementById(id)
          if (!styleTag) {
            styleTag = document.createElement('style')
            styleTag.setAttribute('id', id)
            document.head.appendChild(styleTag)
          }
          styleTag.innerText = newStyle
        }
      }

      if (!this.chalk) {
        this.chalk = chalkCss
          .replace(/@font-face{[^}]+}/, '')
          .replace(/.el-icon-[a-zA-Z0-9-:^]+before{content:"[^}]+}/g, '')
          // 多选下拉框对号图标不展示问题
          .replace(/.el-select-dropdown\.is-multiple \.el-select-dropdown__item\.selected::after{[^}]+}/g, '')
      }

      const chalkHandler = getHandler('chalk', 'chalk-style')

      chalkHandler()

      const styles = [].slice.call(document.querySelectorAll('style'))
        .filter(style => {
          const text = style.innerText
          return new RegExp(oldVal, 'i').test(text) && !/Chalk Variables/.test(text)
        })
      styles.forEach(style => {
        const { innerText } = style
        if (typeof innerText !== 'string') return
        style.innerText = this.updateStyle(innerText, originalCluster, themeCluster)
      })
    }
  },
  methods: {
    updateStyle(style, oldCluster, newCluster) {
      let newStyle = style
      oldCluster.forEach((color, index) => {
        newStyle = newStyle.replace(new RegExp(color, 'ig'), newCluster[index])
      })
      return newStyle
    },
    getThemeCluster(themeColor) {
      const tintColor = (color, tint) => {
        let red = parseInt(color.slice(0, 2), 16)
        let green = parseInt(color.slice(2, 4), 16)
        let blue = parseInt(color.slice(4, 6), 16)

        if (tint === 0) { // when primary color is in its rgb space
          return [red, green, blue].join(',')
        } else {
          red += Math.round(tint * (255 - red))
          green += Math.round(tint * (255 - green))
          blue += Math.round(tint * (255 - blue))

          red = red.toString(16)
          green = green.toString(16)
          blue = blue.toString(16)

          return `#${red}${green}${blue}`
        }
      }

      const shadeColor = (color, shade) => {
        let red = parseInt(color.slice(0, 2), 16)
        let green = parseInt(color.slice(2, 4), 16)
        let blue = parseInt(color.slice(4, 6), 16)

        red = Math.round((1 - shade) * red)
        green = Math.round((1 - shade) * green)
        blue = Math.round((1 - shade) * blue)

        red = red.toString(16)
        green = green.toString(16)
        blue = blue.toString(16)

        return `#${red}${green}${blue}`
      }

      const clusters = [themeColor]
      for (let i = 0; i <= 9; i++) {
        clusters.push(tintColor(themeColor, Number((i / 10).toFixed(2))))
      }
      clusters.push(shadeColor(themeColor, 0.1))
      return clusters
    }
  },
  created() {
    const CONFIG = window.ADMIN_CONFIG || window.WEB_CONFIG
    if (CONFIG.THEME && CONFIG.THEME !== defaultTheme && this.colorMap[CONFIG.THEME]) {
      this.themeKey = CONFIG.THEME
      this.themeColor = this.colorMap[CONFIG.THEME]
    }
  }
}
