<template>
  <div class="drawer-wrap">
    <add-content-data
      ref="table"
      :custom-col-data="['realname', 'sex', 'username']"
      :filter-data="{'userId': ''}"
      :height="null"
      :link="false"
      :single="false"
      :class-code="classCode"
      :not-allowed-arr="notAllowedArr"
      @on-select="onSelect"
    />
    <div class="drawer-footer">
      <div>
        <el-button :disabled="!selectedItem.length" type="primary" @click="confirm">确定</el-button>
        <el-button type="text" @click="close">取消</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import addContentData from '../addContentData/index.vue'
import { saveClassDetail } from '@/api/admin/training/student'
export default {
  components: {
    addContentData
  },
  props: {
    classCode: String
  },
  data() {
    return {
      selectedItem: [],
      notAllowedArr: []
    }
  },
  methods: {
    'onSelect': function(data) {
      this.selectedItem = data
    },
    'close': function() {
      this.$emit('close')
    },
    saveContentJoinCourseMul(postData) {
      return new Promise((resolve, reject) => {
        saveClassDetail(postData).then(res => {
          resolve(res)
        })
      })
    },
    confirm: function() {
      this.loading = true
      const idArr = this.selectedItem.map(item => {
        return { userId: item.userId, realname: item.realname }
      })
      idArr.map((item, index) => {
        this.saveContentJoinCourseMul({ userId: item.userId, classCode: this.$route.params.classCode, majorCode: this.$route.params.majorCode, realname: item.realname })
          .then((res) => {
            this.$message.success(res.data.object)
          })
      })
      setTimeout(() => {
        this.$emit('call', 'refresh')
        this.close()
        this.loading = false
      }, 500)
    }
  }
}
</script>
<style lang="scss" scoped>
.drawer-footer{
    display: flex;
    align-items: center;
    height: 8%;
}
</style>
