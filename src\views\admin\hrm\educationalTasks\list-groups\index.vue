<template>
  <div class="content-wrap-layout">
    <topNav style="margin-top: 26px;"/>
    <page-table
      ref="table"
      :default-selected-arr="defaultSelectedArr"
      @refresh="refresh"
      @link-event="linkEvent"
      @on-select="tabelSelect"
      @on-current="tabelCurrent"
      @changeClass="changeClass"
    />
  </div>
</template>

<script>
import moduleConf from './config'
import pageTable from './table/index.vue'
import topNav from '../index_top_nav'
export default {
  // 教师管理
  name: 'ContentLibrary',
  components: {
    pageTable,
    topNav
  },
  data() {
    return {
      moduleName: moduleConf.name,
      selectItem: [],
      defaultSelectedArr: [],
      changeData: JSON.parse(localStorage.getItem(`manageAffairs_resultList`) || '[]')
    }
  },
  watch: {
    '$route': function(to, from) {
      this.changeData = JSON.parse(localStorage.getItem(`manageAffairs_resultList`) || '[]')
    }
  },
  methods: {
    // 列表点击
    linkEvent: function({ name, row, params }) {
      this.$router.push({ name: name, params: params })
    },
    // 返回已选
    tabelSelect: function(data) {
      this.selectItem = data
    },
    // 返回单选
    tabelCurrent: function(row) {
      this.selectItem = [row]
    },
    // action menu 事件
    actionHandler: function(type, data) {
      switch (type) {
        case 'refresh':
          this.$refs['table'].getList()
      }
    },
    refresh: function() {},
    changeClass: function(data) {
      this.changeData = data
      this.$bus.$emit('headTeacherEventClassChange', this.changeData)
      this.$refs['table'].getList(true, data)
    }
  }
}
</script>
