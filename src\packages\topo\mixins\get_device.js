import { getCategory } from '../api/category'
import { getList } from '../api/physical_device'
import { mapState, mapActions } from 'vuex'
import _ from 'lodash'

export default {
  computed: {
    ...mapState('device', {
      'category_list': state => _.cloneDeep(state.category_list),
      'device_list': state => _.cloneDeep(state.device_list)
    })
  },
  methods: {
    ...mapActions('device', [
      'setState'
    ]),
    // 获取分类
    'get_category_list': function() {
      return new Promise((resolve, reject) => {
        const postData = {}
        getCategory(postData)
          .then(res => {
            this.setState({ key: 'category_list', val: res['data']['data']['result'] })
            resolve()
          })
          .catch((error) => {
            console.log(error)
            reject()
          })
      })
    },
    // 获取设备
    'get_device_list': function() {
      return new Promise((resolve, reject) => {
        const postData = {}
        getList(postData)
          .then(res => {
            // 过滤掉不参与组网的设备
            res['data']['data']['result'] = res['data']['data']['result'].filter(item => item.networking_capability)
            this.setState({ key: 'device_list', val: res['data']['data']['result'] })
            resolve()
          })
          .catch((error) => {
            console.log(error)
            reject()
          })
      })
    }
  }
}
