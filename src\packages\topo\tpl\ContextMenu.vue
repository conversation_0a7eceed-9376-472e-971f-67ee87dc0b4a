<template>
  <div :style="{'left': x + 'px', 'top': y + 'px' }" class="orchestration-context-menu">
    <template v-if="type === 'batch'">
      <template v-if="detailType !== 'consolePermissions' && detailType !=='readPermissions' && detailType !== 'firingPermissions' && detailType !== 'templatePermissions' && detailType !== 'examinationPermissions'">
        <div :class="{'is-disabled': !batchStartAvailable}" @click.stop="clickDrop('batchStart')">
          <i class="el-icon-video-play"/>批量开机
        </div>
        <div :class="{'is-disabled': !batchStopAvailable}" @click.stop="clickDrop('batchStop')">
          <i class="el-icon-switch-button"/>批量关机
        </div>
        <div :class="{'is-disabled': !batchRebootAvailable}" @click.stop="clickDrop('batchReboot')">
          <i class="el-icon-refresh-right"/>批量重启
        </div>
        <div :class="{'is-disabled': !batchSuspendAvailable}" @click.stop="clickDrop('batchSuspend')">
          <i class="el-icon-remove-outline"/>批量挂起
        </div>
        <div :class="{'is-disabled': !batchResumeAvailable}" @click.stop="clickDrop('batchResume')">
          <i class="el-icon-top-left"/>批量恢复
        </div>
        <div :class="{'is-disabled': !batchPauseAvailable}" @click.stop="clickDrop('batchPause')">
          <i class="el-icon-video-pause"/>批量暂停
        </div>
      </template>
      <template v-if="detailType === 'templatePermissions' || detailType === 'allPermissions' || (detailType == 'teamPermissions' && allowLinks === 1)">
        <div :class="{'is-disabled': false}" @click.stop="clickDrop('batchCopy')">
          <i class="el-icon-copy-document"/>批量复制
        </div>
      </template>
      <template v-if="detailType === 'templatePermissions' || detailType === 'allPermissions'">
        <!-- <div :class="{'is-disabled': false}" @click.stop="clickDrop('align', 'left')" >
          <i class="el-icon-copy-document"/>左对齐
        </div>
        <div :class="{'is-disabled': false}" @click.stop="clickDrop('align', 'right')" >
          <i class="el-icon-copy-document"/>右对齐
        </div>
        <div :class="{'is-disabled': false}" @click.stop="clickDrop('align', 'center')" >
          <i class="el-icon-copy-document"/>居中对齐
        </div> -->
        <div :class="{'is-disabled': false}" @click.stop="clickDrop('batchRemove')">
          <i class="el-icon-delete"/>批量删除
        </div>
      </template>
    </template>
    <template v-if="type === 'node'">
      <div v-if="!viewComponent.includes(nodeData.virtual_type)" @click.stop="clickDrop('viewConfig')" >
        <i class="el-icon-view"/>查看
      </div>
      <div v-if="!viewComponent.includes(nodeData.virtual_type) && (detailType === 'allPermissions' || detailType === 'templatePermissions')" @click.stop="clickDrop('config')">
        <i class="el-icon-edit"/>配置
      </div>
      <!-- <div v-if="!viewComponent.includes(nodeData.virtual_type)" :class="{'is-disabled': aboveAvailable}" @click.stop="foldUpAbove(node, node.data.retractAbove)">
        <i class="el-icon-caret-top"/>上方{{ node.data.retractAbove ? '展开' : '收起' }}
      </div>
      <div v-if="!viewComponent.includes(nodeData.virtual_type)" :class="{'is-disabled': belowAvailable}" @click.stop="lowerFold(node, node.data.retractBelow)" >
        <i class="el-icon-caret-bottom"/>下方{{ node.data.retractBelow ? '展开' : '收起' }}
      </div> -->
      <template v-if="nodeData.type === 'pnf' && (detailType === 'allPermissions' || detailType === 'teamPermissions' || detailType === 'matchPermissions' || detailType === 'personalPermissions')">
        <div :class="{'is-disabled': !importAvailable}" @click.stop="clickDrop('import')" >
          <i class="el-icon-lock"/>引入
        </div>
        <div :class="{'is-disabled': isError}" @click.stop="clickDrop('release')">
          <i class="el-icon-unlock"/>释放
        </div>
      </template>
      <template v-if="nodeData.type === 'vnf' && (detailType !=='readPermissions' && detailType !== 'firingPermissions' && detailType !== 'templatePermissions' && detailType !== 'consolePermissions' && detailType !== 'examinationPermissions')">
        <template v-if="nodeData.virtual_type === 'qemu' || nodeData.virtual_type === 'global_qemu'">
          <div :class="{'is-disabled': !startAvailable}" @click.stop="clickDrop('start')">
            <i class="el-icon-video-play"/>开机
          </div>
          <div :class="{'is-disabled': !stopAvailable}" @click.stop="clickDrop('stop')" >
            <i class="el-icon-switch-button"/>关机
          </div>
          <div :class="{'is-disabled': nodeData.status == 'pending'}" @click.stop="clickDrop('clean')">
            <i class="el-icon-unlock"/>释放
          </div>
          <div :class="{'is-disabled': !rebootAvailable}" @click.stop="clickDrop('reboot')">
            <i class="el-icon-refresh-right"/>重启
          </div>
          <div :class="{'is-disabled': !suspendAvailable}" @click.stop="clickDrop('suspend')">
            <i class="el-icon-remove-outline"/>挂起
          </div>
          <div :class="{'is-disabled': !resumeAvailable}" @click.stop="clickDrop('resume')">
            <i class="el-icon-top-left"/>恢复
          </div>
          <!-- <div :class="{'is-disabled': !rebuildAvailable}" @click.stop="clickDrop('rebuild')">
            <i class="el-icon-refresh"/>重建
          </div> -->
          <div :class="{'is-disabled': !pauseAvailable}" @click.stop="clickDrop('pause')">
            <i class="el-icon-video-pause"/>暂停
          </div>
          <!-- <div :class="{'is-disabled': !snapshotAvailable}" @click.stop="clickDrop('createSnapshot')">
            <i class="el-icon-camera"/>创建快照
          </div>
          <div :class="{'is-disabled': !toImageAvailable}" @click.stop="clickDrop('toImage')" >
            <i class="ivu-icon ivu-icon-t2-cloud-images"/>生成镜像
          </div> -->
        </template>
        <template v-else>
          <div :class="{'is-disabled': !activateAvailable}" @click.stop="clickDrop('activate')">
            <i class="el-icon-video-play"/>启动
          </div>
          <div :class="{'is-disabled': nodeData.status == 'pending'}" @click.stop="clickDrop('clean')">
            <i class="el-icon-unlock"/>释放
          </div>
        </template>
      </template>
    </template>
    <template v-if="type === 'edge' && (detailType === 'templatePermissions' || detailType === 'allPermissions' || (detailType == 'teamPermissions' && allowLinks === 1))">
      <div @click.stop="clickDrop('viewEdge')" >
        <i class="el-icon-view"/>查看
      </div>
      <div v-if="edge.connector.name == 'smooth'" @click.stop="clickDrop('connector', 'normal')">
        <svg width="17px" height="17px" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"><path d="M195.584 866.816l-38.4-38.4c-12.288-12.288-12.288-31.744 0-43.52L784.384 157.184c12.288-12.288 31.744-12.288 43.52 0l38.4 38.4c12.288 12.288 12.288 31.744 0 43.52L239.104 866.816c-11.776 12.288-31.232 12.288-43.52 0z" /></svg>直线
      </div>
      <div v-if="edge.connector.name == 'normal'" @click.stop="clickDrop('connector', 'smooth')">
        <svg width="17px" height="12px" viewBox="0 0 1280 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" style="margin-top: 2px;"><path d="M1280 614.4c0-32-32-64-64-64-38.4 0-64 32-64 64 0 44.8-12.8 172.8-70.4 236.8-32 32-70.4 44.8-121.6 44.8-89.6 0-153.6-243.2-204.8-416-64-249.6-128-480-294.4-480C224 0 115.2 364.8 44.8 601.6c-12.8 51.2-25.6 96-38.4 121.6-12.8 32 6.4 70.4 38.4 83.2 32 12.8 70.4-6.4 83.2-38.4 12.8-32 25.6-76.8 44.8-134.4C217.6 460.8 320 128 460.8 128 531.2 128 595.2 358.4 640 512c70.4 249.6 140.8 512 326.4 512 83.2 0 153.6-25.6 204.8-83.2 115.2-121.6 108.8-320 108.8-326.4z" /></svg>曲线
      </div>
    </template>
    <template v-if="type === 'node' && (detailType === 'templatePermissions' || detailType === 'allPermissions' || (detailType == 'teamPermissions' && allowLinks === 1))">
      <!-- 物理设备不支持复制 -->
      <div v-if="nodeData.type !== 'pnf'" @click.stop="clickDrop('copy')" >
        <i class="el-icon-copy-document"/>复制
      </div>
      <div v-if="nodeData.virtual_type === 'panel'" @click.stop="clickDrop('setZIndex')" >
        <i class="el-icon-sort"/>层叠顺序
      </div>
      <div v-if="nodeData.virtual_type === 'panel'" class="set-color" @click.stop="clickDrop('setPanelColor')">
        <el-color-picker ref="panelColor" v-model="panelColor" show-alpha size="mini" @change="changeColor" />设置颜色
      </div>
      <div v-if="nodeData.virtual_type === 'text'" class="set-color" @click.stop="clickDrop('setTextColor')">
        <el-color-picker ref="textColor" v-model="textColor" show-alpha size="mini" @change="changeColor" />设置颜色
      </div>
    </template>
    <div v-if="((type !== 'batch' && (detailType == 'templatePermissions' || detailType === 'allPermissions')) || (type === 'edge' && (detailType == 'teamPermissions' && allowLinks === 1))) && systemModule !== 'testingEdit'" :class="{'is-disabled': false}" @click.stop="clickDrop('remove')" >
      <i class="el-icon-delete"/>删除
    </div>
    <template v-if="detailType === 'examinationPermissions'">
      <!-- 恢复第一个快照 -->
      <!-- <div :class="{'is-disabled': !snapshotAvailable}" @click.stop="clickDrop('resetSnapshot')">
        <i class="el-icon-camera"/> 重置
      </div> -->
    </template>
    <template v-if="type === 'node' && nodeData.console_type && (detailType !== 'templatePermissions' && detailType !== 'readPermissions')">
      <div v-if="showConsole && (nodeData.type === 'pnf' || (nodeData.type === 'vnf' && nodeData.virtual_type !== 'cloud_router' && nodeData.virtual_type !== 'global_network'))" :class="{'is-disabled': !stopAvailable || teamDisabled }" class="console" >
        <i class="el-icon-monitor"/>
        <span>控制台</span>
        <div :style="{left: consoleMenu === 'left' ? '-130px' : '120px'}" class="orchestration-context-menu" style="width: 130px;">
          <div v-if="nodeData.console_type.split(',').includes('vnc')" @click.stop="clickDrop('vnc')">VNC</div>
          <div v-if="nodeData.console_type.split(',').includes('rdp')" @click.stop="clickDrop('rdp')">远程桌面</div>
          <div v-if="nodeData.console_type.split(',').includes('ssh')" @click.stop="clickDrop('ssh')">WebSSH</div>
          <div v-if="nodeData.console_type.split(',').includes('serial')" @click.stop="clickDrop('serial')">串口控制台</div>
          <div v-if="nodeData.console_type.split(',').includes('webshell')" @click.stop="clickDrop('webshell')">命令行</div>
        </div>
      </div>
    </template>
  </div>
</template>

<script>
import { start, stop, reboot, modalDelete, serial, suspend, resume, rebuild, pause, importNode, release, activate, vnc, clean, batchStart, batchStop, batchReboot, batchPause, batchSuspend, batchResume, getConsole, getSnapshot, restoreSnapshot } from '../api/orchestration'
import { Graph } from '@antv/x6'
import moduleConf from '../config'
export default {
  name: 'ContextMenu',
  props: {
    topologyData: { // 拓扑数据
      type: Object,
      default: () => {}
    },
    graph: {
      type: [Object, Graph]
    },
    detailType: {
      type: String,
      default: ''
    },
    allowLinks: {
      type: Number,
      default: 0
    },
    showConsole: { // 是否展示控制台
      type: Boolean,
      default: true
    },
    topicNodeList: { // 团体演练：当前角色下所有的靶机题目
      type: Array,
      default: () => []
    },
    systemModule: { // 检测平台-testingEdit
      type: String,
      default: ''
    }
  },
  data() {
    return {
      panelColor: 'rgba(255, 255, 255, 1)',
      textColor: 'rgba(0, 0, 0, 1)',
      titleMap: {
        'start': '开机',
        'stop': '关机',
        'reboot': '重启',
        'suspend': '挂起',
        'resume': '恢复',
        'rebuild': '重建',
        'pause': '暂停',
        'import': '引入',
        'release': '释放',
        'activate': '启动',
        'createSnapshot': '创建快照',
        'toImage': '生成镜像',
        'clean': '释放',
        'batchStart': '批量开机',
        'batchStop': '批量关机',
        'batchReboot': '批量重启',
        'batchPause': '批量暂停',
        'batchSuspend': '批量挂起',
        'batchResume': '批量恢复'
      },
      consoleMenu: 'right',
      activateStatus: ['pending', 'error'], // 启动 可操作的状态
      startStatus: ['pending', 'shutoff', 'error'], // 开机 可操作的状态
      stopStatus: ['running'], // 关机 可操作的状态
      rebootStatus: ['running', 'error'], // 重启 可操作的状态
      suspendStatus: ['running'], // 挂起 可操作的状态
      resumeStatus: ['suspended', 'paused'], // 恢复 可操作的状态
      rebuildStatus: ['shutoff', 'running'], // 重建 可操作的状态
      pauseStatus: ['running'], // 暂停 可操作的状态
      snapshotStatus: ['shutoff', 'running'], // 创建快照 可操作的状态
      toImageStatus: ['shutoff', 'running', 'paused', 'suspended'], // 生成镜像 可操作的状态
      viewComponent: ['panel', 'text'],
      x: '',
      y: '',
      type: '',
      node: {},
      edge: {},
      nodeData: {},
      edgeData: {},
      batchData: [],
      nodeIdList: [],
      apiObj: {
        'start': start,
        'stop': stop,
        'reboot': reboot,
        'modalDelete': modalDelete,
        'suspend': suspend,
        'resume': resume,
        'rebuild': rebuild,
        'pause': pause,
        'import': importNode,
        'release': release,
        'activate': activate,
        'clean': clean,
        'batchStart': batchStart,
        'batchStop': batchStop,
        'batchReboot': batchReboot,
        'batchPause': batchPause,
        'batchSuspend': batchSuspend,
        'batchResume': batchResume
      }
    }
  },
  computed: {
    // 团体演练-选手：节点所在的阶段未开启时右键置灰
    teamDisabled() {
      let flag = false
      if (this.topicNodeList && this.topicNodeList.length) {
        // 如果节点对应题目的所在阶段未开始，则置灰靶机右键控制台
        const topic = this.topicNodeList.find(topic => topic.nodeId === this.nodeData.node_id)
        if (topic && topic.stageStatus == 0) {
          flag = true
        }
      }
      return flag
    },
    isError() {
      if (this.type === 'node') {
        return this.nodeData.status === 'error'
      } else if (this.type === 'edge') {
        return this.edgeData.status === 'error'
      }
    },
    importAvailable() {
      return this.nodeData.ports.some(item => item.topology_link_to)
    },
    activateAvailable() {
      return this.handleAvailable(this.nodeData, this.activateStatus)
    },
    startAvailable() {
      return this.handleAvailable(this.nodeData, this.startStatus)
    },
    stopAvailable() {
      return this.handleAvailable(this.nodeData, this.stopStatus)
    },
    aboveAvailable() {
      return !this.graph.getIncomingEdges(this.node)
    },
    belowAvailable() {
      return !this.graph.getOutgoingEdges(this.node)
    },
    rebootAvailable() {
      return this.handleAvailable(this.nodeData, this.rebootStatus)
    },
    suspendAvailable() {
      return this.handleAvailable(this.nodeData, this.suspendStatus)
    },
    resumeAvailable() {
      return this.handleAvailable(this.nodeData, this.resumeStatus)
    },
    rebuildAvailable() {
      return this.handleAvailable(this.nodeData, this.rebuildStatus)
    },
    pauseAvailable() {
      return this.handleAvailable(this.nodeData, this.pauseStatus)
    },
    toImageAvailable() {
      return this.handleAvailable(this.nodeData, this.toImageStatus)
    },
    snapshotAvailable() {
      return this.handleAvailable(this.nodeData, this.snapshotStatus)
    },
    batchStartAvailable() { // 只要有一个能开机的就不disable
      return this.batchData.some(item => item.shape === 'custom-vue-node' && this.handleAvailable(item.data, this.startStatus))
    },
    batchStopAvailable() { // 只要有一个能关机的就不disable
      return this.batchData.some(item => item.shape === 'custom-vue-node' && this.handleAvailable(item.data, this.stopStatus))
    },
    batchRebootAvailable() { // 只要有一个能重启的就不disable
      return this.batchData.some(item => item.shape === 'custom-vue-node' && this.handleAvailable(item.data, this.rebootStatus))
    },
    batchSuspendAvailable() { // 只要有一个能挂起的就不disable
      return this.batchData.some(item => item.shape === 'custom-vue-node' && this.handleAvailable(item.data, this.suspendStatus))
    },
    batchResumeAvailable() { // 只要有一个能恢复的就不disable
      return this.batchData.some(item => item.shape === 'custom-vue-node' && this.handleAvailable(item.data, this.resumeStatus))
    },
    batchPauseAvailable() { // 只要有一个能暂停的就不disable
      return this.batchData.some(item => item.shape === 'custom-vue-node' && this.handleAvailable(item.data, this.pauseStatus))
    }
  },
  watch: {
    'node'(val) {
      if (val.data.virtual_type == 'panel') {
        this.panelColor = val.attrs.body.fill
      } else if (val.data.virtual_type == 'text') {
        this.textColor = val.attrs.text.fill
      }
    }
  },
  inject: ['topoVM'],
  methods: {
    changeColor(color) {
      if (this.node.data.virtual_type == 'panel') {
        this.node.attr('body/fill', color)
      } else if (this.node.data.virtual_type == 'text') {
        this.node.attr('text/fill', color)
      }
      this.$emit('close')
    },
    // 上方展开/收起方法
    foldUpAbove(node, retractAbove) {
      // 放入点击元素的id
      this.nodeIdList.push(node.id)
      // 根据传入元素获取 接受的连线
      const edgeList = this.graph.getIncomingEdges(node)
      // 循环全部接受的线条传入上级 元素的id
      edgeList.forEach(item => {
        this.fy(item.source.cell)
      })
      // 过滤掉点击的元素
      this.nodeIdList.filter(item => {
        return item !== node.id
      }).forEach(item => {
        // 上方隐藏为ture 则显示 为false则隐藏
        if (retractAbove) {
          this.graph.getCellById(item).show()
          this.node.data.retractAbove = false
        } else {
          this.graph.getCellById(item).hide()
          this.node.data.retractAbove = true
        }
        this.$emit('close')
      })
    },
    // 下方展开/收起方法
    lowerFold(node, retractBelow) {
      this.nodeIdList.push(node.id)
      const edgeList = this.graph.getOutgoingEdges(node)
      edgeList.forEach(item => {
        this.fy(item.target.cell)
      })
      this.nodeIdList.filter(item => {
        return item !== node.id
      }).forEach(item => {
        if (retractBelow) {
          this.graph.getCellById(item).show()
          this.node.data.retractBelow = false
        } else {
          this.graph.getCellById(item).hide()
          this.node.data.retractBelow = true
        }
        this.$emit('close')
      })
    },
    // 根据 元素id 获取其余下级与上级的关联线 然后在 获取元素 进行重新调用
    fy(item) {
      // 进行过查询的元素id则跳出
      if (this.nodeIdList.indexOf(item) === -1) {
        this.nodeIdList.push(item)
        this.nodeIdList = [...new Set(this.nodeIdList)]
        let arr = []
        this.graph.getConnectedEdges(this.graph.getCellById(item)).forEach(edge => {
        // 判断 该元素下方或者上方进行收起 则不进行获取他下方的线
          if (!this.graph.getCellById(item).data.retractBelow) {
            arr.push(edge.target.cell)
          }
          if (!this.graph.getCellById(item).data.retractAbove) {
            arr.push(edge.source.cell)
          }
        })
        arr = [...new Set(arr)]
        arr.filter(id => {
          return id !== item
        })
        arr.forEach(element => {
          this.fy(element)
        })
        return
      } else {
        return
      }
    },
    // 可操作数据判断逻辑
    handleAvailable(item, status) {
      return !item.task && status.indexOf(item.status.toLowerCase()) > -1
    },
    init(x, y, type, data) {
      this.x = parseInt(x) + ''
      this.y = y + ''
      if (type && data) {
        this.type = type
        if (type === 'node') {
          this.node = data
          this.nodeData = data.data
        } else if (type === 'edge') {
          this.edge = data
          this.edgeData = data.data
        } else {
          this.batchData = data
        }
      }
    },
    updateData(type, data) {
      if (type === 'node') {
        this.$set(this, 'nodeData', data)
      } else if (type === 'edge') {
        this.$set(this, 'edgeData', data)
      }
    },
    clickDrop(name, data) {
      if (name === 'setPanelColor') {
        this.$refs.panelColor.showPicker = true
        this.$refs.panelColor.showPanelColor = true
        return
      }
      if (name === 'setTextColor') {
        this.$refs.textColor.showPicker = true
        this.$refs.textColor.showPanelColor = true
        return
      }
      switch (name) {
        case 'remove':
          this.$emit('open', name, this.type === 'node' ? this.node.id : this.edge.id)
          break
        case 'config':
          this.$emit('open', 'configNode', this.node)
          break
        case 'viewConfig':
          this.$emit('open', 'viewConfigNode', this.node)
          break
        case 'import':
        case 'release':
        case 'activate':
        case 'start':
        case 'clean':
        case 'stop':
        case 'reboot':
        case 'suspend':
        case 'resume':
        case 'rebuild':
        case 'pause':
          this.$bus.$emit(
            'SINGLE_TASK_API',
            {
              taskName: this.titleMap[name],
              resource: this.nodeData,
              apiObj: this.apiObj[name],
              data: { id: this.nodeData.node_id },
              sucsessCallback: (res) => {
                this.$bus.$emit('node_module', 'reloadItem', { 'node': this.node })
              },
              errorCallback: () => {},
              keys: { 'name': 'name', 'id': 'node_id' }
            }
          )
          break
        case 'batchStart':
        case 'batchStop':
        case 'batchReboot':
        case 'batchPause':
        case 'batchSuspend':
        case 'batchResume': {
          const postData = { 'node_ids': this.batchData.map(item => item.shape === 'custom-vue-node' && item.data.node_id).filter(item => item) }
          this.$bus.$emit(
            'SINGLE_TASK_API',
            {
              taskName: this.titleMap[name],
              resource: { id: this.topologyData.id, data: postData },
              apiObj: this.apiObj[name],
              data: { id: this.topologyData.id, data: postData },
              sucsessCallback: (res) => {
                this.$bus.$emit('orchestration_module', 'reload')
              },
              errorCallback: () => {
                this.$bus.$emit('orchestration_module', 'reload')
              }
            }
          )
          break
        }
        case 'batchRemove':
          this.$emit('open', 'remove', this.batchData)
          break
        case 'resetSnapshot': {
          getSnapshot({ 'node_id': this.nodeData.node_id, limit: 999, offset: 0 }).then((res) => {
            const list = res.data.data
            if (list && list.length) {
              this.$bus.$emit(
                'SINGLE_TASK_API',
                {
                  taskName: '重置',
                  resource: list[0],
                  apiObj: restoreSnapshot,
                  data: { id: this.nodeData.node_id, data: { 'snapshot_id': list[0].id }},
                  sucsessCallback: (res) => {},
                  errorCallback: () => {}
                }
              )
            } else {
              this.$message.error('该题目没有快照')
            }
          })
          break
        }
        case 'vnc':
          vnc(this.nodeData.node_id)
            .then(res => {
              if (res.data.code == 0) {
                const url = res.data.data
                if (this.topoVM.consoleTarget == '_self') {
                  this.topoVM.consoleUrl('vnc', url, this.nodeData.node_id)
                  return
                }
                const a = document.createElement('a')
                a.setAttribute('href', encodeURI(url))
                a.setAttribute('target', '_blank')
                a.setAttribute('id', 'camnpr')
                document.body.appendChild(a)
                a.click()
              }
            })
            .catch(() => {})
          break
        case 'serial':
          serial(this.nodeData.node_id)
            .then(res => {
              const name = this.nodeData.name
              const url = res.data.data + '&title=' + name
              if (this.topoVM.consoleTarget == '_self') {
                this.topoVM.consoleUrl('serial', url, this.nodeData.node_id)
                return
              }
              const a = document.createElement('a')
              a.setAttribute('href', encodeURI(url))
              a.setAttribute('target', '_blank')
              a.setAttribute('id', 'camnpr')
              document.body.appendChild(a)
              a.click()
            })
            .catch(() => {})
          break
        case 'rdp': {
          getConsole(this.node.data.node_id, { console_type: 'rdp' })
            .then(res => {
              const url = (window.location.hostname == 'localhost' ? (window.ADMIN_CONFIG ? window.ADMIN_CONFIG.VIP_URL : window.WEB_CONFIG.VIP_URL) : '') + res.data.data
              if (this.topoVM.consoleTarget == '_self') {
                this.topoVM.consoleUrl('rdp', url, this.nodeData.node_id)
                return
              }
              const a = document.createElement('a')
              a.setAttribute('href', encodeURI(url))
              a.setAttribute('target', '_blank')
              a.setAttribute('id', 'camnpr')
              document.body.appendChild(a)
              a.click()
            })
            .catch(() => {})
          break
        }
        case 'ssh': {
          getConsole(this.node.data.node_id, { console_type: 'ssh' })
            .then(res => {
              const url = (window.location.hostname == 'localhost' ? (window.ADMIN_CONFIG ? window.ADMIN_CONFIG.VIP_URL : window.WEB_CONFIG.VIP_URL) : '') + res.data.data
              if (this.topoVM.consoleTarget == '_self') {
                this.topoVM.consoleUrl('ssh', url, this.nodeData.node_id)
                return
              }
              const a = document.createElement('a')
              a.setAttribute('href', encodeURI(url))
              a.setAttribute('target', '_blank')
              a.setAttribute('id', 'camnpr')
              document.body.appendChild(a)
              a.click()
            })
            .catch(() => {})
          break
        }
        case 'webshell': {
          getConsole(this.node.data.node_id, { console_type: 'webshell' })
            .then(res => {
              const url = (window.location.hostname == 'localhost' ? (window.ADMIN_CONFIG ? window.ADMIN_CONFIG.VIP_URL : window.WEB_CONFIG.VIP_URL) : '') + res.data.data
              if (this.topoVM.consoleTarget == '_self') {
                this.topoVM.consoleUrl('webshell', url, this.nodeData.node_id)
                return
              }
              const a = document.createElement('a')
              a.setAttribute('href', encodeURI(url))
              a.setAttribute('target', '_blank')
              a.setAttribute('id', 'camnpr')
              document.body.appendChild(a)
              a.click()
            })
            .catch(() => {})
          break
        }
        case 'createSnapshot':
        case 'toImage':
          this.$emit('open', name, this.nodeData)
          break
        case 'connector':
          this.edge.setConnector(data)
          break
        case 'viewEdge':
          this.$emit('open', name, this.edge)
          break
        case 'align':
          console.log(data)
          break
        case 'setZIndex':
          this.$emit('open', name, this.node)
          break
        case 'copy': {
          const copyNode = this.getCopyNode(this.node)
          this.$emit('createNode', copyNode, true)
          this.graph.resetSelection(copyNode)
          break
        }
        case 'batchCopy': {
          const batchNode = []
          this.batchData.forEach(item => {
            if (item.shape !== 'edge') {
              const copyNode = this.getCopyNode(item)
              batchNode.push(copyNode)
            }
          })
          this.$emit('batchCreateNode', batchNode)
          this.graph.resetSelection(batchNode)
          break
        }
      }
      this.$emit('close')
    },
    // 从一个节点复制一个节点
    getCopyNode(node) {
      const sourceNode = JSON.parse(JSON.stringify(node.toJSON()))
      const copyNode = this.graph.addNode({
        attrs: sourceNode.attrs,
        data: Object.assign({}, sourceNode.data, { name: sourceNode.data.name + '-副本' }),
        position: {
          x: sourceNode.position.x + 50,
          y: sourceNode.position.y + 50
        },
        shape: sourceNode.shape,
        size: sourceNode.size,
        tools: sourceNode.tools,
        zIndex: sourceNode.zIndex,
        ports: sourceNode.data.virtual_type !== 'panel' && sourceNode.data.virtual_type !== 'panel' ? moduleConf.portsConfig : null
      })
      // 文字节点内容加“-副本”
      if (sourceNode.data.type === 'base' && sourceNode.data.virtual_type === 'text') {
        copyNode.label = sourceNode.attrs.text.text + '-副本'
      }
      return copyNode
    },
    getTargetStatus(name) {
      let targetStatus = ''
      switch (name) {
        case 'activate':
          targetStatus = 'running'
          break
        case 'start':
          targetStatus = 'running'
          break
        case 'stop':
          targetStatus = 'shutoff'
          break
        case 'reboot':
          targetStatus = 'running'
          break
        case 'suspend':
          targetStatus = 'suspended'
          break
        case 'resume':
          targetStatus = 'running'
          break
        case 'rebuild':
          targetStatus = 'running'
          break
        case 'pause':
          targetStatus = 'paused'
          break
      }
      return targetStatus
    }
  }
}
</script>

<style lang="less" scoped>
  .orchestration-context-menu {
    position: absolute;
    z-index: 999;
    padding: 5px 0;
    background-color: #fff;
    border: 1px solid #ebeef5;
    border-radius: 2px;
    box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
    ::v-deep > div, .el-popover__reference-wrapper > div {
      line-height: 31px;
      padding: 2px 20px;
      margin: 0;
      min-width: 120px;
      font-size: 13px;
      color: #37668c;
      font-weight: 500;
      cursor: pointer;
      svg {
        font-weight: bold;
        vertical-align: text-top;
        margin-right: 2px;
        path {
          fill: #37668c;
        }
      }
      i {
        font-size: 16px;
        margin-right: 3px;
        font-weight: bold;
        &.ivu-icon {
          vertical-align: baseline;
        }
      }
    }
    ::v-deep > div:hover, .el-popover__reference-wrapper > div:hover {
      background-color: #f5f7fa;
      color: #252525;
      svg path {
        fill: #252525;
      }
    }
    ::v-deep > div.is-disabled, .el-popover__reference-wrapper > div.is-disabled {
      cursor: default;
      color: #bbb;
      cursor: not-allowed;
      pointer-events: none;
    }
    .resume-icon {
      font-size: 18px;
      margin-left: -2px;
      position: relative;
      top: 3px;
    }
    .console {
      position: relative;
      .orchestration-context-menu {
        display: none;
        bottom: -6px;
      }
      &:hover {
        .orchestration-context-menu {
          display: block
        }
      }
      i {
        margin-right: 0;
      }
    }
    .console.is-disabled .orchestration-context-menu{
      display: none;
    }
    .set-color {
      display: flex;
      align-items: center;
      ::v-deep .el-color-picker {
        margin-left: -3px;
        margin-right: 1px;
        height: 20px;
        .el-color-picker__trigger {
          width: 20px;
          height: 20px;
          padding: 0;
        }
      }
    }
  }
</style>
