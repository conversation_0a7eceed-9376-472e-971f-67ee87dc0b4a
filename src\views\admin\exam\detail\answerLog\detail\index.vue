<template>
  <detail-view
    :loading="loading"
    :data="data"
    :id="id"
    :view-item="viewItem"
    :params="{
      'id': $route.params.examId,
      'view': 'answerLog',
      'name': $route.params.examName,
      'cerType': $route.params.cerType
    }"
    :show-header="false"
    title-key="detailTitle"
  />
</template>
<script>
import moduleConf from '../config'
import actionMenu from '../action/index'
import detailView from '@/packages/detail-view/index'
import questionInfo from './questionInfo'

export default {
  components: {
    actionMenu,
    detailView,
    questionInfo
  },
  data() {
    return {
      moduleName: moduleConf.name,
      id: null, // 资源ID
      data: {
        detailTitle: this.$route.params.name
      }, // 资源数据对象
      loading: false,
      viewItem: [
        {
          transName: '题目信息',
          name: 'questionInfo',
          component: questionInfo
        }
      ]
    }
  },
  watch: {
    '$route': function(to, from) {
      console.log(to, from)
      const toId = to.params.hasOwnProperty('id') ? to.params.id : null
      const fromId = from.params.hasOwnProperty('id') ? from.params.id : null
      if (toId !== fromId) {
        this.data['detailTitle'] = to.params.name
      }
    }
  },
  created() {
    this.loadBase()
  },
  methods: {
    actionHandler: function(type, data) {
      switch (type) {
        case 'refresh':
          this.loadBase()
      }
    },
    'loadBase': function() {
      this.id = this.$route.params.id
      this.data.detailTitle = this.$route.params.name
    }
  }
}
</script>
