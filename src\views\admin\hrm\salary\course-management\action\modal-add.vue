<template>
  <div v-loading="loading" class="dialog-wrap">
    <el-form ref="formLabelAlign" :rules="rules" :model="formLabelAlign" label-position="right" label-width="80px">
      <el-form-item label="封面:">
        <div class="upload-div">
          <div v-show="imgUrl">
            <img :src="imgUrl" alt="" style="width: 430px; height: 236px;" @click="handleUpload">
          </div>
          <div v-show="!imgUrl">
            <el-button style="margin-left:10px" type="primary" plain class="btn" @click="handleUpload">
              上传图片
            </el-button>
          </div>
          <span class="describe-info">
            请上传格式为(jpg,jpeg,png)大小(不得超过8M)<br>建议尺寸为(430*236)的图片作为课程封面
          </span>
          <input ref="fileRef" type="file" style="display: none" @change="isSize" >
        </div>
      </el-form-item>
      <el-form-item label="名称:" prop="teachingName">
        <el-input v-model.trim="formLabelAlign.teachingName" placeholder="请输入内容"/>
      </el-form-item>
      <el-form-item label="分类:" prop="categoryCode">
        <el-select v-model="formLabelAlign.categoryCode" clearable filterable placeholder="请选择">
          <el-option
            v-for="item in categoryCodeList"
            :key="item.id"
            :label="item.name"
            :value="item.id"/>
        </el-select>
      </el-form-item>
      <el-form-item label="难度:" prop="difficulty">
        <el-select v-model="formLabelAlign.difficulty" size="medium" clearable placeholder="请选择">
          <el-option
            v-for="item in contentLevelList"
            :key="item.value"
            :label="item.label"
            :value="item.value"/>
        </el-select>
      </el-form-item>
      <el-form-item label="描述:" prop="teachingDescribe">
        <el-input
          :autosize="{ minRows: 4, maxRows: 4}"
          v-model.trim="formLabelAlign.teachingDescribe"
          resize="none"
          type="textarea"
          maxlength="100"
          show-word-limit
          placeholder="请填写描述,0-100个字符以内"/>
      </el-form-item>
    </el-form>
    <div v-if="isJoin" style="width: 100%">
      <el-alert :closable="false" type="warning">
        <div slot="title">
          <p>该课程已被学员加入自学，修改课程后学员侧自学课程将同步修改！</p >
        </div>
      </el-alert>
      <el-checkbox v-model="checked">我已知晓上述风险</el-checkbox>
    </div>
    <div class="dialog-footer">
      <el-button v-show="!editId" type="text" @click="close">取消</el-button>
      <el-button :disabled="!checked" type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>
<script>
import module from '../config.js'
import validate from '@/packages/validate'
import { pjtMyContentQuery, queryCourseCategory, saveCourse, upTeachingCoverApi, updataCourseCategory } from '@/api/teacher/index.js'

export default {
  name: 'AddContent',
  components: {},
  mixins: [],
  props: {
    data: Object
  },
  data() {
    return {
      moduleName: module.name,
      loading: false,
      validate: validate,
      rules: {
        teachingName: [validate.required(), validate.base_name],
        categoryCode: [validate.required()],
        difficulty: [validate.required()]

      },
      formLabelAlign: {
        teachingName: '',
        url: '',
        teachingDescribe: '',
        categoryCode: '',
        categoryContent: [],
        difficulty: ''
      },
      imgUrl: '',
      courseContentList: [],
      categoryCodeList: [],
      editId: this.$route.query.id,
      apiType: saveCourse,
      contentLevelList: [{ label: '初级', value: 1 }, { label: '中级', value: 2 }, { label: '高级', value: 3 }],
      isJoin: false,
      checked: this.isJoin
    }
  },
  async mounted() {
    await this.searchPoint()
    this.isJoin = this.data.selective
    if (!this.isJoin) {
      this.checked = true
    }
    if (this.data.id) {
      this.formLabelAlign['teachingName'] = this.data['name']
      this.formLabelAlign['url'] = this.data['courseCoverUrl']
      this.formLabelAlign['teachingDescribe'] = this.data['courseDescription']
      this.formLabelAlign['categoryCode'] = this.data['courseCategoryId']
      this.formLabelAlign['categoryContent'] = this.data['categoryContent']
      this.imgUrl = this.data['courseCoverUrl']
      this.formLabelAlign['difficulty'] = this.data['difficulty']
    }
  },
  methods: {
    searchPoint() {
      pjtMyContentQuery({ id: '', pageNum: 1, pageSize: 1000 }).then(res => {
        this.courseContentList = res.data.records
      })
      queryCourseCategory().then(res => {
        this.categoryCodeList = res.data
      })
    },
    handleUpload() {
      this.$refs.fileRef.click()
    },
    // 限制分辨率
    isSize() {
      const file = this.$refs.fileRef.files[0]
      const reader = new FileReader()
      reader.onload = () => {
        if (reader.readyState == 2) {
          const img = new Image()
          img.src = reader.result
          this.$nextTick(() => {
            if (
              (img.width && img.width < 431) &&
              (img.height && img.height < 237)
            ) {
              this.isValidRatio = true
              this.doUpload()
            } else {
              this.isValidRatio = false
              this.doUpload()
            }
          })
        }
      }
      reader.readAsDataURL(file)
    },
    doUpload() {
      const file = this.$refs.fileRef.files[0]
      // #begin
      // 限制文件大小
      const maxSize = 8388608
      if (file.size > maxSize) {
        this.$message.warning('文件大小超出限制')
        return
      }
      // 限制分辨率
      if (!this.isValidRatio) {
        this.$message.warning('建议分辨率不超过430*236')
      }
      // 限制文档格式
      // const fileArr = file.name.split('.')
      // const fileType = fileArr[fileArr.length - 1]
      // if (!this.fileTypes.includes(fileType.toLowerCase())) {
      //   this.$message.error('图片格式仅支持 jpg,jpeg,png')
      //   return
      // }
      // #end
      const formData = new FormData()
      formData.append('file', this.$refs.fileRef.files[0])
      upTeachingCoverApi(formData).then((ret) => {
        if (ret.code === 200 || ret.code === 0) {
          this.$message({
            message: '上传成功',
            type: 'success'
          })
          this.imgUrl = ret.data
        }
      }).catch(() => {
        this.$message.error('上传失败')
      })
    },
    close: function() {
      this.$emit('close')
    },
    confirm: function() {
      this.$refs['formLabelAlign'].validate((valid) => {
        if (valid) {
          this.loading = true
          const params = {
            name: this.formLabelAlign.teachingName,
            courseCoverUrl: this.imgUrl,
            courseCategoryId: this.formLabelAlign.categoryCode,
            courseDescription: this.formLabelAlign.teachingDescribe,
            difficulty: this.formLabelAlign.difficulty,
            contentIds: this.formLabelAlign['categoryContent']
          }
          if (this.data.id) {
            params.id = this.data.id
            this.apiType = updataCourseCategory
          }
          this.apiType(params).then(res => {
            if (this.data.id) {
              this.$message.success(`编辑成功`)
            } else {
              this.$message.success(`新建成功`)
            }
            this.$emit('call', 'refresh')
            this.close()
            this.loading = false
          }).catch(() => {
            this.loading = false
          })
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
  ::v-deep .el-form-item__label {
    text-align: left;
  }
  .describe-info{
    color: var(--neutral-600);
    font-weight: 500;
    font-size: 14px;
  }
</style>
