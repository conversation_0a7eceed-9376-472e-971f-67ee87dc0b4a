// key 字段跟权限挂钩，auth接口返回的mange权限里面如果包含key对应的权限才展示对应菜单
// alwaysShow为true时，不走权限，一直显示对应菜单
// path字段对应路由表中的path，通过此字段去查找对应子路由
export default [
  {
    key: 'app',
    title: '业务系统',
    subs: [
      { key: 'match', path: '/manage/match', title: '竞赛系统', icon: 'competition' },
      { key: 'training', path: '/manage/training', title: '实训系统', icon: 'practical-training' },
      { key: 'penetration', path: '/penetrant', title: '渗透测试管控系统', icon: 'permeate' },
      { key: 'simulation', path: '/simulation/training', title: '攻防演练系统', icon: 'drill' },
      { key: 'recurrent', path: '/recurrent', title: '漏洞复现系统', icon: 'recurrent' },
      // 检测管理系统先不走权限，到时候后端走auth接口再对应
      { key: 'testing', path: '/testing', title: '检测管理系统', icon: 'detection' },
      { key: 'exam', path: '/manage/exam', title: '考试中心', icon: 'exam', alwaysShow: true },
      { key: 'analysis', path: '/analysis', title: '数据分析', icon: 'analysis', alwaysShow: true },
      { key: 'scene', path: '/scene', title: '场景管理', icon: 'scene', alwaysShow: true },
      { key: 'nfvo', path: '/os_network', title: '靶场底座', icon: 'simulation', alwaysShow: true }
    ]
  },
  {
    key: 'accumulate',
    title: '资源中心',
    subs: [
      { key: 'baseResource', path: '/baseResource', title: '基础资源', icon: 'baseResource', alwaysShow: true },
      { key: 'assetRepo', path: '/assetRepo', title: '资产库', icon: 'assetRepo', alwaysShow: true },
      { key: 'knowledgeSys', path: '/knowledgeSys', title: '知识体系', icon: 'knowledgeSys', alwaysShow: true }
    ]
  },
  {
    key: 'usersystem',
    title: '用户体系',
    subs: [
      { key: 'users', path: '/usercenter', title: '用户中心', icon: 'user', alwaysShow: true },
      { key: 'permission', path: '/permission', title: '权限中心', icon: 'limits', alwaysShow: true },
      { key: 'management', path: '/team', title: '队伍管理', icon: 'team-management', alwaysShow: true }
    ]
  },
  {
    key: 'ops',
    title: '平台运维',
    subs: [
      { key: 'adminLog', path: '/log', title: '日志管理', icon: 'log', alwaysShow: true },
      { key: 'label', path: '/label', title: '标签管理', icon: 'label', alwaysShow: true },
      { key: 'licenses', path: '/licenses', title: '许可授权', icon: 'licenses', alwaysShow: true }
    ]
  },
  {
    key: 'setConfig',
    title: '设置',
    subs: [
      { key: 'systemSettings', path: '/systemSettings', title: '系统设置', icon: 'website', alwaysShow: true }
    ]
  }
]
