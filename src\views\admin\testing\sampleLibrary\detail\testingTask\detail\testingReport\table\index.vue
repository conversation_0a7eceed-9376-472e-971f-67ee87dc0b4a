<template>
  <div class="resource-table">
    <!-- 操作区 -->
    <div class="operation-wrap">
      <div class="operation-left">
        <slot name="action" />
        <el-button type="primary" icon="el-icon-refresh" @click="refresh" />
      </div>
      <div class="operation-right">
        <el-badge :value="searchBtnShowNum">
          <el-button icon="el-icon-search" @click="openSearch">搜索</el-button>
        </el-badge>
        <t-table-config
          v-if="!customColData.length"
          :data="columnsObj"
          :active-key-arr="columnsViewArr"
          @on-change-col="onChangeCol"
        />
      </div>
    </div>
    <!-- 搜索区 -->
    <t-search-box
      v-show="searchView"
      :search-key-list="searchKeyListView"
      default-placeholder="默认搜索测试报告名称"
      @search="searchMultiple"
    />
    <!-- 列表 -->
    <t-table-view
      ref="tableView"
      :height="height"
      :single="single"
      :loading="tableLoading"
      :data="tableData"
      :total="tableTotal"
      :page-size="pageSize"
      :current="pageCurrent"
      :select-item="selectItem"
      type="list"
      current-key="id"
      @on-select="onSelect"
      @on-current="onCurrent"
      @on-change="changePage"
      @on-sort-change="onSortChange"
      @on-page-size-change="onPageSizeChange"
    >
      <el-table-column
        v-for="item in columnsViewArr"
        :key="item"
        :min-width="columnsObj[item].colMinWidth || colMinWidth"
        :width="columnsObj[item].colWidth"
        :label="columnsObj[item].title"
        :fixed="columnsObj[item].master ? 'left' : false"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span v-if="item == 'sort'">
            {{ scope.$index + 1 }}
          </span>
          <span v-else-if="item === 'size'">
            {{ formatSize(scope.row[item]) }}
          </span>
          <span v-else-if="item === 'auditStatus'">
            <el-badge :type="module.levelObj[scope.row[item]] ? module.levelObj[scope.row[item]].type : 'info'" is-dot />
            <span style="margin-right: 5px;">{{ (module.levelObj[scope.row[item]] && module.levelObj[scope.row[item]].label) || '-' }}</span>
            <el-link v-if="scope.row[item] != 0" :disabled="false" :underline="false" type="primary" @click.stop="processDetail('process', scope.row)">查看</el-link>
          </span>
          <div v-else-if="item === 'operate'">
            <el-link :underline="false" type="primary" @click.stop="handlePreview(scope.row)">查看</el-link>
            <el-link :underline="false" type="primary" @click.stop="handleDown(scope.row)">下载</el-link>
          </div>
          <span v-else>
            {{ scope.row[item] || '-' }}
          </span>
        </template>
      </el-table-column>
    </t-table-view>
    <!-- 中部弹窗 start-->
    <el-dialog
      :title="titleMapping[modalName]"
      :visible.sync="modalShow"
      :width="modalWidth"
      destroy-on-close
      append-to-body
      @close="modalClose"
    >
      <transition name="el-fade-in-linear">
        <component
          v-if="modalShow"
          :is="modalName"
          :name="modalName"
          :data="data"
          :content="modalContent"
          @close="modalClose"
          @call="confirmCall"
        />
      </transition>
    </el-dialog>
    <!-- 中部弹窗 end-->
  </div>
</template>

<script>
import module from '../config.js'
import tSearchBox from '@/packages/search-box/index.vue'
import tTableView from '@/packages/table-view/index.vue'
import tTableConfig from '@/packages/table-config/table-col-config.vue'
import mixinsPageTable from '@/packages/mixins/page_table'
import { testingReportTaskQuery, testingReportDownloadById, testingReportById } from '@/api/testing/index'
import process from '../action/modal-process.vue'

export default {
  components: {
    tSearchBox,
    tTableView,
    tTableConfig,
    process
  },
  mixins: [mixinsPageTable],
  props: {
    data: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      module,
      moduleName: module.name,
      // 搜索配置项
      searchKeyList: [
        { key: 'name', label: '测试报告名称', master: true },
        { key: 'round', label: '测试轮次' },
        { key: 'createUser', label: '提交人' },
        { key: 'statusList', label: '审核状态', type: 'select', valueList: module.levelStrArr },
        { key: 'time', label: '提交时间', type: 'time_range' }
      ],
      // 所有可配置显示列
      columnsObj: {
        'sort': {
          title: '序号',
          master: true,
          colMinWidth: 50
        },
        'round': {
          title: '测试轮次',
          master: true,
          colMinWidth: 100
        },
        'name': {
          title: '测试报告名称',
          colMinWidth: 250
        },
        'size': {
          title: '文件大小',
          colMinWidth: 100
        },
        'createUser': {
          title: '提交人',
          colMinWidth: 100
        },
        'createTime': {
          title: '提交时间'
        },
        'auditStatus': {
          title: '审核状态'
        },
        'operate': {
          title: '操作'
        }
      },
      // 当前显示列
      columnsViewArr: [
        'sort',
        'round',
        'name',
        'size',
        'createUser',
        'createTime',
        'auditStatus',
        'operate'
      ],
      modalDataObj: {},
      modalName: '',
      modalShow: false,
      modalContent: '',
      modalWidth: '520px',
      // 弹窗title映射
      titleMapping: {
        'process': '查看审核'
      }
    }
  },
  methods: {
    modalClose() {
      this.modalShow = false
    },
    processDetail(name, content) {
      this.modalName = name
      this.modalContent = content
      this.modalShow = true
    },
    confirmCall: function(type, data) {
      if (type === 'close') {
        this.modalClose()
      } else if (type === 'refresh') {
        this.getList()
      }
    },
    // 查看附件
    handlePreview(file) {
      testingReportById({ id: file.id }).then(res => {
        if (res.data.code == 0) {
          if (file && file.name.includes('zip')) {
            this.$message.warning('该文件不支持预览，请下载查看')
            return
          }
          this.previewUrl = this.viewFileUrl + encodeURIComponent(btoa(window.ADMIN_CONFIG.VIP_URL + res.data.data.fileUrl))
          window.open(this.previewUrl, '_blank')
        }
      })
    },
    // 下载附件
    handleDown(file) {
      testingReportDownloadById({ id: file.id }).then(res => {
        if (res.data.data) {
          fetch(res.data.data, {
            method: 'get',
            responseType: 'blob'
          })
            .then((response) => response.blob())
            .then((blob) => {
              const a = document.createElement('a')
              const URL = window.URL || window.webkitURL
              const href = URL.createObjectURL(blob)
              a.href = href
              a.download = file.name
              document.body.appendChild(a)
              a.click()
              document.body.removeChild(a)
              URL.revokeObjectURL(href)
            })
        }
      })
    },
    getList(showLoading = true) {
      if (showLoading) {
        this.tableLoading = true
      }
      const params = this.getPostData('page', 'limit')
      if (params.time) {
        params.createTimeStart = params.time.split(',')[0]
        params.createTimeEnd = params.time.split(',')[1]
        delete params.time
      }
      params.projectId = this.data.projectId
      params.taskId = this.data.id
      params.type = 1
      testingReportTaskQuery(params).then(res => {
        this.tableData = res.data.data ? res.data.data.records : []
        this.tableTotal = Number(res.data.data.total) || 0
        this.tableLoading = false
      }).catch(() => {
        this.tableLoading = false
      })
    },
    formatSize(size) {
      if (!size && size !== 0) return '-'
      const byte = Number(size)
      if (byte >= 1024 * 1024) {
        return (byte / 1024 / 1024).toFixed(2) + ' MB'
      }
      return (byte / 1024).toFixed(2) + ' KB'
    }
  }
}
</script>
<style scoped>
</style>
