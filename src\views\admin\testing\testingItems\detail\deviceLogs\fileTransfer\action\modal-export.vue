<template>
  <div v-loading="loading" class="dialog-wrap">
    <el-radio v-model="type" label="search">按搜索结果导出</el-radio>
    <div style="padding: 10px 0px 10px 24px;">
      <el-row>
        <el-col :span="3">搜索项:</el-col>
        <el-col :span="21">
          <template v-if="searchView.length">
            <el-tag
              v-for="item in searchView"
              :key="item.key"
              class="ellipsis mr-5"
              style="max-width: 190px;"
              size="small"
            >
              <span>{{ item.label }}：{{ item.value }}</span>
            </el-tag>
          </template>
          <span v-else>无</span>
        </el-col>
      </el-row>
    </div>
    <el-radio v-model="type" label="all">导出全部</el-radio>
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>
<script>
import module from '../config.js'
import batchTemplate from '@/packages/batch-delete/modal-bat-template'
import modalMixins from '@/packages/mixins/modal_form'
import { exportFileTransferApi } from '@/api/testing/index'
import { downloadExcelWithResData } from '@/utils'

export default {
  components: { batchTemplate },
  mixins: [modalMixins],
  props: {
    id: {
      type: String,
      default: ''
    }
  },
  inject: ['tableVm'],
  data() {
    return {
      moduleName: module.name,
      loading: false,
      type: 'search'
    }
  },
  computed: {
    'searchView': function() {
      const _data = []
      for (const key in this.tableVm.searchData) {
        _data.push({
          key: key,
          value: this.tableVm.searchData[key],
          label: this.tableVm.searchKeyList.find(item => item.key === key).label
        })
      }
      return _data
    }
  },
  methods: {
    close: function() {
      this.$emit('close')
    },
    confirm: function() {
      this.loading = true
      const params = Object.assign({},
        this.tableVm.filterData,
        this.type === 'search' ? this.tableVm.searchData : {}
      )
      params.projectId = this.$route.params.id
      if (this.type == 'all') {
        params.pageType = 0
      }
      exportFileTransferApi(params).then((res) => {
        this.close()
        this.loading = false
        this.$message.success('文件传输记录导出成功')
        downloadExcelWithResData(res)
      })
    }
  }
}
</script>
