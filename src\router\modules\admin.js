/** 系统管理路由 */
import Layout from '@/views/layout/AdminLayout'

const layout = function(meta = {}, path = '/manage', requiresAuth = true) {
  return {
    path: path,
    component: Layout,
    meta: {
      requiresAuth: requiresAuth,
      ...meta
    }
  }
}

export default [
  {
    ...layout(
      {
        permissions: ['manage'],
        title: '考试中心',
        icon: 'exam'
      },
      '/manage/exam'
    ),
    children: [
      {
        name: 'nonCertificate',
        path: 'nonCertificate',
        hidden: false,
        component: () => import('@/views/admin/exam/nonCertificate/index'),
        meta: {
          title: '常规考试',
          icon: 'cr-icon-kaoshiguanli',
          requiresAuth: false,
          permissions: ['manage']
        }
      },
      {
        name: 'ca-exam',
        path: 'index',
        hidden: false,
        component: () => import('@/views/admin/exam/index'),
        meta: {
          title: '认证考试',
          icon: 'cr-icon-kaoshiguanli',
          requiresAuth: false,
          permissions: ['manage']
        }
      },
      {
        name: 'addExam',
        path: 'create',
        hidden: true,
        component: () => import('@/views/admin/exam/create/add-exam'),
        meta: {
          type: 'full_create',
          activeMenu: 'ca-exam',
          requiresAuth: true,
          permissions: ['manage', 'training']
        }
      },
      {
        name: 'editExam',
        path: 'edit',
        hidden: true,
        component: () => import('@/views/admin/exam/create/edit-exam'),
        meta: {
          type: 'full_create',
          activeMenu: 'ca-exam',
          requiresAuth: true,
          permissions: ['manage', 'training']
        }
      },
      {
        name: 'cloneExam',
        path: 'clone',
        hidden: true,
        component: () => import('@/views/admin/exam/create/clone-exam'),
        meta: {
          type: 'full_create',
          activeMenu: 'ca-exam',
          icon: 'cr-icon-kaoshiguanli',
          requiresAuth: true,
          permissions: ['manage', 'training']
        }
      },
      {
        name: 'addExamNonCertificate',
        path: 'createNonCertificate',
        hidden: true,
        component: () => import('@/views/admin/exam/nonCertificate/create/add-exam'),
        meta: {
          type: 'full_create',
          requiresAuth: true,
          activeMenu: 'ca-exam',
          permissions: ['manage', 'training']
        }
      },
      {
        name: 'editExamNonCertificate',
        path: 'editNonCertificate',
        hidden: true,
        component: () => import('@/views/admin/exam/nonCertificate/create/edit-exam'),
        meta: {
          type: 'full_create',
          requiresAuth: true,
          activeMenu: 'ca-exam',
          permissions: ['manage', 'training']
        }
      },
      {
        name: 'cloneExamNonCertificate',
        path: 'cloneNonCertificate',
        hidden: true,
        component: () => import('@/views/admin/exam/nonCertificate/create/clone-exam'),
        meta: {
          type: 'full_create',
          requiresAuth: true,
          activeMenu: 'ca-exam',
          permissions: ['manage', 'training']
        }
      },
      // 证书考试详情
      {
        path: 'detail/:id/:view/:name/:cerType?',
        name: 'detailExam',
        hidden: true,
        component: () => import('@/views/admin/exam/index'),
        meta: {
          parentTitle: '考试管理',
          parentName: 'ca-exam',
          activeMenu: 'ca-exam',
          icon: 'cr-icon-shijuanguanli',
          requiresAuth: true,
          permissions: ['manage', 'training']
        }
      },
      // 非证书考试详情
      {
        path: 'detail/nonCertificate/:id/:view/:name/:cerType?',
        name: 'detailNonCertificateExam',
        hidden: true,
        component: () => import('@/views/admin/exam/nonCertificate/index'),
        meta: {
          parentTitle: '考试管理',
          parentName: 'nonCertificate',
          activeMenu: 'nonCertificate',
          icon: 'cr-icon-shijuanguanli',
          requiresAuth: true,
          permissions: ['manage', 'training']
        }
      },
      // 证书考试答题记录
      {
        path: 'answerLog/:id/:userId/:name/:examId/:examCode/:examName/:evaluationCode/:score/:cerType?/:examStatus?',
        name: 'answerRecord',
        hidden: true,
        component: () => import('@/views/admin/exam/detail/answerLog/detail'),
        meta: {
          parentTitle: '答题记录',
          parentName: 'detailExam',
          activeMenu: 'ca-exam',
          icon: 'cr-icon-shijuanguanli',
          requiresAuth: true,
          permissions: ['manage', 'training']
        }
      },
      // 非证书考试答题记录
      {
        path: 'answerLog/nonCertificate/:id/:userId/:name/:examId/:examCode/:examName/:evaluationCode/:score/:cerType?/:examStatus?',
        name: 'answerNonCertificateRecord',
        hidden: true,
        component: () => import('@/views/admin/exam/detail/answerLog/detail'),
        meta: {
          parentTitle: '答题记录',
          parentName: 'detailNonCertificateExam',
          activeMenu: 'nonCertificate',
          icon: 'cr-icon-shijuanguanli',
          requiresAuth: true,
          permissions: ['manage', 'training']
        }
      },
      // 证书考试阅卷
      {
        name: 'examReview',
        path: 'students/review',
        hidden: true,
        component: () => import('@/views/admin/exam/detail/students/action/modal-review'),
        meta: {
          activeMenu: 'ca-exam',
          requiresAuth: true,
          permissions: ['manage', 'training'],
          parentTitle: '考生列表',
          parentName: 'detailExam',
          icon: 'cr-icon-shijuanguanli'
        }
      },
      // 非证书考试阅卷
      {
        name: 'examNonCertificateReview',
        path: 'students/review/nonCertificate',
        hidden: true,
        component: () => import('@/views/admin/exam/detail/students/action/modal-review'),
        meta: {
          activeMenu: 'nonCertificate',
          requiresAuth: true,
          permissions: ['manage', 'training'],
          parentTitle: '考生列表',
          parentName: 'detailNonCertificateExam',
          icon: 'cr-icon-shijuanguanli'
        }
      },
      {
        name: 'openEnv',
        path: 'students/review/openEnv',
        activeMenu: 'nonCertificate',
        hidden: true,
        component: () => import('@/views/admin/exam/openEnv/index'),
        meta: {
          type: 'exam-openEnv',
          requiresAuth: true,
          activeMenu: 'nonCertificate',
          permissions: ['manage', 'training']
        }
      }
    ]
  },
  {
    ...layout(
      {
        permissions: ['manage', 'match'],
        title: '竞赛系统',
        icon: 'competition'
      },
      '/manage/match'
    ),
    children: [
      {
        name: 'matchManage',
        path: 'matchManage',
        component: () => import('@/views/admin/matchManage/manage/index'),
        meta: {
          title: '赛事管理',
          icon: 'cr-icon-saishiguanli',
          requiresAuth: true,
          permissions: ['manage', 'match']
        }
      },
      {
        name: 'matchDetail',
        path: 'matchDetail/:id/:view',
        hidden: true,
        component: () => import('@/views/admin/matchManage/manage/details/index'),
        meta: {
          activeMenu: 'matchManage',
          icon: 'cr-icon-saishiguanli',
          parentTitle: '赛事管理',
          parentName: 'matchManage',
          requiresAuth: true,
          permissions: ['manage', 'match']
        }
      },
      {
        name: 'createMatch',
        path: 'createMatch',
        hidden: true,
        component: () => import('@/views/admin/matchManage/manage/create/createMatch.vue'),
        meta: {
          type: 'full_create',
          requiresAuth: true,
          permissions: ['manage', 'match']
        }
      },
      {
        name: 'matchList',
        path: 'matchList',
        component: () => import('@/views/admin/matchManage/matchList/index'),
        meta: {
          title: '比赛管理',
          icon: 'cr-icon-bisailiebiao',
          requiresAuth: true,
          permissions: ['manage', 'match']
        }
      },
      {
        name: 'matchListDetail',
        path: 'matchListDetail/:id/:view',
        hidden: true,
        component: () => import('@/views/admin/matchManage/matchList/details/index'),
        meta: {
          requiresAuth: true,
          activeMenu: 'matchList',
          icon: 'cr-icon-bisailiebiao',
          parentTitle: '比赛管理',
          parentName: 'matchList',
          permissions: ['manage', 'match']
        }
      }, {
        name: 'createBigMatch',
        path: 'createBigMatch',
        hidden: true,
        component: () => import('@/views/admin/matchManage/matchList/create/modal-create.vue'),
        meta: {
          type: 'full_create',
          requiresAuth: true,
          permissions: ['manage', 'match']
        }
      }, {
        name: 'createSchedule',
        path: 'create/schedule',
        hidden: true,
        component: () => import('@/views/admin/matchManage/schedule/create/index'),
        meta: {
          title: '比赛管理',
          type: 'full_create',
          icon: 'cr-icon-bisailiebiao',
          requiresAuth: true,
          permissions: ['manage', 'match']
        }
      }, {
        name: 'allocationResource',
        path: 'create/allocationResource/:sharingModel/:bigMatchSeasonId/:id/:topologyId',
        hidden: true,
        component: () => import('@/views/admin/matchManage/schedule/allocationResource/index'),
        meta: {
          title: '资源分配',
          type: 'full_create',
          icon: 'cr-icon-bisailiebiao',
          requiresAuth: true,
          permissions: ['manage', 'match']
        }
      }, {
        path: 'schedule/detail/:id/:bigMatchSeasonId/:view',
        name: 'scheduleDetail',
        hidden: true,
        component: () => import('@/views/admin/matchManage/matchList/details/index'),
        meta: {
          parentTitle: '阶段',
          parentName: 'matchListDetail',
          activeMenu: 'matchList',
          icon: 'cr-icon-bisailiebiao',
          requiresAuth: true,
          permissions: ['manage', 'match']
        }
      }, {
        path: 'matchHave/:id/:view',
        name: 'matchHave',
        hidden: true,
        component: () => import('@/views/admin/matchManage/schedule/detail/matchHave'),
        meta: {
          parentTitle: '比赛管理',
          parentName: 'matchList',
          activeMenu: 'matchList',
          icon: 'cr-icon-bisailiebiao',
          requiresAuth: true,
          permissions: ['manage', 'match']
        }
      },
      {
        name: 'screenConfig',
        path: 'screenConfig',
        component: () => import('@/views/admin/matchManage/screenConfig/index'),
        meta: {
          title: '3D大屏配置管理',
          icon: 'cr-icon-dapingpeizhiguanli-3D',
          requiresAuth: true,
          permissions: ['manage', 'match']
        }
      },
      {
        name: 'eventEvaluation',
        path: 'eventEvaluation',
        component: () => import('@/views/admin/matchManage/eventEvaluation/index'),
        meta: {
          title: '赛事评价',
          icon: 'cr-icon-saishipingjia',
          requiresAuth: true,
          permissions: ['manage', 'match']
        }
      },
      {
        name: 'createConfig',
        path: 'createConfig',
        hidden: true,
        component: () => import('@/views/admin/matchManage/screenConfig/create/index.vue'),
        meta: {
          type: 'full_create',
          requiresAuth: true,
          permissions: ['manage', 'match']
        }
      }
    ]
  },
  {
    ...layout(
      {
        permissions: ['manage', 'training'],
        title: '实训系统',
        icon: 'practical-training'
      },
      '/manage/training'
    ),
    children: [
      // 概览
      {
        path: 'overview',
        name: 'overview',
        component: () => import('@/views/admin/hrm/overview/index'),
        meta: {
          title: '概览',
          icon: 'cr-icon-gaikuang',
          requiresAuth: true,
          permissions: ['manage']
        }
      },

      // 人员管理-学员管理-列表
      {
        path: 'studentManage',
        name: 'studentManage',
        component: () => import('@/views/admin/hrm/personnelManagement/student/index'),
        meta: {
          title: '人员管理',
          icon: 'cr-icon-xueshengguanli',
          requiresAuth: true,
          permissions: ['manage', 'training', 'field']
        }
      },
      // 人员管理-学员管理-学习历程
      {
        path: 'learningProcess',
        name: 'learningProcess',
        hidden: true,
        component: () => import('@/views/admin/hrm/personnelManagement/student/index'),
        meta: {
          parentTitle: '人员管理',
          parentName: 'studentManage',
          title: '人员管理',
          icon: 'cr-icon-xueshengguanli',
          requiresAuth: true,
          permissions: ['manage', 'training', 'field']
        }
      },
      // 人员管理-班级管理-列表
      {
        path: 'classManage',
        name: 'classManage',
        hidden: true,
        component: () => import('@/views/admin/hrm/personnelManagement/class/index'),
        meta: {
          title: '人员管理',
          icon: 'cr-icon-Group-copy',
          activeMenu: 'studentManage',
          hidden: true,
          requiresAuth: true,
          permissions: ['manage', 'training', 'field']
        }
      },
      // 人员管理-班级管理-详情
      {
        path: 'classDetail/:majorCode/:classCode/:majorName',
        name: 'classDetail',
        hidden: true,
        component: () => import('@/views/admin/hrm/personnelManagement/class/index'),
        meta: {
          parentTitle: '班级管理',
          icon: 'cr-icon-xueshengguanli',
          parentName: 'classManage',
          requiresAuth: true,
          permissions: ['manage', 'training', 'field'],
          nav: 'studentManage/:majorCode/:classCode/:majorName'
        }
      },
      // 人员管理-教师管理-列表
      {
        path: 'teacherManage',
        name: 'teacherManage',
        hidden: true,
        component: () => import('@/views/admin/hrm/personnelManagement/teacher/index'),
        meta: {
          title: '人员管理',
          icon: 'cr-icon-jiaoshiguanli',
          activeMenu: 'studentManage',
          requiresAuth: true,
          permissions: ['manage', 'training', 'field']
        }
      },
      // 人员管理-助教管理-列表
      {
        path: 'assistantManage',
        name: 'assistantManage',
        hidden: true,
        component: () => import('@/views/admin/hrm/personnelManagement/assistant/index'),
        meta: {
          title: '人员管理',
          icon: 'cr-icon-jiaoshiguanli',
          hidden: true,
          activeMenu: 'studentManage',
          requiresAuth: true,
          permissions: ['manage', 'training', 'field']
        }
      },

      // 教学事务-考试事务-列表
      {
        name: 'TeachingAffairsExamTraining',
        path: 'TeachingAffairsExam',
        hidden: true,
        component: () => import('@/views/admin/hrm/TeachingAffairs/TeachingAffairsExam/index'),
        meta: {
          title: '教学事务',
          icon: 'cr-icon-jiaoxueshiwu',
          nav: 'affairs'
        }
      },
      // 教学事务-考试事务-详情
      {
        path: 'detail/:id/:view/:examName?/:schedulingCode?',
        name: 'detailExamTraining',
        hidden: true,
        component: () => import('@/views/admin/hrm/TeachingAffairs/TeachingAffairsExam/index'),
        meta: {
          parentTitle: '考试事务',
          parentName: 'TeachingAffairsExamTraining',
          title: '教学事务',
          icon: 'cr-icon-shijuanguanli',
          nav: 'affairs'
        }
      },
      // 教学事务-考试事务-详情-答题记录-详情
      {
        path: 'answerLog/:id/:view/:examName?/:realname?/:schedulingCode?',
        name: 'answerRecordTraining',
        hidden: true,
        component: () => import('@/views/admin/hrm/TeachingAffairs/TeachingAffairsExam/index'),
        meta: {
          parentTitle: '答题记录',
          parentName: 'detailExamTraining',
          title: '教学事务',
          nav: 'affairs',
          icon: 'cr-icon-shijuanguanli'
        }
      },
      // 教学事务-考试的路由
      {
        name: 'studentsListTraining',
        path: 'exam/students',
        hidden: true,
        component: () => import('@/views/admin/hrm/TeachingAffairs/TeachingAffairsExam/detail/students/index'),
        meta: {
          title: '考生列表',
          nav: 'affairs'
        }
      },
      {
        name: 'studentdetailTraining',
        path: 'student/detail',
        hidden: true,
        component: () => import('@/views/admin/hrm/TeachingAffairs/TeachingAffairsExam/detail/students/action/modal-detail'),
        meta: {
        }
      },
      {
        name: 'examReviewTraining',
        path: 'students/reviewTraining',
        hidden: true,
        component: () => import('@/views/admin/hrm/TeachingAffairs/TeachingAffairsExam/detail/students/action/modal-review'),
        meta: {
          title: '教学事务',
          requiresAuth: true,
          permissions: ['manage', 'training'],
          parentTitle: '考生列表',
          parentName: 'detailExamTraining',
          nav: 'affairs',
          icon: 'cr-icon-shijuanguanli'
        }
      },
      {
        name: 'openEnvTraining',
        path: 'students/review/openEnvTraining',
        hidden: true,
        component: () => import('@/views/admin/hrm/TeachingAffairs/TeachingAffairsExam/openEnv/index'),
        meta: {
          title: '教学事务',
          activeMenu: '/manage/training/affairs'
        }
      },
      {
        name: 'affairsConsultExamination',
        path: 'affairs/hrm/TeachingAffairs/consultExamination',
        hidden: true,
        component: () =>
          import('@/views/admin/hrm/TeachingAffairs/consultExamination'),
        meta: {
          title: '教学事务',
          nav: 'affairs',
          icon: 'cr-icon-jiaoxueshiwu',
          requiresAuth: true,
          permissions: ['manage', 'training', 'field']
        }
      },
      {
        name: 'affairsExamination',
        path: 'affairs/hrm/TeachingAffairs/examinationDetails',
        hidden: true,
        component: () =>
          import('@/views/admin/hrm/TeachingAffairs/examinationDetails/index'),
        meta: {
          title: '教学事务',
          parentTitle: '教学事务',
          parentName: 'teacherAffairs',
          icon: 'cr-icon-jiaoxueshiwu',
          activeMenu: '/manage/training/affairs',
          requiresAuth: true,
          permissions: ['manage', 'training', 'field']
        }
      },
      {
        name: 'affairsExamination',
        path: 'affairs/consult/examination',
        hidden: true,
        component: () =>
          import('@/views/admin/hrm/TeachingAffairs/consultExamination'),
        meta: {
          title: '教学事务',
          activeMenu: '/manage/training/affairs',
          icon: 'cr-icon-jiaoxueshiwu',
          requiresAuth: true,
          permissions: ['manage', 'training', 'field']
        }
      },

      // 教学事务-教学方案-列表
      {
        name: 'teachingProgramRecord',
        path: 'affairs/teachingProgramRecord',
        hidden: true,
        component: () => import('@/views/admin/hrm/TeachingAffairs/teachingProgramRecord/index'),
        meta: {
          title: '教学事务',
          nav: 'affairs',
          icon: 'cr-icon-jiaoxuefangan',
          requiresAuth: true,
          permissions: ['manage', 'training', 'field']
        }
      },
      // 教学事务-教学方案-详情
      {
        name: 'headTeachingPlanDetail',
        path: 'programme/plan/detail',
        hidden: true,
        component: () => import('@/views/admin/hrm/TeachingAffairs/teachingProgramRecord/index'),
        meta: {
          parentTitle: '教学方案',
          parentName: 'teachingProgramRecord',
          title: '教学事务',
          icon: 'cr-icon-jiaoxuefangan',
          nav: 'affairs',
          requiresAuth: true,
          permissions: ['manage', 'training', 'field']
        }
      },

      // 教学事务-教学事务-列表
      {
        name: 'teacherAffairs',
        path: 'affairs',
        component: () => import('@/views/admin/hrm/TeachingAffairs/index'),
        meta: {
          title: '教学事务',
          icon: 'cr-icon-jiaoxueshiwu',
          requiresAuth: true,
          permissions: ['manage', 'training', 'field']
        }
      },
      // 教学事务-教学事务-课程详情
      {
        name: 'affairsCourseDetail',
        path: 'affairs/courseDetail',
        hidden: true,
        component: () => import('@/views/course/admin.vue'),
        meta: {
          type: 'full_create_all',
          title: '教学事务',
          parentTitle: '教学事务',
          parentName: 'teacherAffairs',
          nav: 'affairs',
          icon: 'cr-icon-kechengguanli',
          requiresAuth: true,
          permissions: ['manage', 'training', 'field']
        }
      },
      // 教学事务-教学事务-项目详情-小组列表
      {
        name: 'listGroups',
        path: 'listGroups',
        component: () => import('@/views/admin/hrm/TeachingAffairs/index'),
        hidden: true,
        meta: {
          title: '教学事务',
          parentTitle: '教学事务',
          parentName: 'teacherAffairs',
          icon: 'cr-icon-jiaoxueshiwu',
          nav: 'affairs',
          requiresAuth: true,
          permissions: ['manage', 'training', 'field']
        }
      },
      // 教学事务-教学事务-项目详情-小组-详情
      {
        name: 'educationalTasksDetails',
        path: 'educational/tasks/details',
        component: () => import('@/views/admin/hrm/educationalTasks/details.vue'),
        hidden: true,
        meta: {
          title: '教学事务',
          parentTitle: '教学事务',
          parentName: 'teacherAffairs',
          icon: 'cr-icon-jiaoxueshiwu',
          nav: 'affairs',
          requiresAuth: true,
          permissions: ['manage', 'training', 'field']
        }
      },
      // 教学事务-教学事务-项目详情-学生列表
      {
        name: 'studentList',
        path: 'studentList',
        component: () => import('@/views/admin/hrm/TeachingAffairs/index'),
        hidden: true,
        meta: {
          title: '教学事务',
          parentTitle: '教学事务',
          parentName: 'teacherAffairs',
          icon: 'cr-icon-jiaoxueshiwu',
          nav: 'affairs',
          requiresAuth: true,
          permissions: ['manage', 'training', 'field']
        }
      },
      // 教学事务-教学事务-理论课程-详情
      {
        name: 'affairsTheory',
        path: 'affairs/theory',
        hidden: true,
        component: () =>
          import('@/views/admin/hrm/TeachingAffairs/index'),
        meta: {
          title: '教学事务',
          parentTitle: '教学事务',
          icon: 'cr-icon-jiaoxueshiwu',
          parentName: 'teacherAffairs',
          nav: 'affairs',
          requiresAuth: true,
          permissions: ['manage', 'training', 'field']
        }
      },
      // 教学事务-教学事务-仿真课程-详情
      {
        name: 'affairsSimulation',
        path: 'affairs/simulation',
        hidden: true,
        component: () =>
          import('@/views/admin/hrm/TeachingAffairs/index'),
        meta: {
          title: '教学事务',
          parentTitle: '教学事务',
          parentName: 'teacherAffairs',
          icon: 'cr-icon-jiaoxueshiwu',
          nav: 'affairs',
          requiresAuth: true,
          permissions: ['manage', 'training', 'field']
        }
      },
      // 教学事务-教学事务-仿真课程-详情-学生答题记录
      {
        name: 'affairsConsultSimulation',
        path: 'affairs/hrm/Teachinsrc/views/admin/hrm/TeachingAffairs/detail-header.vuegAffairs/consultSimulation',
        hidden: true,
        component: () =>
          import('@/views/admin/hrm/TeachingAffairs/consultSimulation'),
        meta: {
          title: '教学事务',
          nav: 'affairs',
          icon: 'cr-icon-jiaoxueshiwu',
          requiresAuth: true,
          permissions: ['manage', 'training', 'field']
        }
      },
      // 教学事务-教学事务-模拟联练习-分析
      {
        name: 'examanalysis',
        path: 'affairs/examanalysis',
        hidden: true,
        component: () =>
          import('@/views/admin/hrm/TeachingAffairs/examanalysis'),
        meta: {
          title: '教学事务123123123',
          parentName: 'teacherAffairs',
          activeMenu: '/manage/training/affairs',
          requiresAuth: true,
          permissions: ['manage', 'training', 'field']
        }
      },

      // 教学质量中心-教学数据统计
      {
        path: 'teachingDataCount',
        name: 'teachingDataCount',
        component: () => import('@/views/admin/hrm/teachingQualityCenter/teachingDataCount/index'),
        meta: {
          title: '教学质量中心',
          icon: 'cr-icon-jiaoxuezhiliangzhongxin',
          requiresAuth: true,
          permissions: ['manage']
        }
      },
      // 教学质量中心-班级能力统计
      {
        path: 'classAbilityCount',
        name: 'classAbilityCount',
        hidden: true,
        component: () => import('@/views/admin/hrm/teachingQualityCenter/classAbilityCount/index'),
        meta: {
          title: '教学质量中心',
          parentTitle: '班级能力统计',
          icon: 'cr-icon-tikuguanli',
          parentName: 'teachingDataCount',
          requiresAuth: true,
          permissions: ['manage'],
          nav: 'teachingDataCount'
        }
      },

      // 教学方案
      {
        name: 'teacherProgramme',
        path: 'hrm/programme',
        component: () => import('@/views/admin/hrm/TeachingProgram/index'),
        meta: {
          title: '教学方案',
          icon: 'cr-icon-jiaoxuefangan',
          requiresAuth: true,
          permissions: ['manage', 'training', 'field']
        }
      },
      // 教学方案详情
      {
        name: 'teachingPlanDetail',
        path: 'hrm/programme/detail',
        hidden: true,
        component: () => import('@/views/admin/hrm/TeachingProgram/index'),
        meta: {
          title: '教学方案',
          parentTitle: '教学方案',
          parentName: 'teacherProgramme',
          icon: 'cr-icon-jiaoxuefangan',
          requiresAuth: true,
          nav: 'hrm/programme',
          permissions: ['manage', 'training', 'field']
        }
      },
      // 教学方案详情-课程管理-详情
      {
        name: 'TeacherProgrammeCourseDetail',
        path: 'hrm/programme/course/detail',
        hidden: true,
        component: () => import('@/views/course/admin.vue'),
        meta: {
          type: 'full_create_all',
          title: '教学方案',
          parentTitle: '教学方案',
          parentName: 'teachingPlanDetail',
          nav: 'hrm/programme',
          icon: 'cr-icon-kechengguanli',
          requiresAuth: true,
          permissions: ['manage', 'training', 'salary']
        }
      },
      // 教学方案详情-课程管理-详情-学生答题记录
      {
        name: 'consultTheory',
        path: 'hrm/consult/theory',
        component: () =>
          import('@/views/admin/hrm/TeachingAffairs/consultTheory'),
        hidden: true,
        meta: {
          title: '教学事务',
          requiresAuth: true,
          icon: 'cr-icon-jiaoxueshiwu',
          nav: 'affairs',
          permissions: ['manage', 'training', 'field']
        }
      },

      // 课程管理
      {
        path: 'salary/compute',
        name: 'salaryCompute',
        component: () => import('@/views/admin/hrm/salary/course-management/index'),
        meta: {
          title: '课程管理',
          icon: 'cr-icon-kechengguanli',
          requiresAuth: true,
          permissions: ['manage', 'training', 'salary']
        }
      },
      // 课程-试卷详情
      {
        path: '/exercisesPaper',
        name: 'exercisesPaper',
        hidden: true,
        component: () => import('@/components/courseDetail/SimulatedExercises/ExercisesPaper'),
        meta: {
          type: 'full_create',
          title: '试卷详情',
          icon: 'enterprise',
          nav: '/student/requiredCourse',
          requiresAuth: true
        }
      },
      // 课程详情-随堂练习-实操环境界面
      {
        path: 'exercisesPaperEnv',
        name: 'exercisesPaperEnv',
        hidden: true,
        component: () => import('@/components/courseDetail/SimulatedExercises/ExercisesEnv'),
        meta: {
          type: 'full_create_all',
          title: '实验环境',
          icon: 'enterprise',
          requiresAuth: true
        }
      },
      // 课程-试卷详情
      {
        path: '/cannotDynamicPaper',
        name: 'cannotDynamicPaper',
        hidden: true,
        component: () => import('@/components/courseDetail/SimulatedExercises/cannotDynamicPaper'),
        meta: {
          type: 'full_create',
          title: '试卷详情',
          icon: 'enterprise',
          nav: '/student/requiredCourse',
          requiresAuth: true
        }
      },
      // 课程管理-创建课程
      {
        name: 'myCourseCreate',
        path: 'salary/compute/create',
        component: () => import('@/views/admin/hrm/salary/course-management/create'),
        hidden: true,
        meta: {
          title: '创建课程',
          type: 'full_create',
          icon: 'cr-icon-kechengguanli',
          activeMenu: '/manage/training/hrm/salary/compute',
          requiresAuth: true,
          permissions: ['manage', 'training', 'salary']
        }
      },
      // 课程管理-详情
      {
        name: 'courseDetailLibraryEdit',
        path: 'courseDetailEdit',
        hidden: true,
        component: () => import('@/views/admin/hrm/salary/course-management/detail/index'),
        meta: {
          title: '课程管理',
          parentTitle: '课程管理',
          parentName: 'salaryCompute',
          activeMenu: 'salaryCompute',
          icon: 'cr-icon-kechengguanli',
          requiresAuth: true,
          permissions: ['manage', 'training', 'salary']
        }
      },
      // 课程管理-详情（可编辑）
      {
        name: 'courseDetailLibrary',
        path: 'courseDetail',
        hidden: true,
        component: () => import('@/views/course/admin.vue'),
        meta: {
          type: 'full_create_all',
          title: '课程管理',
          parentTitle: '课程管理',
          parentName: 'salaryCompute',
          activeMenu: 'salaryCompute',
          icon: 'cr-icon-kechengguanli',
          requiresAuth: true,
          permissions: ['manage', 'training', 'salary']
        }
      },
      // 课程管理-实验拓扑
      {
        name: 'experiment_topo',
        path: '/experiment_topo',
        hidden: true,
        component: () => import('@/components/courseDetail/experimentTopo/index.vue'),
        meta: {
          title: '实验拓扑',
          type: 'full_create'
        }
      },

      // 课程内容
      {
        path: 'content/index',
        name: 'trainingContent',
        component: () => import('@/views/admin/hrm/content/index'),
        meta: {
          title: '课程内容',
          icon: 'cr-icon-jichukecheng',
          requiresAuth: true,
          permissions: ['manage', 'training', 'salary']
        }
      },
      // 课程内容-详情
      {
        path: 'content/detail',
        name: 'contentDetail',
        hidden: true,
        component: () => import('@/views/admin/hrm/content/index'),
        meta: {
          requiresAuth: true,
          parentTitle: '课程内容',
          parentName: 'trainingContent',
          icon: 'cr-icon-jichukecheng',
          activeMenu: 'trainingContent',
          permissions: ['manage', 'training', 'salary']
        }
      },
      // 课程内容-创建
      {
        name: 'trainingContentCreate',
        path: '/manage/training/content/create',
        component: () => import('@/views/admin/hrm/content/create'),
        hidden: true,
        meta: {
          title: '课程内容',
          type: 'full_create',
          activeMenu: '/manage/training/salary/compute',
          requiresAuth: true,
          permissions: ['manage', 'training', 'salary']
        }
      },
      {
        name: 'trainingContentPractice',
        path: '/teacher/content/practice',
        component: () => import('@/views/admin/hrm/content/practice'),
        hidden: true,
        meta: {
          title: '随堂练习题目',
          activeMenu: '/manage/training/salary/compute',
          requiresAuth: true,
          permissions: ['manage', 'training', 'salary']
        }
      },

      // 知识点管理
      {
        path: 'knowledgeManage',
        name: 'knowledgeManage',
        component: () => import('@/views/admin/hrm/knowledge/index'),
        meta: {
          title: '知识点管理',
          icon: 'cr-icon-zhishidianguanli',
          requiresAuth: true,
          permissions: ['manage', 'training', 'field']
        }
      },
      // 知识点管理-详情
      {
        path: 'knowledgeManage/detail/:id/:view/:name',
        name: 'knowledgeDetails',
        component: () => import('@/views/admin/hrm/knowledge/index'),
        hidden: true,
        meta: {
          parentTitle: '知识点管理',
          parentName: 'knowledgeManage',
          icon: 'cr-icon-zhishidianguanli',
          activeMenu: 'knowledgeManage',
          requiresAuth: true,
          permissions: ['manage', 'training', 'field']
        }
      }
    ]
  },
  {
    ...layout(
      {
        permissions: ['manage', 'penetration'],
        title: '渗透测试系统',
        icon: 'permeate'
      },
      '/penetrant'
    ),
    children: [
      {
        name: 'assetsGroup',
        path: 'assetsGroup',
        component: () => import('@/views/admin/match/index'),
        meta: {
          title: '资产管理',
          icon: 'cr-icon-zichanguanli',
          requiresAuth: true,
          permissions: ['manage', 'penetration']
        }
      },
      {
        name: 'application',
        path: 'application',
        component: () => import('@/views/admin/match/index'),
        meta: {
          title: '工具库管理',
          icon: 'cr-icon-gongjuku',
          requiresAuth: true,
          permissions: ['manage', 'penetration']
        }
      },
      {
        name: 'ExportIpPool',
        path: 'exportIpPool',
        component: () => import('@/views/admin/match/index'),
        meta: {
          title: '工作站管理',
          icon: 'cr-icon-gongzuozhanguanli',
          requiresAuth: true,
          permissions: ['manage', 'penetration']
        }
      },
      {
        name: 'ipAddressPoolManage',
        path: 'ipAddressPoolManage',
        component: () => import('@/views/admin/match/index'),
        meta: {
          title: 'IP地址池管理',
          icon: 'cr-icon-chukouIPchiguanli',
          requiresAuth: true,
          permissions: ['manage', 'penetration']
        }
      },
      {
        name: 'VPNServiceManage',
        path: 'VPNServiceManage',
        component: () => import('@/views/admin/match/index'),
        meta: {
          title: 'VPN服务管理',
          icon: 'cr-icon-fuwuguanli',
          requiresAuth: true,
          permissions: ['manage', 'penetration']
        }
      },
      {
        path: 'manage/log',
        name: 'manageLog',
        component: () => import('@/views/admin/match/index'),
        meta: {
          title: '监控日志',
          icon: 'cr-icon-jiankongrizhi',
          requiresAuth: true,
          permissions: ['manage', 'penetration']
        },
        alwaysShow: true,
        children: [
          {
            path: 'sys',
            component: () => import('@/views/admin/match/index'),
            meta: {
              title: '系统操作日志',
              icon: 'cr-icon-xitongcaozuoxitong',
              requiresAuth: true,
              permissions: ['manage', 'penetration']
            }
          },
          {
            path: 'login',
            component: () => import('@/views/admin/match/index'),
            meta: {
              title: '登录日志',
              icon: 'cr-icon-denglurizhi',
              requiresAuth: true,
              permissions: ['manage', 'penetration']
            }
          }
        ]
      },
      {
        name: 'navigationbar',
        path: 'navigationbar',
        component: () => import('@/views/admin/match/index'),
        meta: {
          title: '系统设置',
          icon: 'cr-icon-xitongguanli',
          requiresAuth: true,
          permissions: ['manage', 'penetration']
        }
      },
      {
        name: 'apiMenu',
        path: 'apiMenu',
        component: () => import('@/views/admin/match/index'),
        meta: {
          title: '菜单管理',
          icon: 'cr-icon-caidanguanli',
          requiresAuth: true,
          permissions: ['manage', 'penetration']
        }
      },
      {
        name: 'manage/system-other',
        path: 'manage/system-other', // 其他配置
        component: () => import('@/views/admin/match/index'),
        meta: {
          title: '标签管理',
          icon: 'cr-icon-biaoqianguanli',
          requiresAuth: true,
          permissions: ['manage', 'penetration']
        }
      },
      {
        name: 'dictType',
        path: 'dictType',
        component: () => import('@/views/admin/match/index'),
        meta: {
          title: '字典管理',
          icon: 'cr-icon-zidianguanli',
          requiresAuth: true,
          permissions: ['manage', 'penetration']
        }
      },
      {
        name: 'dictData',
        path: 'dictData',
        component: () => import('@/views/admin/match/index'),
        meta: {
          title: '字典数据',
          icon: 'cr-icon-zidianbshuju',
          requiresAuth: true,
          permissions: ['manage', 'penetration']
        }
      },
      {
        name: 'notice',
        path: 'notice',
        component: () => import('@/views/admin/match/index'),
        meta: {
          title: '公告消息',
          icon: 'cr-icon-gonggaoxinxi',
          requiresAuth: true,
          permissions: ['manage', 'penetration']
        }
      }
    ]
  },
  {
    ...layout(
      {
        permissions: ['manage', 'recurrent'],
        title: '漏洞复现系统',
        icon: 'recurrent'
      },
      '/recurrent'
    ),
    alwaysShow: true,
    children: [
      // 漏洞靶标
      {
        path: 'target',
        name: 'target',
        component: () => import('@/views/admin/loophole/target/loopholeTarget/index'),
        meta: {
          title: '漏洞靶标',
          icon: 'cr-icon-loudongfuxian',
          requiresAuth: true,
          permissions: ['manage', 'accumulate']
        }
      },
      // 漏洞实例
      {
        path: 'example',
        name: 'example',
        hidden: true,
        component: () => import('@/views/admin/loophole/target/example/index'),
        meta: {
          activeMenu: 'target',
          requiresAuth: true,
          permissions: ['manage', 'accumulate']
        }
      },
      // 创建漏洞靶标
      {
        path: 'target/create',
        name: 'targetCreate',
        hidden: true,
        component: () => import('@/views/admin/loophole/target/action/create'),
        meta: {
          type: 'full_create',
          requiresAuth: true,
          permissions: ['manage', 'accumulate']
        }
      },
      // 漏洞靶标详情
      {
        path: 'target/detail/:id/:view',
        name: 'targetDetail',
        hidden: true,
        component: () => import('@/views/admin/loophole/target/loopholeTarget/index'),
        meta: {
          parentTitle: '漏洞靶标',
          parentName: 'target',
          activeMenu: 'target',
          icon: 'cr-icon-loudongfuxian',
          requiresAuth: true,
          permissions: ['manage', 'accumulate']
        }
      }
    ]
  },
  // 检测管理系统start
  {
    ...layout(
      {
        permissions: ['manage', 'testing'],
        title: '检测管理系统',
        icon: 'detection'
      },
      '/testing'
    ),
    alwaysShow: true,
    children: [
      {
        name: 'overview',
        path: 'overview',
        component: () => import('@/views/admin/testing/testingOverview/overview/index.vue'),
        meta: {
          title: '概览',
          icon: 'cr-icon-gaikuang',
          requiresAuth: true,
          permissions: ['manage', 'testing', 'overview']
        }
      },
      // 厂商管理
      {
        path: 'manufacturerManage',
        name: 'manufacturerManage',
        component: () => import('@/views/admin/testing/manufacturerManage/index'),
        meta: {
          title: '厂商管理',
          icon: 'cr-icon-changshangguanli',
          requiresAuth: true,
          permissions: ['manage', 'testing', 'vendor']
        }
      },
      // 厂商管理详情
      {
        path: 'manufacturer/detail/:id/:view',
        name: 'manufacturerDetail',
        hidden: true,
        component: () => import('@/views/admin/testing/manufacturerManage/index'),
        meta: {
          parentTitle: '厂商管理',
          parentName: 'manufacturerManage',
          activeMenu: 'manufacturerManage',
          icon: 'cr-icon-timu',
          requiresAuth: true,
          permissions: ['manage', 'testing', 'vendor']
        }
      },
      // 检测申请
      {
        path: 'testingApplication',
        name: 'testingApplication',
        redirect: '/testing/testingApplication/applicationNotCompleted',
        component: { render: h => h('router-view') },
        meta: {
          title: '检测申请',
          icon: 'cr-icon-xukeguanli1',
          requiresAuth: true,
          permissions: ['manage', 'testing', 'application']
        },
        children: [
          // 未完成审批
          {
            path: 'applicationNotCompleted',
            name: 'applicationNotCompleted',
            component: () => import('@/views/admin/testing/testingApplication/noPassed/index'),
            meta: {
              title: '未完成审批',
              requiresAuth: true,
              permissions: ['manage', 'testing', 'application', 'unApplication']
            }
          },
          // 已完成审批
          {
            path: 'applicationCompleted',
            name: 'applicationCompleted',
            component: () => import('@/views/admin/testing/testingApplication/passed/index'),
            meta: {
              title: '已完成审批',
              requiresAuth: true,
              permissions: ['manage', 'testing', 'application', 'finishApplication']
            }
          }
        ]
      },
      // 提交检测申请
      {
        path: 'testingApplicationCreate',
        name: 'testingApplicationCreate',
        hidden: true,
        component: () => import('@/views/admin/testing/testingApplication/noPassed/create/create.vue'),
        meta: {
          type: 'full_create',
          title: '提交检测申请',
          requiresAuth: true,
          permissions: ['manage', 'testing', 'application', 'unApplication', 'unApplicationList', 'applicationFormCreate']
        }
      },
      // 编辑检测申请
      {
        path: 'testingApplicationEdit/:id',
        name: 'testingApplicationEdit/:id',
        hidden: true,
        component: () => import('@/views/admin/testing/testingApplication/noPassed/edit/edit.vue'),
        meta: {
          type: 'full_create',
          title: '编辑检测申请',
          requiresAuth: true,
          permissions: ['manage', 'testing', 'application', 'unApplication', 'unApplicationList', 'applicationFormUpdate']
        }
      },
      // 检测申请详情
      {
        path: 'testingApplicationDetail/:id/:view',
        name: 'testingApplicationDetail',
        component: () => import('@/views/admin/testing/testingApplication/noPassed/index.vue'),
        hidden: true,
        meta: {
          parentTitle: '检测申请',
          title: '检测申请详情',
          parentName: 'applicationNotCompleted',
          activeMenu: 'applicationNotCompleted',
          requiresAuth: true,
          permissions: ['manage', 'testing', 'application', 'unApplication', 'applicationDetail']
        }
      },
      {
        path: 'testingItems',
        name: 'testingItems',
        component: () => import('@/views/admin/testing/testingItems/index'),
        meta: {
          title: '检测项目',
          icon: 'cr-icon-timu',
          requiresAuth: true,
          permissions: ['manage', 'testing', 'project', 'projectList']
        }
      },
      // 检测项目详情
      {
        path: 'testing/detail/:id/:view',
        name: 'testing_detail',
        hidden: true,
        component: () => import('@/views/admin/testing/testingItems/detail/index'),
        meta: {
          parentTitle: '检测项目',
          title: '检测项目',
          icon: 'cr-icon-jiance',
          parentName: 'testingItems',
          activeMenu: 'testingItems',
          requiresAuth: true,
          permissions: ['manage', 'testing', 'project', 'projectDetail']
        }
      },
      // 创建检测项目
      {
        path: 'testing/create',
        name: 'testing_create',
        hidden: true,
        component: () => import('@/views/admin/testing/testingItems/create'),
        meta: {
          type: 'full_create',
          parentTitle: '检测项目',
          title: '创建检测项目',
          icon: 'cr-icon-jiance',
          parentName: 'testingItems',
          activeMenu: 'testingItems',
          requiresAuth: true,
          permissions: ['manage', 'testing', 'project', 'projectList', 'projectCreate']
        }
      },
      // 编辑检测项目
      {
        path: 'testing/edit/:id/:status',
        name: 'testing_edit',
        hidden: true,
        component: () => import('@/views/admin/testing/testingItems/create'),
        meta: {
          type: 'full_create',
          parentTitle: '检测项目',
          title: '编辑检测项目',
          icon: 'cr-icon-jiance',
          parentName: 'testingItems',
          activeMenu: 'testingItems',
          requiresAuth: true,
          permissions: ['manage', 'testing', 'project', 'projectList', 'projectUpdate']
        }
      },
      {
        path: 'testing/submit/:id',
        name: 'testing_submit',
        hidden: true,
        component: () => import('@/views/admin/testing/testingItems/submit'),
        meta: {
          type: 'full_create',
          parentTitle: '检测项目',
          title: '项目资料送审',
          icon: 'cr-icon-jiance',
          parentName: 'testingItems',
          activeMenu: 'testingItems',
          requiresAuth: true,
          permissions: ['manage', 'testing', 'project', 'projectList', 'projectTestApply']
        }
      },
      {
        path: 'testing/testingTaskDetail/:id/:projectId/:view',
        name: 'testingTask_detail',
        hidden: true,
        component: () => import('@/views/admin/testing/testingItems/detail/index'),
        meta: {
          parentTitle: '检测项目',
          title: '测试任务详情',
          icon: 'cr-icon-jiance',
          parentName: 'testingItems',
          activeMenu: 'testingItems',
          requiresAuth: true,
          permissions: ['manage', 'testing', 'project', 'projectDetail', 'projectTask', 'taskDetailView']
        }
      },
      // 资源申请审核项目资料
      {
        path: 'testing/resourceApplyExamine/:id/:projectId',
        name: 'resourceApplyExamine',
        hidden: true,
        component: () => import('@/views/admin/testing/testingItems/detail/applyRecord/resourceApply/detail/examine'),
        meta: {
          type: 'full_create',
          parentTitle: '资源申请',
          title: '资源申请详情',
          icon: 'cr-icon-jiance',
          parentName: 'testing_detail',
          activeMenu: 'testingItems',
          requiresAuth: true,
          permissions: ['manage', 'testing', 'project']
        }
      },
      // 问题管理-未修复问题
      {
        path: 'unFixedQuestionList',
        name: 'unFixedQuestionList',
        component: () => import('@/views/admin/testing/questionManage/unFixedQuestion/index'),
        meta: {
          title: '问题管理',
          icon: 'cr-icon-loudongfuxian',
          requiresAuth: true,
          permissions: ['manage', 'testing', 'problem']
        }
      },
      // 问题管理-全部问题
      {
        path: 'allQuestionList',
        name: 'allQuestionList',
        hidden: true,
        component: () => import('@/views/admin/testing/questionManage/allQuestion/index'),
        meta: {
          title: '问题管理',
          icon: 'cr-icon-loudongfuxian',
          requiresAuth: true,
          activeMenu: 'unFixedQuestionList',
          permissions: ['manage', 'testing', 'problem']
        }
      },
      // 新增问题-未修复问题
      {
        path: 'unFixedQuestionList/create',
        name: 'createTestingQuestion',
        hidden: true,
        component: () => import('@/views/admin/testing/questionManage/unFixedQuestion/create'),
        meta: {
          type: 'full_create',
          requiresAuth: true,
          permissions: ['manage', 'testing', 'problem']
        }
      },
      // 编辑问题
      {
        path: 'unFixedQuestionList/edit/:id/',
        name: 'editTestingQuestion',
        hidden: true,
        component: () => import('@/views/admin/testing/questionManage/unFixedQuestion/create'),
        meta: {
          type: 'full_create',
          requiresAuth: true,
          permissions: ['manage', 'testing', 'problem']
        }
      },
      // 问题管理详情-未修复问题
      {
        path: 'unFixedQuestion/detail/:id/:view',
        name: 'testingQuestionDetail',
        hidden: true,
        component: () => import('@/views/admin/testing/questionManage/unFixedQuestion/index'),
        meta: {
          parentTitle: '问题管理',
          parentName: 'unFixedQuestionList',
          activeMenu: 'unFixedQuestionList',
          tabsActive: 'unFixedQuestionList',
          icon: 'cr-icon-loudongfuxian',
          requiresAuth: true,
          permissions: ['manage', 'testing', 'problem']
        }
      },
      // 问题管理详情-全部问题
      {
        path: 'allQuestionList/detail/:id/:view',
        name: 'testingAllQuestionDetail',
        hidden: true,
        component: () => import('@/views/admin/testing/questionManage/allQuestion/index.vue'),
        meta: {
          parentTitle: '问题管理',
          parentName: 'unFixedQuestionList',
          activeMenu: 'unFixedQuestionList',
          tabsActive: 'allQuestionList',
          icon: 'cr-icon-loudongfuxian',
          requiresAuth: true,
          permissions: ['manage', 'testing', 'problem']
        }
      },
      // 测试用例
      {
        path: 'testCases',
        name: 'testCases',
        component: () => import('@/views/admin/testing/testingCases/testCases/index'),
        meta: {
          title: '测试用例',
          icon: 'cr-icon-ceshiyongli',
          requiresAuth: true,
          permissions: ['manage', 'testing', 'testing', 'case']
        }
      },
      // 测试用例详情
      {
        path: 'testingCaseDetail/:id/:view',
        name: 'testCaseDetail',
        component: () => import('@/views/admin/testing/testingCases/testCases/index.vue'),
        hidden: true,
        meta: {
          parentTitle: '测试用例',
          title: '测试用例详情',
          parentName: 'testCases',
          activeMenu: 'testCases',
          requiresAuth: true,
          permissions: ['manage', 'testing', 'testing', 'case', 'caseDetail']
        }
      },
      // 测试套件
      {
        path: 'testKits',
        name: 'testKits',
        component: () => import('@/views/admin/testing/testingCases/testKits/index'),
        meta: {
          title: '测试套件',
          icon: 'cr-icon-ceshitaojian',
          requiresAuth: true,
          permissions: ['manage', 'testing', 'testing', 'suite']
        }
      },
      // 测试套件详情
      {
        path: 'testingKitDetail/:id/:view',
        name: 'testKitDetail',
        component: () => import('@/views/admin/testing/testingCases/testKits/index.vue'),
        hidden: true,
        meta: {
          parentTitle: '测试套件',
          title: '测试套件详情',
          parentName: 'testKits',
          activeMenu: 'testKits',
          requiresAuth: true,
          permissions: ['manage', 'testing', 'testing', 'suite', 'suiteDetail']
        }
      },
      {
        name: 'testingCaseCreate',
        path: 'testingCaseCreate',
        hidden: true,
        component: () => import('@/views/admin/testing/testingCases/testCases/create/index.vue'),
        meta: {
          type: 'full_create',
          requiresAuth: true,
          parentTitle: '测试用例',
          title: '创建测试用例',
          parentName: 'testCases',
          activeMenu: 'testCases',
          permissions: ['manage', 'testing', 'testing', 'case', 'caseList', 'caseCreate']
        }
      },
      // 检测流程
      {
        path: 'testingProcess',
        name: 'testingProcess',
        component: () => import('@/views/admin/testing/testingProcess/index'),
        meta: {
          title: '检测流程',
          icon: 'cr-icon-jianceliucheng',
          requiresAuth: true,
          permissions: ['manage', 'testing', 'testProcesses']
        }
      },
      // 检测流程详情
      {
        path: 'testingProcess/detail/:id/:view',
        name: 'testingProcessDetail',
        hidden: true,
        component: () => import('@/views/admin/testing/testingProcess/index'),
        meta: {
          parentTitle: '检测流程',
          parentName: 'testingProcess',
          activeMenu: 'testingProcess',
          icon: 'cr-icon-jiaoxueshiwu',
          requiresAuth: true,
          permissions: ['manage', 'testing', 'testProcesses']
        }
      },
      // 档案库
      {
        path: 'sampleLibrary',
        name: 'sampleLibrary',
        component: () => import('@/views/admin/testing/sampleLibrary/index'),
        meta: {
          title: '档案库',
          icon: 'cr-icon-danganku',
          requiresAuth: true,
          permissions: ['manage', 'testing', 'sample']
        }
      },
      // 检测项目详情
      {
        path: 'sampleLibrary/detail/:id/:view',
        name: 'sampleLibraryDetail',
        hidden: true,
        component: () => import('@/views/admin/testing/sampleLibrary/detail/index'),
        meta: {
          parentTitle: '档案库',
          title: '检测项目',
          parentName: 'sampleLibrary',
          activeMenu: 'sampleLibrary',
          requiresAuth: true,
          permissions: ['manage', 'testing', 'sample']
        }
      },
      {
        path: 'sampleLibrary/sampleLibraryDetail/:id/:projectId/:view',
        name: 'sampleLibraryTaskDetail',
        hidden: true,
        component: () => import('@/views/admin/testing/sampleLibrary/detail/index'),
        meta: {
          parentTitle: '档案库',
          title: '测试任务详情',
          parentName: 'sampleLibraryDetail',
          activeMenu: 'sampleLibrary',
          requiresAuth: true,
          permissions: ['manage', 'testing', 'sample']
        }
      },
      // 档案库详情tab设备日志详情
      {
        path: 'sampleLibraryCommandDetail/:id/:view',
        name: 'sampleLibraryCommandDetail',
        component: () => import('@/views/admin/testing/sampleLibrary/index.vue'),
        hidden: true,
        meta: {
          parentTitle: '档案库',
          title: '设备日志详情',
          parentName: 'sampleLibraryDetail',
          activeMenu: 'sampleLibrary',
          requiresAuth: true,
          permissions: ['manage', 'testing', 'sample']
        }
      }
    ]
  },
  // 检测管理系统end
  {
    ...layout(
      {
        permissions: ['manage', 'accumulate'],
        title: '基础资源',
        icon: 'baseResource'
      },
      '/baseResource'
    ),
    alwaysShow: true,
    children: [
      // 理论题列表
      {
        path: 'theory',
        name: 'theory',
        component: () => import('@/views/admin/accumulate/questionBank/theory/index'),
        meta: {
          title: '题目',
          icon: 'cr-icon-timu',
          requiresAuth: true,
          permissions: ['manage', 'accumulate']
        }
      },
      // 靶机题列表
      {
        path: 'targetDevice',
        name: 'targetDevice',
        hidden: true,
        component: () => import('@/views/admin/accumulate/questionBank/targetDevice/index'),
        meta: {
          activeMenu: 'theory',
          requiresAuth: true,
          permissions: ['manage', 'accumulate']
        }
      },
      // 仿真题列表
      {
        path: 'simulation',
        name: 'simulation',
        hidden: true,
        component: () => import('@/views/admin/accumulate/questionBank/simulation/index'),
        meta: {
          activeMenu: 'theory',
          requiresAuth: true,
          permissions: ['manage', 'accumulate']
        }
      },
      // 创建
      {
        path: 'questionBank/create/:bankType/:questionDepotId?',
        name: 'questionBankCreate',
        hidden: true,
        component: () => import('@/views/admin/accumulate/questionBank/action/create'),
        meta: {
          type: 'full_create',
          requiresAuth: true,
          permissions: ['manage', 'accumulate']
        }
      },
      // 编辑
      {
        path: 'questionBank/edit/:id/:bankType',
        name: 'questionBankEdit',
        hidden: true,
        component: () => import('@/views/admin/accumulate/questionBank/action/create'),
        meta: {
          type: 'full_create',
          requiresAuth: true,
          permissions: ['manage', 'accumulate']
        }
      },
      // 理论题详情
      {
        path: 'theory/detail/:id/:view',
        name: 'theoryDetail',
        hidden: true,
        component: () => import('@/views/admin/accumulate/questionBank/theory/index'),
        meta: {
          parentTitle: '理论题',
          parentName: 'theory',
          activeMenu: 'theory',
          icon: 'cr-icon-timu',
          requiresAuth: true,
          permissions: ['manage', 'accumulate']
        }
      },
      // 靶机题详情
      {
        path: 'targetDevice/detail/:id/:view',
        name: 'targetDeviceDetail',
        hidden: true,
        component: () => import('@/views/admin/accumulate/questionBank/targetDevice/index'),
        meta: {
          parentTitle: '靶机题',
          parentName: 'targetDevice',
          activeMenu: 'theory',
          icon: 'cr-icon-timu',
          requiresAuth: true,
          permissions: ['manage', 'accumulate']
        }
      },
      // 仿真题详情
      {
        path: 'simulation/detail/:id/:view',
        name: 'simulationDetail',
        hidden: true,
        component: () => import('@/views/admin/accumulate/questionBank/simulation/index'),
        meta: {
          parentTitle: '仿真题',
          parentName: 'simulation',
          activeMenu: 'theory',
          icon: 'cr-icon-timu',
          requiresAuth: true,
          permissions: ['manage', 'accumulate']
        }
      },
      // 题库 =============== start
      {
        path: 'questionLibrary',
        name: 'questionLibrary',
        component: () => import('@/views/admin/accumulate/questionLibrary/index'),
        meta: {
          title: '题库',
          icon: 'cr-icon-tikuguanli',
          requiresAuth: true,
          permissions: ['manage', 'accumulate']
        }
      },
      // 题库详情
      {
        path: 'library/detail/:id/:view',
        name: 'libraryDetail',
        hidden: true,
        component: () => import('@/views/admin/accumulate/questionLibrary/index'),
        meta: {
          parentTitle: '题库',
          parentName: 'questionLibrary',
          activeMenu: 'questionLibrary',
          icon: 'cr-icon-tikuguanli',
          requiresAuth: true,
          permissions: ['manage', 'accumulate']
        }
      },
      // 题库 =============== end
      // 试卷列表
      {
        path: 'exam',
        name: 'exam',
        component: () => import('@/views/admin/accumulate/exam/index'),
        meta: {
          title: '试卷',
          icon: 'cr-icon-shijuanguanli',
          requiresAuth: true,
          permissions: ['manage', 'accumulate']
        }
      },
      // 试卷创建、编辑
      {
        path: 'exam/create',
        name: 'examCreate',
        component: () => import('@/views/admin/accumulate/exam/create/index'),
        hidden: true,
        meta: {
          type: 'full_create',
          title: '创建试卷',
          requiresAuth: true,
          permissions: ['manage', 'accumulate']
        }
      },
      // 试卷详情
      {
        path: 'exam/detail/:id/:view',
        name: 'examDetail',
        hidden: true,
        component: () => import('@/views/admin/accumulate/exam/index'),
        meta: {
          parentTitle: '试卷',
          parentName: 'exam',
          activeMenu: 'exam',
          icon: 'cr-icon-shijuanguanli',
          requiresAuth: true,
          permissions: ['manage', 'accumulate']
        }
      }
    ]
  },
  {
    ...layout(
      {
        permissions: ['manage', 'accumulate'],
        title: '资产库',
        icon: 'assetRepo'
      },
      '/assetRepo'
    ),
    alwaysShow: true,
    children: [
      {
        path: 'hole',
        name: 'hole',
        component: () => import('@/views/admin/loophole/hole/index'),
        meta: {
          title: '漏洞库',
          icon: 'cr-icon-loudongku',
          requiresAuth: true,
          permissions: ['manage', 'accumulate', 'hole']
        }
      },
      {
        path: 'hole/create',
        name: 'holeCreate',
        component: () => import('@/views/admin/loophole/hole/create/index'),
        hidden: true,
        meta: {
          type: 'full_create',
          title: '创建漏洞',
          requiresAuth: true,
          permissions: ['manage', 'accumulate']
        }
      },
      {
        path: 'hole/update',
        name: 'holeUpdate',
        component: () => import('@/views/admin/loophole/hole/update/index'),
        hidden: true,
        meta: {
          type: 'full_create',
          title: '更新补丁',
          requiresAuth: true,
          permissions: ['manage', 'accumulate']
        }
      },
      {
        path: 'hole/detail/:id/:view',
        name: 'holeDetail',
        hidden: true,
        component: () => import('@/views/admin/loophole/hole/index'),
        meta: {
          parentTitle: '漏洞',
          parentName: 'hole',
          activeMenu: 'hole',
          icon: 'cr-icon-loudongku',
          requiresAuth: true,
          permissions: ['manage', 'accumulate']
        }
      },
      {
        path: 'knowledge',
        name: 'knowledge',
        component: () => import('@/views/admin/sourceLibrary/knowledge'),
        meta: {
          title: '装备库',
          icon: 'cr-icon-danyaoku',
          requiresAuth: true,
          permissions: ['manage', 'accumulate', 'knowledge']
        }
      },
      {
        name: 'knowledge_detail',
        path: 'knowledge/detail/:id/:view',
        hidden: true,
        component: () => import('@/views/admin/sourceLibrary/knowledge'),
        meta: {
          parentTitle: '装备库',
          icon: 'cr-icon-danyaoku',
          activeMenu: 'knowledge',
          parentName: 'knowledge',
          requiresAuth: true,
          permissions: ['manage', 'accumulate', 'knowledge']
        }
      },
      {
        path: 'tool',
        name: 'tool',
        component: () => import('@/views/admin/sourceLibrary/tool'),
        meta: {
          title: '工具库',
          icon: 'cr-icon-gongjuku',
          requiresAuth: true,
          permissions: ['manage', 'accumulate', 'tool']
        }
      }
    ]
  },
  {
    ...layout(
      {
        permissions: ['manage', 'accumulate'],
        title: '知识体系',
        icon: 'knowledgeSys'
      },
      '/knowledgeSys'
    ),
    alwaysShow: true,
    children: [
      // 技能点
      {
        path: 'skill/point',
        name: 'skillPoint',
        component: () => import('@/views/admin/accumulate/skillPoint/index'),
        meta: {
          title: '技能点',
          icon: 'cr-icon-zhishidian',
          requiresAuth: true,
          permissions: ['manage', 'accumulate']
        }
      },
      {
        path: 'skillPoint/detail/:id/:name/:view',
        name: 'skillDetails',
        component: () => import('@/views/admin/accumulate/skillPoint/index'),
        hidden: true,
        meta: {
          parentTitle: '技能点',
          parentName: 'skillPoint',
          icon: 'cr-icon-zhishidian',
          activeMenu: 'skillPoint',
          requiresAuth: true,
          permissions: ['manage', 'accumulate']
        }
      },
      // 知识库
      {
        path: 'knowledgeBase',
        name: 'knowledgeBase',
        redirect: '/knowledgeSys/knowledgeBase',
        component: { render: h => h('router-view') },
        meta: {
          title: '知识库',
          icon: 'cr-icon-xukeguanli1',
          requiresAuth: true,
          permissions: ['manage', 'accumulate']
        },
        children: [
          // 文件库
          {
            path: 'fileLibrary',
            name: 'fileLibrary',
            component: () => import('@/views/admin/accumulate/knowledgeBase/fileLibrary/index.vue'),
            meta: {
              title: '文件库',
              requiresAuth: true,
              parentTitle: '知识库',
              parentName: 'knowledgeBase',
              activeMenu: 'knowledgeBase',
              permissions: ['manage', 'accumulate']
            }
          },
          // 资源库
          {
            path: 'resourceLibrary',
            name: 'resourceLibrary',
            component: () => import('@/views/admin/accumulate/knowledgeBase/resourceLibrary/index.vue'),
            meta: {
              title: '资源库',
              requiresAuth: true,
              parentTitle: '知识库',
              parentName: 'knowledgeBase',
              activeMenu: 'knowledgeBase',
              permissions: ['manage', 'accumulate']
            }
          }
        ]
      }
    ]
  },
  {
    ...layout(
      {
        permissions: ['manage', 'permission'],
        title: '权限中心',
        icon: 'limits'
      },
      '/permission'
    ),
    alwaysShow: true,
    children: [
      {
        name: 'admin',
        path: 'admin',
        component: () => import('@/views/admin/permission/admin/index'),
        meta: {
          title: '后台权限分配',
          icon: 'cr-icon-houtaiquanxianfenpei',
          requiresAuth: true,
          permissions: ['manage', 'permission']
        }
      },
      {
        name: 'business',
        path: 'business',
        component: () => import('@/views/admin/permission/business/index'),
        meta: {
          title: '业务角色管理',
          icon: 'cr-icon-yewujueseguanli',
          requiresAuth: true,
          permissions: ['manage', 'permission']
        }
      }
    ]
  },
  {
    ...layout(
      {
        permissions: ['manage', 'users'],
        title: '用户中心',
        icon: 'user'
      },
      '/usercenter'
    ),
    alwaysShow: true,
    children: [
      {
        name: 'user',
        path: 'user',
        component: () => import('@/views/admin/usercenter/user/index'),
        meta: {
          title: '用户管理',
          icon: 'cr-icon-yonghuguanli',
          requiresAuth: true,
          permissions: ['manage', 'users']
        }
      },
      {
        name: 'user_detail',
        path: 'user/detail/:id/:view',
        hidden: true,
        component: () => import('@/views/admin/usercenter/user/index'),
        meta: {
          parentTitle: '用户管理',
          icon: 'cr-icon-yonghuguanli',
          activeMenu: 'user',
          parentName: 'user',
          requiresAuth: true,
          permissions: ['manage', 'users']
        }
      }
    ]
  },
  {
    ...layout(
      {
        permissions: ['manage', 'adminLog'],
        title: '日志管理',
        icon: 'log'
      },
      '/log'
    ),
    alwaysShow: true,
    children: [
      {
        name: 'handleLog',
        path: 'handleLog',
        component: () => import('@/views/admin/log/handleLog/index.vue'),
        meta: {
          title: '操作日志',
          icon: 'cr-icon-shujucaozuorizhi',
          requiresAuth: true,
          permissions: ['manage', 'adminLog', 'systemLog']
        }
      },
      {
        name: 'login',
        path: 'login',
        component: () => import('@/views/admin/log/loginLog/index.vue'),
        meta: {
          title: '登录日志',
          icon: 'cr-icon-denglurizhi',
          requiresAuth: true,
          permissions: ['manage', 'adminLog', 'loginLog']
        }
      }
    ]
  },
  {
    ...layout(
      {
        permissions: ['manage'],
        title: '标签管理',
        icon: 'label'
      },
      '/label'
    ),
    alwaysShow: true,
    children: [
      {
        name: 'label',
        path: 'label',
        component: () => import('@/views/admin/label/index'),
        meta: {
          title: '标签管理',
          icon: 'cr-icon-biaoqianguanli'
        }
      }
    ]
  },
  {
    ...layout(
      {
        permissions: ['manage'],
        title: '许可授权',
        icon: 'licenses'
      },
      '/licenses'
    ),
    alwaysShow: true,
    children: [
      {
        name: 'cert',
        path: 'cert',
        component: () => import('@/views/admin/licenses/license/cert.vue'),
        meta: {
          title: '许可管理',
          icon: 'cr-icon-xukeguanli1',
          requiresAuth: true,
          permissions: ['manage']
        }
      },
      {
        name: 'license',
        path: 'license',
        hidden: true,
        component: () => import('@/views/admin/licenses/license/index.vue'),
        meta: {
          icon: 'cr-icon-shujucaozuorizhi',
          activeMenu: 'cert',
          requiresAuth: true,
          permissions: ['manage']
        }
      },
      {
        name: 'history',
        path: 'history',
        component: () => import('@/views/admin/licenses/history/index.vue'),
        meta: {
          title: '授权记录',
          icon: 'cr-icon-shouquanjilu',
          requiresAuth: true,
          permissions: ['manage']
        }
      }
    ]
  },
  {
    ...layout(
      {
        permissions: ['manage', 'simulation'],
        title: '攻防演练系统',
        icon: 'drill'
      },
      '/simulation/training'
    ),
    children: [
      {
        name: 'overview',
        path: 'overview',
        component: () => import('@/views/admin/simulationTraining/overview/index'),
        meta: {
          title: '概览',
          icon: 'cr-icon-gaikuang',
          requiresAuth: true,
          permissions: ['manage', 'simulation']
        }
      },
      {
        name: 'personal',
        path: 'personal',
        component: () => import('@/views/admin/simulationTraining/personal/index'),
        meta: {
          title: '单兵演练',
          icon: 'cr-icon-danrenyanlian',
          requiresAuth: true,
          permissions: ['manage', 'simulation']
        }
      },
      {
        path: 'personal/detail/:id/:view',
        name: 'personal_detail',
        hidden: true,
        component: () => import('@/views/admin/simulationTraining/personal/index'),
        meta: {
          parentTitle: '单兵演练',
          parentName: 'personal',
          icon: 'cr-icon-danrenyanlian',
          activeMenu: 'personal',
          requiresAuth: true,
          permissions: ['manage', 'simulation']
        }
      },
      {
        path: 'record/detail/:id',
        name: 'record_detail',
        hidden: true,
        component: () => import('@/views/admin/simulationTraining/personal/detail/detail-record/detail'),
        meta: {
          parentTitle: '训练记录',
          parentName: 'personal_detail',
          icon: 'cr-icon-danrenyanlian',
          activeMenu: 'personal',
          requiresAuth: true,
          permissions: ['manage', 'simulation']
        }
      },
      {
        name: 'team',
        path: 'team',
        component: () => import('@/views/admin/simulationTraining/team/index'),
        meta: {
          title: '团体演练',
          icon: 'cr-icon-duorenyanlian',
          requiresAuth: true,
          permissions: ['manage', 'simulation']
        }
      }, {
        path: 'team/create',
        name: 'team_create',
        component: () => import('@/views/admin/simulationTraining/team/create/index'),
        hidden: true,
        meta: {
          type: 'full_create',
          parentTitle: '团体演练',
          parentName: 'team',
          icon: 'cr-icon-duorenyanlian',
          requiresAuth: true,
          permissions: ['manage', 'simulation']
        }
      }, {
        path: 'team/modify',
        name: 'team_modify',
        component: () => import('@/views/admin/simulationTraining/team/create/modify'),
        hidden: true,
        meta: {
          type: 'full_create',
          parentTitle: '团体演练',
          parentName: 'team',
          icon: 'cr-icon-duorenyanlian',
          requiresAuth: true,
          permissions: ['manage', 'simulation']
        }
      }, {
        path: 'team/detail/:id/:view',
        name: 'team_detail',
        hidden: true,
        component: () => import('@/views/admin/simulationTraining/team/index'),
        meta: {
          parentTitle: '团体演练',
          icon: 'cr-icon-duorenyanlian',
          activeMenu: 'team',
          parentName: 'team',
          requiresAuth: true,
          permissions: ['manage', 'simulation']
        }
      }
    ]
  },
  {
    ...layout(
      {
        permissions: ['manage'],
        title: '队伍管理',
        icon: 'team-management'
      },
      '/team'
    ),
    alwaysShow: true,
    children: [
      {
        name: 'team_management',
        path: 'management',
        component: () => import('@/views/admin/teamManagement/index'),
        meta: {
          title: '队伍管理',
          icon: 'cr-icon-duiwuguanli',
          requiresAuth: true,
          permissions: ['manage']
        }
      },
      {
        path: 'management/detail/:id/:view',
        name: 'management_detail',
        hidden: true,
        component: () => import('@/views/admin/teamManagement/index'),
        meta: {
          parentTitle: '队伍管理',
          icon: 'cr-icon-duiwuguanli',
          activeMenu: 'team_management',
          parentName: 'team_management',
          requiresAuth: true,
          permissions: ['manage']
        }
      }
    ]
  },
  {
    ...layout(
      {
        permissions: ['manage'],
        title: '数据分析',
        icon: 'analysis'
      },
      '/analysis'
    ),
    alwaysShow: true,
    children: [
      {
        name: 'assess',
        path: 'assess',
        component: () => import('@/views/admin/abilityAssess/index'),
        meta: {
          title: '能力评估',
          icon: 'cr-icon-nenglipinggu',
          requiresAuth: true,
          permissions: ['manage']
        }
      },
      {
        path: 'ability/detail/:id/:view',
        name: 'abilityDetail',
        hidden: true,
        component: () => import('@/views/admin/abilityAssess/index'),
        meta: {
          parentTitle: '能力评估',
          icon: 'cr-icon-nenglipinggu',
          activeMenu: 'assess',
          parentName: 'assess',
          requiresAuth: true,
          permissions: ['manage']
        }
      }
    ]
  },
  {
    ...layout(
      {
        permissions: ['manage', 'scene'],
        title: '场景管理',
        icon: 'scene'
      },
      '/scene'
    ),
    alwaysShow: true,
    children: [{
      name: 'scenement',
      path: 'scenement',
      component: () => import('@/views/admin/scenemanage/scene/index'),
      meta: {
        title: '场景',
        icon: 'cr-icon-changjing',
        requiresAuth: true,
        permissions: ['manage', 'scene']
      }
    },
    {
      path: 'scenement/detail/:id/:view',
      name: 'scenement_detail',
      hidden: true,
      component: () => import('@/views/admin/scenemanage/scene/index'),
      meta: {
        parentTitle: '场景',
        title: '场景',
        icon: 'cr-icon-changjing',
        parentName: 'scenement',
        activeMenu: 'scenement',
        requiresAuth: true,
        permissions: ['manage', 'scene']
      }
    },
    {
      path: 'scenement/detailTask',
      name: 'detailTask',
      hidden: true,
      component: () => import('@/views/admin/scenemanage/scene/detail/detail_task'),
      meta: {
        icon: 'cr-icon-changjing',
        type: 'full_create',
        requiresAuth: true,
        permissions: ['manage', 'scene']
      }
    },
    {
      path: 'scenement/create',
      name: 'scenement_create',
      hidden: true,
      component: () => import('@/views/admin/scenemanage/scene/create'),
      meta: {
        type: 'full_create',
        requiresAuth: true,
        permissions: ['manage', 'scene']
      }
    },
    {
      path: 'taskTemplate/create',
      name: 'taskTemplate_create',
      hidden: true,
      component: () => import('@/views/admin/scenemanage/taskTemplate/create'),
      meta: {
        type: 'full_create',
        requiresAuth: true,
        permissions: ['manage', 'scene']
      }
    }, {
      path: '/orchestration',
      name: 'orchestration_detail',
      component: () => import('@/packages/topo/index'),
      hidden: true,
      meta: {
        type: 'full_create',
        requiresAuth: true,
        permissions: ['manage', 'scene']
      }
    }, {
      name: 'topo/template',
      path: 'topo/template',
      component: () => import('@/views/admin/scenemanage/topoTemplate/index'),
      meta: {
        title: '拓扑模板',
        icon: 'cr-icon-tuopumuban-copy',
        requiresAuth: true,
        permissions: ['manage', 'scene']
      }
    }, {
      name: 'task/template',
      path: 'task/template',
      component: () => import('@/views/admin/scenemanage/taskTemplate/index'),
      meta: {
        title: '任务模板',
        icon: 'cr-icon-renwumoban',
        requiresAuth: true,
        permissions: ['manage', 'scene']
      }
    }, {
      name: 'taskTemplateDetail',
      path: 'task/template/detail/:id',
      component: () => import('@/views/admin/scenemanage/taskTemplate/index'),
      hidden: true,
      meta: {
        parentTitle: '任务模板',
        icon: 'cr-icon-renwumoban',
        parentName: 'task/template',
        activeMenu: 'task/template',
        requiresAuth: true,
        permissions: ['manage', 'scene']
      }
    }]
  },
  {
    ...layout(
      {
        permissions: ['manage'],
        title: '系统设置',
        icon: 'website'
      },
      '/systemSettings'
    ),
    alwaysShow: true,
    children: [
      {
        name: 'systemSetting',
        path: 'systemSetting',
        component: () => import('@/views/admin/hrm/systemSetting/index'),
        meta: {
          title: '系统设置',
          icon: 'cr-icon-xitongguanli',
          requiresAuth: true,
          permissions: ['manage']
        }
      },
      {
        name: 'baseInfoConfig',
        path: 'baseInfoConfig',
        component: () => import('@/views/admin/hrm/systemSetting/navigationbar/index'),
        meta: {
          title: '网站信息配置',
          icon: 'cr-icon-wangzhanxinxipeizhi',
          requiresAuth: true,
          permissions: ['manage', 'netconf']
        }
      },
      {
        name: 'ResourceAutoConfig',
        path: 'ResourceAutoConfig',
        component: () => import('@/views/admin/hrm/systemSetting/resourceAutoConfig'),
        meta: {
          title: '资源管理配置',
          icon: 'cr-icon-wangzhanxinxipeizhi',
          requiresAuth: true,
          permissions: ['manage', 'netconf']
        }
      },
      {
        name: 'dataConfiguration',
        path: 'dataConfiguration',
        component: () => import('@/views/admin/hrm/dataConfiguration/index'),
        meta: {
          title: '数据配置',
          icon: 'cr-icon-wangzhanxinxipeizhi',
          requiresAuth: true,
          permissions: ['manage', 'netconf']
        }
      }
    ]
  },
  {
    ...layout(
      {
        permissions: ['manage'],
        title: '靶场底座',
        icon: 'simulation'
      },
      '/os_network'
    ),
    alwaysShow: true,
    children: [
      {
        name: 'image',
        path: 'image',
        component: () => import('@/views/admin/nfvo/image/index'),
        meta: {
          title: '虚拟机镜像',
          icon: 'cr-icon-xunijijingxiang',
          requiresAuth: true,
          permissions: ['manage']
        }
      },
      {
        name: 'vm_images_upload',
        path: 'vm_images_upload',
        hidden: true,
        component: () => import('@/views/admin/nfvo/image/images_upload/index'),
        meta: {
          title: '上传镜像',
          type: 'full_create_all',
          icon: 'cr-icon-xunijijingxiang',
          requiresAuth: true,
          permissions: ['manage']
        }
      },
      {
        name: 'container_image',
        path: 'container_image',
        component: () => import('@/views/admin/nfvo/container_image/index'),
        meta: {
          title: '容器镜像',
          icon: 'cr-icon-rongqijingxiang',
          requiresAuth: true,
          permissions: ['manage']
        }
      },
      {
        name: 'images_upload',
        path: 'images_upload',
        hidden: true,
        component: () => import('@/views/admin/nfvo/container_image/images_upload/index'),
        meta: {
          title: '上传镜像',
          type: 'full_create_all',
          icon: 'cr-icon-xunijijingxiang',
          requiresAuth: true,
          permissions: ['manage']
        }
      },
      {
        name: 'physical_device',
        path: 'physical_device',
        component: () => import('@/views/admin/nfvo/physical_device/index'),
        meta: {
          title: '物理设备',
          icon: 'cr-icon-wulishebei',
          requiresAuth: true,
          permissions: ['manage']
        }
      },
      {
        name: 'physical_device_detail',
        path: 'physical_device/detail/:id/:view',
        hidden: true,
        component: () => import('@/views/admin/nfvo/physical_device/index'),
        meta: {
          parentTitle: '物理设备',
          title: '物理设备详情',
          icon: 'cr-icon-wulishebei',
          activeMenu: 'physical_device',
          requiresAuth: true,
          permissions: ['manage']
        }
      },
      {
        name: 'virtual_device',
        path: 'virtual_device',
        component: () => import('@/views/admin/nfvo/virtual_device/index'),
        meta: {
          title: '虚拟设备',
          icon: 'cr-icon-xunishebei',
          requiresAuth: true,
          permissions: ['manage']
        }
      },
      {
        name: 'virtual_device_detail',
        path: 'virtual_device/detail/:id/:view',
        hidden: true,
        component: () => import('@/views/admin/nfvo/virtual_device/index'),
        meta: {
          title: '虚拟设备详情',
          icon: 'cr-icon-xunishebei',
          activeMenu: 'virtual_device',
          requiresAuth: true,
          permissions: ['manage']
        }
      },
      {
        name: 'graphic_device',
        path: 'graphic_device',
        component: () => import('@/views/admin/nfvo/graphic_device/index'),
        meta: {
          title: '图形设备',
          icon: 'cr-icon-tuxingshebei',
          requiresAuth: true,
          permissions: ['manage']
        }
      },
      {
        name: 'graphic_device_detail',
        path: 'graphic_device/detail/:id/:view',
        hidden: true,
        component: () => import('@/views/admin/nfvo/graphic_device/index'),
        meta: {
          title: '图形设备详情',
          icon: 'cr-icon-tuxingshebei',
          parentTitle: '图形设备',
          parentName: 'graphic_device',
          activeMenu: 'graphic_device',
          requiresAuth: true,
          permissions: ['manage']
        }
      },
      {
        name: 'tradition_switchboard',
        path: 'tradition_switchboard',
        component: () => import('@/views/admin/nfvo/tradition_switchboard/index'),
        meta: {
          title: '传统交换机',
          icon: 'cr-icon-chuantongjiaohuanji',
          requiresAuth: true,
          permissions: ['manage']
        }
      },
      {
        name: 'tradition_switchboard_detail',
        path: 'tradition_switchboard/detail/:id/:view',
        hidden: true,
        component: () => import('@/views/admin/nfvo/tradition_switchboard/index'),
        meta: {
          title: '传统交换机详情',
          icon: 'cr-icon-chuantongjiaohuanji',
          parentTitle: '传统交换机',
          parentName: 'tradition_switchboard',
          activeMenu: 'tradition_switchboard',
          requiresAuth: true,
          permissions: ['manage']
        }
      },
      {
        name: 'job_history',
        path: 'job_history',
        component: () => import('@/views/admin/nfvo/job_manager/job_history/index'),
        meta: {
          title: '操作日志',
          icon: 'cr-icon-caozuoshouce',
          requiresAuth: true,
          permissions: ['manage']
        }
      },
      {
        name: 'job_history_detail',
        path: 'job_history/detail/:id/:view/',
        hidden: true,
        component: () => import('@/views/admin/nfvo/job_manager/job_history/index'),
        meta: {
          title: '任务详情',
          icon: 'cr-icon-caozuoshouce',
          parentTitle: '操作历史',
          parentName: 'job_history',
          activeMenu: 'job_history',
          requiresAuth: true,
          permissions: ['manage']
        }
      },
      {
        name: 'current_job',
        path: 'current_job',
        hidden: true,
        component: () => import('@/views/admin/nfvo/job_manager/current_job/index'),
        meta: {
          title: '当前任务',
          icon: 'cr-icon-caozuoshouce',
          parentName: 'job_history',
          activeMenu: 'job_history',
          requiresAuth: true,
          permissions: ['manage']
        }
      },
      {
        name: 'current_job_detail',
        path: 'current_job/detail/:id/:view/',
        hidden: true,
        component: () => import('@/views/admin/nfvo/job_manager/current_job/index'),
        meta: {
          title: '任务详情',
          icon: 'cr-icon-caozuoshouce',
          parentTitle: '当前任务',
          parentName: 'job_history',
          activeMenu: 'job_history',
          requiresAuth: true,
          permissions: ['manage']
        }
      }
    ]
  }
]
