<template>
  <div v-loading="loading" class="dialog-wrap">
    <batch-template
      :data="data"
      :available-data="availableData"
      :show-delete-warning="true"
      view-key="name"
    />
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button :disabled="checked" type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>

<script>
import batchTemplate from '@/packages/batch-delete/modal-bat-template.vue'
import modalMixins from '@/packages/mixins/modal_form'
import { deleteById } from '@/api/exam/index.js'

export default {
  components: {
    batchTemplate
  },
  mixins: [modalMixins],
  props: {
    data: {
      type: Array,
      default: () => {
        return []
      }
    }
  },
  data() {
    return {
      loading: false,
      checked: false
    }
  },
  computed: {
    availableData: function() {
      const tempArr = this.data.filter((item) => {
        return item.published === '0'
      })
      return tempArr
    }
  },
  created() {
    this.availableData.length == 0 ? this.checked = true : this.checked = false
  },
  methods: {
    close() {
      this.$emit('close')
    },
    confirm: function() {
      this.loading = true
      const result = this.availableData.map(item => {
        const params = { id: item.id, name: item.name }
        return deleteById(params)
      })
      Promise.all(result).then((res, error) => {
        if (result.length === res.length) {
          this.close()
          this.$emit('call', 'refresh')
          this.loading = false
          this.$message.success('删除成功')
        }
      }).catch(() => {
        this.close()
        this.loading = false
      })
    }
  }
}
</script>
