<template>
  <el-row :gutter="20">
    <el-col :span="12">
      <detail-card title="基本信息" class="detail-wrap">
        <el-form slot="content" label-position="left" label-width="140px">
          <el-form-item label="项目名称：">{{ data.name || '-' }}</el-form-item>
          <el-form-item label="测试轮次：">{{ data.round || '-' }}</el-form-item>
          <el-form-item label="状态：">
            <el-badge :type="getStatusClass(projectStatus)" is-dot/>
            {{ getStatusLabel(projectStatus) }}
          </el-form-item>
          <el-form-item label="测试项目负责人：">{{ data.managerName || '-' }}</el-form-item>
          <el-form-item label="检测流程：">{{ data.processName || '-' }}</el-form-item>
          <el-form-item label="计划开始日期：">{{ data.planBeginDate || '-' }}</el-form-item>
          <el-form-item label="实际开始时间：">{{ data.actualBeginTime || '-' }}</el-form-item>
          <el-form-item label="实际结束时间：">{{ data.actualEndTime || '-' }}</el-form-item>
          <el-form-item label="创建人：">{{ data.createByName || '-' }}</el-form-item>
          <el-form-item label="创建时间：">{{ data.createAt || '-' }}</el-form-item>
          <el-form-item label="项目描述：">
            <myEditor
              key="description"
              :content="data.description"
              :only-editor="true"
              :is-read-only="true"
              id-prefix="content"
              width="100%"
              height="180px"
            />
          </el-form-item>
        </el-form>
      </detail-card>
    </el-col>
    <el-col :span="12">
      <detail-card title="厂商及产品信息">
        <el-form slot="content" label-position="left" label-width="140px">
          <template v-if="data.applicationId">
            <el-form-item label="检测申请：">
              <a :href="`/testing/testingApplication/applicationCompleted?id=${data.applicationId}&applicationNumber=${data.applicationNo}&detail=true&routeSearch=true&searchVal=${data.applicationNo}&searchKey=applicationNumber&moduleName=testingApplicationPassed`" target="_blank" rel="noopener noreferrer">
                {{ data.applicationNo || '-' }}
              </a>
            </el-form-item>
          </template>
          <el-form-item label="厂商名称：">{{ data.vendorName || '-' }}</el-form-item>
          <el-form-item label="厂商联系人：">{{ data.vendorMainContact || '-' }}</el-form-item>
          <el-form-item label="联系方式：">{{ data.vendorContactInfo || '-' }}</el-form-item>
          <el-form-item label="检测产品：">{{ data.productName || '-' }}</el-form-item>
          <el-form-item label="版本号：">{{ data.productVersion || '-' }}</el-form-item>
          <template v-if="data.applicationId">
            <el-form-item label="系统类别：">{{ data.systemCategory || '-' }}</el-form-item>
            <el-form-item label="建设方式：">{{ data.constructionMethod || '-' }}</el-form-item>
            <el-form-item label="系统主要功能模块：">
              <myEditor
                key="mainModules"
                :content="data.mainModules"
                :only-editor="true"
                :is-read-only="true"
                id-prefix="content"
                width="100%"
                height="180px"
              />
            </el-form-item>
            <el-form-item label="系统增加功能模块：">
              <myEditor
                key="additionalModules"
                :content="data.additionalModules"
                :only-editor="true"
                :is-read-only="true"
                id-prefix="content"
                width="100%"
                height="170px"
              />
            </el-form-item>
          </template>
        </el-form>
      </detail-card>
    </el-col>
  </el-row>
</template>

<script>
import detailCard from '@/packages/detail-view/detail-card.vue'
import { getSampleVendorById } from '@/api/testing/index'
import myEditor from '@/packages/editor/index.vue'

export default {
  components: {
    detailCard,
    myEditor
  },
  props: {
    data: {
      type: Object,
      default: () => ({})
    },
    id: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      vendorData: {}, // 厂商及产品信息
      projectStatus: '' // 项目状态
    }
  },
  watch: {
    data: {
      handler(newVal) {
        if (newVal && newVal.id) {
          // 获取厂商信息
          if (newVal.vendorId) {
            this.getVendorInfo(newVal.id, newVal.vendorId)
          }

          // 获取项目状态
          this.getProjectStatus(newVal.id)
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    // 获取厂商及产品信息
    getVendorInfo(vendorId) {
      if (!vendorId) return

      getSampleVendorById({ id: vendorId }).then(res => {
        if (res.data.code === 0) {
          this.vendorData = res.data.data || {}
        }
      })
    },
    // 获取项目状态
    getProjectStatus(projectId) {
      // 处理状态显示逻辑
      if (this.data.pendingStatus === 9) {
        // 如果是挂起状态，设置为挂起状态码
        this.projectStatus = 9
      } else {
        this.projectStatus = this.data.projectStatus
      }
    },
    getStatusLabel(status) {
      const statusLabels = {
        '0': '待测试',
        '1': '测试中',
        '2': '测试通过',
        '3': '测试不通过',
        '4': '待送审资料',
        '5': '待审核资料',
        '6': '待部署环境',
        '8': '取消挂起',
        '9': '已挂起'
      }
      return statusLabels[status] || '-'
    },
    getStatusClass(status) {
      const statusMap = {
        '0': 'info',
        '1': 'warning',
        '2': 'success',
        '3': 'danger',
        '4': 'info',
        '5': 'info',
        '6': 'info',
        '8': 'info',
        '9': 'info'
      }
      return statusMap[status] || 'info'
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep .detail-wrap .detail-card-body {
  max-height: 87vh;
  overflow: auto;
  img {
    margin-right: 10px;
  }
}
.success {
  color: #67C23A;
}
.warning {
  color: #E6A23C;
}
.primary {
  color: var(--color-600);
}
.danger {
  color: #F56C6C;
}
</style>
