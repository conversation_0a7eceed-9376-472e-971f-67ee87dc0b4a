<template>
  <div class="content-wrap-layout">
    <contentHeader/>
    <page-table
      ref="table"
      :default-selected-arr="defaultSelectedArr"
      @refresh="refresh"
      @link-event="linkEvent"
      @on-select="tabelSelect"
      @on-current="tabelCurrent"
      @transmitTime="transmitTime"
      @changeClass="changeClass"
    >
      <action-menu
        slot="action"
        :module-name="moduleName"
        :select-item="selectItem"
        :time-params="timeParams"
        @call="actionHandler"
        @changeClass="changeClass"
      />
    </page-table>
  </div>
</template>

<script>
import module from './config'
import pageTable from '../table/examination-details.vue'
import actionMenu from './action/index.vue'
import contentHeader from '../detail-header.vue'
export default {
  name: module.name,
  components: {
    pageTable,
    actionMenu,
    contentHeader
  },
  data() {
    return {
      moduleName: module.name,
      selectItem: [],
      defaultSelectedArr: [],
      content: this.$route.query.content,
      timeParams: {}
    }
  },
  methods: {
    // 列表点击
    linkEvent: function({ name, row, params }) {
      this.$router.push({ name: name, query: params })
    },
    // 返回已选
    tabelSelect: function(data) {
      this.selectItem = data
    },
    // 返回单选
    tabelCurrent: function(row) {
      this.selectItem = [row]
    },
    transmitTime(val) {
      this.timeParams = val
    },
    // action menu 事件
    actionHandler: function(type, data) {
      switch (type) {
        case 'refresh':
          this.$refs['table'].getList()
      }
    },
    refresh: function() {
    },
    changeClass: function(data) {
      this.$refs['table'].getList(data)
    }
  }
}
</script>


