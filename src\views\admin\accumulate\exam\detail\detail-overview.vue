<template>
  <el-row :gutter="20">
    <el-col :span="12">
      <detail-card title="基本信息">
        <el-form slot="content" label-position="left" label-width="110px">
          <el-form-item label="ID：">{{ data.id || '-' }}</el-form-item>
          <el-form-item label="名称：">{{ data.examName || '-' }}</el-form-item>
          <el-form-item label="分类：">{{ data.categoryName || '-' }}</el-form-item>
          <el-form-item label="描述：">{{ data.examDesc || '-' }}</el-form-item>
          <el-form-item label="难度：">{{ moduleConf.difficultyObj[data.difficulty].label }}</el-form-item>
          <el-form-item label="推荐时长：">{{ data.suggestTime || '0' }}分钟</el-form-item>
          <el-form-item label="用途：">{{ data.sceneTypeId ? moduleConf.sceneTypeObj[data.sceneTypeId].value : '-' }}</el-form-item>
          <el-form-item v-if="data.examType == 0" label="组卷方式：">{{ data.generatingTestPaper == 1 ? '智能组卷' : '手动组卷' }}</el-form-item>
          <el-form-item label="试卷类型：">{{ data.examType == 1 ? '动态试卷' : '静态试卷' }}</el-form-item>
          <el-form-item label="选题方式：">基于分类</el-form-item>
          <el-form-item label="分数：">{{ data.score || '0' }}</el-form-item>
          <el-form-item label="题目数量：">{{ data.questionNum || '0' }}</el-form-item>
          <el-form-item label="引用次数：">{{ data.citedNum || '0' }}</el-form-item>
          <el-form-item label="创建人：">{{ data.createByName || '-' }}</el-form-item>
          <el-form-item label="创建时间：">{{ data.createTime || '-' }}</el-form-item>
        </el-form>
      </detail-card>
    </el-col>
  </el-row>
</template>

<script>
import moduleConf from '../config.js'
import detailCard from '@/packages/detail-view/detail-card.vue'
export default {
  name: 'DetailOverview',
  components: {
    detailCard
  },
  props: {
    id: {
      type: [Number, String]
    },
    data: {
      type: Object
    }
  },
  data() {
    return {
      moduleConf: moduleConf
    }
  }
}
</script>
