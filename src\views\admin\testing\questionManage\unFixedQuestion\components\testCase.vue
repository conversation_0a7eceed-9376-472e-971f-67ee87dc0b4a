<template>
  <div class="drawer-wrap">
    <testCase
      ref="table"
      :link="false"
      :task-id="taskId"
      height="auto"
      @link-event="linkEvent"
      @on-select="onSelect"
    />
    <!-- 底部 -->
    <div class="drawer-footer">
      <el-button :disabled="!selectItem.length" type="primary" @click="confirm">确定</el-button>
      <el-button type="text" @click="close">取消</el-button>
    </div>
  </div>
</template>

<script>
import testCase from '@/components/testing/testCase/index'
export default {
  components: {
    testCase
  },
  props: {
    name: {
      type: String,
      default: ''
    },
    taskId: {
      type: Number,
      default: null
    }
  },
  data() {
    return {
      selectItem: []
    }
  },
  methods: {
    'linkEvent': function({ name, row, params }) {
      this.$router.push({ name: name, params: params })
    },
    'onSelect': function(data) {
      this.selectItem = data
    },
    'close': function() {
      this.$emit('close')
    },
    'confirm': function() {
      if (this.selectItem && this.selectItem.length > 0) {
        this.$emit('call', this.name, this.selectItem)
      } else {
        this.$emit('call', this.name, [])
      }
    }
  }
}
</script>
