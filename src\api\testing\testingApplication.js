import request from '@/utils/request'
const headers = { 'Content-Type': 'application/json;charset=UTF-8' }

// 获取厂商列表
export function vendorList(data) {
  return request({
    url: '/testing/vendor/list',
    method: 'get',
    data,
    headers
  })
}
// 创建检测申请
export function detectionApplicationCreate(data) {
  return request({
    url: '/testing/detection/application/create',
    method: 'post',
    data,
    headers
  })
}
// 动态创建检测申请
export function detectionApplicationFormCreate(data) {
  return request({
    url: '/testing/detection/application/form/create',
    method: 'post',
    data,
    headers
  })
}
// 更新检测申请
export function detectionApplicationUpdate(data) {
  return request({
    url: '/testing/detection/application/form/update',
    method: 'post',
    data,
    headers
  })
}
// 获取检测申请详情
export function detectionApplicationGet(data) {
  return request({
    url: '/testing/detection/application/get',
    method: 'get',
    data,
    headers
  })
}
// 根据 厂商ID 查询联系人信息
export function getContactByVendorId(id) {
  return request({
    url: `/testing/vendorContact/getByVendorId/${id}`,
    method: 'post',
    headers
  })
}
// 删除检测申请
export function detectionApplicationDelete(data) {
  return request({
    url: '/testing/detection/application/delete',
    method: 'post',
    data,
    headers
  })
}
// 提交检测流程
export function detectionSubmitProcess(data) {
  return request({
    url: '/testing/detection/application/submit/process',
    method: 'post',
    data,
    headers
  })
}
// 拒绝检测流程
export function applicationReject(data) {
  return request({
    url: '/testing/detection/application/reject',
    method: 'post',
    data,
    headers
  })
}
// 分页查询检测申请
export function detectionApplicationPage(data) {
  return request({
    url: '/testing/detection/application/page',
    method: 'post',
    data,
    headers
  })
}
// 分页查询已完成检测申请
export function detectionApplicationFinishPage(data) {
  return request({
    url: '/testing/detection/application/finishPage',
    method: 'post',
    data,
    headers
  })
}
// 通过流程实例id和目标节点id，回退到目标节点
export function revertToSpecificMode(data) {
  return request({
    url: '/testing/detection/application/revertToSpecificMode',
    method: 'post',
    data,
    headers
  })
}
// 关联检测项目
export function detectionAppProjectId(data) {
  return request({
    url: '/testing/detection/application/project/bind',
    method: 'post',
    data,
    headers
  })
}

// 上传电子签名附件
export function electronicSignatureAttachmentUpload(data) {
  return request({
    url: '/testing/electronicSignatureAttachment/upload',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
// 获取当前用户最新的电子签名附件
export function getSignatureAttachment() {
  return request({
    url: '/testing/electronicSignatureAttachment/getLatest',
    method: 'get',
    headers
  })
}
// 获取动态表单
export function getByModelByKey(modelKey) {
  return request({
    url: `/testing/dynamic/form/getByModelKey/${modelKey}`,
    method: 'get',
    headers
  })
}
// 删除电子签名附件
export function deleteSignatureAttachment(id) {
  return request({
    url: `/testing/electronicSignatureAttachment/delete/${id}`,
    method: 'post',
    headers
  })
}
// 上传附件
export function uploadApplicationFile(data) {
  return request({
    url: '/testing/testProcesses/uploadFile',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
// 删除附件
export function deleteApplicationFile(data) {
  return request({
    url: '/testing/testProcesses/deleteFile',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 根据id获取电子签名
export function getSignatureAttachmentById(id) {
  return request({
    url: `/testing/electronicSignatureAttachment/get/${id}`,
    method: 'get',
    headers
  })
}
// 获取流程轨迹
export function getAuditQuery(params) {
  return request({
    url: `/admin/audit/trail/query`,
    method: 'get',
    params,
    headers
  })
}
// 查询可退回的节点
export function getReturnableNode(businessKey, taskId) {
  return request({
    url: `/admin/audit/trail/returnableNode/businessKey/${businessKey}/${taskId}`,
    method: 'get',
    headers
  })
}
// 获取检测申请详情
export function getApplicationForm(id) {
  return request({
    url: `/testing/detection/application/form/get/${id}`,
    method: 'post',
    headers
  })
}
// 获取检测申请详情
export function applicationProjectBind(data) {
  return request({
    url: `/testing/detection/application/project/bind`,
    method: 'post',
    data,
    headers
  })
}
// 终止流程
export function applicationTerminateProcess(data) {
  return request({
    url: `/testing/detection/application/terminateProcess`,
    method: 'post',
    data,
    headers
  })
}
// 确认检测申请
export function confirmDetectionApplication(id) {
  return request({
    url: `/testing/detection/application/confirm/${id}`,
    method: 'post',
    headers
  })
}
// 查询检测项目
export function applicationProjectList(data) {
  return request({
    url: `/testing/detection/application/project/list`,
    method: 'post',
    data,
    headers
  })
}
// 获取当前登录人厂商
export function getVendorByUserId(params) {
  return request({
    url: `/testing/vendor/getVendorByUserId`,
    method: 'get',
    params,
    headers
  })
}
