import request from '@/utils/request'
const headers = { 'Content-Type': 'application/json;charset=UTF-8' }

// 上传文件
export function uploadFile(data) {
  var param = new FormData()
  Object.keys(data).forEach(key => {
    param.append(key, data[key])
  })
  return request({
    url: 'admin/sysExamQuestion/uploadFile',
    method: 'post',
    data: param,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 获取题目
export function getQuestionBank(data) {
  return request({
    url: `admin/sysExamQuestion/queryPage`,
    method: 'post',
    headers,
    data
  })
}

// 获取详情
export function getQuestionBankItem(params) {
  return request({
    url: `admin/sysExamQuestion/get`,
    method: 'get',
    headers,
    params
  })
}

// 通过题目ID查询被引用的试卷列表
export function queryExamPageByQues(data) {
  return request({
    url: `admin/sysExamQuestion/queryExamPage`,
    method: 'post',
    headers,
    data
  })
}

// 创建题目
export function createQuestionBank(data) {
  return request({
    url: `admin/sysExamQuestion/create`,
    method: 'post',
    headers,
    data
  })
}

// 编辑题目
export function updateQuestionBank(data) {
  return request({
    url: `admin/sysExamQuestion/update`,
    method: 'post',
    headers,
    data
  })
}

// 删除题目
export function removeQuestionBank(data) {
  return request({
    url: `admin/sysExamQuestion/remove`,
    method: 'post',
    headers,
    data
  })
}

// 实训知识点列表
export function getTrainingPoint(data) {
  return request({
    url: 'training/pjtKnowledgePoint/searchPoint',
    method: 'post',
    headers,
    data
  })
}

// 获取网元设备列表
export function getNetworkElement(params) {
  return request({
    url: window.NFVO_CONFIG.nfvo_api + '/networkelement',
    method: 'get',
    headers,
    params
  })
}

// 获取网元设备详情
export function getNetworkElementItem(id) {
  return request({
    url: window.NFVO_CONFIG.nfvo_api + '/networkelement/' + id,
    method: 'get',
    headers
  })
}

// 下载导入题目模板
export function exportQuestion(data) {
  return request({
    url: 'admin/sysExamQuestion/exportQuestion',
    method: 'post',
    headers,
    data,
    responseType: 'blob'
  })
}

// 上传导入题目
export function importQuestion(data) {
  return request({
    url: 'admin/sysExamQuestion/importQuestion',
    method: 'post',
    headers,
    data
  })
}

// 智能抽题查询下方可用题数
export function categoryTypeNumber(data) {
  return request({
    url: `admin/sysExamQuestion/categoryTypeNumber`,
    method: 'post',
    headers,
    data
  })
}

// 下载批量导入题目错误列表
export function downImportQuestionErrorExcel(data) {
  return request({
    url: 'admin/sysExamQuestion/downImportQuestionErrorExcel',
    method: 'post',
    headers,
    data,
    responseType: 'blob'
  })
}

// 删除 错误数据表
export function delErrorExcel(data) {
  return request({
    url: 'admin/sysExamQuestion/delErrorExcel',
    method: 'post',
    headers,
    data
  })
}

// 智能抽题根据可用题数量获取题目信息
export function getIntelligence(data) {
  return request({
    url: `admin/sysExamQuestion/intelligence`,
    method: 'post',
    headers,
    data
  })
}
