<template>
  <div v-loading="loading" class="dialog-wrap">
    <el-form ref="form" :model="formData" :rules="rules" label-width="80px" @submit.native.prevent>
      <el-form-item label="名称" prop="name">
        <el-input v-model.trim="formData.name"/>
      </el-form-item>
      <el-form-item label="描述" prop="description">
        <el-input v-model.trim="formData.description" type="textarea"/>
      </el-form-item>
    </el-form>
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>

<script>
import { createSnapshot } from '../api/orchestration'
import modalMixins from '../../mixins/modal_form'
import validate from '../../validate'
export default {
  mixins: [modalMixins],
  props: {
    // 传入数据
    data: [Object, Array],
    resourceData: Object
  },
  data() {
    return {
      loading: false,
      nodeId: '',
      formData: {
        'name': '',
        'description': ''
      },
      rules: {
        'name': [
          validate.required(),
          validate.base_name
        ],
        'description': [
          validate.description
        ]
      }
    }
  },
  created() {
    if (this.resourceData) {
      this.nodeId = this.resourceData.id
    } else this.nodeId = this.data.node_id
  },
  methods: {
    // modal点击确定
    'confirm': function() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.loading = true
          createSnapshot(this.nodeId, this.formData)
            .then(res => {
              this.$message({
                message: '创建快照已执行',
                type: 'success'
              })
            })
            .catch((error) => {
              this.$message.error(error)
            })
          this.close()
        }
      })
    },
    close() {
      this.$emit('close')
    }
  }
}
</script>
