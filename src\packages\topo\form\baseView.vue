<template>
  <div v-loading="loading" class="drawer-wrap orchestration-drawer-wrap">
    <div class="drawer-wrap-content">
      <el-form ref="form" :model="formData" :rules="rules" label-width="140px">
        <el-form-item v-if="!disabledType" label="基础组件名称" prop="deviceName">
          {{ formData.deviceName }}
        </el-form-item>
        <el-form-item :label="disabledType ? '名称' : '组件名称'" prop="name">
          {{ formData.name || '-' }}
        </el-form-item>
        <el-form-item label="描述" prop="description">
          {{ formData.description || '-' }}
        </el-form-item>
        <el-form-item v-if="isSwitch" label="CIDR" prop="cidr">
          {{ formData.cidr || '-' }}
        </el-form-item>
        <el-form-item v-if="isSwitch" label="网关" prop="gateway">
          {{ formData.gateway || '-' }}
        </el-form-item>
        <el-form-item v-if="isSwitch" label="网络类型" prop="network_type">
          {{ formData.network_type ? (formData.network_type === 'vxlan' ? 'VXLAN' : 'VLAN') : '-' }}
        </el-form-item>
        <el-form-item v-if="isExternal" label="外部网络" prop="vnf_uuid">
          {{ externalNetworkList.filter(item => { return item.vnf_uuid == formData.vnf_uuid }).length ? externalNetworkList.filter(item => { return item.vnf_uuid == formData.vnf_uuid })[0].cidr : '-' }}
        </el-form-item>
      </el-form>
    </div>
    <div class="drawer-footer">
      <el-button type="primary" @click="close">确定</el-button>
    </div>
  </div>
</template>
<script>
import { getNodeItem, getExternalNetwork } from '../api/orchestration'
import { Graph } from '@antv/x6'
export default {
  components: {},
  props: {
    data: {
      type: Object,
      default: () => {
        return {}
      }
    },
    type: {
      type: String,
      default: ''
    },
    graph: {
      type: [Object, Graph]
    }
  },
  data() {
    return {
      loading: true,
      externalNetworkList: [],
      selectedExternalNetworkIds: [], // 拓扑中外部网络节点已经选择的网络id
      formData: {
        'name': '',
        'deviceName': '',
        'description': '',
        'cidr': '',
        'gateway': '',
        'vnf_uuid': ''
      },
      rules: {}
    }
  },
  computed: {
    disabledCIDR() {
      return this.data.node.data.status !== 'pending'
    },
    disabledType() {
      return this.type !== 'allPermissions' && this.type !== 'templatePermissions'
    },
    'isSwitch': function(data) {
      return this.data.node.data.virtual_type === 'logic_switch'
    },
    'isExternal': function(data) {
      return this.data.node.data.virtual_type === 'external_switch'
    }
  },
  created() {
    const nodes = this.graph.getNodes()
    this.selectedExternalNetworkIds = nodes.filter(item => item.data.vnf_uuid).map(item => item.data.vnf_uuid)
    const data = this.data.node.data
    this.formData['name'] = data['name']
    this.formData['deviceName'] = data['deviceName']
    this.formData['description'] = data['description'] || ''
    this.formData['cidr'] = data['cidr'] || ''
    this.formData['gateway'] = data['gateway'] || ''
    Promise.all([this.getNodeItem(), this.getExternalNetwork()]).then(() => {
      this.loading = false
    })
  },
  methods: {
    // 重新获取node的端口
    'getNodeItem': function() {
      return new Promise((resolve, reject) => {
        getNodeItem(this.data.node.data.node_id)
          .then(res => {
            this.formData['network_type'] = res.data.data['network_type'] || ''
            const data = res.data.data.external_network
            if (data) {
              this.formData['vnf_uuid'] = data.id || ''
              this.selectedExternalNetworkIds = this.selectedExternalNetworkIds.filter(item => item != data.id)
            }
            resolve()
          })
          .catch((error) => {
            console.log(error)
            reject()
          })
      })
    },
    // 获取云平台外部网络
    'getExternalNetwork': function() {
      return new Promise((resolve, reject) => {
        getExternalNetwork()
          .then(res => {
            this.externalNetworkList = res.data.data.result || []
            resolve()
          })
          .catch((error) => {
            console.log(error)
            reject()
          })
      })
    },
    'close': function() {
      this.$emit('close')
    }
  }
}
</script>
<style lang="less">
.orchestration-drawer-wrap {
  .el-form {
    & >.el-form-item > .el-form-item__content > .el-dropdown,
    & >.el-form-item > .el-form-item__content > .el-input,
    & >.el-form-item > .el-form-item__content > .el-select,
    & >.el-form-item > .el-form-item__content > .el-textarea {
      width: 90%;
    }
  }
  .port-form {
    th {
      padding: 0;
    }
    >.el-form-item__label {
      width: 70px !important;
    }
    >.el-form-item__content {
      margin-left: 70px !important;
      // margin-top: 40px;
      .data-table-footer {
        display: none;
      }
    }
  }
}
</style>
