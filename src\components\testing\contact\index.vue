<template>
  <div class="resource-table">
    <!-- 操作区 -->
    <div class="operation-wrap">
      <div class="operation-left">
        <slot name="action" />
        <el-button type="primary" icon="el-icon-refresh" @click="refresh" />
      </div>
      <div class="operation-right">
        <el-badge :value="searchBtnShowNum">
          <el-button icon="el-icon-search" @click="openSearch">搜索</el-button>
        </el-badge>
        <!-- 自定义表格列 -->
        <t-table-config
          :data="columnsObj"
          :active-key-arr="columnsViewArr"
          @on-change-col="onChangeCol"
        />
      </div>
    </div>
    <!-- 搜索区 -->
    <t-search-box
      v-show="searchView"
      :search-key-list="searchKeyListView"
      :default-placeholder="'默认搜索姓名'"
      @search="searchMultiple"
    />
    <!-- 列表 -->
    <t-table-view
      ref="tableView"
      :height="height"
      :single="isSingle"
      :loading="tableLoading"
      :data="tableData"
      :total="tableTotal"
      :page-size="pageSize"
      :current="pageCurrent"
      :select-item="selectItem"
      current-key="userId"
      @on-select="onSelect"
      @on-current="onCurrent"
      @on-change="changePage"
      @on-sort-change="onSortChange"
      @on-page-size-change="onPageSizeChange"
    >
      <el-table-column
        v-for="item in columnsViewArr"
        :key="item"
        :min-width="colMinWidth"
        :label="columnsObj[item].title"
        :fixed="columnsObj[item].master ? 'left' : false"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span v-if="item == 'sex'">
            {{ scope.row[item] ? (scope.row[item] == 1 ? '男' : '女') : "-" }}
          </span>
          <span v-else>{{ scope.row[item] || "-" }}</span>
        </template>
      </el-table-column>
    </t-table-view>
  </div>
</template>
<script>
import tSearchBox from '@/packages/search-box/index.vue'
import mixinsPageTable from '@/packages/mixins/page_table'
import tTableView from '@/packages/table-view/index.vue'
import tTableConfig from '@/packages/table-config/table-col-config.vue'
import tableTdMultiCol from '@/packages/table-config/table-td-multi-col.vue'
import { getVendorContactUserList } from '@/api/testing/manufacturer'
import module from './config.js'
// 引入封装的方法
export default {
  components: {
    tSearchBox,
    tTableView,
    tTableConfig,
    tableTdMultiCol
  },
  mixins: [mixinsPageTable],
  props: {
    // 默认单选
    isSingle: {
      type: Boolean,
      default: true
    },
    vendorId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      moduleName: module.name,
      selectItem: [],
      searchKeyList: [
        { key: 'realname', label: '姓名', master: true, placeholder: '请输入姓名' },
        { key: 'username', label: '账号', placeholder: '请输入账号' }
      ],
      columnsObj: {
        'realname': {
          title: '姓名', master: true
        },
        'username': {
          title: '账号'
        },
        'mobile': {
          title: '联系电话'
        },
        'sex': {
          title: '性别'
        }
      },
      columnsViewArr: [
        'realname',
        'username',
        'mobile',
        'sex'
      ]
    }
  },
  methods: {
    async getList(showLoading = true) {
      if (showLoading) {
        this.tableLoading = true
      }
      const params = this.getPostData('page', 'limit')
      // 启用状态账号
      params.status = '1'
      params.vendorId = this.vendorId
      getVendorContactUserList(params).then((res) => {
        this.tableData = res.data ? res.data.records : []
        this.tableTotal = Number(res.data.total) || 0
        this.tableLoading = false
        this.handleSelection()
      }).catch(() => {
        this.tableLoading = false
      })
    }
  }
}
</script>
