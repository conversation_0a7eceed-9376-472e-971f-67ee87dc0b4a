<template>
  <div v-loading="loading" class="dialog-wrap">
    <batch-template
      :data="data"
      :available-data="availableArr"
      :show-delete-warning="false"
      view-key="questionName"
    />
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button :disabled="!availableArr.length" type="primary" @click="confirm">确定</el-button>
    </div>
  </div>
</template>
<script>
import batchTemplate from '@/packages/batch-delete/modal-bat-template'
import { queryExamPageByQues, removeQuestionBank } from '@/api/accumulate/questionBank'

export default {
  components: { batchTemplate },
  props: {
    data: {
      type: Array,
      default: () => {
        return []
      }
    },
    // 1-理论 2-靶机 3-仿真
    bankType: {
      type: Number,
      default: 1
    },
    // 当前模块名称
    moduleName: String
  },
  data() {
    return {
      examListMap: [],
      loading: true
    }
  },
  computed: {
    // 筛选返回可操作数据
    'availableArr': function() {
      const _data = []
      this.data.forEach((item, index) => {
        const examList = this.examListMap[index]
        // 题目没有被组卷
        // 或者被组卷了，但是试卷的使用次数为0
        if (examList && (examList.length === 0 || (examList.length > 0 && examList.every(val => !val.citedNum)))) {
          _data.push(item)
        }
      })
      return _data
    }
  },
  created() {
    const result = this.data.map(item => {
      return this.queryExamPageByQues(item)
    })
    Promise.all(result).then((data) => {
      this.examListMap = data
      this.loading = false
    }).catch((data) => {
      this.loading = false
    })
  },
  methods: {
    // 通过题目ID查询被引用的试卷列表
    'queryExamPageByQues': function(item) {
      return new Promise((resolve, reject) => {
        queryExamPageByQues({ pageType: 0, id: item.id }).then(res => {
          resolve(res.data.records)
        }).catch(() => {
          reject([])
        })
      })
    },
    close: function() {
      this.$emit('close')
    },
    confirm: function() {
      this.loading = true
      const result = this.availableArr.map(item => item.id)
      removeQuestionBank(result).then(() => {
        this.$message.success('删除题目成功')
        this.$bus.$emit(this.moduleName + '_module', 'reload')
      }).catch(() => {
        this.$bus.$emit(this.moduleName + '_module', 'reload')
      })
      // 点击确定，立刻关闭弹窗
      this.close()
    }
  }
}
</script>
