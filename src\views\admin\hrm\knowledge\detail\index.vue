<template>
  <detail-view
    :loading="loading"
    :data="data"
    :id="id"
    :view-item="viewItem"
    mode="drawer"
    title-key="detailTitle"
  />
</template>
<script>
import moduleConf from '../config'
import actionMenu from '../action/index'
import detailView from '@/packages/detail-view/index'
import detailBasicCourse from './basicCourse'
import detailTaskTrain from './taskTrain'
import detailQuestionNum from './questionNum'
export default {
  components: {
    actionMenu,
    detailView,
    detailBasicCourse,
    detailTaskTrain,
    detailQuestionNum
  },
  data() {
    return {
      moduleName: moduleConf.name,
      id: null, // 资源ID
      data: { detailTitle: this.$route.params.name }, // 资源数据对象
      loading: false,
      viewItem: [
        {
          transName: '课程内容',
          name: 'basicCourse',
          component: detailBasicCourse
        },
        {
          transName: '项目实训',
          name: 'taskTrain',
          component: detailTaskTrain
        },
        {
          transName: '题库题目',
          name: 'questionNum',
          component: detailQuestionNum
        }
      ]
    }
  },
  watch: {
    '$route': function(to, from) {
      const toId = to.params.hasOwnProperty('id') ? to.params.id : null
      const fromId = from.params.hasOwnProperty('id') ? from.params.id : null
      if (toId !== fromId) {
        this.loadBase()
      }
    }
  },
  created() {
    this.loadBase()
  },
  methods: {
    actionHandler: function(type, data) {
      switch (type) {
        case 'refresh':
          this.loadBase()
      }
    },
    'loadBase': function() {
      this.id = this.$route.params.id
    }
  }
}
</script>
