<template>
  <div class="resource-table">
    <!-- 操作区 -->
    <div class="operation-wrap">
      <div class="operation-left">
        <slot name="action" />
        <el-button type="primary" icon="el-icon-refresh" @click="refresh" />
      </div>
      <div class="operation-right">
        <el-badge :value="searchBtnShowNum">
          <el-button icon="el-icon-search" @click="openSearch">搜索</el-button>
        </el-badge>
        <!-- 自定义表格列 -->
        <t-table-config
          v-if="!customColData.length"
          :data="columnsObj"
          :active-key-arr="columnsViewArr"
          @on-change-col="onChangeCol"
        />
      </div>
    </div>
    <!-- 搜索区 -->
    <t-search-box
      v-show="searchView"
      :search-key-list="searchKeyListView"
      default-placeholder="默认搜索设备名称"
      @search="searchMultiple"
    />
    <!-- 列表 -->
    <t-table-view
      ref="tableView"
      :height="height"
      :single="single"
      :loading="tableLoading"
      :data="tableData"
      :total="tableTotal"
      :page-size="pageSize"
      :current="pageCurrent"
      :select-item="selectItem"
      type="list"
      current-key="id"
      @on-select="onSelect"
      @on-current="onCurrent"
      @on-change="changePage"
      @on-sort-change="onSortChange"
      @on-page-size-change="onPageSizeChange"
    >
      <el-table-column
        v-for="item in columnsViewArr"
        :key="item"
        :min-width="colMinWidth"
        :width="columnsObj[item].colWidth"
        :label="columnsObj[item].title"
        :fixed="columnsObj[item].master ? 'left' : false"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <div v-if="item == 'sort'">
            {{ scope.$index + 1 }}
          </div>
          <div v-else-if="item == 'credentials'">
            <div class="flex jc-between ai-center">
              <div class="ellipsis overflow-tooltip">
                <div class="ellipsis">{{ scope.row.credentials[0] || "-" }}</div>
              </div>
              <CountPopover :list="scope.row.credentials" />
            </div>
          </div>
          <span v-else-if="item == 'handle'">
            <el-link :underline="false" type="primary" @click.stop="jumpToOptionPage(scope.row)">进入</el-link>
          </span>
          <span v-else>{{ scope.row[item] || "-" }}</span>
        </template>
      </el-table-column>
    </t-table-view>
  </div>
</template>
<script>
import tSearchBox from '@/packages/search-box/index.vue'
import mixinsPageTable from '@/packages/mixins/page_table'
import tTableView from '@/packages/table-view/index.vue'
import tTableConfig from '@/packages/table-config/table-col-config.vue'
import tableTdMultiCol from '@/packages/table-config/table-td-multi-col.vue'
import modalResumeSnapshot from '../action/modal_resume_snapshot.vue'
import module from '../config.js'
import { queryDevicesAPI, instanceConsole, taskInstanceConsole } from '@/api/testing/index'
import CountPopover from '@/components/CountPopover/index'
export default {
  components: {
    tSearchBox,
    tTableView,
    tTableConfig,
    tableTdMultiCol,
    modalResumeSnapshot,
    CountPopover
  },
  mixins: [mixinsPageTable],
  data() {
    return {
      module,
      moduleName: module.name,
      searchKeyList: [
        { key: 'name', label: '设备名称', placeholder: '请输入', master: true }
      ],
      columnsObj: {
        sort: {
          title: '序号', master: true, colWidth: 70
        },
        name: {
          title: '设备名称', master: true
        },
        target: {
          title: 'IP地址/URL'
        },
        targetPort: {
          title: '端口号'
        },
        credentials: {
          title: '用户名/密码'
        },
        remark: {
          title: '备注'
        },
        handle: {
          title: '操作', colWidth: 120
        }
      },
      columnsViewArr: [
        'sort',
        'name',
        'target',
        'targetPort',
        'credentials',
        'remark',
        'handle'
      ],
      topologyId: '',
      resourceId: '',
      projectId: this.$route.params.projectId || this.$route.params.id,
      apiType: instanceConsole // 默认调用项目控制台
    }
  },
  methods: {
    getList: function(showLoading = true) {
      if (showLoading) {
        this.tableLoading = true
      }
      const params = this.getPostData('page', 'limit')
      params.projectId = this.projectId
      queryDevicesAPI(params).then((res) => {
        if (res.data.code === 0 && res.data) {
          const data = { ...res.data.data }
          this.tableData = data.records
          this.tableData.map(item => {
            item.credentials = (item.deviceAccountVOList || []).map(acc => `${acc.username} / ${acc.password}`)
          })
          this.tableTotal = data.total || 0
        }
        this.tableLoading = false
      }).catch(() => {
        this.tableLoading = false
      })
    },
    jumpToOptionPage: function(data) {
      const params = {
        vmId: data.nodeId,
        projectId: this.projectId,
        ip: data.target || data.deviceIp
      }
      if (this.$route.name == 'testingTask_detail') {
        params.projectId = this.$route.params.projectId
        params.taskId = this.$route.params.id
        params.ip = data.target || data.deviceIp
        this.apiType = taskInstanceConsole
      }
      this.apiType(params).then((res) => {
        window.open(res.data.data, '_blank')
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.resource-table {
  height: 100%;
  padding: 15px 0 0 0;
}
</style>
