<template>
  <div v-loading="loading" class="dialog-wrap" >
    <el-alert type="warning">
      <div slot="title">
        <p>1. 恢复快照前先将实例关机。</p>
        <p>2. 恢复快照会覆盖当前数据，请谨慎操作。</p>
      </div>
    </el-alert>
    <el-checkbox v-model="confirmValue" label="已知晓上述风险" />
    <div class="dialog-footer">
      <el-button type="text" @click="close">取消</el-button>
      <el-button :disabled="!confirmValue || !canCreate || noActive(data[0].status) || data[0].status === 'error' || resourceData.status.toLowerCase() !== 'shutoff'" type="primary" @click="confirm" >确定</el-button>
    </div>
  </div>
</template>
<script>
import { restoreSnapshot } from '@/packages/topo/api/orchestration'
import module from '../config'
import operationValid from '@/packages/topo/instance/detail/detail_snapshot_chain/action/operation_valid.js'
export default {
  mixins: [operationValid],
  props: {
    data: {
      type: Array,
      default: () => []
    },
    resourceId: {
      type: String
    },
    resourceData: {
      type: Object
    }
  },
  data() {
    return {
      moduleName: module.name,
      loading: false,
      confirmValue: false
    }
  },
  methods: {
    close() {
      this.$emit('close')
    },
    // modal点击确定
    confirm() {
      this.$bus.$emit(
        'SINGLE_TASK_API',
        {
          taskName: '恢复快照',
          resource: this.data[0],
          apiObj: restoreSnapshot,
          data: { id: this.resourceId, data: { 'snapshot_id': this.data[0].id }},
          sucsessCallback: (res) => {
            this.$bus.$emit(this.moduleName + '_module', 'reloadItem', this.data[0].id)
            this.$bus.$emit('node_module', 'reloadItem', this.resourceId)
            this.$bus.$emit('instances_module', 'reloadItem', this.resourceId)
          },
          errorCallback: () => {
            this.$bus.$emit(this.moduleName + '_module', 'reloadItem', this.data[0].id)
          }
        }
      )
      this.$emit('close')
    }
  }
}
</script>
