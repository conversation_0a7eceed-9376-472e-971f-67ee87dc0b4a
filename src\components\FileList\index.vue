<template>
  <ul class="file-list-view">
    <li
      v-for="(file, fileIndex) in fileList"
      :key="fileIndex + ''"
      :class="['file-item', `file-item--${file.status}`]"
      @click="clickFile(file, fileIndex)"
    >
      <img :src="getFileTypeIcon(file.name)" alt="" class="pic-icon" >
      <div
        v-overflow-tooltip
        :class="[
          'file-name',
          { 'is-active': switchable && fileIndex == active },
          { 'is-pointer': switchable },
        ]"
      >
        {{ file.name }}
      </div>
      <!-- <div class="file-size">( {{ file.size | fontSizeValue }} )</div> -->
      <div class="file-action">
        <span
          v-if="downloadable && file.status == 'success'"
          @click.stop="downloadClick(file)"
        >
          <i title="下载" class="el-icon-download pointer" />下载
        </span>
        <span
          v-if="deletable && file.status == 'success' || file.status == 'error'"
          @click.stop="deleteFile(file)"
        >
          <i :style="{ 'color': file.status == 'error' ? '#F56C6C' : 'var(--color-600)' }" title="删除" class="el-icon-delete pointer"/>
        </span>
      </div>
      <!-- 上传进度条 -->
      <el-progress
        v-if="file.status == 'uploading'"
        :stroke-width="2"
        :percentage="parseInt(file.percentage || 0, 10)"
      />
    </li>
  </ul>
</template>

<script>
import { fileSize, getFileIconWithSuffix } from '@/utils'

export default {
  name: 'FileList',
  filters: {
    fontSizeValue(size) {
      return fileSize(size)
    }
  },
  props: {
    // 当前选中
    value: {
      type: [Number, String],
      default: 0
    },
    // 文件列表
    list: {
      type: Array,
      required: true
    },
    // 可切换
    switchable: {
      type: Boolean,
      default: false
    },
    // 可删除
    deletable: {
      type: Boolean,
      default: true
    },
    // 可下载
    downloadable: {
      type: Boolean,
      default: true
    },
    // 下载方法
    onDownload: {
      type: Function
    }
  },
  data() {
    return {
      active: this.value,
      fileList: []
    }
  },
  watch: {
    list: {
      immediate: true,
      handler(list) {
        this.fileList = list.map((item) => {
          // item.uid = item.uid || (Date.now() + this.tempIndex++)
          item.status = item.status || 'success'
          return item
        })
      }
    }
  },
  methods: {
    // 文件图标类型
    getFileTypeIcon(url) {
      if (!url) return ''
      const temps = url ? url.split('.') : []
      let ext = ''
      if (temps.length > 0) {
        ext = temps[temps.length - 1]
      } else {
        ext = ''
      }

      return getFileIconWithSuffix(ext)
    },
    // 点击文件
    clickFile(file, index) {
      if (!this.switchable) return
      this.active = index
      this.$emit('input', index)
      this.$emit('click', file, index)
    },
    // 文件删除
    deleteFile(file) {
      console.log('file:', file)
      if (file.status == 'success') {
        this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.$emit('delete', file)
          })
          .catch(() => {
            this.$message.info('已取消删除')
          })
      } else {
        this.$emit('delete', file)
      }
    },
    // 文件下载
    downloadClick(file) {
      if (this.onDownload) {
        this.onDownload(file)
      } else {
        this.download(file)
      }
      // this.$emit('download', file)
    },
    download(file) {
      if (file.url) {
        const a = document.createElement('a')
        a.href = file.url
        a.download = file.fileName
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
      }
    }
  }
}
</script>

<style scoped lang="scss">
.file-list-view {
  .pointer {
    cursor: pointer;
  }
  width: 100%;
  .file-item {
    position: relative;
    width: 100%;
    font-size: 14px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: flex-start;

    overflow-wrap: break-word;
    word-break: normal;
    line-height: 1.5;
    border: 1px solid #DCDFE6;
    margin-bottom: 5px;
    padding: 6px 10px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow:ellipsis;
    cursor: pointer;

    &.file-item--success {
      border-color: #abdcff;
      background-color: #f0faff;
    }
    &.file-item--error {
      border-color: #f56c6c;
      background-color: #fff6f4;
    }

    /deep/ .el-progress {
      position: absolute;
      top: 20px;
      width: 100%;
      .el-progress__text {
        position: absolute;
        right: 20px;
        top: -10px;
      }
    }

    &:hover {
      // background-color: var(--neutral-100);
      .file-name {
        // color: var(--color-600);
      }
    }

    .pic-icon {
      width: 12px;
      margin-right: 8px;
      flex-shrink: 0;
    }

    &.file-item--uploading {
      .file-name {
        padding-right: 32px;
      }
    }

    .file-name {
      width: 100%;
      overflow: hidden;
      white-space: nowrap;
      text-overflow:ellipsis;
      padding-right: 12px;

      &.is-active {
        color: var(--color-500);
      }
      &.is-pointer {
        cursor: pointer;
      }
    }
    .file-size {
      color: #999;
      margin-right: 15px;
      flex-shrink: 0;
    }
    .file-action {
      color: var(--color-500);
      flex-shrink: 0;
      margin-left: auto;
    }
    .el-icon-download {
      cursor: pointer;
    }
    .el-icon-delete {
      cursor: pointer;
      margin-left: 5px;
    }
  }
}
</style>
