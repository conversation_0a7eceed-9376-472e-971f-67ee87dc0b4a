export default {
  props: {

  },
  data() {
    return {
      modalWidth: '520px',
      modalShow: false,
      modalName: null,
      drawerWidth: '720px',
      drawerShow: false,
      drawerName: null
    }
  },
  computed: {
    // 是否在列表界面
    'isList': function() {
      return this.page === 'list'
    },
    // 单选禁用
    'singleDisabled': function() {
      return !this.selectItem.length || this.selectItem.length > 1
    },
    // 多选禁用
    'multipleDisabled': function() {
      return !this.selectItem.length
    }
  },
  watch: {
    // 监听模态框动态组件，如置空则关闭模态框
    'modalName': function(val) {
      this.modalShow = (!!val)
    },
    // 监听抽屉框动态组件，如置空则关闭模态框
    'drawerName': function(val) {
      this.drawerShow = (!!val)
    }
  },
  methods: {
    'modalClose': function() {
      this.modalName = null
      this.modalWidth = '520px'
    },
    'drawerClose': function() {
      this.drawerName = null
      this.drawerWidth = '720px'
    },
    'clickDrop': function(name) {
      if (this.drawerAction && this.drawerAction.includes(name)) {
        this.drawerName = name
      } else {
        this.modalName = name
      }
    }
  }
}
