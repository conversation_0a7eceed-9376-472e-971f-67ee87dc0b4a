<template>
  <div class="resource-table">
    <!-- 操作区 -->
    <div class="operation-wrap">
      <div class="operation-left">
        <slot name="action" />
        <el-button type="primary" icon="el-icon-refresh" @click="refresh" />
      </div>
      <div class="operation-right">
        <el-badge :value="searchBtnShowNum">
          <el-button icon="el-icon-search" @click="openSearch">搜索</el-button>
        </el-badge>
        <!-- 自定义表格列 -->
        <t-table-config
          v-if="!customColData.length"
          :data="columnsObj"
          :active-key-arr="columnsViewArr"
          @on-change-col="onChangeCol"
        />
      </div>
    </div>
    <!-- 搜索区 -->
    <t-search-box
      v-show="searchView"
      :search-key-list="searchKeyListView"
      default-placeholder="默认搜索漏洞名称"
      @search="searchMultiple"
    />
    <!-- 列表 -->
    <t-table-view
      ref="tableView"
      :height="height"
      :single="true"
      :loading="tableLoading"
      :data="tableData"
      :total="tableTotal"
      :page-size="pageSize"
      :current="pageCurrent"
      :select-item="selectItem"
      current-key="bugId"
      @on-select="onSelect"
      @on-current="onCurrent"
      @on-change="changePage"
      @on-sort-change="onSortChange"
      @on-page-size-change="onPageSizeChange"
    >
      <el-table-column
        v-for="item in columnsViewArr"
        :key="item"
        :min-width="item === 'icon' ? '80' : colMinWidth"
        :label="columnsObj[item].title"
        :fixed="columnsObj[item].master ? 'left' : false"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <span v-if="item === 'bugName'">
            <a
              :href="`/assetRepo/hole/detail/${scope.row.bugId}/overview`"
              target="_blank"
            >{{ scope.row[item] || "-" }}</a
            >
          </span>
          <span v-else-if="item === 'bugType'">{{
            bugTypeObj[scope.row[item]]
              ? bugTypeObj[scope.row[item]].label
              : scope.row[item]
          }}</span>
          <span v-else-if="item === 'bugLevel'">{{
            bugLevelObj[scope.row[item]]
              ? bugLevelObj[scope.row[item]].label
              : scope.row[item]
          }}</span>
          <span v-else>{{ scope.row[item] || "-" }}</span>
        </template>
      </el-table-column>
    </t-table-view>
  </div>
</template>

<script>
import moduleName from './config'
import tSearchBox from '@/packages/search-box/index.vue'
import tTableView from '@/packages/table-view/index.vue'
import tTableConfig from '@/packages/table-config/table-col-config.vue'
import mixinsPageTable from '@/packages/mixins/page_table'
import { getProblemLoopholeGenPage } from '@/api/testing/index.js'
import { listLabel } from '@/api/admin/label'

export default {
  components: {
    tSearchBox,
    tTableView,
    tTableConfig
  },
  mixins: [mixinsPageTable],
  props: {
    bugType: {
      type: String,
      default: ''
    },
    bugLevel: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      moduleName: moduleName.name,
      // 搜索配置项
      searchKeyList: [
        { key: 'bugName', label: '漏洞名称', master: true },
        { key: 'cveNumber', label: ' CVE编号' },
        { key: 'bugTypes', label: ' 漏洞类型', type: 'select', valueList: [] }
      ],
      // 所有可配置显示列 master：不可隐藏 title:列名称
      columnsObj: {
        bugName: { title: '漏洞名称', master: true },
        cveNumber: { title: 'CVE编号' },
        bugType: { title: '漏洞类型' },
        bugLevel: { title: '漏洞等级' }
      },
      // 当前显示列key表 默认，如果localStorage有数据将被覆盖
      columnsViewArr: ['bugName', 'cveNumber', 'bugType', 'bugLevel'],
      bugTypeList: [],
      bugTypeObj: {},
      bugLevelList: [],
      bugLevelObj: {},
      loopholeTypeOptions: []
    }
  },
  created() {
    this.getBugType()
    this.getBugLevel()
  },
  methods: {
    // 获取漏洞类型
    getBugType: function() {
      listLabel({ page: 1, limit: 10000, lableId: 107 })
        .then(res => {
          this.bugTypeList = res.rows.map(item => {
            return { label: item.lableName, value: item.lableId + '' }
          })
          this.searchKeyList.find(item => item.key === 'bugTypes').valueList = this.bugTypeList
          this.bugTypeObj = this.bugTypeList.reduce((acc, prev) => {
            acc[prev.value] = prev
            return acc
          }, {})
        })
        .catch(() => {
          this.bugTypeList = []
          this.bugTypeObj = {}
        })
    },
    // 获取漏洞级别
    getBugLevel: function() {
      listLabel({ page: 1, limit: 10000, lableId: 108 })
        .then(res => {
          this.bugLevelList = res.rows.map(item => {
            return { label: item.lableName, value: item.lableId + '' }
          })
          this.bugLevelObj = this.bugLevelList.reduce((acc, prev) => {
            acc[prev.value] = prev
            return acc
          }, {})
          this.searchKeyList.push({
            key: 'bugLevels',
            label: '漏洞等级',
            type: 'select',
            valueList: this.bugLevelList
          })
        })
        .catch(() => {
          this.bugLevelList = []
          this.bugLevelObj = {}
        })
    },
    getList: function(showLoading = true) {
      if (showLoading) {
        this.tableLoading = true
      }
      const params = this.getPostData('page', 'limit')
      getProblemLoopholeGenPage(params)
        .then(res => {
          const data = { ...res.data.data }
          this.tableData = data ? data.records : []
          this.tableTotal = Number(data.total) || 0
          this.tableLoading = false
          this.handleSelection()
        })
        .catch(() => {
          this.tableLoading = false
        })
    }
  }
}
</script>
