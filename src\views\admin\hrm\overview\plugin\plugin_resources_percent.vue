<template>
  <!-- 资源状态统计 待办 进行中...-->
  <div class="plugin-view">
    <h3 class="plugin-title">{{ pluginTitle }}</h3>
    <div v-loading="true" v-if="loading" class="plugin-loading" />
    <div v-else-if="!apiData" class="flex-1 flex-center">暂无数据</div>
    <div v-else class="resources-percent-content">
      <div class="left">
        <span class="icon-view">
          <svg t="1712045667166" class="icon" viewBox="0 0 1031 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="67727" width="200" height="200"><path d="M113.8 834v64c0 14.2 11.4 25.6 25.6 25.6h179.4c14.2 0 25.6-11.4 25.6-25.6v-64H113.8z m230.6-616.1V89.7c0-14.2-11.4-25.6-25.6-25.6H139.4c-14.2 0-25.6 11.4-25.6 25.6v128.2h230.6z m-230.6 51.2h230.7v539.2H113.8V269.1z m0 0M394.9 834v64c0 14.2 11.4 25.6 25.6 25.6h179.4c14.2 0 25.6-11.4 25.6-25.6v-64H394.9z m230.7-616.1V89.7c0-14.2-11.4-25.6-25.6-25.6H420.5c-14.2 0-25.6 11.4-25.6 25.6v128.2h230.7z m-230.7 51.2h230.7v539.2H394.9V269.1z m0 0M791.3 848.5l11.1 63.1c2.4 14 15.7 23.2 29.7 20.8l176.7-31.1c14-2.4 23.2-15.7 20.8-29.7l-11.1-63.1-227.2 40zM905 202.3L882.7 76.2C880.3 62.2 867 53 853 55.4L676.4 86.5c-14 2.4-23.2 15.7-20.8 29.7l22.2 126.2L905 202.3z m-218.3 90.6l227.2-40L1014 783.3l-227.2 40-100.1-530.4z m0 0" p-id="67728" fill="#2c2c2c"/></svg>
        </span>
        <span class="label">
          <div class="count">
            <span>{{ apiData.openCourse + apiData.noOpenCourse }}</span>
          </div>
          <div class="name">总数量</div>
        </span>
      </div>
      <div v-if="pluginApiType === 'personal_tasks' || pluginApiType === 'team_tasks' || pluginApiType === 'penetration_tasks'" class="right">
        <div class="state-row">
          <el-badge type="success" is-dot />公开课
          <span class="count">
            <span>{{ apiData.openCourse }}</span>
          </span>
        </div>
        <div class="state-row">
          <el-badge type="danger" is-dot />非公开课
          <span class="count">
            <span>{{ apiData.noOpenCourse }}</span>
          </span>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="scss">
  .resources-percent-content {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    .state-row {
      width: 100%;
      padding: 0 12px;
      font-size: 14px;
      color: var(--neutral-600);
      white-space: nowrap;
      overflow: hidden;
      height: 40px;
      line-height: 40px;
      border-bottom: solid 1px #dbdde0;
      .ivu-badge-status-text{
        font-size: 14px;
        color: #707275;
      }
      .count {
        float: right;
        color: #4a4c4f;
        a {
          color: #4a4c4f;
          &:hover {
            color: var(--color-600);
          }
        }
      }
    }
    .icon-view {
      display: inline-block;
      background: #f5f7fa;
      position: relative;
      width: 70px;
      height: 70px;
      border-radius: 50%;
      color: #707275;
      font-size: 20px;
      font-weight: 800;
      .icon {
        width: 36px;
        height: 36px;
        top: 17px;
        position: absolute;
        left: 16px;
      }
    }
    .label {
      display: inline-block;
      margin-left: 10px;
      vertical-align: top;
      line-height: 30px;
      padding-top: 5px;
      .count {
        color: #181a1d;
        font-size: 24px;
        font-weight: 600;
        a {
          color: #181a1d;
          &:hover {
            color: var(--color-600);
          }
        }
      }
      .name {
        font-size: 14px;
        color: #707275;
      }
    }
    .left {
      width: 50%;
      padding-top: 15px;
      text-align: center;
    }
    .right {
      width: 50%;
      margin-left: 30px;
    }
    .legend-dot {
      display: inline-block;
      width: 8px;
      height: 8px;
      border-radius: 50%;
      position: relative;
      right: 10px;
    }
  }
</style>
<script>
import pluginMixin from './mixin_plugin.js'
export default {
  mixins: [
    pluginMixin
  ],
  props: {
    personData: {
      type: Object
    }
  },
  data() {
    return {}
  },
  watch: {
    personData: {
      handler(newVal) {
      // 处理对象参数的变化
        const { openCourse, noOpenCourse } = newVal
        this.apiData = { openCourse, noOpenCourse }
      },
      immediate: true,
      deep: true // 监听对象的深层变化
    }
  },
  method: {
    getData() {
    }
  }
}
</script>
