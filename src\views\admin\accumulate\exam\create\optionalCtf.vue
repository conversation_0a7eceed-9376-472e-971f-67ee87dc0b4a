<template>
  <div class="card-bg optional-ctf">
    <div class="classification-list">
      <div class="classification-list-title">
        <div>
          题目分类：
        </div>
        <div class="classification-list-add">
          <el-button type="text" icon="el-icon-plus" @click="modalShow = true, modalName = 'modalAdd'">添加</el-button>
        </div>
      </div>
      <div>
        <div v-for="(item, index) in examClassifys" :key="index" :class="examClassValue == item ? 'is-active classification-content' : 'classification-content'" @click="switchExamClass(item)">
          <div v-overflow-tooltip class="classification-content-overflow">
            {{ item }}
          </div>
          <div>
            <i class="el-icon-delete" @click.stop="deleteExamClass(index)" />
          </div>
        </div>
      </div>
    </div>
    <div class="_paper_container">
      <div class="_paper_top">
        <div class="_paper_top_title"><div class="line"/>分类</div>
        <div style="margin-bottom: 5px;">
          <el-tag
            v-for="(item,index) in classifyObj[examClassValue]" :key="index"
            :disable-transitions="true"
            closable
            style="margin: 3px 5px 5px 0"
            @close="closeClassify(index, item)">
            {{ item.categoryName }}
          </el-tag>
        </div>
        <el-button type="ghost" class="mb-10" @click="drawerName = 'selectClassify', drawerShow = true">选择分类</el-button>
        <div class="_paper_search">
          <div class="line"/>
          <div class="_paper_search_1">
            总题数
            <span style="font-weight: bold; color: var(--color-600); font-size: 18px; margin-right: 10px;">{{
              tableTotal
            }}</span>
          </div>
          <div class="_paper_search_1">
            已选
            <span style="font-weight: bold; color: var(--color-600); font-size: 18px;">{{
              checkQuestionList.filter(item => { return item.examClassify == examClassValue }).length
            }}</span>
          </div>
        </div>
        <div class="mb-10">
          <transverse-list :data="bankTypeList" :allow-deletion="false" :allow-add="false" :allow-edit="false" title="类别" id-name="id" label="name" @node-click="selectBankType"/>
        </div>
        <div class="ju-between">
          <transverse-list :data="questionTypeList" :allow-deletion="false" :allow-add="false" :allow-edit="false" title="题型" id-name="id" label="name" @node-click="selectQuestionType"/>
        </div>
      </div>
      <div class="_paper_bottom">
        <div class="_paper_header">
          <div>
            <el-button @click="selectAll()">全选</el-button>
            <el-button @click="deSelectAll()">取消全选</el-button>
          </div>
          <el-badge :value="searchBtnShowNum">
            <el-button icon="el-icon-search" @click="openSearch">搜索</el-button>
          </el-badge>
        </div>
        <!-- 搜索区 -->
        <t-search-box
          v-show="searchView"
          :search-key-list="searchKeyListView"
          style="margin: 10px 0 0 0;"
          default-placeholder="默认搜索名称"
          @search="searchMultiple"
        />
        <!-- 题目列表 -->
        <div v-if="questionList.length" class="_question_list">
          <div
            v-for="(q, index) in questionList"
            :class="`_question_item ${
              checkQuestionList.filter(item => { return item.id === q.id }).length &&
              '_question_item_check'
            }`"
            :key="q.id"
            @click="handleCheckQuestion(q)"
          >
            <div>
              <img v-if="checkQuestionList.filter(item => { return item.id === q.id }).length" :src="select_actie" alt="">
              <img v-else :src="select" alt="">
              <div class="_question_item_content">{{ index + 1 }}.&nbsp;<span v-html="q.questionType != 10 ? q.content : q.questionName"/></div>
              <div class="_question_item_type">
                <el-tag size="small">{{ questionConf.questionTypObj[q.questionType].label }}</el-tag>
              </div>
            </div>
            <div v-if="q.questionType == 1" class="_question_option">
              <el-radio-group
                v-for="(op, i) in JSON.parse(q.questionOptions)"
                :key="op"
                :value="q.questionAnswer"
              >
                <el-radio :label="optionLabel[i]" disabled>{{ op }}</el-radio>
              </el-radio-group>
            </div>
            <div v-if="q.questionType == 2">
              <el-checkbox-group :value="q.questionAnswer.split('')">
                <div class="_question_option">
                  <el-checkbox
                    v-for="(op, i) in JSON.parse(q.questionOptions)"
                    :key="op"
                    :label="optionLabel[i]" disabled
                  >{{ op }}</el-checkbox
                  >
                </div>
              </el-checkbox-group>
            </div>
            <div v-if="q.questionType == 3" class="_question_option">
              <el-radio-group :value="q.questionAnswer">
                <el-radio label="A" disabled>正确</el-radio>
                <el-radio label="B" disabled>错误</el-radio>
              </el-radio-group>
            </div>
            <div v-else-if="q.questionType == 10" class="combination-question-wrap">
              <div v-for="(item, index) in q.combinationQuestionBOS" :key="index">
                <div class="comp-question">
                  综合题{{ index + 1 }}.&nbsp;<span v-html="item.questionName"/>
                </div>
                <div v-for="(sub, subIndex) in item.content" :key="subIndex" class="comp-content-wrap">
                  <div>题目{{ subIndex + 1 }}.&nbsp;<span v-html="sub.contentName"/></div>
                </div>
              </div>
            </div>
            <div v-else class="_question_option">
              <span>{{ q.questionAnswer }}</span>
            </div>
          </div>
        </div>
        <el-empty
          v-else
          :image="img"
          :image-size="110"
          style="margin: 100px auto"
          description="暂无数据"
        />
      </div>
      <!-- 中部弹窗 start-->
      <el-dialog
        :title="titleMapping[modalName]"
        :visible.sync="modalShow"
        :width="modalWidth"
        :destroy-on-close="true"
        @close="modalClose"
      >
        <transition name="el-fade-in-linear">
          <component
            :is="modalName"
            :name="modalName"
            @close="modalClose"
            @call="confirmCall"
          />
        </transition>
      </el-dialog>
      <!-- 中部弹窗 end-->
      <!-- 侧拉弹窗 start-->
      <el-drawer
        :title="titleMapping[drawerName]"
        :visible.sync="drawerShow"
        :size="drawerWidth"
        append-to-body
        @close="drawerClose"
      >
        <transition v-if="drawerShow" name="el-fade-in-linear">
          <component
            :is="drawerName"
            :name="drawerName"
            @close="drawerClose"
            @call="drawerConfirmCall"
          />
        </transition>
      </el-drawer>
      <!-- 侧拉弹窗 end-->
    </div>
  </div>
</template>

<script>
import questionConf from '../../questionBank/config.js'
import transverseList from '@/packages/transverse-list/index.vue'
import tSearchBox from '@/packages/search-box/index.vue'
import mixinsPageTable from '@/packages/mixins/page_table'
import selectClassify from './select-classify.vue'
import modalAdd from './modal-add.vue'

import { getQuestionBank } from '@/api/accumulate/questionBank'

export default {
  components: {
    selectClassify,
    transverseList,
    modalAdd,
    tSearchBox
  },
  mixins: [mixinsPageTable],
  props: {
    classObj: {
      type: Object
    },
    selectQuestionList: {
      type: Array
    },
    examClassList: {
      type: Array
    }
  },
  data() {
    return {
      questionConf: questionConf,
      titleMapping: {
        'selectClassify': '分类列表',
        'modalAdd': '添加'
      },
      examClassValue: '',
      drawerWidth: '720px',
      modalWidth: '520px',
      examClassifys: [],
      drawerShow: false,
      modalShow: false,
      modalName: null,
      drawerName: null,
      select_actie: require('@/assets/select_active.png'),
      select: require('@/assets/select.png'),
      img: require('@/assets/empty_state.png'),
      // 搜索配置项
      searchKeyList: [
        { key: 'content', label: '题干', master: true },
        { key: 'complexity', label: '难度', type: 'radio', valueList: questionConf.complexityArr },
        { key: 'useList', label: '用途', type: 'select', valueList: questionConf.usesArr }
      ],
      optionLabel: Array.from({ length: 26 }, (_, index) => String.fromCharCode('A'.charCodeAt(0) + index)),
      bankTypeList: [
        { id: 1, name: '理论' },
        { id: 2, name: '靶机' },
        { id: 3, name: '仿真' }
      ],
      questionTypeList: [
        { id: 1, name: '单选题' },
        { id: 2, name: '多选题' },
        { id: 3, name: '判断题' },
        { id: 4, name: 'CTF题' },
        { id: 5, name: 'AWD题' },
        { id: 6, name: '其它' },
        { id: 7, name: '填空题' },
        { id: 8, name: '简答题' },
        { id: 9, name: '漏洞题' },
        { id: 10, name: '组合题' }
      ],
      checkQuestionList: [],
      questionList: [],
      classifyObj: {
        bool: false
      },
      questionType: '',
      bankType: '',
      tableTotal: 0
    }
  },
  watch: {
    classifyObj: {
      handler(newValue, oldValue) {
        if (this.classifyObj[this.examClassValue]) {
          this.getList()
        }
      },
      deep: true
    },
    checkQuestionList() {
      this.$emit('check-question-list-change', this.checkQuestionList)
    }
  },
  created() {
    this.examClassifys = this.examClassList
    this.examClassValue = this.examClassifys[0] || ''
    this.classifyObj = this.classObj
    this.classifyObj.bool = !this.classifyObj.bool
    this.checkQuestionList = this.selectQuestionList
  },
  methods: {
    modalClose() {
      this.modalShow = false
    },
    confirmCall(name) {
      this.examClassifys.push(name)
    },
    switchExamClass(item) {
      this.examClassValue = item
      this.getList()
    },
    selectAll() {
      this.questionList.forEach(q => {
        if (this.checkQuestionList.filter(item => { return item.id === q.id }).length === 0) {
          q.examClassify = this.examClassValue
          this.checkQuestionList.push(q)
        }
      })
    },
    deSelectAll() {
      this.questionList.forEach(q => {
        this.checkQuestionList = this.checkQuestionList.filter(
          (el) => el.id !== q.id
        )
      })
    },
    closeClassify(index, item) {
      this.checkQuestionList = this.checkQuestionList.filter(i => { return i.categoryId !== item.id && i.examClassify !== this.examClassValue })
      this.classifyObj[this.examClassValue].splice(index, 1)
      this.classifyObj.bool = !this.classifyObj.bool
    },
    deleteExamClass(index) {
      this.$confirm(`此操作将删除分类“${this.examClassifys[index]}”，是否继续？`, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        if (this.classifyObj[this.examClassifys[index]]) {
          this.checkQuestionList = this.checkQuestionList.filter(i => { return i.examClassify !== this.examClassifys[index] })
        }
        Object.defineProperty(this.classifyObj, this.examClassifys[index], { enumerable: false })
        this.examClassifys.splice(index, 1)
        this.examClassValue = this.examClassifys[0] || ''
        this.getList()
      }).catch(() => {
      })
    },
    selectBankType(item) {
      this.bankType = item.id
      this.getList()
    },
    selectQuestionType(item) {
      this.questionType = item.id
      this.getList()
    },
    // 侧拉选择资源回调
    drawerConfirmCall: function(type, data) {
      if (type === 'confirm_classify') {
        const arr = this.classifyObj[this.examClassValue] ? this.classifyObj[this.examClassValue] : []
        data.forEach(item => {
          if (arr.filter(classify => { return classify.id == item.id }).length === 0) {
            arr.push(item)
          }
        })
        this.classifyObj[this.examClassValue] = arr
        this.classifyObj.bool = !this.classifyObj.bool
        this.drawerClose()
      }
    },
    drawerClose() {
      this.drawerShow = false
    },
    // 选择题目
    handleCheckQuestion(q) {
      // 取消
      if (this.checkQuestionList.filter(item => { return item.id === q.id }).length !== 0) {
        this.checkQuestionList = this.checkQuestionList.filter(
          (el) => el.id !== q.id
        )
        return
      }
      // 选择
      q.examClassify = this.examClassValue
      this.checkQuestionList.push(q)
    },
    getList: function() {
      const params = this.getPostData('page', 'limit')
      params.pageType = 0
      const categoryIds = []
      if (!this.classifyObj[this.examClassValue]) {
        this.questionList = []
        this.tableTotal = 0
        return
      }
      this.classifyObj[this.examClassValue].forEach(item => {
        categoryIds.push(item.id)
      })
      params.categoryIds = categoryIds
      params.questionType = this.questionType
      params.bankType = this.bankType
      if (categoryIds.length === 0) {
        this.questionList = []
        this.tableTotal = 0
        return
      }
      getQuestionBank(params).then(res => {
        this.questionList = res.data.records
        this.questionList.sort((a, b) => {
          return a.questionType - b.questionType
        })
        this.questionList.forEach(item => {
          if (item.questionType == 10) {
            item.combinationQuestionBOS.forEach(sub => {
              sub.content = JSON.parse(sub.content).map(val => {
                return {
                  contentName: val,
                  questionScore: null
                }
              })
            })
          }
        })
        if (this.checkQuestionList.length) {
          const arr = this.checkQuestionList.filter(item => { return item.examClassify != this.examClassValue })
          arr.forEach(element => {
            if (this.questionList.filter(item => { return element.id == item.id }).length != 0) {
              this.questionList = this.questionList.filter(
                (el) => el.id !== element.id
              )
            }
          })
        }
        this.tableTotal = this.questionList.length
      }).catch(() => {})
    }
  }
}
</script>

<style lang="scss" scoped>
._paper_container {
  overflow: auto;
  border-left: 1px solid #C0C4CC;
  padding-left: 15px;
  .line {
    width: 3px;
    height: 15px;
    margin-right: 5px;
    background: var(--color-600);
  }
  ._paper_top {
    padding-bottom: 10px;
    .ju-between {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    ._paper_search {
      display: flex;
      align-items: center;
      margin: 5px 0;
      ._paper_search_1 {
        font-size: 16px;
        font-weight: 500;
        color: #333;
      }
    }
    ._paper_top_title {
      font-size: 16px;
      font-weight: 500;
      color: #333;
      margin-bottom: 10px;
      display: flex;
      align-items: center;
    }
    ::v-deep {
      .transverse-list {
        padding: 0;
        border: none;
        .transverse-list-operate {
          display: none;
        }
      }
    }
  }
  ._paper_bottom {
    ._paper_header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .select-box {
        display: flex;
        align-items: center;
        justify-content: space-between;
        color: #4e5969;
        font-size: 14px;
      }
    }
    ._paper_search_screen {
      padding: 11px;
      > div {
        display: flex;
        margin-bottom: 20px;
        > div {
          margin-right: 40px;
          padding: 10px;
          // background: #288FEF;
          font-size: 14px;
          color: #999999;
          text-align: center;
          border-radius: 4px;
          cursor: pointer;
        }
      }
      ._paper_type_check {
        background: #288fef;
        color: white;
      }
    }
    ._question_list {
      margin-top: 10px;
      ._question_item {
        padding: 15px 20px 10px 40px;
        min-height: 90px;
        background: #ffffff;
        display: flex;
        flex-direction: column;
        font-size: 14px;
        color: #4e5969;
        position: relative;
        border: 1px solid #e5e6eb;
        border-radius: 4px;
        margin-bottom: 10px;
        ._question_option {
          margin-top: 10px;
          font-size: 14px;
          color: #4e5969;
          display: flex;
          flex-direction: column;
          line-height: 22px;
          word-break: break-all;
          ::v-deep .el-radio {
            margin-bottom: 8px;
            display: flex;
            align-items: flex-start;
            .el-radio__label {
              font-size: 14px;
              color: #4e5969;
              white-space: normal;
              word-break: break-all;
            }
          }
          ::v-deep .el-checkbox {
            margin-bottom: 8px;
            display: flex;
            align-items: flex-start;
            .el-checkbox__label {
              font-size: 14px;
              color: #4e5969;
              white-space: normal;
              word-break: break-all;
            }
          }
        }
        ._question_item_content {
          display: flex;
          // max-height: 200px;
          // overflow-y: auto;
          overflow-x: auto;
          margin-right: 55px;
        }
        ._question_item_type {
          position: absolute;
          right: 15px;
          top: 15px;
        }
        .combination-question-wrap {
          >div {
            border: 1px solid rgb(229, 230, 235);
            margin: 5px 0px 10px;
            padding: 15px 20px 5px;
            .comp-question {
              display: flex;
              // max-height: 200px;
              // overflow-y: auto;
              overflow-x: auto;
              >span {
                flex: 1;
                word-break: break-all;
              }
            }
            .comp-content-wrap {
              border: 1px solid #e5e6eb;
              margin: 5px 0 10px;
              padding: 15px 20px;
              >div:first-child {
                display: flex;
                // max-height: 200px;
                // overflow-y: auto;
                overflow-x: auto;
                >span {
                  flex: 1;
                  word-break: break-all;
                }
              }
            }
          }
        }
      }
      img {
        position: absolute;
        left: 12px;
        top: 17px;
        width: 16px;
        height: 16px
      }
      ._question_item_check {
        border: 1px solid var(--color-600);
      }
    }
  }
}
::v-deep {
  .el-icon-search {
    cursor: pointer;
  }
}
.card-bg {
  border-radius: 4px;
  padding: 15px;
  background-color: #FFFFFF;
}
.optional-ctf {
  display: flex;
  .classification-list {
    min-width: 200px;
    height: 100%;
    padding-right: 15px;
    .classification-list-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .classification-list-add {
        .el-button--text {
          color: var(--color-600);
          i {
            font-size: 12px;
          }
        }
      }
    }
    .classification-content {
      padding: 5px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 10px;
      cursor: pointer;
      .classification-content-overflow {
        flex: 1;
        white-space: nowrap; /* 不换行 */
        overflow: hidden;    /* 隐藏超出部分 */
        text-overflow: ellipsis; /* 显示省略号 */
        margin-right: 10px;
      }
    }
    .classification-content:hover {
      font-weight: 700;
      background-color: var(--color-50) !important;
      color: var(--color-600);
    }
    .is-active {
      font-weight: 700;
      background-color: var(--color-50) !important;
      color: var(--color-600);
    }
  }
}
</style>
